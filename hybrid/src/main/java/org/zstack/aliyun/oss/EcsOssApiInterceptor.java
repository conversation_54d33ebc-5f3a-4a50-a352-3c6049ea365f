package org.zstack.aliyun.oss;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.Platform;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.aliyun.oss.*;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.message.APIMessage;

/**
 * Created by mingjian.deng on 17/3/2.
 */
public class EcsOssApiInterceptor implements ApiMessageInterceptor {
    @Autowired
    protected DatabaseFacade dbf;

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIDetachOssBucketFromEcsDataCenterMsg) {
            validate((APIDetachOssBucketFromEcsDataCenterMsg) msg);
        } else if (msg instanceof APIAddOssBucketFromRemoteMsg) {
            validate((APIAddOssBucketFromRemoteMsg) msg);
        }
        return msg;
    }

    private void validate(final APIAddOssBucketFromRemoteMsg msg) {
        if (Q.New(OssBucketVO.class).
                eq(OssBucketVO_.dataCenterUuid, msg.getDataCenterUuid()).
                eq(OssBucketVO_.bucketName, msg.getBucketName()).count() > 0) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "ossBucket [%s] is already added in dataCenter [%s]", msg.getBucketName(), msg.getDataCenterUuid()));
        }
    }

    private void validate(final APIDetachOssBucketFromEcsDataCenterMsg msg) {
        OssBucketVO ovo = dbf.findByUuid(msg.getOssBucketUuid(), OssBucketVO.class);
        if (ovo.getCurrent().equals("false")) {
            throw new ApiMessageInterceptionException(Platform.argerr("OssBucket[%s] is not attached.", ovo.getUuid()));
        }
    }
}
