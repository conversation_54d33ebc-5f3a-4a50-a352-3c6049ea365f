package org.zstack.aliyun.network.vpn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.header.Component;
import org.zstack.header.aliyun.network.vpn.VpcVpnExtensionPoint;
import org.zstack.header.core.Completion;
import org.zstack.header.datacenter.DataCenterInventory;
import org.zstack.header.hybrid.network.vpn.VpcUserVpnGatewayInventory;
import org.zstack.header.hybrid.network.vpn.VpcVpnGatewayInventory;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.function.ForEachFunction;

import java.util.List;
import java.util.Map;

/**
 * Created by mingjian.deng on 2017/8/3.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class VpcVpnEmitter implements Component {
    @Autowired
    private PluginRegistry pluginRgty;
    private List<VpcVpnExtensionPoint> vpnExtensions;

    @Override
    public boolean start() {
        populateExtensions();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    private void populateExtensions() {
        vpnExtensions = pluginRgty.getExtensionList(VpcVpnExtensionPoint.class);
    }

    public void syncUserVpnGateway(final DataCenterInventory inv, Map data, Completion completion) {
        CollectionUtils.safeForEach(vpnExtensions, new ForEachFunction<VpcVpnExtensionPoint>() {
            @Override
            public void run(VpcVpnExtensionPoint arg) {
                arg.syncVpcUserVpnGateway(inv, data, completion);
            }
        });
    }

    public void afterSyncUserVpnGateway(final List<VpcUserVpnGatewayInventory> invs, Map data, Completion completion) {
        CollectionUtils.safeForEach(vpnExtensions, new ForEachFunction<VpcVpnExtensionPoint>() {
            @Override
            public void run(VpcVpnExtensionPoint arg) {
                arg.afterSyncVpcUserVpnGateway(invs, data, completion);
            }
        });
    }

    public void syncVpnGateway(final List<String> dcUuids, Map data, Completion completion) {
        CollectionUtils.safeForEach(vpnExtensions, new ForEachFunction<VpcVpnExtensionPoint>() {
            @Override
            public void run(VpcVpnExtensionPoint arg) {
                arg.syncVpcVpnGateway(dcUuids, data, completion);
            }
        });
    }

    public void afterSyncVpnGateway(final List<VpcVpnGatewayInventory> invs, Map data, Completion completion) {
        CollectionUtils.safeForEach(vpnExtensions, new ForEachFunction<VpcVpnExtensionPoint>() {
            @Override
            public void run(VpcVpnExtensionPoint arg) {
                arg.afterSyncVpcVpnGateway(invs, data, completion);
            }
        });
    }
}
