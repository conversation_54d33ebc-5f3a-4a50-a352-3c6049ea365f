package org.zstack.aliyun.network;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SimpleQuery;
import org.zstack.header.aliyun.network.connection.ConnectionAccessPointInventory;
import org.zstack.header.aliyun.network.connection.ConnectionAccessPointVO;
import org.zstack.header.aliyun.network.connection.ConnectionAccessPointVO_;
import org.zstack.header.core.Completion;
import org.zstack.header.datacenter.DataCenterInventory;
import org.zstack.header.datacenter.DataCenterVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/17.
 */
public class AliyunConnectionAccessPointCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(AliyunConnectionAccessPointCascadeExtension.class);

    @Autowired
    private DatabaseFacade dbf;

    private static final String NAME = ConnectionAccessPointVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            completion.success();
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletion(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletion(final CascadeAction action, final Completion completion) {
        final List<ConnectionAccessPointInventory> aps = apsFromAction(action);
        for (ConnectionAccessPointInventory ap : aps) {
            dbf.removeByPrimaryKey(ap.getUuid(), ConnectionAccessPointVO.class);
        }
        completion.success();
    }

    private List<ConnectionAccessPointInventory> apsFromAction(CascadeAction action) {
        final List<ConnectionAccessPointInventory> list = new ArrayList<>();
        if (DataCenterVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<DataCenterInventory> datacenters = action.getParentIssuerContext();
            for (DataCenterInventory dc: datacenters) {
                List<ConnectionAccessPointVO> vos = dbf.createQuery(ConnectionAccessPointVO.class).add(
                        ConnectionAccessPointVO_.dataCenterUuid, SimpleQuery.Op.EQ, dc.getUuid()
                ).list();
                if (vos != null) {
                    list.addAll(ConnectionAccessPointInventory.valueOf(vos));
                }
            }
        } else if (NAME.equals(action.getParentIssuer())) {
            list.addAll(action.getParentIssuerContext());
        }
        return list;
    }

    @Override
    public List<String> getEdgeNames() {
        return Collections.singletonList(DataCenterVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<ConnectionAccessPointInventory> ctx = apsFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }
        return null;
    }
}
