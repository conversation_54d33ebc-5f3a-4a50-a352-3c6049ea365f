package org.zstack.aliyun.network.connection;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.aliyun.network.connection.APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.MessageReply;
import org.zstack.header.vrouterRoute.SyncVRouterEntryFromRouteTableMsg;
import org.zstack.hybrid.core.HybridUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.NetworkUtils;
import org.zstack.vrouterRoute.*;

import java.util.List;
import java.util.Map;

/**
 * Created by mingjian.deng on 17/6/26.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class CreateLocalRouteEntryFlow implements Flow {
    private static final CLogger logger = Utils.getLogger(CreateLocalRouteEntryFlow.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected VRouterRouteManager routeManager;

    @Override
    @SuppressWarnings("unchecked")
    public void run(FlowTrigger trigger, Map data) {
        APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg msg = (APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg)data.get("msg");
        if (msg.getDirection().equalsIgnoreCase("in")) {
            trigger.next();
            return;
        }
        String vpcCidr = (String)data.get(AliyunVpcConnectionConstant.VPC_CIDR);
        String vRouterVmUuid = (String)data.get(AliyunVpcConnectionConstant.LOCAL_VROUTER_UUID);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("create-local-vrouter-entry-%s", vRouterVmUuid));

        chain.then(new NoRollbackFlow() {
            String __name__ = "create-router-table-in-vrouter-if-not-existed";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                List<String> vRouterTableUuids = routeManager.getTableUuidsFromVrUuid(vRouterVmUuid);
                String vRouterTableUuid = null;
                if (vRouterTableUuids == null || vRouterTableUuids.isEmpty()) {
                    VRouterRouteTableVO vo = new VRouterRouteTableVO();
                    vo.setUuid(Platform.getUuid());
                    vo.setName(String.format("Auto-Created-%s", System.currentTimeMillis()));
                    vo.setType("User");
                    vo.setDescription("auto created route table for create connection by zstack");

                    dbf.persistAndRefresh(vo);
                    vRouterTableUuid = vo.getUuid();

                    routeManager.attachRouterTableToVRouter(vRouterTableUuid, vRouterVmUuid);
                } else {
                    vRouterTableUuid = vRouterTableUuids.get(0);
                }
                data1.put("table", vRouterTableUuid);
                trigger1.next();
            }
        }).then(new Flow() {
            String __name__ = "create-vrouter-route-entry-if-not-existed";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                String tableUuid = (String)data1.get("table");
                List<VRouterRouteEntryVO> entryVOs = Q.New(VRouterRouteEntryVO.class).
                        eq(VRouterRouteEntryVO_.routeTableUuid, tableUuid).
                        eq(VRouterRouteEntryVO_.target, msg.getCpeIp()).list();
                if (entryVOs != null && entryVOs.size() > 0) {
                    for (VRouterRouteEntryVO entryVO: entryVOs) {
                        if (NetworkUtils.isCidrOverlap(entryVO.getDestination(), vpcCidr)) {
                            data.put("vrouter-entry", entryVO.getUuid());
                            trigger1.next();
                            return;
                        }
                    }
                }

                VRouterRouteEntryVO vo = new VRouterRouteEntryVO();
                vo.setUuid(Platform.getUuid());
                vo.setDestination(vpcCidr);
                vo.setTarget(msg.getCpeIp());
                vo.setDescription("auto created route entry for create connection by zstack");
                vo.setRouteTableUuid(tableUuid);
                vo.setType(VRouterRouteEntryType.UserStatic);
                vo.setDistance(AliyunVpcConnectionConstant.DEFAULT_ROUTE_DISTANCE);
                dbf.persistAndRefresh(vo);

                SyncVRouterEntryFromRouteTableMsg semsg = new SyncVRouterEntryFromRouteTableMsg();
                semsg.setUuid(tableUuid);
                bus.makeTargetServiceIdByResourceUuid(semsg, VRouterRouteConstants.SERVICE_ID, tableUuid);
                bus.send(semsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            data.put("vrouter-entry", vo.getUuid());
                            trigger1.next();
                        } else {
                            dbf.remove(vo);
                            trigger1.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger1, Map data1) {
                String uuid = (String)data.get("vrouter-entry");
                if (uuid != null) {
                    VRouterRouteEntryDeletionMsg demsg = new VRouterRouteEntryDeletionMsg();
                    demsg.setUuid(uuid);
                    bus.makeTargetServiceIdByResourceUuid(demsg, VRouterRouteConstants.SERVICE_ID, demsg.getUuid());
                    bus.send(demsg, new CloudBusCallBack(trigger1) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.warn(reply.getError().toString());
                            } else {
                                logger.debug(String.format("delete VRouterRouteEntry[uuid:%s] success", demsg.getUuid()));
                            }
                            trigger1.rollback();
                        }
                    });
                } else {
                    trigger1.rollback();
                }
            }
        }).done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data) {
                trigger.next();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                trigger.fail(errCode);
            }
        }).start();

    }

    @Override
    public void rollback(FlowRollback trigger, Map data) {
        String uuid = (String)data.get("vrouter-entry");
        if (uuid != null) {
            VRouterRouteEntryDeletionMsg demsg = new VRouterRouteEntryDeletionMsg();
            demsg.setUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(demsg, VRouterRouteConstants.SERVICE_ID, demsg.getUuid());
            bus.send(demsg, new CloudBusCallBack(trigger) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(reply.getError().toString());
                    } else {
                        logger.debug(String.format("delete VRouterRouteEntry[uuid:%s] success", demsg.getUuid()));
                    }
                    trigger.rollback();
                }
            });
        } else {
            trigger.rollback();
        }
    }
}
