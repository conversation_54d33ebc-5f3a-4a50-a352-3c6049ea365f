package org.zstack.aliyun.network;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.header.Component;
import org.zstack.header.aliyun.network.AliyunNetworkExtensionPoint;
import org.zstack.header.aliyun.network.vpc.EcsVSwitchInventory;
import org.zstack.header.aliyun.network.vpc.EcsVpcInventory;
import org.zstack.header.aliyun.network.vrouter.VpcVirtualRouteEntryInventory;
import org.zstack.header.aliyun.network.vrouter.VpcVirtualRouterInventory;
import org.zstack.header.core.Completion;
import org.zstack.header.datacenter.DataCenterInventory;
import org.zstack.header.identityzone.IdentityZoneInventory;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.function.ForEachFunction;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by mingjian.deng on 2017/8/2.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class AliyunNetworkEmitter implements Component {
    @Autowired
    private PluginRegistry pluginRgty;
    private List<AliyunNetworkExtensionPoint> networkExtensions;

    @Override
    public boolean start() {
        populateExtensions();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    private void populateExtensions() {
        networkExtensions = pluginRgty.getExtensionList(AliyunNetworkExtensionPoint.class);
    }

    public void syncEcsVSwitch(final List<IdentityZoneInventory> invs, Map data, Completion completion) {
        CollectionUtils.safeForEach(networkExtensions, new ForEachFunction<AliyunNetworkExtensionPoint>() {
            @Override
            public void run(AliyunNetworkExtensionPoint arg) {
                List<String> dcUuids = invs.stream().map(IdentityZoneInventory::getDataCenterUuid).distinct().collect(Collectors.toList());
                arg.syncEcsVSwitch(dcUuids, data, completion);
            }
        });
    }

    public void afterSyncEcsVSwitch(final List<EcsVSwitchInventory> invs, Map data, Completion completion) {
        CollectionUtils.safeForEach(networkExtensions, new ForEachFunction<AliyunNetworkExtensionPoint>() {
            @Override
            public void run(AliyunNetworkExtensionPoint arg) {
                arg.afterSyncEcsVSwitch(invs, data, completion);
            }
        });
    }

    public void syncEcsVpc(final DataCenterInventory inv, Map data, Completion completion) {
        CollectionUtils.safeForEach(networkExtensions, new ForEachFunction<AliyunNetworkExtensionPoint>() {
            @Override
            public void run(AliyunNetworkExtensionPoint arg) {
                arg.syncEcsVpc(inv, data, completion);
            }
        });
    }

    public void afterSyncEcsVpc(final List<EcsVpcInventory> invs, Map data, Completion completion) {
        CollectionUtils.safeForEach(networkExtensions, new ForEachFunction<AliyunNetworkExtensionPoint>() {
            @Override
            public void run(AliyunNetworkExtensionPoint arg) {
                arg.afterSyncEcsVpc(invs, data, completion);
            }
        });
    }

    public void afterSyncAliyunVirtualRouter(final List<VpcVirtualRouterInventory> invs, Map data, Completion completion) {
        CollectionUtils.safeForEach(networkExtensions, new ForEachFunction<AliyunNetworkExtensionPoint>() {
            @Override
            public void run(AliyunNetworkExtensionPoint arg) {
                arg.afterSyncAliyunVirtualRouter(invs, data, completion);
            }
        });
    }

    public void syncAliyunVirtualRouterEntry(final List<String> vrUuids, String vrType, Map map, Completion completion) {
        CollectionUtils.safeForEach(networkExtensions, new ForEachFunction<AliyunNetworkExtensionPoint>() {
            @Override
            public void run(AliyunNetworkExtensionPoint arg) {
                arg.syncAliyunVirtualRouterEntry(vrUuids, vrType, map, completion);
            }
        });
    }

    public void afterSyncAliyunVirtualRouterEntry(final List<VpcVirtualRouteEntryInventory> invs, String vrType, Map map, Completion completion) {
        CollectionUtils.safeForEach(networkExtensions, new ForEachFunction<AliyunNetworkExtensionPoint>() {
            @Override
            public void run(AliyunNetworkExtensionPoint arg) {
                arg.afterSyncAliyunVirtualRouterEntry(invs, vrType, map, completion);
            }
        });
    }
}
