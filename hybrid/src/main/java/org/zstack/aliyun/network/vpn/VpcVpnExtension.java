package org.zstack.aliyun.network.vpn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.aliyun.network.vpn.AliyunVpcVpnConstant;
import org.zstack.header.aliyun.network.vpn.VpcVpnExtensionPoint;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.datacenter.DataCenterInventory;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.hybrid.network.vpn.*;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.message.MessageReply;
import org.zstack.hybrid.core.HybridUtilsForAliyun;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by mingjian.deng on 2017/8/3.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class VpcVpnExtension implements VpcVpnExtensionPoint {
    @Autowired
    protected CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;

    @Override
    public void syncVpcUserVpnGateway(DataCenterInventory inv, Map map, Completion completion) {
        SessionInventory session = (SessionInventory)map.get("session");

        SyncVpcUserVpnGatewayInnerMsg suimsg = new SyncVpcUserVpnGatewayInnerMsg();
        suimsg.setSession(session);
        suimsg.setDataCenterUuid(inv.getUuid());

        bus.makeTargetServiceIdByResourceUuid(suimsg, AliyunVpcVpnConstant.SERVICE_ID, inv.getUuid());
        bus.send(suimsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    SyncVpcUserVpnGatewayInnerReply reply1 = reply.castReply();
                    afterSyncVpcUserVpnGateway(reply1.getInventories(), map, completion);
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    @Override
    public void afterSyncVpcUserVpnGateway(List<VpcUserVpnGatewayInventory> invs, Map map, Completion completion) {
        if (invs.size() > 0) {
            List<String> dcUuids = invs.stream().map(VpcUserVpnGatewayInventory::getDataCenterUuid).distinct().collect(Collectors.toList());
            syncVpcVpnConnection(dcUuids, map, completion);
        } else {
            completion.success();
        }
    }

    @Override
    public void syncVpcVpnConnection(List<String> dcUuids, Map map, Completion completion) {
        SessionInventory session = (SessionInventory)map.get("session");
        ErrorCodeList elist = new ErrorCodeList();
        List<VpcVpnConnectionInventory> connections = new ArrayList<>();

        new While<>(dcUuids).all((dcUuid, compl) -> {
            SyncVpnConnectionInnerMsg scimsg = new SyncVpnConnectionInnerMsg();
            scimsg.setSession(session);
            scimsg.setDataCenterUuid(dcUuid);

            bus.makeTargetServiceIdByResourceUuid(scimsg, AliyunVpcVpnConstant.SERVICE_ID, dcUuid);
            bus.send(scimsg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        SyncVpnConnectionInnerReply reply1 = reply.castReply();
                        connections.addAll(reply1.getInventories());
                    } else {
                        elist.getCauses().add(reply.getError());
                    }
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (elist.getCauses().size() > 0) {
                    completion.fail(elist.getCauses().get(0));
                } else {
                    afterSyncVpcVpnConnection(connections, map, completion);
                }
            }
        });
    }

    @Override
    public void afterSyncVpcVpnConnection(List<VpcVpnConnectionInventory> invs, Map map, Completion completion) {
        completion.success();
    }

    @Override
    public void syncVpcVpnGateway(List<String> dcUuids, Map map, Completion completion) {
        SessionInventory session = (SessionInventory)map.get("session");
        ErrorCodeList elist = new ErrorCodeList();
        List<VpcVpnGatewayInventory> vpngates = new ArrayList<>();

        new While<>(dcUuids).all((dcUuid, compl) -> {
            SyncVpcVpnGatewayInnerMsg svimsg = new SyncVpcVpnGatewayInnerMsg();
            svimsg.setSession(session);
            svimsg.setDataCenterUuid(dcUuid);
            bus.makeTargetServiceIdByResourceUuid(svimsg, AliyunVpcVpnConstant.SERVICE_ID, dcUuid);
            bus.send(svimsg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        SyncVpcVpnGatewayInnerReply reply1 = reply.castReply();
                        vpngates.addAll(reply1.getInventories());
                    } else {
                        elist.getCauses().add(reply.getError());
                    }
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (elist.getCauses().size() > 0) {
                    completion.fail(elist.getCauses().get(0));
                } else {
                    afterSyncVpcVpnGateway(vpngates, map, completion);
                }
            }
        });
    }

    @Override
    public void afterSyncVpcVpnGateway(List<VpcVpnGatewayInventory> invs, Map map, Completion completion) {
        if (invs.size() > 0) {
            List<String> tmp = new ArrayList<>();
            for (VpcVpnGatewayInventory inv : invs) {
                tmp.add(HybridUtilsForAliyun.getDataCenterUuidByVSwitchUuid(dbf, inv.getvSwitchUuid()));
            }
            List<String> dcUuids = tmp.stream().distinct().collect(Collectors.toList());
            syncVpcVpnConnection(dcUuids, map, completion);
        } else {
            completion.success();
        }
    }
}
