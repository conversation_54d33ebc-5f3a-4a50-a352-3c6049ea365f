package org.zstack.aliyun.network.connection;

/**
 * Created by mingji<PERSON>.deng on 2017/8/3.
 */

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.header.Component;
import org.zstack.header.aliyun.network.connection.ConnectionAccessPointInventory;
import org.zstack.header.aliyun.network.connection.ExpressExtensionPoint;
import org.zstack.header.aliyun.network.connection.VirtualBorderRouterInventory;
import org.zstack.header.core.Completion;
import org.zstack.header.datacenter.DataCenterInventory;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.function.ForEachFunction;

import java.util.List;
import java.util.Map;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class ExpressEmitter implements Component {
    @Autowired
    private PluginRegistry pluginRgty;

    private List<ExpressExtensionPoint> expressExtensions;

    @Override
    public boolean start() {
        populateExtensions();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    private void populateExtensions() {
        expressExtensions = pluginRgty.getExtensionList(ExpressExtensionPoint.class);
    }

    public void syncAccessPoint(final DataCenterInventory inv, Map map, Completion completion) {
        CollectionUtils.safeForEach(expressExtensions, new ForEachFunction<ExpressExtensionPoint>() {
            @Override
            public void run(ExpressExtensionPoint arg) {
                arg.syncAccessPoint(inv, map, completion);
            }
        });
    }

    public void afterSyncAccessPoint(final List<ConnectionAccessPointInventory> invs, Map map, Completion completion) {
        CollectionUtils.safeForEach(expressExtensions, new ForEachFunction<ExpressExtensionPoint>() {
            @Override
            public void run(ExpressExtensionPoint arg) {
                arg.afterSyncAccessPoint(invs, map, completion);
            }
        });
    }

    public void afterSyncVbr(final List<VirtualBorderRouterInventory> invs, Map map, Completion completion) {
        CollectionUtils.safeForEach(expressExtensions, new ForEachFunction<ExpressExtensionPoint>() {
            @Override
            public void run(ExpressExtensionPoint arg) {
                arg.afterSyncVbr(invs, map, completion);
            }
        });
    }

    public void syncVbr(DataCenterInventory inv, Map map, Completion completion) {
        CollectionUtils.safeForEach(expressExtensions, new ForEachFunction<ExpressExtensionPoint>() {
            @Override
            public void run(ExpressExtensionPoint arg) {
                arg.syncVbr(inv, map, completion);
            }
        });
    }

    public void syncRouterInterface(final List<String> dcUuids, Map map, Completion completion) {
        CollectionUtils.safeForEach(expressExtensions, new ForEachFunction<ExpressExtensionPoint>() {
            @Override
            public void run(ExpressExtensionPoint arg) {
                arg.syncRouterInterface(dcUuids, map, completion);
            }
        });
    }
}
