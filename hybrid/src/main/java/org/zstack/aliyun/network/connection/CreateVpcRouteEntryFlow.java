package org.zstack.aliyun.network.connection;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.aliyun.network.AliyunNetworkConstant;
import org.zstack.header.aliyun.network.connection.APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg;
import org.zstack.header.aliyun.network.vpc.CreateAliyunVpcRouteEntryInnerMsg;
import org.zstack.header.aliyun.network.vpc.CreateAliyunVpcRouteEntryInnerReply;
import org.zstack.header.aliyun.network.vpc.DeleteAliyunVpcRouteEntryInnerMsg;
import org.zstack.header.aliyun.network.vrouter.*;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.hybrid.network.HybridNetworkConstant;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.NetworkUtils;

import javax.persistence.Tuple;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by mingjian.deng on 17/6/26.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class CreateVpcRouteEntryFlow implements Flow {
    private static final CLogger logger = Utils.getLogger(CreateVpcRouteEntryFlow.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;

    @Override
    @SuppressWarnings("unchecked")
    public void run(FlowTrigger trigger, Map data) {
        APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg msg = (APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg)data.get("msg");
        if (msg.getDirection().equalsIgnoreCase("out")) {
            trigger.next();
            return;
        }
        List<String> entries = new ArrayList<>();
        String vpcRiId = (String)data.get(AliyunVpcConnectionConstant.VPC_RI_ID);

        List<String> localCidrs = (List<String>)data.get(AliyunVpcConnectionConstant.LOCAL_VROUTER_CIDR);
        List<String> skipCidrs = new ArrayList<>();
        String vRouterUuid = (String)data.get(AliyunVpcConnectionConstant.VPC_VROUTER_UUID);

        List<Tuple> tuples = Q.New(VpcVirtualRouteEntryVO.class).
                eq(VpcVirtualRouteEntryVO_.virtualRouterUuid, vRouterUuid).
                select(VpcVirtualRouteEntryVO_.destinationCidrBlock, VpcVirtualRouteEntryVO_.nextHopId, VpcVirtualRouteEntryVO_.uuid).
                listTuple();
        for (Tuple tuple: tuples) {
            String tupleCidr = tuple.get(0, String.class);
            for (String cidr: localCidrs) {
                if (!tupleCidr.equals(HybridNetworkConstant.FULL_CIDR) && !cidr.equals(HybridNetworkConstant.FULL_CIDR) && NetworkUtils.isCidrOverlap(tupleCidr, cidr)) {
                    if (tuple.get(1, String.class).equals(vpcRiId)) {
                        logger.debug(String.format("local cidr and entry is already existed in vrouter [%s], skip create entry", vRouterUuid));
                        entries.add(tuple.get(2, String.class));
                        skipCidrs.add(cidr);
                    } else {
                        trigger.fail(Platform.operr("custom cidr [%s] is already existed in vrouter [%s], " +
                                        "it is overlapped with target cidr [%s], please check and delete it first.",
                                tuple.get(0, String.class), vRouterUuid, cidr));
                        return;
                    }
                }
            }
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("create vpc route entry").allowEmptyFlow();
        for (String cidr: localCidrs) {
            if (skipCidrs.contains(cidr)) {
                continue;
            }
            chain.then(new Flow() {
                @Override
                public void run(FlowTrigger trigger1, Map data1) {
                    CreateAliyunVpcRouteEntryInnerMsg cremsg = new CreateAliyunVpcRouteEntryInnerMsg();
                    cremsg.setSession(msg.getSession());
                    cremsg.setDstCidrBlock(cidr);
                    cremsg.setNextHopType(NextHopType.routerinterface.toString());
                    cremsg.setNextHopId(vpcRiId);
                    cremsg.setvRouterType(VRouterType.vrouter.toString());
                    cremsg.setvRouterUuid(vRouterUuid);
                    bus.makeTargetServiceIdByResourceUuid(cremsg, AliyunNetworkConstant.SERVICE_ID, msg.getVbrUuid());
                    bus.send(cremsg, new CloudBusCallBack(trigger1) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                CreateAliyunVpcRouteEntryInnerReply rly = reply.castReply();
                                data1.put("entry", rly.getInventory());
                                entries.add(rly.getInventory().getUuid());
                                trigger1.next();
                            } else {
                                trigger1.fail(reply.getError());
                            }
                        }
                    });
                }

                @Override
                public void rollback(FlowRollback trigger1, Map data1) {
                    VpcVirtualRouteEntryInventory inventory = (VpcVirtualRouteEntryInventory)data1.get("entry");
                    if (inventory == null) {
                        trigger1.rollback();
                        return;
                    }
                    DeleteAliyunVpcRouteEntryInnerMsg dremsg = new DeleteAliyunVpcRouteEntryInnerMsg();
                    dremsg.setSession(msg.getSession());
                    dremsg.setEntry(VpcVirtualRouteEntryInventory.valueOf(dbf.findByUuid(inventory.getUuid(), VpcVirtualRouteEntryVO.class)));

                    bus.makeTargetServiceIdByResourceUuid(dremsg, AliyunNetworkConstant.SERVICE_ID, inventory.getUuid());
                    bus.send(dremsg, new CloudBusCallBack(msg) {
                        @Override
                        public void run(MessageReply reply) {
                            trigger1.rollback();
                        }
                    });
                }
            });
        }

        chain.done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data1) {
                data.put("entry", entries);
                trigger.next();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                trigger.fail(errCode);
            }
        }).start();

    }

    @Override
    public void rollback(FlowRollback trigger, Map data) {
        APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg msg = (APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg)data.get("msg");

        List<String> entries = (List<String>)data.get("entry");
        if (entries == null || entries.size() == 0) {
            trigger.rollback();
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("delete vpc route entry").allowEmptyFlow();
        for (String entry: entries) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(FlowTrigger trigger1, Map data1) {
                    DeleteAliyunVpcRouteEntryInnerMsg dremsg = new DeleteAliyunVpcRouteEntryInnerMsg();
                    dremsg.setSession(msg.getSession());
                    dremsg.setEntry(VpcVirtualRouteEntryInventory.valueOf(dbf.findByUuid(entry, VpcVirtualRouteEntryVO.class)));

                    bus.makeTargetServiceIdByResourceUuid(dremsg, AliyunNetworkConstant.SERVICE_ID, entry);
                    bus.send(dremsg, new CloudBusCallBack(msg) {
                        @Override
                        public void run(MessageReply reply) {
                            trigger1.next();
                        }
                    });
                }
            });
        }

        chain.done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data) {
                trigger.rollback();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                trigger.rollback();
            }
        }).start();

    }
}
