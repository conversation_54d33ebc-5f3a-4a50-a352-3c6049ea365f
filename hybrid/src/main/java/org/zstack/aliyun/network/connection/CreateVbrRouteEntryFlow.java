package org.zstack.aliyun.network.connection;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.aliyun.network.AliyunNetworkConstant;
import org.zstack.header.aliyun.network.connection.APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg;
import org.zstack.header.aliyun.network.connection.VirtualBorderRouterVO;
import org.zstack.header.aliyun.network.vpc.CreateAliyunVpcRouteEntryInnerMsg;
import org.zstack.header.aliyun.network.vpc.CreateAliyunVpcRouteEntryInnerReply;
import org.zstack.header.aliyun.network.vpc.DeleteAliyunVpcRouteEntryInnerMsg;
import org.zstack.header.aliyun.network.vrouter.*;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.hybrid.network.HybridNetworkConstant;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.NetworkUtils;

import javax.persistence.Tuple;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by mingjian.deng on 17/6/26.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class CreateVbrRouteEntryFlow implements Flow {
    private static final CLogger logger = Utils.getLogger(CreateVbrRouteEntryFlow.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;

    @Override
    @SuppressWarnings("unchecked")
    public void run(FlowTrigger trigger, Map data) {
        APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg msg = (APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg)data.get("msg");
        List<String> localCidrs = (List<String>)data.get(AliyunVpcConnectionConstant.LOCAL_VROUTER_CIDR);
        String vpcCidr = (String)data.get(AliyunVpcConnectionConstant.VPC_CIDR);
        String vbrRiId = (String)data.get(AliyunVpcConnectionConstant.VBR_RI_ID);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("create-route-entry-in-vbr-%s", msg.getVbrUuid()));

        List<Tuple> tuples = Q.New(VpcVirtualRouteEntryVO.class).
                eq(VpcVirtualRouteEntryVO_.virtualRouterUuid, msg.getVbrUuid()).
                select(VpcVirtualRouteEntryVO_.destinationCidrBlock, VpcVirtualRouteEntryVO_.nextHopId, VpcVirtualRouteEntryVO_.uuid)
                .listTuple();

        chain.then(new Flow() {
            String __name__ = "create-entry-point-to-vpc";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                if (msg.getDirection().equalsIgnoreCase("in")) {
                    trigger1.next();
                    return;
                }
                List<String> entries = new ArrayList<>();

                for (Tuple tuple: tuples) {
                    String tupleCidr = tuple.get(0, String.class);
                    if (!tupleCidr.equals(HybridNetworkConstant.FULL_CIDR) && !vpcCidr.equals(HybridNetworkConstant.FULL_CIDR)
                            && NetworkUtils.isCidrOverlap(tupleCidr, vpcCidr)) {
                        if (tuple.get(1, String.class).equals(vbrRiId)) {
                            logger.info(String.format("vpc cidr and entry is already existed in vbr [%s], skip create entry", msg.getVbrUuid()));
                            entries.add(tuple.get(2, String.class));
                            trigger1.next();
                            return;
                        } else {
                            trigger1.fail(Platform.operr("custom cidr [%s] is already existed in vbr [%s], " +
                                    "it is overlapped with target cidr [%s], please check and delete it first.",
                                    tuple.get(0, String.class), msg.getVbrUuid(), vpcCidr));
                            return;
                        }
                    }
                }

                CreateAliyunVpcRouteEntryInnerMsg cremsg = new CreateAliyunVpcRouteEntryInnerMsg();
                cremsg.setSession(msg.getSession());
                cremsg.setDstCidrBlock(vpcCidr);
                cremsg.setNextHopType(NextHopType.routerinterface.toString());
                cremsg.setNextHopId(vbrRiId);
                cremsg.setvRouterType(VRouterType.vbr.toString());
                cremsg.setvRouterUuid(msg.getVbrUuid());
                bus.makeTargetServiceIdByResourceUuid(cremsg, AliyunNetworkConstant.SERVICE_ID, msg.getVbrUuid());
                bus.send(cremsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            CreateAliyunVpcRouteEntryInnerReply rly = reply.castReply();
                            entries.add(rly.getInventory().getUuid());
                            data1.put("entry1", entries);
                            trigger1.next();
                        } else {
                            trigger1.fail(reply.getError());
                        }
                    }
                });
            }
            @Override
            public void rollback(FlowRollback trigger1, Map data1) {
                List<String> entries = (List<String>)data1.get("entry1");
                if (entries == null || entries.size() == 0) {
                    trigger1.rollback();
                    return;
                }
                DeleteAliyunVpcRouteEntryInnerMsg dremsg = new DeleteAliyunVpcRouteEntryInnerMsg();
                dremsg.setSession(msg.getSession());
                dremsg.setEntry(VpcVirtualRouteEntryInventory.valueOf(dbf.findByUuid(entries.get(0), VpcVirtualRouteEntryVO.class)));

                bus.makeTargetServiceIdByResourceUuid(dremsg, AliyunNetworkConstant.SERVICE_ID, entries.get(0));
                bus.send(dremsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        trigger1.rollback();
                    }
                });
            }
        }).then(new Flow() {
            String __name__ = "create-entry-point-to-local";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                if (msg.getDirection().equalsIgnoreCase("out")) {
                    trigger1.next();
                    return;
                }
                List<String> entries = new ArrayList<>();

                VirtualBorderRouterVO vbrvo = dbf.findByUuid(msg.getVbrUuid(), VirtualBorderRouterVO.class);

                List<String> skipCidrs = new ArrayList<>();
                for (Tuple tuple: tuples) {
                    String tupleCidr = tuple.get(0, String.class);
                    for (String cidr: localCidrs) {
                        if (!tupleCidr.equals(HybridNetworkConstant.FULL_CIDR) && !cidr.equals(HybridNetworkConstant.FULL_CIDR)
                                && NetworkUtils.isCidrOverlap(tupleCidr, cidr)) {
                            if (tuple.get(1, String.class).equals(vbrvo.getVlanInterfaceId())) {
                                logger.info(String.format("local cidr and entry is already existed in vbr [%s], skip create entry", msg.getVbrUuid()));
                                entries.add(tuple.get(2, String.class));
                                skipCidrs.add(cidr);
                            } else {
                                trigger1.fail(Platform.operr("custom cidr [%s] is already existed in vbr [%s], " +
                                                "it is overlapped with target cidr [%s], please check and delete it first.",
                                        tuple.get(0, String.class), msg.getVbrUuid(), cidr));
                                return;
                            }
                        }
                    }
                }

                FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
                chain.setName("create vbr route entry").allowEmptyFlow();

                for (String cidr: localCidrs) {
                    if (skipCidrs.contains(cidr)) {
                        continue;
                    }
                    chain.then(new Flow() {
                        @Override
                        public void run(FlowTrigger trigger2, Map data2) {
                            CreateAliyunVpcRouteEntryInnerMsg cremsg = new CreateAliyunVpcRouteEntryInnerMsg();
                            cremsg.setSession(msg.getSession());
                            cremsg.setDstCidrBlock(cidr);
                            cremsg.setNextHopType(NextHopType.routerinterface.toString());
                            cremsg.setNextHopId(dbf.findByUuid(msg.getVbrUuid(), VirtualBorderRouterVO.class).getVlanInterfaceId());
                            cremsg.setvRouterType(VRouterType.vbr.toString());
                            cremsg.setvRouterUuid(msg.getVbrUuid());
                            bus.makeTargetServiceIdByResourceUuid(cremsg, AliyunNetworkConstant.SERVICE_ID, msg.getVbrUuid());
                            bus.send(cremsg, new CloudBusCallBack(trigger) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (reply.isSuccess()) {
                                        CreateAliyunVpcRouteEntryInnerReply rly = reply.castReply();
                                        entries.add(rly.getInventory().getUuid());
                                        data2.put("entry2", entries);
                                        trigger2.next();
                                    } else {
                                        trigger2.fail(reply.getError());
                                    }
                                }
                            });
                        }

                        @Override
                        public void rollback(FlowRollback trigger2, Map data2) {
                            List<String> entires = (List<String>)data2.get("entry2");
                            if (entires == null || entires.size() == 0) {
                                trigger2.rollback();
                                return;
                            }
                            DeleteAliyunVpcRouteEntryInnerMsg dremsg = new DeleteAliyunVpcRouteEntryInnerMsg();
                            dremsg.setSession(msg.getSession());
                            dremsg.setEntry(VpcVirtualRouteEntryInventory.valueOf(dbf.findByUuid(entries.get(0), VpcVirtualRouteEntryVO.class)));

                            bus.makeTargetServiceIdByResourceUuid(dremsg, AliyunNetworkConstant.SERVICE_ID, entires.get(0));
                            bus.send(dremsg, new CloudBusCallBack(msg) {
                                @Override
                                public void run(MessageReply reply) {
                                    entries.remove(0);
                                    data2.put("entry2", entires);
                                    trigger2.rollback();
                                }
                            });
                        }
                    });
                }

                chain.done(new FlowDoneHandler(trigger1) {
                    @Override
                    public void handle(Map data2) {
                        data1.put("entry2", entries);
                        trigger1.next();
                    }
                }).error(new FlowErrorHandler(trigger) {
                    @Override
                    public void handle(ErrorCode errCode, Map data2) {
                        trigger1.fail(errCode);
                    }
                }).start();
            }
            @Override
            public void rollback(FlowRollback trigger1, Map data1) {
                List<String> entries = (List<String>)data1.get("entry2");
                if (entries == null || entries.size() == 0) {
                    trigger1.rollback();
                    return;
                }
                FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
                chain.setName("delete vbr route entry").allowEmptyFlow();
                for (String entry: entries) {
                    chain.then(new NoRollbackFlow() {
                        @Override
                        public void run(FlowTrigger trigger2, Map data2) {
                            DeleteAliyunVpcRouteEntryInnerMsg dremsg = new DeleteAliyunVpcRouteEntryInnerMsg();
                            dremsg.setSession(msg.getSession());
                            dremsg.setEntry(VpcVirtualRouteEntryInventory.valueOf(dbf.findByUuid(entry, VpcVirtualRouteEntryVO.class)));

                            bus.makeTargetServiceIdByResourceUuid(dremsg, AliyunNetworkConstant.SERVICE_ID, entry);
                            bus.send(dremsg, new CloudBusCallBack(msg) {
                                @Override
                                public void run(MessageReply reply) {
                                    trigger2.next();
                                }
                            });
                        }
                    });
                }
                chain.done(new FlowDoneHandler(trigger1) {
                    @Override
                    public void handle(Map data1) {
                        trigger1.rollback();
                    }
                }).error(new FlowErrorHandler(trigger1) {
                    @Override
                    public void handle(ErrorCode errCode, Map data1) {
                        trigger1.rollback();
                    }
                }).start();
            }
        }).done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data1) {
                data.put("entry1", data1.get("entry1"));
                data.put("entry2", data1.get("entry2"));
                trigger.next();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data1) {
                trigger.fail(errCode);
            }
        }).start();


    }

    @Override
    public void rollback(FlowRollback trigger, Map data) {
        APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg msg = (APICreateConnectionBetweenL3NetworkAndAliyunVSwitchMsg)data.get("msg");

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-route-entry-in-vbr-%s", msg.getVbrUuid()));

        chain.then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                List<VpcVirtualRouteEntryInventory> inventories = (List<VpcVirtualRouteEntryInventory>)data.get("entry2");
                if (inventories == null || inventories.size() == 0) {
                    trigger1.next();
                    return;
                }

                FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
                chain.setName("delete vbr route entry").allowEmptyFlow();
                for (VpcVirtualRouteEntryInventory inventory: inventories) {
                    chain.then(new NoRollbackFlow() {
                        @Override
                        public void run(FlowTrigger trigger2, Map data2) {
                            DeleteAliyunVpcRouteEntryInnerMsg dremsg = new DeleteAliyunVpcRouteEntryInnerMsg();
                            dremsg.setSession(msg.getSession());
                            dremsg.setEntry(VpcVirtualRouteEntryInventory.valueOf(dbf.findByUuid(inventory.getUuid(), VpcVirtualRouteEntryVO.class)));

                            bus.makeTargetServiceIdByResourceUuid(dremsg, AliyunNetworkConstant.SERVICE_ID, inventory.getUuid());
                            bus.send(dremsg, new CloudBusCallBack(msg) {
                                @Override
                                public void run(MessageReply reply) {
                                    trigger2.next();
                                }
                            });
                        }
                    });
                }
                chain.done(new FlowDoneHandler(trigger1) {
                    @Override
                    public void handle(Map data) {
                        trigger1.next();
                    }
                }).error(new FlowErrorHandler(trigger1) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        trigger1.next();
                    }
                }).start();
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                VpcVirtualRouteEntryInventory inventory = (VpcVirtualRouteEntryInventory)data.get("entry1");
                if (inventory == null) {
                    trigger1.next();
                    return;
                }
                DeleteAliyunVpcRouteEntryInnerMsg dremsg = new DeleteAliyunVpcRouteEntryInnerMsg();
                dremsg.setSession(msg.getSession());
                dremsg.setEntry(VpcVirtualRouteEntryInventory.valueOf(dbf.findByUuid(inventory.getUuid(), VpcVirtualRouteEntryVO.class)));

                bus.makeTargetServiceIdByResourceUuid(dremsg, AliyunNetworkConstant.SERVICE_ID, inventory.getUuid());
                bus.send(dremsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        trigger1.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data) {
                trigger.rollback();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                trigger.rollback();
            }
        }).start();
    }
}
