package org.zstack.aliyun.storage.disk;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

import java.util.List;


/**
 * Created by camile on 2017/7/20.
 */

public class SyncAliyunDiskRemoteMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private List<String> diskIds;
    private String izId;
    private String ecsId;

    public String getIzId() {
        return izId;
    }

    public void setIzId(String izId) {
        this.izId = izId;
    }

    public String getEcsId() {
        return ecsId;
    }

    public void setEcsId(String ecsId) {
        this.ecsId = ecsId;
    }

    public List<String> getDiskIds() {
        return diskIds;
    }

    public void setDiskIds(List<String> diskIds) {
        this.diskIds = diskIds;
    }
}