package org.zstack.aliyun.storage.disk;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.aliyun.core.AliyunUtils;
import org.zstack.core.Platform;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.AbstractService;
import org.zstack.header.aliyun.AliyunConstant;
import org.zstack.header.aliyun.ecs.EcsInstanceVO;
import org.zstack.header.aliyun.ecs.EcsInstanceVO_;
import org.zstack.header.aliyun.storage.disk.*;
import org.zstack.header.aliyun.storage.snapshot.AliyunSnapshotManager;
import org.zstack.header.aliyun.storage.snapshot.AliyunSnapshotVO;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.identityzone.IdentityZoneVO;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.hybrid.core.HybridCascadeAction;
import org.zstack.hybrid.core.HybridLicenseChecker;
import org.zstack.hybrid.core.HybridUtilsForAliyun;
import org.zstack.identity.AccountManager;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.TagManager;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.*;

import static org.zstack.utils.CollectionDSL.*;

/**
 * Created by camile on 2017/7/18.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class AliyunDiskManagerImpl extends AbstractService
        implements AliyunSnapshotManager, GlobalApiMessageInterceptor {

    private static final CLogger logger = Utils.getLogger(AliyunDiskManagerImpl.class);
    @Autowired
    private HybridLicenseChecker hyimpl;
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CascadeFacade casf;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    protected AccountManager acmgr;
    @Autowired
    private ThreadFacade thdf;

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APICreateAliyunDiskFromRemoteMsg) {
            handle((APICreateAliyunDiskFromRemoteMsg) msg);
        } else if (msg instanceof APIAttachAliyunDiskToEcsMsg) {
            handle((APIAttachAliyunDiskToEcsMsg) msg);
        } else if (msg instanceof APIDetachAliyunDiskFromEcsMsg) {
            handle((APIDetachAliyunDiskFromEcsMsg) msg);
        } else if (msg instanceof APIUpdateAliyunDiskMsg) {
            handle((APIUpdateAliyunDiskMsg) msg);
        } else if (msg instanceof APIDeleteAliyunDiskFromRemoteMsg) {
            handle((APIDeleteAliyunDiskFromRemoteMsg) msg);
        } else if (msg instanceof APISyncDiskFromAliyunFromRemoteMsg) {
            handle((APISyncDiskFromAliyunFromRemoteMsg) msg);
        } else if (msg instanceof APIDeleteAliyunDiskFromLocalMsg) {
            handle((APIDeleteAliyunDiskFromLocalMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(final APIDeleteAliyunDiskFromLocalMsg msg) {
        APIDeleteAliyunDiskFromLocalEvent evt = new APIDeleteAliyunDiskFromLocalEvent(msg.getId());
        final String issuer = AliyunDiskVO.class.getSimpleName();
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-aliyun-disk-local-%s", msg.getUuid()));
        final List<AliyunDiskInventory> ctx = list(
                AliyunDiskInventory.valueOf(dbf.findByUuid(msg.getUuid(), AliyunDiskVO.class)));
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                CascadeAction action = new HybridCascadeAction().
                        setRootIssuer(issuer).
                        setRootIssuerContext(ctx).
                        setParentIssuer(issuer).
                        setParentIssuerContext(ctx).
                        setActionCode(CascadeConstant.DELETION_DELETE_CODE).
                        setFullTraverse(true);
                ((HybridCascadeAction) action).setSession(msg.getSession());
                casf.asyncCascade(action, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(evt);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                evt.setError(errCode);
                bus.publish(evt);
            }
        }).start();
    }

    private void handle(final APISyncDiskFromAliyunFromRemoteMsg msg) {
        IdentityZoneVO izVO = dbf.findByUuid(msg.getIdentityUuid(),IdentityZoneVO.class) ;
        APISyncDiskFromAliyunFromRemoteEvent evt = new APISyncDiskFromAliyunFromRemoteEvent(msg.getId());
        SyncAliyunDiskRemoteMsg simsg = new SyncAliyunDiskRemoteMsg();
        simsg.setIzId(izVO.getZoneId());
        Optional.of(msg).map(APISyncDiskFromAliyunFromRemoteMsg::getDiskId).map(Arrays::asList).ifPresent(simsg::setDiskIds);
        simsg.setRegionId(HybridUtilsForAliyun.getRegionIdFromIdentityZoneUuid(msg.getIdentityUuid()));
        simsg.setAccountUuid(msg.getSession().getAccountUuid());
        Completion completion = new Completion(msg) {
            @Override
            public void success() {
                bus.publish(evt);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                evt.setError(errorCode);
                bus.publish(evt);
            }
        };
        bus.makeTargetServiceIdByResourceUuid(simsg, AliyunConstant.SERVICE_ID, msg.getIdentityUuid());
        bus.send(simsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    SyncAliyunDiskRemoteReply rpl = reply.castReply();
                    Map<String, List<AliyunDiskInventory>> result = syncAliyunDisks(rpl, izVO.getUuid(), msg.getSession().getAccountUuid());
                    List<AliyunDiskInventory> delList = result.get("del");
                    List<AliyunDiskInventory> allList = result.get("all");
                    evt.setInventories(allList);

                    final String issuer = AliyunDiskVO.class.getSimpleName();
                    if (delList.size() > 0) {
                        casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, delList, new Completion(completion) {
                            @Override
                            public void success() {
                                completion.success();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                            }
                        });
                    } else {
                        completion.success();
                    }
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    private Map<String, List<AliyunDiskInventory>> syncAliyunDisks(SyncAliyunDiskRemoteReply reply, String izUuid, String accountUuid) {
        List<AliyunDiskVO> vos = new ArrayList<>();
        Map<String, List<AliyunDiskInventory>> result = new HashMap<>();

        IdentityZoneVO iz = dbf.findByUuid(izUuid, IdentityZoneVO.class);
        vos.addAll(Q.New(AliyunDiskVO.class).eq(AliyunDiskVO_.identityZoneUuid, iz.getUuid()).list());

        List<AliyunDiskInventory> delList = new ArrayList<>();
        List<AliyunDiskInventory> allList = new ArrayList<>();

        List<String> addList = new ArrayList<>();
        CollectionUtils.subtract(vos, reply.getProperties()).forEach(collection -> delList.add((AliyunDiskInventory.valueOf((AliyunDiskVO) collection))));
        CollectionUtils.subtract(reply.getProperties(), vos).forEach(collection -> addList.add(((AliyunDiskProperty) collection).getDiskId()));

        for (AliyunDiskProperty property : reply.getProperties()) {
            AliyunDiskVO dvo = null;
            boolean exists = false;

            if (addList.contains(property.getDiskId())) {
                dvo = new AliyunDiskVO();
                dvo.setUuid(Platform.getUuid());
                dvo.setAccountUuid(accountUuid);
            } else {
                for (AliyunDiskVO vo : vos) {
                    if (vo.getDiskId().equals(property.getDiskId())) {
                        dvo = vo;
                        exists = true;
                        break;
                    }
                }

                if (!exists) {
                    continue;
                }
            }

            dvo.setDiskId(property.getDiskId());
            dvo.setIdentityZoneUuid(izUuid);
            dvo.setSizeWithGB(property.getSizeWithGB());
            dvo.setDiskCategory(AliyunDiskCategory.get(property.getDiskCategory()));
            if (StringUtils.isEmpty(property.getDiskChargeType())){
                //property has no charge type when init disk, so skip it
                continue;
            } else {
                dvo.setDiskChargeType(AliyunDiskChargeType.get(property.getDiskChargeType()));
            }
            dvo.setStatus(AliyunDiskStatus.get(property.getDiskStatus()));
            dvo.setName(StringUtils.isBlank(property.getName()) ? String.format("Sync-by-ZStack-%s",System.identityHashCode(property))
                    : property.getName());
            dvo.setDescription(property.getDescription());
            dvo.setDiskType(AliyunDiskType.get(property.getDiskType()));
            if (property.getEcsInstanceId() != null && !property.getEcsInstanceId().equals("")) {
                String ecsUuid = Q.New(EcsInstanceVO.class)
                        .select(EcsInstanceVO_.uuid)
                        .eq(EcsInstanceVO_.identityZoneUuid, izUuid)
                        .eq(EcsInstanceVO_.ecsInstanceId, property.getEcsInstanceId())
                        .findValue();
                if (ecsUuid == null) {
                    logger.warn(String.format("disk id[%s] attached by ecs id [%s] has been deleted from local, skip it",
                            property.getDiskId(), property.getEcsInstanceId()));
                    continue;
                }
                dvo.setEcsInstanceUuid(ecsUuid);
            }
            Optional.of(property).map(AliyunDiskProperty::getDeviceInfo).ifPresent(dvo::setDeviceInfo);
            Optional.of(property).map(AliyunDiskProperty::getCreateDate).map(HybridUtilsForAliyun::fmtTime).ifPresent(dvo::setCreateDate);
            if (!exists) {
                dvo = dbf.persistAndRefresh(dvo);
            } else {
                dvo = dbf.updateAndRefresh(dvo);
            }
            String diskUuid = dvo.getUuid();

            if (StringUtils.isNotBlank(property.getAttachDate()) && AliyunDiskSystemTags.INITIAL_DISK.getTag(diskUuid) == null) {
                SystemTagCreator creator = AliyunDiskSystemTags.INITIAL_DISK.newSystemTagCreator(diskUuid);
                creator.inherent = false;
                creator.recreate = true;
                creator.create();
            }

            if (property.isDeleteAutoSnapshot()) {
                if (AliyunDiskSystemTags.DISK_AUTO_DELETED.getTag(diskUuid) == null) {
                    SystemTagCreator creator = AliyunDiskSystemTags.DISK_AUTO_DELETED.newSystemTagCreator(diskUuid);
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                }
            } else {
                AliyunDiskSystemTags.DISK_AUTO_DELETED.delete(diskUuid);
            }

            if (property.isDeleteWithInstance()) {
                if (AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.getTag(diskUuid) == null) {
                    SystemTagCreator creator = AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.newSystemTagCreator(diskUuid);
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                }
            } else {
                AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.delete(diskUuid);
            }

            if (property.isEnableAutoSnapshot()) {
                if (AliyunDiskSystemTags.DISK_AUTO_ENABLE.getTag(diskUuid) == null) {
                    SystemTagCreator creator = AliyunDiskSystemTags.DISK_AUTO_ENABLE.newSystemTagCreator(diskUuid);
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                }
            } else {
                AliyunDiskSystemTags.DISK_AUTO_ENABLE.delete(diskUuid);
            }

            if (property.isPortable()) {
                if (AliyunDiskSystemTags.DISK_PORTABLE.getTag(diskUuid) == null) {
                    SystemTagCreator creator = AliyunDiskSystemTags.DISK_PORTABLE.newSystemTagCreator(diskUuid);
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                }
            } else {
                AliyunDiskSystemTags.DISK_PORTABLE.delete(diskUuid);
            }

            if (property.getOperationLockReasons() != null) {
                if (AliyunDiskSystemTags.DISK_OPERATION_LOCK_REASONS.getTag(diskUuid) != null) {
                    SystemTagCreator creator = AliyunDiskSystemTags.DISK_OPERATION_LOCK_REASONS.newSystemTagCreator(diskUuid);
                    creator.setTagByTokens(map(e(AliyunDiskSystemTags.OPERATION_LOCK_REASONS_TOKEN, property.getOperationLockReasons())));
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                } else {
                    AliyunDiskSystemTags.DISK_OPERATION_LOCK_REASONS.update(
                            diskUuid, AliyunDiskSystemTags.DISK_OPERATION_LOCK_REASONS.instantiateTag(
                                    map(e(AliyunDiskSystemTags.OPERATION_LOCK_REASONS_TOKEN, property.getOperationLockReasons()))));
                }
            } else {
                AliyunDiskSystemTags.DISK_OPERATION_LOCK_REASONS.delete(diskUuid);
            }

            allList.add(AliyunDiskInventory.valueOf(dvo));
        }

        result.put("del", delList);
        result.put("all", allList);
        return result;
    }

    private void handle(final APIDeleteAliyunDiskFromRemoteMsg msg) {
        APIDeleteAliyunDiskFromRemoteEvent evt = new APIDeleteAliyunDiskFromRemoteEvent(msg.getId());
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-disk-remote-%s", msg.getUuid()));
        final String issuer = AliyunDiskVO.class.getSimpleName();
        final List<AliyunDiskInventory> ctx = list(AliyunDiskInventory.valueOf(dbf.findByUuid(msg.getUuid(), AliyunDiskVO.class)));
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                CascadeAction action = new HybridCascadeAction().
                        setRootIssuer(issuer).
                        setRootIssuerContext(ctx).
                        setParentIssuer(issuer).
                        setParentIssuerContext(ctx).
                        setActionCode(CascadeConstant.DELETION_CHECK_CODE).
                        setFullTraverse(true);
                ((HybridCascadeAction) action).setSession(msg.getSession());
                casf.asyncCascade(action, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                CascadeAction action = new HybridCascadeAction().
                        setRootIssuer(issuer).
                        setRootIssuerContext(ctx).
                        setParentIssuer(issuer).
                        setParentIssuerContext(ctx).
                        setActionCode(AliyunDiskConstant.DELETE_ACTION_FROM_MYSELF).
                        setFullTraverse(true);
                ((HybridCascadeAction) action).setSession(msg.getSession());
                casf.asyncCascade(action, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                CascadeAction action = new HybridCascadeAction().
                        setRootIssuer(issuer).
                        setRootIssuerContext(ctx).
                        setParentIssuer(issuer).
                        setParentIssuerContext(ctx).
                        setActionCode(CascadeConstant.DELETION_DELETE_CODE).
                        setFullTraverse(true);
                ((HybridCascadeAction) action).setSession(msg.getSession());
                casf.asyncCascade(action, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(evt);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                evt.setError(errCode);
                bus.publish(evt);
            }
        }).start();
    }

    private void handle(final APIUpdateAliyunDiskMsg msg) {
        APIUpdateAliyunDiskEvent event = new APIUpdateAliyunDiskEvent(msg.getId());
        AliyunDiskVO vo = dbf.findByUuid(msg.getUuid(), AliyunDiskVO.class);
        UpdateAliyunDiskRemoteMsg umsg = new UpdateAliyunDiskRemoteMsg();
        umsg.setDiskId(vo.getDiskId());
        umsg.setRegionId(HybridUtilsForAliyun.getRegionIdFromIdentityZoneUuid(vo.getIdentityZoneUuid()));
        umsg.setAccountUuid(AliyunUtils.getAccountUuid(msg.getSession()));
        Optional.of(msg).map(APIUpdateAliyunDiskMsg::getName).ifPresent(umsg::setName);
        Optional.of(msg).map(APIUpdateAliyunDiskMsg::getDescription).ifPresent(umsg::setDescription);
        Optional.of(msg).map(APIUpdateAliyunDiskMsg::isDeleteWithInstance).ifPresent(umsg::setDeleteWithInstance);
        Optional.of(msg).map(APIUpdateAliyunDiskMsg::isDeleteAutoSnapshot).ifPresent(umsg::setDeleteAutoSnapshot);
        Optional.of(msg).map(APIUpdateAliyunDiskMsg::isEnableAutoSnapshot).ifPresent(umsg::setEnableAutoSnapshot);

        Optional.of(msg).map(APIUpdateAliyunDiskMsg::getName).ifPresent(vo::setName);
        Optional.of(msg).map(APIUpdateAliyunDiskMsg::getDescription).ifPresent(vo::setDescription);

        bus.makeTargetServiceIdByResourceUuid(umsg, AliyunConstant.SERVICE_ID, msg.getUuid());
        bus.send(umsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    dbf.updateAndRefresh(vo);
                    if (BooleanUtils.isTrue(msg.isDeleteAutoSnapshot())
                            && AliyunDiskSystemTags.DISK_AUTO_DELETED.getTag(msg.getUuid()) == null){

                                SystemTagCreator creator = AliyunDiskSystemTags.DISK_AUTO_DELETED.newSystemTagCreator(msg.getUuid());
                                creator.inherent = false;
                                creator.recreate = true;
                                creator.create();
                        }

                    if (BooleanUtils.isFalse(msg.isDeleteAutoSnapshot())){
                            AliyunDiskSystemTags.DISK_AUTO_DELETED.delete(msg.getUuid());
                        }

                    if (BooleanUtils.isTrue(msg.isDeleteWithInstance())
                            && AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.getTag(msg.getUuid()) == null){
                                SystemTagCreator creator = AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.newSystemTagCreator(msg.getUuid());
                                creator.inherent = false;
                                creator.recreate = true;
                                creator.create();
                        }

                    if (BooleanUtils.isFalse(msg.isDeleteWithInstance())){
                        AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.delete(msg.getUuid());
                    }

                    if (BooleanUtils.isTrue(msg.isEnableAutoSnapshot())
                            && AliyunDiskSystemTags.DISK_AUTO_ENABLE.getTag(msg.getUuid()) == null){
                        SystemTagCreator creator = AliyunDiskSystemTags.DISK_AUTO_ENABLE.newSystemTagCreator(msg.getUuid());
                        creator.inherent = false;
                        creator.recreate = true;
                        creator.create();
                    }

                    if (BooleanUtils.isFalse(msg.isEnableAutoSnapshot())){
                        AliyunDiskSystemTags.DISK_AUTO_ENABLE.delete(msg.getUuid());
                    }

                    event.setInventory(AliyunDiskInventory.valueOf(vo));
                } else {
                    event.setError(reply.getError());
                }
                bus.publish(event);
            }
        });
    }

    private void detachAliyunDisk(final APIDetachAliyunDiskFromEcsMsg msg, final AliyunDiskVO diskVO, final SyncTaskChain taskChain) {
        APIDetachAliyunDiskFromEcsEvent event = new APIDetachAliyunDiskFromEcsEvent(msg.getId());
        EcsInstanceVO ecsVo = dbf.findByUuid(diskVO.getEcsInstanceUuid(), EcsInstanceVO.class);
        DetachAliyunDiskFromEcsMsg dmsg = new DetachAliyunDiskFromEcsMsg();
        dmsg.setAccountUuid(AliyunUtils.getAccountUuid(msg.getSession()));
        dmsg.setEcsId(ecsVo.getEcsInstanceId());
        dmsg.setRegionId(HybridUtilsForAliyun.getRegionIdFromIdentityZoneUuid(ecsVo.getIdentityZoneUuid()));
        dmsg.setDiskId(diskVO.getDiskId());
        bus.makeTargetServiceIdByResourceUuid(dmsg, AliyunConstant.SERVICE_ID, diskVO.getEcsInstanceUuid());
        bus.send(dmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    diskVO.setEcsInstanceUuid(null);
                    diskVO.setStatus(AliyunDiskStatus.Available);
                    AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.delete(diskVO.getUuid());
                    dbf.update(diskVO);
                } else {
                    event.setError(reply.getError());
                }
                bus.publish(event);
                taskChain.next();
            }
        });
    }

    private void handle(final APIDetachAliyunDiskFromEcsMsg msg) {
        AliyunDiskVO diskVO = dbf.findByUuid(msg.getUuid(), AliyunDiskVO.class);
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                detachAliyunDisk(msg, diskVO, chain);
            }

            @Override
            public String getName() {
                return String.format("detach-aliyun-disk-from-ecs-%s", diskVO.getEcsInstanceUuid());
            }
        });
    }

    private void createSystemTagsAfterAttachDisk(final APIAttachAliyunDiskToEcsMsg msg) {
        if (msg.isDeleteWithInstance()) {
            SystemTagCreator creator = AliyunDiskSystemTags.DISK_DELETE_WITH_INSTANCE.newSystemTagCreator(msg.getDiskUuid());
            creator.inherent = false;
            creator.recreate = true;
            creator.create();
        }
        if (AliyunDiskSystemTags.INITIAL_DISK.getTag(msg.getDiskUuid()) == null) {
            SystemTagCreator creator = AliyunDiskSystemTags.INITIAL_DISK.newSystemTagCreator(msg.getDiskUuid());
            creator.inherent = false;
            creator.recreate = true;
            creator.create();
        }
    }

    private void attachAliyunDisk(final APIAttachAliyunDiskToEcsMsg msg, final SyncTaskChain taskChain) {
        APIAttachAliyunDiskToEcsEvent event = new APIAttachAliyunDiskToEcsEvent(msg.getId());
        EcsInstanceVO vo = dbf.findByUuid(msg.getEcsUuid(), EcsInstanceVO.class);
        String ecsUuid = msg.getEcsUuid();
        AttachAliyunDiskToEcsMsg amsg = new AttachAliyunDiskToEcsMsg();
        amsg.setAccountUuid(AliyunUtils.getAccountUuid(msg.getSession()));
        amsg.setEcsId(vo.getEcsInstanceId());
        amsg.setRegionId(HybridUtilsForAliyun.getRegionIdFromIdentityZoneUuid(vo.getIdentityZoneUuid()));
        AliyunDiskVO diskVO = dbf.findByUuid(msg.getDiskUuid(), AliyunDiskVO.class);
        amsg.setDiskId(diskVO.getDiskId());
        amsg.setDeleteWithInstance(msg.isDeleteWithInstance());
        bus.makeTargetServiceIdByResourceUuid(amsg, AliyunConstant.SERVICE_ID, ecsUuid);
        bus.send(amsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    diskVO.setEcsInstanceUuid(ecsUuid);
                    diskVO.setStatus(AliyunDiskStatus.In_use);
                    createSystemTagsAfterAttachDisk(msg);
                    dbf.updateAndRefresh(diskVO);
                    event.setInventory((AliyunDiskInventory.valueOf(diskVO)));
                } else {
                    event.setError(reply.getError());

                }
                bus.publish(event);
                taskChain.next();
            }
        });
    }

    private void handle(final APIAttachAliyunDiskToEcsMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                attachAliyunDisk(msg, chain);
            }

            @Override
            public String getName() {
                return String.format("attach-aliyun-disk-to-ecs-%s", msg.getEcsUuid());
            }
        });
    }


    private void handle(final APICreateAliyunDiskFromRemoteMsg msg) {
        APICreateAliyunDiskFromRemoteEvent event = new APICreateAliyunDiskFromRemoteEvent(msg.getId());
        CreateAliyunDiskRemoteMsg cmsg = new CreateAliyunDiskRemoteMsg();
        cmsg.setAccountUuid(AliyunUtils.getAccountUuid(msg.getSession()));
        cmsg.setRegionId(HybridUtilsForAliyun.getRegionIdFromIdentityZoneUuid(msg.getIdentityUuid()));
        cmsg.setZoneId(dbf.findByUuid(msg.getIdentityUuid(), IdentityZoneVO.class).getZoneId());
        Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getSizeWithGB).ifPresent(cmsg::setSize);
        Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getName).ifPresent(cmsg::setName);
        Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getDescription).ifPresent(cmsg::setDescription);
        Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getDiskCategory).ifPresent(category ->
                cmsg.setDiskCategory(AliyunDiskCategory.get(category)));
        Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getSnapshotUuid)
                .map(uuid -> dbf.findByUuid(uuid, AliyunSnapshotVO.class).getSnapshotId()).ifPresent(cmsg::setSnapshotId);
        bus.makeTargetServiceIdByResourceUuid(cmsg, AliyunConstant.SERVICE_ID, msg.getIdentityUuid());
        bus.send(cmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    AliyunDiskVO vo = new AliyunDiskVO();
                    String uuid = msg.getResourceUuid() != null ? msg.getResourceUuid() : Platform.getUuid();
                    vo.setUuid(uuid);
                    Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getSizeWithGB).ifPresent(vo::setSizeWithGB);
                    Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getName).ifPresent(vo::setName);
                    Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getDescription).ifPresent(vo::setDescription);
                    Optional.of(msg).map(APICreateAliyunDiskFromRemoteMsg::getDiskCategory).ifPresent(category -> vo.setDiskCategory(AliyunDiskCategory.get(category)));
                    vo.setIdentityZoneUuid(msg.getIdentityUuid());
                    vo.setDiskId(((CreateAliyunDiskRemoteReply) reply).getDiskId());
                    vo.setDiskType(AliyunDiskType.data);
                    vo.setDiskChargeType(AliyunDiskChargeType.PostPaid);
                    vo.setStatus(AliyunDiskStatus.Available);
                    vo.setAccountUuid(msg.getSession().getAccountUuid());
                    dbf.persistAndRefresh(vo);
                    SystemTagCreator creator = AliyunDiskSystemTags.DISK_AUTO_DELETED.newSystemTagCreator(vo.getUuid());
                    creator.inherent = false;
                    creator.recreate = true;
                    creator.create();
                    SystemTagCreator creator2 = AliyunDiskSystemTags.DISK_PORTABLE.newSystemTagCreator(vo.getUuid());
                    creator2.inherent = true;
                    creator2.recreate = true;
                    creator2.create();
                    event.setInventory(AliyunDiskInventory.valueOf(vo));
                    bus.publish(event);
                } else {
                    event.setError(reply.getError());
                    bus.publish(event);
                }
            }
        });
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof SyncAliyunDiskInnerMsg) {
            handle((SyncAliyunDiskInnerMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(final SyncAliyunDiskInnerMsg msg) {
        SyncAliyunDiskInnerReply sreply = new SyncAliyunDiskInnerReply();

        IdentityZoneVO ivo = dbf.findByUuid(msg.getIzUuid(), IdentityZoneVO.class);
        String regionId = HybridUtilsForAliyun.getRegionIdFromDcUuid(ivo.getDataCenterUuid());
        SyncAliyunDiskRemoteMsg simsg = new SyncAliyunDiskRemoteMsg();
        Optional.of(msg).map(SyncAliyunDiskInnerMsg::getDiskId).map(Arrays::asList).ifPresent(simsg::setDiskIds);
        simsg.setIzId(ivo.getZoneId());
        simsg.setRegionId(regionId);
        simsg.setAccountUuid(msg.getSession().getAccountUuid());
        Completion completion = new Completion(msg) {
            @Override
            public void success() {
                bus.reply(msg, sreply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                sreply.setError(errorCode);
                bus.reply(msg, sreply);
            }
        };
        bus.makeTargetServiceIdByResourceUuid(simsg, AliyunConstant.SERVICE_ID, msg.getIzUuid());
        bus.send(simsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    SyncAliyunDiskRemoteReply rpl = reply.castReply();
                    Map<String, List<AliyunDiskInventory>> result = syncAliyunDisks(rpl, msg.getIzUuid(), msg.getSession().getAccountUuid());
                    List<AliyunDiskInventory> delList = result.get("del");
                    List<AliyunDiskInventory> allList = result.get("all");
                    sreply.setInventories(allList);

                    final String issuer = AliyunDiskVO.class.getSimpleName();
                    if (delList.size() > 0) {
                        final List<AliyunDiskInventory> ctx = delList;
                        casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(completion) {
                            @Override
                            public void success() {
                                completion.success();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                            }
                        });
                    } else {
                        completion.success();
                    }
                }else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(AliyunDiskConstant.SERVICE_ID);
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        return Arrays.asList(APICreateAliyunDiskFromRemoteMsg.class, APIAttachAliyunDiskToEcsMsg.class,
                APIDetachAliyunDiskFromEcsMsg.class, APIUpdateAliyunDiskMsg.class,
                APIDeleteAliyunDiskFromRemoteMsg.class, APISyncDiskFromAliyunFromRemoteMsg.class,
                APIDeleteAliyunDiskFromLocalMsg.class);
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.FRONT;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        hyimpl.intercept(msg);
        return msg;
    }
}
