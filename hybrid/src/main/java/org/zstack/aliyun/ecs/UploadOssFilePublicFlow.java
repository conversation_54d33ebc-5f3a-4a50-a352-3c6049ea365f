package org.zstack.aliyun.ecs;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.aliyun.image.EcsImageSystemTags;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.header.aliyun.AliyunConstant;
import org.zstack.header.aliyun.AliyunErrors;
import org.zstack.header.aliyun.image.CreateEcsImageInnerMsg;
import org.zstack.header.aliyun.image.EcsImageConstant;
import org.zstack.header.aliyun.oss.*;
import org.zstack.header.core.workflow.Flow;
import org.zstack.header.core.workflow.FlowRollback;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.message.MessageReply;
import org.zstack.hybrid.core.HybridUtilsForAliyun;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.Map;

/**
 * Created by mingjian.deng on 17/2/20.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class UploadOssFilePublicFlow implements Flow {
    private static final CLogger logger = Utils.getLogger(UploadOssFilePublicFlow.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;

    private void updateStage(String stage, String imageUuid) {
        EcsImageSystemTags.IMAGE_IMPORTING.updateTagByToken(imageUuid, EcsImageSystemTags.IMAGE_IMPORTING_STAGE, stage);
    }

    @Override
    public void run(FlowTrigger trigger, Map data) {
        String oss_remote = (String)data.get(AliyunOssConstant.OSS_FILE_REMOTE_EXISTED);
        CreateEcsImageInnerMsg msg = (CreateEcsImageInnerMsg)data.get(EcsImageConstant.CREATE_ECS_IMAGE_MESSAGE);
        updateStage(AliyunOssConstant.OSS_UPLOAD_PROGRESS, msg.getImageUuid());

        if (oss_remote != null && oss_remote.equals("existed")) {
            logger.debug("image existed or oss file existed, skip this flow...");
            trigger.next();
            return;
        }

        String dcUuid = msg.getDataCenterUuid();
        String regionId = HybridUtilsForAliyun.getRegionIdFromDcUuid(dcUuid);

        // upload...
        UploadOssFileMsg omsg = new UploadOssFileMsg();
        omsg.setMulti(true);
        omsg.setRegionId(regionId);
        omsg.setAccountUuid(msg.getSession().getAccountUuid());
        omsg.setBucketName((String)data.get(AliyunOssConstant.OSS_BUCKET_NAME_CTXT));
        String ossFileKey = (String)data.get(AliyunOssConstant.OSS_OBJECT_FILE_KEY);
        omsg.setOssFileKey(ossFileKey);
        omsg.setUrl((String)data.get(AliyunOssConstant.OSS_FILE_LOCAL_URL));
        omsg.setMd5sum((String)data.get(AliyunOssConstant.OSS_FILE_MD5_SUM));
        OssBucketVO obvo = Q.New(OssBucketVO.class).eq(OssBucketVO_.dataCenterUuid, dcUuid).eq(OssBucketVO_.current, "true").find();
        if (obvo == null) {
            throw new OperationFailureException(Platform.err(AliyunErrors.NO_OSS_BUCKET_ATTACHED_TO_DATACENTER,
                    String.format("couldn't find oss bucket in datacenter uuid:[%s]", dcUuid)));
        }
        omsg.setOssBucketUuid(obvo.getUuid());

        bus.makeTargetServiceIdByResourceUuid(omsg, AliyunConstant.OSS_SERVICE_ID, msg.getImageUuid());
        bus.send(omsg, new CloudBusCallBack(trigger) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    UploadOssFileReply rpl = reply.castReply();
                    deleteUploadVO(msg.getImageUuid(), dcUuid);
                    data.put(AliyunOssConstant.OSS_FILE_MD5_SUM, rpl.getOssMd5Sum());
                    trigger.next();
                } else {
                    trigger.fail(reply.getError());
                }
            }
        });
    }

    @Override
    public void rollback(FlowRollback trigger, Map data) {
        if (data.get("need_delete") != null) {
            CreateEcsImageInnerMsg msg = (CreateEcsImageInnerMsg)data.get(EcsImageConstant.CREATE_ECS_IMAGE_MESSAGE);
            String dcUuid = msg.getDataCenterUuid();
            String regionId = HybridUtilsForAliyun.getRegionIdFromDcUuid(dcUuid);
            DeleteOssBucketFileMsg odmsg = new DeleteOssBucketFileMsg();
            odmsg.setRegionId(regionId);
            odmsg.setAccountUuid(msg.getSession().getAccountUuid());
            odmsg.setBucketName((String) data.get(AliyunOssConstant.OSS_BUCKET_NAME_CTXT));
            odmsg.setOssFileKey((String) data.get(AliyunOssConstant.OSS_OBJECT_FILE_KEY));
            bus.makeTargetServiceIdByResourceUuid(odmsg, AliyunConstant.OSS_SERVICE_ID, msg.getImageUuid());
            bus.send(odmsg, new CloudBusCallBack(trigger) {
                @Override
                public void run(MessageReply reply) {
                    trigger.rollback();
                }
            });
        } else {
            trigger.rollback();
        }
    }

    private void deleteUploadVO(String imageUuid, String dcUuid) {
        OssBucketVO obvo = Q.New(OssBucketVO.class).eq(OssBucketVO_.dataCenterUuid, dcUuid).eq(OssBucketVO_.current, "true").find();
        if (obvo != null) {
            SQL.New("delete from OssUploadPartsVO where uploadId = :id and ossBucketUuid = :oss").
                    param("id", imageUuid).param("oss", obvo.getUuid()).execute();
        }
    }
}
