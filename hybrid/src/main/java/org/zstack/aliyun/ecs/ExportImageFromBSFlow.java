package org.zstack.aliyun.ecs;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.aliyun.core.AliyunGlobalConfig;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.aliyun.AliyunConstant;
import org.zstack.header.aliyun.AliyunErrors;
import org.zstack.header.aliyun.image.CreateEcsImageInnerMsg;
import org.zstack.header.aliyun.image.EcsImageConstant;
import org.zstack.header.aliyun.oss.*;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.image.ImageVO;
import org.zstack.header.message.MessageReply;
import org.zstack.header.storage.backup.BackupStorageConstant;
import org.zstack.header.storage.backup.ExportImageFromBackupStorageMsg;
import org.zstack.header.storage.backup.ExportImageFromBackupStorageReply;
import org.zstack.hybrid.core.HybridUtilsForAliyun;
import org.zstack.storage.backup.imagestore.DeleteExportedImageFromImageStoreBackupStorageMsg;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.Map;

/**
 * Created by mingjian.deng on 17/2/21.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class ExportImageFromBSFlow implements Flow {
    private static final CLogger logger = Utils.getLogger(ExportImageFromBSFlow.class);
    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;

    @Override
    public void run(FlowTrigger trigger, Map data) {
        CreateEcsImageInnerMsg msg = (CreateEcsImageInnerMsg)data.get(EcsImageConstant.CREATE_ECS_IMAGE_MESSAGE);
        String regionId = HybridUtilsForAliyun.getRegionIdFromDcUuid(msg.getDataCenterUuid());
        String ossBucket = (String)data.get(AliyunOssConstant.OSS_BUCKET_NAME_CTXT);
        ImageVO ivo = dbf.findByUuid(msg.getImageUuid(), ImageVO.class);
        if (ivo == null) {
            throw new OperationFailureException(Platform.operr("image has been deleted!"));
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("export-local-image");
        chain.then(new Flow() {
            String __name__ = "export local image";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                ExportImageFromBackupStorageMsg eimsg = new ExportImageFromBackupStorageMsg();
                eimsg.setExportFormat(AliyunGlobalConfig.ALIYUN_IMAGE_UPLOAD_FORMAT.value(String.class));
                eimsg.setImageUuid(msg.getImageUuid());
                eimsg.setBackupStorageUuid(msg.getBackupStorageUuid());

                bus.makeTargetServiceIdByResourceUuid(eimsg, BackupStorageConstant.SERVICE_ID, eimsg.getImageUuid());
                bus.send(eimsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            ExportImageFromBackupStorageReply rpl = reply.castReply();
                            data.put(AliyunOssConstant.OSS_FILE_LOCAL_URL, rpl.getImageUrl());
                            data.put(AliyunOssConstant.OSS_FILE_MD5_SUM, rpl.getMd5sum());
                            data1.put("imagelocalpath", rpl.getImageLocalPath());
                            logger.debug(String.format("export image path: %s", rpl.getImageUrl()));
                            trigger1.next();
                        } else {
                            trigger1.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger1, Map data1) {
                deleteUploadVO(msg.getImageUuid(), msg.getDataCenterUuid());
                trigger1.rollback();
            }
        });
        /**
         * the tools must be used in guest now, so we don't use it
         */
//        chain.then(new NoRollbackFlow() {
//            String __name__ = "use tool from aliyun to check whether image is valid";
//            @Override
//            public void run(FlowTrigger trigger, Map data1) {
//                if (!AliyunGlobalConfig.CHECK_IMAGE_BEFORE_UPLOAD.value(Boolean.class)) {
//                    trigger.next();
//                    return;
//                }
//                String imageUrl = (String)data1.get("imagelocalpath");
//                String toolPath = PathUtil.findFileOnClassPath("tools/checkaliyunimage", true).getAbsolutePath();
//                String scriptsPath = ShellUtils.getScriptsPath("scripts/checkimageforaliyun.sh");
//                String cmdStr = String.format("bash %s %s %s", scriptsPath, imageUrl, toolPath);
//                ShellResult res = ShellUtils.runAndReturn(cmdStr);
//                if (res.getRetCode() == 0) {
//                    trigger.next();
//                } else if (res.getRetCode() == 2) {
//                    trigger.fail(Platform.operr("image is not valid for aliyun, check result bellow: %s", res.getStdout()));
//                } else if (res.getRetCode() == 3) {
//                    logger.warn("there are some warnings in the image, but we let it go");
//                    logger.info(res.getStdout());
//                    trigger.next();
//                } else {
//                    trigger.fail(Platform.operr(res.getStderr()));
//                }
//            }
//        });
        chain.then(new NoRollbackFlow() {
            String __name__ = "check md5 sum remote";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                DescribeOssFileRemoteMsg domsg = new DescribeOssFileRemoteMsg();
                domsg.setAccountUuid(msg.getSession().getAccountUuid());
                domsg.setRegionId(regionId);
                domsg.setBucketName(ossBucket);
                domsg.setOssMd5Sum((String)data.get(AliyunOssConstant.OSS_FILE_MD5_SUM));

                bus.makeTargetServiceIdByResourceUuid(domsg, AliyunConstant.OSS_SERVICE_ID, msg.getImageUuid());
                bus.send(domsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            DescribeOssFileRemoteReply rpl = reply.castReply();
                            if (rpl.isExisted()) {
                                data.put(AliyunOssConstant.OSS_FILE_REMOTE_EXISTED, "existed");
                                data.put(AliyunOssConstant.OSS_OBJECT_FILE_KEY, rpl.getFiles().get(0));
                                updateUploadVO(msg.getImageUuid(), msg.getDataCenterUuid(), rpl.getFiles().get(0));
                            } else {
                                String extFormat = AliyunGlobalConfig.ALIYUN_IMAGE_UPLOAD_FORMAT.value(String.class);
                                data.put(AliyunOssConstant.OSS_OBJECT_FILE_KEY, String.format("%s%s.%s", AliyunOssConstant.OSS_OBJECT_PREFIX, msg.getImageUuid(), extFormat));
                            }
                            trigger1.next();
                        } else {
                            trigger1.fail(reply.getError());
                        }
                    }
                });
            }
        }).done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data1) {
                trigger.next();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data1) {
                trigger.fail(errCode);
            }
        }).start();
    }

    private void updateUploadVO(String imageUuid, String dcUuid, String file) {
        OssBucketVO obvo = Q.New(OssBucketVO.class).eq(OssBucketVO_.dataCenterUuid, dcUuid).eq(OssBucketVO_.current, "true").find();
        if (obvo == null) {
            throw new OperationFailureException(Platform.err(AliyunErrors.NO_OSS_BUCKET_ATTACHED_TO_DATACENTER,
                    String.format("couldn't find oss bucket in datacenter uuid:[%s]", dcUuid)));
        }
        OssUploadPartsVO vo = Q.New(OssUploadPartsVO.class).
                eq(OssUploadPartsVO_.uploadId, imageUuid).
                eq(OssUploadPartsVO_.ossBucketUuid, obvo.getUuid()).find();
        if (vo != null) {
            vo.setFileKey(file);
            dbf.updateAndRefresh(vo);
        }
    }

    private void deleteUploadVO(String imageUuid, String dcUuid) {
        OssBucketVO obvo = Q.New(OssBucketVO.class).eq(OssBucketVO_.dataCenterUuid, dcUuid).eq(OssBucketVO_.current, "true").find();
        if (obvo != null) {
            SQL.New("delete from OssUploadPartsVO where uploadId = :id and ossBucketUuid = :oss").
                    param("id", imageUuid).param("oss", obvo.getUuid()).execute();
        }
    }

    @Override
    public void rollback(FlowRollback trigger, Map data) {
        CreateEcsImageInnerMsg msg = (CreateEcsImageInnerMsg)data.get(EcsImageConstant.CREATE_ECS_IMAGE_MESSAGE);
        deleteUploadVO(msg.getImageUuid(), msg.getDataCenterUuid());

        if (data.get(AliyunOssConstant.OSS_FILE_LOCAL_URL) != null) {
            DeleteExportedImageFromImageStoreBackupStorageMsg demsg = new DeleteExportedImageFromImageStoreBackupStorageMsg();
            demsg.setImageUuid(msg.getImageUuid());
            demsg.setBackupStorageUuid(msg.getBackupStorageUuid());
//            demsg.setRawPath((String)data.get(AliyunOssConstant.OSS_FILE_LOCAL_URL));
            demsg.setExportFormat("raw");
            bus.makeTargetServiceIdByResourceUuid(demsg, BackupStorageConstant.SERVICE_ID, demsg.getImageUuid());
            bus.send(demsg, new CloudBusCallBack(trigger) {
                @Override
                public void run(MessageReply reply) {
                    trigger.rollback();
                }
            });
        } else {
            trigger.rollback();
        }
    }
}
