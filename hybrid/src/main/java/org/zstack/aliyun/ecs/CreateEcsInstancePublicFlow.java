package org.zstack.aliyun.ecs;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.aliyun.storage.disk.AliyunDiskProperty;
import org.zstack.aliyun.storage.disk.SyncAliyunDiskRemoteMsg;
import org.zstack.aliyun.storage.disk.SyncAliyunDiskRemoteReply;
import org.zstack.aliyun.storage.disk.UpdateAliyunDiskRemoteMsg;
import org.zstack.core.Platform;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.db.Q;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.aliyun.AliyunChargeType;
import org.zstack.header.aliyun.AliyunConstant;
import org.zstack.header.aliyun.AliyunErrors;
import org.zstack.header.aliyun.ecs.*;
import org.zstack.header.aliyun.image.EcsImageConstant;
import org.zstack.header.aliyun.image.EcsImageInventory;
import org.zstack.header.aliyun.image.EcsImageVO;
import org.zstack.header.aliyun.image.EcsImageVO_;
import org.zstack.header.aliyun.network.group.EcsSecurityGroupVO;
import org.zstack.header.aliyun.network.vpc.EcsVSwitchVO;
import org.zstack.header.aliyun.storage.disk.*;
import org.zstack.header.configuration.InstanceOfferingVO;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.identityzone.IdentityZoneVO;
import org.zstack.header.message.MessageReply;
import org.zstack.hybrid.core.HybridCascadeAction;
import org.zstack.hybrid.core.HybridUtilsForAliyun;
import org.zstack.identity.AccountManager;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.*;
import java.util.stream.Collectors;

import static org.zstack.utils.CollectionDSL.*;

/**
 * Created by mingjian.deng on 17/2/20.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class CreateEcsInstancePublicFlow implements Flow {
    private static final CLogger logger = Utils.getLogger(CreateEcsInstancePublicFlow.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;
    @Autowired
    private CascadeFacade casf;
    @Autowired
    protected AccountManager acmgr;

    private void arrangeType(List<EcsInstanceType> list) {
        list.sort(new ecsComparator());
    }

    class ecsComparator implements Comparator {
        public int compare(Object o1, Object o2) {
            EcsInstanceType type1 = (EcsInstanceType) o1;
            EcsInstanceType type2 = (EcsInstanceType) o2;
            if (type1.getCpu() * type1.getMemory() <= type2.getCpu() * type2.getMemory()) {
                if (type1.getCpu() <= type2.getCpu()) {
                    return -1;
                }
                return 1;
            } else {
                return 1;
            }
        }
    }

    EcsInstanceVO getEcsInstanceVO(EcsInstanceInventory inventory) {
        EcsInstanceVO vo = new EcsInstanceVO();
        vo.setUuid(inventory.getUuid());
        if (inventory.getLocalVmInstanceUuid() != null) {
            vo.setLocalVmInstanceUuid(inventory.getLocalVmInstanceUuid());
        }
        vo.setEcsInstanceId(inventory.getEcsInstanceId());
        vo.setName(inventory.getName());
        vo.setEcsInstanceRootPassword(inventory.getEcsInstanceRootPassword());
        vo.setCpuCores(inventory.getCpuCores());
        vo.setMemorySize(inventory.getMemorySize());
        vo.setEcsInstanceType(inventory.getEcsInstanceType());
        vo.setEcsBandWidth(inventory.getEcsBandWidth());
        vo.setEcsRootVolumeId(inventory.getEcsRootVolumeId());
        vo.setEcsRootVolumeCategory(inventory.getEcsRootVolumeCategory());
        vo.setEcsRootVolumeSize(inventory.getEcsRootVolumeSize());
        vo.setEcsVSwitchUuid(inventory.getEcsVSwitchUuid());
        vo.setEcsImageUuid(inventory.getEcsImageUuid());
        vo.setEcsSecurityGroupUuid(inventory.getEcsSecurityGroupUuid());
        vo.setIdentityZoneUuid(inventory.getIdentityZoneUuid());
        vo.setDescription(inventory.getDescription());
        vo.setPrivateIpAddress(inventory.getPrivateIpAddress());
        vo.setEcsStatus(EcsStatus.STOPPED);
        vo.setChargeType(AliyunChargeType.get(inventory.getChargeType()));
        vo.setExpireDate(inventory.getExpireDate());

        return vo;
    }

    @Override
    public void run(FlowTrigger trigger, Map data) {
        EcsInstanceInventory ecs = (EcsInstanceInventory) data.get(EcsInstanceConstant.ECS_INSTANCE_SPEC);
        CreateEcsInnerMsg msg = (CreateEcsInnerMsg) data.get(EcsInstanceConstant.CREATE_ECS_MESSAGE);
        String dcUuid = HybridUtilsForAliyun.getDataCenterUuidByIzoneUuid(ecs.getIdentityZoneUuid());
        String regionId = HybridUtilsForAliyun.getRegionIdFromDcUuid(dcUuid);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("create-ecs-instance-%s", ecs.getUuid()));

        if (msg.getInstanceType() == null) {
            InstanceOfferingVO vo = dbf.findByUuid(msg.getInstanceOfferingUuid(), InstanceOfferingVO.class);
            if (vo == null) {
                throw new OperationFailureException(
                        Platform.err(AliyunErrors.NO_SUCH_INSTANCE_OFFERING_ID, "no such instance-offering uuid"));
            }
            long cpuNum = vo.getCpuNum();
            long mem = vo.getMemorySize() / 1024 / 1024 / 1024;
            if (mem == 0 || mem < cpuNum) {
                throw new OperationFailureException(
                        Platform.err(AliyunErrors.INSTANCE_OFFERING_NOT_SUPPORT_BY_ALIYUN,
                                "mem must \\> 1G, and mem GB must \\>= cpu")
                );
            }

            chain.then(new NoRollbackFlow() {
                String __name__ = "get-avaliable-instance-types";

                @Override
                public void run(FlowTrigger trigger1, Map data1) {
                    GetAvailableInstanceTypeMsg gaimsg = new GetAvailableInstanceTypeMsg();
                    gaimsg.setRegionId(regionId);
                    gaimsg.setAccountUuid(msg.getSession().getAccountUuid());
                    gaimsg.setCpu((int) cpuNum);
                    gaimsg.setMemory(mem);
                    bus.makeTargetServiceIdByResourceUuid(gaimsg, AliyunConstant.SERVICE_ID, ecs.getUuid());
                    bus.send(gaimsg, new CloudBusCallBack(trigger1) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                GetAvailableInstanceTypeReply rly = reply.castReply();
                                List<EcsInstanceType> list = new ArrayList<>();
                                for (EcsInstanceType type : rly.getTypes()) {
                                    if (type.getCpu() >= cpuNum &&
                                            type.getMemory() >= mem &&
                                            type.getCpu() * mem * 2 >= cpuNum * mem) {
                                        list.add(type);
                                    }
                                }
                                StringBuffer buf = new StringBuffer("available instance type: [");
                                list.stream().forEach(t -> buf.append(t.getTypeId() + ","));
                                buf.append("]");
                                logger.debug(buf.toString());
                                data1.put("types", list);
                                trigger1.next();
                            } else {
                                trigger1.fail(reply.getError());
                            }
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                String __name__ = "arrange-priority-for-instance-type";

                @Override
                public void run(FlowTrigger trigger1, Map data1) {
                    List<EcsInstanceType> types = (List<EcsInstanceType>) data1.get("types");
                    if (types.size() == 0) {
                        trigger1.fail(Platform.operr("No Available instance types now."));
                        return;
                    }

                    arrangeType(types);
                    ecs.setCpuCores(Long.valueOf(types.get(0).getCpu()));
                    ecs.setMemorySize(types.get(0).getMemory());
                    data.put(EcsInstanceConstant.ECS_INSTANCE_SPEC, ecs);

                    data1.put("type", types.get(0).getTypeId());
                    trigger1.next();
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(FlowTrigger trigger1, Map data1) {
                    String __name__ = "get-instance-type-";
                    GetInstanceTypeMsg gtmsg = new GetInstanceTypeMsg();
                    gtmsg.setTypeId(msg.getInstanceType());
                    gtmsg.setRegionId(regionId);
                    gtmsg.setAccountUuid(msg.getSession().getAccountUuid());

                    bus.makeTargetServiceIdByResourceUuid(gtmsg, AliyunConstant.SERVICE_ID, dcUuid);
                    bus.send(gtmsg, new CloudBusCallBack(trigger1) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                GetInstanceTypeReply rly = reply.castReply();
                                ecs.setCpuCores(rly.getCpu());
                                ecs.setMemorySize(rly.getMem());
                                chain.getData().put("type", msg.getInstanceType());
                                trigger1.next();
                            } else {
                                trigger1.fail(reply.getError());
                            }
                        }
                    });
                }
            });
        }

        chain.then(new NoRollbackFlow() {
            String __name__ = "check-instance-types-supported-by-image";

            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                String nameOfEcsImageUuid = Q.New(EcsImageVO.class)
                        .select(EcsImageVO_.ecsImageId)
                        .eq(EcsImageVO_.uuid, msg.getImageUuid())
                        .findValue();
                DescribeImageSupportInstanceTypesMsg dmsg = new DescribeImageSupportInstanceTypesMsg();
                dmsg.setRegionId(regionId);
                dmsg.setImageId(nameOfEcsImageUuid);
                dmsg.setAccountUuid(msg.getSession().getAccountUuid());

                bus.makeTargetServiceIdByResourceUuid(dmsg, AliyunConstant.SERVICE_ID, ecs.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger1.fail(reply.getError());
                            return;
                        }
                        DescribeImageSupportInstanceTypesReply dreply = reply.castReply();
                        if (!dreply.getTypes().stream()
                                .map(EcsInstanceType::getTypeId)
                                .collect(Collectors.toList())
                                .contains((String) data1.get("type"))) {
                            trigger1.fail(Platform.operr("This region [%s] cannot produce instance type [%s] now, " +
                                    "please select another instance type or another region", regionId, data1.get("type")));
                            return;
                        }
                        trigger1.next();
                    }
                });
            }
        });

        chain.then(new NoRollbackFlow() {
            String __name__ = "create-ecs-instance-public";

            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                CreateEcsInstanceMsg cemsg = new CreateEcsInstanceMsg();
                cemsg.setAccountUuid(msg.getSession().getAccountUuid());
                cemsg.setRegionId(regionId);
                cemsg.setDescription(ecs.getDescription());
                cemsg.setEcsName(ecs.getName());
                EcsImageInventory image = new EcsImageInventory();
                switch (msg.getImageType()) {
                    case "ecs": {
                        image = EcsImageInventory.valueOf(dbf.findByUuid(msg.getImageUuid(), EcsImageVO.class));
                        break;
                    }
                    case "local": {
                        image = (EcsImageInventory) data.get(EcsImageConstant.ECS_IMAGE_INSTANCE_SPEC);
                        break;
                    }
                    default:
                        break;
                }
                cemsg.setImageId(image.getEcsImageId());
                ecs.setEcsImageUuid(image.getUuid());
                ecs.setEcsInstanceType((String) data1.get("type"));
                cemsg.setInternetBandwidthMb(ecs.getEcsBandWidth());
                cemsg.setSecurityGroupId(dbf.findByUuid(ecs.getEcsSecurityGroupUuid(), EcsSecurityGroupVO.class).getSecurityGroupId());
                cemsg.setRootPassword(ecs.getEcsInstanceRootPassword());
                cemsg.setZoneId(dbf.findByUuid(ecs.getIdentityZoneUuid(), IdentityZoneVO.class).getZoneId());
                cemsg.setRootVolumeCategory(ecs.getEcsRootVolumeCategory());
                cemsg.setvSwitchId(dbf.findByUuid(ecs.getEcsVSwitchUuid(), EcsVSwitchVO.class).getvSwitchId());
                if (ecs.getPrivateIpAddress() != null) {
                    cemsg.setPrivateIpAddress(ecs.getPrivateIpAddress());
                }

                cemsg.setInstanceType((String) data1.get("type"));

                bus.makeTargetServiceIdByResourceUuid(cemsg, AliyunConstant.SERVICE_ID, ecs.getUuid());
                bus.send(cemsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            CreateEcsInstanceReply rpl = reply.castReply();
                            ecs.setEcsInstanceId(rpl.getEcsInstanceId());
                            ecs.setPrivateIpAddress(rpl.getEcsPrivateIp());
                            ecs.setEcsRootVolumeId(rpl.getRootVolumeId());
                            if (rpl.getExpireTime() != null) {
                                ecs.setExpireDate(HybridUtilsForAliyun.fmtTime(rpl.getExpireTime()));
                            }
                            data.put(EcsInstanceConstant.ECS_INSTANCE_SPEC, ecs);
                            trigger1.next();
                        } else {
                            trigger1.fail(reply.getError());
                        }
                    }
                });
            }
        });

        chain.then(new NoRollbackFlow() {
            String __name__ = "sync-ecs-system-volume";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                SyncAliyunDiskRemoteMsg smsg = new SyncAliyunDiskRemoteMsg();
                smsg.setAccountUuid(msg.getSession().getAccountUuid());
                smsg.setRegionId(regionId);
                smsg.setEcsId(ecs.getEcsInstanceId());
                bus.makeTargetServiceIdByResourceUuid(smsg, AliyunConstant.SERVICE_ID, ecs.getUuid());
                bus.send(smsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            SyncAliyunDiskRemoteReply rpl = reply.castReply();
                            for (AliyunDiskProperty property : rpl.getProperties()) {
                                if (property.getDiskType().equals(AliyunDiskType.system.toString())) {
                                    data1.put("diskVO", getAliyunSystemVolumeVO(property, ecs.getIdentityZoneUuid(), ecs.getUuid()));
                                    trigger1.next();
                                    return;
                                }
                            }
                            trigger1.fail(Platform.operr("no system disk found for ecs: [%s], ecs id is: [%s]", ecs.getUuid(), ecs.getEcsInstanceId()));
                        } else {
                            trigger1.fail(reply.getError());
                        }
                    }
                });
            }
        });

        chain.then(new NoRollbackFlow() {
            String __name__ = "generate-name-for-system-volume";
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                String diskName = HybridUtilsForAliyun.generateSystemVolumeNameForEcs(ecs);
                AliyunDiskVO dvo = (AliyunDiskVO)data1.get("diskVO");
                dvo.setName(diskName);
                UpdateAliyunDiskRemoteMsg smsg = new UpdateAliyunDiskRemoteMsg();
                smsg.setAccountUuid(msg.getSession().getAccountUuid());
                smsg.setRegionId(regionId);
                smsg.setDiskId(dvo.getDiskId());
                smsg.setName(diskName);
                bus.makeTargetServiceIdByResourceUuid(smsg, AliyunConstant.SERVICE_ID, ecs.getUuid());
                bus.send(smsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            data1.put("diskVO", dvo);
                            trigger1.next();
                        } else {
                            trigger1.fail(reply.getError());
                        }
                    }
                });
            }
        }).done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data) {
                new SQLBatch() {
                    @Override
                    protected void scripts() {
                        EcsInstanceVO ecsvo = getEcsInstanceVO(ecs);
                        ecsvo.setAccountUuid(msg.getSession().getAccountUuid());
                        dbf.persistAndRefresh(ecsvo);
                        if (ecsvo.getEcsImageUuid() != null) {
                            createEcsSystemTag(ecsvo);
                        }
                        AliyunDiskVO diskVO = (AliyunDiskVO) data.get("diskVO");
                        diskVO.setAccountUuid(msg.getSession().getAccountUuid());
                        dbf.persistAndRefresh(diskVO);
                    }
                }.execute();
                trigger.next();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                trigger.fail(errCode);
            }
        }).start();
    }

    private void createEcsSystemTag(EcsInstanceVO ecsvo) {
        EcsImageVO ecsImageVO = dbf.findByUuid(ecsvo.getEcsImageUuid(), EcsImageVO.class);
        if (ecsImageVO != null) {
            SystemTagCreator creator = EcsSystemTags.IMAGE_FORMAT.newSystemTagCreator(ecsvo.getUuid());
            creator.setTagByTokens(map(e(EcsSystemTags.IMAGE_FORMAT_TOKEN, ecsImageVO.getFormat().toLowerCase())));
            creator.recreate = true;
            creator.create();
        }
    }

    @Override
    public void rollback(FlowRollback trigger, Map data) {
        CreateEcsInnerMsg msg = (CreateEcsInnerMsg) data.get(EcsInstanceConstant.CREATE_ECS_MESSAGE);
        EcsInstanceInventory inventory = (EcsInstanceInventory) data.get(EcsInstanceConstant.ECS_INSTANCE_SPEC);
        if (inventory.getEcsInstanceId() == null) {
            trigger.rollback();
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-ecs-instance-remote-%s", inventory.getUuid()));
        final String issuer = EcsInstanceVO.class.getSimpleName();
        final List<EcsInstanceInventory> ctx = list(inventory);
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger1) {
                    @Override
                    public void success() {
                        trigger1.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger1.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                CascadeAction action = new HybridCascadeAction().
                        setRootIssuer(issuer).
                        setRootIssuerContext(ctx).
                        setParentIssuer(issuer).
                        setParentIssuerContext(ctx).
                        setActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE).
                        setFullTraverse(true);
                ((HybridCascadeAction) action).setSession(msg.getSession());
                casf.asyncCascade(action, new Completion(trigger1) {
                    @Override
                    public void success() {
                        trigger1.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger1.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                CascadeAction action = new HybridCascadeAction().
                        setRootIssuer(issuer).
                        setRootIssuerContext(ctx).
                        setParentIssuer(issuer).
                        setParentIssuerContext(ctx).
                        setActionCode(CascadeConstant.DELETION_DELETE_CODE).
                        setFullTraverse(true);
                ((HybridCascadeAction) action).setSession(msg.getSession());
                casf.asyncCascade(action, new Completion(trigger1) {
                    @Override
                    public void success() {
                        trigger1.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger1.fail(errorCode);
                    }
                });
            }
        }).done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                trigger.rollback();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                trigger.rollback();
            }
        }).start();
    }

    private AliyunDiskVO getAliyunSystemVolumeVO(AliyunDiskProperty property, String identityUuid, String ecsUuid) {
        AliyunDiskVO dvo = new AliyunDiskVO();
        dvo.setUuid(Platform.getUuid());
        dvo.setDiskId(property.getDiskId());
        dvo.setIdentityZoneUuid(identityUuid);
        dvo.setSizeWithGB(property.getSizeWithGB());
        dvo.setDiskCategory(AliyunDiskCategory.get(property.getDiskCategory()));
        if (!StringUtils.isEmpty(property.getDiskChargeType())){
            dvo.setDiskChargeType(AliyunDiskChargeType.get(property.getDiskChargeType()));
        }
        dvo.setStatus(AliyunDiskStatus.get(property.getDiskStatus()));
        dvo.setName(property.getName());
        dvo.setDescription(property.getDescription());
        dvo.setDiskType(AliyunDiskType.get(property.getDiskType()));
        dvo.setEcsInstanceUuid(ecsUuid);
        Optional.of(property).map(AliyunDiskProperty::getDeviceInfo).ifPresent(dvo::setDeviceInfo);
        Optional.of(property).map(AliyunDiskProperty::getCreateDate).map(HybridUtilsForAliyun::fmtTime).ifPresent(dvo::setCreateDate);
        return dvo;
    }
}
