package org.zstack.aliyun.ecs;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.aliyun.core.AliyunGlobalConfig;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.header.aliyun.image.CreateEcsImageInnerMsg;
import org.zstack.header.aliyun.image.EcsImageConstant;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.message.MessageReply;
import org.zstack.header.storage.backup.BackupStorageConstant;
import org.zstack.storage.backup.imagestore.DeleteExportedImageFromImageStoreBackupStorageMsg;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.Map;

/**
 * Created by mingjian.deng on 17/2/21.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class DeleteExportedImageFromLocalFlow extends NoRollbackFlow {
    private static final CLogger logger = Utils.getLogger(DeleteExportedImageFromLocalFlow.class);
    @Autowired
    protected CloudBus bus;
    @Override
    public void run(FlowTrigger trigger, Map data) {
        CreateEcsImageInnerMsg msg = (CreateEcsImageInnerMsg)data.get(EcsImageConstant.CREATE_ECS_IMAGE_MESSAGE);

        DeleteExportedImageFromImageStoreBackupStorageMsg demsg = new DeleteExportedImageFromImageStoreBackupStorageMsg();
        demsg.setImageUuid(msg.getImageUuid());
        demsg.setBackupStorageUuid(msg.getBackupStorageUuid());
        demsg.setExportFormat(AliyunGlobalConfig.ALIYUN_IMAGE_UPLOAD_FORMAT.value(String.class));
        bus.makeTargetServiceIdByResourceUuid(demsg, BackupStorageConstant.SERVICE_ID, demsg.getImageUuid());
        bus.send(demsg, new CloudBusCallBack(trigger) {
            @Override
            public void run(MessageReply reply) {
                    trigger.next();
                }
        });
    }
}
