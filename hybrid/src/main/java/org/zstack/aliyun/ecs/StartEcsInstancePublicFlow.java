package org.zstack.aliyun.ecs;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.aliyun.AliyunConstant;
import org.zstack.header.aliyun.ecs.*;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.MessageReply;
import org.zstack.hybrid.core.HybridUtilsForAliyun;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.Map;

/**
 * Created by mingjian.deng on 17/2/25.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class StartEcsInstancePublicFlow extends NoRollbackFlow {
    private static final CLogger logger = Utils.getLogger(StartEcsInstancePublicFlow.class);
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected DatabaseFacade dbf;

    @Override
    public void run(FlowTrigger trigger, Map data) {
        EcsInstanceInventory ecs = (EcsInstanceInventory)data.get(EcsInstanceConstant.ECS_INSTANCE_SPEC);
        CreateEcsInnerMsg msg = (CreateEcsInnerMsg)data.get(EcsInstanceConstant.CREATE_ECS_MESSAGE);
        String regionId = HybridUtilsForAliyun.getRegionIdFromIdentityZoneUuid(ecs.getIdentityZoneUuid());
        String accountUuid = msg.getSession().getAccountUuid();

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("start-ecs-instance-and-modify-ecs-vnc-password");

        chain.then(new NoRollbackFlow() {
            String __name__ = String.format("start-ecs-instance-%s", ecs.getUuid());
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                UpdateEcsInstanceStatusMsg semsg = new UpdateEcsInstanceStatusMsg();
                semsg.setRegionId(regionId);
                semsg.setAccountUuid(accountUuid);
                semsg.setEcsId(ecs.getEcsInstanceId());
                semsg.setAction(EcsInstanceConstant.EcsAction.START_ACTION.toString());

                bus.makeTargetServiceIdByResourceUuid(semsg, AliyunConstant.SERVICE_ID, ecs.getUuid());
                bus.send(semsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.warn(String.format("ecs instance [%s] start failed, due to: [code: %s, details: %s]",
                                            ecs.getUuid(), reply.getError().getCode(), reply.getError().getDetails()));
                        } else {
                            UpdateEcsInstanceStatusReply rpl = reply.castReply();
                            if (!rpl.getStatus().equalsIgnoreCase("running")) {
                                logger.warn(String.format("ecs instance [%s] start isn't finish, status is still [%s]",
                                        ecs.getUuid(), rpl.getStatus()));
                            } else {
                                EcsInstanceVO evo = dbf.findByUuid(ecs.getUuid(), EcsInstanceVO.class);
                                evo.setEcsStatus(EcsStatus.RUNNING);
                                dbf.updateAndRefresh(evo);
                                data1.put("started", "true");
                            }
                        }
                        trigger1.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = String.format("modify-ecs-%s-console-vnc-password", ecs.getUuid());
            @Override
            public void run(FlowTrigger trigger1, Map data1) {
                if (data1.get("started") == null) {
                    logger.warn(String.format("ecs [%s] start failed, skip modify vnc password", ecs.getUuid()));
                    trigger1.next();
                    return;
                }
                UpdateEcsInstanceConsolePasswordMsg uecmsg = new UpdateEcsInstanceConsolePasswordMsg();
                uecmsg.setEcsId(ecs.getEcsInstanceId());
                uecmsg.setRegionId(regionId);
                uecmsg.setAccountUuid(accountUuid);
                uecmsg.setPassword(msg.getEcsConsolePassword());

                bus.makeTargetServiceIdByResourceUuid(uecmsg, AliyunConstant.SERVICE_ID, ecs.getUuid());
                bus.send(uecmsg, new CloudBusCallBack(trigger1) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.warn(String.format("modify-ecs-[%s]-console-vnc-password-failed, due to [code: %s, details: %s]",
                                            ecs.getUuid(), reply.getError().getCode(), reply.getError().getDetails()));
                        }
                        trigger1.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(trigger) {
            @Override
            public void handle(Map data) {
                trigger.next();
            }
        }).error(new FlowErrorHandler(trigger) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                trigger.fail(errCode);
            }
        }).start();
    }
}
