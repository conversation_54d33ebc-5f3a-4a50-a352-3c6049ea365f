package org.zstack.hybrid.backup;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.aliyun.backup.BackupToAliyunMessage;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.componentloader.PluginDSL;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.errorcode.ErrorFacade;
import org.zstack.header.AbstractService;
import org.zstack.header.aliyun.oss.APICreateOssBackupBucketRemoteMsg;
import org.zstack.header.aliyun.oss.APIGetOssBackupBucketFromRemoteMsg;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.hybrid.backup.*;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.hybrid.core.HybridLicenseChecker;
import org.zstack.hybrid.core.HybridType;

import java.util.*;

/**
 * Created by mingjian.deng on 17/5/24.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class BackupManagerImpl extends AbstractService
        implements BackupManager, GlobalApiMessageInterceptor {
    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected ErrorFacade errf;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private HybridLicenseChecker hyimpl;

    private Map<HybridType, BackupFactory> backupFactories = Collections.synchronizedMap(new HashMap<HybridType, BackupFactory>());

    @Override
    public boolean start() {
        populateExtensions();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        bus.dealWithUnknownMessage(msg);
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof BackupToAliyunMessage) {
            handle(msg, HybridType.aliyun);
        } else if (msg instanceof APIBackupDatabaseToPublicCloudMsg) {
            handle((APIBackupDatabaseToPublicCloudMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APIMessage msg, HybridType type) {
        BackupFactory factory = getFactory(type);
        factory.getBackupService().handleMessage(msg);
    }

    private void handle(final APIBackupDatabaseToPublicCloudMsg msg) {
        BackupFactory factory = getFactory(HybridType.valueOf(msg.getType()));
        factory.getBackupService().handleMessage(msg);
    }

    private BackupFactory getFactory(HybridType type) {
        BackupFactory factory = backupFactories.get(type);
        if (factory == null) {
            throw new CloudRuntimeException(String.format("No BackupFactory of type[%s] found", type));
        }
        return factory;
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(BackupConstant.SERVICE_ID);
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        return Arrays.asList(APIBackupDatabaseToPublicCloudMsg.class, APICreateOssBackupBucketRemoteMsg.class,
                APIGetOssBackupBucketFromRemoteMsg.class, APIDeleteBackupFileInPublicMsg.class,
                APIDownloadBackupFileFromPublicCloudMsg.class);
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        hyimpl.intercept(msg);
        return msg;
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.FRONT;
    }

    private void populateExtensions() {
        for (BackupFactory f : pluginRgty.getExtensionList(BackupFactory.class)) {
            BackupFactory old = backupFactories.get(f.getType());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate BackupFactory[%s, %s] for type[%s]",
                        f.getClass().getName(), old.getClass().getName(), f.getType()));
            }
            backupFactories.put(f.getType(), f);
        }
    }

    {
        PluginDSL.PluginDefinition definition = new PluginDSL.PluginDefinition(BackupManagerImpl.class);
        definition.newExtension().extensionClass(GlobalApiMessageInterceptor.class);
    }
}
