package org.zstack.header.aliyun.network.group

import org.zstack.header.aliyun.network.group.APISyncEcsSecurityGroupFromRemoteEvent

doc {
    title "SyncEcsSecurityGroupFromRemote"

    category "aliyun.network.group"

    desc """同步远程阿里云安全组"""

    rest {
        request {
			url "PUT /v1/hybrid/aliyun/security-group/{ecsVpcUuid}/sync"

			header (Authorization: 'OAuth the-session-uuid')

            clz APISyncEcsSecurityGroupFromRemoteMsg.class

            desc """同步远程阿里云安全组"""
            
			params {

				column {
					name "ecsVpcUuid"
					enclosedIn "syncEcsSecurityGroupFromRemote"
					desc "阿里云主机VPC UUID"
					location "url"
					type "String"
					optional false
					since "0.6"
				}
				column {
					name "ecsSecurityGroupId"
					enclosedIn "syncEcsSecurityGroupFromRemote"
					desc "阿里云主机安全组ID"
					location "query"
					type "String"
					optional true
					since "0.6"
				}
				column {
					name "resourceUuid"
					enclosedIn "syncEcsSecurityGroupFromRemote"
					desc "资源UUID"
					location "body"
					type "String"
					optional true
					since "0.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
				column {
					name "securityGroupId"
					enclosedIn "syncEcsSecurityGroupFromRemote"
					desc ""
					location "body"
					type "String"
					optional true
					since "0.6"
				}
				column {
					name "tagUuids"
					enclosedIn "syncEcsSecurityGroupFromRemote"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
			}
        }

        response {
            clz APISyncEcsSecurityGroupFromRemoteEvent.class
        }
    }
}