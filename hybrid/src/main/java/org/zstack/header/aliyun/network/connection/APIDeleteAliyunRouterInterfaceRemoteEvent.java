package org.zstack.header.aliyun.network.connection;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@RestResponse
public class APIDeleteAliyunRouterInterfaceRemoteEvent extends APIEvent {
    public APIDeleteAliyunRouterInterfaceRemoteEvent() {
        super(null);
    }

    public APIDeleteAliyunRouterInterfaceRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APIDeleteAliyunRouterInterfaceRemoteEvent __example__() {
        APIDeleteAliyunRouterInterfaceRemoteEvent event = new APIDeleteAliyunRouterInterfaceRemoteEvent();
        return event;
    }
    
}