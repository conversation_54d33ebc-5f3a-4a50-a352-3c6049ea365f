package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.aliyun.ProcessAliyunReply;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/10.
 */
public class SyncAliyunVirtualRouteEntryReply extends ProcessAliyunReply {
    private List<AliyunVirtualRouteEntryProperty> vres = new ArrayList<>();

    public List<AliyunVirtualRouteEntryProperty> getVres() {
        return vres;
    }

    public void setVres(List<AliyunVirtualRouteEntryProperty> vres) {
        this.vres = vres;
    }
}
