package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.ProcessAliyunReply;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/12.
 */
public class SyncVirtualBorderRouterReply extends ProcessAliyunReply {
    private List<VirtualBorderRouterProperty> vbrs = new ArrayList<>();

    public List<VirtualBorderRouterProperty> getVbrs() {
        return vbrs;
    }

    public void setVbrs(List<VirtualBorderRouterProperty> vbrs) {
        this.vbrs = vbrs;
    }
}
