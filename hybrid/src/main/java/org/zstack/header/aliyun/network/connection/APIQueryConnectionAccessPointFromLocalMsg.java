package org.zstack.header.aliyun.network.connection;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;

import java.util.List;
import static java.util.Arrays.asList;

/**
 * Created by mingjian.deng on 17/4/11.
 */
@AutoQuery(replyClass = APIQueryConnectionAccessPointFromLocalReply.class, inventoryClass = ConnectionAccessPointInventory.class)
@Action(category = ConnectionConstant.ACTION_CATEGORY, names = {"read"})
@RestRequest(
        path = "/hybrid/aliyun/access-point",
        optionalPaths = {"/hybrid/aliyun/access-point/{uuid}"},
        method = HttpMethod.GET,
        responseClass = APIQueryConnectionAccessPointFromLocalReply.class
)
public class APIQueryConnectionAccessPointFromLocalMsg extends APIQueryMessage {

    public static List<String> __example__() {
        return asList("name:connectionAccessPoint1");
    }
    
}