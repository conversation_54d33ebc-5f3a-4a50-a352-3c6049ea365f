package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.network.HybridConnectionType;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

/**
 * Created by mingjian.deng on 17/6/27.
 */
@StaticMetamodel(HybridConnectionRefVO.class)
public class HybridConnectionRefVO_ {
    public static volatile SingularAttribute<HybridConnectionRefVO, String> uuid;
    public static volatile SingularAttribute<HybridConnectionRefVO, String> resourceUuid;
    public static volatile SingularAttribute<HybridConnectionRefVO, String> resourceType;
    public static volatile SingularAttribute<HybridConnectionRefVO, HybridConnectionType> connectionType;
    public static volatile SingularAttribute<HybridConnectionRefVO, String> connectionUuid;
    public static volatile SingularAttribute<HybridConnectionRefVO, String> direction;
    public static volatile SingularAttribute<HybridConnectionRefVO, Timestamp> createDate;
    public static volatile SingularAttribute<HybridConnectionRefVO, Timestamp> lastOpDate;
}
