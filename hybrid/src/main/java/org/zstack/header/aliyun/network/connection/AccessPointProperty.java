package org.zstack.header.aliyun.network.connection;

/**
 * Created by mingjian.deng on 17/4/11.
 */
public class AccessPointProperty {
    private String accessPointId;
    private String type;
    private String status;
    private String name;
    private String description;
    private String location;
    private String hostOperator;

    public String getAccessPointId() {
        return accessPointId;
    }

    public void setAccessPointId(String accessPointId) {
        this.accessPointId = accessPointId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getHostOperator() {
        return hostOperator;
    }

    public void setHostOperator(String hostOperator) {
        this.hostOperator = hostOperator;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ConnectionAccessPointVO) {
            return accessPointId.equals(((ConnectionAccessPointVO) obj).getAccessPointId());
        } else if (obj instanceof VirtualBorderRouterProperty) {
            return accessPointId.equals(((AccessPointProperty) obj).getAccessPointId());
        } else {
            return super.equals(obj);
        }
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
