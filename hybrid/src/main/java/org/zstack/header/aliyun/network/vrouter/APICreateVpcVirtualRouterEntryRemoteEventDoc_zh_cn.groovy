package org.zstack.header.aliyun.network.vrouter

import org.zstack.header.errorcode.ErrorCode
import org.zstack.header.aliyun.network.vrouter.VpcVirtualRouteEntryInventory

doc {

	title "VPC虚拟路由清单"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.header.aliyun.network.vrouter.APICreateVpcVirtualRouterEntryRemoteEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
	ref {
		name "inventory"
		path "org.zstack.header.aliyun.network.vrouter.APICreateVpcVirtualRouterEntryRemoteEvent.inventory"
		desc "null"
		type "VpcVirtualRouteEntryInventory"
		since "0.6"
		clz VpcVirtualRouteEntryInventory.class
	}
}
