package org.zstack.header.aliyun.network.connection;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.utils.CollectionDSL;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@RestResponse(allTo = "inventories")
public class APISyncVirtualBorderRouterFromRemoteEvent extends APIEvent {
    List<VirtualBorderRouterInventory> inventories = new ArrayList<>();

    public List<VirtualBorderRouterInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<VirtualBorderRouterInventory> inventories) {
        this.inventories = inventories;
    }

    public APISyncVirtualBorderRouterFromRemoteEvent() {
        super(null);
    }

    public APISyncVirtualBorderRouterFromRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APISyncVirtualBorderRouterFromRemoteEvent __example__() {
        APISyncVirtualBorderRouterFromRemoteEvent event = new APISyncVirtualBorderRouterFromRemoteEvent();
        VirtualBorderRouterInventory inventory = new VirtualBorderRouterInventory();
        inventory.setUuid(uuid());
        inventory.setAccessPointUuid(uuid());
        inventory.setCircuitCode("");
        inventory.setDataCenterUuid(uuid());
        inventory.setName("test-vbr");
        inventory.setVbrId("test-vbr-id");
        inventory.setVlanId("2995");
        inventory.setVlanInterfaceId("interface-id");
        inventory.setPeerGatewayIp("*********");
        inventory.setPeeringSubnetMask("***************");
        inventory.setPhysicalConnectionId("pc-234lakg81");
        inventory.setPhysicalConnectionStatus("Enabled");
        inventory.setStatus("active");
        inventory.setLocalGatewayIp("*********");
        event.setInventories(CollectionDSL.list(inventory));
        return event;
    }
    
}