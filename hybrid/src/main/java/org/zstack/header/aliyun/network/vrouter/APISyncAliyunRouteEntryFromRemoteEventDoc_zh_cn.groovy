package org.zstack.header.aliyun.network.vrouter

import org.zstack.header.errorcode.ErrorCode

doc {

	title "操作结果返回"

	ref {
		name "error"
		path "org.zstack.header.aliyun.network.vrouter.APISyncAliyunRouteEntryFromRemoteEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	field {
		name "addEcs"
		desc ""
		type "List"
		since "0.6"
	}
	field {
		name "delEcs"
		desc ""
		type "List"
		since "0.6"
	}
}
