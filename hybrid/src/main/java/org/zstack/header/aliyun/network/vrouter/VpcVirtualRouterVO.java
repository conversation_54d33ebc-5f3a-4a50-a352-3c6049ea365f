package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.aliyun.network.vpc.EcsVpcVO;
import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.vo.ForeignKey;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.vo.ToInventory;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Created by mingjian.deng on 17/4/8.
 */
@Entity
@Table
public class VpcVirtualRouterVO extends ResourceVO implements OwnedByAccount, ToInventory {
    @Transient
    private String accountUuid;

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    @Override
    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    @Column
    private String vrId;
    @Column
    @ForeignKey(parentEntityClass = EcsVpcVO.class, parentKey = "uuid", onDeleteAction = ForeignKey.ReferenceOption.RESTRICT)
    private String vpcUuid;
    @Column
    private String name;
    @Column
    private String description;
    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getVrId() {
        return vrId;
    }

    public void setVrId(String vrId) {
        this.vrId = vrId;
    }

    public String getVpcUuid() {
        return vpcUuid;
    }

    public void setVpcUuid(String vpcUuid) {
        this.vpcUuid = vpcUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof AliyunVirtualRouterProperty) {
            return vrId.equals(((AliyunVirtualRouterProperty) obj).getVrId());
        } else {
            return super.equals(obj);
        }
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
