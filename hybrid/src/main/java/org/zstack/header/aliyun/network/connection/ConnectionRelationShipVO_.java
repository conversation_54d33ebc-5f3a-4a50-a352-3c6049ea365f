package org.zstack.header.aliyun.network.connection;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

/**
 * Created by ming<PERSON>an.deng on 17/6/26.
 */
@StaticMetamodel(ConnectionRelationShipVO.class)
public class ConnectionRelationShipVO_ {
    public static volatile SingularAttribute<ConnectionRelationShipVO, String> uuid;
    public static volatile SingularAttribute<ConnectionRelationShipVO, String> relationShips;
    public static volatile SingularAttribute<ConnectionRelationShipVO, String> name;
    public static volatile SingularAttribute<ConnectionRelationShipVO, String> description;
    public static volatile SingularAttribute<ConnectionRelationShipVO, Timestamp> createDate;
    public static volatile SingularAttribute<ConnectionRelationShipVO, Timestamp> lastOpDate;
}
