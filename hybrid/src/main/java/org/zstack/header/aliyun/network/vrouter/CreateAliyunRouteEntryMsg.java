package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 17/4/10.
 */
public class CreateAliyunRouteEntryMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String destinationCidrBlock;
    private NextHopType nextHopType;
    private String nextHopId;
    private String virtualRouterId;
    private VRouterType routerType;


    public String getDestinationCidrBlock() {
        return destinationCidrBlock;
    }

    public void setDestinationCidrBlock(String destinationCidrBlock) {
        this.destinationCidrBlock = destinationCidrBlock;
    }

    public String getNextHopId() {
        return nextHopId;
    }

    public void setNextHopId(String nextHopId) {
        this.nextHopId = nextHopId;
    }

    public String getVirtualRouterId() {
        return virtualRouterId;
    }

    public void setVirtualRouterId(String virtualRouterId) {
        this.virtualRouterId = virtualRouterId;
    }

    public VRouterType getRouterType() {
        return routerType;
    }

    public void setRouterType(VRouterType routerType) {
        this.routerType = routerType;
    }

    public NextHopType getNextHopType() {
        return nextHopType;
    }

    public void setNextHopType(NextHopType nextHopType) {
        this.nextHopType = nextHopType;
    }
}
