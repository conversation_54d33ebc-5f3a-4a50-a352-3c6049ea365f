package org.zstack.header.aliyun.network.connection;

import org.springframework.http.HttpMethod;
import org.zstack.header.aliyun.network.AliyunNetworkMessage;
import org.zstack.header.datacenter.DataCenterVO;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 17/4/8.
 */
@Action(category = ConnectionConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/hybrid/aliyun/access-point",
        method = HttpMethod.POST,
        parameterName = "params",
        responseClass = APIAddConnectionAccessPointFromRemoteEvent.class
)
public class APIAddConnectionAccessPointFromRemoteMsg extends APICreateMessage implements AliyunNetworkMessage {
    @APIParam(resourceType = DataCenterVO.class, checkAccount = true)
    private String dataCenterUuid;
    @APIParam(emptyString = false)
    private String accessPointId;

    public String getDataCenterUuid() {
        return dataCenterUuid;
    }

    public void setDataCenterUuid(String dataCenterUuid) {
        this.dataCenterUuid = dataCenterUuid;
    }

    public String getAccessPointId() {
        return accessPointId;
    }

    public void setAccessPointId(String accessPointId) {
        this.accessPointId = accessPointId;
    }

    public static APIAddConnectionAccessPointFromRemoteMsg __example__() {
        APIAddConnectionAccessPointFromRemoteMsg msg = new APIAddConnectionAccessPointFromRemoteMsg();
        msg.setDataCenterUuid(uuid());
        msg.setAccessPointId("id32");
        return msg;
    }
    
}
