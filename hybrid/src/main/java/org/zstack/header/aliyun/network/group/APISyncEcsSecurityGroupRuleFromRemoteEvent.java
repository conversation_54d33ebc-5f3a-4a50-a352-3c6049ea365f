package org.zstack.header.aliyun.network.group;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static java.util.Arrays.asList;

/**
 * Created by ming<PERSON><PERSON>.deng on 17/5/13.
 */
@RestResponse(allTo = "inventories")
public class APISyncEcsSecurityGroupRuleFromRemoteEvent extends APIEvent {
    private List<EcsSecurityGroupRuleInventory> inventories = new ArrayList<>();

    public List<EcsSecurityGroupRuleInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<EcsSecurityGroupRuleInventory> inventories) {
        this.inventories = inventories;
    }

    public APISyncEcsSecurityGroupRuleFromRemoteEvent() {
        super(null);
    }

    public APISyncEcsSecurityGroupRuleFromRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APISyncEcsSecurityGroupRuleFromRemoteEvent __example__() {
        APISyncEcsSecurityGroupRuleFromRemoteEvent event = new APISyncEcsSecurityGroupRuleFromRemoteEvent();
        EcsSecurityGroupRuleInventory inventory = new EcsSecurityGroupRuleInventory();
        inventory.setUuid(uuid());
        inventory.setEcsSecurityGroupUuid(uuid());
        inventory.setProtocol("protocol");
        inventory.setPortRange("2223:2342");
        inventory.setCidrIp("*********");
        inventory.setDirection("ingress");
        inventory.setCreateDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        inventory.setLastOpDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        event.setInventories(asList(inventory));
        return event;
    }
    
}