package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.utils.CollectionDSL;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/8.
 */
@RestResponse(allTo = "inventories")
public class APISyncAliyunVirtualRouterFromRemoteEvent extends APIEvent {
    private List<VpcVirtualRouterInventory> inventories = new ArrayList<>();

    public List<VpcVirtualRouterInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<VpcVirtualRouterInventory> inventories) {
        this.inventories = inventories;
    }

    public APISyncAliyunVirtualRouterFromRemoteEvent() {
        super(null);
    }

    public APISyncAliyunVirtualRouterFromRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APISyncAliyunVirtualRouterFromRemoteEvent __example__() {
        APISyncAliyunVirtualRouterFromRemoteEvent msg = new APISyncAliyunVirtualRouterFromRemoteEvent();
        VpcVirtualRouterInventory inventory = new VpcVirtualRouterInventory();
        inventory.setUuid(uuid());
        inventory.setVrId("vr-id");
        inventory.setName("vr-name");
        inventory.setDescription("description");
        inventory.setVpcUuid(uuid());
        msg.setInventories(CollectionDSL.list(inventory));
        return msg;
    }
    
}