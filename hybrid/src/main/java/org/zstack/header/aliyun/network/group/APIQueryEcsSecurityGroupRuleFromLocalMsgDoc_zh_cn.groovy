package org.zstack.header.aliyun.network.group

import org.zstack.header.aliyun.network.group.APIQueryEcsSecurityGroupRuleFromLocalReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "查询本地阿里云安全组规则（QueryEcsSecurityGroupRuleFromLocal）"

    category "aliyun.network.group"

    desc """查询本地阿里云安全组规则"""

    rest {
        request {
			url "GET /v1/hybrid/aliyun/security-group-rule"
			url "GET /v1/hybrid/aliyun/security-group-rule/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryEcsSecurityGroupRuleFromLocalMsg.class

            desc """查询本地阿里云安全组规则"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryEcsSecurityGroupRuleFromLocalReply.class
        }
    }
}