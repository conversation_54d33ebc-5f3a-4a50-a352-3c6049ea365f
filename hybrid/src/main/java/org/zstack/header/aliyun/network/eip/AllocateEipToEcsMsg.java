package org.zstack.header.aliyun.network.eip;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 2017/12/7.
 * The difference between AllocateEipToEcsMsg and AttachEipToEcsMsg is:
 * AllocateEipToEcsMsg: the public ip is bind to ecs and cannot unbind, it is not identity eip
 * AttachEipToEcsMsg: the public ip is eip, and it can both bind and unbind, the eip could find in the eip list
 */
public class AllocateEipToEcsMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String ecsId;

    public String getEcsId() {
        return ecsId;
    }

    public void setEcsId(String ecsId) {
        this.ecsId = ecsId;
    }
}
