package org.zstack.header.aliyun.network.vrouter;

import org.springframework.http.HttpMethod;
import org.zstack.header.hybrid.network.HybridNetworkConstant;
import org.zstack.header.identity.Action;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;

import java.util.List;

import static java.util.Arrays.asList;

/**
 * Created by mingjian.deng on 17/4/8.
 */
@AutoQuery(replyClass = APIQueryAliyunVirtualRouterFromLocalReply.class, inventoryClass = VpcVirtualRouterInventory.class)
@Action(category = HybridNetworkConstant.ACTION_CATEGORY, names = {"read"})
@RestRequest(
        path = "/hybrid/aliyun/vrouter",
        optionalPaths = {"/hybrid/aliyun/vrouter/{uuid}"},
        responseClass = APIQueryAliyunVirtualRouterFromLocalReply.class,
        method = HttpMethod.GET
)
public class APIQueryAliyunVirtualRouterFromLocalMsg extends APIQueryMessage {

    public static List<String> __example__() {
        return asList("name=Test", "state=Enabled");
    }

}