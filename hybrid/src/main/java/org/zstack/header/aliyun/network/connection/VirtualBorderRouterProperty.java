package org.zstack.header.aliyun.network.connection;

/**
 * Created by mingjian.deng on 17/4/12.
 */
public class VirtualBorderRouterProperty {
    private String vbrId;
    private String status;
    private String vlanId;
    private String vlanInterfaceId;
    private String circuitCode;
    private String localGatewayIp;
    private String peerGatewayIp;
    private String peeringSubnetMask;
    private String physicalConnectionId;
    private String physicalConnectionStatus;
    private String accessPointId;
    private String name;
    private String description;
    private String createTime;

    public String getVbrId() {
        return vbrId;
    }

    public void setVbrId(String vbrId) {
        this.vbrId = vbrId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVlanId() {
        return vlanId;
    }

    public void setVlanId(String vlanId) {
        this.vlanId = vlanId;
    }

    public String getCircuitCode() {
        return circuitCode;
    }

    public void setCircuitCode(String circuitCode) {
        this.circuitCode = circuitCode;
    }

    public String getLocalGatewayIp() {
        return localGatewayIp;
    }

    public void setLocalGatewayIp(String localGatewayIp) {
        this.localGatewayIp = localGatewayIp;
    }

    public String getPeerGatewayIp() {
        return peerGatewayIp;
    }

    public void setPeerGatewayIp(String peerGatewayIp) {
        this.peerGatewayIp = peerGatewayIp;
    }

    public String getPeeringSubnetMask() {
        return peeringSubnetMask;
    }

    public void setPeeringSubnetMask(String peeringSubnetMask) {
        this.peeringSubnetMask = peeringSubnetMask;
    }

    public String getPhysicalConnectionId() {
        return physicalConnectionId;
    }

    public void setPhysicalConnectionId(String physicalConnectionId) {
        this.physicalConnectionId = physicalConnectionId;
    }

    public String getPhysicalConnectionStatus() {
        return physicalConnectionStatus;
    }

    public void setPhysicalConnectionStatus(String physicalConnectionStatus) {
        this.physicalConnectionStatus = physicalConnectionStatus;
    }

    public String getAccessPointId() {
        return accessPointId;
    }

    public void setAccessPointId(String accessPointId) {
        this.accessPointId = accessPointId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVlanInterfaceId() {
        return vlanInterfaceId;
    }

    public void setVlanInterfaceId(String vlanInterfaceId) {
        this.vlanInterfaceId = vlanInterfaceId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof VirtualBorderRouterVO) {
            return vbrId.equals(((VirtualBorderRouterVO) obj).getVbrId());
        } else if (obj instanceof VirtualBorderRouterProperty) {
            return vbrId.equals(((VirtualBorderRouterProperty) obj).getVbrId());
        } else {
            return super.equals(obj);
        }
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
