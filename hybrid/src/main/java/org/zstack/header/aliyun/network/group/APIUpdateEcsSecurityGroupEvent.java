package org.zstack.header.aliyun.network.group;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

import java.sql.Timestamp;

/**
 * Created by camile  on 17/9/16.
 */
@RestResponse(allTo = "inventory")
public class APIUpdateEcsSecurityGroupEvent extends APIEvent {
    private EcsSecurityGroupInventory inventory;

    public EcsSecurityGroupInventory getInventory() {
        return inventory;
    }

    public void setInventory(EcsSecurityGroupInventory inventory) {
        this.inventory = inventory;
    }

    public APIUpdateEcsSecurityGroupEvent() {
        super(null);
    }

    public APIUpdateEcsSecurityGroupEvent(String apiId) {
        super(apiId);
    }

    public static APIUpdateEcsSecurityGroupEvent __example__() {
        APIUpdateEcsSecurityGroupEvent event = new APIUpdateEcsSecurityGroupEvent();
        EcsSecurityGroupInventory inventory = new EcsSecurityGroupInventory();
        inventory.setUuid(uuid());
        inventory.setEcsVpcUuid(uuid());
        inventory.setSecurityGroupId(uuid());
        inventory.setName("test-name");
        inventory.setDescription("description");
        inventory.setCreateDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        inventory.setLastOpDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        event.setInventory(inventory);
        return event;
    }
    
}