package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 17/4/12.
 */
public class TerminalVirtualBorderRouterMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String vbrId;

    public String getVbrId() {
        return vbrId;
    }

    public void setVbrId(String vbrId) {
        this.vbrId = vbrId;
    }
}
