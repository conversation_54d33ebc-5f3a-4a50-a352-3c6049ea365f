package org.zstack.header.aliyun.account;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.hybrid.account.HybridAccountVO;

/**
 * Created by mingjian.deng on 17/5/5.
 */
@Action(category = AliyunAccountConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/hybrid/aliyun/key/{uuid}/detach",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APIDetachAliyunKeyEvent.class
)
public class APIDetachAliyunKeyMsg extends APIMessage {
    @APIParam(resourceType = HybridAccountVO.class, checkAccount = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public static APIDetachAliyunKeyMsg __example__() {
        APIDetachAliyunKeyMsg msg = new APIDetachAliyunKeyMsg();
        msg.setUuid(uuid());
        return msg;
    }
    
}
