package org.zstack.header.aliyun.network.vrouter;

import org.zstack.hybrid.core.HybridNeedReplyMessage;

/**
 * Created by mingjian.deng on 17/6/26.
 */
public class SyncAliyunVpcRouteEntryInnerMsg extends HybridNeedReplyMessage {
    private String vRouterUuid;
    private String vRouterType;

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public String getvRouterType() {
        return vRouterType;
    }

    public void setvRouterType(String vRouterType) {
        this.vRouterType = vRouterType;
    }
}
