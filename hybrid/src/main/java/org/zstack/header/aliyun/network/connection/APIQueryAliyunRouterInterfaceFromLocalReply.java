package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.network.vrouter.VRouterType;
import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import static java.util.Arrays.asList;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@RestResponse(allTo = "inventories")
public class APIQueryAliyunRouterInterfaceFromLocalReply extends APIQueryReply {
    private List<AliyunRouterInterfaceInventory> inventories = new ArrayList<>();

    public List<AliyunRouterInterfaceInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<AliyunRouterInterfaceInventory> inventories) {
        this.inventories = inventories;
    }

    public static APIQueryAliyunRouterInterfaceFromLocalReply __example__() {
        APIQueryAliyunRouterInterfaceFromLocalReply reply = new APIQueryAliyunRouterInterfaceFromLocalReply();
        AliyunRouterInterfaceInventory interfaceInventory = new AliyunRouterInterfaceInventory();
        interfaceInventory.setUuid(uuid());
        interfaceInventory.setDataCenterUuid(uuid());
        interfaceInventory.setRouterInterfaceId("router-id");
        interfaceInventory.setAccessPointUuid(uuid());
        interfaceInventory.setVirtualRouterUuid(uuid());
        interfaceInventory.setRole("role");
        interfaceInventory.setvRouterType(VRouterType.vrouter.name());
        interfaceInventory.setSpec("spec");
        interfaceInventory.setName("test");
        interfaceInventory.setDescription("description");
        interfaceInventory.setStatus("Running");
        interfaceInventory.setOppositeInterfaceUuid(uuid());
        interfaceInventory.setCreateDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        interfaceInventory.setLastOpDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        reply.setInventories(asList(interfaceInventory));
        return reply;
    }
    
}