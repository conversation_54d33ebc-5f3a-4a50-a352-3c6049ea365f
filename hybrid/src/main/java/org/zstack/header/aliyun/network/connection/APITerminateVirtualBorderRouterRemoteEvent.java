package org.zstack.header.aliyun.network.connection;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@RestResponse
public class APITerminateVirtualBorderRouterRemoteEvent extends APIEvent {
    public APITerminateVirtualBorderRouterRemoteEvent() {
        super(null);
    }

    public APITerminateVirtualBorderRouterRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APITerminateVirtualBorderRouterRemoteEvent __example__() {
        APITerminateVirtualBorderRouterRemoteEvent event = new APITerminateVirtualBorderRouterRemoteEvent();
        return event;
    }
    
}