package org.zstack.header.aliyun.network.group

import org.zstack.header.aliyun.network.group.APIQueryEcsSecurityGroupFromLocalReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryEcsSecurityGroupFromLocal"

    category "aliyun.network.group"

    desc """查询本地阿里云安全组"""

    rest {
        request {
			url "GET /v1/hybrid/aliyun/security-group"
			url "GET /v1/hybrid/aliyun/security-group/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryEcsSecurityGroupFromLocalMsg.class

            desc """查询本地阿里云安全组"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryEcsSecurityGroupFromLocalReply.class
        }
    }
}