package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 17/4/8.
 */
public class DeleteAliyunVpcVirtualRouteEntryMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String vRouterId;
    private String destinationCidrBlock;
    private String nextHopId;
    private VRouterType routerType;

    public String getvRouterId() {
        return vRouterId;
    }

    public void setvRouterId(String vRouterId) {
        this.vRouterId = vRouterId;
    }

    public String getDestinationCidrBlock() {
        return destinationCidrBlock;
    }

    public void setDestinationCidrBlock(String destinationCidrBlock) {
        this.destinationCidrBlock = destinationCidrBlock;
    }

    public String getNextHopId() {
        return nextHopId;
    }

    public void setNextHopId(String nextHopId) {
        this.nextHopId = nextHopId;
    }

    public VRouterType getRouterType() {
        return routerType;
    }

    public void setRouterType(VRouterType routerType) {
        this.routerType = routerType;
    }
}
