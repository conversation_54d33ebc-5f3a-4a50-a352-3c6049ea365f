package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 17/4/11.
 */
public class UpdateRouterInterfaceMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String vRouterInterfaceId;
    private String opType;
    private String oppositeRouterId;
    private String oppositeInterfaceId;

    public String getvRouterInterfaceId() {
        return vRouterInterfaceId;
    }

    public void setvRouterInterfaceId(String vRouterInterfaceId) {
        this.vRouterInterfaceId = vRouterInterfaceId;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public String getOppositeRouterId() {
        return oppositeRouterId;
    }

    public void setOppositeRouterId(String oppositeRouterId) {
        this.oppositeRouterId = oppositeRouterId;
    }

    public String getOppositeInterfaceId() {
        return oppositeInterfaceId;
    }

    public void setOppositeInterfaceId(String oppositeInterfaceId) {
        this.oppositeInterfaceId = oppositeInterfaceId;
    }
}
