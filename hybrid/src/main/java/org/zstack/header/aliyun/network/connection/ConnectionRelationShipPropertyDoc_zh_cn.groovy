package org.zstack.header.aliyun.network.connection

import org.zstack.header.aliyun.network.HybridConnectionType
import java.sql.Timestamp

doc {

	title "在这里输入结构的名称"

	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "0.6"
	}
	field {
		name "resourceType"
		desc "资源类型"
		type "String"
		since "0.6"
	}
	field {
		name "accountUuid"
		desc "账户UUID"
		type "String"
		since "0.6"
	}
	ref {
		name "connectionType"
		path "org.zstack.header.aliyun.network.connection.ConnectionRelationShipProperty.connectionType"
		desc "连接类型"
		type "HybridConnectionType"
		since "0.6"
		clz HybridConnectionType.class
	}
	field {
		name "direction"
		desc "方向"
		type "String"
		since "0.6"
	}
	field {
		name "relationShips"
		desc "连接关系"
		type "String"
		since "0.6"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "0.6"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "0.6"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "0.6"
	}
}
