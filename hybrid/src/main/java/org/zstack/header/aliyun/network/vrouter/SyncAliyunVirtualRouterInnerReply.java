package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.message.MessageReply;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 2017/8/3.
 */
public class SyncAliyunVirtualRouterInnerReply extends MessageReply {
    private List<VpcVirtualRouterInventory> inventories = new ArrayList<>();

    public List<VpcVirtualRouterInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<VpcVirtualRouterInventory> inventories) {
        this.inventories = inventories;
    }
}
