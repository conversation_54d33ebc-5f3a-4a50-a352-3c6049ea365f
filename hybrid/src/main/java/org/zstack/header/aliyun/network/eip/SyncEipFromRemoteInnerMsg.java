package org.zstack.header.aliyun.network.eip;

import org.zstack.hybrid.core.HybridNeedReplyMessage;

/**
 * Created by mingjian.deng on 2017/7/5.
 */
public class SyncEipFromRemoteInnerMsg extends HybridNeedReplyMessage {
    private String dcUuid;
    private boolean allocated = false;

    public String getDcUuid() {
        return dcUuid;
    }

    public void setDcUuid(String dcUuid) {
        this.dcUuid = dcUuid;
    }

    public boolean isAllocated() {
        return allocated;
    }

    public void setAllocated(boolean allocated) {
        this.allocated = allocated;
    }
}
