package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 17/4/12.
 */
public class UpdateVirtualBorderRouterMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String vbrId;
    private String vlanId;
    private String circuitCode;
    private String localGatewayIp;
    private String peerGatewayIp;
    private String peeringSubnetMask;
    private String name;
    private String description;

    public String getVbrId() {
        return vbrId;
    }

    public void setVbrId(String vbrId) {
        this.vbrId = vbrId;
    }

    public String getVlanId() {
        return vlanId;
    }

    public void setVlanId(String vlanId) {
        this.vlanId = vlanId;
    }

    public String getCircuitCode() {
        return circuitCode;
    }

    public void setCircuitCode(String circuitCode) {
        this.circuitCode = circuitCode;
    }

    public String getLocalGatewayIp() {
        return localGatewayIp;
    }

    public void setLocalGatewayIp(String localGatewayIp) {
        this.localGatewayIp = localGatewayIp;
    }

    public String getPeerGatewayIp() {
        return peerGatewayIp;
    }

    public void setPeerGatewayIp(String peerGatewayIp) {
        this.peerGatewayIp = peerGatewayIp;
    }

    public String getPeeringSubnetMask() {
        return peeringSubnetMask;
    }

    public void setPeeringSubnetMask(String peeringSubnetMask) {
        this.peeringSubnetMask = peeringSubnetMask;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
