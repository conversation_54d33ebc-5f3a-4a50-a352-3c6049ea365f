package org.zstack.header.aliyun.network.connection;

import org.zstack.header.core.Completion;
import org.zstack.header.datacenter.DataCenterInventory;

import java.util.List;
import java.util.Map;

/**
 * Created by mingjian.deng on 2017/8/3.
 */
public interface ExpressExtensionPoint {
    void afterSyncAccessPoint(List<ConnectionAccessPointInventory> invs, Map map, Completion completion);
    void syncAccessPoint(DataCenterInventory inv, Map map, Completion completion);

    void afterSyncVbr(List<VirtualBorderRouterInventory> invs, Map map, Completion completion);
    void syncVbr(DataCenterInventory inv, Map map, Completion completion);

    void afterSyncRouterInterface(List<AliyunRouterInterfaceInventory> invs, Map map, Completion completion);
    void syncRouterInterface(List<String> dcUuids, Map map, Completion completion);
}
