package org.zstack.header.aliyun.network.vrouter;

import org.springframework.http.HttpMethod;
import org.zstack.header.aliyun.network.AliyunNetworkMessage;
import org.zstack.header.hybrid.network.HybridNetworkConstant;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 17/5/6.
 */
@Action(category = HybridNetworkConstant.ACTION_CATEGORY, names = {"read"})
@RestRequest(
        path = "/hybrid/aliyun/vrouter/{uuid}",
        method = HttpMethod.DELETE,
        responseClass = APIDeleteVirtualRouterLocalEvent.class
)
public class APIDeleteVirtualRouterLocalMsg extends APIDeleteMessage implements AliyunNetworkMessage {
    @APIParam(resourceType = VpcVirtualRouterVO.class, checkAccount = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public static APIDeleteVirtualRouterLocalMsg __example__() {
        APIDeleteVirtualRouterLocalMsg msg = new APIDeleteVirtualRouterLocalMsg();
        msg.setUuid(uuid());
        return msg;
    }
    
}