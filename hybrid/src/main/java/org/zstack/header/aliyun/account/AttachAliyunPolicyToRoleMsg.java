package org.zstack.header.aliyun.account;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 2017/8/15.
 */
public class AttachAliyunPolicyToRoleMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    String roleName;
    String policyName;
    String policyType;

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getPolicyName() {
        return policyName;
    }

    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }
}
