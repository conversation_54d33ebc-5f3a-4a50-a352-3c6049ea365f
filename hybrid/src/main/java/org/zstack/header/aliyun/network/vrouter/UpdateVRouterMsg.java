package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 2017/7/27.
 */
public class UpdateVRouterMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String vRouterId;
    private String name;
    private String description;

    public String getvRouterId() {
        return vRouterId;
    }

    public void setvRouterId(String vRouterId) {
        this.vRouterId = vRouterId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
