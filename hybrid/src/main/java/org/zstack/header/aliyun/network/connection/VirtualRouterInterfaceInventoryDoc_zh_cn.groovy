package org.zstack.header.aliyun.network.connection

import org.zstack.header.aliyun.network.vrouter.VRouterType

doc {

	title "虚拟路由接口清单"

	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "0.6"
	}
	field {
		name "dataCenterUuid"
		desc "区域UUID"
		type "String"
		since "0.6"
	}
	field {
		name "routerInterfaceId"
		desc "路由接口ID"
		type "String"
		since "0.6"
	}
	field {
		name "virtualRouterUuid"
		desc "云路由UUID"
		type "String"
		since "0.6"
	}
	field {
		name "accessPointUuid"
		desc "接入点UUID"
		type "String"
		since "0.6"
	}
	field {
		name "role"
		desc "作用"
		type "String"
		since "0.6"
	}
	ref {
		name "vRouterType"
		path "org.zstack.header.aliyun.network.connection.AliyunRouterInterfaceInventory.vRouterType"
		desc "虚拟路由类型"
		type "VRouterType"
		since "0.6"
		clz VRouterType.class
	}
	field {
		name "spec"
		desc "详细"
		type "String"
		since "0.6"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "0.6"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "0.6"
	}
	field {
		name "status"
		desc "状态"
		type "String"
		since "0.6"
	}
	field {
		name "oppositeInterfaceUuid"
		desc "对立路由接口UUID"
		type "String"
		since "0.6"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "0.6"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "0.6"
	}
}
