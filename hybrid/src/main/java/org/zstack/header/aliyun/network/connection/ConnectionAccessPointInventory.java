package org.zstack.header.aliyun.network.connection;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/10.
 */
@Inventory(mappingVOClass = ConnectionAccessPointVO.class)
@PythonClassInventory
public class ConnectionAccessPointInventory implements Serializable {
    private String uuid;
    private String accessPointId;
    private String type;
    private String name;
    private String dataCenterUuid;
    private String description;
    private String status;
    private String hostOperator;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public ConnectionAccessPointInventory() {
    }

    public ConnectionAccessPointInventory(ConnectionAccessPointInventory other) {
        uuid = other.getUuid();
        accessPointId = other.getAccessPointId();
        type = other.getType();
        name = other.getName();
        dataCenterUuid = other.getDataCenterUuid();
        description = other.getDescription();
        status = other.getStatus();
        hostOperator = other.getHostOperator();
        createDate = other.getCreateDate();
        lastOpDate = other.getLastOpDate();
    }

    public static ConnectionAccessPointInventory valueOf(ConnectionAccessPointVO vo) {
        ConnectionAccessPointInventory inventory = new ConnectionAccessPointInventory();
        inventory.setUuid(vo.getUuid());
        inventory.setAccessPointId(vo.getAccessPointId());
        inventory.setType(vo.getType());
        inventory.setName(vo.getName());
        inventory.setDataCenterUuid(vo.getDataCenterUuid());
        inventory.setDescription(vo.getDescription());
        inventory.setStatus(vo.getStatus());
        inventory.setHostOperator(vo.getHostOperator());
        inventory.setCreateDate(vo.getCreateDate());
        inventory.setLastOpDate(vo.getLastOpDate());
        return inventory;
    }

    public static List<ConnectionAccessPointInventory> valueOf(Collection<ConnectionAccessPointVO> vos) {
        List<ConnectionAccessPointInventory> inventories = new ArrayList<>();
        for(ConnectionAccessPointVO vo: vos) {
            inventories.add(valueOf(vo));
        }
        return inventories;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getAccessPointId() {
        return accessPointId;
    }

    public void setAccessPointId(String accessPointId) {
        this.accessPointId = accessPointId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDataCenterUuid() {
        return dataCenterUuid;
    }

    public void setDataCenterUuid(String dataCenterUuid) {
        this.dataCenterUuid = dataCenterUuid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getHostOperator() {
        return hostOperator;
    }

    public void setHostOperator(String hostOperator) {
        this.hostOperator = hostOperator;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
