package org.zstack.header.aliyun.network.vrouter;

/**
 * Created by ming<PERSON><PERSON>.deng on 17/4/8.
 */
public enum NextHopType {
    instance("Instance"),
    service("service"),
    local("local"),
    vpn("VpnGateway"),
    nat("NatGateway"),
    vip("HaVip"),
    routerinterface("RouterInterface"),
    ecmp("");

    String type;

    NextHopType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return type;
    }

    public static NextHopType get(String type) {
        for (NextHopType tmp: NextHopType.values()) {
            if (tmp.toString().equalsIgnoreCase(type)) {
                return tmp;
            }
        }
        return NextHopType.valueOf(type);
    }

    public static boolean isKnown(String type) {
        for (NextHopType tmp: NextHopType.values()) {
            if (tmp.toString().equalsIgnoreCase(type)) {
                return true;
            }
            if (tmp.name().equalsIgnoreCase(type)) {
                return true;
            }
        }
        return false;
    }

    public static String getString(String type) {
        for (NextHopType tmp: NextHopType.values()) {
            if (tmp.toString().equalsIgnoreCase(type)) {
                return tmp.name();
            }
        }
        return type;
    }
}
