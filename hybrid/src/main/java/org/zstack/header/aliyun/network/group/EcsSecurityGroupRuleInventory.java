package org.zstack.header.aliyun.network.group;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by mingjian.deng on 17/3/20.
 */
@Inventory(mappingVOClass = EcsSecurityGroupRuleVO.class)
@PythonClassInventory
public class EcsSecurityGroupRuleInventory implements Serializable {
    private String uuid;
    private String ecsSecurityGroupUuid;
    private String protocol;
    private String portRange;
    private String cidrIp;
    private String priority;
    private String direction;
    private String nicType;
    private String policy;
    private String description;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public EcsSecurityGroupRuleInventory() {
    }

    public EcsSecurityGroupRuleInventory(EcsSecurityGroupRuleInventory other) {
        uuid = other.getUuid();
        ecsSecurityGroupUuid = other.getEcsSecurityGroupUuid();
        protocol = other.getProtocol();
        portRange = other.getPortRange();
        cidrIp = other.getCidrIp();
        nicType = other.getNicType();
        policy = other.getPolicy();
        direction = other.getDirection();
        priority = other.getPriority();
        description = other.getDescription();
        createDate = other.getCreateDate();
        lastOpDate = other.getLastOpDate();
    }

    public static EcsSecurityGroupRuleInventory valueOf(EcsSecurityGroupRuleVO vo) {
        EcsSecurityGroupRuleInventory inventory = new EcsSecurityGroupRuleInventory();
        inventory.setUuid(vo.getUuid());
        inventory.setEcsSecurityGroupUuid(vo.getEcsSecurityGroupUuid());
        inventory.setPolicy(vo.getPolicy());
        inventory.setDirection(vo.getDirection());
        inventory.setProtocol(vo.getProtocol());
        inventory.setPortRange(vo.getPortRange());
        inventory.setCidrIp(vo.getCidrIp());
        inventory.setPriority(vo.getPriority());
        inventory.setNicType(vo.getNicType());
        inventory.setDescription(vo.getDescription());
        inventory.setCreateDate(vo.getCreateDate());
        inventory.setLastOpDate(vo.getLastOpDate());
        return inventory;
    }

    public static List<EcsSecurityGroupRuleInventory> valueOf(Collection<EcsSecurityGroupRuleVO> vos) {
        List<EcsSecurityGroupRuleInventory> inventories = new ArrayList<>();
        for(EcsSecurityGroupRuleVO vo: vos) {
            inventories.add(valueOf(vo));
        }
        return inventories;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getEcsSecurityGroupUuid() {
        return ecsSecurityGroupUuid;
    }

    public void setEcsSecurityGroupUuid(String ecsSecurityGroupUuid) {
        this.ecsSecurityGroupUuid = ecsSecurityGroupUuid;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getPortRange() {
        return portRange;
    }

    public void setPortRange(String portRange) {
        this.portRange = portRange;
    }

    public String getCidrIp() {
        return cidrIp;
    }

    public void setCidrIp(String cidrIp) {
        this.cidrIp = cidrIp;
    }

    public String getNicType() {
        return nicType;
    }

    public void setNicType(String nicType) {
        this.nicType = nicType;
    }

    public String getPolicy() {
        return policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
