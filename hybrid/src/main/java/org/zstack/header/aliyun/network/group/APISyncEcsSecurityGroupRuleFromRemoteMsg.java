package org.zstack.header.aliyun.network.group;

import org.springframework.http.HttpMethod;
import org.zstack.header.aliyun.network.AliyunNetworkMessage;
import org.zstack.header.hybrid.network.HybridNetworkConstant;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 17/5/13.
 */
@Action(category = HybridNetworkConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/hybrid/aliyun/security-group-rule/{uuid}/sync",
        responseClass = APISyncEcsSecurityGroupRuleFromRemoteEvent.class,
        isAction = true,
        method = HttpMethod.PUT
)
public class APISyncEcsSecurityGroupRuleFromRemoteMsg extends APICreateMessage implements AliyunNetworkMessage {
    @APIParam(resourceType = EcsSecurityGroupVO.class, checkAccount = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public static APISyncEcsSecurityGroupRuleFromRemoteMsg __example__() {
        APISyncEcsSecurityGroupRuleFromRemoteMsg msg = new APISyncEcsSecurityGroupRuleFromRemoteMsg();
        msg.setUuid(uuid());
        return msg;
    }
    
}