package org.zstack.header.aliyun.network.connection;

import org.zstack.hybrid.core.HybridNeedReplyMessage;

/**
 * Created by mingjian.deng on 2017/8/3.
 */
public class SyncRouterInterfaceInnerMsg extends HybridNeedReplyMessage {
    private String dataCenterUuid;

    public String getDataCenterUuid() {
        return dataCenterUuid;
    }

    public void setDataCenterUuid(String dataCenterUuid) {
        this.dataCenterUuid = dataCenterUuid;
    }
}
