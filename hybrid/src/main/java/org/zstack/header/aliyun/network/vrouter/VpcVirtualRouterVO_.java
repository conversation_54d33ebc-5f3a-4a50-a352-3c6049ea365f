package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

/**
 * Created by ming<PERSON><PERSON>.deng on 17/4/8.
 */
@StaticMetamodel(VpcVirtualRouterVO.class)
public class VpcVirtualRouterVO_ extends ResourceVO_{
    public static volatile SingularAttribute<VpcVirtualRouterVO, String> vrId;
    public static volatile SingularAttribute<VpcVirtualRouterVO, String> vpcUuid;
    public static volatile SingularAttribute<VpcVirtualRouterVO, String> name;
    public static volatile SingularAttribute<VpcVirtualRouterVO, String> description;
    public static volatile SingularAttribute<VpcVirtualRouterVO, Timestamp> createDate;
    public static volatile SingularAttribute<VpcVirtualRouterVO, Timestamp> lastOpDate;
}
