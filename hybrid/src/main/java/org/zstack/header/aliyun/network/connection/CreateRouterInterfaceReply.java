package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.ProcessAliyunReply;

/**
 * Created by mingjian.deng on 17/4/12.
 */
public class CreateRouterInterfaceReply extends ProcessAliyunReply {
    private String routerInterfaceId;
    private String status;

    public String getRouterInterfaceId() {
        return routerInterfaceId;
    }

    public void setRouterInterfaceId(String routerInterfaceId) {
        this.routerInterfaceId = routerInterfaceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
