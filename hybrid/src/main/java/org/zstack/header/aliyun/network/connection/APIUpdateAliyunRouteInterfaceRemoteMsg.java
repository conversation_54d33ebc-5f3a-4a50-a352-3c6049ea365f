package org.zstack.header.aliyun.network.connection;

import org.springframework.http.HttpMethod;
import org.zstack.header.aliyun.network.AliyunNetworkMessage;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@Action(category = ConnectionConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/hybrid/aliyun/router-interface/{uuid}/actions",
        isAction = true,
        method = HttpMethod.PUT,
        responseClass = APIUpdateAliyunRouteInterfaceRemoteEvent.class
)
public class APIUpdateAliyunRouteInterfaceRemoteMsg extends APIMessage implements AliyunNetworkMessage {
    @APIParam(resourceType = AliyunRouterInterfaceVO.class, checkAccount = true)
    private String uuid;
    @APIParam(validValues = {"active", "inactive"})
    private String op;
    @APIParam(validValues = {"vbr", "vrouter"})
    private String vRouterType;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getOp() {
        return op;
    }

    public void setOp(String op) {
        this.op = op;
    }

    public String getvRouterType() {
        return vRouterType;
    }

    public void setvRouterType(String vRouterType) {
        this.vRouterType = vRouterType;
    }

    public static APIUpdateAliyunRouteInterfaceRemoteMsg __example__() {
        APIUpdateAliyunRouteInterfaceRemoteMsg msg = new APIUpdateAliyunRouteInterfaceRemoteMsg();
        msg.setUuid(uuid());
        msg.setOp("active");
        msg.setvRouterType("vbr");
        return msg;
    }
    
}
