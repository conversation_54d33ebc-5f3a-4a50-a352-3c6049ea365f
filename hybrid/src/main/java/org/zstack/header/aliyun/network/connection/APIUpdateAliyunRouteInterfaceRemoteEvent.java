package org.zstack.header.aliyun.network.connection;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@RestResponse
public class APIUpdateAliyunRouteInterfaceRemoteEvent extends APIEvent {
    public APIUpdateAliyunRouteInterfaceRemoteEvent() {
        super(null);
    }

    public APIUpdateAliyunRouteInterfaceRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APIUpdateAliyunRouteInterfaceRemoteEvent __example__() {
        APIUpdateAliyunRouteInterfaceRemoteEvent event = new APIUpdateAliyunRouteInterfaceRemoteEvent();
        return event;
    }
    
}