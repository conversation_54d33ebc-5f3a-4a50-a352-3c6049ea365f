package org.zstack.header.aliyun.network.group

import org.zstack.header.errorcode.ErrorCode
import org.zstack.header.aliyun.network.group.EcsSecurityGroupInventory

doc {

	title "阿里云安全组清单"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.header.aliyun.network.group.APICreateEcsSecurityGroupRemoteEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
	ref {
		name "inventory"
		path "org.zstack.header.aliyun.network.group.APICreateEcsSecurityGroupRemoteEvent.inventory"
		desc "null"
		type "EcsSecurityGroupInventory"
		since "0.6"
		clz EcsSecurityGroupInventory.class
	}
}
