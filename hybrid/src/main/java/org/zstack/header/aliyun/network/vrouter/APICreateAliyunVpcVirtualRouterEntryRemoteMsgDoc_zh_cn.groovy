package org.zstack.header.aliyun.network.vrouter

import org.zstack.header.aliyun.network.vrouter.APICreateAliyunVpcVirtualRouterEntryRemoteEvent

doc {
    title "创建远程阿里云VPC虚拟路由（CreateAliyunVpcVirtualRouterEntryRemote）"

    category "aliyun.network.vrouter"

    desc """创建远程阿里云VPC虚拟路由"""

    rest {
        request {
			url "POST /v1/hybrid/aliyun/route-entry"

			header (Authorization: 'OAuth the-session-uuid')

            clz APICreateAliyunVpcVirtualRouterEntryRemoteMsg.class

            desc """创建远程阿里云VPC虚拟路由"""
            
			params {

				column {
					name "vRouterUuid"
					enclosedIn "params"
					desc "虚拟路由UUID"
					location "body"
					type "String"
					optional false
					since "0.6"
				}
				column {
					name "dstCidrBlock"
					enclosedIn "params"
					desc "下一跳网段"
					location "body"
					type "String"
					optional false
					since "0.6"
				}
				column {
					name "nextHopId"
					enclosedIn "params"
					desc "下一跳Id"
					location "body"
					type "String"
					optional false
					since "0.6"
				}
				column {
					name "nextHopType"
					enclosedIn "params"
					desc "下一跳类型"
					location "body"
					type "String"
					optional false
					since "0.6"
					values ("Instance","RouterInterface")
				}
				column {
					name "vRouterType"
					enclosedIn "params"
					desc "虚拟路由类型"
					location "body"
					type "String"
					optional false
					since "0.6"
					values ("vbr","vrouter")
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc "资源UUID"
					location "body"
					type "String"
					optional true
					since "0.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
				column {
					name "nextHopUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional false
					since "0.6"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "3.4.0"
				}
			}
        }

        response {
            clz APICreateAliyunVpcVirtualRouterEntryRemoteEvent.class
        }
    }
}