package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/8.
 */
@Inventory(mappingVOClass = VpcVirtualRouterVO.class)
@PythonClassInventory
public class VpcVirtualRouterInventory implements Serializable {
    private String uuid;
    private String vrId;
    private String vpcUuid;
    private String name;
    private String description;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public VpcVirtualRouterInventory() {
    }

    public VpcVirtualRouterInventory(VpcVirtualRouterInventory other) {
        uuid = other.getUuid();
        vrId = other.getVrId();
        vpcUuid = other.getVpcUuid();
        name = other.getName();
        description = other.getDescription();
        createDate = other.getCreateDate();
        lastOpDate = other.getLastOpDate();
    }

    public static VpcVirtualRouterInventory valueOf(VpcVirtualRouterVO vo) {
        VpcVirtualRouterInventory inventory = new VpcVirtualRouterInventory();
        inventory.setUuid(vo.getUuid());
        inventory.setVrId(vo.getVrId());
        inventory.setVpcUuid(vo.getVpcUuid());
        inventory.setName(vo.getName());
        inventory.setDescription(vo.getDescription());
        inventory.setCreateDate(vo.getCreateDate());
        inventory.setLastOpDate(vo.getLastOpDate());
        return inventory;
    }

    public static List<VpcVirtualRouterInventory> valueOf(Collection<VpcVirtualRouterVO> vos) {
        List<VpcVirtualRouterInventory> inventories = new ArrayList<>();
        for(VpcVirtualRouterVO vo: vos) {
            inventories.add(valueOf(vo));
        }
        return inventories;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getVrId() {
        return vrId;
    }

    public void setVrId(String vrId) {
        this.vrId = vrId;
    }

    public String getVpcUuid() {
        return vpcUuid;
    }

    public void setVpcUuid(String vpcUuid) {
        this.vpcUuid = vpcUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
