package org.zstack.header.aliyun.network.connection;

import org.springframework.http.HttpMethod;
import org.zstack.header.aliyun.network.AliyunNetworkMessage;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@Action(category = ConnectionConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/hybrid/aliyun/border-router/{uuid}/actions",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APIUpdateVirtualBorderRouterRemoteEvent.class
)
public class APIUpdateVirtualBorderRouterRemoteMsg extends APIMessage implements AliyunNetworkMessage {
    @APIParam(resourceType = VirtualBorderRouterVO.class, checkAccount = true)
    private String uuid;
    @APIParam(required = false)
    private String localGatewayIp;
    @APIParam(required = false)
    private String peerGatewayIp;
    @APIParam(required = false)
    private String peeringSubnetMask;
    @APIParam(required = false, maxLength = 64)
    private String name;
    @APIParam(required = false, maxLength = 1024)
    private String description;
    @APIParam(required = false)
    private String vlanId;
    @APIParam(required = false)
    private String circuitCode;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getLocalGatewayIp() {
        return localGatewayIp;
    }

    public void setLocalGatewayIp(String localGatewayIp) {
        this.localGatewayIp = localGatewayIp;
    }

    public String getPeerGatewayIp() {
        return peerGatewayIp;
    }

    public void setPeerGatewayIp(String peerGatewayIp) {
        this.peerGatewayIp = peerGatewayIp;
    }

    public String getPeeringSubnetMask() {
        return peeringSubnetMask;
    }

    public void setPeeringSubnetMask(String peeringSubnetMask) {
        this.peeringSubnetMask = peeringSubnetMask;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVlanId() {
        return vlanId;
    }

    public void setVlanId(String vlanId) {
        this.vlanId = vlanId;
    }

    public String getCircuitCode() {
        return circuitCode;
    }

    public void setCircuitCode(String circuitCode) {
        this.circuitCode = circuitCode;
    }

    public static APIUpdateVirtualBorderRouterRemoteMsg __example__() {
        APIUpdateVirtualBorderRouterRemoteMsg msg = new APIUpdateVirtualBorderRouterRemoteMsg();
        msg.setUuid(uuid());
        msg.setLocalGatewayIp("***********");
        msg.setPeerGatewayIp("***********");
        msg.setPeeringSubnetMask("*************");
        msg.setName("test-name");
        msg.setDescription("description");
        msg.setVlanId("vlan-id");
        msg.setCircuitCode("code");
        return msg;
    }
    
}