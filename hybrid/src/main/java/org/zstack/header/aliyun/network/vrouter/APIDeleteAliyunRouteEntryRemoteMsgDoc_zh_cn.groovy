package org.zstack.header.aliyun.network.vrouter

import org.zstack.header.aliyun.network.vrouter.APIDeleteAliyunRouteEntryRemoteEvent

doc {
    title "删除远程阿里云路由（DeleteAliyunRouteEntryRemote）"

    category "aliyun.network.vrouter"

    desc """删除远程阿里云路由"""

    rest {
        request {
			url "DELETE /v1/hybrid/aliyun/route-entry/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeleteAliyunRouteEntryRemoteMsg.class

            desc """删除远程阿里云路由"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "0.6"
				}
				column {
					name "type"
					enclosedIn ""
					desc "类型"
					location "body"
					type "String"
					optional false
					since "0.6"
					values ("vbr","vrouter")
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc "删除策略"
					location "body"
					type "String"
					optional true
					since "0.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
			}
        }

        response {
            clz APIDeleteAliyunRouteEntryRemoteEvent.class
        }
    }
}