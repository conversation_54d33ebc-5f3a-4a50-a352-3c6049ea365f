package org.zstack.header.aliyun.network.vrouter;

/**
 * Created by mingjian.deng on 17/4/10.
 */
public class AliyunVirtualRouteEntryProperty {
    private String destinationCidrBlock;
    private String nextHopId;
    private String nextHopType;
    private String type;
    private String status;

    public String getDestinationCidrBlock() {
        return destinationCidrBlock;
    }

    public void setDestinationCidrBlock(String destinationCidrBlock) {
        this.destinationCidrBlock = destinationCidrBlock;
    }

    public String getNextHopId() {
        return nextHopId;
    }

    public void setNextHopId(String nextHopId) {
        this.nextHopId = nextHopId;
    }

    public String getNextHopType() {
        return nextHopType;
    }

    public void setNextHopType(String nextHopType) {
        this.nextHopType = nextHopType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof VpcVirtualRouteEntryVO) {
            return destinationCidrBlock.equals(((VpcVirtualRouteEntryVO) obj).getDestinationCidrBlock());
        } else if (obj instanceof AliyunVirtualRouteEntryProperty) {
            return destinationCidrBlock.equals(((AliyunVirtualRouteEntryProperty) obj).getDestinationCidrBlock());
        } else {
            return super.equals(obj);
        }
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
