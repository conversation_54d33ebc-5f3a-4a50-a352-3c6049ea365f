package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.message.MessageReply;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 17/6/26.
 */
public class SyncAliyunVpcRouteEntryInnerReply extends MessageReply {
    private List<VpcVirtualRouteEntryInventory> inventories = new ArrayList<>();

    public List<VpcVirtualRouteEntryInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<VpcVirtualRouteEntryInventory> inventories) {
        this.inventories = inventories;
    }
}
