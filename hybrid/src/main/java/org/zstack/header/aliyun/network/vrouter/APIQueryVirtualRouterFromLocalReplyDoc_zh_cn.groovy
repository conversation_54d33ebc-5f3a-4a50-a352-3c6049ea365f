package org.zstack.header.aliyun.network.vrouter

import org.zstack.header.errorcode.ErrorCode
import org.zstack.header.aliyun.network.vrouter.VpcVirtualRouterInventory

doc {

	title "阿里云虚拟路由清单"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.header.aliyun.network.vrouter.APIQueryAliyunVirtualRouterFromLocalReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
	ref {
		name "inventories"
		path "org.zstack.header.aliyun.network.vrouter.APIQueryAliyunVirtualRouterFromLocalReply.inventories"
		desc "null"
		type "List"
		since "0.6"
		clz VpcVirtualRouterInventory.class
	}
}
