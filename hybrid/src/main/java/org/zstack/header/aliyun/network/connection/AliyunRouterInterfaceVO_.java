package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.network.vrouter.VRouterType;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

/**
 * Created by ming<PERSON><PERSON>.deng on 17/4/10.
 */
@StaticMetamodel(AliyunRouterInterfaceVO.class)
public class AliyunRouterInterfaceVO_ {
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> uuid;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> dataCenterUuid;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> routerInterfaceId;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> virtualRouterUuid;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> accessPointUuid;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, VRouterType> vRouterType;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> role;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> spec;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> name;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> status;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> oppositeInterfaceUuid;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, String> description;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, Timestamp> createDate;
    public static volatile SingularAttribute<AliyunRouterInterfaceVO, Timestamp> lastOpDate;
}
