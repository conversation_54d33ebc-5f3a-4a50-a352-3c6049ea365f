package org.zstack.header.aliyun.network.group;

import org.springframework.http.HttpMethod;
import org.zstack.header.aliyun.network.AliyunNetworkMessage;
import org.zstack.header.aliyun.network.EcsSecurityGroupRuleStrategy;
import org.zstack.header.aliyun.network.vpc.EcsVpcVO;
import org.zstack.header.hybrid.network.HybridNetworkConstant;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 17/5/9.
 */
@Action(category = HybridNetworkConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/hybrid/aliyun/security-group/remote",
        responseClass = APICreateEcsSecurityGroupRemoteEvent.class,
        parameterName = "params",
        method = HttpMethod.POST
)
public class APICreateEcsSecurityGroupRemoteMsg extends APICreateMessage implements AliyunNetworkMessage {
    @APIParam(resourceType = EcsVpcVO.class, checkAccount = true)
    private String vpcUuid;
    @APIParam(maxLength = 256, required = false)
    private String description;
    @APIParam(maxLength = 64, emptyString = false)
    private String name;
    @APIParam(required = false, validValues = { EcsSecurityGroupRuleStrategy.all,
            EcsSecurityGroupRuleStrategy.security, EcsSecurityGroupRuleStrategy.basic})
    private String strategy;

    public String getVpcUuid() {
        return vpcUuid;
    }

    public void setVpcUuid(String vpcUuid) {
        this.vpcUuid = vpcUuid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public static APICreateEcsSecurityGroupRemoteMsg __example__() {
        APICreateEcsSecurityGroupRemoteMsg msg = new APICreateEcsSecurityGroupRemoteMsg();
        msg.setVpcUuid(uuid());
        msg.setDescription("description");
        msg.setName("test-name");
        msg.setStrategy(EcsSecurityGroupRuleStrategy.security);
        return msg;
    }
    
}