package org.zstack.header.aliyun.network.group

import org.zstack.header.aliyun.network.group.APIDeleteEcsSecurityGroupRuleRemoteEvent

doc {
    title "DeleteEcsSecurityGroupRuleRemote"

    category "aliyun.network.group"

    desc """删除远程阿里云安全组规则"""

    rest {
        request {
			url "DELETE /v1/hybrid/aliyun/security-group-rule/remote/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeleteEcsSecurityGroupRuleRemoteMsg.class

            desc """删除远程阿里云安全组规则"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "0.6"
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc "删除策略"
					location "body"
					type "String"
					optional true
					since "0.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "0.6"
				}
			}
        }

        response {
            clz APIDeleteEcsSecurityGroupRuleRemoteEvent.class
        }
    }
}