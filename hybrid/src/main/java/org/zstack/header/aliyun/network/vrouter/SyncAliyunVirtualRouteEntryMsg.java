package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.aliyun.AliyunClientMessage;
import org.zstack.header.aliyun.ProcessAliyunMsg;

/**
 * Created by mingjian.deng on 17/4/10.
 */
public class SyncAliyunVirtualRouteEntryMsg extends ProcessAliyunMsg implements AliyunClientMessage {
    private String vRouterId;

    private String vRouterType;

    public String getvRouterId() {
        return vRouterId;
    }

    public void setvRouterId(String vRouterId) {
        this.vRouterId = vRouterId;
    }

    public String getvRouterType() {
        return vRouterType;
    }

    public void setvRouterType(String vRouterType) {
        this.vRouterType = vRouterType;
    }
}
