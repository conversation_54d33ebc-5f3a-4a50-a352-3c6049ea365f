package org.zstack.header.aliyun.account

import org.zstack.header.aliyun.account.APIAddAliyunKeySecretEvent

doc {
    title "添加阿里云密钥（AddAliyunKeySecret）"

    category "aliyun.account"

    desc """添加阿里云密钥"""

    rest {
        request {
			url "POST /v1/hybrid/aliyun/key"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIAddAliyunKeySecretMsg.class

            desc """添加阿里云密钥"""
            
			params {

				column {
					name "key"
					enclosedIn "params"
					desc "密钥ID"
					location "body"
					type "String"
					optional false
					since "2.1"
				}
				column {
					name "secret"
					enclosedIn "params"
					desc "密钥"
					location "body"
					type "String"
					optional false
					since "2.1"
				}
				column {
					name "accountUuid"
					enclosedIn "params"
					desc "账户UUID"
					location "body"
					type "String"
					optional true
					since "2.1"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "阿里云密钥"
					location "body"
					type "String"
					optional true
					since "2.1"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc "资源UUID"
					location "body"
					type "String"
					optional true
					since "2.1"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "2.1"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "2.1"
				}
				column {
					name "name"
					enclosedIn "params"
					desc "资源名称"
					location "body"
					type "String"
					optional false
					since "2.1"
				}
				column {
					name "sync"
					enclosedIn "params"
					desc ""
					location "body"
					type "Boolean"
					optional true
					since "0.6"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "3.4.0"
				}
			}
        }

        response {
            clz APIAddAliyunKeySecretEvent.class
        }
    }
}