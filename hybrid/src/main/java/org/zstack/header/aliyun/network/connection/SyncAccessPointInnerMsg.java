package org.zstack.header.aliyun.network.connection;

import org.zstack.hybrid.core.HybridNeedReplyMessage;

/**
 * Created by mingjian.deng on 2017/7/19.
 */
public class SyncAccessPointInnerMsg extends HybridNeedReplyMessage {
    private String dataCenterUuid;
    private String apId;

    public String getDataCenterUuid() {
        return dataCenterUuid;
    }

    public void setDataCenterUuid(String dataCenterUuid) {
        this.dataCenterUuid = dataCenterUuid;
    }

    public String getApId() {
        return apId;
    }

    public void setApId(String apId) {
        this.apId = apId;
    }
}
