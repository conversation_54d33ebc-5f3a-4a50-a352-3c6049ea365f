package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.aliyun.ProcessAliyunReply;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/8.
 */
public class SyncAliyunVirtualRouterReply extends ProcessAliyunReply {
    List<AliyunVirtualRouterProperty> vrs = new ArrayList<>();

    public List<AliyunVirtualRouterProperty> getVrs() {
        return vrs;
    }

    public void setVrs(List<AliyunVirtualRouterProperty> vrs) {
        this.vrs.addAll(vrs);
    }
}
