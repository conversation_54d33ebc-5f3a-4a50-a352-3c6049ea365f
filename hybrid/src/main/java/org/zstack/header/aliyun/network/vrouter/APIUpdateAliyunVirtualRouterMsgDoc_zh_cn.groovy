package org.zstack.header.aliyun.network.vrouter

import org.zstack.header.aliyun.network.vrouter.APIUpdateAliyunVirtualRouterEvent

doc {
    title "UpdateAliyunVirtualRouter"

    category "aliyun.network.vrouter"

    desc """更新阿里云路由"""

    rest {
        request {
			url "PUT /v1/hybrid/aliyun/vrouter/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIUpdateAliyunVirtualRouterMsg.class

            desc """更新阿里云路由"""
            
			params {

				column {
					name "uuid"
					enclosedIn "updateAliyunVirtualRouter"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "2.2"
				}
				column {
					name "name"
					enclosedIn "updateAliyunVirtualRouter"
					desc "资源名称"
					location "body"
					type "String"
					optional true
					since "2.2"
				}
				column {
					name "description"
					enclosedIn "updateAliyunVirtualRouter"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "2.2"
				}
				column {
					name "resourceUuid"
					enclosedIn "updateAliyunVirtualRouter"
					desc ""
					location "body"
					type "String"
					optional true
					since "2.2"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "2.2"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "2.2"
				}
			}
        }

        response {
            clz APIUpdateAliyunVirtualRouterEvent.class
        }
    }
}