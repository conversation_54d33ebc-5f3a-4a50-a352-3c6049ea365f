package org.zstack.header.aliyun.network.connection;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * Created by mingjian.deng on 17/4/7.
 */
@RestResponse(allTo = "inventory")
public class APIUpdateVirtualBorderRouterRemoteEvent extends APIEvent {
    VirtualBorderRouterInventory inventory;

    public VirtualBorderRouterInventory getInventory() {
        return inventory;
    }

    public void setInventory(VirtualBorderRouterInventory inventory) {
        this.inventory = inventory;
    }

    public APIUpdateVirtualBorderRouterRemoteEvent() {
        super(null);
    }

    public APIUpdateVirtualBorderRouterRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APIUpdateVirtualBorderRouterRemoteEvent __example__() {
        APIUpdateVirtualBorderRouterRemoteEvent event = new APIUpdateVirtualBorderRouterRemoteEvent();
        VirtualBorderRouterInventory inventory = new VirtualBorderRouterInventory();
        inventory.setUuid(uuid());
        inventory.setAccessPointUuid(uuid());
        inventory.setCircuitCode("");
        inventory.setDataCenterUuid(uuid());
        inventory.setName("test-vbr");
        inventory.setVbrId("test-vbr-id");
        inventory.setVlanId("2995");
        inventory.setVlanInterfaceId("interface-id");
        inventory.setPeerGatewayIp("*********");
        inventory.setPeeringSubnetMask("***************");
        inventory.setPhysicalConnectionId("pc-234lakg81");
        inventory.setPhysicalConnectionStatus("Enabled");
        inventory.setStatus("active");
        inventory.setLocalGatewayIp("*********");
        event.setInventory(inventory);
        return event;
    }
    
}