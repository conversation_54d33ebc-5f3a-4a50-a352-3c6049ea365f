package org.zstack.header.aliyun.network.vrouter;

import org.springframework.http.HttpMethod;
import org.zstack.header.aliyun.network.AliyunNetworkMessage;
import org.zstack.header.aliyun.network.vpc.EcsVpcVO;
import org.zstack.header.hybrid.network.HybridNetworkConstant;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

/**
 * Created by mingjian.deng on 17/4/8.
 */
@Action(category = HybridNetworkConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/hybrid/aliyun/vrouter/{vpcUuid}/sync",
        responseClass = APISyncAliyunVirtualRouterFromRemoteEvent.class,
        isAction = true,
        method = HttpMethod.PUT
)
public class APISyncAliyunVirtualRouterFromRemoteMsg extends APICreateMessage implements AliyunNetworkMessage {
    @APIParam(resourceType = EcsVpcVO.class, checkAccount = true)
    private String vpcUuid;

    public String getVpcUuid() {
        return vpcUuid;
    }

    public void setVpcUuid(String vpcUuid) {
        this.vpcUuid = vpcUuid;
    }

    public static APISyncAliyunVirtualRouterFromRemoteMsg __example__() {
        APISyncAliyunVirtualRouterFromRemoteMsg msg = new APISyncAliyunVirtualRouterFromRemoteMsg();
        msg.setVpcUuid(uuid());
        return msg;
    }
    
}
