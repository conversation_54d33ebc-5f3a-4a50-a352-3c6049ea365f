package org.zstack.header.aliyun.network.vrouter;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

import java.sql.Timestamp;

/**
 * Created by mingjian.deng on 17/5/16.
 */
@RestResponse(allTo = "inventory")
public class APICreateAliyunVpcVirtualRouterEntryRemoteEvent extends APIEvent {
    private VpcVirtualRouteEntryInventory inventory;

    public VpcVirtualRouteEntryInventory getInventory() {
        return inventory;
    }

    public void setInventory(VpcVirtualRouteEntryInventory inventory) {
        this.inventory = inventory;
    }

    public APICreateAliyunVpcVirtualRouterEntryRemoteEvent() {
        super(null);
    }

    public APICreateAliyunVpcVirtualRouterEntryRemoteEvent(String apiId) {
        super(apiId);
    }

    public static APICreateAliyunVpcVirtualRouterEntryRemoteEvent __example__() {
        APICreateAliyunVpcVirtualRouterEntryRemoteEvent event = new APICreateAliyunVpcVirtualRouterEntryRemoteEvent();
        VpcVirtualRouteEntryInventory inventory = new VpcVirtualRouteEntryInventory();
        inventory.setUuid(uuid());
        inventory.setType("");
        inventory.setvRouterType(VRouterType.vrouter.toString());
        inventory.setStatus("Running");
        inventory.setDestinationCidrBlock("");
        inventory.setNextHopId("ecs-id-1");
        inventory.setVirtualRouterUuid(uuid());
        inventory.setNextHopType("Instance");
        inventory.setCreateDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        inventory.setLastOpDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        event.setInventory(inventory);
        return event;
    }
}
