package org.zstack.header.aliyun.network.connection;

import org.zstack.header.aliyun.ProcessAliyunReply;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by mingjian.deng on 17/4/11.
 */
public class SyncRouterInterfaceReply extends ProcessAliyunReply {
    List<RouterInterfaceProperty> vrs = new ArrayList<>();

    public List<RouterInterfaceProperty> getVrs() {
        return vrs;
    }

    public void setVrs(List<RouterInterfaceProperty> vrs) {
        this.vrs.addAll(vrs);
    }
}
