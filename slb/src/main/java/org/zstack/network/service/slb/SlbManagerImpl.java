package org.zstack.network.service.slb;

import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.appliancevm.*;
import org.zstack.compute.vm.CleanUpAfterVmChangeImageExtensionPoint;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventCallback;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.*;
import org.zstack.core.defer.Defer;
import org.zstack.core.defer.Deferred;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.ha.*;
import org.zstack.header.AbstractService;
import org.zstack.header.affinitygroup.*;
import org.zstack.header.allocator.HostAllocatorConstant;
import org.zstack.header.configuration.*;
import org.zstack.header.core.Completion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.image.ImageVO;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.message.NeedReplyMessage;
import org.zstack.header.network.l3.*;
import org.zstack.header.network.service.NetworkServiceL3NetworkRefVO;
import org.zstack.header.network.service.NetworkServiceType;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.rest.SyncHttpCallHandler;
import org.zstack.header.vipQos.VpcSharedQosVO;
import org.zstack.header.vipQos.VpcSharedQosVO_;
import org.zstack.header.vm.*;
import org.zstack.header.vpc.VpcConstants;
import org.zstack.header.vpc.VpcRouterVmInventory;
import org.zstack.header.vpc.VpcRouterVmVO;
import org.zstack.header.vpc.ha.*;
import org.zstack.identity.Account;
import org.zstack.network.service.flat.L3NetworkGetIpStatisticExtensionPoint;
import org.zstack.network.service.lb.*;
import org.zstack.network.service.vip.*;
import org.zstack.network.service.virtualrouter.*;
import org.zstack.network.service.virtualrouter.lb.LbConfigProxy;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6Constants;
import org.zstack.vpc.ha.VpcHaGroupOperator;

import javax.persistence.Tuple;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

public class SlbManagerImpl extends AbstractService implements SlbManager, VipReleaseExtensionPoint,
        GetCandidateVmNicsForLoadBalancerExtensionPoint, GetPeerL3NetworksForLoadBalancerExtensionPoint,
        L3NetworkGetIpStatisticExtensionPoint, VmFencerRuleExtensionPoint , GetProviderUuidOfNetworkServiceUseTheVip,
        CleanUpAfterVmChangeImageExtensionPoint {
    private static final CLogger logger = Utils.getLogger(SlbManagerImpl.class);

    @Autowired
    CloudBus bus;
    @Autowired
    DatabaseFacade dbf;
    @Autowired
    private ApplianceVmFacade apvmf;
    @Autowired
    protected PluginRegistry pluginRgty;
    @Autowired
    private LoadBalancerExtension loadBalancerExtension;
    @Autowired
    private SlbOfferingFactory slbOfferingFactory;
    @Autowired
    private LbConfigProxy proxy;
    @Autowired
    private RESTFacade restf;
    @Autowired
    protected EventFacade evtf;

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIGetLoadBalancerOwnerMsg) {
            handle((APIGetLoadBalancerOwnerMsg) msg);
        } else if (msg instanceof APICreateSlbGroupMsg) {
            handle((APICreateSlbGroupMsg)msg);
        } else if (msg instanceof VmInstanceMessage) {
            handleSlbVmInstanceMessage((VmInstanceMessage)msg);
        } else if (msg instanceof SlbGroupMessage) {
            handleSlbGroupMessage((SlbGroupMessage) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APIGetLoadBalancerOwnerMsg msg) {
        APIGetLoadBalancerOwnerReply reply = new APIGetLoadBalancerOwnerReply();
        LoadBalancerVO lbVO = dbf.findByUuid(msg.getLoadBalancerUuid(), LoadBalancerVO.class);
        if (lbVO.getType() == LoadBalancerType.SLB) {
            SlbLoadBalancerVO slb = dbf.findByUuid(msg.getLoadBalancerUuid(), SlbLoadBalancerVO.class);
            SlbGroupVO group = dbf.findByUuid(slb.getSlbGroupUuid(), SlbGroupVO.class);
            reply.setType(APIGetLoadBalancerOwnerReply.LoadBalancerOwnerType.SLB.toString());
            reply.setSlb(SlbGroupInventory.valueOf(group));
        } else {
            List<String> vrUuids = proxy.getVrUuidsByNetworkService(
                    LoadBalancerVO.class.getSimpleName(), msg.getLoadBalancerUuid());
            if (vrUuids == null || vrUuids.isEmpty()) {
                reply.setType(APIGetLoadBalancerOwnerReply.LoadBalancerOwnerType.noOwner.toString());
            } else {
                VpcRouterVmVO vpc = dbf.findByUuid(vrUuids.get(0), VpcRouterVmVO.class);
                if (vpc.getHaStatus().equals(ApplianceVmHaStatus.NoHa)) {
                    reply.setType(APIGetLoadBalancerOwnerReply.LoadBalancerOwnerType.VPC.toString());
                    reply.setVpc(VpcRouterVmInventory.valueOf(vpc));
                } else {
                    String groupUuid = vpc.getHaRef().stream().map(VpcHaGroupApplianceVmRefVO::getVpcHaRouterUuid)
                            .collect(Collectors.toList()).get(0);
                    VpcHaGroupVO groupVO = dbf.findByUuid(groupUuid, VpcHaGroupVO.class);
                    reply.setType(APIGetLoadBalancerOwnerReply.LoadBalancerOwnerType.VPC.toString());
                    reply.setVpcHa(VpcHaGroupInventory.valueOf(groupVO));
                }
            }
        }
    }

    private void handleSlbVmInstanceMessage(VmInstanceMessage msg) {
        SlbVmInstanceVO vo = dbf.findByUuid(msg.getVmInstanceUuid(), SlbVmInstanceVO.class);
        if (vo == null) {
            String err = String.format("Cannot find SlbVmInstanceVO[uuid:%s], it may have been deleted", msg.getVmInstanceUuid());
            bus.replyErrorByMessageType((Message) msg, err);
            return;
        }

        SlbVm slbVm = new SlbVm(vo);
        slbVm.handleMessage(msg);
    }

    private void handleSlbGroupMessage(SlbGroupMessage msg) {
        SlbGroupVO vo = dbf.findByUuid(msg.getSlbGroupUuid(), SlbGroupVO.class);

        if (vo == null) {
            String err = String.format("Cannot find SlbGroupVO[uuid:%s], it may have been deleted", msg.getSlbGroupUuid());
            bus.replyErrorByMessageType((Message) msg, err);
            return;
        }

        SlbGroupBase slbGroup = new SlbGroupBase(vo);
        slbGroup.handleMessage((Message) msg);
    }

    @Deferred
    @Override
    public boolean checkGroupInstanceBeforeCreate(String groupUuid) {
        // doDestroySlbVmInstance can not be put into queue: slbGroup-uuid-name, because SlbGroupDeletionMsg will call this function
        GLock lock = new GLock("'check-slb-before-create-instance'", TimeUnit.MINUTES.toSeconds(30));
        lock.lock();
        Defer.defer(lock::unlock);

        SlbGroupVO slbGroup = Q.New(SlbGroupVO.class).eq(SlbGroupVO_.uuid, groupUuid).find();

        if (slbGroup.getDeployType().equals(SlbDeployType.NoHA.toString())) {
            if (!slbGroup.getSlbVms().isEmpty()) {
                return false;
            }
        } else if (slbGroup.getDeployType().equals(SlbDeployType.HA.toString())) {
            if (slbGroup.getSlbVms().size() > 1) {
                return false;
            }
        }

        return true;
    }

    private void handle(APICreateSlbGroupMsg msg) {
        APICreateSlbGroupEvent event = new APICreateSlbGroupEvent(msg.getId());

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("create-vpcHa-router-%s", msg.getName()));
        chain.then(new Flow() {
            String __name__ = "persist-vpcHa-db";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                SlbGroupVO groupVO = new SlbGroupVO();
                groupVO.setUuid(msg.getResourceUuid() == null ? Platform.getUuid(): msg.getResourceUuid());
                groupVO.setName(msg.getName());
                groupVO.setSlbOfferingUuid(msg.getSlbOfferingUuid());
                groupVO.setBackendType(msg.getBackendType() == null ? SlbBackendType.VYOS.toString() : msg.getBackendType());
                groupVO.setDeployType(msg.getDeployType() == null ? SlbDeployType.NoHA.toString() : msg.getDeployType());
                groupVO.setDescription(msg.getDescription());
                groupVO.setAccountUuid(msg.getSession().getAccountUuid());
                groupVO = dbf.persist(groupVO);

                List<SlbGroupL3NetworkRefVO> l3s = new ArrayList<>();
                SlbGroupL3NetworkRefVO ref = new SlbGroupL3NetworkRefVO();
                ref.setSlbGroupUuid(groupVO.getUuid());
                ref.setL3NetworkUuid(msg.getFrontEndL3NetworkUuid());
                ref.setType(SlbL3NetworkType.FrontEnd.toString());
                l3s.add(ref);
                for (String uuid : msg.getBackendL3NetworkUuids()) {
                    ref = new SlbGroupL3NetworkRefVO();
                    ref.setSlbGroupUuid(groupVO.getUuid());
                    ref.setL3NetworkUuid(uuid);
                    ref.setType(SlbL3NetworkType.Backend.toString());
                    l3s.add(ref);
                }
                dbf.persistCollection(l3s);

                List<SlbGroupMonitorIpVO> monitorIpVOS = new ArrayList<>();
                for (String ip : msg.getMonitorIps()) {
                    SlbGroupMonitorIpVO vo = new SlbGroupMonitorIpVO();
                    vo.setSlbGroupUuid(ip);
                    vo.setSlbGroupUuid(groupVO.getUuid());
                    monitorIpVOS.add(vo);
                }
                if (!monitorIpVOS.isEmpty()) {
                    dbf.persistCollection(monitorIpVOS);
                }

                groupVO = dbf.reload(groupVO);

                data.put("group", groupVO);

                trigger.next();
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                SlbGroupVO group = (SlbGroupVO) data.get("group");
                dbf.removeByPrimaryKey(group.getUuid(), SlbGroupVO.class);
                trigger.rollback();
            }
        }).then(new Flow() {
            String __name__ = "create-affinityGroup-for-slb-group";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                SlbGroupVO group = (SlbGroupVO) data.get("group");
                if (group.getDeployType().equals(SlbDeployType.NoHA.toString())) {
                    trigger.next();
                    return;
                }

                CreateAffinityGroupMsg cmsg = new CreateAffinityGroupMsg();
                cmsg.setName("affinityGroup-for-slb-" + group.getName());
                cmsg.setAccountUuid(msg.getSession().getAccountUuid());
                cmsg.setPolicy(AffinityGroupPolicy.ANTISOFT.toString());
                cmsg.setType(AffinityGroupType.HOST.toString());
                cmsg.setApplianceType(AffinityGroupAppliance.VROUTER_HA.toString());
                bus.makeLocalServiceId(cmsg, AffinityGroupConstants.SERVICE_ID);
                MessageReply reply = bus.call(cmsg);
                if (!reply.isSuccess()) {
                    trigger.fail(operr("create affinityGroup for slb group [uuid:%s] failed", group.getName()));
                    return;
                }

                CreateAffinityGroupReply areply = (CreateAffinityGroupReply) reply;
                data.put("affinityGroup", areply.getAffinityGroup());

                SystemTagCreator creator = SlbSystemTags.SLB_AFFINITYGROUP.newSystemTagCreator(group.getUuid());
                creator.setTagByTokens(CollectionDSL.map(
                        e(SlbSystemTags.SLB_AFFINITYGROUP_TOKEN, areply.getAffinityGroup().getUuid())
                ));
                creator.create();

                trigger.next();
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                AffinityGroupInventory agInv = (AffinityGroupInventory)data.get("affinityGroup");
                if (agInv == null) {
                    trigger.rollback();
                    return;
                }

                AffinityGroupDeletionMsg dmsg = new AffinityGroupDeletionMsg();
                dmsg.setUuid(agInv.getUuid());
                dmsg.setForceDelete(true);
                bus.makeTargetServiceIdByResourceUuid(dmsg, AffinityGroupConstants.SERVICE_ID, agInv.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format("delete AffinityGroup[uuid:%s] failed", agInv.getUuid()));
                        }
                        trigger.rollback();
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                SlbGroupVO group = (SlbGroupVO) data.get("group");
                event.setInventory(SlbGroupInventory.valueOf(group));
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg){
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();


        bus.publish(event);
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(SlbConstants.SERVICE_ID);
    }

    @Override
    public void upgradeSlbInstance() {
        if (!LoadBalancerGlobalProperty.UPGRADE_LB_SERVER_GROUP) {
            return;
        }

        List<L3NetworkVO> l3NetworkVOS = Q.New(L3NetworkVO.class)
                .eq(L3NetworkVO_.type, L3NetworkConstant.L3_BASIC_NETWORK_TYPE)
                .eq(L3NetworkVO_.category, L3NetworkCategory.Private).list();
        for (L3NetworkVO l3Vo : l3NetworkVOS) {
            boolean hasSnat = false;

            /* slb instance in flat network which doesn't not has snat service */
            for (NetworkServiceL3NetworkRefVO ref : l3Vo.getNetworkServices()) {
                if (ref.getNetworkServiceType().equals(NetworkServiceType.SNAT.toString())) {
                    hasSnat = true;
                    break;
                }
            }

            if (hasSnat) {
                continue;
            }

            List<IpRangeVO> iprs = l3Vo.getIpRanges().stream().filter(r -> r.getIpVersion() == IPv6Constants.IPv4)
                    .collect(Collectors.toList());
            if (iprs.isEmpty()) {
                continue;
            }

            /* slb instance nic ip is not the gateway ip */
            String vrUuid = Q.New(VmNicVO.class).select(VmNicVO_.vmInstanceUuid)
                    .in(VmNicVO_.metaData, VirtualRouterNicMetaData.GUEST_NIC_MASK_STRING_LIST)
                    .eq(VmNicVO_.l3NetworkUuid, l3Vo.getUuid())
                    .notEq(VmNicVO_.ip, iprs.get(0).getGateway()).findValue();
            if (vrUuid == null) {
                continue;
            }

            /* upgrade virtual router slb instance */
            VirtualRouterVmVO vrVO = dbf.findByUuid(vrUuid, VirtualRouterVmVO.class);
            vrVO.setApplianceVmType(SlbConstants.SLB_VM_TYPE); /* change appliance type */
            dbf.update(vrVO);
            /* add an ip address for private nic */
            for (VmNicVO nicVO : vrVO.getVmNics()) {
                nicVO.setMetaData(null);
                dbf.update(nicVO);
            }
            //delete vr loadBalancer ref
            SQL.New("delete from VirtualRouterLoadBalancerRefVO where virtualRouterVmUuid = :vrUuid")
                    .param("vrUuid", vrVO.getUuid()).execute();

            //set vip used for snat to non-system
            VirtualRouterOfferingVO vrOffering = dbf.findByUuid(vrVO.getInstanceOfferingUuid(), VirtualRouterOfferingVO.class);
            if (vrOffering.getPublicNetworkUuid() != null) {
                List<VipVO> vipVOs = SQL.New("select vip from VipVO vip, VmNicVO nic, UsedIpVO usedIp where vip.l3NetworkUuid = :l3Uuid "+
                        "and vip.usedIpUuid = usedIp.uuid and usedIp.vmNicUuid = nic.uuid and vip.useFor = :snat " +
                        "and nic.vmInstanceUuid = :vmUuid")
                        .param("l3Uuid", vrOffering.getPublicNetworkUuid())
                        .param("snat", NetworkServiceType.SNAT.toString())
                        .param("vmUuid", vrVO.getUuid())
                        .list();

                for (VipVO vip : vipVOs) {
                    vip.setSystem(false);
                    dbf.update(vip);
                }
            }

            /* create slb offering */
            APICreateSlbOfferingMsg msg = new APICreateSlbOfferingMsg();
            SessionInventory session = new SessionInventory();
            session.setAccountUuid(Account.getAccountUuidOfResource(vrVO.getUuid()));
            msg.setSession(session);
            msg.setType(SlbConstants.SLB_VM_TYPE);
            msg.setImageUuid(vrVO.getImageUuid());
            msg.setManagementNetworkUuid(vrVO.getManagementNetworkUuid());
            msg.setZoneUuid(vrVO.getZoneUuid());

            InstanceOfferingVO instanceOfferingVO = new InstanceOfferingVO();
            instanceOfferingVO.setUuid(Platform.getUuid());
            instanceOfferingVO.setAllocatorStrategy(HostAllocatorConstant.LEAST_VM_PREFERRED_HOST_ALLOCATOR_STRATEGY_TYPE);
            instanceOfferingVO.setName(String.format("slb-offering-for-%s", vrVO.getName()));
            instanceOfferingVO.setCpuNum(vrVO.getCpuNum());
            instanceOfferingVO.setCpuSpeed((int)vrVO.getCpuSpeed());
            instanceOfferingVO.setDescription(String.format("slb-offering-for-%s", vrVO.getName()));
            instanceOfferingVO.setState(InstanceOfferingState.Enabled);
            instanceOfferingVO.setMemorySize(vrVO.getMemorySize());
            instanceOfferingVO.setDuration(InstanceOfferingDuration.Permanent);
            instanceOfferingVO.setType(SlbConstants.SLB_VM_TYPE);
            instanceOfferingVO.setAccountUuid(msg.getSession().getAccountUuid());
            slbOfferingFactory.createInstanceOffering(instanceOfferingVO, msg);

            /* create slb group */
            SlbGroupVO groupVO = new SlbGroupVO();
            groupVO.setUuid(Platform.getUuid());
            groupVO.setName(String.format("slb-group-%s", vrVO.getName()));
            groupVO.setSlbOfferingUuid(instanceOfferingVO.getUuid());
            groupVO.setBackendType(SlbBackendType.VYOS.toString());
            groupVO.setDeployType(SlbDeployType.NoHA.toString());
            groupVO.setDescription(String.format("slb-group-%s", vrVO.getName()));
            groupVO.setAccountUuid(instanceOfferingVO.getAccountUuid());
            dbf.persist(groupVO);

            /*create SlbGroupL3NetworkRefVO*/
            SlbGroupL3NetworkRefVO slbGroupFrontL3RefVO = new SlbGroupL3NetworkRefVO();
            slbGroupFrontL3RefVO.setL3NetworkUuid(vrOffering.getPublicNetworkUuid());
            slbGroupFrontL3RefVO.setSlbGroupUuid(groupVO.getUuid());
            slbGroupFrontL3RefVO.setType(SlbL3NetworkType.FrontEnd.toString());

            SlbGroupL3NetworkRefVO slbGroupBkL3RefVO = new SlbGroupL3NetworkRefVO();
            slbGroupBkL3RefVO.setL3NetworkUuid(l3Vo.getUuid());
            slbGroupBkL3RefVO.setSlbGroupUuid(groupVO.getUuid());
            slbGroupBkL3RefVO.setType(SlbL3NetworkType.Backend.toString());
            dbf.persist(slbGroupFrontL3RefVO);
            dbf.persist(slbGroupBkL3RefVO);

            new SQLBatch() {
                @Override
                protected void scripts() {
                    /* insert SlbVmInstanceVO */
                    dbf.getEntityManager().createNativeQuery((String.format("insert into SlbVmInstanceVO (uuid, slbGroupUuid) values ('%s', '%s')",
                            vrUuid, groupVO.getUuid()))).executeUpdate();

                    /* insert SlbLoadBalancerVO */
                    List<String> nicUuids = q(VmNicVO.class).eq(VmNicVO_.l3NetworkUuid, l3Vo.getUuid()).select(VmNicVO_.uuid).listValues();
                    if (nicUuids.isEmpty()) {
                        return;
                    }

                    List<String> listenerUuids = q(LoadBalancerListenerVmNicRefVO.class).in(LoadBalancerListenerVmNicRefVO_.vmNicUuid, nicUuids)
                            .select(LoadBalancerListenerVmNicRefVO_.listenerUuid).listValues();
                    if (listenerUuids.isEmpty()) {
                        return;
                    }

                    List<String> lbUuids = Q.New(LoadBalancerListenerVO.class).in(LoadBalancerListenerVO_.uuid, listenerUuids)
                            .select(LoadBalancerListenerVO_.loadBalancerUuid).listValues();
                    Set<String> lbSets =  new HashSet<>(lbUuids);
                    for (String uuid : lbSets) {
                        SQL.New(LoadBalancerVO.class).eq(LoadBalancerVO_.uuid, uuid).set(LoadBalancerVO_.type, LoadBalancerType.SLB).update();
                        dbf.getEntityManager().createNativeQuery(String.format("insert into SlbLoadBalancerVO (uuid, slbGroupUuid) values ('%s', '%s')",
                                uuid, groupVO.getUuid())).executeUpdate();
                    }
                }
            }.execute();
        }

    }

    @Override
    public boolean start() {
        upgradeSlbInstance();

        evtf.onLocal(ApplianceVmCanonicalEvents.APPLIANCEVM_HASTATUS_CHANGED_PATH, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                ApplianceVmCanonicalEvents.ApplianceVmHaStatusChangedData d =
                        (ApplianceVmCanonicalEvents.ApplianceVmHaStatusChangedData) data;
                SlbVmInstanceVO slbVo = dbf.findByUuid(d.getApplianceVmUuid(), SlbVmInstanceVO.class);
                if (slbVo == null) {
                    return;
                }

                evtf.fire(SlbVmCanonicalEvents.SLB_VM_HASTATUS_CHANGED_PATH, data);
            }
        });

        evtf.onLocal(ApplianceVmCanonicalEvents.APPLIANCEVM_ABNORMAL_FILE_REPORT_PATH, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                ApplianceVmCanonicalEvents.ApplianceVmAbnormalFilesDate d =
                        (ApplianceVmCanonicalEvents.ApplianceVmAbnormalFilesDate) data;
                SlbVmInstanceVO slbVo = dbf.findByUuid(d.getApplianceVmUuid(), SlbVmInstanceVO.class);
                if (slbVo == null) {
                    return;
                }

                evtf.fire(SlbVmCanonicalEvents.SLB_VM_ABNORMAL_FILE_REPORT_PATH, data);
            }
        });

        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public String getVipUse() {
        return SlbConstants.SLB_NETWORK_SERVICE_TYPE;
    }

    @Override
    public void releaseServicesOnVip(VipInventory vip, Completion completion) {
        loadBalancerExtension.releaseServicesOnVip(vip, completion);
    }

    @Override
    public List<VmNicInventory> getCandidateVmNicsForLoadBalancerInVirtualRouter(APIGetCandidateVmNicsForLoadBalancerMsg msg, List<VmNicInventory> candidates) {
        LoadBalancerVO loadBalancerVO = dbf.findByUuid(msg.getLoadBalancerUuid(), LoadBalancerVO.class);

        if (loadBalancerVO.getType() != LoadBalancerType.SLB) {
            return candidates;
        }

        SlbLoadBalancerVO slbLoadBalancerVO = dbf.findByUuid(msg.getLoadBalancerUuid(), SlbLoadBalancerVO.class);
        SlbGroupVO slbGroupVO = dbf.findByUuid(slbLoadBalancerVO.getSlbGroupUuid(), SlbGroupVO.class);
        List<String> backendL3Uuids = SlbGroupOperator.getBackendL3Uuids(slbGroupVO);
        backendL3Uuids.add(SlbGroupOperator.getFrontEndL3Uuid(slbGroupVO));

        candidates = candidates.stream().filter(nic -> backendL3Uuids.contains(nic.getL3NetworkUuid())).collect(Collectors.toList());

        return candidates;
    }

    @Override
    public List<L3NetworkInventory> getPeerL3NetworksForLoadBalancer(String lbUuid, List<L3NetworkInventory> candidates) {
        SlbLoadBalancerVO slb = dbf.findByUuid(lbUuid, SlbLoadBalancerVO.class);
        if (slb == null) {
            /* not slb will not filter out */
            return candidates;
        }

        /* only backend l3 are permitted */
        SlbGroupVO slbGroupVO = dbf.findByUuid(slb.getSlbGroupUuid(), SlbGroupVO.class);
        return candidates.stream().filter(l3 -> SlbGroupOperator.getBackendL3Uuids(slbGroupVO).contains(l3.getUuid())).collect(Collectors.toList());
    }

    @Override
    public String getApplianceVmInstanceType() {
        return SlbConstants.SLB_VM_TYPE;
    }

    @Override
    public List<String> getParentUuid(String uuid, String vipUuid) {
        Set<String> parents = new HashSet<>();
        if (Q.New(SlbVmInstanceVO.class).eq(SlbVmInstanceVO_.uuid, uuid).isExists()) {
            Set<String> slbGroupUuids = new HashSet<>(Q.New(SlbVmInstanceVO.class)
                    .select(SlbVmInstanceVO_.slbGroupUuid)
                    .eq(SlbVmInstanceVO_.uuid, uuid)
                    .listValues());
            for (String slbGroupUuid : slbGroupUuids) {
                List<Tuple> slbAndVips = Q.New(SlbLoadBalancerVO.class)
                        .select(SlbLoadBalancerVO_.uuid, SlbLoadBalancerVO_.vipUuid)
                        .eq(SlbLoadBalancerVO_.slbGroupUuid, slbGroupUuid)
                        .listTuple();
                for (Tuple t : slbAndVips) {
                    if (vipUuid == null || vipUuid.equals(t.get(1, String.class))) {
                        parents.add(t.get(0, String.class));
                    }
                }
            }
        }
        return new ArrayList<>(parents);
    }

    private AddVmFencerRuleToHostMsg generateAddVmFencerRuleToHost(List<VmRuleAttachFencer> blockRules, List<VmRuleAttachFencer> allowRules, String hostUuid) {
        AddVmFencerRuleToHostMsg msg = new AddVmFencerRuleToHostMsg();
        msg.setBlockRules(blockRules);
        msg.setAllowRules(allowRules);
        msg.setHostUuid(hostUuid);
        bus.makeTargetServiceIdByResourceUuid(msg, HaConstants.SERVICE_ID, msg.getHostUuid());
        return msg;
    }

    private RemoveVmFencerRuleFromHostMsg generateRemoveVmFencerRuleFromHost(List<VmRuleAttachFencer> blockRules, List<VmRuleAttachFencer> allowRules, String hostUuid) {
        RemoveVmFencerRuleFromHostMsg msg = new RemoveVmFencerRuleFromHostMsg();
        msg.setHostUuid(hostUuid);
        msg.setBlockRules(blockRules);
        msg.setAllowRules(allowRules);
        bus.makeTargetServiceIdByResourceUuid(msg, HaConstants.SERVICE_ID, msg.getHostUuid());
        return msg;
    }


    @Override
    public AddVmFencerRuleToHostMsg generateAddVmFencerRuleToHostMsg(List<String> vmUuids, String hostUuid) {
        List<String> vmUuidsOnHost = Q.New(SlbVmInstanceVO.class)
                .in(SlbVmInstanceVO_.uuid, vmUuids)
                .eq(SlbVmInstanceVO_.hostUuid, hostUuid)
                .select(SlbVmInstanceVO_.uuid)
                .listValues();
        if (CollectionUtils.isEmpty(vmUuidsOnHost)) {
            return null;
        }

        List<VmRuleAttachFencer> blockRules = new ArrayList<>();
        List<VmRuleAttachFencer> allowRules = new ArrayList<>();

        VmRuleAttachFencer blockFencer = new VmRuleAttachFencer();
        blockFencer.setFencerName(HaConstants.HOST_BUSINESS_NIC);
        blockFencer.setVmUuids(vmUuidsOnHost);
        blockRules.add(blockFencer);

        VmRuleAttachFencer allowFencer = new VmRuleAttachFencer();
        allowFencer.setFencerName(HaConstants.HOST_STORAGE_STATE);
        allowFencer.setVmUuids(vmUuidsOnHost);
        allowRules.add(allowFencer);

        return generateAddVmFencerRuleToHost(blockRules, allowRules, hostUuid);
    }

    @Override
    public RemoveVmFencerRuleFromHostMsg generateVmFencerRuleFromHostMsg(VmInstanceInventory inv, String hostUuid) {
        if (!Q.New(SlbVmInstanceVO.class)
                .eq(SlbVmInstanceVO_.uuid, inv.getUuid())
                .isExists()) {
            return null;
        }

        List<VmRuleAttachFencer> blockRules = new ArrayList<>();
        List<VmRuleAttachFencer> allowRules = new ArrayList<>();

        VmRuleAttachFencer blockFencer = new VmRuleAttachFencer();
        blockFencer.setFencerName(HaConstants.HOST_BUSINESS_NIC);
        List<String> vmUuids = new ArrayList<>();
        vmUuids.add(inv.getUuid());
        blockFencer.setVmUuids(vmUuids);
        blockRules.add(blockFencer);

        VmRuleAttachFencer allowFencer = new VmRuleAttachFencer();
        allowFencer.setFencerName(HaConstants.HOST_STORAGE_STATE);
        allowFencer.setVmUuids(vmUuids);
        allowRules.add(allowFencer);

        return generateRemoveVmFencerRuleFromHost(blockRules, allowRules, hostUuid);
    }

    @Override
    public void sendVmFencerRuleToHostMsg(NeedReplyMessage msg) {
        if (msg == null) {
            return;
        }
        bus.send(msg);
    }

    @Override
    public String getProviderUuidOfNetworkServiceUseTheVip(String serviceUuid) {
        String slbGroupUuid = Q.New(SlbLoadBalancerVO.class).eq(SlbLoadBalancerVO_.uuid, serviceUuid).select(SlbLoadBalancerVO_.slbGroupUuid).findValue();
        if (slbGroupUuid == null) {
            return null;
        }

        String slbVmUuid = Q.New(SlbVmInstanceVO.class).eq(SlbVmInstanceVO_.slbGroupUuid, slbGroupUuid).select(SlbVmInstanceVO_.uuid).findValue();
        return slbVmUuid;
    }

    @Override
    public void cleanUpAfterVmChangeImage(VmInstanceInventory inv) {
        SlbVmInstanceVO slbVmVO = dbf.findByUuid(inv.getUuid(), SlbVmInstanceVO.class);
        if (slbVmVO == null) {
            return;
        }

        ImageVO imageVO = dbf.findByUuid(slbVmVO.getImageUuid(), ImageVO.class);
        String userName;
        if (imageVO.getGuestOsType().equalsIgnoreCase(VirtualRouterConstant.X86_VPC_EULER_GUEST_OS_TYPE)) {
            userName = VirtualRouterConstant.X86_VPC_EULER_GUEST_OS_USER;
        } else if (imageVO.getGuestOsType().equalsIgnoreCase(VirtualRouterConstant.X86_VPC_VYOS_GUEST_OS_TYPE)) {
            userName = VirtualRouterConstant.X86_VPC_VYOS_GUEST_OS_USER;
        } else if (imageVO.getGuestOsType().equalsIgnoreCase(VirtualRouterConstant.ARM_VPC_VYOS_GUEST_OS_TYPE)) {
            userName = VirtualRouterConstant.ARM_VPC_VYOS_GUEST_OS_USER;
        } else {
            return;
        }

        SystemTagCreator creator = VirtualRouterSystemTags.VIRTUAL_ROUTER_LOGIN_USER.newSystemTagCreator(inv.getUuid());
        creator.setTagByTokens(map(
                e(VirtualRouterSystemTags.VIRTUAL_ROUTER_LOGIN_USER_TOKEN, userName)
        ));
        creator.inherent = true;
        creator.recreate = true;
        creator.create();
    }
}
