package org.zstack.network.service.slb;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.appliancevm.*;
import org.zstack.appliancevm.ApplianceVmConstant.BootstrapParams;
import org.zstack.core.cloudbus.EventCallback;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SimpleQuery;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.ha.HaSystemTags;
import org.zstack.ha.VmHaLevel;
import org.zstack.header.Component;
import org.zstack.header.core.workflow.Flow;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.network.l2.APICreateL2NetworkMsg;
import org.zstack.header.network.l2.L2NetworkInventory;
import org.zstack.header.network.l3.L3NetworkConstant;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.header.vm.VmInstanceSpec;
import org.zstack.header.vpc.VpcConstants;
import org.zstack.network.service.lb.LoadBalancerConstants;
import org.zstack.network.service.lb.LoadBalancerVO;
import org.zstack.network.service.vip.VipGetUsedPortRangeExtensionPoint;
import org.zstack.network.service.vip.VipVO;
import org.zstack.network.service.virtualrouter.VirtualRouterGlobalConfig;
import org.zstack.network.service.virtualrouter.VirtualRouterGlobalProperty;
import org.zstack.network.service.virtualrouter.vyos.VyosConstants;
import org.zstack.network.service.virtualrouter.vyos.VyosGlobalConfig;
import org.zstack.network.service.virtualrouter.vyos.VyosVmBaseFactory;
import org.zstack.resourceconfig.ResourceConfigFacade;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.RangeSet;
import org.zstack.utils.Utils;
import org.zstack.utils.VipUseForList;
import org.zstack.utils.logging.CLogger;
import org.zstack.vpc.VpcGlobalConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

public class SlbVmFactory extends VyosVmBaseFactory implements Component, VipGetUsedPortRangeExtensionPoint {
    private static final CLogger logger = Utils.getLogger(SlbVmFactory.class);
    public static ApplianceVmType applianceVmType = new ApplianceVmType(SlbConstants.SLB_VM_TYPE);

    @Autowired
    DatabaseFacade dbf;
    @Autowired
    ResourceConfigFacade rcf;
    @Autowired
    EventFacade evtf;

    private List<String> slbPostCreateFlows;
    private List<String> slbPostStartFlows;
    private List<String> slbPostRebootFlows;
    private List<String> slbPostDestroyFlows;
    private List<String> slbReconnectFlows;
    private FlowChainBuilder postCreateFlowsBuilder;
    private FlowChainBuilder postStartFlowsBuilder;
    private FlowChainBuilder postRebootFlowsBuilder;
    private FlowChainBuilder postDestroyFlowsBuilder;
    private FlowChainBuilder reconnectFlowsBuilder;

    protected List<SlbVmInstanceLifeCycleExtensionPoint> slbVmInstanceLifeCycleExtensionPoints;

    @Override
    public ApplianceVmType getApplianceVmType() {
        return applianceVmType;
    }

    @Override
    public ApplianceVm getSubApplianceVm(ApplianceVmVO apvm) {
        SlbVmInstanceVO slb = dbf.findByUuid(apvm.getUuid(), SlbVmInstanceVO.class);
        return new SlbVm(slb);
    }

    @Override
    public ApplianceVmVO persistApplianceVm(ApplianceVmSpec spec, ApplianceVmVO apvm) {
        SlbVmInstanceVO slbVmInstanceVO = new SlbVmInstanceVO(apvm);
        SlbGroupVO slbGroup = spec.getExtensionData(SlbConstants.Param.SLB_GROUP.toString(), SlbGroupVO.class);
        String frontL3Uuid = SlbGroupOperator.getFrontEndL3Uuid(slbGroup);
        slbVmInstanceVO.setSlbGroupUuid(slbGroup.getUuid());
        slbVmInstanceVO.setPublicNetworkUuid(frontL3Uuid);
        dbf.getEntityManager().persist(slbVmInstanceVO);
        return slbVmInstanceVO;
    }

    @Override
    public void removeApplianceVm(ApplianceVmSpec spec, ApplianceVmVO apvm) {
        dbf.removeByPrimaryKey(apvm.getUuid(), SlbVmInstanceVO.class);
    }

    @Override
    public boolean start() {
        buildWorkFlowBuilder();
        slbVmInstanceLifeCycleExtensionPoints = pluginRgty.getExtensionList(SlbVmInstanceLifeCycleExtensionPoint.class);
        super.populateExtensions();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    public List<String> getSlbPostCreateFlows() {
        return slbPostCreateFlows;
    }

    public void setSlbPostCreateFlows(List<String> slbPostCreateFlows) {
        this.slbPostCreateFlows = slbPostCreateFlows;
    }

    public List<String> getSlbPostStartFlows() {
        return slbPostStartFlows;
    }

    public void setSlbPostStartFlows(List<String> slbPostStartFlows) {
        this.slbPostStartFlows = slbPostStartFlows;
    }

    public List<String> getSlbPostRebootFlows() {
        return slbPostRebootFlows;
    }

    public void setSlbPostRebootFlows(List<String> slbPostRebootFlows) {
        this.slbPostRebootFlows = slbPostRebootFlows;
    }

    public List<String> getSlbPostDestroyFlows() {
        return slbPostDestroyFlows;
    }

    public void setSlbPostDestroyFlows(List<String> slbPostDestroyFlows) {
        this.slbPostDestroyFlows = slbPostDestroyFlows;
    }

    public List<String> getSlbReconnectFlows() {
        return slbReconnectFlows;
    }

    public void setSlbReconnectFlows(List<String> slbReconnectFlows) {
        this.slbReconnectFlows = slbReconnectFlows;
    }

    public List<Flow> getPostCreateFlows() {
        List<Flow> flows = new ArrayList<>();
        flows.addAll(postCreateFlowsBuilder.getFlows());
        flows.addAll(slbVmInstanceLifeCycleExtensionPoints.stream().map(SlbVmInstanceLifeCycleExtensionPoint::slbVmPostCreateFlow).collect(Collectors.toList()));
        flows.add(slbVmInstancePostCreateFlow());
        return flows;
    }

    public List<Flow> getPostStartFlows() {
        List<Flow> flows = new ArrayList<>();
        flows.addAll(postStartFlowsBuilder.getFlows());
        flows.addAll(slbVmInstanceLifeCycleExtensionPoints.stream().map(SlbVmInstanceLifeCycleExtensionPoint::slbVmPostStartFlow).collect(Collectors.toList()));
        return flows;
    }

    public List<Flow> getPostRebootFlows() {
        List<Flow> flows = new ArrayList<>();
        flows.addAll(postRebootFlowsBuilder.getFlows());
        flows.addAll(slbVmInstanceLifeCycleExtensionPoints.stream().map(SlbVmInstanceLifeCycleExtensionPoint::slbVmPostRebootFlow).collect(Collectors.toList()));
        return flows;
    }

    public List<Flow> getPostStopFlows() {
        return null;
    }

    public List<Flow> getPostMigrateFlows() {
        return null;
    }

    public List<Flow> getPostDestroyFlows() {
        List<Flow> flows = new ArrayList<>();
        flows.addAll(postDestroyFlowsBuilder.getFlows());
        flows.addAll(slbVmInstanceLifeCycleExtensionPoints.stream().map(SlbVmInstanceLifeCycleExtensionPoint::slbVmPostDestroyFlow).collect(Collectors.toList()));
        return flows;
    }

    public FlowChain getReconnectFlowChain() {
        FlowChain c = reconnectFlowsBuilder.build();
        for (SlbVmInstanceLifeCycleExtensionPoint ext : slbVmInstanceLifeCycleExtensionPoints) {
            c.then(ext.slbVmPostReconnectFlow());
        }
        return c;
    }

    @Override
    protected void buildWorkFlowBuilder() {
        postCreateFlowsBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(slbPostCreateFlows).construct();
        postStartFlowsBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(slbPostStartFlows).construct();
        postRebootFlowsBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(slbPostRebootFlows).construct();
        postDestroyFlowsBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(slbPostDestroyFlows).construct();
        reconnectFlowsBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(slbReconnectFlows).construct();
    }

    @Override
    public void afterCreateL2Network(L2NetworkInventory l2Network) {
    }

    @Override
    public void prepareDbInitialValue() {
    }

    @Override
    public void beforeCreateL2Network(APICreateL2NetworkMsg msg) {
    }

    @Override
    public String getNetworkServiceProviderUuid() {
        return super.getNetworkServiceProviderUuid();
    }

    @Override
    public void applianceVmPrepareBootstrapInfo(VmInstanceSpec spec, Map<String, Object> info) {
        SimpleQuery<ApplianceVmVO> q = dbf.createQuery(ApplianceVmVO.class);
        q.add(ApplianceVmVO_.applianceVmType, SimpleQuery.Op.EQ, SlbConstants.SLB_VM_TYPE);
        q.add(ApplianceVmVO_.uuid, SimpleQuery.Op.EQ, spec.getVmInventory().getUuid());
        ApplianceVmVO applianceVmVO = q.find();
        if (applianceVmVO == null) {
            return;
        }

        info.put(VyosConstants.HA_STATUS, applianceVmVO.getHaStatus().toString());

        logger.debug("add vpc password to vpc vrouter");
        info.put(VyosConstants.BootstrapInfoKey.vyosPassword.toString(), VirtualRouterGlobalConfig.VYOS_PASSWORD.value());
        info.put(BootstrapParams.sshPort.toString(), VirtualRouterGlobalConfig.SSH_PORT.value(Integer.class));

        if (rcf.getResourceConfigValue(SlbGlobalConfig.CONFIG_FIREWALL_WITH_IPTABLES, spec.getVmInventory().getUuid(), Boolean.class)) {
            info.put(VyosConstants.REPLACE_FIREWALL_WITH_IPTBALES, true);
        }

        if (rcf.getResourceConfigValue(VyosGlobalConfig.ENABLE_VYOS_CMD, spec.getVmInventory().getUuid(), Boolean.class)) {
            info.put(VyosConstants.CONFIG_ENABLE_VYOS, true);
        } else {
            info.put(VyosConstants.CONFIG_ENABLE_VYOS, false);
        }

        info.put(VpcConstants.TC_FOR_VIPQOS, rcf.getResourceConfigValue(SlbGlobalConfig.TC_FOR_VIPQOS, spec.getVmInventory().getUuid(), Boolean.class));
        info.put(ApplianceVmConstant.ABNORMAL_FILE_MAX_SIZE, ApplianceVmGlobalConfig.ABNORMAL_FILE_MAX_SIZE.value(Integer.class));
        Boolean  passwordAuth = VirtualRouterGlobalConfig.SSH_LOGIN_PASSWORD.value(Boolean.class);
        info.put(VyosConstants.ALLOW_PASSWORD_AUTH, passwordAuth ? "yes" : "no");
    }

    @Override
    public RangeSet getVipUsePortRange(String vipUuid, String protocol, VipUseForList useForList) {
        RangeSet portRangeList = new RangeSet();
        List<RangeSet.Range> portRanges = new ArrayList<RangeSet.Range>();
        portRangeList.setRanges(portRanges);

        /* vip is the vip for slb */
        if (Q.New(SlbLoadBalancerVO.class).eq(SlbLoadBalancerVO_.vipUuid, vipUuid).isExists()) {
            if (protocol.equalsIgnoreCase(LoadBalancerConstants.LB_PROTOCOL_TCP)){
                int sshPort = VirtualRouterGlobalConfig.SSH_PORT.value(Integer.class);
                portRanges.add(new RangeSet.Range(sshPort, sshPort, true));

                int agentPort = VirtualRouterGlobalProperty.AGENT_PORT;
                portRanges.add(new RangeSet.Range(agentPort, agentPort, true));
            }
            return portRangeList;
        }

        if (Q.New(SlbLoadBalancerVO.class).eq(SlbLoadBalancerVO_.ipv6VipUuid, vipUuid).isExists()) {
            if (protocol.equalsIgnoreCase(LoadBalancerConstants.LB_PROTOCOL_TCP)){
                int sshPort = VirtualRouterGlobalConfig.SSH_PORT.value(Integer.class);
                portRanges.add(new RangeSet.Range(sshPort, sshPort, true));

                int agentPort = VirtualRouterGlobalProperty.AGENT_PORT;
                portRanges.add(new RangeSet.Range(agentPort, agentPort, true));
            }
        }

        return portRangeList;
    }

    private Flow getSetHaLevelFlow() {
        return new NoRollbackFlow() {
            String __name__ = "set-ha-level";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                final VmInstanceSpec spec = (VmInstanceSpec) data.get(VmInstanceConstant.Params.VmInstanceSpec.toString());
                SlbVmInstanceInventory slb = SlbVmInstanceInventory.valueOf(dbf.findByUuid(spec.getVmInventory().getUuid(), SlbVmInstanceVO.class));

                SystemTagCreator creator = HaSystemTags.HA.newSystemTagCreator(slb.getUuid());
                creator.inherent = true;
                creator.setTagByTokens(map(e(HaSystemTags.HA_TOKEN, VmHaLevel.NeverStop.toString())));
                creator.create();

                trigger.next();
            }
        };
    }

    public Flow slbVmInstancePostCreateFlow() {
        return getSetHaLevelFlow();
    }
}
