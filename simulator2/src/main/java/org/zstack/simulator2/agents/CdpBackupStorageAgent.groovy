package org.zstack.simulator2.agents

import org.springframework.http.HttpEntity
import org.zstack.core.db.Q
import org.zstack.header.agent.AgentResponse
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.kvm.KVMAgentCommands
import org.zstack.simulator2.Simulator
import org.zstack.simulator2.SimulatorGlobalProperty
import org.zstack.storage.cdp.CdpBackupKvmCommands
import org.zstack.storage.cdp.CdpBackupServerCommands
import org.zstack.storage.cdp.CdpBackupStorageGlobalProperty
import org.zstack.utils.gson.JSONObjectUtil

class CdpBackupStorageAgent extends Agent {
    CdpBackupStorageAgent(Simulator simulator) {
        super(simulator)

        CdpBackupStorageGlobalProperty.AGENT_PORT = SimulatorGlobalProperty.SIMULATOR_AGENT_PORT
    }

    @Override
    void setupAgentHandler() {
        handle(CdpBackupKvmCommands.REST_API_TAKE_VOLUME_MIRROR_PATH) {
            def rsp = new AgentResponse()
            rsp.success = true
            return rsp
        }

        handle(CdpBackupKvmCommands.REST_API_STOP_VOLUME_MIRROR_PATH) {
            def rsp = new AgentResponse()
            rsp.success = true
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_PREPARE_CDP_PATH) {
            AgentResponse rsp = new AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_STOP_CDP_PATH) {
            def rsp = new AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_DESTROY_CDP_PATH) {
            def rsp = new AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_DEL_CDP_DATA_PATH) {
            def rsp = new AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_RUN_CDP_TASK_WITH_POLICY_PATH) {
            def rsp = new AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_GET_CDP_STORAGE_USAGE_PATH) {
            def rsp = new CdpBackupServerCommands.GetCdpStorageResponse()
            rsp.cdpStatus = "running"
            rsp.storageUsage = 171727972
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_EXPORT_RECOVERY_POINTS_PATH) { HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, CdpBackupServerCommands.RecoveryPointCmd.class)
            def volUuids = Q.New(VolumeVO.class)
                    .eq(VolumeVO_.vmInstanceUuid, cmd.vmUuid)
                    .eq(VolumeVO_.isShareable, false)
                    .select(VolumeVO_.uuid)
                    .listValues()
            def rsp = new CdpBackupServerCommands.ExportVolumesRsp()
            rsp.nbdPort = 1122
            rsp.nbdVols = volUuids.collectEntries {[it, it+"expt"]}
            rsp.nbdSizes = volUuids.collectEntries {[it, 1073741824]}
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_UNEXPORT_RECOVERY_POINTS_PATH) {
            def rsp = new KVMAgentCommands.AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_PROTECT_RECOVERY_POINTS_PATH) {
            def rsp = new KVMAgentCommands.AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_UNPROTECT_RECOVERY_POINTS_PATH) {
            def rsp = new KVMAgentCommands.AgentResponse()
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_ADD_VOLUMES_TO_NBD_SERVER_PATH) { HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, CdpBackupServerCommands.AddVolumesToStartNbdServerCmd.class)
            def rsp = new CdpBackupServerCommands.AddVolumesToStartNbdServerRsp()
            def volUuids = Q.New(VolumeVO.class)
                    .eq(VolumeVO_.vmInstanceUuid, cmd.vmUuid)
                    .eq(VolumeVO_.isShareable, false)
                    .select(VolumeVO_.uuid)
                    .listValues()
            rsp.nbdPort = 1122
            rsp.nbdVols = volUuids.collectEntries {[it, it+"expt"]}
            return rsp
        }

        handle(CdpBackupServerCommands.REST_API_DELETE_VOLUMES_FROM_NBD_SERVER_PATH) { HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, CdpBackupServerCommands.DeleteVolumesFromNbdServerCmd.class)
            def rsp = new CdpBackupServerCommands.DeleteVolumesFromNbdServerRsp()
            def volUuids = Q.New(VolumeVO.class)
                    .eq(VolumeVO_.vmInstanceUuid, cmd.vmUuid)
                    .eq(VolumeVO_.isShareable, false)
                    .select(VolumeVO_.uuid)
                    .listValues()
            def toBeDeleted = []
            if (cmd.volumes != null) {
                toBeDeleted = cmd.volumes.collectEntries({ it.uuid })
            }

            volUuids.removeAll(toBeDeleted)
            rsp.nbdPort = 1122
            rsp.nbdVols = volUuids.collectEntries {[it, it+"expt"]}
            return rsp
        }

    }
}
