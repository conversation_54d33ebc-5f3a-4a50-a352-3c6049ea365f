package org.zstack.simulator2.agents

import org.springframework.http.HttpEntity
import org.zstack.baremetal2.chassis.ipmi.BareMetal2IpmiChassisVO
import org.zstack.baremetal2.chassis.ipmi.BareMetal2IpmiChassisVO_
import org.zstack.baremetal2.gateway.BareMetal2GatewayCommands
import org.zstack.baremetal2.gateway.BareMetal2GatewayConstant
import org.zstack.baremetal2.instance.BareMetal2InstanceConstant
import org.zstack.core.db.Q
import org.zstack.header.Constants
import org.zstack.simulator2.Simulator
import org.zstack.simulator2.config.host.KvmHost

class Baremetal2Agent extends Agent{

    Baremetal2Agent(Simulator simulator) {
        super(simulator)
    }

    @Override
    void setupAgentHandler() {
        handle(BareMetal2GatewayConstant.PREPARE_PROVISION_NETWORK_PATH) {
            return new BareMetal2GatewayCommands.PrepareProvisionNetworkInGatewayRsp()
        }

        handle(BareMetal2GatewayConstant.DESTROY_PROVISION_NETWORK_PATH) {
            return new BareMetal2GatewayCommands.DestroyProvisionNetworkInGatewayRsp()
        }

        handle(BareMetal2GatewayConstant.CREATE_CONFIGURATIONS_FOR_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.CreateProvisionConfigurationForInstanceRsp()
        }

        handle(BareMetal2GatewayConstant.DELETE_CONFIGURATIONS_FOR_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.DeleteProvisionConfigurationForInstanceRsp()
        }

        handle(BareMetal2GatewayConstant.PREPARE_VOLUME_FOR_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.PrepareVolumeForInstanceRsp()
        }

        handle(BareMetal2GatewayConstant.DESTROY_VOLUME_FOR_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.DestroyVolumeForInstanceRsp()
        }

        handle(BareMetal2GatewayConstant.PREPARE_VOLUME_CONVERT_PATH) {
            return new BareMetal2GatewayCommands.PrepareConvertVolumeConfigRsp()
        }

        handle(BareMetal2GatewayConstant.DESTROY_VOLUME_CONVERT_PATH) {
            return new BareMetal2GatewayCommands.DestroyVolumeForInstanceRsp()
        }

        handle(BareMetal2GatewayConstant.GET_VOLUME_LUNID_FOR_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.GetVolumeLunIdRsp()
        }

        handle(BareMetal2GatewayConstant.CREATE_CONSOLE_PROXY_FOR_INSTANCE_PATH) {
            def rsp = new BareMetal2GatewayCommands.CreateConsoleProxyForInstanceRsp()
            rsp.scheme = "vnc"
            rsp.port = 5900
            return rsp
        }

        handle(BareMetal2InstanceConstant.PING_BAREMETAL2_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.PingBareMetal2InstanceRsp()
        }

        handle(BareMetal2InstanceConstant.STOP_BAREMETAL2_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.StopBareMetal2InstanceRsp()
        }

        handle(BareMetal2InstanceConstant.REBOOT_BAREMETAL2_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.RebootBareMetal2InstanceRsp()
        }

        handle(BareMetal2InstanceConstant.ATTACH_NIC_TO_BM_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.AttachNicToInstanceRsp()
        }

        handle(BareMetal2InstanceConstant.DETACH_NIC_FROM_BM_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.DetachNicFromInstanceRsp()
        }

        handle(BareMetal2InstanceConstant.ATTACH_VOLUME_TO_BM_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.AttachVolumeToInstanceRsp()
        }

        handle(BareMetal2InstanceConstant.DETACH_VOLUME_FROM_BM_INSTANCE_PATH) {
            return new BareMetal2GatewayCommands.DetachVolumeFromInstanceRsp()
        }

        handle(BareMetal2InstanceConstant.CHANGE_BM_INSTANCE_DEFAULT_ROUTE_PATH) {
            return new BareMetal2GatewayCommands.ChangeInstanceDefaultRouteRsp()
        }

        handle(BareMetal2InstanceConstant.CHANGE_BM_INSTANCE_SYSTEM_PASSWORD_PATH) {
            return new BareMetal2GatewayCommands.ChangeInstanceSystemPasswordRsp()
        }
    }
}
