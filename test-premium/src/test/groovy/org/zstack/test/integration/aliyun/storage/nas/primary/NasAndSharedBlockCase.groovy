package org.zstack.test.integration.aliyun.storage.nas.primary

import org.zstack.core.db.Q
import org.zstack.header.storage.primary.PrimaryStorageHostRefVO
import org.zstack.header.storage.primary.PrimaryStorageHostRefVO_
import org.zstack.license.LicenseType
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.HostInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
/**
 * Created by mingjian.deng on 2019/6/11.*/
class NasAndSharedBlockCase extends PremiumSubCase {
    EnvSpec env
    PrimaryStorageInventory nasPs
    PrimaryStorageInventory sbPs
    InstanceOfferingInventory instanceOffering
    DiskOfferingInventory diskOffering
    ImageInventory image
    L3NetworkInventory l3_1
    L3NetworkInventory l3_2
    HostInventory nasHost
    HostInventory sbHost

    @Override
    void environment() {
        env = AliyunNasPrimaryEnv.createAliyunNasAndSharedBlockBoth()
    }

    @Override
    void test() {
        env.create {
            prepare()
            testCreateVm()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
        spring {
            hybrid()
            plugins()
        }
    }

    void prepare() {
        instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        image = env.inventoryByName("image") as ImageInventory
        diskOffering = env.inventoryByName("diskOffering") as DiskOfferingInventory
        nasPs = env.inventoryByName("nas-ps") as PrimaryStorageInventory
        sbPs = env.inventoryByName("sb-ps") as PrimaryStorageInventory

        l3_1 = env.inventoryByName("l3-1") as L3NetworkInventory
        l3_2 = env.inventoryByName("l3-2") as L3NetworkInventory
        nasHost = env.inventoryByName("kvm1") as HostInventory
        sbHost = env.inventoryByName("kvm2") as HostInventory


        assert Q.New(PrimaryStorageHostRefVO.class).eq(PrimaryStorageHostRefVO_.primaryStorageUuid, nasPs.uuid).eq(PrimaryStorageHostRefVO_.hostUuid, nasHost.uuid).count() == 1
        assert Q.New(PrimaryStorageHostRefVO.class).eq(PrimaryStorageHostRefVO_.primaryStorageUuid, sbPs.uuid).eq(PrimaryStorageHostRefVO_.hostUuid, sbHost.uuid).count() == 1
        assert Q.New(PrimaryStorageHostRefVO.class).count() == 2
    }

    void testCreateVm() {
        def nasVm = createVmInstance {
            name = "nas-vm"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            primaryStorageUuidForRootVolume = nasPs.uuid
            l3NetworkUuids = [l3_1.uuid]
            dataDiskOfferingUuids = [diskOffering.uuid]
        } as VmInstanceInventory

        assert nasVm.hostUuid == nasHost.uuid
        nasVm.allVolumes.each {
            assert it.primaryStorageUuid == nasPs.uuid
        }

        def sbVm = createVmInstance {
            name = "vm1"
            l3NetworkUuids = [l3_2.uuid]
            primaryStorageUuidForRootVolume = sbPs.uuid
            imageUuid = image.uuid
            instanceOfferingUuid = instanceOffering.uuid
            dataDiskOfferingUuids = [diskOffering.uuid]
        } as VmInstanceInventory

        assert sbVm.hostUuid == sbHost.uuid
        sbVm.allVolumes.each {
            assert it.primaryStorageUuid == sbPs.uuid
        }
    }

}
