package org.zstack.test.integration.premium.pciDevice

import org.springframework.http.HttpEntity
import org.zstack.core.db.DatabaseFacade
import org.zstack.header.host.HostStatus
import org.zstack.header.vm.VmInstanceVO
import org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend
import org.zstack.pciDevice.PciDeviceTO
import org.zstack.pciDevice.PciDeviceTOExampleMaker
import org.zstack.pciDevice.PciDeviceVO
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus
import org.zstack.sdk.KVMHostInventory
import org.zstack.sdk.PciDeviceInventory
import org.zstack.sdk.PciDeviceOfferingInventory
import org.zstack.sdk.PciDevicePciDeviceOfferingRefInventory
import org.zstack.sdk.PciDeviceSpecInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
/**
 * Created by GuoYi on 2020-06-08.
 */
class TestSyncPciDeviceInfoAfterCreatePciOfferingCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.11"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm2"
                        managementIp = "127.0.0.12"
                        username = "root"
                        password = "password"
                    }
                }
            }
        }
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            initEnv()
            testSyncPciDeviceInfoAfterCreatePciOffering()
        }
    }

    void initEnv() {
        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_GPU(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_AUDIO(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
                    PciDeviceTOExampleMaker.GT710B_AUDIO(),
                    PciDeviceTOExampleMaker.S7150_VIRTUALIZED(),
                    PciDeviceTOExampleMaker.S7150V_3936M_1(),
                    PciDeviceTOExampleMaker.S7150V_3936M_2(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        List<KVMHostInventory> result = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : result) {
            updateHostIommuState {
                delegate.uuid = host.uuid
                delegate.state = "Enabled"
            }
            reconnectHost {
                delegate.uuid = host.uuid
            }
        }
        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
            assert (queryPciDevice { conditions = ["vendorId=1002", "deviceId=692f"] } as List<PciDeviceInventory>).size() == 2 * 2
        }
    }

    void testSyncPciDeviceInfoAfterCreatePciOffering() {
        createPciDeviceOffering {
            name = "S7150_VF_OFFERING"
            vendorId = "1002"
            deviceId = "692f"
        }

        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_GPU(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_AUDIO(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
                    PciDeviceTOExampleMaker.GT710B_AUDIO(),
                    PciDeviceTOExampleMaker.S7150_VIRTUALIZED(),
                    PciDeviceTOExampleMaker.S7150V_3936M_1(),
                    PciDeviceTOExampleMaker.S7150V_3936M_2(),
                    PciDeviceTOExampleMaker.S7150V_3936M_3(),
                    PciDeviceTOExampleMaker.S7150V_3936M_4(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        List<KVMHostInventory> result = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : result) {
            reconnectHost {
                delegate.uuid = host.uuid
            }
        }
        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
            assert (queryPciDevice { conditions = ["vendorId=1002", "deviceId=692f"] } as List<PciDeviceInventory>).size() == 4 * 2
        }

        PciDeviceOfferingInventory pciInv = createPciDeviceOffering {
            name = "MOXA_DEVICE"
            vendorId = "1393"
            deviceId = "1041"
            subvendorId = "1393"
            subdeviceId = "1041"
        }

        assert (queryPciDevicePciDeviceOffering { conditions = ["pciDeviceOfferingUuid=${pciInv.uuid}"] } as List<PciDevicePciDeviceOfferingRefInventory>).size() == 2
    }

    @Override
    void clean() {
        env.delete()
    }
}
