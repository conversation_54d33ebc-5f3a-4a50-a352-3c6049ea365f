package org.zstack.test.integration.premium.image

import com.google.gson.Gson
import org.springframework.http.HttpEntity
import org.zstack.core.Platform
import org.zstack.core.agent.AgentConstant
import org.zstack.core.db.Q
import org.zstack.header.Constants
import org.zstack.header.image.APICreateDataVolumeTemplateFromVolumeMsg
import org.zstack.header.image.APICreateRootVolumeTemplateFromRootVolumeMsg
import org.zstack.header.image.ImagePlatform
import org.zstack.header.image.ImageVO
import org.zstack.header.image.ImageVO_
import org.zstack.header.longjob.LongJobState
import org.zstack.header.longjob.LongJobVO
import org.zstack.header.longjob.LongJobVO_
import org.zstack.header.message.Message
import org.zstack.kvm.KVMAgentCommands
import org.zstack.sdk.BackupStorageInventory
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.LongJobInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VolumeInventory
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageConstant
import org.zstack.storage.ceph.primary.CephPrimaryStorageBase
import org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.storage.primary.sharedblock.SharedBlockImageStoreKvmBackend

class CreateSharedBlockTemplateLongJobCase extends PremiumSubCase {
    EnvSpec env
    BackupStorageInventory imageStore
    VolumeInventory dataVol
    VmInstanceInventory vm
    Gson gson = new Gson()

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void test() {
        env.create {
            imageStore = env.inventoryByName("imagestore") as BackupStorageInventory
            vm = env.inventoryByName("vm") as VmInstanceInventory
            prepareEnv()
            testCancelCreateRootTemplateOnBs(imageStore.uuid)
            testCancelCreateDataTemplateOnBs(imageStore.uuid)
            testCreateDataTemplate()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    void prepareEnv() {
        def diskOffer = env.inventoryByName("diskOffering1") as DiskOfferingInventory

        dataVol = createDataVolume {
            name = "dataVolume"
            diskOfferingUuid = diskOffer.uuid
        } as VolumeInventory

        attachDataVolumeToVm {
            volumeUuid = dataVol.uuid
            vmInstanceUuid = vm.uuid
        }
    }

    void testCancelCreateRootTemplateOnBs(String bsUuid) {
        def msg = new APICreateRootVolumeTemplateFromRootVolumeMsg()
        msg.name = "test"
        msg.rootVolumeUuid = vm.rootVolumeUuid
        msg.platform = ImagePlatform.Linux.toString()
        msg.backupStorageUuids = [bsUuid]
        msg.resourceUuid = Platform.uuid
        testCancelCreateTemplateOnBs(msg, bsUuid, SharedBlockImageStoreKvmBackend.COMMIT_PATH)
    }

    void testCancelCreateDataTemplateOnBs(String bsUuid) {
        def msg = new APICreateDataVolumeTemplateFromVolumeMsg()
        msg.name = "test"
        msg.volumeUuid = dataVol.uuid
        msg.backupStorageUuids = [bsUuid]
        msg.resourceUuid = Platform.uuid

        testCancelCreateTemplateOnBs(msg, bsUuid, SharedBlockImageStoreKvmBackend.COMMIT_PATH)
    }

    void testCancelCreateTemplateOnBs(Message msg, String bsUuid, String path) {
        boolean canceled = false
        String hostUuid
        env.simulator(path) { HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.AgentCommand.class)
            hostUuid = e.getHeaders().getFirst(Constants.AGENT_HTTP_HEADER_RESOURCE_UUID)
            while(!canceled) {
                sleep(500)
            }

            def rsp = new SharedBlockKvmCommands.AgentRsp()
            rsp.error = "job canceled"
            rsp.success = false
            return rsp
        }

        LongJobInventory jobInv
        env.simulator(AgentConstant.CANCEL_JOB) { HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.CancelCmd.class)
            def cancelHostUuid = e.getHeaders().getFirst(Constants.AGENT_HTTP_HEADER_RESOURCE_UUID)

            assert cmd.cancellationApiId == jobInv.apiId
            assert cancelHostUuid == hostUuid
            canceled = true
            return new CephPrimaryStorageBase.AgentResponse()
        }

        jobInv = submitLongJob {
            jobName = msg.getClass().getSimpleName()
            jobData = gson.toJson(msg)
        } as LongJobInventory

        while(hostUuid == null) {
            sleep(500)
        }

        cancelLongJob {
            uuid = jobInv.uuid
        }

        retryInSecs {
            assert Q.New(LongJobVO.class).eq(LongJobVO_.uuid, jobInv.uuid).select(LongJobVO_.state).findValue() == LongJobState.Canceled
            assert !Q.New(ImageVO.class).eq(ImageVO_.uuid, msg.resourceUuid).isExists()
        }
    }

    void testCreateDataTemplate() {
        env.cleanSimulatorHandlers()
        def image = createDataVolumeTemplateFromVolume {
            name = "vol-image"
            volumeUuid = dataVol.uuid
        } as ImageInventory

        assert image.backupStorageRefs[0].backupStorageUuid == imageStore.uuid
    }

    @Override
    void environment() {
        env = makeEnv {
            account {
                name = "test1"
                password = "password"
            }

            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            cephBackupStorage {
                name = "ceph-bk"
                description = "Test"
                totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                availableCapacity = SizeUnit.GIGABYTE.toByte(100)
                url = "/bk"
                fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                monUrls = ["root:password@localhost/?monPort=7777"]
            }

            imageStore {
                name = "imagestore"
                username = "root"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                }
                image {
                    name = "vr"
                    url = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("sharedblock-ps")
                    attachL2Network("l2")
                }

                sbgPrimaryStorage {
                    name = "sharedblock-ps"
                    description = "TestPs1"
                    diskUuids = ["0121520F-55D2-4541-9345-887B9074A157"]
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(100)
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                diskOffering {
                    name = "diskOffering1"
                    diskSize = SizeUnit.GIGABYTE.toByte(5)
                }

                diskOffering {
                    name = "diskOffering2"
                    diskSize = SizeUnit.GIGABYTE.toByte(10)
                }

                attachBackupStorage("imagestore")
                attachBackupStorage("ceph-bk")
            }

            vm {
                name = "vm"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useL3Networks("pubL3")
            }
        }
    }
}
