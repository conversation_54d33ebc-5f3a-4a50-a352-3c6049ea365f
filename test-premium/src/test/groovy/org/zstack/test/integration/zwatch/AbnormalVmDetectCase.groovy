package org.zstack.test.integration.zwatch

import org.springframework.http.HttpEntity
import org.zstack.header.Constants
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vm.VmAbnormalLifeCycleStruct
import org.zstack.header.vm.VmInstanceState
import org.zstack.kvm.KVMConstant
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.sdk.HostInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.zwatch.alarm.sns.AbstractTextTemplate
import org.zstack.zwatch.namespace.VmAbstractNamespace
import org.zstack.zwatch.namespace.VmNamespace

class AbnormalVmDetectCase extends PremiumSubCase {
    EnvSpec env
    String BASE_URL = "/test-http-endpoint"

    @Override
    void setup() {
        useSpring(ZWatchTest.springSpec)
    }

    @Override
    void test() {
        env.create {
            testVMStateChangedOnHostEvent()
        }
    }

    void testVMStateChangedOnHostEvent() {
        VmNamespace ns = bean(VmNamespace.class)
        HostInventory host = env.inventoryByName("kvm2")

        List<Map> events = []
        env.simulator(BASE_URL) { HttpEntity<String> e ->
            logger.debug("received an event ${e.body}")
            events.add(json(e.body, LinkedHashMap.class))
        }

        VmInstanceInventory vm = env.inventoryByName("vm")

        // make kvm2 vm sync running state as a result of split-brain
        env.afterSimulator(KVMConstant.KVM_VM_SYNC_PATH) { rsp, HttpEntity<String> e ->
            def hostUuid = e.getHeaders().getFirst(Constants.AGENT_HTTP_HEADER_RESOURCE_UUID)
            if (hostUuid == host.uuid) {
                rsp.states[vm.uuid] = KVMConstant.KvmVmState.Running.toString()
            }

            return rsp
        }

        reconnectHost {
            uuid = host.uuid
        }

        retryInSecs {
            assert events.size() == 1
        }

        Map event = events.get(0)
        assert event.get(AbstractTextTemplate.PARAM_EVENT_NAME) == VmNamespace.VmAbnormalLifeCycleDetected.name
        assert event.get(AbstractTextTemplate.PARAM_EVENT_NAMESPACE) == ns.getName()
        assert event.containsKey(AbstractTextTemplate.PARAM_EVENT_LABELS)
        assert event.get(AbstractTextTemplate.PARAM_EVENT_LABELS)[VmAbstractNamespace.EventLabelNames2.OldState.toString()] == VmInstanceState.Running.toString()
        assert event.get(AbstractTextTemplate.PARAM_EVENT_LABELS)[VmAbstractNamespace.EventLabelNames2.NewState.toString()] == VmInstanceState.Running.toString()
        assert event.get(AbstractTextTemplate.PARAM_EVENT_LABELS)[VmAbstractNamespace.EventLabelNames2.DestinationHostUuid.toString()] == host.uuid
        assert event.get(AbstractTextTemplate.PARAM_EVENT_LABELS)[VmAbstractNamespace.EventLabelNames2.SourceHostUuid.toString()] == vm.hostUuid
        assert event.get(AbstractTextTemplate.PARAM_EVENT_LABELS)[VmAbstractNamespace.EventLabelNames2.VmAbnormalLifeCycleOperation.toString()] == VmAbnormalLifeCycleStruct
                .VmAbnormalLifeCycleOperation.VmMigrateToAnotherHost.toString()
    }

    @Override
    void environment() {
        VmNamespace ns = bean(VmNamespace.class)
        env = makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(20)
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm"
                        managementIp = "localhost"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = VyosConstants.VYOS_ROUTER_PROVIDER_TYPE
                            types = [NetworkServiceType.DHCP.toString(), NetworkServiceType.DNS.toString()]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }

            vm {
                name = "vm"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useL3Networks("l3")
                useHost("kvm")
            }

            sns {
                topic {
                    name = "topic"
                }

                httpEndpoint {
                    name = "http"
                    url = "http://127.0.0.1:8989${BASE_URL}"

                    subscribe("topic")
                }
            }

            zwatch {
                event {
                    namespace = ns.getName()
                    eventName = VmNamespace.VmAbnormalLifeCycleDetected.name
                    useTopic("topic")
                }
            }
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}
