package org.zstack.test.integration.baremetal2

import org.apache.commons.collections.list.SynchronizedList
import org.springframework.http.HttpEntity
import org.zstack.baremetal2.BareMetal2GlobalConfig
import org.zstack.baremetal2.gateway.BareMetal2GatewayCommands
import org.zstack.baremetal2.instance.BareMetal2InstanceConstant
import org.zstack.baremetal2.instance.BareMetal2InstanceStatus
import org.zstack.baremetal2.instance.BareMetal2InstanceVO
import org.zstack.baremetal2.instance.BareMetal2InstanceVO_
import org.zstack.core.db.Q
import org.zstack.header.network.l3.UsedIpVO
import org.zstack.header.network.l3.UsedIpVO_
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmNicVO
import org.zstack.header.vm.VmNicVO_
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.userdata.UserdataConstant
import org.zstack.sdk.BareMetal2InstanceInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

/**
 * Created by GuoYi on 11/30/20.
 */
class BareMetal2InstanceVlanNetworkCase extends PremiumSubCase {
    EnvSpec env
    L3NetworkInventory l3_1
    L3NetworkInventory l3_2
    BareMetal2InstanceInventory bm

    List<BareMetal2GatewayCommands.AttachNicToInstanceCmd> attachNicCmds = [] as SynchronizedList
    List<BareMetal2GatewayCommands.DetachNicFromInstanceCmd> detachNicCmds = [] as SynchronizedList

    @Override
    void setup() {
        spring {
            useSpring(BareMetal2Test.springSpec)
        }
    }

    @Override
    void environment() {
        env = makeEnv {
            zone {
                name = "zone"

                bareMetal2ProvisionNetwork {
                    name = "provision_net_1"
                    dhcpInterface = "enp0s0f0"
                    dhcpRangeStartIp = "************"
                    dhcpRangeEndIp = "************0"
                    dhcpRangeNetmask = "*************"
                    dhcpRangeGateway = "***********"
                }

                l2VlanNetwork {
                    name = "l2-vlan"
                    physicalInterface = "eth0"
                    vlan = 10
                    l3Network {
                        name = "l3_1"
                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }
                    }

                    l3Network {
                        name = "l3_2"
                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }
                    }
                }

                sbgPrimaryStorage {
                    name = "sharedblock-ps"
                    description = "Test"
                    diskUuids = ["0121520F-55D2-4541-9345-887B9074A157"]
                }

                bareMetal2Cluster {
                    name = "bm2_cluster_1"

                    bareMetal2Gateway {
                        name = "gateway-1"
                        managementIp = "127.0.0.11"
                        username = "root"
                        password = "password"
                    }

                    bareMetal2Chassis {
                        name = "chassis_1"
                        ipmiAddress = "*********"
                        ipmiUsername = "username"
                        ipmiPassword = "password"

                        hardwareInfo {
                            info = "{'bootMode':'UEFI', 'architecture':'x86_64', 'cpuModelName':'Intel i7-6700K', 'cpuNum':'8', 'memorySize':'33421254656'," +
                                    "'nics':[{'nicMac':'40:8d:5c:f7:8d:60', 'nicSpeed':'1000Mbps', 'isProvisionNic':'true', 'nicName': 'eth0'}," +
                                    "{'nicMac':'40:8d:5c:f7:8d:61', 'nicSpeed':'1000Mbps', 'isProvisionNic':'false', 'nicName': 'eth1'}," +
                                    "{'nicMac':'40:8d:5c:f7:8d:62', 'nicSpeed':'1000Mbps', 'isProvisionNic':'false', 'nicName': 'eth2'}," +
                                    "{'nicMac':'40:8d:5c:f7:8d:63', 'nicSpeed':'1000Mbps', 'isProvisionNic':'false', 'nicName': 'eth3'}]}"
                        }
                    }

                    attachL2Network("l2-vlan")
                    attachPrimaryStorage("sharedblock-ps")
                    attachProvisionNetworks("provision_net_1")
                }

                attachBackupStorage("imagestore")
            }

            imageStore {
                name = "imagestore"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "bm_raw"
                    format = "raw"
                    url = "http://zstack.org/download/test.raw"
                    systemTags = ["baremetal2", "bootMode::UEFI"]
                }
            }

            bareMetal2Instance {
                name = "BM_1"
                useImage("bm_raw")
                useChassis("chassis_1")
            }
        }
    }

    @Override
    void test() {
        env.create {
            initEnv()

            testAttachVlanNetworkToBareMetal2Instance()
            testDetachVlanNetworkFromBareMetal2Instance()
        }
    }

    void initEnv() {
        bm = env.inventoryByName("BM_1") as BareMetal2InstanceInventory
        l3_1 = env.inventoryByName("l3_1") as L3NetworkInventory
        l3_2 = env.inventoryByName("l3_2") as L3NetworkInventory

        BareMetal2GlobalConfig.BAREMETAL2_INSTANCE_PING_INTERVAL.updateValue(1)

        retryInSecs {
            Q.New(BareMetal2InstanceVO.class)
                    .eq(BareMetal2InstanceVO_.uuid, bm.uuid)
                    .eq(BareMetal2InstanceVO_.state, VmInstanceState.Running)
                    .eq(BareMetal2InstanceVO_.status, BareMetal2InstanceStatus.Connected)
                    .isExists()
        }

        env.afterSimulator(BareMetal2InstanceConstant.ATTACH_NIC_TO_BM_INSTANCE_PATH) { rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, BareMetal2GatewayCommands.AttachNicToInstanceCmd.class)
            attachNicCmds.add(cmd)
            return rsp
        }

        env.afterSimulator(BareMetal2InstanceConstant.DETACH_NIC_FROM_BM_INSTANCE_PATH) { rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, BareMetal2GatewayCommands.DetachNicFromInstanceCmd.class)
            detachNicCmds.add(cmd)
            return rsp
        }
    }

    void testAttachVlanNetworkToBareMetal2Instance() {
        attachNicCmds.clear()

        attachL3NetworkToVm {
            vmInstanceUuid = bm.uuid
            l3NetworkUuid = l3_1.uuid
            customMac = "40:8d:5c:f7:8d:61"
        }

        retryInSecs {
            assert 1 == attachNicCmds.size()

            def cmd = attachNicCmds.get(0) as BareMetal2GatewayCommands.AttachNicToInstanceCmd
            assert cmd.nic.vlanId == 10

            assert 1L == Q.New(VmNicVO.class).eq(VmNicVO_.vmInstanceUuid, bm.uuid).count()
            assert 3L == Q.New(UsedIpVO.class).in(UsedIpVO_.l3NetworkUuid, [l3_1.uuid, l3_2.uuid]).count()
        }

        expect(AssertionError.class) {
            attachL3NetworkToVm {
                vmInstanceUuid = bm.uuid
                l3NetworkUuid = l3_2.uuid
                customMac = "40:8d:5c:f7:8d:61"
            }
        }

        assert 1 == attachNicCmds.size()
    }

    void testDetachVlanNetworkFromBareMetal2Instance() {
        attachNicCmds.clear()

        def nicUuid = Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, bm.uuid)
                .eq(VmNicVO_.l3NetworkUuid, l3_1.uuid)
                .select(VmNicVO_.uuid)
                .findValue()

        detachL3NetworkFromVm {
            vmNicUuid = nicUuid
        }

        retryInSecs {
            assert 1 == detachNicCmds.size()

            def cmd = detachNicCmds.get(0) as BareMetal2GatewayCommands.DetachNicFromInstanceCmd
            assert cmd.nic.vlanId == 10

            assert 0L == Q.New(VmNicVO.class).eq(VmNicVO_.vmInstanceUuid, bm.uuid).count()
            assert 2L == Q.New(UsedIpVO.class).in(UsedIpVO_.l3NetworkUuid, [l3_1.uuid, l3_2.uuid]).count()
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}