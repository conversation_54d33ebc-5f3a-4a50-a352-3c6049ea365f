package org.zstack.test.integration.premium.vhostuser

import org.springframework.http.HttpEntity
import org.zstack.appliancevm.ApplianceVmConstant
import org.zstack.appliancevm.ApplianceVmKvmCommands
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.cluster.MevocoClusterGlobalConfig
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.db.Q
import org.zstack.header.host.*
import org.zstack.header.network.l3.L3NetworkConstant
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmNicVO
import org.zstack.header.vm.VmNicVO_
import org.zstack.header.vm.VmOvsNicConstant
import org.zstack.header.vpc.VpcConstants
import org.zstack.ipsec.IPsecConstants
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.lb.LoadBalancerConstants
import org.zstack.network.service.lb.LoadBalancerType
import org.zstack.network.service.portforwarding.PortForwardingConstant
import org.zstack.network.service.slb.SlbSystemTags
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.vrouterRoute.VRouterRouteConstants

class TestApplianceVmWithVHostUserNicCase extends PremiumSubCase{
    EnvSpec env

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(2)
                cpu = 2
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(20)
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm-1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalCpu = 40
                        totalMem = SizeUnit.GIGABYTE.toByte(32)

                    }

                    kvm {
                        name = "kvm-2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 40
                        totalMem = SizeUnit.GIGABYTE.toByte(32)

                    }

                    attachPrimaryStorage("nfs")
                    attachL2Network("l2")
                }

                nfsPrimaryStorage {
                    name = "nfs"
                    url = "localhost:/nfs"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3-1"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-2"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-3"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-vrouter"
                        type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "pubL3"
                        category = "Public"

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE
                            types = [NetworkServiceType.DHCP.toString(), NetworkServiceType.DNS.toString(), EipConstant.EIP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString()]
                        }
                    }

                    l3Network {
                        name = "pubL3-1"
                        category = "Public"

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE
                            types = [NetworkServiceType.DHCP.toString(), NetworkServiceType.DNS.toString(), EipConstant.EIP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }
                    }

                    l3Network {
                        name = "pubL3-2"
                        category = "Public"

                        ip {
                            startIp = "*************20"
                            endIp = "***************"
                            netmask = "*************"
                            gateway = "*************"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE
                            types = [NetworkServiceType.DHCP.toString(), NetworkServiceType.DNS.toString(), EipConstant.EIP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }
                    }

                    l3Network {
                        name = "flat-l3"
                        category = "Private"

                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     EipConstant.EIP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString()]
                        }
                    }
                }

                l2NoVlanNetwork {
                    name = "l2-ovs"
                    vSwitchType = "OvsDpdk"
                    physicalInterface = "bond0"

                    l3Network {
                        name = "l3-ovs"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr-offering"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr")
                }

                attachBackupStorage("sftp")

                vpcVRouter {
                    name = "vpc"
                    useVirtualRouterOffering("vr-offering")
                }
            }
        }
    }

    @Override
    void test() {
        env.create {
            initEnv()
            TestCreateVpcSlbInstanceWithVHostUser()
        }
    }

    void initEnv() {

        updateGlobalConfig {
            category = "networkService"
            name = "enableVHostUser"
            value = "true"
        }

        VpcRouterVmInventory vpc = queryVpcRouter {conditions=["name=vpc"]} [0]

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> entity, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory();
            inv1.setInterfaceName("enp101s0f0");
            inv1.setSpeed(10000L);
            inv1.setCarrierActive(true);
            inv1.setMac("ac:1f:6b:93:6c:8c");
            inv1.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory();
            inv2.setInterfaceName("enp101s0f1");
            inv2.setSpeed(10000L);
            inv2.setCarrierActive(true);
            inv2.setMac("ac:1f:6b:93:6c:8d");
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2];

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.OVS_BONDING_TYPE.toString());
            bond0.setMode("active-backup 1");
            bond0.setXmitHashPolicy("layer 3+4");
            bond0.setSlaves(reply.nics);

            reply.bondings = [bond0]

            return reply;
        }

        List<KVMHostInventory> allHosts = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : allHosts) {
            // set host under cluster to maintenance mode
            updateHostIommuState {
                delegate.uuid = host.uuid
                delegate.state = "Enabled"
            }
            changeHostState {
                uuid = host.getUuid()
                stateEvent = HostStateEvent.maintain.toString()
            }
        }

        List<ClusterInventory> allClusters = queryCluster {} as List<ClusterInventory>
        for (ClusterInventory cluster : allClusters) {
            // enable OVS_DPDK_SUPPORT
            updateResourceConfig {
                category = MevocoClusterGlobalConfig.CATEGORY
                name = MevocoClusterGlobalConfig.HUGEPAGE_ENABLE.name
                value = "true"
                resourceUuid = cluster.uuid
            }
            updateResourceConfig {
                category = MevocoClusterGlobalConfig.CATEGORY
                name = MevocoClusterGlobalConfig.OVS_DPDK_SUPPORT.name
                value = "true"
                resourceUuid = cluster.uuid
            }
        }

        for (KVMHostInventory host : allHosts) {
            // set host under cluster to enable mode
            changeHostState {
                uuid = host.getUuid()
                stateEvent = HostStateEvent.enable.toString()
            }

            reconnectHost {
                uuid = host.getUuid()
            }
        }

        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
        }

        L2NetworkInventory l2_ovs = env.inventoryByName("l2-ovs")
        ClusterInventory cluster = env.inventoryByName("cluster")

        attachL2NetworkToCluster {
            l2NetworkUuid = l2_ovs.getUuid()
            clusterUuid = cluster.getUuid()
        }

        startVmInstance {
            uuid = vpc.uuid
        }


        retryInSecs(15) {
            vpc = queryVpcRouter {conditions=["name=vpc"]} [0]
            assert vpc.state == VmInstanceState.Running.toString()
        }

    }

    void TestCreateVpcSlbInstanceWithVHostUser() {
        L3NetworkInventory pub = env.inventoryByName("pubL3")
        L3NetworkInventory pub_2 = env.inventoryByName("pubL3-2")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")
        L3NetworkInventory l3_3 = env.inventoryByName("l3-3")
        L3NetworkInventory l3_ovs = env.inventoryByName("l3-ovs")
        ZoneInventory zone = env.inventoryByName("zone")
        ImageInventory image = queryImage {conditions=["name=vr"]} [0]
        VpcRouterVmInventory vpc = queryVpcRouter {conditions=["name=vpc"]} [0]

        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpc.uuid
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpc.uuid
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_3.uuid
            vmInstanceUuid = vpc.uuid
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_ovs.uuid
            vmInstanceUuid = vpc.uuid
        }

        SlbOfferingInventory slbOffering = createSlbOffering {
            zoneUuid = zone.uuid
            managementNetworkUuid = pub.uuid
            imageUuid = image.uuid
            name = "slb-image"
            cpuNum = 1
            memorySize = 1024*1024*1024
        }

        SlbGroupInventory slbGroup = createSlbGroup {
            name = "slb group"
            slbOfferingUuid = slbOffering.uuid
            frontEndL3NetworkUuid = pub.uuid
            backendL3NetworkUuids = [l3_1.uuid, l3_2.uuid, l3_ovs.uuid]
        }

        expect(AssertionError.class) {
            createSlbGroup {
                name = "slb group failed"
                slbOfferingUuid = slbOffering.uuid
                frontEndL3NetworkUuid = pub.uuid
                backendL3NetworkUuids = [pub_2.uuid]
            }
        }

        VipInventory slbVip = createVip {
            name = "slb"
            l3NetworkUuid = pub.uuid
        }

        createLoadBalancer {
            name = "test-lb-1"
            vipUuid = slbVip.getUuid()
            systemTags = [String.format("%s::%s", SlbSystemTags.LOAD_BALANCER_SLB_GROUP_TOKEN, slbGroup.uuid)]
        }

        LoadBalancerInventory lb = queryLoadBalancer {conditions = ["type="+ LoadBalancerType.SLB.toString()]} [0]
        assert lb.vipUuid == slbVip.uuid

        ApplianceVmKvmCommands.PrepareBootstrapInfoCmd cmd = null
        env.afterSimulator(ApplianceVmKvmCommands.PrepareBootstrapInfoCmd.PATH) { rsp, HttpEntity<String> e ->
            cmd = JSONObjectUtil.toObject(e.body, ApplianceVmKvmCommands.PrepareBootstrapInfoCmd)
            return rsp
        }

        SlbVmInstanceInventory slbVm = createSlbInstance {
            name = "slb-vm-1"
            slbGroupUuid = slbGroup.uuid
        }
        assert slbVm.getType() == ApplianceVmConstant.APPLIANCE_VM_TYPE
        assert slbVm.vmNics.size() == 4
        assert cmd.info[VyosConstants.REPLACE_FIREWALL_WITH_IPTBALES].toString() == true.toString()
        assert cmd.info[VpcConstants.TC_FOR_VIPQOS].toString() == false.toString()

        VmNicInventory pubNic
        VmNicInventory vHostUserNic
        for (VmNicInventory nic : slbVm.vmNics) {
            if (nic.l3NetworkUuid == pub.uuid) {
                pubNic = nic
            } else if (nic.l3NetworkUuid == l3_ovs.uuid) {
                vHostUserNic = nic
            }
        }
        assert pubNic.ip == slbVip.ip
        assert vHostUserNic.type.equals(VmOvsNicConstant.ACCEL_TYPE_VHOST_USER_SPACE)
        assert Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, slbVm.uuid)
                .eq(VmNicVO_.type, VmOvsNicConstant.ACCEL_TYPE_VHOST_USER_SPACE)
                .count() == 1

        expect(AssertionError.class) {
            attachL3NetworkToVm {
                l3NetworkUuid = pub_2.uuid
                vmInstanceUuid = slbVm.uuid
            }
        }

        stopVmInstance {
            uuid = slbVm.uuid
            stopHA = true
        }

        updateResourceConfig {
            category = "vpc"
            name = "tc.for.vipqos"
            resourceUuid = slbVm.uuid
            value = false
        }

        startVmInstance {
            uuid = slbVm.uuid
        }
        retryInSecs {
            assert cmd.info[VpcConstants.TC_FOR_VIPQOS].toString() == false.toString()
        }

        rebootVmInstance {
            uuid = slbVm.uuid
        }

        reconnectVirtualRouter {
            vmInstanceUuid = slbVm.uuid
        }

        destroyVmInstance {
            uuid = slbVm.uuid
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}
