package org.zstack.test.integration.premium.vpc

import org.springframework.http.HttpEntity
import org.zstack.compute.vm.VmSystemTags
import org.zstack.core.db.DatabaseFacade
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.volume.VolumeProvisioningStrategy
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.userdata.UserdataConstant
import org.zstack.pluginpremium.compute.allocator.HostAllocatorConstant
import org.zstack.pluginpremium.compute.allocator.HostAllocatorSystemTags
import org.zstack.sdk.*
import org.zstack.storage.ceph.CephPoolCapacity
import org.zstack.storage.ceph.DataSecurityPolicy
import org.zstack.storage.ceph.primary.CephPrimaryStorageBase
import org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

class VpcSpecifyVolumePsCase extends PremiumSubCase {
    EnvSpec env
    HostInventory host1
    HostInventory host1_1
    HostInventory host2
    HostInventory host3
    L3NetworkInventory l3
    L3NetworkInventory l3_2
    L3NetworkInventory l3_3
    BackupStorageInventory bs
    ImageInventory image1
    PrimaryStorageInventory local
    PrimaryStorageInventory local_2
    PrimaryStorageInventory local_3
    PrimaryStorageInventory ceph
    PrimaryStorageInventory sblk
    CephPrimaryStoragePoolInventory cephDataPool, cephRootPool
    InstanceOfferingInventory instanceOffering1G10C
    DiskOfferingInventory diskOffering
    ClusterInventory cluster1
    ClusterInventory cluster2
    ClusterInventory cluster3
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = makeEnv {
            zone {
                name = "zone"

                attachBackupStorage("imagestore")

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(61)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(61)
                }
                localPrimaryStorage {
                    name = "local_2"
                    url = "/local_ps_2"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(61)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(61)
                }
                localPrimaryStorage {
                    name = "local_3"
                    url = "/local_ps_3"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(61)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(61)
                }
                cephPrimaryStorage {
                    name = "ceph-pri"
                    description = "Second Ceph Primary Storage"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(1000)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(1000)
                    url = "ceph://pri"
                    fsid = "7ff218d9-f525-435f-8a40-3618d1772a65"
                    monUrls = ["root:password@127.0.0.12/?monPort=7777"]
                    rootVolumePoolName = "root-volume"
                    dataVolumePoolName = "data-volume"
                    imageCachePoolName = "image-cache"
                }
                sbgPrimaryStorage {
                    name="sharedblock-ps"
                    description="Test"
                    diskUuids=["0121520F-55D2-4541-9345-887B9074A157"]
                }
                nfsPrimaryStorage {
                    name = "nfs1"
                    url = "localhost:/nfs1"
                }
                nfsPrimaryStorage {
                    name = "nfs2"
                    url = "localhost:/nfs2"
                }

                cluster {
                    name = "cluster1"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalCpu = 32
                        totalMem = SizeUnit.GIGABYTE.toByte(32)
                    }

                    kvm {
                        name = "kvm1_1"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 32
                        totalMem = SizeUnit.GIGABYTE.toByte(32)
                    }

                    attachPrimaryStorage("local")
                    attachPrimaryStorage("local_2")
                    attachPrimaryStorage("ceph-pri")
                    attachL2Network("l2")
                }

                cluster {
                    name = "cluster2"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                        totalMem = SizeUnit.GIGABYTE.toByte(16)
                    }

                    attachPrimaryStorage("ceph-pri")
                    attachL2Network("l2_2")
                }

                cluster {
                    name = "cluster3"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm3"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                        totalMem = SizeUnit.GIGABYTE.toByte(16)
                    }

                    attachPrimaryStorage("sharedblock-ps")
                    attachL2Network("l2_3")
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3-1"
                        category = "Public"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                l2NoVlanNetwork {
                    name = "l2_2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3_2"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3-1_2"
                        category = "Public"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                l2NoVlanNetwork {
                    name = "l2_3"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3_3"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3-1_3"
                        category = "Public"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3-1")
                    usePublicL3Network("pubL3-1")
                    useImage("vr")
                }

                virtualRouterOffering {
                    name = "vr_2"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3-1_2")
                    usePublicL3Network("pubL3-1_2")
                    useImage("vr")
                }

                virtualRouterOffering {
                    name = "vr_3"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3-1_3")
                    usePublicL3Network("pubL3-1_3")
                    useImage("vr")
                }
            }

            imageStore {
                name = "imagestore"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                    size = SizeUnit.GIGABYTE.toByte(1)
                    actualSize = SizeUnit.GIGABYTE.toByte(1)
                    platform = "Linux"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            instanceOffering {
                name = "instanceOffering1G10C"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 10
                allocatorStrategy = HostAllocatorConstant.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_TYPE
                systemTags = [HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE.instantiateTag([(HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE_TOKEN): HostAllocatorConstant.HOST_ALLOCATOR_STRATEGY_MODE_HARD])]
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(10)
            }
        }
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            initEnv()
            testGetPrimaryStorageCandidatesForVpc()
            testCreateVpcSpecifyRootVolumePsWithDesignatedHost()
            testCreateVpcSpecifyCephPsWithDesignatedPoolCase()
            testCreateVpcSpecifySblkPsWithDesignatedVolumeProvisioningStrategyCase()
        }
    }

    void initEnv() {
        host1 = env.inventoryByName("kvm1") as HostInventory
        host1_1 = env.inventoryByName("kvm1_1") as HostInventory
        host2 = env.inventoryByName("kvm2") as HostInventory
        host3 = env.inventoryByName("kvm3") as HostInventory
        l3 = env.inventoryByName("l3") as L3NetworkInventory
        l3_2 = env.inventoryByName("l3_2") as L3NetworkInventory
        l3_3 = env.inventoryByName("l3_3") as L3NetworkInventory
        bs = env.inventoryByName("imagestore") as BackupStorageInventory
        local = env.inventoryByName("local") as PrimaryStorageInventory
        local_2 = env.inventoryByName("local_2") as PrimaryStorageInventory
        local_3 = env.inventoryByName("local_3") as PrimaryStorageInventory
        ceph = env.inventoryByName("ceph-pri") as PrimaryStorageInventory
        sblk = env.inventoryByName("sharedblock-ps") as PrimaryStorageInventory
        cluster1 = env.inventoryByName("cluster1") as ClusterInventory
        cluster2 = env.inventoryByName("cluster2") as ClusterInventory
        cluster3 = env.inventoryByName("cluster3") as ClusterInventory
        instanceOffering1G10C = env.inventoryByName("instanceOffering1G10C") as InstanceOfferingInventory
        diskOffering = env.inventoryByName("diskOffering") as DiskOfferingInventory
        image1 = env.inventoryByName("image1") as ImageInventory
    }

    void testGetPrimaryStorageCandidatesForVpc() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr")

        def vrNew1 = createVpcVRouter {
            delegate.name = "vpc-vr-0"
            delegate.virtualRouterOfferingUuid = offering.uuid
            delegate.hostUuid = host1_1.uuid
            delegate.primaryStorageUuidForRootVolume = local_2.uuid
        }as VirtualRouterVmInventory
        assert vrNew1.allVolumes.primaryStorageUuid[0] == local_2.uuid
        assert vrNew1.hostUuid == host1_1.uuid

        List<PrimaryStorageInventory> candidates = getPrimaryStorageCandidatesForVmMigration {
            vmInstanceUuid = vrNew1.getUuid()
        } as List<PrimaryStorageInventory>
        assert candidates.size() == 3
    }

    void testCreateVpcSpecifyRootVolumePsWithDesignatedHost() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr")

        def vrNew1 = createVpcVRouter {
            delegate.name = "vpc-vr-1"
            delegate.virtualRouterOfferingUuid = offering.uuid
            delegate.hostUuid = host1_1.uuid
            delegate.primaryStorageUuidForRootVolume = local_2.uuid
        }as VirtualRouterVmInventory
        assert vrNew1.allVolumes.primaryStorageUuid[0] == local_2.uuid
        assert vrNew1.hostUuid == host1_1.uuid
    }

    void testCreateVpcSpecifyCephPsWithDesignatedPoolCase() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr_2")

        env.simulator(CephPrimaryStorageBase.ADD_POOL_PATH) { HttpEntity<String> entity ->
            def cmd = JSONObjectUtil.toObject(entity.body, CephPrimaryStorageBase.AddPoolCmd.class)

            CephPrimaryStorageBase.AddPoolRsp rsp = new CephPrimaryStorageBase.AddPoolRsp()
            rsp.setAvailableCapacity(SizeUnit.GIGABYTE.toByte(100))
            rsp.setTotalCapacity(SizeUnit.GIGABYTE.toByte(100))
            List<CephPoolCapacity> poolCapacities = [
                    new CephPoolCapacity(
                            name: cmd.poolName,
                            availableCapacity: SizeUnit.GIGABYTE.toByte(100),
                            usedCapacity: 0,
                            totalCapacity: SizeUnit.GIGABYTE.toByte(100),
                            securityPolicy: DataSecurityPolicy.Copy.toString(),
                            replicatedSize: 3,
                            diskUtilization: 0.33,
                            relatedOsds: "osd.1"
                    ),
                    new CephPoolCapacity(
                            availableCapacity: SizeUnit.GIGABYTE.toByte(100),
                            usedCapacity: 0,
                            totalCapacity: SizeUnit.GIGABYTE.toByte(100),
                            securityPolicy: DataSecurityPolicy.Copy.toString(),
                            replicatedSize: 3,
                            diskUtilization: 0.33,
                            relatedOsds: "osd.2"
                    ),
                    new CephPoolCapacity(
                            availableCapacity: SizeUnit.GIGABYTE.toByte(100),
                            usedCapacity: 0,
                            totalCapacity: SizeUnit.GIGABYTE.toByte(100),
                            securityPolicy: DataSecurityPolicy.Copy.toString(),
                            replicatedSize: 3,
                            diskUtilization: 0.33,
                            relatedOsds: "osd.3"
                    )
            ]
            rsp.setPoolCapacities(poolCapacities)

            return rsp
        }

        cephDataPool = addCephPrimaryStoragePool {
            poolName = "ceph1-data"
            primaryStorageUuid = ceph.uuid
            type = "Data"
            isCreate = true
        } as CephPrimaryStoragePoolInventory

        cephRootPool = addCephPrimaryStoragePool {
            poolName = "ceph1-root"
            primaryStorageUuid = ceph.uuid
            type = "Root"
            isCreate = true
        } as CephPrimaryStoragePoolInventory

        def vm = createVmInstance {
            name = "vm1"
            l3NetworkUuids = [l3_2.uuid]
            imageUuid = image1.uuid
            instanceOfferingUuid = instanceOffering1G10C.uuid
            dataDiskOfferingUuids = [diskOffering.uuid]
            primaryStorageUuidForRootVolume = ceph.uuid
            systemTags = [VmSystemTags.PRIMARY_STORAGE_UUID_FOR_DATA_VOLUME.instantiateTag([(VmSystemTags.PRIMARY_STORAGE_UUID_FOR_DATA_VOLUME_TOKEN): ceph.uuid])]
            rootVolumeSystemTags = ["ceph::rootPoolName::ceph1-root"]
            dataVolumeSystemTags = ["ceph::pool::ceph1-data"]
        } as VmInstanceInventory
        assert vm.allVolumes.primaryStorageUuid[0] == ceph.uuid
        assert vm.allVolumes.primaryStorageUuid[1] == ceph.uuid
        //[ceph://ceph1-root/561231858a1840a4b948eaf2e6384ab8, ceph://ceph1-data/06e505ef1eac4d0ead7570bc21b08bd4]
        assert vm.allVolumes.installPath.findAll {it.contains("ceph1-")}.size() == 2

        def vrNew2 = createVpcVRouter {
            delegate.name = "vpc-vr-2"
            delegate.virtualRouterOfferingUuid = offering.uuid
            delegate.primaryStorageUuidForRootVolume = ceph.uuid
            delegate.rootVolumeSystemTags = ["ceph::rootPoolName::ceph1-root"]
        } as VirtualRouterVmInventory
        assert vrNew2.allVolumes.primaryStorageUuid[0] == ceph.uuid
        assert vrNew2.allVolumes.installPath.findAll {it.contains("ceph1-")}.size() == 1
    }

    void testCreateVpcSpecifySblkPsWithDesignatedVolumeProvisioningStrategyCase() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr_3")

        def createVolumeFromCacheCmd = new SharedBlockKvmCommands.CreateVolumeFromCacheCmd() as SharedBlockKvmCommands.CreateVolumeFromCacheCmd
        env.simulator(SharedBlockKvmCommands.CREATE_VOLUME_FROM_CACHE_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.CreateVolumeFromCacheCmd.class)
            createVolumeFromCacheCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }

        def vrNew2 = createVpcVRouter {
            delegate.name = "vpc-vr-2"
            delegate.virtualRouterOfferingUuid = offering.uuid
            delegate.primaryStorageUuidForRootVolume = sblk.uuid
            delegate.rootVolumeSystemTags = ["volumeProvisioningStrategy::ThinProvisioning"]
        } as VirtualRouterVmInventory
        assert createVolumeFromCacheCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert createVolumeFromCacheCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()
    }

    @Override
    void clean() {
        deleteCephPrimaryStoragePool {
            uuid = cephRootPool.uuid
        }

        deleteCephPrimaryStoragePool {
            uuid = cephDataPool.uuid
        }
        env.delete()
    }
}
