package org.zstack.test.integration.ticket

import org.zstack.core.db.Q
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vm.APIAttachL3NetworkToVmMsg
import org.zstack.header.vm.APIDetachL3NetworkFromVmMsg
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.ticket.entity.ArchiveTicketInventory
import org.zstack.sdk.ticket.entity.ArchiveTicketStatusHistoryInventory
import org.zstack.sdk.ticket.entity.TicketFlowCollectionInventory
import org.zstack.sdk.ticket.entity.TicketInventory
import org.zstack.sdk.ticket.entity.TicketRequest
import org.zstack.sdk.ticket.entity.TicketStatusEvent
import org.zstack.sdk.ticket.entity.TicketStatusHistoryInventory
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.ticket.entity.TicketStatus
import org.zstack.ticket.entity.TicketStatusHistoryVO
import org.zstack.ticket.entity.TicketStatusHistoryVO_
import org.zstack.ticket.entity.TicketVO
import org.zstack.ticket.entity.TicketVO_
import org.zstack.ticket.history.OperationContextType
import org.zstack.ticket.iam2.IAM2TicketApprover
import org.zstack.ticket.iam2.IAM2TicketContext
import org.zstack.ticket.iam2.IAM2TicketManager
import org.zstack.ticket.iam2.api.APICreateIAM2TickFlowCollectionMsg
import org.zstack.ticket.type.ChangeVmInstanceTicket
import org.zstack.utils.data.SizeUnit

/**
 * <AUTHOR>
 * @date 2022-08-17
 */
class TicketFlowNameCase extends TicketCaseStub{
    EnvPremiumSpec env
    TicketFlowCollectionInventory fc
    VmInstanceInventory vm
    TicketInventory ticket
    List<TicketStatusHistoryInventory> ths

    @Override
    void setup() {
        useSpring(TicketTest.springSpec)
    }

    @Override
    void environment() {
        env = makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
                toPublic = true
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(20)
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image"
                    url = "http://zstack.org/download/test.qcow2"

                    toPublic = true
                }

                image {
                    name = "vr"
                    url = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm"
                        managementIp = "localhost"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = VirtualRouterConstant.PROVIDER_TYPE
                            types = [NetworkServiceType.DHCP.toString(), NetworkServiceType.DNS.toString()]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "***********"
                            endIp = "***********0"
                            netmask = "*************"
                            gateway = "**********"
                        }

                        toPublic = true
                    }

                    l3Network {
                        name = "pubL3_2"

                        ip {
                            startIp = "***********"
                            endIp = "***********0"
                            netmask = "*************"
                            gateway = "**********"
                        }

                        toPublic = true
                    }

                    l3Network {
                        name = "pubL3_3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }

                        toPublic = true
                    }
                }

                virtualRouterOffering {
                    name = "vr"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }

            iam2 {
                project {
                    name = "project"

                    id {
                        name = "id"
                        password = "password"
                        attrPlatformAdmin()
                    }

                    id {
                        name = "id0"
                        password = "password"
                        attrProjectOperator()
                    }

                    id {
                        name = "id1"
                        password = "password"
                        attrProjectOperator()
                    }

                    id {
                        name = "id2"
                        password = "password"
                    }

                    id {
                        name = "id3"
                        password = "password"
                    }
                }
            }
        }
    }

    @Override
    void test() {
        env.create {
            prepare()
            testFlowNameInHistoryAfterCreateTicket()
            testFlowNameInHistoryAfterFirstApprove()
            testFlowNameInHistoryAfterRejectTicket()
            testFlowNameInHistoryAfterReopenTicket()
            testFlowNameInHistoryAfterCancelTicket()
            testNewFlowNameInHistoryAfterUpdateTicketFlow()
            testFlowNameInArchiveHistoryAfterDeleteFlowCollection()
            testFlowNameInArchiveHistoryAfterDeleteTicket()
        }
    }

    void prepare() {
        String adminSessionUuid = adminSession()

        withSession("project", "id", "password"){
            def list = []
            def proj = env.inventoryByName("project")

            def id = env.inventoryByName("id")
            def id0 = env.inventoryByName("id0")
            def id1 = env.inventoryByName("id1")
            def id2 = env.inventoryByName("id2")

            APICreateIAM2TickFlowCollectionMsg.IAM2FlowStruct struct = new APICreateIAM2TickFlowCollectionMsg.IAM2FlowStruct()
            struct.name = "flow1"
            struct.description = "this is flow1"
            struct.approverUuid = id0.uuid
            struct.approverTitle = IAM2TicketApprover.ProjectOperator.toString()

            list.add(struct)

            struct = new APICreateIAM2TickFlowCollectionMsg.IAM2FlowStruct()
            struct.name = "flow2"
            struct.description = "this is flow2"
            struct.approverUuid = id.uuid
            struct.approverTitle = IAM2TicketApprover.PlatformAdmin.toString()

            list.add(struct)

            List types = zqlQuery("query tickettype where adminOnly = 'false' and uuid not in " +
                    "(query tickettypeticketflowcollectionref.ticketTypeUuid where ticketFlowCollectionUuid in " +
                    "(query iam2ticketflowcollection.uuid where projectUuid = '${proj.uuid}'))")

            assert types.size() == 3

            fc = createIAM2TickFlowCollection {
                flows = list
                name = "test"
                description = "test"
                projectUuid = proj.uuid
                sessionId = adminSessionUuid
                ticketTypeUuids = [ChangeVmInstanceTicket.CHANGE_VM_INSTANCE_TICKET_UUID]
            } as TicketFlowCollectionInventory

            addRoleToVirtualIDByName(id0.uuid, "ticket")
            addRoleToVirtualIDByName(id0.uuid, "vm")
            addRoleToVirtualIDByName(id1.uuid, "ticket")
            addRoleToVirtualIDByName(id1.uuid, "vm")
            addRoleToVirtualIDByName(id2.uuid, "ticket")
            addRoleToVirtualIDByName(id2.uuid, "vm")

            fc = queryTicketFlowCollection {
                conditions = ["uuid=${fc.uuid}"]
            }[0] as TicketFlowCollectionInventory

            assert fc.ticketTypeUuids.size() == 1

            def image = env.inventoryByName("image")
            def instanceOffering = env.inventoryByName("instanceOffering")
            def l3 = env.inventoryByName("pubL3")
            vm = createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                instanceOfferingUuid = instanceOffering.uuid
                l3NetworkUuids = [l3.uuid]
            } as VmInstanceInventory
        }
    }

    void testFlowNameInHistoryAfterCreateTicket() {
        withSession("project", "id2", "password") {
            def id2 = env.inventoryByName("id2")
            def proj = env.inventoryByName("project")
            def publicL32 = env.inventoryByName("pubL3_2")

            ticket = createTicket {
                name = "ticket"
                accountSystemType = IAM2TicketManager.type
                accountSystemContext = new IAM2TicketContext(virtualIDUuid: id2.uuid, projectUuid: proj.uuid)
                requests = [new TicketRequest(
                        apiName: APIDetachL3NetworkFromVmMsg.class.getName(),
                        executeTimes: 1,
                        apiBody: new APIDetachL3NetworkFromVmMsg(
                                vmInstanceUuid: vm.uuid,
                                vmNicUuid: vm.vmNics.get(0).uuid
                        )
                ), new TicketRequest(
                        apiName: APIAttachL3NetworkToVmMsg.class.getName(),
                        executeTimes: 1,
                        apiBody: new APIAttachL3NetworkToVmMsg(
                                vmInstanceUuid: vm.uuid,
                                l3NetworkUuid: publicL32.uuid
                        )
                )]
            } as TicketInventory
        }

        assert Q.New(TicketVO.class)
                .eq(TicketVO_.uuid, ticket.uuid)
                .eq(TicketVO_.ticketTypeUuid, ChangeVmInstanceTicket.CHANGE_VM_INSTANCE_TICKET_UUID)
                .isExists()

        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>

        assert ths
        assert !ths[0].flowName
    }

    void testFlowNameInHistoryAfterFirstApprove() {
        withSession("project", "id0", "password") {
            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.approve
                comment = "approved"
            } as TicketInventory
        }

        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>

        assert ths.size() == 2
        assert ths[1].flowName == "flow1"
    }

    void testFlowNameInHistoryAfterRejectTicket() {
        withSession("project", "id", "password") {
            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.reject
                comment = "rejected"
            } as TicketInventory
        }

        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>

        assert ths.size() == 3
        assert ths[2].flowName == "flow2"
    }

    void testFlowNameInHistoryAfterReopenTicket() {
        withSession("project", "id2", "password") {
            def publicL32 = env.inventoryByName("pubL3_2")

            ticket = updateTicketRequest {
                uuid = ticket.uuid
                requests = [new TicketRequest(
                        apiName: APIDetachL3NetworkFromVmMsg.class.getName(),
                        executeTimes: 1,
                        apiBody: new APIDetachL3NetworkFromVmMsg(
                                vmInstanceUuid: vm.uuid,
                                vmNicUuid: vm.vmNics.get(0).uuid
                        )
                ), new TicketRequest(
                        apiName: APIAttachL3NetworkToVmMsg.class.getName(),
                        executeTimes: 1,
                        apiBody: new APIAttachL3NetworkToVmMsg(
                                vmInstanceUuid: vm.uuid,
                                l3NetworkUuid: publicL32.uuid
                        )
                )]
            } as TicketInventory

            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.reopen
                comment = "reopen"
            } as TicketInventory
        }

        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>

        assert ths.size() == 5
        assert ths[3].flowName == null
        assert ths[4].flowName == null
    }

    void testFlowNameInHistoryAfterCancelTicket() {
        withSession("project", "id0", "password") {
            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.approve
                comment = "approved"
            } as TicketInventory
        }

        withSession("project", "id2", "password") {
            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.cancel
                comment = "cancel"
            } as TicketInventory
        }

        withSession("project", "id2", "password") {
            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.reopen
                comment = "reopen"
            } as TicketInventory
        }

        withSession("project", "id0", "password") {
            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.approve
                comment = "approved"
            } as TicketInventory
        }

        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>

        assert ths.size() == 9
        assert ths[5].flowName == "flow1"
        assert ths[6].flowName == null
        assert ths[7].flowName == null
        assert ths[8].flowName == "flow1"
    }

    void testNewFlowNameInHistoryAfterUpdateTicketFlow() {
        String adminSessionUuid = adminSession()
        def status

        withSession("project", "id", "password") {
            def list = []

            def id = env.inventoryByName("id")
            def id1 = env.inventoryByName("id1")

            APICreateIAM2TickFlowCollectionMsg.IAM2FlowStruct struct = new APICreateIAM2TickFlowCollectionMsg.IAM2FlowStruct()
            struct.name = "new flow1"
            struct.description = "this is new flow1"
            struct.approverUuid = id1.uuid
            struct.approverTitle = IAM2TicketApprover.ProjectOperator.toString()

            list.add(struct)

            struct = new APICreateIAM2TickFlowCollectionMsg.IAM2FlowStruct()
            struct.name = "flow2"
            struct.description = "this is flow2"
            struct.approverUuid = id.uuid
            struct.approverTitle = IAM2TicketApprover.PlatformAdmin.toString()

            list.add(struct)

            fc = updateIAM2TicketFlowCollection {
                flows = list
                uuid = fc.uuid
                sessionId = adminSessionUuid
            } as TicketFlowCollectionInventory
        }

        retryInSecs {
            status = Q.New(TicketVO.class).select(TicketVO_.status).eq(TicketVO_.uuid, ticket.uuid).findValue()
            assert TicketStatus.Pending == status
        }

        withSession("project", "id1", "password") {
            ticket = changeTicketStatus {
                uuid = ticket.uuid
                statusEvent = TicketStatusEvent.approve
                comment = "approved"
            } as TicketInventory
        }

        status = Q.New(TicketVO.class).select(TicketVO_.status).eq(TicketVO_.uuid, ticket.uuid).findValue()
        assert TicketStatus.IntermediateApproved == status

        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>

        assert ths.size() == 10
        assert ths[1].flowName == "flow1"
        assert ths[5].flowName == "flow1"
        assert ths[8].flowName == "flow1"
        assert ths[9].flowName == "new flow1"
    }

    void testFlowNameInArchiveHistoryAfterDeleteFlowCollection() {
        deleteTicketFlowCollection {
            uuid = fc.uuid
        }

        retryInSecs {
            TicketVO vo = dbFindByUuid(ticket.uuid, TicketVO.class)
            assert vo.flowCollectionUuid == IAM2TicketManager.ADMIN_ONLY_FLOW_UUID
            assert vo.currentFlowUuid == null
            assert vo.status.toString() == TicketStatus.Pending.toString()
        }

        ticket = changeTicketStatus {
            uuid = ticket.uuid
            statusEvent = TicketStatusEvent.approve
            comment = "approved"
        } as TicketInventory

        def status = Q.New(TicketVO.class).select(TicketVO_.status).eq(TicketVO_.uuid, ticket.uuid).findValue()
        assert TicketStatus.FinalApproved == status

        // wait request api callback and check the record
        retryInSecs {
            assert Q.New(TicketStatusHistoryVO.class)
                    .eq(TicketStatusHistoryVO_.ticketUuid, ticket.uuid)
                    .eq(TicketStatusHistoryVO_.operationContextType, OperationContextType.RequestCompleted.toString())
                    .isExists()
        }

        vm = queryVmInstance {
            conditions = ["uuid=${vm.uuid}"]
        }[0] as VmInstanceInventory

        def publicL32 = env.inventoryByName("pubL3_2")
        assert vm.defaultL3NetworkUuid == publicL32.uuid

        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>
        assert ths
        assert ths.size() == 12
        assert ths[1].flowName == "flow1"
        assert ths[2].flowName == "flow2"
        assert ths[5].flowName == "flow1"
        assert ths[8].flowName == "flow1"
        assert ths[9].flowName == "new flow1"
        assert ths[10].flowName == "iam2-admin-flow"
        assert ths[11].flowName == "iam2-admin-flow"
    }

    void testFlowNameInArchiveHistoryAfterDeleteTicket() {
        ths = queryTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<TicketStatusHistoryInventory>
        assert ths
        assert ths.size() == 12
        
        deleteTicket {
            uuid = ticket.uuid
        }

        ArchiveTicketInventory at = queryArchiveTicket {
            conditions = ["ticketUuid=${ticket.uuid}"]
        }[0] as ArchiveTicketInventory
        assert at

        List aths = queryArchiveTicketHistory {
            conditions = ["ticketUuid=${ticket.uuid}"]
            sortBy = "sequence"
        } as List<ArchiveTicketStatusHistoryInventory>
        assert aths
        assert aths.size() == 12
        for (int i = 0; i < aths.size(); i++) {
            assert aths[i].flowName == ths[i].flowName
        }

        ticket = queryTicket {
            conditions = ["uuid=${ticket.uuid}"]
        }[0] as TicketInventory
        assert !ticket
    }

    @Override
    void clean() {
        env.delete()
    }
}