package org.zstack.test.integration.premium.vpc

import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.network.l3.IpRangeVO_
import org.zstack.header.network.l3.NormalIpRangeVO
import org.zstack.header.network.l3.NormalIpRangeVO_
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vm.VmNicVO
import org.zstack.header.vm.VmNicVO_
import org.zstack.ipsec.IPsecConstants
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.sdk.DeleteIpRangeAction
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.VirtualRouterOfferingInventory
import org.zstack.sdk.VirtualRouterVmInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.TestPremium
import org.zstack.utils.data.SizeUnit
import org.zstack.header.vpc.VpcConstants
import org.zstack.vrouterRoute.VRouterRouteConstants

class ParallelDeleteVpcIprangesCase extends PremiumSubCase {
    EnvPremiumSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = TestPremium.makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalCpu = 512
                        totalMem = SizeUnit.GIGABYTE.toByte(512)
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3-1"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "************0"
                            endIp = "************5"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-2"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "************0"
                            endIp = "************3"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-3"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "************0"
                            endIp = "************3"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-4"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "************0"
                            endIp = "************3"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "pubL3-1"
                        category = "Public"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr-offering"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3-1")
                    usePublicL3Network("pubL3-1")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }
        }
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            parallelDeleteAllIpranges()
            parallelDeleteSomeIpranges()
        }
    }

    void parallelDeleteAllIpranges() {
        def offering = env.inventoryByName("vr-offering") as VirtualRouterOfferingInventory
        def l3_1 = env.inventoryByName("l3-1") as L3NetworkInventory
        def l3_2 = env.inventoryByName("l3-2") as L3NetworkInventory
        def instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        def image = env.inventoryByName("image1") as ImageInventory

        def vpcVr = createVpcVRouter {
            delegate.name = "vpc-vr-1"
            delegate.virtualRouterOfferingUuid = offering.uuid
        } as VirtualRouterVmInventory

        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        (1..10).each {
            def vmName = "Test1Vm${it}"
            createVmInstance {
                name = vmName
                instanceOfferingUuid = instanceOffering.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3_1.getUuid()]
            }
        }

        (1..10).each {
            def vmName = "Test2Vm${it}"
            createVmInstance {
                name = vmName
                instanceOfferingUuid = instanceOffering.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3_2.getUuid()]
            } as VmInstanceInventory
        }

        def threads = []
        def errors = []

        def ipranges = Q.New(NormalIpRangeVO.class)
                .select(IpRangeVO_.uuid)
                .in(IpRangeVO_.l3NetworkUuid, [l3_1.uuid, l3_2.uuid])
                .listValues()

        ipranges.each {
            def act = new DeleteIpRangeAction()
            act.uuid = it
            act.sessionId = adminSession()

            def thread = Thread.start {
                errors.add(act.call().error)
            }
            threads.add(thread)
        }
        threads.each { it.join() }

        assert errors.each { it == null}
        ipranges = Q.New(NormalIpRangeVO.class)
                .select(NormalIpRangeVO_.uuid)
                .in(NormalIpRangeVO_.l3NetworkUuid, [l3_1.uuid, l3_2.uuid])
                .listValues()
        assert ipranges == null || ipranges.isEmpty()

        assert Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, vpcVr.uuid)
                .in(VmNicVO_.l3NetworkUuid, [l3_1.uuid, l3_2.uuid])
                .list().size() == 0
    }

    void parallelDeleteSomeIpranges() {
        def vpcVr = queryVirtualRouterVm {}[0] as VirtualRouterVmInventory
        def l3_3 = env.inventoryByName("l3-3") as L3NetworkInventory
        def l3_4 = env.inventoryByName("l3-4") as L3NetworkInventory
        def instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        def image = env.inventoryByName("image1") as ImageInventory

        attachL3NetworkToVm {
            l3NetworkUuid = l3_3.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_4.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        (1..10).each {
            def vmName = "Test3Vm${it}"
            createVmInstance {
                name = vmName
                instanceOfferingUuid = instanceOffering.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3_3.getUuid()]
            } as VmInstanceInventory
        }

        (1..10).each {
            def vmName = "Test4Vm${it}"
            createVmInstance {
                name = vmName
                instanceOfferingUuid = instanceOffering.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3_4.getUuid()]
            } as VmInstanceInventory
        }

        def threads = []
        def errors = []

        String sql = "select ip.ipRangeUuid from UsedIpVO ip, VmNicVO nic where ip.uuid = nic.usedIpUuid and nic.vmInstanceUuid = :vmUuid"
        List<String> vpcIprUuids = SQL.New(sql).param("vmUuid", vpcVr.uuid).list()

        def ipranges = Q.New(NormalIpRangeVO.class)
                .select(NormalIpRangeVO_.uuid)
                .eq(NormalIpRangeVO_.l3NetworkUuid, l3_3.uuid).notIn(NormalIpRangeVO_.uuid, vpcIprUuids)
                .limit(4)
                .listValues()
        ipranges.addAll(
                Q.New(NormalIpRangeVO.class)
                        .select(NormalIpRangeVO_.uuid)
                        .eq(NormalIpRangeVO_.l3NetworkUuid, l3_4.uuid).notIn(NormalIpRangeVO_.uuid, vpcIprUuids)
                        .limit(4)
                        .listValues()
        )

        ipranges.each {
            def act = new DeleteIpRangeAction()
            act.uuid = it
            act.sessionId = adminSession()

            def thread = Thread.start {
                errors.add(act.call().error)
            }
            threads.add(thread)
        }
        threads.each { it.join() }

        assert errors.each { it == null}

        vpcVr = queryVirtualRouterVm {}[0] as VirtualRouterVmInventory
        assert Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, vpcVr.uuid)
                .in(VmNicVO_.l3NetworkUuid, [l3_3.uuid, l3_4.uuid])
                .list().size() == 2

        threads = []
        errors = []
        ipranges = Q.New(NormalIpRangeVO.class)
                .select(NormalIpRangeVO_.uuid)
                .in(NormalIpRangeVO_.l3NetworkUuid, [l3_3.uuid, l3_4.uuid])
                .listValues()
        ipranges.each {
            def act = new DeleteIpRangeAction()
            act.uuid = it
            act.sessionId = adminSession()

            def thread = Thread.start {
                errors.add(act.call().error)
            }
            threads.add(thread)
        }
        threads.each { it.join() }
        assert errors.each { it == null}

        vpcVr = queryVirtualRouterVm {}[0] as VirtualRouterVmInventory
        assert Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, vpcVr.uuid)
                .in(VmNicVO_.l3NetworkUuid, [l3_3.uuid, l3_4.uuid])
                .list().size() == 0
    }

    @Override
    void clean() {
        env.delete()
    }
}
