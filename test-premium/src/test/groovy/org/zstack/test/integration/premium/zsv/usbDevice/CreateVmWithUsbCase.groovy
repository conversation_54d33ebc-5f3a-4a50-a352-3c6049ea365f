package org.zstack.test.integration.premium.zsv.usbDevice

import org.springframework.http.HttpEntity
import org.zstack.compute.host.HostApiInterceptor
import org.zstack.compute.vm.VmSystemTags
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.host.HostStatus
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.sdk.HostInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.KVMHostInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.UsbDeviceTestConstant
import org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend
import org.zstack.usbDevice.UsbAttachType
import org.zstack.usbDevice.UsbDeviceVO
import org.zstack.usbDevice.UsbDeviceVO_
import org.zstack.usbDevice.UsbSystemTags
import org.zstack.utils.data.SizeUnit

/**
 * @Author: DaoDao* @Date: 2023/9/7
 */
class CreateVmWithUsbCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf
    ImageInventory image
    L3NetworkInventory l3
    InstanceOfferingInventory instanceOffering
    HostInventory kvm1

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env{
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster1"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm11"
                        managementIp = "127.0.0.11"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm12"
                        managementIp = "127.0.0.12"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                cluster {
                    name = "cluster2"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm21"
                        managementIp = "**********"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm22"
                        managementIp = "**********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("nfs")
                    attachL2Network("l2")
                }

                nfsPrimaryStorage {
                    name = "nfs"
                    url = "**********:/nfs"
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString()]
                        }

                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }

            vm {
                name = "vm1"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useL3Networks("l3")
                useCluster("cluster1")
            }

            vm {
                name = "vm2"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useL3Networks("l3")
                useCluster("cluster2")
            }
        }
    }

    @Override
    void test() {
        env.create {
            image = env.inventoryByName("image1")
            l3 = env.inventoryByName("l3")
            instanceOffering = env.inventoryByName("instanceOffering")
            kvm1 = env.inventoryByName("kvm11")
            initEnv()
            testCreateVmWithUsb()
        }
    }

    void testCreateVmWithUsb () {
        def attach = false
        def detach = false
        def startUsbRedirect = false
        def port = 4010
        env.simulator(UsbDeviceKvmBackend.KVM_ATTACH_USB_DEVICE_PATH) { HttpEntity<String> entity, EnvSpec spec ->
            UsbDeviceKvmBackend.KvmAttachUsbDeviceRsp rsp = new UsbDeviceKvmBackend.KvmAttachUsbDeviceRsp()
            rsp.setSuccess(true)
            attach = true
            return rsp
        }

        env.simulator(UsbDeviceKvmBackend.KVM_DETACH_USB_DEVICE_PATH) { HttpEntity<String> entity, EnvSpec spec ->
            UsbDeviceKvmBackend.KvmDetachUsbDeviceRsp rsp = new UsbDeviceKvmBackend.KvmDetachUsbDeviceRsp()
            rsp.setSuccess(true)
            detach = true
            return rsp
        }

        env.simulator(UsbDeviceKvmBackend.HOST_START_USB_REDIRECT_PATH) { HttpEntity<String> entity, EnvSpec spec ->
            UsbDeviceKvmBackend.StartUsbServerRsp rsp = new UsbDeviceKvmBackend.StartUsbServerRsp()
            port = port + 1
            rsp.port = port
            rsp.setSuccess(true)
            startUsbRedirect = true
            return rsp
        }


        UsbDeviceVO usb = Q.New(UsbDeviceVO.class).limit(1).find()
        VmInstanceInventory vm1 = createVmInstance {
            name = "vm"
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
            instanceOfferingUuid = instanceOffering.uuid
            hostUuid = kvm1.uuid
            systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usb.uuid, UsbAttachType.PassThrough)]
        }

        usb = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, usb.uuid).find()
        assert usb.getVmInstanceUuid() == vm1.uuid
        assert usb.attachType.equals(UsbAttachType.PassThrough.toString())

        removeVm(vm1.uuid)

        usb = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, usb.uuid).find()
        assert usb.getVmInstanceUuid() == null
        assert usb.attachType == null

        vm1 = createVmInstance {
            name = "vm"
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
            instanceOfferingUuid = instanceOffering.uuid
            hostUuid = kvm1.uuid
            systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usb.uuid, UsbAttachType.Redirect)]
        }

        assert UsbSystemTags.USB_REDIRECT_PORT.hasTag(usb.uuid)
        usb = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, usb.uuid).find()
        assert usb.getVmInstanceUuid() == vm1.uuid
        assert usb.attachType.equals(UsbAttachType.Redirect.toString())
        assert startUsbRedirect

        removeVm(vm1.uuid)

        usb = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, usb.uuid).find()
        assert usb.getVmInstanceUuid() == null
        assert usb.attachType == null
        assert !UsbSystemTags.USB_REDIRECT_PORT.hasTag(usb.uuid)

        List<UsbDeviceVO> usbDeviceVOS = Q.New(UsbDeviceVO.class).like(UsbDeviceVO_.usbVersion, "3%").limit(3).list()
        startUsbRedirect = false
        //multiple usb
        vm1 = createVmInstance {
            name = "vm"
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
            instanceOfferingUuid = instanceOffering.uuid
            hostUuid = kvm1.uuid
            systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(0).uuid, UsbAttachType.Redirect),
                          String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(1).uuid, UsbAttachType.PassThrough),
                          String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(2).uuid, UsbAttachType.Redirect)]
        }

        assert Q.New(UsbDeviceVO.class)
                .eq(UsbDeviceVO_.uuid, usbDeviceVOS.get(0).getUuid())
                .eq(UsbDeviceVO_.vmInstanceUuid, vm1.uuid)
                .eq(UsbDeviceVO_.attachType, UsbAttachType.Redirect.toString())
                .isExists()

        assert Q.New(UsbDeviceVO.class)
                .eq(UsbDeviceVO_.uuid, usbDeviceVOS.get(1).getUuid())
                .eq(UsbDeviceVO_.vmInstanceUuid, vm1.uuid)
                .eq(UsbDeviceVO_.attachType, UsbAttachType.PassThrough.toString())
                .isExists()

        assert Q.New(UsbDeviceVO.class)
                .eq(UsbDeviceVO_.uuid, usbDeviceVOS.get(2).getUuid())
                .eq(UsbDeviceVO_.vmInstanceUuid, vm1.uuid)
                .eq(UsbDeviceVO_.attachType, UsbAttachType.Redirect.toString())
                .isExists()
        assert !UsbSystemTags.USB_REDIRECT_PORT.hasTag(usbDeviceVOS.get(1).uuid)
        assert UsbSystemTags.USB_REDIRECT_PORT.hasTag(usbDeviceVOS.get(0).uuid)
        assert UsbSystemTags.USB_REDIRECT_PORT.hasTag(usbDeviceVOS.get(2).uuid)

        assert startUsbRedirect
        removeVm(vm1.uuid)
        usb = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, usbDeviceVOS.get(0).getUuid()).find()
        assert usb.getVmInstanceUuid() == null
        assert usb.attachType == null
        assert !UsbSystemTags.USB_REDIRECT_PORT.hasTag(usbDeviceVOS.get(0).uuid)

        usb = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, usbDeviceVOS.get(1).getUuid()).find()
        assert usb.getVmInstanceUuid() == null
        assert usb.attachType == null

        usb = Q.New(UsbDeviceVO.class).eq(UsbDeviceVO_.uuid, usbDeviceVOS.get(2).getUuid()).find()
        assert usb.getVmInstanceUuid() == null
        assert usb.attachType == null
        assert !UsbSystemTags.USB_REDIRECT_PORT.hasTag(usbDeviceVOS.get(2).uuid)

        //multiple usb error
        usbDeviceVOS = Q.New(UsbDeviceVO.class).like(UsbDeviceVO_.usbVersion, "1%").limit(2).list()
        expect(AssertionError.class) {
            createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                instanceOfferingUuid = instanceOffering.uuid
                hostUuid = kvm1.uuid
                systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(1).uuid, UsbAttachType.PassThrough)]
            }
        }

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()


        usbDeviceVOS = Q.New(UsbDeviceVO.class).like(UsbDeviceVO_.usbVersion, "2%").limit(7).list()
        expect(AssertionError.class) {
            createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                instanceOfferingUuid = instanceOffering.uuid
                hostUuid = kvm1.uuid
                systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(1).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(2).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(3).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(4).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(5).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(6).uuid, UsbAttachType.Redirect)]
            }
        }

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()

        usbDeviceVOS = Q.New(UsbDeviceVO.class).like(UsbDeviceVO_.usbVersion, "3%").limit(5).list()
        expect(AssertionError.class) {
            createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                instanceOfferingUuid = instanceOffering.uuid
                hostUuid = kvm1.uuid
                systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(1).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(2).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(3).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usbDeviceVOS.get(4).uuid, UsbAttachType.Redirect)]
            }
        }

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()

        List<UsbDeviceVO> usb1VOS = Q.New(UsbDeviceVO.class).like(UsbDeviceVO_.usbVersion, "1%").limit(2).list()
        List<UsbDeviceVO> usb2VOS = Q.New(UsbDeviceVO.class).like(UsbDeviceVO_.usbVersion, "2%").limit(7).list()
        List<UsbDeviceVO> usb3VOS = Q.New(UsbDeviceVO.class).like(UsbDeviceVO_.usbVersion, "3%").limit(5).list()

        expect(AssertionError.class) {
            createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                instanceOfferingUuid = instanceOffering.uuid
                hostUuid = kvm1.uuid
                systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usb1VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb1VOS.get(1).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(3).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(4).uuid, UsbAttachType.Redirect)]
            }
        }

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()

        vm1 = createVmInstance {
            name = "vm"
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
            instanceOfferingUuid = instanceOffering.uuid
            hostUuid = kvm1.uuid
            systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usb1VOS.get(0).uuid, UsbAttachType.Redirect),
                          String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(0).uuid, UsbAttachType.PassThrough),
                          String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(3).uuid, UsbAttachType.Redirect),
                          String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(4).uuid, UsbAttachType.Redirect)]
        }

        removeVm(vm1.uuid)

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()

        expect(AssertionError.class) {
            createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                instanceOfferingUuid = instanceOffering.uuid
                hostUuid = kvm1.uuid
                systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usb1VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(1).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(3).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(4).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(5).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(6).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(2).uuid, UsbAttachType.Redirect)]
            }
        }

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()

        expect(AssertionError.class) {
            createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                instanceOfferingUuid = instanceOffering.uuid
                hostUuid = kvm1.uuid
                systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usb1VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(1).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(1).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(2).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(3).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(4).uuid, UsbAttachType.Redirect)]
            }
        }

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()


        expect(AssertionError.class) {
            createVmInstance {
                name = "vm"
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                instanceOfferingUuid = instanceOffering.uuid
                hostUuid = kvm1.uuid
                systemTags = [String.format("usbDeviceUuid::%s::attachType::%s", usb1VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(1).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb2VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(0).uuid, UsbAttachType.Redirect),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(1).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(2).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(3).uuid, UsbAttachType.PassThrough),
                              String.format("usbDeviceUuid::%s::attachType::%s", usb3VOS.get(4).uuid, UsbAttachType.Redirect)]
            }
        }

        assert !Q.New(UsbDeviceVO.class)
                .notEq(UsbDeviceVO_.vmInstanceUuid, null)
                .isExists()
    }

    void removeVm (String vmUuid) {
        destroyVmInstance {
            uuid = vmUuid
        }

        expungeVmInstance {
            uuid = vmUuid
        }
    }


    void initEnv() {
        env.simulator(UsbDeviceKvmBackend.GET_USB_DEVICES_PATH) { HttpEntity<String> entity, EnvSpec spec ->
            UsbDeviceKvmBackend.GetUsbDevicesRsp rsp = new UsbDeviceKvmBackend.GetUsbDevicesRsp()
            rsp.usbDevicesInfo = UsbDeviceTestConstant.usbDevicesInfo
            rsp.setSuccess(true)
            return rsp
        }


        List<KVMHostInventory> result = queryHost {}
        for (KVMHostInventory host : result) {
            reconnectHost {
                delegate.uuid = host.uuid
            }
        }
        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
        }
    }



    @Override
    void clean() {
        env.delete()
    }
}
