package org.zstack.test.integration.premium.storage.volume

import org.zstack.core.db.Q
import org.zstack.header.configuration.DiskOfferingVO
import org.zstack.header.configuration.DiskOfferingVO_
import org.zstack.header.volume.VolumeVO
import org.zstack.sdk.CreateDiskOfferingAction
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.GetVolumeQosResult
import org.zstack.sdk.VolumeInventory
import org.zstack.storage.volume.MevocoVolumeSystemTags
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
/**
 * Created by mingjian.deng on 2017/9/20.
 */
class DiskOfferingQosCase extends PremiumSubCase {
    EnvSpec env
    @Override
    void environment() {
        env = env {
            diskOffering {
                name = "qos-offering"
                description = "qos-desc"
                diskSize = SizeUnit.GIGABYTE.toByte(2)
                systemTags = [
                        "volumeTotalBandwidth::********",
                        "volumeReadIops::23333"
                ]
            }
        }
    }

    @Override
    void test() {
        env.create {
            testCreateOfferingWithQos()
            testInvalidQos()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    void testCreateOfferingWithQos() {
        def offering = env.inventoryByName("qos-offering") as DiskOfferingInventory
        assert MevocoVolumeSystemTags.VOLUME_TOTAL_BANDWIDTH.getTag(offering.uuid).contains("volumeTotalBandwidth::********")
        assert MevocoVolumeSystemTags.VOLUME_READ_IOPS.getTag(offering.uuid).contains("volumeReadIops::23333")

        def volume = createDataVolume {
            name = "disk-qos"
            diskOfferingUuid = offering.uuid
        } as VolumeInventory

        assert MevocoVolumeSystemTags.NORMAL_ACCOUNT_VOLUME_BANDWIDTH_UP_THRESHOLD.getTokenByResourceUuid(volume.uuid, VolumeVO.class, MevocoVolumeSystemTags.VOLUME_TOTAL_BANDWIDTH_TOKEN) == "********"
        assert MevocoVolumeSystemTags.NORMAL_ACCOUNT_VOLUME_IOPS_UP_THRESHOLD.getTokenByResourceUuid(volume.uuid, VolumeVO.class, MevocoVolumeSystemTags.VOLUME_READ_IOPS_TOKEN) == "23333"

        def qos = getVolumeQos {
            uuid = volume.uuid
        } as GetVolumeQosResult

        assert qos.volumeBandwidth == ********
        assert qos.iopsRead == 23333
    }

    void testInvalidQos() {
        def action = new CreateDiskOfferingAction()
        action.name = "qos-offering-1"
        action.description = "qos-desc-1"
        action.diskSize = SizeUnit.GIGABYTE.toByte(2)
        action.systemTags = ["volumeTotalBandwidth::-********"]
        action.sessionId = adminSession()

        assert action.call().error != null
        assert Q.New(DiskOfferingVO.class).eq(DiskOfferingVO_.name, "qos-offering-1").count() == 0


        def val = Long.MAX_VALUE
        def action2 = new CreateDiskOfferingAction()
        action2.name = "qos-offering-1"
        action2.description = "qos-desc-1"
        action2.diskSize = SizeUnit.GIGABYTE.toByte(2)
        action2.systemTags = ["volumeTotalBandwidth::" + val.toString() + "1"]
        action2.sessionId = adminSession()

        CreateDiskOfferingAction.Result ret = action2.call()
        assert ret.error != null
        // can't checkout concrete content of error message
        // assert ret.error.details.contains("is larger than")
        assert Q.New(DiskOfferingVO.class).eq(DiskOfferingVO_.name, "qos-offering-1").count() == 0

        action = new CreateDiskOfferingAction()
        action.name = "qos-offering-1"
        action.description = "qos-desc-1"
        action.diskSize = SizeUnit.GIGABYTE.toByte(2)
        action.systemTags = ["volumeTotalIops::-********"]
        action.sessionId = adminSession()

        assert action.call().error != null
        assert Q.New(DiskOfferingVO.class).eq(DiskOfferingVO_.name, "qos-offering-1").count() == 0
    }
}
