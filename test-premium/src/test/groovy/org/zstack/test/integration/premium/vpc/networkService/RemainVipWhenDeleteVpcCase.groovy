package org.zstack.test.integration.premium.vpc.networkService

import org.springframework.http.HttpEntity
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.network.l3.L3NetworkConstant
import org.zstack.header.network.l3.UsedIpVO
import org.zstack.header.network.l3.UsedIpVO_
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vm.VmNicVO
import org.zstack.header.volume.VolumeStatus
import org.zstack.header.volume.VolumeVO_
import org.zstack.ipsec.IPsecConstants
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.lb.LoadBalancerConstants
import org.zstack.network.service.lb.LoadBalancerListenerVO
import org.zstack.network.service.lb.LoadBalancerListenerVO_
import org.zstack.network.service.lb.LoadBalancerServerGroupVmNicRefVO
import org.zstack.network.service.lb.LoadBalancerServerGroupVmNicRefVO_
import org.zstack.network.service.lb.LoadBalancerVO
import org.zstack.network.service.lb.LoadBalancerVO_
import org.zstack.network.service.portforwarding.PortForwardingConstant
import org.zstack.network.service.portforwarding.PortForwardingProtocolType
import org.zstack.network.service.portforwarding.PortForwardingRuleVO
import org.zstack.network.service.portforwarding.PortForwardingRuleVO_
import org.zstack.network.service.userdata.UserdataConstant
import org.zstack.network.service.vip.VipNetworkServicesRefVO
import org.zstack.network.service.vip.VipNetworkServicesRefVO_
import org.zstack.network.service.vip.VipVO
import org.zstack.network.service.vip.VipVO_
import org.zstack.network.service.virtualrouter.VirtualRouterCommands
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend
import org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerRefVO
import org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerRefVO_
import org.zstack.network.service.virtualrouter.portforwarding.VirtualRouterPortForwardingRuleRefVO
import org.zstack.network.service.virtualrouter.portforwarding.VirtualRouterPortForwardingRuleRefVO_
import org.zstack.network.service.virtualrouter.vip.VirtualRouterVipVO
import org.zstack.network.service.virtualrouter.vip.VirtualRouterVipVO_
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.TestPremium
import org.zstack.utils.data.SizeUnit
import org.zstack.header.vpc.VpcConstants
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.vrouterRoute.VRouterRouteConstants
import org.zstack.ipsec.IPsecConnectionVO
import org.zstack.ipsec.IPsecConnectionVO_
import org.zstack.ipsec.vyos.VyosIPsecBackend

import java.util.stream.Collectors

import static java.util.Arrays.asList

class RemainVipWhenDeleteVpcCase extends PremiumSubCase{
    EnvSpec env
    DatabaseFacade dbf
    VirtualRouterVmInventory vpcVr1
    VmInstanceInventory vpcVm1
    VmInstanceInventory vpcVm2

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = TestPremium.makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3-1"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-2"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-3"
                        type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE.toString()

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "************0"
                            endIp = "************00"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-4"
                        type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE.toString()

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-5"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "pubL3-1"
                        category = "Public"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        ip {
                            startIp = "***********"
                            endIp = "***********0"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }

                    l3Network {
                        name = "pubL3-2"
                        category = "Public"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr-offering"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3-1")
                    usePublicL3Network("pubL3-1")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }
        }
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            initEnv()
            testRemainVipWhenDeleteVpc()
            initEnv()
            testDeleteVipWhenDeleteVpc()
        }
    }

    void initEnv() {
        L3NetworkInventory pubL3_1 = env.inventoryByName("pubL3-1")
        L3NetworkInventory pubL3_2 = env.inventoryByName("pubL3-2")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")
        InstanceOfferingInventory offering = env.inventoryByName("vr-offering")
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        def image = env.inventoryByName("image1") as ImageInventory

        vpcVr1 = createVpcVRouter {
            delegate.name = "vpc-vr-1"
            delegate.virtualRouterOfferingUuid = offering.uuid
        }

        //attach pubL3_2
        attachL3NetworkToVm {
            l3NetworkUuid = pubL3_2.uuid
            vmInstanceUuid = vpcVr1.uuid
        }

        //attach l3_1
        attachL3NetworkToVm {
            delegate.l3NetworkUuid = l3_1.uuid
            delegate.vmInstanceUuid = vpcVr1.uuid
        }

        //attach l3_2
        attachL3NetworkToVm {
            delegate.l3NetworkUuid = l3_2.uuid
            delegate.vmInstanceUuid = vpcVr1.uuid
        }

        vpcVr1 = queryVirtualRouterVm {
            conditions=["uuid=${vpcVr1.uuid}"]
        }[0] as VirtualRouterVmInventory

        vpcVm1 = createVmInstance {
            name = "TestVm1"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3_1.uuid, l3_2.uuid]
            defaultL3NetworkUuid = l3_1.uuid
        }

        vpcVm2 = createVmInstance {
            name = "TestVm2"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3_1.uuid, l3_2.uuid]
            defaultL3NetworkUuid = l3_1.uuid
        }
    }

    void testRemainVipWhenDeleteVpc(){
        L3NetworkInventory pubL3_1 = env.inventoryByName("pubL3-1")
        L3NetworkInventory pubL3_2 = env.inventoryByName("pubL3-2")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")

        setVpcVRouterNetworkServiceState {
            uuid = vpcVr1.uuid
            networkService = NetworkServiceType.SNAT.toString()
            state = "enable"
        }
        def result = getVpcVRouterNetworkServiceState {
            uuid = vpcVr1.uuid
            networkService = NetworkServiceType.SNAT.toString()
        } as GetVpcVRouterNetworkServiceStateResult

        assert result.state == "enable"

        VmNicInventory publicNic1, publicNic2
        for (VmNicInventory nic : vpcVr1.getVmNics()) {
            if (nic.l3NetworkUuid == pubL3_1.uuid) {
                publicNic1 = nic
                continue
            }
            if (nic.l3NetworkUuid == pubL3_2.uuid) {
                publicNic2 = nic
                continue
            }
        }
        VipInventory systemVip1 = queryVip {delegate.conditions = ["ip=${publicNic1.ip}"]}[0]
        VipInventory systemVip2 = queryVip {delegate.conditions = ["ip=${publicNic2.ip}"]}[0]

        def lbSystem1 = createLoadBalancer {
            name = "TestLoadBalancer-system-1"
            vipUuid = systemVip1.uuid
        } as LoadBalancerInventory
        def lbSystem2 = createLoadBalancer {
            name = "TestLoadBalancer-system-2"
            vipUuid = systemVip2.uuid
        } as LoadBalancerInventory

        LoadBalancerListenerInventory lbl1 = createLoadBalancerListener {
            loadBalancerUuid = lbSystem1.uuid
            name = "listener"
            instancePort = 222
            loadBalancerPort = 222
            protocol = LoadBalancerConstants.LB_PROTOCOL_TCP
        }
        LoadBalancerListenerInventory lbl2 = createLoadBalancerListener {
            loadBalancerUuid = lbSystem2.uuid
            name = "listener"
            instancePort = 222
            loadBalancerPort = 222
            protocol = LoadBalancerConstants.LB_PROTOCOL_TCP
        }

        addVmNicToLoadBalancer {
            vmNicUuids = [vpcVm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid]
            listenerUuid = lbl1.uuid
        }
        addVmNicToLoadBalancer {
            vmNicUuids = [vpcVm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_2.uuid }.uuid]
            listenerUuid = lbl2.uuid
        }

        destroyVmInstance {
            uuid = vpcVr1.uuid
        }

        assert Q.New(LoadBalancerVO.class).eq(LoadBalancerVO_.uuid, lbSystem1.uuid).isExists()
        assert Q.New(LoadBalancerVO.class).eq(LoadBalancerVO_.uuid, lbSystem2.uuid).isExists()
        assert Q.New(LoadBalancerListenerVO.class).eq(LoadBalancerListenerVO_.uuid, lbl1.uuid).isExists()
        assert Q.New(LoadBalancerListenerVO.class).eq(LoadBalancerListenerVO_.uuid, lbl2.uuid).isExists()
        assert !Q.New(VirtualRouterVipVO.class).eq(VirtualRouterVipVO_.virtualRouterVmUuid, vpcVr1.uuid).isExists()
        assert Q.New(VipVO.class).eq(VipVO_.system, false).eq(VipVO_.uuid, systemVip1.uuid).isExists()
        assert Q.New(VipVO.class).eq(VipVO_.system, false).eq(VipVO_.uuid, systemVip2.uuid).isExists()
        assert !Q.New(VipVO.class).eq(VipVO_.uuid, systemVip1.uuid).find().getServicesTypes().contains(NetworkServiceType.SNAT.toString())
        assert !Q.New(VipVO.class).eq(VipVO_.uuid, systemVip2.uuid).find().getServicesTypes().contains(NetworkServiceType.SNAT.toString())
        assert Q.New(LoadBalancerServerGroupVmNicRefVO.class).eq(LoadBalancerServerGroupVmNicRefVO_.vmNicUuid, vpcVm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid)
        assert Q.New(LoadBalancerServerGroupVmNicRefVO.class).eq(LoadBalancerServerGroupVmNicRefVO_.vmNicUuid, vpcVm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_2.uuid }.uuid)
        assert !Q.New(UsedIpVO.class).eq(UsedIpVO_.uuid, Q.New(VipVO.class).eq(VipVO_.uuid, systemVip1.uuid).find().getUsedIpUuid()).isExists()

        //Clean Env
        destroyVmInstance {
            uuid = vpcVm1.uuid
        }
        destroyVmInstance {
            uuid = vpcVm2.uuid
        }
        deleteLoadBalancer {
            uuid = lbSystem1.uuid
        }
        deleteLoadBalancer {
            uuid = lbSystem2.uuid
        }
        deleteVip {
            uuid = systemVip1.uuid
        }
        deleteVip {
            uuid = systemVip2.uuid
        }
    }

    void testDeleteVipWhenDeleteVpc(){
        L3NetworkInventory pubL3_1 = env.inventoryByName("pubL3-1")
        L3NetworkInventory pubL3_2 = env.inventoryByName("pubL3-2")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")

        setVpcVRouterNetworkServiceState {
            uuid = vpcVr1.uuid
            networkService = NetworkServiceType.SNAT.toString()
            state = "enable"
        }
        def result = getVpcVRouterNetworkServiceState {
            uuid = vpcVr1.uuid
            networkService = NetworkServiceType.SNAT.toString()
        } as GetVpcVRouterNetworkServiceStateResult

        assert result.state == "enable"

        VmNicInventory publicNic1, publicNic2
        for (VmNicInventory nic : vpcVr1.getVmNics()) {
            if (nic.l3NetworkUuid == pubL3_1.uuid) {
                publicNic1 = nic
                continue
            }
            if (nic.l3NetworkUuid == pubL3_2.uuid) {
                publicNic2 = nic
                continue
            }
        }
        VipInventory systemVip1 = queryVip {delegate.conditions = ["ip=${publicNic1.ip}"]}[0]
        VipInventory systemVip2 = queryVip {delegate.conditions = ["ip=${publicNic2.ip}"]}[0]
        assert Q.New(VipVO.class).eq(VipVO_.system, true).eq(VipVO_.uuid, systemVip1.uuid).isExists()
        assert Q.New(VipVO.class).eq(VipVO_.system, true).eq(VipVO_.uuid, systemVip2.uuid).isExists()
        assert Q.New(VipVO.class).eq(VipVO_.uuid, systemVip1.uuid).find().getServicesTypes().contains(NetworkServiceType.SNAT.toString())
        assert !Q.New(VipVO.class).eq(VipVO_.uuid, systemVip2.uuid).find().getServicesTypes().contains(NetworkServiceType.SNAT.toString())

        destroyVmInstance {
            uuid = vpcVr1.uuid
        }

        assert !Q.New(VirtualRouterVipVO.class).eq(VirtualRouterVipVO_.virtualRouterVmUuid, vpcVr1.uuid).isExists()
        assert !Q.New(VipVO.class).eq(VipVO_.uuid, systemVip1.uuid).isExists();
        assert !Q.New(VipVO.class).eq(VipVO_.uuid, systemVip2.uuid).isExists();

        //Clean Env
        destroyVmInstance {
            uuid = vpcVm1.uuid
        }
        destroyVmInstance {
            uuid = vpcVm2.uuid
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}

