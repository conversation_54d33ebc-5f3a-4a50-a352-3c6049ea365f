package org.zstack.test.integration.premium.storage.primary.ceph

import org.springframework.http.HttpEntity
import org.springframework.web.util.UriComponentsBuilder
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.host.HostStatus
import org.zstack.header.host.HostVO
import org.zstack.header.host.HostVO_
import org.zstack.header.rest.RESTConstant
import org.zstack.header.rest.RESTFacade
import org.zstack.header.storage.primary.PrimaryStorageHostRefVO
import org.zstack.header.storage.primary.PrimaryStorageHostRefVO_
import org.zstack.header.storage.primary.PrimaryStorageHostStatus
import org.zstack.header.storage.primary.UpdatePrimaryStorageHostStatusMsg
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.volume.VolumeStatus
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.sdk.HostInventory
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VolumeInventory
import org.zstack.storage.ceph.CephGlobalConfig
import org.zstack.storage.ceph.primary.CephPrimaryStorageBase
import org.zstack.storage.ceph.primary.CephPrimaryStorageMonBase
import org.zstack.storage.primary.PrimaryStorageGlobalConfig
import org.zstack.storage.primary.local.LocalStorageKvmBackend
import org.zstack.storage.volume.VolumeSystemTags
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.HttpError
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.SizeUtils
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

import java.util.concurrent.atomic.AtomicInteger

class CephPrimaryStorageHostConnectionStatusCase extends PremiumSubCase {
    EnvSpec env

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = PremiumEnv.oneHostTwoVmsNoVrCephEnv()
    }

    @Override
    void test() {
        env.create {
            testCephPsMonPingFail()
            testPSAndHostConnectionRef()
            testAddNewCephPsConnectionRef()
            testCheckCephStatusFailWontCreateRef()
            testAttachVolumeFailureWhenHostNotConnectedWithStorage()
            testStorageFailureRefCreation()
        }
    }

    void testAttachVolumeFailureWhenHostNotConnectedWithStorage() {
        def zone = env.inventoryByName("zone")
        def cluster = env.inventoryByName("cluster")
        def vm = env.inventoryByName("vm1")
        def targetHostUuid = vm.hostUuid
        def ceph = env.inventoryByName("ceph") as PrimaryStorageInventory

        env.afterSimulator(LocalStorageKvmBackend.INIT_PATH) { rsp, HttpEntity<String> e ->
            rsp = new LocalStorageKvmBackend.InitRsp()
            rsp.totalCapacity = SizeUtils.sizeStringToBytes("100G")
            rsp.availableCapacity = SizeUtils.sizeStringToBytes("90G")
            rsp.localStorageUsedCapacity = 0
            return rsp
        }

        // test attach volume to vm which runs on the storage disconnected host
        // expect: failure
        VolumeInventory dataVolume1 = createDataVolume {
            diskSize = SizeUnit.MEGABYTE.toByte(1)
            primaryStorageUuid = ceph.uuid
            name = "data-volume"
        } as VolumeInventory

        VolumeInventory dataVolume2 = createDataVolume {
            diskSize = SizeUnit.MEGABYTE.toByte(1)
            name = "data-volume"
            systemTags = [VolumeSystemTags.SHAREABLE.getTagFormat()]
        } as VolumeInventory

        def ps = addLocalPrimaryStorage {
            name = "ls2"
            url = "/localPs"
            zoneUuid = zone.uuid
        }

        attachPrimaryStorageToCluster {
            clusterUuid = cluster.uuid
            primaryStorageUuid = ps.uuid
        }

        startVmInstance {
            uuid = vm.uuid
            hostUuid = targetHostUuid
        }

        env.afterSimulator(CephPrimaryStorageBase.CHECK_HOST_STORAGE_CONNECTION_PATH) { rsp, HttpEntity<String> e ->
            CephPrimaryStorageBase.CheckHostStorageConnectionCmd cmd = JSONObjectUtil.toObject(e.body, CephPrimaryStorageBase.CheckHostStorageConnectionCmd)

            if (cmd.hostUuid == targetHostUuid) {
                throw new HttpError(404, "on purpose")
            }

            return new KVMAgentCommands.AgentResponse()
        }

        reconnectHost {
            uuid = targetHostUuid
        }

        retryInSecs {
            assert Q.New(PrimaryStorageHostRefVO.class)
                    .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                    .eq(PrimaryStorageHostRefVO_.hostUuid, targetHostUuid)
                    .eq(PrimaryStorageHostRefVO_.status, PrimaryStorageHostStatus.Disconnected)
                    .count() == 1
        }

        assert dataVolume1.status == VolumeStatus.Ready.toString()
        checkAttachVolumeToVm(dataVolume1, vm)

        assert dataVolume2.status == VolumeStatus.NotInstantiated.toString()
        // test attach volume to vm which runs on the storage disconnected host
        checkAttachVolumeToVm(dataVolume2, vm)
    }

    void checkAttachVolumeToVm(VolumeInventory dataVolume, VmInstanceInventory vm) {
        // test attach volume to vm which runs on the storage disconnected host
        expect(AssertionError.class) {
            attachDataVolumeToVm {
                vmInstanceUuid = vm.uuid
                volumeUuid = dataVolume.uuid
            }
        }

        stopVmInstance {
            uuid = vm.uuid
        }

        // for stopped vm instance, attach volume should be ok
        attachDataVolumeToVm {
            vmInstanceUuid = vm.uuid
            volumeUuid = dataVolume.uuid
        }

        startVmInstance {
            uuid = vm.uuid
        }
    }

    void testCephPsMonPingFail() {
        def ceph = env.inventoryByName("ceph") as PrimaryStorageInventory

        def pingFailedCount = 0
        env.simulator(CephPrimaryStorageMonBase.PING_PATH) { HttpEntity<String> e ->
            CephPrimaryStorageMonBase.PingRsp rsp = new CephPrimaryStorageMonBase.PingRsp()
            def cmd = JSONObjectUtil.toObject(e.body, CephPrimaryStorageMonBase.PingCmd.class)
            if (cmd.primaryStorageUuid.equals(ceph.uuid)) {
                pingFailedCount++
                throw new HttpError(503, "on purpose")
            }
            return rsp
        }

        CephGlobalConfig.SLEEP_TIME_AFTER_PING_FAILURE.updateValue(0)
        PrimaryStorageGlobalConfig.PING_INTERVAL.updateValue(2)

        retryInSecs(4) {
            assert pingFailedCount == 3
        }

        env.cleanSimulatorAndMessageHandlers()
    }

    void testCheckCephStatusFailWontCreateRef() {
        def vm = env.inventoryByName("vm1")
        def hostUuid = vm.hostUuid
        def ceph = env.inventoryByName("ceph") as PrimaryStorageInventory

        assert Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .isExists()
        // delete all refs to test concurrently issue
        SQL.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .hardDelete()

        env.afterSimulator(CephPrimaryStorageBase.CHECK_HOST_STORAGE_CONNECTION_PATH) { rsp, HttpEntity<String> e ->
            CephPrimaryStorageBase.CheckHostStorageConnectionCmd cmd = JSONObjectUtil.toObject(e.body, CephPrimaryStorageBase.CheckHostStorageConnectionCmd)

            if (cmd.hostUuid == hostUuid) {
                throw new HttpError(404, "on purpose")
            }

            return new KVMAgentCommands.AgentResponse()
        }

        AtomicInteger counter = new AtomicInteger(0)
        def cleanup = notifyWhenReceivedMessage(UpdatePrimaryStorageHostStatusMsg.class) { UpdatePrimaryStorageHostStatusMsg umsg ->
            counter.addAndGet(1)
        }

        List<HostInventory> hosts = queryHost {}
        hosts.each { hostInv ->
            if (hostInv.uuid == hostUuid) {
                expect(AssertionError.class) {
                    reconnectHost {
                        uuid = hostInv.uuid
                    }
                }
            } else {
                reconnectHost {
                    uuid = hostInv.uuid
                }
            }
        }

        retryInSecs {
            assert Q.New(PrimaryStorageHostRefVO.class)
                    .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                    .count() == 2
        }

        assert Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .eq(PrimaryStorageHostRefVO_.status, PrimaryStorageHostStatus.Disconnected)
                .count() == 1
        assert Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .eq(PrimaryStorageHostRefVO_.hostUuid, hostUuid)
                .count() == 1
        assert counter.get() == 2

        env.cleanAfterSimulatorHandlers()
        reconnectHost {
            uuid = hostUuid
        }

        // confirm host connected
        retryInSecs {
            assert Q.New(HostVO.class)
                    .eq(HostVO_.uuid, hostUuid)
                    .eq(HostVO_.status, HostStatus.Connected)
                    .count() == 1
        }

        reconnectPrimaryStorage {
            uuid = ceph.uuid
        }

        cleanup()
    }

    void testStorageFailureRefCreation() {
        def host = env.inventoryByName("kvm")
        def ceph = env.inventoryByName("ceph")

        assert Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .isExists()
        // delete all refs to test concurrently issue
        SQL.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .hardDelete()
        assert !Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .isExists()

        RESTFacade restf = bean(RESTFacade.class)

        UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(restf.getBaseUrl())
        ub.path(RESTConstant.COMMAND_CHANNEL_PATH)
        String url = ub.build().toUriString()

        def header = [(RESTConstant.COMMAND_PATH): KVMConstant.KVM_REPORT_PS_STATUS]
        def cmd = new KVMAgentCommands.ReportPsStatusCmd()
        cmd.hostUuid = host.uuid
        cmd.psUuids = [ceph.uuid]
        cmd.psStatus = PrimaryStorageHostStatus.Disconnected.toString()

        AtomicInteger counter = new AtomicInteger(0)
        def cleanup = notifyWhenReceivedMessage(UpdatePrimaryStorageHostStatusMsg.class) { msg ->
            counter.addAndGet(1)
        }

        def threads = []
        for (int i = 0; i < 20; i++) {
            def thread = Thread.start {
                restf.syncJsonPost(url, JSONObjectUtil.toJsonString(cmd), header, KVMAgentCommands.AgentResponse)
            }
            threads.add(thread)
        }
        threads.each { it.join() }

        retryInSecs {
            assert counter.get() < 20
        }

        assert Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .eq(PrimaryStorageHostRefVO_.hostUuid, host.uuid)
                .count() == 1

        cleanup()
    }

    void testAddNewCephPsConnectionRef() {
        def cluster = env.inventoryByName("cluster")
        def ceph = env.inventoryByName("ceph")

        detachPrimaryStorageFromCluster {
            primaryStorageUuid = ceph.uuid
            clusterUuid = cluster.uuid
        }

        assert Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .isExists()

        // delete all refs to test new attached ceph
        SQL.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .hardDelete()
        assert !Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, ceph.uuid)
                .isExists()

        attachPrimaryStorageToCluster {
            primaryStorageUuid = ceph.uuid
            clusterUuid = cluster.uuid
        }

        List<String> hostUuids = Q.New(HostVO.class).select(HostVO_.uuid).listValues()

        for (String hostUuid : hostUuids) {
            assert Q.New(PrimaryStorageHostRefVO.class)
                    .eq(PrimaryStorageHostRefVO_.hostUuid, hostUuid)
                    .eq(PrimaryStorageHostRefVO_.status, PrimaryStorageHostStatus.Connected)
                    .isExists()
        }
    }

    void testPSAndHostConnectionRef() {
        def cluster = env.inventoryByName("cluster")

        List<String> hostUuids = Q.New(HostVO.class).select(HostVO_.uuid).listValues()

        for (String hostUuid : hostUuids) {
            assert Q.New(PrimaryStorageHostRefVO.class)
                    .eq(PrimaryStorageHostRefVO_.hostUuid, hostUuid)
                    .eq(PrimaryStorageHostRefVO_.status, PrimaryStorageHostStatus.Connected)
                    .isExists()
        }

        def host = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm2"
            managementIp = "127.0.0.199"
            clusterUuid = cluster.uuid
        } as HostInventory

        assert Q.New(PrimaryStorageHostRefVO.class)
                .eq(PrimaryStorageHostRefVO_.hostUuid, host.uuid)
                .eq(PrimaryStorageHostRefVO_.status, PrimaryStorageHostStatus.Connected)
                .isExists()
    }

    @Override
    void clean() {
        env.delete()
    }
}
