package org.zstack.test.integration.zwatch

import com.google.common.collect.EvictingQueue
import io.prometheus.client.Collector
import io.prometheus.client.CollectorRegistry
import org.springframework.http.HttpEntity
import org.zstack.core.Platform
import org.zstack.core.cloudbus.EventFacade
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.core.db.SimpleQuery
import org.zstack.header.storage.primary.PrimaryStorageCanonicalEvent
import org.zstack.header.storage.primary.PrimaryStorageCapacityVO
import org.zstack.header.storage.primary.PrimaryStorageCapacityVO_
import org.zstack.header.storage.primary.PrimaryStorageHistoricalUsageVO
import org.zstack.header.storage.primary.PrimaryStorageHistoricalUsageVO_
import org.zstack.sdk.ClusterInventory
import org.zstack.sdk.GetPrimaryStorageUsageReportResult
import org.zstack.sdk.GetResourceConfigResult
import org.zstack.sdk.GlobalConfigInventory
import org.zstack.sdk.HostInventory
import org.zstack.sdk.UsageReport
import org.zstack.storage.ceph.CephPoolCapacity
import org.zstack.storage.ceph.DataSecurityPolicy
import org.zstack.storage.ceph.primary.CephOsdGroupHistoricalUsageVO
import org.zstack.storage.ceph.primary.CephOsdGroupHistoricalUsageVO_
import org.zstack.storage.ceph.primary.CephPrimaryStorageBase
import org.zstack.storage.ceph.primary.CephPrimaryStoragePoolType
import org.zstack.storage.ceph.primary.CephPrimaryStoragePoolVO
import org.zstack.storage.ceph.primary.CephPrimaryStoragePoolVO_
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.zwatch.alarm.AlarmInventory
import org.zstack.sdk.zwatch.datatype.Label
import org.zstack.sdk.zwatch.datatype.Operator
import org.zstack.sdk.zwatch.ruleengine.ComparisonOperator
import org.zstack.storage.ceph.primary.CephPrimaryStorageVO
import org.zstack.storage.primary.PrimaryStorageGlobalConfig
import org.zstack.storage.primary.PrimaryStorageUsageReport
import org.zstack.storage.primary.local.LocalStorageHostRefVO
import org.zstack.storage.primary.local.LocalStorageHostHistoricalUsageVO
import org.zstack.storage.primary.local.LocalStorageHostHistoricalUsageVO_
import org.zstack.storage.primary.local.LocalStorageHostUsageReport

import org.zstack.testlib.CephPrimaryStorageSpec
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.controller.PrimaryStorageController
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.util.TProxy
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.zwatch.ZWatchGlobalConfig
import org.zstack.zwatch.alarm.AlarmVO
import org.zstack.zwatch.alarm.AlarmVO_
import org.zstack.zwatch.datatype.Datapoint
import org.zstack.zwatch.datatype.EmergencyLevel
import org.zstack.zwatch.datatype.MetricQueryObject
import org.zstack.zwatch.migratedb.AlarmRecordsVO
import org.zstack.zwatch.migratedb.AlarmRecordsVO_
import org.zstack.zwatch.namespace.CephPrimaryStoragePoolNamespace
import org.zstack.zwatch.namespace.LocalPrimaryStorageHostNamespace
import org.zstack.zwatch.namespace.PrimaryStorageNamespace
import org.zstack.zwatch.prometheus.CephPrimaryStoragePoolPrometheusNamespace
import org.zstack.zwatch.prometheus.LocalPrimaryStorageHostPrometheusNamespace
import org.zstack.zwatch.prometheus.PrimaryStoragePrometheusNamespace
import org.zstack.zwatch.prometheus.PrometheusDatabaseDriver
import org.zstack.zwatch.prometheus.PrometheusNamespace
import org.zstack.storage.ceph.primary.PoolUsageReport

import java.sql.Timestamp
import java.time.LocalDate
import java.util.concurrent.TimeUnit
import java.util.stream.IntStream


class PrimaryStorageDisconnectBarrierCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf
    PrometheusDatabaseDriver driver
    PrimaryStorageNamespace primaryStorageNamespace
    PrimaryStorageInventory nfs
    PrimaryStorageInventory local
    PrimaryStorageInventory ceph
    ClusterInventory cluster
    ClusterInventory cluster2
    HostInventory host21
    HostInventory host22
    PrimaryStorageUsageReport psReporter
    PoolUsageReport poolReporter
    LocalStorageHostUsageReport localStorageHostReporter
    CephPrimaryStoragePoolVO rootPool
    CephPrimaryStoragePoolVO dataPool
    CephPrimaryStoragePoolVO imageCachePool
    String BASE_URL = "/test-http-endpoint"

    double psThreshold = 0.85
    double cephThreshold = 0.84
    double localHostThreshold = 0.83
    long repeatCount

    @Override
    void setup() {
        useSpring(ZWatchTest.springSpec)
    }

    @Override
    void environment() {
        env = makeEnv {
            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(64)
                        totalCpu = 8
                    }

                    attachPrimaryStorage("nfs")
                }

                cluster {
                    name = "cluster2"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm21"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }
                    kvm {
                        name = "kvm22"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachPrimaryStorage("ceph")
                }

                nfsPrimaryStorage {
                    name = "nfs"
                    url = "127.0.0.1:/nfs"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100 * 1000)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(15 * 1000)
                }

                cephPrimaryStorage {
                    name = "ceph"
                    description = "Test"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100 * 1000)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(15 * 1000)
                    url = "ceph://pri"
                    fsid = "7ff218d9-f525-435f-8a40-3618d1772a65"
                    monUrls = ["root:password@**********/?monPort=7777"]
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100 * 1000)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(15 * 1000)
                }
            }

            sns {
                topic {
                    name = "topic"
                }

                httpEndpoint {
                    name = "http"
                    url = "http://127.0.0.1:8989${BASE_URL}"

                    subscribe("topic")
                }
            }
        }
    }

    void prepareEnv() {
        nfs = env.inventoryByName("nfs") as PrimaryStorageInventory
        local = env.inventoryByName("local") as PrimaryStorageInventory
        ceph = env.inventoryByName("ceph") as PrimaryStorageInventory
        cluster = env.inventoryByName("cluster") as ClusterInventory
        cluster2 = env.inventoryByName("cluster2") as ClusterInventory

        dbf = bean(DatabaseFacade.class)

        host21 = env.inventoryByName("kvm21") as HostInventory
        host22 = env.inventoryByName("kvm22") as HostInventory

        rootPool = Q.New(CephPrimaryStoragePoolVO.class).eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, ceph.uuid)
                .eq(CephPrimaryStoragePoolVO_.type, CephPrimaryStoragePoolType.Root.toString()).find()
        dataPool = Q.New(CephPrimaryStoragePoolVO.class).eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, ceph.uuid)
                .eq(CephPrimaryStoragePoolVO_.type, CephPrimaryStoragePoolType.Data.toString()).find()
        imageCachePool = Q.New(CephPrimaryStoragePoolVO.class).eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, ceph.uuid)
                .eq(CephPrimaryStoragePoolVO_.type, CephPrimaryStoragePoolType.ImageCache.toString()).find()


        psReporter = bean(PrimaryStorageUsageReport.class) as PrimaryStorageUsageReport
        poolReporter = bean(PoolUsageReport.class) as PoolUsageReport
        localStorageHostReporter = bean(LocalStorageHostUsageReport.class) as LocalStorageHostUsageReport

        // update ceph capacity
        env.simulator(CephPrimaryStorageBase.INIT_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, CephPrimaryStorageBase.InitCmd.class)
            CephPrimaryStorageSpec cspec = spec.specByUuid(cmd.uuid)

            def rsp = new CephPrimaryStorageBase.InitRsp()
            rsp.fsid = cspec.fsid
            rsp.userKey = Platform.uuid
            rsp.totalCapacity = SizeUnit.GIGABYTE.toByte(100 * 1000) * 3
            rsp.availableCapacity = SizeUnit.GIGABYTE.toByte(15 * 1000) * 3
            rsp.poolCapacities = [
                    new CephPoolCapacity(
                            name: rootPool.poolName,
                            usedCapacity: SizeUnit.GIGABYTE.toByte(85 * 1000),
                            availableCapacity: SizeUnit.GIGABYTE.toByte(15 * 1000),
                            totalCapacity: SizeUnit.GIGABYTE.toByte(100 * 1000),
                            relatedOsds: 'osd.1',
                            securityPolicy: DataSecurityPolicy.Copy.toString(),
                            replicatedSize: 3,
                            diskUtilization: 0.33
                    ),
                    new CephPoolCapacity(
                            name: dataPool.poolName,
                            usedCapacity: SizeUnit.GIGABYTE.toByte(85 * 1000),
                            availableCapacity: SizeUnit.GIGABYTE.toByte(15 * 1000),
                            totalCapacity: SizeUnit.GIGABYTE.toByte(100 * 1000),
                            relatedOsds: 'osd.2',
                            securityPolicy: DataSecurityPolicy.Copy.toString(),
                            replicatedSize: 3,
                            diskUtilization: 0.33
                    ),
                    new CephPoolCapacity(
                            name: imageCachePool.poolName,
                            usedCapacity: SizeUnit.GIGABYTE.toByte(85 * 1000),
                            availableCapacity: SizeUnit.GIGABYTE.toByte(15 * 1000),
                            totalCapacity: SizeUnit.GIGABYTE.toByte(100 * 1000),
                            relatedOsds: 'osd.3',
                            securityPolicy: DataSecurityPolicy.Copy.toString(),
                            replicatedSize: 3,
                            diskUtilization: 0.33
                    )
            ]
            return rsp
        }
        reconnectPrimaryStorage {
            uuid = ceph.uuid
        }

        driver = bean(PrometheusDatabaseDriver.class)
        primaryStorageNamespace = bean(PrimaryStorageNamespace.class)
        TProxy psProxy = new TProxy(driver).mockMethod("query") { MetricQueryObject queryObject ->
            List<Datapoint> datas = []
            if (PrimaryStorageNamespace.TimeDurationRequiredForPrimaryStorageForecastUsageExceedingThresholdUsage.getName() == queryObject.getMetricName()) {
                Q.New(CephPrimaryStorageVO.class).list().forEach({ ps ->
                    datas.add(new Datapoint(value: 7, labels: [
                            (PrimaryStorageNamespace.LabelNames.PrimaryStorageUuid.toString()): ps.getUuid(),
                            (PrimaryStorageNamespace.LabelNames.PrimaryStorageType.toString()): ps.getType()])
                    )
                })
            } else if (CephPrimaryStoragePoolNamespace.TimeDurationRequiredForCephPoolForecastUsageExceedingThresholdUsage.getName() == queryObject.getMetricName()) {
                Q.New(CephPrimaryStoragePoolVO.class).list().forEach({ pool ->
                    if (pool.getUuid() == rootPool.uuid) {
                        datas.add(new Datapoint(value: 7, labels: [
                                (CephPrimaryStoragePoolNamespace.LabelNames.PrimaryStorageUuid.toString()): pool.getPrimaryStorageUuid(),
                                (CephPrimaryStoragePoolNamespace.LabelNames.PoolName.toString())          : pool.getPoolName(),
                                (CephPrimaryStoragePoolNamespace.LabelNames.PoolUuid.toString())          : pool.getUuid()
                        ]))
                    }
                })
            } else if (LocalPrimaryStorageHostNamespace.TimeDurationRequiredForLocalStorageHostForecastUsageExceedingThresholdUsage.getName() == queryObject.getMetricName()) {
                Q.New(LocalStorageHostRefVO.class).list().forEach({ ref ->
                    if (ref.getHostUuid() == host21.uuid) {
                        datas.add(new Datapoint(value: 7, labels: [
                                (LocalPrimaryStorageHostNamespace.LabelNames.PrimaryStorageUuid.toString()): ref.getPrimaryStorageUuid(),
                                (LocalPrimaryStorageHostNamespace.LabelNames.HostUuid.toString())          : ref.getHostUuid()])
                        )
                    }
                })
            }
            return datas
        }

        def ps = psProxy.protect(primaryStorageNamespace, "driver")
        onCleanExecute { ps.recover() }
        primaryStorageNamespace.driver = psProxy as PrometheusDatabaseDriver
    }

    void testPrimaryStorageDisconnected() {
        def controller = new PrimaryStorageController(env)
        controller.disconnect("nfs")

        EventFacade evtf = bean(EventFacade.class)
        for (int i = 0; i < 400; i++) {
            evtf.fire(PrimaryStorageCanonicalEvent.PRIMARY_STORAGE_DISCONNECTED, new PrimaryStorageCanonicalEvent.PrimaryStorageStatusChangedData())
        }

        controller.connect("nfs")
    }

    @Override
    void test() {
        env.create {
            prepareEnv()
            testForecastThresholdGlobalConfig()
            testCollectAndPredictUsedPhysicalCapacity()
            testPoolCollectAndPredictUsedPhysicalCapacity()
            testLocalHostCollectAndPredictUsedPhysicalCapacity()
            testPrometheusCollectorPredictedUsedPhysicalCapacity()
            testCreateUsedPhysicalCapacityAlarm()
            testZeroUsedPhysicalCapacity()
            testLoadHistoricalUsagesFromDatabase()
            testDeleteCascade()
            testCollectUsageWithOutPersist()
            testUpdateCollectAndForecastIntervalGlobalConfig()
            testPrimaryStorageDisconnected()
        }
    }

    void testForecastThresholdGlobalConfig() {
        // ps config
        assert PrimaryStorageGlobalConfig.PRIMARY_STORAGE_USED_PHYSICAL_CAPACITY_FORECAST_THRESHOLD.value(Double.class) == 0.9

        updateGlobalConfig {
            category = PrimaryStorageGlobalConfig.CATEGORY
            name = PrimaryStorageGlobalConfig.PRIMARY_STORAGE_USED_PHYSICAL_CAPACITY_FORECAST_THRESHOLD.name
            value = psThreshold
        }
        def globalConfig = queryGlobalConfig {
            conditions = ["category=${PrimaryStorageGlobalConfig.CATEGORY}",
                          "name=${PrimaryStorageGlobalConfig.PRIMARY_STORAGE_USED_PHYSICAL_CAPACITY_FORECAST_THRESHOLD.name}"]
        }[0] as GlobalConfigInventory
        assert globalConfig.getValue() == psThreshold.toString()

        // ceph config
        updateResourceConfig {
            category = PrimaryStorageGlobalConfig.CATEGORY
            name = PrimaryStorageGlobalConfig.PRIMARY_STORAGE_USED_PHYSICAL_CAPACITY_FORECAST_THRESHOLD.name
            value = cephThreshold
            resourceUuid = ceph.uuid
        }
        def cephConfig = getResourceConfig {
            category = PrimaryStorageGlobalConfig.CATEGORY
            name = PrimaryStorageGlobalConfig.PRIMARY_STORAGE_USED_PHYSICAL_CAPACITY_FORECAST_THRESHOLD.name
            resourceUuid = ceph.uuid
        } as GetResourceConfigResult
        assert cephConfig.value == cephThreshold.toString()

        // lcoal config
        updateResourceConfig {
            category = PrimaryStorageGlobalConfig.CATEGORY
            name = PrimaryStorageGlobalConfig.PRIMARY_STORAGE_USED_PHYSICAL_CAPACITY_FORECAST_THRESHOLD.name
            value = localHostThreshold
            resourceUuid = local.uuid
        }
        def localConfig = getResourceConfig {
            category = PrimaryStorageGlobalConfig.CATEGORY
            name = PrimaryStorageGlobalConfig.PRIMARY_STORAGE_USED_PHYSICAL_CAPACITY_FORECAST_THRESHOLD.name
            resourceUuid = local.uuid
        } as GetResourceConfigResult
        assert localConfig.value == localHostThreshold.toString()
    }

    void testCollectAndPredictUsedPhysicalCapacity() {
        // today - 1
        List<LocalDate> dates = new ArrayList<>()
        IntStream.range(0, 366).forEach({ i -> dates.add(LocalDate.now().minusDays(366 - i)) })

        // dataSize = 14
        0.upto(13) { i ->
            psReporter.collectUsage(dates.get(i as int))
        }
        assert psReporter.historicalUsageMap[ceph.uuid].historicalUsedPhysicalCapacities.size() == 14

        psReporter.forecastUsage()
        assert !psReporter.usedPhysicalCapacityForecastsMap.containsKey(ceph.uuid)

        // query ps usage report
        GetPrimaryStorageUsageReportResult result = getPrimaryStorageUsageReport {
            primaryStorageUuid = ceph.uuid
        } as GetPrimaryStorageUsageReportResult
        assert result.usageReport.usedPhysicalCapacitiesForecast.size() == 0
        assert result.usageReport.usedPhysicalCapacitiesHistory.size() == 14
        assert result.usageReport.totalPhysicalCapacitiesHistory.size() == 14

        // dataSize = 15
        psReporter.collectUsage(dates.get(14))
        psReporter.forecastUsage()
        assert psReporter.historicalUsageMap[ceph.uuid].historicalUsedPhysicalCapacities.size() == 15
        assert psReporter.getFutureForecastsInPercent(ceph.uuid).size() == 185 - 1
        assert Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid).count() == 15

        // dataSize = 366
        15.upto(365) { i ->
            psReporter.collectUsage(dates.get(i as int))
        }
        psReporter.historicalUsageMap[ceph.uuid].historicalUsedPhysicalCapacities.size() == 366
        assert Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid).count() == 366

        // Retain data for one year
        PrimaryStorageHistoricalUsageVO vo = Q.New(PrimaryStorageHistoricalUsageVO.class)
                .eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid)
                .orderBy(PrimaryStorageHistoricalUsageVO_.id, SimpleQuery.Od.ASC).limit(1).find()

        SQL.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid)
                .eq(PrimaryStorageHistoricalUsageVO_.id, vo.id).set(PrimaryStorageHistoricalUsageVO_.recordDate,
                Timestamp.valueOf(LocalDate.now().minusDays(367).atStartOfDay())).update()

        psReporter.collectUsage(LocalDate.now())
        psReporter.forecastUsage()
        assert psReporter.historicalUsageMap[ceph.uuid].historicalUsedPhysicalCapacities.size() == 366
        assert Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid).count() == 366
        assert !Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.id, vo.id).isExists()

        // collect check recordDate
        List<PrimaryStorageHistoricalUsageVO> vos = Q.New(PrimaryStorageHistoricalUsageVO.class)
                .eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid)
                .orderBy(PrimaryStorageHistoricalUsageVO_.recordDate, SimpleQuery.Od.ASC).list()
        vo = vos.get(vos.size() - 1)
        vo.getRecordDate().getTime() == Timestamp.valueOf(LocalDate.now().atStartOfDay()).getTime()

        psReporter.collectUsage(LocalDate.now())
        psReporter.collectUsage(LocalDate.now().minusDays(1))
        psReporter.collectUsage(LocalDate.now().minusDays(2))
        assert Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid).count() == 366

        // query ps usage report
        result = getPrimaryStorageUsageReport {
            primaryStorageUuid = ceph.uuid
        } as GetPrimaryStorageUsageReportResult
        assert result.usageReport.usedPhysicalCapacitiesForecast.size() == 366 + 185
        assert result.usageReport.usedPhysicalCapacitiesHistory.size() == 366
        assert result.usageReport.totalPhysicalCapacitiesHistory.size() == 366
        assert result.usageReport.interval == 1

        Timestamp recordDate = Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid)
                .orderBy(PrimaryStorageHistoricalUsageVO_.recordDate, SimpleQuery.Od.ASC).limit(1)
                .select(PrimaryStorageHistoricalUsageVO_.recordDate).findValue()
        assert result.usageReport.startTime == recordDate.getTime()
    }

    void testPoolCollectAndPredictUsedPhysicalCapacity() {
        List<LocalDate> dates = new ArrayList<>()
        IntStream.range(0, 366).forEach({ i -> dates.add(LocalDate.now().minusDays(366 - i)) })

        // dataSize = 14
        0.upto(13) { i ->
            poolReporter.collectUsage(dates.get(i as int))
        }
        assert poolReporter.historicalUsageMap[rootPool.osdGroup.uuid].historicalUsedPhysicalCapacities.size() == 14

        // dataSize = 15
        poolReporter.collectUsage(dates.get(14))
        poolReporter.forecastUsage()
        assert poolReporter.historicalUsageMap[rootPool.osdGroup.uuid].historicalUsedPhysicalCapacities.size() == 15
        assert poolReporter.getFutureForecastsInPercent(rootPool.osdGroup.uuid).size() == 185 - 1
        assert Q.New(CephOsdGroupHistoricalUsageVO.class).eq(CephOsdGroupHistoricalUsageVO_.osdGroupUuid, rootPool.osdGroup.uuid).count() == 15

        // dataSize = 366
        15.upto(365) { i ->
            poolReporter.collectUsage(dates.get(i as int))
        }
        assert poolReporter.historicalUsageMap[rootPool.osdGroup.uuid].historicalUsedPhysicalCapacities.size() == 366
        assert Q.New(CephOsdGroupHistoricalUsageVO.class).eq(CephOsdGroupHistoricalUsageVO_.osdGroupUuid, rootPool.osdGroup.uuid).count() == 366

        // Retain data for one year
        CephOsdGroupHistoricalUsageVO vo = Q.New(CephOsdGroupHistoricalUsageVO.class)
                .eq(CephOsdGroupHistoricalUsageVO_.osdGroupUuid, rootPool.osdGroup.uuid)
                .orderBy(CephOsdGroupHistoricalUsageVO_.id, SimpleQuery.Od.ASC).limit(1).find()

        SQL.New(CephOsdGroupHistoricalUsageVO.class).eq(CephOsdGroupHistoricalUsageVO_.osdGroupUuid, rootPool.osdGroup.uuid)
                .eq(CephOsdGroupHistoricalUsageVO_.id, vo.id).set(CephOsdGroupHistoricalUsageVO_.recordDate,
                Timestamp.valueOf(LocalDate.now().minusDays(367).atStartOfDay())).update()

        poolReporter.collectUsage(LocalDate.now())
        poolReporter.forecastUsage()
        assert poolReporter.historicalUsageMap[rootPool.osdGroup.uuid].historicalUsedPhysicalCapacities.size() == 366
        assert Q.New(CephOsdGroupHistoricalUsageVO.class).eq(CephOsdGroupHistoricalUsageVO_.osdGroupUuid, rootPool.osdGroup.uuid).count() == 366
        assert !Q.New(CephOsdGroupHistoricalUsageVO.class).eq(CephOsdGroupHistoricalUsageVO_.id, vo.id).isExists()

        // query pool usage report
        GetPrimaryStorageUsageReportResult resultPool = getPrimaryStorageUsageReport {
            primaryStorageUuid = ceph.uuid
            uris = [String.format("ceph://%s", rootPool.uuid), String.format("ceph://%s", dataPool.uuid), String.format("ceph://%s", imageCachePool.uuid)]
        } as GetPrimaryStorageUsageReportResult
        def rootPoolUsageReport = resultPool.uriUsageForecast[rootPool.uuid] as UsageReport
        assert rootPoolUsageReport.usedPhysicalCapacitiesForecast.size() == 366 + 185
        assert rootPoolUsageReport.usedPhysicalCapacitiesHistory.size() == 366
        assert rootPoolUsageReport.totalPhysicalCapacitiesHistory.size() == 366
        assert rootPoolUsageReport.interval == 1

        Timestamp recordDate = Q.New(CephOsdGroupHistoricalUsageVO.class).eq(CephOsdGroupHistoricalUsageVO_.osdGroupUuid, rootPool.osdGroup.uuid)
                .orderBy(CephOsdGroupHistoricalUsageVO_.recordDate, SimpleQuery.Od.ASC).limit(1)
                .select(CephOsdGroupHistoricalUsageVO_.recordDate).findValue()
        assert rootPoolUsageReport.startTime == recordDate.getTime()
    }

    void testLocalHostCollectAndPredictUsedPhysicalCapacity() {
        List<LocalDate> dates = new ArrayList<>()
        IntStream.range(0, 366).forEach({ i -> dates.add(LocalDate.now().minusDays(366 - i)) })

        // dataSize = 14
        0.upto(13) { i ->
            localStorageHostReporter.collectUsage(dates.get(i as int))
        }
        assert localStorageHostReporter.historicalUsageMap[host21.uuid].historicalUsedPhysicalCapacities.size() == 14

        // dataSize = 15
        localStorageHostReporter.collectUsage(dates.get(14))
        localStorageHostReporter.forecastUsage()
        assert localStorageHostReporter.historicalUsageMap[host21.uuid].historicalUsedPhysicalCapacities.size() == 15
        assert localStorageHostReporter.getFutureForecastsInPercent(host21.uuid).size() == 185 - 1
        assert Q.New(LocalStorageHostHistoricalUsageVO.class).eq(LocalStorageHostHistoricalUsageVO_.hostUuid, host21.uuid).count() == 15

        // dataSize = 366
        15.upto(365) { i ->
            localStorageHostReporter.collectUsage(dates.get(i as int))
        }
        assert localStorageHostReporter.historicalUsageMap[host21.uuid].historicalUsedPhysicalCapacities.size() == 366
        assert Q.New(LocalStorageHostHistoricalUsageVO.class).eq(LocalStorageHostHistoricalUsageVO_.hostUuid, host21.uuid).count() == 366

        // Retain data for one year
        LocalStorageHostHistoricalUsageVO vo = Q.New(LocalStorageHostHistoricalUsageVO.class)
                .eq(LocalStorageHostHistoricalUsageVO_.hostUuid, host21.uuid)
                .orderBy(LocalStorageHostHistoricalUsageVO_.id, SimpleQuery.Od.ASC).limit(1).find()

        SQL.New(LocalStorageHostHistoricalUsageVO.class).eq(LocalStorageHostHistoricalUsageVO_.hostUuid, host21.uuid)
                .eq(LocalStorageHostHistoricalUsageVO_.id, vo.id).set(LocalStorageHostHistoricalUsageVO_.recordDate,
                Timestamp.valueOf(LocalDate.now().minusDays(367).atStartOfDay())).update()

        localStorageHostReporter.collectUsage(LocalDate.now())
        localStorageHostReporter.forecastUsage()
        assert localStorageHostReporter.historicalUsageMap[host21.uuid].historicalUsedPhysicalCapacities.size() == 366
        assert Q.New(LocalStorageHostHistoricalUsageVO.class).eq(LocalStorageHostHistoricalUsageVO_.hostUuid, host21.uuid).count() == 366
        assert !Q.New(LocalStorageHostHistoricalUsageVO.class).eq(LocalStorageHostHistoricalUsageVO_.id, vo.id).isExists()

        // query local host usage report
        GetPrimaryStorageUsageReportResult resultHost = getPrimaryStorageUsageReport {
            primaryStorageUuid = local.uuid
            uris = [String.format("hostUuid://%s", host21.uuid), String.format("hostUuid://%s", host22.uuid)]
        } as GetPrimaryStorageUsageReportResult
        def host21UsageReport = resultHost.uriUsageForecast[host21.uuid] as UsageReport
        host21UsageReport.usedPhysicalCapacitiesForecast.size() == 366 + 185
        host21UsageReport.usedPhysicalCapacitiesHistory.size() == 366
        host21UsageReport.totalPhysicalCapacitiesHistory.size() == 366
        host21UsageReport.interval == 1

        Timestamp recordDate = Q.New(LocalStorageHostHistoricalUsageVO.class).eq(LocalStorageHostHistoricalUsageVO_.hostUuid, host21.uuid)
                .orderBy(LocalStorageHostHistoricalUsageVO_.recordDate, SimpleQuery.Od.ASC).limit(1)
                .select(LocalStorageHostHistoricalUsageVO_.recordDate).findValue()
        assert host21UsageReport.startTime == recordDate.getTime()
    }

    void testPrometheusCollectorPredictedUsedPhysicalCapacity() {
        List<Double> percent = [0.1D, 0.2D, 0.3D, 0.4D, 0.5D, 0.6D, 0.7D, 0.83D, 0.84D, 0.85D, 0.86D, 0.9D]

        // ceph ps
        psReporter.usedPhysicalCapacityForecastsMap[nfs.uuid].setFutureForecastsInPercent(new ArrayList<>())
        psReporter.usedPhysicalCapacityForecastsMap[local.uuid].setFutureForecastsInPercent(new ArrayList<>())
        psReporter.usedPhysicalCapacityForecastsMap[ceph.uuid].setFutureForecastsInPercent(new ArrayList<>())
        psReporter.usedPhysicalCapacityForecastsMap[nfs.uuid].setFutureForecastsInPercent(percent)
        psReporter.usedPhysicalCapacityForecastsMap[local.uuid].setFutureForecastsInPercent(percent)
        psReporter.usedPhysicalCapacityForecastsMap[ceph.uuid].setFutureForecastsInPercent(percent)

        CollectorRegistry registry = new CollectorRegistry()
        def collector = new PrimaryStoragePrometheusNamespace.PrimaryStorageCollector()
        registry.register(new Collector() {
            @Override
            List<Collector.MetricFamilySamples> collect() {
                return collector.collect()
            }
        })

        Enumeration<Collector.MetricFamilySamples> em = registry.filteredMetricFamilySamples([] as Set)
        em.each { it ->
            def samples = it.samples
            def name = it.name.split(PrometheusNamespace.SERIES_NAME_SPLITTER)[1]
            if (name == PrimaryStorageNamespace.TimeDurationRequiredForPrimaryStorageForecastUsageExceedingThresholdUsage.name) {
                assert findPoolSample(samples, nfs.uuid).value == Double.valueOf(10)
                assert findPoolSample(samples, ceph.uuid).value == Double.valueOf(9)
                assert findPoolSample(samples, local.uuid).value == Double.valueOf(8)
            }
        }

        // On the new day, the alarm needs to filter out the predicted value about the new day.
        List<Long> recordDates = new ArrayList<>(psReporter.historicalUsageMap[ceph.uuid].getRecordDates())
        recordDates.remove(recordDates.size()-1)
        recordDates.add(Timestamp.valueOf(LocalDate.now().minusDays(1).atStartOfDay()).getTime())

        EvictingQueue<Long> recordDatesQueue = EvictingQueue.create(366);
        recordDates.forEach { recordDatesQueue.add(it) }
        psReporter.historicalUsageMap[ceph.uuid].setRecordDates(recordDatesQueue)

        registry = new CollectorRegistry()
        collector = new PrimaryStoragePrometheusNamespace.PrimaryStorageCollector()
        registry.register(new Collector() {
            @Override
            List<Collector.MetricFamilySamples> collect() {
                return collector.collect()
            }
        })

        em = registry.filteredMetricFamilySamples([] as Set)
        em.each { it ->
            def samples = it.samples
            def name = it.name.split(PrometheusNamespace.SERIES_NAME_SPLITTER)[1]
            if (name == PrimaryStorageNamespace.TimeDurationRequiredForPrimaryStorageForecastUsageExceedingThresholdUsage.name) {
                assert findPoolSample(samples, ceph.uuid).value == Double.valueOf(9-1)
            }
        }

        // ceph pool
        poolReporter.usedPhysicalCapacityForecastsMap[rootPool.osdGroup.uuid].setFutureForecastsInPercent(new ArrayList<>())
        poolReporter.usedPhysicalCapacityForecastsMap[rootPool.osdGroup.uuid].setFutureForecastsInPercent(percent)

        CollectorRegistry registryPool = new CollectorRegistry()
        def collectorPool = new CephPrimaryStoragePoolPrometheusNamespace.CephPrimaryStoragePoolCollector()
        registryPool.register(new Collector() {
            @Override
            List<Collector.MetricFamilySamples> collect() {
                return collectorPool.collect()
            }
        })

        Enumeration<Collector.MetricFamilySamples> emPool = registry.filteredMetricFamilySamples([] as Set)
        emPool.each { it ->
            def samples = it.samples
            def name = it.name.split(PrometheusNamespace.SERIES_NAME_SPLITTER)[1]
            if (name == CephPrimaryStoragePoolNamespace.TimeDurationRequiredForCephPoolForecastUsageExceedingThresholdUsage.name) {
                assert findPoolSample(samples, ceph.uuid, rootPool.uuid).value == Double.valueOf(9)
            }
        }

        // On the new day, the alarm needs to filter out the predicted value about the new day.
        recordDates = new ArrayList<>(poolReporter.historicalUsageMap[rootPool.osdGroup.uuid].getRecordDates())
        recordDates.remove(recordDates.size() - 1)
        recordDates.add(Timestamp.valueOf(LocalDate.now().minusDays(1).atStartOfDay()).getTime())

        recordDatesQueue = EvictingQueue.create(366);
        recordDates.forEach { recordDatesQueue.add(it) }
        poolReporter.historicalUsageMap[rootPool.osdGroup.uuid].setRecordDates(recordDatesQueue)

        registryPool = new CollectorRegistry()
        collectorPool = new CephPrimaryStoragePoolPrometheusNamespace.CephPrimaryStoragePoolCollector()
        registryPool.register(new Collector() {
            @Override
            List<Collector.MetricFamilySamples> collect() {
                return collectorPool.collect()
            }
        })

        emPool = registry.filteredMetricFamilySamples([] as Set)
        emPool.each { it ->
            def samples = it.samples
            def name = it.name.split(PrometheusNamespace.SERIES_NAME_SPLITTER)[1]
            if (name == CephPrimaryStoragePoolNamespace.TimeDurationRequiredForCephPoolForecastUsageExceedingThresholdUsage.name) {
                assert findPoolSample(samples, ceph.uuid, rootPool.uuid).value == Double.valueOf(9-1)
            }
        }

        // local host
        localStorageHostReporter.usedPhysicalCapacityForecastsMap[host21.uuid].setFutureForecastsInPercent(new ArrayList<>())
        localStorageHostReporter.usedPhysicalCapacityForecastsMap[host21.uuid].setFutureForecastsInPercent(percent)

        CollectorRegistry registryLocalHost = new CollectorRegistry()
        def collectorLocalHost = new LocalPrimaryStorageHostPrometheusNamespace.LocalPrimaryStorageHostCollector()
        registryLocalHost.register(new Collector() {
            @Override
            List<Collector.MetricFamilySamples> collect() {
                return collectorLocalHost.collect()
            }
        })

        Enumeration<Collector.MetricFamilySamples> emLocalHost = registry.filteredMetricFamilySamples([] as Set)
        emLocalHost.each { it ->
            def samples = it.samples
            def name = it.name.split(PrometheusNamespace.SERIES_NAME_SPLITTER)[1]
            if (name == LocalPrimaryStorageHostNamespace.TimeDurationRequiredForLocalStorageHostForecastUsageExceedingThresholdUsage.name) {
                assert findLocalHostSample(samples, local.uuid, host21.uuid).value == Double.valueOf(8)
            }
        }

        // On the new day, the alarm needs to filter out the predicted value about the new day.
        recordDates = new ArrayList<>(localStorageHostReporter.historicalUsageMap[host21.uuid].getRecordDates())
        recordDates.remove(recordDates.size() - 1)
        recordDates.add(Timestamp.valueOf(LocalDate.now().minusDays(1).atStartOfDay()).getTime())

        recordDatesQueue = EvictingQueue.create(366);
        recordDates.forEach { recordDatesQueue.add(it) }
        localStorageHostReporter.historicalUsageMap[host21.uuid].setRecordDates(recordDatesQueue)

        registryLocalHost = new CollectorRegistry()
        collectorLocalHost = new LocalPrimaryStorageHostPrometheusNamespace.LocalPrimaryStorageHostCollector()
        registryLocalHost.register(new Collector() {
            @Override
            List<Collector.MetricFamilySamples> collect() {
                return collectorLocalHost.collect()
            }
        })

        emLocalHost = registry.filteredMetricFamilySamples([] as Set)
        emLocalHost.each { it ->
            def samples = it.samples
            def name = it.name.split(PrometheusNamespace.SERIES_NAME_SPLITTER)[1]
            if (name == LocalPrimaryStorageHostNamespace.TimeDurationRequiredForLocalStorageHostForecastUsageExceedingThresholdUsage.name) {
                assert findLocalHostSample(samples, local.uuid, host21.uuid).value == Double.valueOf(8-1)
            }
        }
    }

    void testCreateUsedPhysicalCapacityAlarm() {
        // ceph ps
        AlarmInventory alarm = createAlarm {
            name = "ceph-ps"
            comparisonOperator = ComparisonOperator.LessThan.toString()
            emergencyLevel = EmergencyLevel.Emergent.name()
            namespace = primaryStorageNamespace.getName()
            metricName = PrimaryStorageNamespace.TimeDurationRequiredForPrimaryStorageForecastUsageExceedingThresholdUsage.name
            labels = [
                    new Label(key: PrimaryStorageNamespace.LabelNames.PrimaryStorageUuid.toString(), op: Operator.Equal.toString(), value: ceph.uuid),
                    new Label(key: PrimaryStorageNamespace.LabelNames.PrimaryStorageType.toString(), op: Operator.Equal.toString(), value: ceph.type)
            ]
            threshold = 15
            period = 1
            repeatInterval = 1
            repeatCount = 1
        } as AlarmInventory

        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(1)
        retryInSecs {
            assert Q.New(AlarmVO.class).eq(AlarmVO_.uuid, alarm.uuid).isExists()
            assert Q.New(AlarmRecordsVO.class)
                    .eq(AlarmRecordsVO_.alarmUuid, alarm.uuid)
                    .eq(AlarmRecordsVO_.resourceUuid, ceph.uuid).count() == 1
        }

        updateAlarm {
            uuid = alarm.uuid
            repeatCount = 3
        }
        retryInSecs {
            assert Q.New(AlarmRecordsVO.class)
                    .eq(AlarmRecordsVO_.alarmUuid, alarm.uuid)
                    .eq(AlarmRecordsVO_.resourceUuid, ceph.uuid).count() == 3
        }

        deleteAlarm {
            uuid = alarm.uuid
        }
        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(600)

        // local host
        AlarmInventory localHostAlarm = createAlarm {
            name = "local-host"
            comparisonOperator = ComparisonOperator.LessThan.toString()
            emergencyLevel = EmergencyLevel.Emergent.name()
            namespace = primaryStorageNamespace.getName()
            metricName = LocalPrimaryStorageHostNamespace.TimeDurationRequiredForLocalStorageHostForecastUsageExceedingThresholdUsage.name
            labels = [
                    new Label(key: LocalPrimaryStorageHostNamespace.LabelNames.PrimaryStorageUuid.toString(), op: Operator.Equal.toString(), value: local.uuid),
                    new Label(key: LocalPrimaryStorageHostNamespace.LabelNames.HostUuid.toString(), op: Operator.Equal.toString(), value: host21.uuid)
            ]
            threshold = 15
            period = 1
            repeatInterval = 1
            repeatCount = 1
        } as AlarmInventory

        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(1)
        retryInSecs {
            assert Q.New(AlarmVO.class).eq(AlarmVO_.uuid, localHostAlarm.uuid).isExists()
            assert Q.New(AlarmRecordsVO.class)
                    .eq(AlarmRecordsVO_.alarmUuid, localHostAlarm.uuid)
                    .eq(AlarmRecordsVO_.resourceUuid, local.uuid).count() == 1
        }

        updateAlarm {
            uuid = localHostAlarm.uuid
            threshold = 15
            repeatCount = 3
        }
        retryInSecs {
            assert Q.New(AlarmRecordsVO.class)
                    .eq(AlarmRecordsVO_.alarmUuid, localHostAlarm.uuid)
                    .eq(AlarmRecordsVO_.resourceUuid, local.uuid).count() == 3
        }

        deleteAlarm {
            uuid = localHostAlarm.uuid
        }
        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(600)

        // ceph pool
        AlarmInventory poolAlarm = createAlarm {
            name = "ceph-pool"
            comparisonOperator = ComparisonOperator.LessThan.toString()
            emergencyLevel = EmergencyLevel.Emergent.name()
            namespace = primaryStorageNamespace.getName()
            metricName = CephPrimaryStoragePoolNamespace.TimeDurationRequiredForCephPoolForecastUsageExceedingThresholdUsage.name
            labels = [
                    new Label(key: CephPrimaryStoragePoolNamespace.LabelNames.PrimaryStorageUuid.toString(), op: Operator.Equal.toString(), value: rootPool.uuid),
                    new Label(key: CephPrimaryStoragePoolNamespace.LabelNames.PoolName.toString(), op: Operator.Equal.toString(), value: rootPool.poolName),
                    new Label(key: CephPrimaryStoragePoolNamespace.LabelNames.PoolUuid.toString(), op: Operator.Equal.toString(), value: rootPool.uuid)
            ]
            threshold = 15
            period = 1
            repeatInterval = 1
            repeatCount = 1
        } as AlarmInventory

        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(1)
        retryInSecs {
            assert Q.New(AlarmVO.class).eq(AlarmVO_.uuid, poolAlarm.uuid).isExists()
            assert Q.New(AlarmRecordsVO.class)
                    .eq(AlarmRecordsVO_.alarmUuid, poolAlarm.uuid)
                    .eq(AlarmRecordsVO_.resourceUuid, ceph.uuid).count() == 1
        }

        updateAlarm {
            uuid = poolAlarm.uuid
            threshold = 15
            repeatCount = 3
        }
        retryInSecs {
            assert Q.New(AlarmRecordsVO.class)
                    .eq(AlarmRecordsVO_.alarmUuid, poolAlarm.uuid)
                    .eq(AlarmRecordsVO_.resourceUuid, ceph.uuid).count() == 3
        }

        deleteAlarm {
            uuid = poolAlarm.uuid
        }

        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(600)
    }

    void testZeroUsedPhysicalCapacity() {
        detachPrimaryStorageFromCluster {
            clusterUuid = cluster2.uuid
            primaryStorageUuid = local.uuid
        }

        long count = Q.New(PrimaryStorageHistoricalUsageVO.class)
                .eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, local.uuid).count()

        PrimaryStorageCapacityVO capacityVO = Q.New(PrimaryStorageCapacityVO.class)
                .eq(PrimaryStorageCapacityVO_.uuid, local.uuid).find()
        assert capacityVO.totalPhysicalCapacity == 0

        psReporter.collectUsage(LocalDate.now().plusDays(1))
        List<PrimaryStorageHistoricalUsageVO> usageVOS = Q.New(PrimaryStorageHistoricalUsageVO.class)
                .eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, local.uuid)
                .orderBy(PrimaryStorageHistoricalUsageVO_.id, SimpleQuery.Od.ASC).list()
        assert (long) usageVOS.size() == count + 1
        assert usageVOS.get(usageVOS.size() - 1).totalPhysicalCapacity != 0
        assert usageVOS.get(usageVOS.size() - 1).getRecordDate().getTime() ==
                Timestamp.valueOf(LocalDate.now().plusDays(1).atStartOfDay()).getTime()

        attachPrimaryStorageToCluster {
            clusterUuid = cluster2.uuid
            primaryStorageUuid = local.uuid
        }
    }

    void testLoadHistoricalUsagesFromDatabase() {
        // load historical usage from database and add missing historical usage
        List<PrimaryStorageHistoricalUsageVO> usageVOs = Q.New(PrimaryStorageHistoricalUsageVO.class)
                .eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid)
                .orderBy(PrimaryStorageHistoricalUsageVO_.id, SimpleQuery.Od.ASC).limit(5).list()

        // historical usage
        usageVOs.get(0).setRecordDate(Timestamp.valueOf(LocalDate.now().minusDays(8).atStartOfDay()))
        usageVOs.get(1).setRecordDate(Timestamp.valueOf(LocalDate.now().minusDays(7).atStartOfDay()))
        usageVOs.get(2).setRecordDate(Timestamp.valueOf(LocalDate.now().minusDays(6).atStartOfDay()))
        usageVOs.get(3).setRecordDate(Timestamp.valueOf(LocalDate.now().minusDays(5).atStartOfDay()))
        usageVOs.get(4).setRecordDate(Timestamp.valueOf(LocalDate.now().minusDays(4).atStartOfDay()))
        dbf.updateCollection(usageVOs)

        SQL.New(PrimaryStorageHistoricalUsageVO.class)
                .gt(PrimaryStorageHistoricalUsageVO_.id, usageVOs.get(4).getId())
                .eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid)
                .hardDelete()

        // new env load historical usage from database
        SQL.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, local.uuid).hardDelete()
        assert Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, local.uuid).count() == 0

        // cleanup historicalUsageMap
        psReporter.historicalUsageMap.clear()

        // load historical usage from database
        psReporter.loadHistoricalUsageFromDatabase()
        // ceph
        List<Long> historicalForecasts = new ArrayList<>(psReporter.historicalUsageMap[ceph.uuid].getTotalPhysicalCapacities())
        assert historicalForecasts.size() == 8

        usageVOs = Q.New(PrimaryStorageHistoricalUsageVO.class)
                .eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid)
                .orderBy(PrimaryStorageHistoricalUsageVO_.id, SimpleQuery.Od.ASC).list()
        assert usageVOs.size() == 8
        assert usageVOs.get(5).getRecordDate().getTime() - usageVOs.get(4).getRecordDate().getTime() == TimeUnit.DAYS.toMillis(1)
        assert usageVOs.get(6).getRecordDate().getTime() - usageVOs.get(5).getRecordDate().getTime() == TimeUnit.DAYS.toMillis(1)
        assert usageVOs.get(7).getRecordDate().getTime() - usageVOs.get(6).getRecordDate().getTime() == TimeUnit.DAYS.toMillis(1)
    }

    void testDeleteCascade() {
        // cluster2
        detachPrimaryStorageFromCluster {
            clusterUuid = cluster2.uuid
            primaryStorageUuid = ceph.uuid
        }
        deletePrimaryStorage {
            uuid = ceph.uuid
        }
        assert Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, ceph.uuid).count() == 0
        assert Q.New(CephOsdGroupHistoricalUsageVO.class).count() == 0

        psReporter.collectUsage(LocalDate.now())
        psReporter.forecastUsage()
        assert !psReporter.historicalUsageMap.containsKey(ceph.uuid)
        assert !psReporter.usedPhysicalCapacityForecastsMap.containsKey(ceph.uuid)

        poolReporter.collectUsage(LocalDate.now())
        poolReporter.forecastUsage()
        assert !poolReporter.historicalUsageMap.containsKey(rootPool.osdGroup.uuid)
        assert !poolReporter.usedPhysicalCapacityForecastsMap.containsKey(rootPool.osdGroup.uuid)

        detachPrimaryStorageFromCluster {
            clusterUuid = cluster2.uuid
            primaryStorageUuid = local.uuid
        }
        deletePrimaryStorage {
            uuid = local.uuid
        }
        assert Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, local.uuid).count() == 0
        assert Q.New(LocalStorageHostHistoricalUsageVO.class).count() == 0

        localStorageHostReporter.collectUsage(LocalDate.now())
        localStorageHostReporter.forecastUsage()
        assert !localStorageHostReporter.historicalUsageMap.containsKey(host21.uuid)
        assert !localStorageHostReporter.usedPhysicalCapacityForecastsMap.containsKey(host21.uuid)
    }

    void testCollectUsageWithOutPersist() {
        SQL.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, nfs.uuid).hardDelete()
        psReporter.historicalUsageMap.remove(nfs.uuid)

        psReporter.collectUsage(LocalDate.now())
        assert psReporter.historicalUsageMap[nfs.uuid].recordDates.size() == 1

        psReporter.historicalUsageMap[nfs.uuid].historicalUsedPhysicalCapacities.clear()
        psReporter.historicalUsageMap[nfs.uuid].totalPhysicalCapacities.clear()
        psReporter.historicalUsageMap[nfs.uuid].recordDates.clear()

        psReporter.collectUsage(LocalDate.now())

        assert psReporter.historicalUsageMap[nfs.uuid].historicalUsedPhysicalCapacities.size() == 1
        assert psReporter.historicalUsageMap[nfs.uuid].totalPhysicalCapacities.size() == 1
        assert psReporter.historicalUsageMap[nfs.uuid].recordDates.size() == 1
        Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, nfs.uuid).list().size() == 1
    }

    void testUpdateCollectAndForecastIntervalGlobalConfig() {
        SQL.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, nfs.uuid).hardDelete()
        psReporter.historicalUsageMap.remove(nfs.uuid)

        PrimaryStorageGlobalConfig.COLLECT_AND_FORECAST_INTERVAL.updateValue(2)
        retryInSecs {
            assert psReporter.historicalUsageMap[nfs.uuid].recordDates.size() == 1
            Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, nfs.uuid).list().size() == 1
        }

        PrimaryStorageGlobalConfig.COLLECT_AND_FORECAST_INTERVAL.updateValue(1)
        retryInSecs {
            assert psReporter.historicalUsageMap[nfs.uuid].recordDates.size() == 1
            Q.New(PrimaryStorageHistoricalUsageVO.class).eq(PrimaryStorageHistoricalUsageVO_.primaryStorageUuid, nfs.uuid).list().size() == 1
        }
    }

    private static Collector.MetricFamilySamples.Sample findPoolSample(
            List<Collector.MetricFamilySamples.Sample> samples, String psUuid, boolean errorOutIfNotExists = true) {
        def s = samples.find {
            assert it.labelNames.size() == 2
            assert it.labelNames[0] == PrimaryStorageNamespace.LabelNames.PrimaryStorageUuid.toString()
            assert it.labelNames[1] == PrimaryStorageNamespace.LabelNames.PrimaryStorageType.toString()
            assert it.labelValues.size() == 2

            return it.labelValues[0] == psUuid
        }

        if (errorOutIfNotExists) {
            assert s != null: "cannot find the primary storage[uuid:${psUuid}]"
        }

        return s
    }

    private static Collector.MetricFamilySamples.Sample findPoolSample(
            List<Collector.MetricFamilySamples.Sample> samples, String psUuid, String poolUuid, boolean errorOutIfNotExists = true) {
        def s = samples.find {
            assert it.labelNames.size() == 3
            assert it.labelNames[0] == CephPrimaryStoragePoolNamespace.LabelNames.PrimaryStorageUuid.toString()
            assert it.labelNames[1] == CephPrimaryStoragePoolNamespace.LabelNames.PoolName.toString()
            assert it.labelNames[2] == CephPrimaryStoragePoolNamespace.LabelNames.PoolUuid.toString()
            assert it.labelValues.size() == 3

            return it.labelValues[0] == psUuid
        }

        if (errorOutIfNotExists) {
            assert s != null: "cannot find the ceph[uuid:${psUuid}] pool[uuid:${poolUuid}]"
        }

        return s
    }

    private static Collector.MetricFamilySamples.Sample findLocalHostSample(
            List<Collector.MetricFamilySamples.Sample> samples, String psUuid, String hostUuid, boolean errorOutIfNotExists = true) {
        def s = samples.find {
            assert it.labelNames.size() == 2
            assert it.labelNames[0] == LocalPrimaryStorageHostNamespace.LabelNames.PrimaryStorageUuid.toString()
            assert it.labelNames[2] == LocalPrimaryStorageHostNamespace.LabelNames.HostUuid.toString()
            assert it.labelValues.size() == 2

            return it.labelValues[0] == psUuid
        }

        if (errorOutIfNotExists) {
            assert s != null: "cannot find the ceph[uuid:${psUuid}] host[uuid:${hostUuid}]"
        }

        return s
    }

    @Override
    void clean() {
        env.delete()
    }
}
