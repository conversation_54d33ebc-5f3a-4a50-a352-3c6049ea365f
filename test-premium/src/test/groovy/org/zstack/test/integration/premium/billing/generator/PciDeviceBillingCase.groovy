package org.zstack.test.integration.premium.billing.generator

import org.springframework.http.HttpEntity
import org.zstack.billing.BillingConstants
import org.zstack.billing.BillingResourceLabelKey
import org.zstack.billing.BillingSystemTags
import org.zstack.billing.spendingcalculator.pcidevice.PciDeviceSpending
import org.zstack.billing.spendingcalculator.pcidevice.PciDeviceSpendingInventory
import org.zstack.billing.spendingcalculator.pcidevice.PciDeviceUsageVO
import org.zstack.billing.spendingcalculator.pcidevice.PciDeviceUsageVO_
import org.zstack.billing.Spending
import org.zstack.billing.SpendingStruct
import org.zstack.billing.generator.BillingResourceLabelVO
import org.zstack.billing.generator.BillingResourceLabelVO_
import org.zstack.billing.generator.BillingType
import org.zstack.billing.generator.pcidevice.PciDeviceBillingGenerator
import org.zstack.billing.generator.pcidevice.PciDeviceBillingSpendingCalculator
import org.zstack.billing.generator.pcidevice.PciDeviceBillingVO
import org.zstack.billing.generator.pcidevice.PciDeviceBillingVO_
import org.zstack.billing.generator.pcidevice.PciDeviceUsageHistoryVO
import org.zstack.billing.generator.pcidevice.PciDeviceUsageHistoryVO_
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend
import org.zstack.pciDevice.PciDeviceStatus
import org.zstack.pciDevice.PciDeviceSystemTags
import org.zstack.pciDevice.PciDeviceTOExampleMaker
import org.zstack.pciDevice.PciDeviceVO
import org.zstack.pciDevice.PciDeviceVO_
import org.zstack.sdk.ClusterInventory
import org.zstack.sdk.HostIommuStateType
import org.zstack.sdk.KVMHostInventory
import org.zstack.sdk.PciDeviceInventory
import org.zstack.sdk.PciDeviceOfferingInventory
import org.zstack.sdk.PriceInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.TestPremium
import org.zstack.utils.data.SizeUnit

import java.util.concurrent.TimeUnit

class PciDeviceBillingCase extends PremiumSubCase{
    EnvSpec env
    DatabaseFacade dbf

    KVMHostInventory kvm1
    KVMHostInventory kvm2
    ClusterInventory cluster1
    PciDeviceInventory nv_video_1
    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = TestPremium.makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString()]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                attachBackupStorage("sftp")
            }
        }
    }

    @Override
    void test() {
        env.create {
            initEnv()
            execTest()
            testQuery()
            testBillingSpendingCalculator()
            testBillingWithoutPciDevice()
        }
    }

    void initEnv() {
        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp();
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_GPU(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_AUDIO(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
                    PciDeviceTOExampleMaker.GT710B_AUDIO(),
                    PciDeviceTOExampleMaker.M60_VIRTUALIZABLE(),
                    PciDeviceTOExampleMaker.S7150_VIRTUALIZED(),
                    PciDeviceTOExampleMaker.S7150V_3936M_1(),
                    PciDeviceTOExampleMaker.S7150V_3936M_2(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        cluster1 = env.inventoryByName("cluster")

        kvm1 = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm1"
            managementIp = "127.0.0.11"
            clusterUuid = cluster1.uuid
        }

        kvm2 = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm1"
            managementIp = "127.0.0.12"
            clusterUuid = cluster1.uuid
        }

        updateHostIommuState {
            uuid = kvm1.uuid
            state = HostIommuStateType.Enabled.toString()
        }

        updateHostIommuState {
            uuid = kvm2.uuid
            state = HostIommuStateType.Enabled.toString()
        }

        env.simulator(KVMConstant.KVM_START_VM_PATH){ HttpEntity<String> e ->
            KVMAgentCommands.StartVmResponse response = new KVMAgentCommands.StartVmResponse()
            response.setSuccess(true)
            return response
        }
    }


    void execTest() {
        dbf = bean(DatabaseFacade.class)

        nv_video_1 = queryPciDevice {
            conditions = ["pciDeviceAddress=06:00.0", "hostUuid=${kvm1.uuid}"]
        }[0] as PciDeviceInventory

        def tag_video_1 = "${PciDeviceSystemTags.PCI_DEVICE_TOKEN}::${nv_video_1.uuid}".toString() as String

        VmInstanceInventory vm1 = createVmInstance {
            name = "vm"
            imageUuid = env.inventoryByName("image1").uuid
            l3NetworkUuids = [env.inventoryByName("l3").uuid]
            instanceOfferingUuid = env.inventoryByName("instanceOffering").uuid
            systemTags = [tag_video_1]
        }

        PciDeviceOfferingInventory pciInv = createPciDeviceOffering {
            name = "test-pci"
            description = nv_video_1.description
            vendorId = nv_video_1.vendorId
            deviceId = nv_video_1.deviceId
            subvendorId = nv_video_1.subvendorId
            subdeviceId = nv_video_1.subdeviceId
        }
        double price_ = 100.0
        PriceInventory inv = createResourcePrice {
            timeUnit = "s"
            price = price_
            resourceName = BillingConstants.SPENDING_PCI_DEVICE
            systemTags = [String.format("%s::%s", BillingSystemTags.PRICE_GPU_OFFERING_UUID_TOKEN, pciInv.uuid)]
        }

        retryInSecs(3) {
            boolean exists = Q.New(PciDeviceUsageVO.class)
                    .eq(PciDeviceUsageVO_.pciDeviceUuid, nv_video_1.uuid)
                    .isExists()
            assert exists
        }

        TimeUnit.SECONDS.sleep(2)
        destroyVmInstance {
            uuid = vm1.uuid
        }

        expungeVmInstance {
            uuid = vm1.uuid
        }

        assert retryInSecs(3) {
            boolean exists = Q.New(PciDeviceUsageVO.class)
                    .eq(PciDeviceUsageVO_.pciDeviceUuid, nv_video_1.uuid)
                    .eq(PciDeviceUsageVO_.status, PciDeviceStatus.Active.toString())
                    .exists
            assert exists
        }

        PciDeviceBillingGenerator pciDeviceGenerator = bean(PciDeviceBillingGenerator.class)
        pciDeviceGenerator.generate(currentEnvSpec.session.accountUuid)

        BillingResourceLabelVO labelVO = Q.New(BillingResourceLabelVO.class)
                .eq(BillingResourceLabelVO_.resourceUuid, nv_video_1.uuid)
                .eq(BillingResourceLabelVO_.labelKey, BillingResourceLabelKey.HYPERVISORTYPE.toString())
                .find()

        assert labelVO.labelValue == vm1.hypervisorType

        PciDeviceBillingVO billingVO = Q.New(PciDeviceBillingVO.class)
                .eq(PciDeviceBillingVO_.resourceUuid, nv_video_1.uuid)
                .find()
        assert billingVO != null
        assert billingVO.resourceUuid == nv_video_1.uuid
        assert billingVO.accountUuid == currentEnvSpec.session.accountUuid
        assert billingVO.vmName == vm1.getName()
        assert billingVO.billingType == BillingType.GPU
        assert billingVO.startTime > 0
        assert billingVO.endTime > 0
        assert billingVO.spending > 0
        assert billingVO.hypervisorType == vm1.hypervisorType

        List<PciDeviceUsageHistoryVO> historyVOList = Q.New(PciDeviceUsageHistoryVO.class)
                .eq(PciDeviceUsageHistoryVO_.accountUuid, currentEnvSpec.session.accountUuid)
                .eq(PciDeviceUsageHistoryVO_.pciDeviceUuid, nv_video_1.uuid)
                .list()
        assert historyVOList.size() == 2

        List<PciDeviceUsageVO> usageVOList = Q.New(PciDeviceUsageVO.class)
                .eq(PciDeviceUsageVO_.accountUuid, currentEnvSpec.session.accountUuid)
                .eq(PciDeviceUsageVO_.pciDeviceUuid, nv_video_1.uuid)
                .list()
        assert usageVOList.size() == 0
    }

    void testQuery() {
        zqlQuery("query PciDevice where uuid in " +
                "(query PciDevicePciDeviceOfferingRef.pciDeviceUuid where pciDeviceOfferingUuid='fake')")
    }

    void testBillingSpendingCalculator() {
        SpendingStruct param = new SpendingStruct(
                accountUuid: currentEnvSpec.session.accountUuid,
                dateStart: 0,
                dateEnd: System.currentTimeMillis()
        )
        PciDeviceBillingSpendingCalculator calculator = bean(PciDeviceBillingSpendingCalculator.class)

        Spending spending = calculator.calculate(param)
        assert spending.spending > 0
        assert spending.dateEnd == param.dateEnd
        assert spending.dateStart == param.dateStart
        assert spending.spendingType == BillingConstants.SPENDING_PCI_DEVICE
        assert spending.details.size() == 1

        PciDeviceSpending details = spending.details.find{it.resourceUuid == nv_video_1.uuid} as PciDeviceSpending
        assert details.spending == spending.spending
        assert details.resourceUuid == nv_video_1.uuid
        assert details.resourceName == nv_video_1.description
        assert details.sizeInventory.size() == 1

        PciDeviceSpendingInventory inventory = details.sizeInventory.get(0)
        assert inventory.spending == spending.spending
        assert inventory.startTime > 0
        assert inventory.endTime > 0
    }

    void testBillingWithoutPciDevice(){
        nv_video_1 = queryPciDevice {
            conditions = ["pciDeviceAddress=06:00.0", "hostUuid=${kvm1.uuid}"]
        }[0] as PciDeviceInventory

        def tag_video_1 = "${PciDeviceSystemTags.PCI_DEVICE_TOKEN}::${nv_video_1.uuid}".toString() as String

        VmInstanceInventory vm1 = createVmInstance {
            name = "vm"
            imageUuid = env.inventoryByName("image1").uuid
            l3NetworkUuids = [env.inventoryByName("l3").uuid]
            instanceOfferingUuid = env.inventoryByName("instanceOffering").uuid
            systemTags = [tag_video_1]
        }

        retryInSecs(3) {
            boolean exists = Q.New(PciDeviceUsageVO.class)
                    .eq(PciDeviceUsageVO_.pciDeviceUuid, nv_video_1.uuid)
                    .isExists()
            assert exists
        }

        TimeUnit.SECONDS.sleep(2)
        destroyVmInstance {
            uuid = vm1.uuid
        }

        expungeVmInstance {
            uuid = vm1.uuid
        }

        assert retryInSecs(3) {
            boolean exists = Q.New(PciDeviceUsageVO.class)
                    .eq(PciDeviceUsageVO_.pciDeviceUuid, nv_video_1.uuid)
                    .eq(PciDeviceUsageVO_.status, PciDeviceStatus.Active.toString())
                    .exists
            assert exists
        }

        PciDeviceVO pciDeviceVO = Q.New(PciDeviceVO.class)
                .eq(PciDeviceVO_.uuid, nv_video_1.uuid)
                .find()

        dbf.remove(pciDeviceVO)

        BillingResourceLabelVO labelVO = Q.New(BillingResourceLabelVO.class)
                .eq(BillingResourceLabelVO_.resourceUuid, nv_video_1.uuid)
                .eq(BillingResourceLabelVO_.labelKey, BillingResourceLabelKey.HYPERVISORTYPE.toString())
                .find()

        dbf.remove(labelVO)

        boolean islabelExist = Q.New(BillingResourceLabelVO.class)
                .eq(BillingResourceLabelVO_.resourceUuid, nv_video_1.uuid)
                .eq(BillingResourceLabelVO_.labelKey, BillingResourceLabelKey.HYPERVISORTYPE.toString())
                .exists
        assert !islabelExist

        PciDeviceBillingGenerator pciDeviceGenerator = bean(PciDeviceBillingGenerator.class)
        pciDeviceGenerator.generate(currentEnvSpec.session.accountUuid)


        List<PciDeviceBillingVO> billingVOs = Q.New(PciDeviceBillingVO.class)
                .eq(PciDeviceBillingVO_.resourceUuid, nv_video_1.uuid)
                .list()

        PciDeviceBillingVO billingVO = billingVOs.get(1)
        assert billingVO != null
        assert billingVO.resourceUuid == nv_video_1.uuid
        assert billingVO.accountUuid == currentEnvSpec.session.accountUuid
        assert billingVO.vmName == vm1.getName()
        assert billingVO.billingType == BillingType.GPU
        assert billingVO.startTime > 0
        assert billingVO.endTime > 0
        assert billingVO.spending > 0
        assert billingVO.hypervisorType == vm1.hypervisorType

        List<PciDeviceUsageHistoryVO> historyVOList = Q.New(PciDeviceUsageHistoryVO.class)
                .eq(PciDeviceUsageHistoryVO_.accountUuid, currentEnvSpec.session.accountUuid)
                .eq(PciDeviceUsageHistoryVO_.pciDeviceUuid, nv_video_1.uuid)
                .list()
        assert historyVOList.size() == 4

        List<PciDeviceUsageVO> usageVOList = Q.New(PciDeviceUsageVO.class)
                .eq(PciDeviceUsageVO_.accountUuid, currentEnvSpec.session.accountUuid)
                .eq(PciDeviceUsageVO_.pciDeviceUuid, nv_video_1.uuid)
                .list()
        assert usageVOList.size() == 0
    }
}
