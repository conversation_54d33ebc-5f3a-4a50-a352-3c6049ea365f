package org.zstack.test.integration.premium.host

import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.host.*
import org.zstack.network.hostNetworkInterface.HostNetworkBondingInventory
import org.zstack.network.hostNetworkInterface.HostNetworkBondingVO
import org.zstack.network.hostNetworkInterface.HostNetworkBondingVO_
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceInventory
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO_
import org.zstack.sdk.GetHostNetworkFactsResult
import org.zstack.sdk.GetInterfaceServiceTypeStatisticResult
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase

class TestGetServiceTypeStatisticCase extends PremiumSubCase {
    EnvSpec env
    org.zstack.sdk.HostInventory host
    GetHostNetworkFactsResult facts
    GetInterfaceServiceTypeStatisticResult serverTypeResult
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = PremiumHostEnv.oneHostEnv()
    }

    @Override
    void test() {
        env.create {
            host = env.inventoryByName("kvm") as org.zstack.sdk.HostInventory
            dbf = bean(DatabaseFacade.class)
            testGetInterfaceServiceTypeStatistic()
        }
    }

    void testGetInterfaceServiceTypeStatistic() {
        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> entity, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setUuid(Platform.getUuid())
            inv1.setHostUuid(host.getUuid())
            inv1.setInterfaceName("enp101s0f0")
            inv1.setSpeed(10000L)
            inv1.setIpAddresses(Collections.singletonList("*********/16"))
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:8c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            reply.nics = [inv1]

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setUuid(Platform.getUuid())
            bond0.setHostUuid(host.getUuid())
            bond0.setBondingName("bond0");
            bond0.setBondingType("noBridge")
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE.toString());
            bond0.setMode("802.3ad");
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setSlaves(reply.nics);
            bond0.setIpAddresses(Collections.singletonList("**********17/16"))
            bond0.setGateway("**********");

            reply.bondings = [bond0]

            return reply;
        }

        facts = getHostNetworkFacts {
            hostUuid = host.uuid
        } as GetHostNetworkFactsResult
        assert facts.nics.size() == 1
        assert facts.bondings.size() == 1

        // get service type statistic for interface
        List<HostNetworkInterfaceVO> interfaceVOS = Q.New(HostNetworkInterfaceVO.class)
                .eq(HostNetworkInterfaceVO_.hostUuid, host.getUuid())
                .list()

        setServiceTypeOnHostNetworkInterface {
            sessionId = adminSession()
            interfaceUuids = [interfaceVOS.get(0).uuid]
            vlanIds = [10,20]
            serviceTypes = ["TenantNetwork"]
        }

        serverTypeResult = getInterfaceServiceTypeStatistic {
            interfaceUuid = interfaceVOS.get(0).uuid
            hostUuid = host.uuid
        }
        List<ServiceTypeStatisticData> serviceData = serverTypeResult.serviceTypeStatistics
        assert serviceData.size() == 2
        assert serverTypeResult.total == null

        serverTypeResult = getInterfaceServiceTypeStatistic {
            interfaceUuid = interfaceVOS.get(0).uuid
            hostUuid = host.uuid
            replyWithCount = true
        }
        List<ServiceTypeStatisticData> serviceData1 = serverTypeResult.serviceTypeStatistics
        long serviceDataCount1 = serverTypeResult.total
        assert serviceData1.size() == 2
        assert serviceDataCount1 == 2

        // get service type statistic for bonding
        List<HostNetworkBondingVO> bondingVOS = Q.New(HostNetworkBondingVO.class)
                .eq(HostNetworkBondingVO_.hostUuid, host.getUuid())
                .list()

        setServiceTypeOnHostNetworkBonding {
            sessionId = adminSession()
            bondingUuids = [bondingVOS.get(0).uuid]
            serviceTypes = ["TenantNetwork", "StorageNetwork"]
        }

        serverTypeResult = getInterfaceServiceTypeStatistic {
            interfaceUuid = bondingVOS.get(0).uuid
            hostUuid = host.uuid
            replyWithCount = true
        }
        List<ServiceTypeStatisticData> serviceData2 = serverTypeResult.serviceTypeStatistics
        long serviceDataCount2 = serverTypeResult.total
        assert serviceData2.size() == 1
        assert serviceData2.get(0).serviceTypes.contains("TenantNetwork")
        assert serviceData2.get(0).serviceTypes.contains("StorageNetwork")
        assert serviceDataCount2 == 1

        serverTypeResult = getInterfaceServiceTypeStatistic {
            interfaceUuid = bondingVOS.get(0).uuid
            hostUuid = host.uuid
            serviceType = Collections.singletonList(ServiceTypeStatisticConstants.ServiceType.TENANT_NETWORK)
            replyWithCount = true
        }
        List<ServiceTypeStatisticData> serviceData2_1 = serverTypeResult.serviceTypeStatistics
        assert serviceData2_1.size() == 1
        assert serviceData2_1.get(0).serviceTypes.contains("TenantNetwork")
        assert serviceData2_1.get(0).serviceTypes.contains("StorageNetwork")

        serverTypeResult = getInterfaceServiceTypeStatistic {
            interfaceUuid = bondingVOS.get(0).uuid
            hostUuid = host.uuid
            serviceType = Arrays.asList(ServiceTypeStatisticConstants.ServiceType.TENANT_NETWORK,ServiceTypeStatisticConstants.ServiceType.STORAGE_NETWORK)
            replyWithCount = true
        }
        List<ServiceTypeStatisticData> serviceData2_2 = serverTypeResult.serviceTypeStatistics
        assert serviceData2_2.size() == 1
        assert serviceData2_2.get(0).serviceTypes.contains("TenantNetwork")
        assert serviceData2_2.get(0).serviceTypes.contains("StorageNetwork")

        serverTypeResult = getInterfaceServiceTypeStatistic {
            interfaceUuid = bondingVOS.get(0).uuid
            hostUuid = host.uuid
            serviceType = Arrays.asList(ServiceTypeStatisticConstants.ServiceType.TENANT_NETWORK,ServiceTypeStatisticConstants.ServiceType.MANAGEMENT_NETWORK)
            replyWithCount = true
        }
        List<ServiceTypeStatisticData> serviceData2_3 = serverTypeResult.serviceTypeStatistics
        assert serviceData2_3.size() == 1

        serverTypeResult = getInterfaceServiceTypeStatistic {
            interfaceUuid = bondingVOS.get(0).uuid
            hostUuid = host.uuid
            serviceType = Arrays.asList(ServiceTypeStatisticConstants.ServiceType.BACKUP_NETWORK)
            replyWithCount = true
        }
        List<ServiceTypeStatisticData> serviceData2_4 = serverTypeResult.serviceTypeStatistics
        assert serviceData2_4.size() == 0



        // get service type statistic for host
        serverTypeResult = getInterfaceServiceTypeStatistic {
            hostUuid = host.uuid
            replyWithCount = true
        }
        List<ServiceTypeStatisticData> serviceData3 = serverTypeResult.serviceTypeStatistics
        long serviceDataCount3 = serverTypeResult.total
        assert serviceData3.size() == 3
        assert serviceDataCount3 == 3

        // order by interfaceName and vlanId when sortBy is vlanId  expected: bond0 bond0.10 bond0.20 enp101s0f0.10 enp101s0f0.20
        setServiceTypeOnHostNetworkBonding {
            sessionId = adminSession()
            bondingUuids = [bondingVOS.get(0).uuid]
            vlanIds = [10,20]
            serviceTypes = ["TenantNetwork"]
        }
        serverTypeResult = getInterfaceServiceTypeStatistic {
            hostUuid = host.uuid
            sortBy = "VlanId"
        }
        List<ServiceTypeStatisticData> serviceData4 = serverTypeResult.serviceTypeStatistics
        assert serviceData4.size() == 5
        assert serviceData4.get(0).interfaceName == "bond0"
        assert serviceData4.get(3).interfaceName == "enp101s0f0"

        // interfaceOnly
        serverTypeResult = getInterfaceServiceTypeStatistic {
            hostUuid = host.uuid
            interfaceType = "Interface"
        }
        List<ServiceTypeStatisticData> serviceData5 = serverTypeResult.serviceTypeStatistics
        assert serviceData5.size() == 2

        // bondingOnly
        serverTypeResult = getInterfaceServiceTypeStatistic {
            hostUuid = host.uuid
            interfaceType = "Bonding"
        }
        List<ServiceTypeStatisticData> serviceData6 = serverTypeResult.serviceTypeStatistics
        assert serviceData6.size() == 3

    }
    
    @Override
    void clean() {
        env.delete()
    }
}
