package org.zstack.test.integration.premium.vdpa

import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.cluster.MevocoClusterGlobalConfig
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.db.Q
import org.zstack.header.host.*
import org.zstack.header.vdpa.VmVdpaNicVO
import org.zstack.header.vdpa.VmVdpaNicVO_
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.pciDevice.*
import org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit

/**
 * <AUTHOR>
 * @date 2021/10/1
 * since
 */
class TestMigrateVmWithVdpaNicCase extends PremiumSubCase{
    EnvSpec env
    ImageInventory image
    L3NetworkInventory l3
    L3NetworkInventory l3_2
    ClusterInventory cluster1
    PrimaryStorageInventory ps1
    ClusterInventory cluster2
    PrimaryStorageInventory ps2
    InstanceOfferingInventory offer
    VmInstanceInventory vm

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(5)
            }

            cephBackupStorage {
                name="ceph-bs-1"
                description="First Ceph Backup Storage"
                totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                availableCapacity= SizeUnit.GIGABYTE.toByte(100)
                url = "/bk-1"
                fsid ="7ff218d9-f525-435f-8a40-3618d1772a64"
                monUrls = ["root:password@127.0.0.11/?monPort=7777"]

                image {
                    name = "test-iso"
                    url  = "http://zstack.org/download/test.iso"
                }
                image {
                    name = "image"
                    url  = "http://zstack.org/download/image.qcow2"
                }
                image {
                    name = "image1"
                    url  = "http://zstack.org/download/image1.qcow2"
                }
            }

            cephBackupStorage {
                name="ceph-bs-2"
                description="Second Ceph Backup Storage"
                totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                availableCapacity= SizeUnit.GIGABYTE.toByte(100)
                url = "/bk-2"
                fsid ="7ff218d9-f525-435f-8a40-3618d1772a65"
                monUrls = ["root:password@127.0.0.12/?monPort=7777"]
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster1"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1_1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm1_2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("ceph-pri-1")
                }

                cluster {
                    name = "cluster2"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm2_1"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm2_2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("ceph-pri-2")
                }


                cephPrimaryStorage {
                    name="ceph-pri-1"
                    description="First Ceph Primary Storage"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                    availableCapacity= SizeUnit.GIGABYTE.toByte(100)
                    url="ceph://pri-1"
                    fsid="7ff218d9-f525-435f-8a40-3618d1772a64"
                    monUrls=["root:password@127.0.0.11/?monPort=7777"]
                }

                cephPrimaryStorage {
                    name="ceph-pri-2"
                    description="Second Ceph Primary Storage"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                    availableCapacity= SizeUnit.GIGABYTE.toByte(100)
                    url="ceph://pri-2"
                    fsid="7ff218d9-f525-435f-8a40-3618d1772a65"
                    monUrls=["root:password@127.0.0.12/?monPort=7777"]
                    rootVolumePoolName = "root-volume"
                    dataVolumePoolName = "data-volume"
                    imageCachePoolName = "image-cache"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    vSwitchType = "OvsDpdk"
                    physicalInterface = "bond0"
                    l3Network {
                        name = "l3"
                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }
                    l3Network {
                        name = "l3-2"
                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }
                }

                attachBackupStorage("ceph-bs-1")
                attachBackupStorage("ceph-bs-2")
            }
        }
    }

    @Override
    void test() {
        env.create {
            initEnv()
            TestLiveMigrateVmWithVdpa()
            //TestStorageLiveMigrateVmWithVdpa()
            TestLiveMigrateVmWithVdpaFailed()
        }
    }

    void changeCluster2PciDeviceAddress() {
        KVMHostInventory kvm2 = env.inventoryByName("kvm2_1")
        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            PciDeviceTO mlnx_cx5_pf1 = PciDeviceTOExampleMaker.MLNX_CX5_VIRTUALIZED_PF1()
            mlnx_cx5_pf1.setPciDeviceAddress("0f:00.0")
            PciDeviceTO mlnx_cx5_pf2 = PciDeviceTOExampleMaker.MLNX_CX5_VIRTUALIZED_PF2()
            mlnx_cx5_pf2.setPciDeviceAddress("0f:00.1")
            PciDeviceTO mlnx_cx5_vf1 = PciDeviceTOExampleMaker.MLNX_CX5_VF1()
            mlnx_cx5_vf1.setParentAddress("0f:00.0")
            mlnx_cx5_vf1.setPciDeviceAddress("0f:00.2")
            PciDeviceTO mlnx_cx5_vf2 = PciDeviceTOExampleMaker.MLNX_CX5_VF2()
            mlnx_cx5_vf2.setParentAddress("0f:00.1")
            mlnx_cx5_vf2.setPciDeviceAddress("0f:00.3")
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
                    PciDeviceTOExampleMaker.GT710B_AUDIO(),
                    mlnx_cx5_pf1,
                    mlnx_cx5_pf2,
                    mlnx_cx5_vf1,
                    mlnx_cx5_vf2,
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> entity, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory();
            inv1.setInterfaceName("enp101s0f0");
            inv1.setSpeed(10000L);
            inv1.setCarrierActive(true);
            inv1.setMac("ac:1f:6b:93:6c:8c");
            inv1.setPciDeviceAddress("0f:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory();
            inv2.setInterfaceName("enp101s0f1");
            inv2.setSpeed(10000L);
            inv2.setCarrierActive(true);
            inv2.setMac("ac:1f:6b:93:6c:8d");
            inv2.setPciDeviceAddress("0f:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2];

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.OVS_BONDING_TYPE.toString());
            bond0.setMode("active-backup 1");
            bond0.setXmitHashPolicy("layer 3+4");
            bond0.setSlaves(reply.nics);

            reply.bondings = [bond0]

            return reply;
        }

        List<KVMHostInventory> hostInCluster = queryHost {
            conditions = ["clusterUuid=${cluster2.uuid}".toString()]
        } as List<KVMHostInventory>

        for (KVMHostInventory host : hostInCluster) {
            changeHostState {
                uuid = host.getUuid()
                stateEvent = HostStateEvent.maintain.toString()
            }

            changeHostState {
                uuid = host.getUuid()
                stateEvent = HostStateEvent.enable.toString()
            }

            reconnectHost {
                uuid = host.getUuid()
            }
        }

        getClusterHostNetworkFacts {
            clusterUuid = cluster2.uuid
        }

        retryInSecs {
            assert Q.New(PciDeviceVO.class)
                    .eq(PciDeviceVO_.hostUuid, kvm2.uuid)
                    .eq(PciDeviceVO_.type, PciDeviceType.Ethernet_Controller)
                    .eq(PciDeviceVO_.pciDeviceAddress, "0f:00.0")
                    .count() == 1
        }
    }

    void enableOvsDpdk() {
        List<KVMHostInventory> allHosts = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : allHosts) {
            // set host under cluster to maintenance mode
            updateHostIommuState {
                delegate.uuid = host.uuid
                delegate.state = "Enabled"
            }
            changeHostState {
                uuid = host.getUuid()
                stateEvent = HostStateEvent.maintain.toString()
            }
        }

        List<ClusterInventory> allClusters = queryCluster {} as List<ClusterInventory>
        for (ClusterInventory cluster : allClusters) {
            // enable OVS_DPDK_SUPPORT
            updateResourceConfig {
                category = MevocoClusterGlobalConfig.CATEGORY
                name = MevocoClusterGlobalConfig.HUGEPAGE_ENABLE.name
                value = "true"
                resourceUuid = cluster.uuid
            }
            updateResourceConfig {
                category = MevocoClusterGlobalConfig.CATEGORY
                name = MevocoClusterGlobalConfig.OVS_DPDK_SUPPORT.name
                value = "true"
                resourceUuid = cluster.uuid
            }
        }

        for (KVMHostInventory host : allHosts) {
            // set host under cluster to enable mode
            changeHostState {
                uuid = host.getUuid()
                stateEvent = HostStateEvent.enable.toString()
            }

            reconnectHost {
                uuid = host.getUuid()
            }
        }

        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
        }
    }

    void initEnv() {
        updateGlobalConfig {
            category = "networkService"
            name = "enableVHostUser"
            value = "false"
        }
        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.MLNX_CX5_VIRTUALIZED_PF1(),
                    PciDeviceTOExampleMaker.MLNX_CX5_VIRTUALIZED_PF2(),
                    PciDeviceTOExampleMaker.MLNX_CX5_VF1(),
                    PciDeviceTOExampleMaker.MLNX_CX5_VF2(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> entity, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory();
            inv1.setInterfaceName("enp101s0f0");
            inv1.setSpeed(10000L);
            inv1.setCarrierActive(true);
            inv1.setMac("ac:1f:6b:93:6c:8c");
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory();
            inv2.setInterfaceName("enp101s0f1");
            inv2.setSpeed(10000L);
            inv2.setCarrierActive(true);
            inv2.setMac("ac:1f:6b:93:6c:8d");
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2];

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.OVS_BONDING_TYPE.toString());
            bond0.setMode("active-backup 1");
            bond0.setXmitHashPolicy("layer 3+4");
            bond0.setSlaves(reply.nics);

            reply.bondings = [bond0]

            return reply;
        }

        enableOvsDpdk()

        L2NetworkInventory l2 = env.inventoryByName("l2")
        l3 = env.inventoryByName("l3")
        l3_2 = env.inventoryByName("l3-2")
        image = env.inventoryByName("image1")
        offer = env.inventoryByName("instanceOffering")
        cluster1 = env.inventoryByName("cluster1")
        cluster2 = env.inventoryByName("cluster2")
        ps1 = env.inventoryByName("ceph-pri-1")
        ps2 = env.inventoryByName("ceph-pri-2")
        DiskOfferingInventory diskOffering= env.inventoryByName("diskOffering")

        attachL2NetworkToCluster {
            l2NetworkUuid = l2.getUuid()
            clusterUuid = cluster1.getUuid()
        }
        attachL2NetworkToCluster {
            l2NetworkUuid = l2.getUuid()
            clusterUuid = cluster2.getUuid()
        }

        // create vm with vdpa nic
        vm = createVmInstance {
            name = "vm"
            clusterUuid = cluster1.uuid
            instanceOfferingUuid = offer.uuid
            defaultL3NetworkUuid = l3.uuid
            l3NetworkUuids = [l3.uuid, l3_2.uuid]
            imageUuid = image.uuid
            rootDiskOfferingUuid = diskOffering.uuid
            dataDiskOfferingUuids = [diskOffering.uuid]
        } as VmInstanceInventory

        retryInSecs {
            assert Q.New(PciDeviceVO.class)
                    .eq(PciDeviceVO_.vmInstanceUuid, vm.uuid)
                    .eq(PciDeviceVO_.type, PciDeviceType.Ethernet_Controller)
                    .count() == 2

            assert Q.New(VmVdpaNicVO.class)
                    .eq(VmVdpaNicVO_.vmInstanceUuid, vm.uuid)
                    .notNull(VmVdpaNicVO_.srcPath)
                    .notNull(VmVdpaNicVO_.pciDeviceUuid)
                    .count() == 2
        }

        changeCluster2PciDeviceAddress()
    }

    void TestLiveMigrateVmWithVdpa() {

        KVMAgentCommands.GenerateVdpaCmd genCmd = null
        env.afterSimulator(KVMConstant.KVM_GENERATE_VDPA_PATH) { rsp, HttpEntity<String> e ->
            genCmd = json(e.body, KVMAgentCommands.GenerateVdpaCmd.class)
            return rsp
        }

        KVMAgentCommands.DeleteVdpaCmd deleteCmd = null
        env.afterSimulator(KVMConstant.KVM_DELETE_VDPA_PATH) { rsp, HttpEntity<String> e ->
            deleteCmd = json(e.body, KVMAgentCommands.DeleteVdpaCmd.class)
            return rsp
        }

        VmInstanceInventory oldVmInv = vm

        List<String> hostUuids = Q.New(HostVO.class).select(HostVO_.uuid).eq(HostVO_.clusterUuid, vm.getClusterUuid()).notEq(HostVO_.uuid, vm.getHostUuid()).listValues()
        def toHostUuid = hostUuids[0]

        vm = migrateVm {
            vmInstanceUuid = vm.uuid
            hostUuid = toHostUuid
        } as VmInstanceInventory

        assert oldVmInv.hostUuid != vm.hostUuid

        assert genCmd.vmUuid == vm.uuid
        for (KVMAgentCommands.NicTO to : genCmd.nics) {
            assert to.getSrcPath() != null
            assert to.getPciDeviceAddress() != null
        }

        assert deleteCmd.vmUuid == vm.uuid

        retryInSecs(15) {
            def count = Q.New(PciDeviceVO.class)
                    .eq(PciDeviceVO_.vmInstanceUuid, vm.uuid)
                    .eq(PciDeviceVO_.type, PciDeviceType.Ethernet_Controller)
                    .eq(PciDeviceVO_.status, org.zstack.pciDevice.PciDeviceStatus.Attached)
                    .eq(PciDeviceVO_.hostUuid, vm.hostUuid)
                    .count()
            assert count == 1 || count ==2

            count =  Q.New(VmVdpaNicVO.class)
                    .eq(VmVdpaNicVO_.vmInstanceUuid, vm.uuid)
                    .notNull(VmVdpaNicVO_.srcPath)
                    .notNull(VmVdpaNicVO_.pciDeviceUuid)
                    .count()
            assert count == 1 || count ==2
        }

        String oldHostUuid = oldVmInv.hostUuid
        vm = migrateVm {
            vmInstanceUuid = vm.uuid
            hostUuid = oldHostUuid
        }

        vm = migrateVm {
            vmInstanceUuid = vm.uuid
            hostUuid = toHostUuid
        }
    }

    void TestStorageLiveMigrateVmWithVdpa() {

        KVMAgentCommands.GenerateVdpaCmd genCmd = null
        env.afterSimulator(KVMConstant.KVM_GENERATE_VDPA_PATH) { rsp, HttpEntity<String> e ->
            genCmd = json(e.body, KVMAgentCommands.GenerateVdpaCmd.class)
            return rsp
        }

        KVMAgentCommands.DeleteVdpaCmd deleteCmd = null
        env.afterSimulator(KVMConstant.KVM_DELETE_VDPA_PATH) { rsp, HttpEntity<String> e ->
            deleteCmd = json(e.body, KVMAgentCommands.DeleteVdpaCmd.class)
            return rsp
        }

        VmInstanceInventory oldVmInv = vm

        vm = primaryStorageMigrateVm {
            vmInstanceUuid = vm.uuid
            dstPrimaryStorageUuid = ps2.uuid
            withSnapshots = false
        } as VmInstanceInventory

        retryInSecs() {
            assert oldVmInv.hostUuid != vm.hostUuid

            assert genCmd.vmUuid == vm.uuid
            assert deleteCmd.vmUuid == vm.uuid
        }

        retryInSecs(15) {
            def count = Q.New(PciDeviceVO.class)
                    .eq(PciDeviceVO_.vmInstanceUuid, vm.uuid)
                    .eq(PciDeviceVO_.type, PciDeviceType.Ethernet_Controller)
                    .eq(PciDeviceVO_.status, org.zstack.pciDevice.PciDeviceStatus.Attached)
                    .eq(PciDeviceVO_.hostUuid, vm.hostUuid)
                    .count()
            assert count == 1 || count ==2

            count =  Q.New(VmVdpaNicVO.class)
                    .eq(VmVdpaNicVO_.vmInstanceUuid, vm.uuid)
                    .notNull(VmVdpaNicVO_.srcPath)
                    .notNull(VmVdpaNicVO_.pciDeviceUuid)
                    .notNull(VmVdpaNicVO_.lastPciDeviceUuid)
                    .count()
            assert count == 1 || count ==2
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    void TestLiveMigrateVmWithVdpaFailed() {
        L2NetworkInventory l2 = env.inventoryByName("l2")
        l3 = env.inventoryByName("l3")
        l3_2 = env.inventoryByName("l3-2")
        image = env.inventoryByName("image1")
        offer = env.inventoryByName("instanceOffering")
        cluster1 = env.inventoryByName("cluster1")
        cluster2 = env.inventoryByName("cluster2")
        ps1 = env.inventoryByName("ceph-pri-1")
        ps2 = env.inventoryByName("ceph-pri-2")
        DiskOfferingInventory diskOffering= env.inventoryByName("diskOffering")

        VmInstanceInventory oldVmInv = vm
        List<String> hostUuids = Q.New(HostVO.class).select(HostVO_.uuid).eq(HostVO_.clusterUuid, vm.getClusterUuid()).notEq(HostVO_.uuid, vm.getHostUuid()).listValues()
        def toHostUuid = hostUuids[0]

        // create vm with vdpa nic
        VmInstanceInventory vm_new = createVmInstance {
            name = "vm"
            clusterUuid = oldVmInv.getClusterUuid()
            hostUuid = toHostUuid
            instanceOfferingUuid = offer.uuid
            defaultL3NetworkUuid = l3.uuid
            l3NetworkUuids = [l3.uuid, l3_2.uuid]
            imageUuid = image.uuid
            rootDiskOfferingUuid = diskOffering.uuid
        } as VmInstanceInventory

        List<VmVdpaNicVO> oldVmVdpas = Q.New(VmVdpaNicVO.class).eq(VmVdpaNicVO_.vmInstanceUuid, vm_new.uuid).list()
        expect(AssertionError.class) {
            vm = migrateVm {
                vmInstanceUuid = vm_new.uuid
                hostUuid = oldVmInv.hostUuid
            } as VmInstanceInventory
        }
        def pciDeviceUuids = oldVmVdpas.collect{it.pciDeviceUuid}.toSet()
        List<VmVdpaNicVO> newVmVdpas = Q.New(VmVdpaNicVO.class).eq(VmVdpaNicVO_.vmInstanceUuid, vm_new.uuid).list()
        assert newVmVdpas.stream().allMatch{it -> pciDeviceUuids.contains(it.getPciDeviceUuid())}

        destroyVmInstance {
            uuid = vm_new.uuid
        }
        List<PciDeviceVO> pciDeviceVOS = Q.New(PciDeviceVO.class).in(PciDeviceVO_.uuid, pciDeviceUuids).list()
        assert pciDeviceVOS.stream().allMatch{it-> it.getVmInstanceUuid() == null}
    }
}
