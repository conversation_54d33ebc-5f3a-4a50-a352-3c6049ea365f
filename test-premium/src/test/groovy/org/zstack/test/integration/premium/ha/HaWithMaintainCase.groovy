package org.zstack.test.integration.premium.ha

import org.springframework.http.HttpEntity
import org.zstack.compute.host.HostGlobalConfig
import org.zstack.core.db.Q
import org.zstack.ha.HaGlobalConfig
import org.zstack.ha.HaKvmHostSiblingChecker
import org.zstack.ha.SelfFencerStrategy
import org.zstack.ha.VmHaLevel
import org.zstack.header.host.HostState
import org.zstack.header.host.HostStateEvent
import org.zstack.header.host.HostStatus
import org.zstack.header.host.HostVO
import org.zstack.header.host.HostVO_
import org.zstack.header.vm.VmInstanceState
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.sdk.HostInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

class HaWithMaintainCase extends PremiumSubCase {
    EnvSpec env
    VmInstanceInventory vm
    HostInventory host1
    HostInventory host2
    HostInventory host3

    List<String> killHosts = []
    String slibingHostUuid

    @Override
    void environment() {
        env = VmHaGCEnv.fourClusterWithSameNFSPrimaryStorageEnv()
    }

    @Override
    void test() {
        env.create {
            prepare()
            changeHostToMaintain(host2)
            disconnect(host1)
            testVmHa1()
            disconnect(host3)
            testVmHa2()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    void prepare() {
        vm = env.inventoryByName("vm") as VmInstanceInventory
        host1 = env.inventoryByName("kvm1") as HostInventory
        host2 = env.inventoryByName("kvm2") as HostInventory
        host3 = env.inventoryByName("kvm3") as HostInventory

        HaGlobalConfig.SELF_FENCER_STRATEGY.updateValue(SelfFencerStrategy.Force.toString())
        HaGlobalConfig.ALL.updateValue(true)
        HaGlobalConfig.HOST_CHECK_INTERVAL.updateValue(1)
        HaGlobalConfig.HOST_CHECK_MAX_ATTEMPTS.updateValue(1)
        HaGlobalConfig.HOST_CHECK_SUCCESS_INTERVAL.updateValue(1)
        HaGlobalConfig.HOST_CHECK_SUCCESS_TIMES.updateValue(1)
        HostGlobalConfig.AUTO_RECONNECT_ON_ERROR.updateValue(false)
        HostGlobalConfig.PING_HOST_INTERVAL.updateValue(1)
        HaGlobalConfig.NEVER_STOP_VM_FAILURE_RETRY_DELAY.updateValue(1)
        HaGlobalConfig.NEVER_STOP_VM_SCAN_INTERVAL.updateValue(5)
        HaGlobalConfig.SUPPORT_HA_SLIBING_CROSS_CLUSTERS.updateValue(true)

        setVmInstanceHaLevel {
            uuid = vm.uuid
            level = VmHaLevel.NeverStop.toString()
        }
    }

    void changeHostToMaintain(HostInventory host) {
        changeHostState {
            uuid = host.uuid
            stateEvent = HostStateEvent.maintain
        }

        slibingHostUuid = null
        env.afterSimulator(HaKvmHostSiblingChecker.SCAN_HOST_PATH) { HaKvmHostSiblingChecker.ScanRsp rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, HaKvmHostSiblingChecker.ScanCmd.class)
            slibingHostUuid = cmd.slibingHostUuid
            return rsp
        }
    }

    void disconnect(HostInventory host) {
        env.afterSimulator(KVMConstant.KVM_CONNECT_PATH) { KVMAgentCommands.AgentResponse rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.ConnectCmd.class)
            rsp.success = false
            if (cmd.hostUuid == host.uuid || cmd.hostUuid in killHosts) {
                rsp.success = false
                if (!killHosts.contains(cmd.hostUuid)) {
                    killHosts.add(cmd.hostUuid)
                }
            }
            return rsp
        }

        slibingHostUuid = null
        env.afterSimulator(HaKvmHostSiblingChecker.SCAN_HOST_PATH) { HaKvmHostSiblingChecker.ScanRsp rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, HaKvmHostSiblingChecker.ScanCmd.class)
            slibingHostUuid = cmd.slibingHostUuid
            return rsp
        }

        expect(AssertionError.class) {
            reconnectHost {
                uuid = host.uuid
            }
        }

        retryInSecs {
            assert Q.New(HostVO.class).select(HostVO_.status).eq(HostVO_.uuid, host.uuid).findValue() == HostStatus.Disconnected
        }
    }

    // disconnect host1, maintain host2,  vm ha to host3
    void testVmHa1() {
        assert host2.status == HostStatus.Connected.toString()
        assert host3.status == HostStatus.Connected.toString()
        retryInSecs(6) {
            vm = queryVmInstance {
                conditions = ["uuid=${vm.uuid}"]
            }[0] as VmInstanceInventory
            assert VmInstanceState.Running.toString() == vm.state
        }

        assert vm.hostUuid == host3.uuid
        assert slibingHostUuid == host3.uuid
    }

    // disconnect host1, host3, maintain host2, vm can not ha
    void testVmHa2() {
        sleep 1000
        assert host2.status == HostStatus.Connected.toString()
        retryInSecs(6) {
            vm = queryVmInstance {
                conditions = ["uuid=${vm.uuid}"]
            }[0] as VmInstanceInventory
            assert VmInstanceState.Running.toString() != vm.state
        }

        assert slibingHostUuid == null
    }
}
