package org.zstack.test.integration.premium.vpc.policyRoute

import org.springframework.http.HttpEntity
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.policyRoute.PolicyRouteCommands
import org.zstack.policyRoute.PolicyRouteConstants
import org.zstack.policyRoute.PolicyRouteRuleSetConfigProxy
import org.zstack.policyRoute.PolicyRouteRuleSetL3RefVO
import org.zstack.policyRoute.PolicyRouteRuleSetVO
import org.zstack.policyRoute.PolicyRouteRuleVO
import org.zstack.policyRoute.PolicyRouteTableConfigProxy
import org.zstack.policyRoute.PolicyRouteTableRouteEntryVO
import org.zstack.policyRoute.PolicyRouteTableVO
import org.zstack.policyRoute.PolicyRouteTableVO_
import org.zstack.policyRoute.PolicyRouteType
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.utils.network.NetworkUtils

class EgressWhereComeFromCase extends PremiumSubCase {
    EnvPremiumSpec env
    DatabaseFacade dbf
    VirtualRouterVmInventory vr
    L3NetworkInventory l3
    PolicyRouteRuleInventory rule
    PolicyRouteTableConfigProxy tcp
    PolicyRouteRuleSetConfigProxy rcp

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = PolicyRouteEnv.VpcEnv()
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        tcp = bean(PolicyRouteTableConfigProxy.class)
        rcp = bean(PolicyRouteRuleSetConfigProxy.class)
        env.create {
            testEnableEgressWhereComeFromWithoutAdditionalPublicNic()
            testDeleteVpc()
            testDeleteRuleSetWithNoRouteTable()
            testEnableSystemPolicyRouteWithUserPolicyRoute()
            testVpcManagementNicDifferentFromPublic()
        }
    }

    void testEnableEgressWhereComeFromWithoutAdditionalPublicNic(){
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr")
        L3NetworkInventory pub_1 = env.inventoryByName("pubL3-1")
        L3NetworkInventory pub_2 = env.inventoryByName("pubL3-2")
        L3NetworkInventory pub_3 = env.inventoryByName("pubL3-3")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")
        L3NetworkInventory l3_3 = env.inventoryByName("l3-3")

        VirtualRouterVmInventory vpc1 = createVpcVRouter {
            name = "vpc-1"
            virtualRouterOfferingUuid = offering.uuid
        }

        int syncCount = 0
        PolicyRouteCommands.ApplyPolicyRouteCmd cmd = null
        env.simulator(PolicyRouteConstants.POLICY_ROUTE_SYNC) { HttpEntity<String> e, EnvSpec spec ->
            syncCount++
            cmd = JSONObjectUtil.toObject(e.body, PolicyRouteCommands.ApplyPolicyRouteCmd.class)
            return new PolicyRouteCommands.ApplyPolicyRouteRsp()
        }
        createPolicyRouteRuleSet {
            vRouterUuid = vpc1.uuid
            name = "system"
            type = PolicyRouteType.EgressWhereComeFrom.toString()
        }
        assert syncCount == 0
        List<String> ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 1
        PolicyRouteRuleSetVO defaultRuleSet = dbf.findByUuid(ruleSetUuids.get(0), PolicyRouteRuleSetVO.class)
        assert defaultRuleSet.getRules().size() == 0
        assert defaultRuleSet.type == PolicyRouteType.EgressWhereComeFrom
        List<String> tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 0

        /* there is no additional public network, no policy route rules change */
        syncCount = 0
        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpc1.uuid
        }
        assert syncCount == 0
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 1
        defaultRuleSet = dbf.findByUuid(ruleSetUuids.get(0), PolicyRouteRuleSetVO.class)
        assert defaultRuleSet.getRules().size() == 0
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 0

        /* 1 additional public nic will add:
        *   1 ruleset (including 1 rule, 1 l3Ref),
        *   1 routeTable(1 default route entry +  1 private nic * direct route + 1 public nic * direct route)   */
        cmd = null
        attachL3NetworkToVm {
            l3NetworkUuid = pub_2.uuid
            vmInstanceUuid = vpc1.uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 2
        defaultRuleSet = dbf.findByUuid(defaultRuleSet.getUuid(), PolicyRouteRuleSetVO.class)
        assert defaultRuleSet.getRules().size() == 0
        String pubRuleSetUuid2 = ruleSetUuids.get(0).equals(defaultRuleSet.getUuid()) ? ruleSetUuids.get(1) : ruleSetUuids.get(0)
        PolicyRouteRuleSetVO pubRuleSet2 = dbf.findByUuid(pubRuleSetUuid2, PolicyRouteRuleSetVO.class)
        assert pubRuleSet2.type == PolicyRouteType.EgressWhereComeFrom
        assert pubRuleSet2.rules.size() == 1
        assert pubRuleSet2.l3Refs.size() == 1
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 1
        PolicyRouteTableVO pubTable2 = dbf.findByUuid(tableUuids.get(0), PolicyRouteTableVO.class)
        assert pubTable2.type == PolicyRouteType.EgressWhereComeFrom
        assert pubTable2.routes.size() == 3
        assert cmd != null
        assert cmd.markConntrack
        assert cmd.routes.size() == 3
        boolean defaultRoute = false
        boolean routeForL31 = false
        for (PolicyRouteCommands.RouteInfo route : cmd.routes) {
            if (route.destinationCidr == "0.0.0.0/0") {
                defaultRoute = true
                assert route.nextHopIp != null
            } else if (route.destinationCidr == NetworkUtils.fmtCidr(l3_1.getIpRanges().get(0).getNetworkCidr())) {
                routeForL31 = true
            } else if (route.destinationCidr == NetworkUtils.fmtCidr(pub_1.getIpRanges().get(0).getNetworkCidr())) {
                assert route.outNicMic != null
            }
        }
        assert defaultRoute && routeForL31

        expect(AssertionError.class) {
            updateVirtualRouter {
                vmInstanceUuid = vpc1.uuid
                defaultRouteL3NetworkUuid = pub_2.uuid
            }
        }

        /* 1 additional public nic will add:
        *   1 ruleset (including 1 rule, 1 l3Ref),
        *   1 routeTable(1 default route entry +  1 private nic * direct route) + 1 public nic * direct route) */
        syncCount = 0
        cmd = null
        attachL3NetworkToVm {
            l3NetworkUuid = pub_3.uuid
            vmInstanceUuid = vpc1.uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 3
        ruleSetUuids.remove(defaultRuleSet.getUuid())
        ruleSetUuids.remove(pubRuleSetUuid2)
        assert ruleSetUuids.size() == 1
        String pubRuleSetUuid3 = ruleSetUuids.get(0)
        PolicyRouteRuleSetVO pubRuleSet3 = dbf.findByUuid(pubRuleSetUuid3, PolicyRouteRuleSetVO.class)
        assert pubRuleSet3.type == PolicyRouteType.EgressWhereComeFrom
        assert pubRuleSet3.rules.size() == 1
        assert pubRuleSet3.l3Refs.size() == 1
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 2
        tableUuids.remove(pubTable2.getUuid())
        assert tableUuids.size() == 1
        PolicyRouteTableVO pubTable3 = dbf.findByUuid(tableUuids.get(0), PolicyRouteTableVO.class)
        assert pubTable3.type == PolicyRouteType.EgressWhereComeFrom
        assert pubTable3.routes.size() == 3
        assert pubTable2.tableNumber != pubTable3.tableNumber

        /* add private nic will add 1 entry to each policy table */
        syncCount = 0
        cmd = null
        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpc1.uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 3
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 2
        pubTable2 = dbf.findByUuid(pubTable2.getUuid(), PolicyRouteTableVO.class)
        assert pubTable2.routes.size() == 4
        pubTable3 = dbf.findByUuid(pubTable3.getUuid(), PolicyRouteTableVO.class)
        assert pubTable3.routes.size() == 4
        defaultRoute = false
        routeForL31 = false
        boolean routeForL32 = false
        for (PolicyRouteCommands.RouteInfo route : cmd.routes) {
            if (route.destinationCidr == "0.0.0.0/0") {
                defaultRoute = true
            } else if (route.destinationCidr == NetworkUtils.fmtCidr(l3_1.getIpRanges().get(0).getNetworkCidr())) {
                routeForL31 = true
            } else if (route.destinationCidr == NetworkUtils.fmtCidr(l3_2.getIpRanges().get(0).getNetworkCidr())) {
                routeForL32 = true
            }
        }
        assert defaultRoute && routeForL31 && routeForL32

        /* add private nic will add 1 entry to each policy table */
        syncCount = 0
        cmd = null
        attachL3NetworkToVm {
            l3NetworkUuid = l3_3.uuid
            vmInstanceUuid = vpc1.uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 3
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 2
        pubTable2 = dbf.findByUuid(pubTable2.getUuid(), PolicyRouteTableVO.class)
        assert pubTable2.routes.size() == 5
        pubTable3 = dbf.findByUuid(pubTable3.getUuid(), PolicyRouteTableVO.class)
        assert pubTable3.routes.size() == 5

        /* disable system policy route */
        List<PolicyRouteRuleSetInventory> sysPolicyRuleSets = getPolicyRouteRuleSetFromVirtualRouter {
            vmInstanceUuid = vpc1.getUuid()
        }
        assert sysPolicyRuleSets.size() == 1
        syncCount = 0
        cmd = null
        deletePolicyRouteRuleSet {
            uuid = sysPolicyRuleSets.get(0).uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 0
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 0
        assert cmd != null

        vpc1 = queryVpcRouter { conditions = ["uuid=${vpc1.uuid}"] } [0]
        /* enable system policy route */
        syncCount = 0
        cmd = null
        createPolicyRouteRuleSet {
            vRouterUuid = vpc1.uuid
            name = "system"
            type = PolicyRouteType.EgressWhereComeFrom.toString()
        }
        VmNicInventory pub2_nic
        VmNicInventory pub3_nic
        for (VmNicInventory nic : vpc1.vmNics) {
            if (nic.l3NetworkUuid == pub_2.uuid) {
                pub2_nic = nic
            } else if (nic.l3NetworkUuid == pub_3.uuid) {
                pub3_nic = nic
            }
        }

        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 3
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 2
        List<PolicyRouteTableVO> tables = Q.New(PolicyRouteTableVO.class).in(PolicyRouteTableVO_.uuid, tableUuids).list()
        for (PolicyRouteTableVO table : tables) {
            for (PolicyRouteTableRouteEntryVO r : table.routes) {
                if (r.destinationCidr == NetworkUtils.fmtCidr(pub_2.getIpRanges().get(0).getNetworkCidr())) {
                    assert r.nextHopIp == pub2_nic.ip
                    pubTable2 = table
                    break
                } else if (r.destinationCidr == NetworkUtils.fmtCidr(pub_3.getIpRanges().get(0).getNetworkCidr())){
                    assert r.nextHopIp == pub3_nic.ip
                    pubTable3 = table
                    break
                }
            }
        }
        assert pubTable2 != null
        assert pubTable2.routes.size() == 5
        assert pubTable3 != null
        assert pubTable3.routes.size() == 5
        assert cmd != null
        assert cmd.ruleSets.size() == 3
        assert cmd.rules.size() == 2
        assert cmd.tableNumbers.size() == 2
        assert cmd.routes.size() == 10
        assert cmd.refs.size() == 2

        vpc1 = queryVirtualRouterVm {conditions=["name=vpc-1"]} [0]
        VmNicInventory pubNic2
        VmNicInventory priNic1
        VmNicInventory priNic2
        VmNicInventory priNic3
        for (VmNicInventory nic : vpc1.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pub_2.getUuid())){
                pubNic2 = nic
            } else if (nic.getL3NetworkUuid().equals(l3_1.getUuid())) {
                priNic1 = nic
            } else if (nic.getL3NetworkUuid().equals(l3_2.getUuid())) {
                priNic2 = nic
            } else if (nic.getL3NetworkUuid().equals(l3_3.getUuid())) {
                priNic3 = nic
            }
        }

        /* detach 1 private nic, will remove 1 route entry */
        syncCount = 0
        detachL3NetworkFromVm {
            vmNicUuid = priNic1.uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 3
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 2
        pubTable2 = dbf.findByUuid(pubTable2.getUuid(), PolicyRouteTableVO.class)
        assert pubTable2.routes.size() == 4
        pubTable3 = dbf.findByUuid(pubTable3.getUuid(), PolicyRouteTableVO.class)
        assert pubTable3.routes.size() == 4

        /* detach 1 public nic, will remove 1 routeset, 1 route table */
        syncCount = 0
        detachL3NetworkFromVm {
            vmNicUuid = pubNic2.uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 2
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 1
        pubTable3 = dbf.findByUuid(pubTable3.getUuid(), PolicyRouteTableVO.class)
        assert pubTable3.routes.size() == 4

        /* detach 1 private nic, will remove 1 route entry */
        syncCount = 0
        detachL3NetworkFromVm {
            vmNicUuid = priNic2.uuid
        }
        assert syncCount == 1
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 2
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 1
        pubTable3 = dbf.findByUuid(pubTable3.getUuid(), PolicyRouteTableVO.class)
        assert pubTable3.routes.size() == 3

        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpc1.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpc1.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = pub_2.uuid
            vmInstanceUuid = vpc1.uuid
        }

        List<PolicyRouteRuleSetInventory> defaultRuleSetInvs = getPolicyRouteRuleSetFromVirtualRouter {
            vmInstanceUuid = vpc1.uuid
        }
        assert defaultRuleSetInvs.size() == 1
        deletePolicyRouteRuleSet {
            uuid = defaultRuleSetInvs.get(0).uuid
        }
        ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 0
        tableUuids = tcp.getServiceUuidsByRouterUuid(vpc1.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 0

        destroyVmInstance {
            uuid = vpc1.uuid
        }
    }

    void testDeleteVpc() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr")
        L3NetworkInventory pub_2 = env.inventoryByName("pubL3-2")
        L3NetworkInventory pub_3 = env.inventoryByName("pubL3-3")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")
        L3NetworkInventory l3_3 = env.inventoryByName("l3-3")

        VirtualRouterVmInventory vpc2 = createVpcVRouter {
            name = "vpc-2"
            virtualRouterOfferingUuid = offering.uuid
        }

        env.simulator(PolicyRouteConstants.POLICY_ROUTE_SYNC) { HttpEntity<String> e, EnvSpec spec ->
            return new PolicyRouteCommands.ApplyPolicyRouteRsp()
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpc2.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = pub_2.uuid
            vmInstanceUuid = vpc2.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = pub_3.uuid
            vmInstanceUuid = vpc2.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpc2.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = l3_3.uuid
            vmInstanceUuid = vpc2.uuid
        }
        createPolicyRouteRuleSet {
            vRouterUuid = vpc2.uuid
            name = "system"
            type = PolicyRouteType.EgressWhereComeFrom.toString()
        }

        /* each additional public network has a ruleset and there is an system empty ruleset to show this feature is enabled  */
        assert Q.New(PolicyRouteRuleSetVO.class).count() == 3
        List<PolicyRouteTableVO> tables = Q.New(PolicyRouteTableVO.class).list()
        assert tables.size() == 2
        assert tables.get(0).tableNumber != tables.get(1).tableNumber

        destroyVmInstance {
            uuid = vpc2.uuid
        }

        assert Q.New(PolicyRouteRuleSetVO.class).count() == 0
        assert Q.New(PolicyRouteTableVO.class).count() == 0
        assert Q.New(PolicyRouteRuleSetL3RefVO.class).count() == 0
        assert Q.New(PolicyRouteRuleVO).count() == 0
        assert Q.New(PolicyRouteTableRouteEntryVO).count() == 0
    }

    void testDeleteRuleSetWithNoRouteTable() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr")

        VirtualRouterVmInventory vpc2 = createVpcVRouter {
            name = "vpc-3"
            virtualRouterOfferingUuid = offering.uuid
        }

        env.simulator(PolicyRouteConstants.POLICY_ROUTE_SYNC) { HttpEntity<String> e, EnvSpec spec ->
            return new PolicyRouteCommands.ApplyPolicyRouteRsp()
        }
        PolicyRouteRuleSetInventory ruleset = createPolicyRouteRuleSet {
            vRouterUuid = vpc2.uuid
            name = "system"
            type = PolicyRouteType.EgressWhereComeFrom.toString()
        }

        deletePolicyRouteRuleSet {
            uuid = ruleset.uuid
        }

        assert Q.New(PolicyRouteRuleSetVO.class).count() == 0
        assert Q.New(PolicyRouteTableVO.class).count() == 0
        assert Q.New(PolicyRouteRuleSetL3RefVO.class).count() == 0
        assert Q.New(PolicyRouteRuleVO).count() == 0
        assert Q.New(PolicyRouteTableRouteEntryVO).count() == 0
    }

    void testEnableSystemPolicyRouteWithUserPolicyRoute() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr")
        L3NetworkInventory pub_2 = env.inventoryByName("pubL3-2")
        L3NetworkInventory pub_3 = env.inventoryByName("pubL3-3")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")

        VirtualRouterVmInventory vpc4 = createVpcVRouter {
            name = "vpc-4"
            virtualRouterOfferingUuid = offering.uuid
        }

        attachL3NetworkToVm {
            l3NetworkUuid = pub_3.uuid
            vmInstanceUuid = vpc4.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = pub_2.uuid
            vmInstanceUuid = vpc4.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpc4.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpc4.uuid
        }

        /* create user policy route */
        PolicyRouteRuleSetInventory userRuleSet = createPolicyRouteRuleSet {
            vRouterUuid = vpc4.uuid
            name = "pr-test-1"
            type = PolicyRouteType.User.toString()
        }
        PolicyRouteTableInventory userTable = createPolicyRouteTable {
            vRouterUuid = vpc4.uuid
            number = 1
            description = "table description"
        }
        createPolicyRouteTableRouteEntry {
            tableUuid = userTable.uuid
            destinationCidr = "*************/24"
            nextHopIp = "***********"
        }
        createPolicyRouteRule {
            ruleSetUuid = userRuleSet.uuid
            tableUuid = userTable.uuid
            ruleNumber = 1001
            destIp = "*******"
        }
        attachPolicyRouteRuleSetToL3 {
            ruleSetUuid = userRuleSet.uuid
            l3Uuid = l3_1.uuid
        }

        /* enable system policy route */
        PolicyRouteRuleSetInventory ruleset = createPolicyRouteRuleSet {
            vRouterUuid = vpc4.uuid
            name = "system"
            type = PolicyRouteType.EgressWhereComeFrom.toString()
        }

        /* pub_2 has been attached to system policy route */
        expect(AssertionError.class) {
            attachPolicyRouteRuleSetToL3 {
                ruleSetUuid = userRuleSet.uuid
                l3Uuid = pub_2.uuid
            }
        }

        List<PolicyRouteRuleSetInventory> defaultRuleSetInvs = getPolicyRouteRuleSetFromVirtualRouter {
            vmInstanceUuid = vpc4.uuid
        }
        assert defaultRuleSetInvs.size() == 1
        deletePolicyRouteRuleSet {
            uuid = defaultRuleSetInvs.get(0).uuid
        }

        /* user ruleset */
        List<String> ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc4.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 1
        /* user routeTable */
        List<String> tableUuids = tcp.getServiceUuidsByRouterUuid(vpc4.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size()  == 1

        defaultRuleSetInvs = getPolicyRouteRuleSetFromVirtualRouter {
            vmInstanceUuid = vpc4.uuid
        }
        assert defaultRuleSetInvs.size() == 0
        /* user ruleset */
        List<PolicyRouteRuleSetInventory> ruleSets = queryPolicyRouteRuleSet {}
        assert ruleSets.size() == 1
        /* user routeTable */
        List<PolicyRouteTableInventory> tables = queryPolicyRouteTable {}
        assert tables.size() == 1
    }

    void testVpcManagementNicDifferentFromPublic() {
        VirtualRouterOfferingInventory offering = env.inventoryByName("vr-1")
        L3NetworkInventory l3_4 = env.inventoryByName("l3-4")

        VirtualRouterVmInventory vpc5 = createVpcVRouter {
            name = "vpc-5"
            virtualRouterOfferingUuid = offering.uuid
        }
        attachL3NetworkToVm {
            l3NetworkUuid = l3_4.uuid
            vmInstanceUuid = vpc5.uuid
        }

        int syncCount = 0
        PolicyRouteCommands.ApplyPolicyRouteCmd cmd = null
        env.simulator(PolicyRouteConstants.POLICY_ROUTE_SYNC) { HttpEntity<String> e, EnvSpec spec ->
            syncCount++
            cmd = JSONObjectUtil.toObject(e.body, PolicyRouteCommands.ApplyPolicyRouteCmd.class)
            return new PolicyRouteCommands.ApplyPolicyRouteRsp()
        }
        createPolicyRouteRuleSet {
            vRouterUuid = vpc5.uuid
            name = "system"
            type = PolicyRouteType.EgressWhereComeFrom.toString()
        }
        assert syncCount == 0
        List<String> ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vpc5.uuid, PolicyRouteRuleSetVO.class.getSimpleName())
        assert ruleSetUuids.size() == 1
        PolicyRouteRuleSetVO defaultRuleSet = dbf.findByUuid(ruleSetUuids.get(0), PolicyRouteRuleSetVO.class)
        assert defaultRuleSet.getRules().size() == 0
        assert defaultRuleSet.type == PolicyRouteType.EgressWhereComeFrom
        List<String> tableUuids = tcp.getServiceUuidsByRouterUuid(vpc5.uuid, PolicyRouteTableVO.class.getSimpleName())
        assert tableUuids.size() == 0
    }

    @Override
    void clean() {
        env.delete()
    }
}
