package org.zstack.test.integration.aliyun.storage.ebs.backup


import org.zstack.aliyun.core.AliyunPluginConstant
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.Q
import org.zstack.header.aliyun.ebs.AliyunEbsBackupStorageVO
import org.zstack.header.aliyun.ebs.AliyunEbsBackupStorageVO_
import org.zstack.header.aliyun.oss.OssBucketDomainVO
import org.zstack.header.aliyun.oss.OssBucketDomainVO_
import org.zstack.header.message.MessageReply
import org.zstack.header.storage.backup.BackupStorageConstant
import org.zstack.header.storage.backup.BackupStorageStatus
import org.zstack.header.storage.backup.BackupStorageVO
import org.zstack.header.storage.backup.PingBackupStorageMsg
import org.zstack.license.LicenseType
import org.zstack.sdk.BackupStorageInventory
import org.zstack.sdk.DataCenterInventory
import org.zstack.sdk.OssBucketInventory
import org.zstack.sdk.ZoneInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
/**
 * Created by mingjian.deng on 2018/8/17.*/
class AliyunEbsBackupStorageCase extends PremiumSubCase {
    EnvSpec env
    BackupStorageInventory inventory
    OssBucketInventory bucket
    ZoneInventory zone
    DataCenterInventory dc

    CloudBus bus

    @Override
    void environment() {
        env = AliyunEbsBackupEnv.createWithoutBS()
    }

    @Override
    void test() {
        env.create {
            prepare()
            testGetOSS()
            testAddBS()
            testAttachBS()
            testUpdateBS()
            testPingBS()
            testDetachBS()
            testDeleteBS()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    void prepare() {
        zone = env.inventoryByName("zone") as ZoneInventory
        bucket = env.inventoryByName("test-bucket") as OssBucketInventory
        dc = env.inventoryByName("guizhou") as DataCenterInventory
        bus = bean(CloudBus.class)

        assert Q.New(OssBucketDomainVO.class).eq(OssBucketDomainVO_.ossBucketUuid, bucket.uuid).count() == 1
    }

    void testGetOSS() {
        getOssBucketNameFromRemote {
            dataCenterUuid = dc.uuid
            ossDomain = "oss-endpoint.com"
            ossKey = "oss-key"
            ossSecret = "oss-secret"
        }

        // only care api succeed or not
    }

    void testAddBS() {
        inventory = addAliyunEbsBackupStorage {
            delegate.name = "ebs-bs"
            delegate.description = "description"
            delegate.url = "http://127.0.0.1:8989/ocean/api"
            delegate.ossBucketUuid = bucket.uuid
        } as BackupStorageInventory

        assert inventory.name == "ebs-bs"
        assert inventory.type == AliyunPluginConstant.ALIYUN_EBS_BACKUP_STORAGE_TYPE
        assert inventory.status == BackupStorageStatus.Connected.toString()

        assert Q.New(AliyunEbsBackupStorageVO.class).eq(AliyunEbsBackupStorageVO_.ossBucketUuid, bucket.uuid).count() == 1
    }

    void testAttachBS() {
        inventory = attachBackupStorageToZone {
            backupStorageUuid = inventory.uuid
            zoneUuid = zone.uuid
        } as BackupStorageInventory

        assert inventory.attachedZoneUuids == [zone.uuid]
    }

    void testUpdateBS() {
        updateAliyunEbsBackupStorage {
            uuid = inventory.uuid
        }

        assert Q.New(AliyunEbsBackupStorageVO.class).eq(AliyunEbsBackupStorageVO_.ossBucketUuid, bucket.uuid).count() == 1
    }

    void testPingBS() {
        def reply = pingBS(inventory.uuid) as MessageReply
        assert reply.success
    }

    void testDetachBS() {
        inventory = detachBackupStorageFromZone {
            backupStorageUuid = inventory.uuid
            zoneUuid = zone.uuid
        } as BackupStorageInventory

        assert inventory.attachedZoneUuids == []
    }

    void testDeleteBS() {
        def count = Q.New(BackupStorageVO.class).count()
        def ref =  Q.New(AliyunEbsBackupStorageVO.class).count()

        deleteBackupStorage {
            uuid = inventory.uuid
        }
        assert Q.New(BackupStorageVO.class).count() == count - 1
        assert Q.New(AliyunEbsBackupStorageVO.class).count() == ref - 1
    }

    private MessageReply pingBS(String uuid) {
        PingBackupStorageMsg msg = new PingBackupStorageMsg()
        msg.setBackupStorageUuid(uuid)
        bus.makeTargetServiceIdByResourceUuid(msg, BackupStorageConstant.SERVICE_ID, uuid)
        return bus.call(msg)
    }
}
