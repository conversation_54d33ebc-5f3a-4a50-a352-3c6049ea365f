package org.zstack.test.integration.premium.pciDevice

import org.springframework.http.HttpEntity
import org.springframework.web.util.UriComponentsBuilder
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.host.HostStatus
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.rest.RESTConstant
import org.zstack.header.rest.RESTFacade
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmInstanceVO
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend
import org.zstack.pciDevice.PciDeviceStatus
import org.zstack.pciDevice.PciDeviceSystemTags
import org.zstack.pciDevice.PciDeviceTOExampleMaker
import org.zstack.pciDevice.PciDeviceVO
import org.zstack.pciDevice.PciDeviceVO_
import org.zstack.pciDevice.specification.mdev.MdevDeviceSpecVO
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus
import org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceChooser
import org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceStatus
import org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceSystemTags
import org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceVO
import org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceVO_
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil
/**
 * Created by GuoYi on 2019/5/13.
 */
class TestMdevDeviceCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf
    ClusterInventory cluster1
    ClusterInventory cluster2
    HostInventory kvm11
    HostInventory kvm21
    VmInstanceInventory vm1
    VmInstanceInventory vm2
    PciDeviceInventory m60InCluster1
    PciDeviceInventory m60InCluster2
    MdevDeviceInventory mdevInCluster1
    MdevDeviceInventory mdevInCluster2

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
        spring {
            vyos()
        }
    }

    @Override
    void environment() {
        env = env {
            account {
                name = "test"
                password = "password"
            }

            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster1"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm11"
                        managementIp = "127.0.0.11"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm12"
                        managementIp = "127.0.0.12"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("nfs")
                    attachL2Network("l2")
                }

                cluster {
                    name = "cluster2"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm21"
                        managementIp = "**********"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm22"
                        managementIp = "**********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("nfs")
                    attachL2Network("l2")
                }

                nfsPrimaryStorage {
                    name = "nfs"
                    url = "**********:/nfs"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString()]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }

            vm {
                name = "vm1"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useL3Networks("l3")
                useCluster("cluster1")
            }

            vm {
                name = "vm2"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useL3Networks("l3")
                useCluster("cluster2")
            }
        }
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            initEnv()
            testQueryVirtualizablePciDeviceWithMdevSpecs()
            testGenerateMdevDevicesFromAttachedPciDevice()
            testGenerateMdevDevices()
            testGenerateMdevDevicesFromVirtualizedPciDevice()
            testGetMdevDeviceCandidatesForStoppedVm()
            testAttachMdevDeviceToStoppedVm()
            testDetachMdevDeviceFromStoppedVm()
            testGetMdevDeviceCandidatesForRunningVm()
            testAttachMdevDeviceToRunningVm()
            testAttachMdevDeviceToNewVm()
            testGetMdevDeviceSpecForRunningVm()
            testSetMdevDeviceSpecForRunningVm()
            testGetMdevDeviceSpecForStoppedVm()
            testSetMdevDeviceSpecForStoppedVm()
            testReleaseMdevDeviceWhenVmStoppedFromZStack()
            testReleaseMdevDeviceWhenVmStoppedUsingInnerCommand()
            testReleaseMdevDeviceWhenVmFailedToStart()
            testUngenerateMdevDevicesFromNonVirtualizedPciDevice()
            testUngenerateMdevDevicesThatAreStillAttached()
            testUngenerateMdevDevices()
        }
    }

    void initEnv() {
        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_GPU(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_AUDIO(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
                    PciDeviceTOExampleMaker.GT710B_AUDIO(),
                    PciDeviceTOExampleMaker.M60_VIRTUALIZABLE(),
                    PciDeviceTOExampleMaker.S7150_VIRTUALIZED(),
                    PciDeviceTOExampleMaker.S7150V_3936M_1(),
                    PciDeviceTOExampleMaker.S7150V_3936M_2(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        List<KVMHostInventory> result = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : result) {
            updateHostIommuState {
                delegate.uuid = host.uuid
                delegate.state = "Enabled"
            }
            reconnectHost {
                delegate.uuid = host.uuid
            }
        }
        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
        }

        cluster1 = env.inventoryByName("cluster1")
        cluster2 = env.inventoryByName("cluster2")
        kvm11 = env.inventoryByName("kvm11")
        kvm21 = env.inventoryByName("kvm21")
        vm1 = env.inventoryByName("vm1")
        vm2 = env.inventoryByName("vm2")

        m60InCluster1 = queryPciDevice {
            conditions = [
                    "hostUuid=${vm1.hostUuid}",
                    "type=GPU_Video_Controller",
                    "virtStatus=VFIO_MDEV_VIRTUALIZABLE"
            ]
        }[0]
        assert m60InCluster1 != null

        m60InCluster2 = queryPciDevice {
            conditions = [
                    "hostUuid=${vm2.hostUuid}",
                    "type=GPU_Video_Controller",
                    "virtStatus=VFIO_MDEV_VIRTUALIZABLE"
            ]
        }[0]
        assert m60InCluster2 != null
    }

    void testQueryVirtualizablePciDeviceWithMdevSpecs() {
        def pcis = queryPciDevice { conditions = ["type=GPU_Video_Controller",
                                             "virtStatus=UNVIRTUALIZABLE"] }
        // four hosts, one host with one gpu that is unvirtualizable
        assert 4 == pcis.size()

        pcis = queryPciDevice { conditions = ["type=GPU_Video_Controller",
                                                 "virtStatus=VFIO_MDEV_VIRTUALIZABLE"] }
        // four hosts, one host with one virtualizable gpu that support vfio_mdev
        assert 4 == pcis.size()

        pcis = queryPciDevice { conditions = ["type=GPU_Video_Controller",
                                                 "virtStatus=SRIOV_VIRTUALIZED"] }
        // four hosts, one host with one virtualized gpu that support sr-iov
        assert 4 == pcis.size()

        pcis = queryPciDevice { conditions = ["type=GPU_Video_Controller",
                                             "virtStatus=SRIOV_VIRTUAL"] }
        // four hosts, one host with two sriov virtual gpu
        assert 8 == pcis.size()
    }

    void testGenerateMdevDevicesFromAttachedPciDevice() {
        attachPciDeviceToVm {
            vmInstanceUuid = vm1.uuid
            pciDeviceUuid = m60InCluster1.uuid
        }
        def m60VO = dbf.findByUuid(m60InCluster1.uuid, PciDeviceVO.class)
        assert m60VO.status == PciDeviceStatus.Attached

        expect(AssertionError.class) {
            generateMdevDevices {
                sessionId = adminSession()
                pciDeviceUuid = m60InCluster1.uuid
                mdevSpecUuid = m60InCluster1.mdevSpecRefs[0].mdevSpecUuid
            }
        }

        detachPciDeviceFromVm {
            vmInstanceUuid = vm1.uuid
            pciDeviceUuid = m60InCluster1.uuid
        }
        m60VO = dbf.findByUuid(m60InCluster1.uuid, PciDeviceVO.class)
        assert m60VO.status == PciDeviceStatus.System
    }

    void testGenerateMdevDevices() {
        assert 0 == dbf.count(MdevDeviceVO.class)
        // we only simulated m60_2a and m60_2b
        assert 2 == dbf.count(MdevDeviceSpecVO.class)

        List<MdevDeviceSpecVO> specVOList = Q.New(MdevDeviceSpecVO.class).list()
        assert specVOList.size() == 2
        assert specVOList.get(0).vendor == "NVIDIA"

        queryMdevDeviceSpec {}
        def specs = queryMdevDeviceSpec {} as List<MdevDeviceSpecInventory>
        assert specs.size() == 2
        assert specs.get(0).vendor == "NVIDIA"

        generateMdevDevices {
            sessionId = adminSession()
            pciDeviceUuid = m60InCluster1.uuid
            mdevSpecUuid = m60InCluster1.mdevSpecRefs[0].mdevSpecUuid
        }

        generateMdevDevices {
            sessionId = adminSession()
            pciDeviceUuid = m60InCluster2.uuid
            mdevSpecUuid = m60InCluster1.mdevSpecRefs[1].mdevSpecUuid
        }

        // both hosts that running vm1 and vm2 have 4 mdev devices
        assert 8 == dbf.count(MdevDeviceVO.class)

        def m60VO = dbf.findByUuid(m60InCluster1.uuid, PciDeviceVO.class)
        assert m60VO.virtStatus == PciDeviceVirtStatus.VFIO_MDEV_VIRTUALIZED
        m60VO = dbf.findByUuid(m60InCluster2.uuid, PciDeviceVO.class)
        assert m60VO.virtStatus == PciDeviceVirtStatus.VFIO_MDEV_VIRTUALIZED

        mdevInCluster1 = queryMdevDevice { conditions = ["hostUuid=${vm1.hostUuid}"] }[0]
        assert mdevInCluster1 != null
        assert mdevInCluster1.parentUuid == m60InCluster1.uuid
        assert mdevInCluster1.mdevSpecUuid == m60InCluster1.mdevSpecRefs[0].mdevSpecUuid

        mdevInCluster2 = queryMdevDevice { conditions = ["hostUuid=${vm2.hostUuid}"] }[0]
        assert mdevInCluster2 != null
        assert mdevInCluster2.parentUuid == m60InCluster2.uuid
        assert mdevInCluster2.mdevSpecUuid == m60InCluster1.mdevSpecRefs[1].mdevSpecUuid
    }

    void testGenerateMdevDevicesFromVirtualizedPciDevice() {
        def m60VO = dbf.findByUuid(m60InCluster1.uuid, PciDeviceVO.class)
        assert m60VO.virtStatus == PciDeviceVirtStatus.VFIO_MDEV_VIRTUALIZED

        expect(AssertionError.class) {
            generateMdevDevices {
                sessionId = adminSession()
                pciDeviceUuid = m60InCluster1.uuid
                mdevSpecUuid = m60InCluster1.mdevSpecRefs[0].mdevSpecUuid
            }
        }
    }

    void testGetMdevDeviceCandidatesForStoppedVm() {
        stopVmInstance {
            uuid = vm1.uuid
        }

        // vm1 can start in both cls1 and cls2, so all mdevs can be used
        def mdevs = getMdevDeviceCandidates {
            vmInstanceUuid = vm1.uuid
        } as List<MdevDeviceInventory>
        assert 8 == mdevs.size()

        mdevs = getMdevDeviceCandidates {
            clusterUuids = [cluster2.uuid]
        }
        assert 4 == mdevs.size()

        mdevs = getMdevDeviceCandidates {
            hostUuid = mdevInCluster1.hostUuid
        }
        assert 4 == mdevs.size()

        // test normal account
        def testAccountSession = logInByAccount {
            accountName = "test"
            password = "password"
        }

        // user test cannot access unshared mdev devices
        def testMdevs = getMdevDeviceCandidates {
            clusterUuids = [cluster1.uuid]
            sessionId = testAccountSession.uuid
        }
        assert 0 == testMdevs.size()

        // share mdevs to public in cluster1
        shareResource {
            resourceUuids = [mdevs[0].uuid, mdevs[1].uuid, mdevs[2].uuid, mdevs[3].uuid]
            toPublic = true
        }

        // user test can access mdevs in cluster1
        testMdevs = getMdevDeviceCandidates {
            clusterUuids = [cluster1.uuid]
            sessionId = testAccountSession.uuid
        }
        assert 4 == testMdevs.size()
    }

    void testAttachMdevDeviceToStoppedVm() {
        attachMdevDeviceToVm {
            vmInstanceUuid = vm1.uuid
            mdevDeviceUuid = mdevInCluster1.uuid
        }
        retryInSecs {
            def mdevVO = dbf.findByUuid(mdevInCluster1.uuid, MdevDeviceVO.class)
            assert mdevVO.status == MdevDeviceStatus.Attached
        }

        // with one mdev attached, only the left mdevs in same host can be used
        def mdevs = getMdevDeviceCandidates {
            vmInstanceUuid = vm1.uuid
        } as List<MdevDeviceInventory>
        assert 3 == mdevs.size()
    }

    void testDetachMdevDeviceFromStoppedVm() {
        detachMdevDeviceFromVm {
            vmInstanceUuid = vm1.uuid
            mdevDeviceUuid = mdevInCluster1.uuid
        }
        def mdevVO = dbf.findByUuid(mdevInCluster1.uuid, MdevDeviceVO.class)
        assert mdevVO.status == MdevDeviceStatus.Active

        // with no mdev attached, all mdevs can be used in both cls1 and cls2
        def mdevs = getMdevDeviceCandidates {
            vmInstanceUuid = vm1.uuid
        } as List<MdevDeviceInventory>

        assert 8 == mdevs.size()
    }

    void testGetMdevDeviceCandidatesForRunningVm() {
        assert dbFindByUuid(vm2.uuid, VmInstanceVO.class).state.toString() == VmInstanceState.Running.toString()

        // only get mdev candidates when vm is stopped, because mdev devices cannot hot-plugin
        expect(AssertionError.class) {
            getMdevDeviceCandidates {
                vmInstanceUuid = vm2.uuid
            }
        }
    }

    void testAttachMdevDeviceToRunningVm() {
        assert dbFindByUuid(vm2.uuid, VmInstanceVO.class).state.toString() == VmInstanceState.Running.toString()

        // mdev devices cannot hot-plugin
        expect(AssertionError.class) {
            attachMdevDeviceToVm {
                vmInstanceUuid = vm2.uuid
                mdevDeviceUuid = mdevInCluster2.uuid
            }
        }
    }

    void testAttachMdevDeviceToNewVm() {
        def mdevVO = dbf.findByUuid(mdevInCluster1.uuid, MdevDeviceVO.class)
        assert mdevVO.status == MdevDeviceStatus.Active

        // create vm with mdev device
        def newvm = createVmInstance {
            name = "newvm"
            instanceOfferingUuid = env.specByName("instanceOffering").inventory.uuid
            imageUuid = env.specByName("image1").inventory.uuid
            l3NetworkUuids = [env.specByName("l3").inventory.uuid]
            clusterUuid = cluster1.uuid
            systemTags = [String.format("%s::%s", MdevDeviceSystemTags.MDEV_DEVICE_TOKEN, mdevInCluster1.uuid)]
        }

        mdevVO = dbf.findByUuid(mdevInCluster1.uuid, MdevDeviceVO.class)
        assert mdevVO.status == MdevDeviceStatus.Attached
        assert mdevVO.vmInstanceUuid == newvm.uuid

        // delete the vm, all attached mdev devices will be released automatically
        destroyVmInstance {
            uuid = newvm.uuid
        }

        expungeVmInstance {
            uuid = newvm.uuid
        }

        mdevVO = dbf.findByUuid(mdevInCluster1.uuid, MdevDeviceVO.class)
        assert mdevVO.status == MdevDeviceStatus.Active
    }

    void testGetMdevDeviceSpecForRunningVm() {
        assert dbFindByUuid(vm2.uuid, VmInstanceVO.class).state.toString() == VmInstanceState.Running.toString()
        expect(AssertionError.class) {
            getMdevDeviceSpecCandidates {
                vmInstanceUuid = vm2.uuid
            }
        }
    }

    void testSetMdevDeviceSpecForRunningVm() {
        assert dbFindByUuid(vm2.uuid, VmInstanceVO.class).state.toString() == VmInstanceState.Running.toString()

        def spec = queryPciDeviceSpec { conditions = ["type=GPU_Video_Controller"] }[0]
        assert spec != null

        expect(AssertionError.class) {
            addMdevDeviceSpecToVmInstance {
                vmInstanceUuid = vm2.uuid
                mdevSpecUuid = spec
            }
        }
    }

    void testGetMdevDeviceSpecForStoppedVm() {
        assert dbFindByUuid(vm1.uuid, VmInstanceVO.class).state.toString() == VmInstanceState.Stopped.toString()

        def mdevs = Q.New(MdevDeviceVO.class)
                .eq(MdevDeviceVO_.type, org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceType.GPU_Video_Controller)
                .select(MdevDeviceVO_.uuid)
                .listValues()
        assert mdevs.size() > 0

        // vm1 can start in both cls1 and cls2, so all mdev specs can be used
        def specs = getMdevDeviceSpecCandidates {
            clusterUuids = [cluster1.uuid]
        } as List<MdevDeviceSpecInventory>
        assert 1 == specs.size()

        specs = getMdevDeviceSpecCandidates {
            hostUuid = mdevInCluster1.hostUuid
        } as List<MdevDeviceSpecInventory>
        assert 1 == specs.size()

        specs = getMdevDeviceSpecCandidates {
            vmInstanceUuid = vm1.uuid
        } as List<MdevDeviceSpecInventory>
        assert 2 == specs.size()

        // test normal account
        def testAccountSession = logInByAccount {
            accountName = "test"
            password = "password"
        }

        // user test cannot access unshared mdev specs
        def specsForNormal = getMdevDeviceSpecCandidates {
            clusterUuids = [cluster1.uuid]
            sessionId = testAccountSession.uuid
        } as List<MdevDeviceSpecInventory>
        assert 0 == specsForNormal.size()

        specsForNormal = getMdevDeviceSpecCandidates {
            hostUuid = mdevInCluster1.hostUuid
            sessionId = testAccountSession.uuid
        } as List<MdevDeviceSpecInventory>
        assert 0 == specsForNormal.size()

        // share mdev specs to public
        shareResource {
            resourceUuids = [specs[0].uuid, specs[1].uuid]
            toPublic = true
        }

        // make sure mdev device not shared
        revokeResourceSharing {
            resourceUuids = mdevs
            all = true
        }

        specsForNormal = getMdevDeviceSpecCandidates {
            clusterUuids = [cluster1.uuid, cluster2.uuid]
            sessionId = testAccountSession.uuid
        }
        assert 0 == specsForNormal.size()

        specsForNormal = getMdevDeviceSpecCandidates {
            hostUuid = mdevInCluster1.hostUuid
            sessionId = testAccountSession.uuid
        }
        assert 0 == specsForNormal.size()

        // share mdev devices to public
        shareResource {
            resourceUuids = mdevs
            toPublic = true
        }

        specsForNormal = getMdevDeviceSpecCandidates {
            clusterUuids = [cluster1.uuid, cluster2.uuid]
            sessionId = testAccountSession.uuid
        }
        assert 2 == specsForNormal.size()

        specsForNormal = getMdevDeviceSpecCandidates {
            hostUuid = mdevInCluster1.hostUuid
            sessionId = testAccountSession.uuid
        }
        assert 1 == specsForNormal.size()

        // revoke shared mdev specs
        revokeResourceSharing {
            resourceUuids = [specs[0].uuid, specs[1].uuid]
            all = true
        }
    }

    void testSetMdevDeviceSpecForStoppedVm() {
        assert dbFindByUuid(vm1.uuid, VmInstanceVO.class).state.toString() == VmInstanceState.Stopped.toString()

        // vm1 can start in both cls1 and cls2, so all available mdevs can be used
        def mdevs = getMdevDeviceCandidates {
            vmInstanceUuid = vm1.uuid
        } as List<MdevDeviceInventory>
        assert 8 == mdevs.size()

        // vm1 can start in both cls1 and cls2, so all mdev specs can be used
        def specs = getMdevDeviceSpecCandidates {
            vmInstanceUuid = vm1.uuid
        } as List<MdevDeviceSpecInventory>
        assert 2 == specs.size()

        // make sure no mdev spec for vm1
        assert !Q.New(MdevDeviceVO.class)
                .eq(MdevDeviceVO_.vmInstanceUuid, vm1.uuid)
                .eq(MdevDeviceVO_.chooser, MdevDeviceChooser.Spec)
                .notNull(MdevDeviceVO_.uuid)
                .isExists()

        // add mdev spec to vm1
        addMdevDeviceSpecToVmInstance {
            vmInstanceUuid = vm1.uuid
            mdevSpecUuid = specs[0].uuid
            mdevDeviceNumber = 4
        }

        // whem vm1 started, mdevs that belong to the spec are attached automatically
        startVmInstance {
            uuid = vm1.uuid
        }
        assert 4 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()

        // mdev device do not auto release when vm stopped
        assert !PciDeviceSystemTags.AUTO_RELEASE_SPEC_RELEATED_VIRTUAL_PCI_DEVICE.hasTag(vm1.uuid)
        stopVmInstance {
            uuid = vm1.uuid
        }
        assert 4 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()

        mdevs = getMdevDeviceCandidates {
            vmInstanceUuid = vm1.uuid
        } as List<MdevDeviceInventory>
        assert 0 == mdevs.size()
    }

    void testReleaseMdevDeviceWhenVmStoppedFromZStack() {
        assert !PciDeviceSystemTags.AUTO_RELEASE_SPEC_RELEATED_VIRTUAL_PCI_DEVICE.hasTag(vm1.uuid)

        createSystemTag {
            resourceType = VmInstanceVO.class.simpleName
            resourceUuid = vm1.uuid
            tag = "autoReleaseSpecReleatedVirtualPciDevice"
        }
        assert PciDeviceSystemTags.AUTO_RELEASE_SPEC_RELEATED_VIRTUAL_PCI_DEVICE.hasTag(vm1.uuid)

        startVmInstance {
            uuid = vm1.uuid
        }
        assert 4 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()

        stopVmInstance {
            uuid = vm1.uuid
        }
        assert 0 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()
    }

    void testReleaseMdevDeviceWhenVmStoppedUsingInnerCommand() {
        assert PciDeviceSystemTags.AUTO_RELEASE_SPEC_RELEATED_VIRTUAL_PCI_DEVICE.hasTag(vm1.uuid)
        startVmInstance {
            uuid = vm1.uuid
        }
        assert 4 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()

        // libvirt detected vm power off
        RESTFacade restf = bean(RESTFacade.class)
        UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(restf.getBaseUrl())
        ub.path(RESTConstant.COMMAND_CHANNEL_PATH)
        String url = ub.build().toUriString()

        def header = [(RESTConstant.COMMAND_PATH): KVMConstant.KVM_REPORT_VM_SHUTDOWN_EVENT]
        def cmd = new KVMAgentCommands.ReportVmShutdownEventCmd()
        cmd.vmUuid = vm1.uuid
        restf.syncJsonPost(url, JSONObjectUtil.toJsonString(cmd), header, KVMAgentCommands.AgentResponse)

        // auto release mdev devices because libvirt detected vm power off
        retryInSecs {
            assert 0 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()
        }
    }

    void testReleaseMdevDeviceWhenVmFailedToStart() {
        stopVmInstance {
            uuid = vm1.uuid
        }
        assert PciDeviceSystemTags.AUTO_RELEASE_SPEC_RELEATED_VIRTUAL_PCI_DEVICE.hasTag(vm1.uuid)
        assert dbFindByUuid(vm1.uuid, VmInstanceVO.class).state.toString() == VmInstanceState.Stopped.toString()

        env.simulator(KVMConstant.KVM_START_VM_PATH) {
            def rsp = new KVMAgentCommands.StopVmResponse()
            rsp.setError("start fail on purpose")
            return rsp
        }

        // failed to start vm
        expect(AssertionError.class) {
            startVmInstance {
                uuid = vm1.uuid
            }
        }
        retryInSecs {
            assert 0 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()
        }

        env.simulator(KVMConstant.KVM_START_VM_PATH) {
            return new KVMAgentCommands.StopVmResponse()
        }

        // successfully start vm
        startVmInstance {
            uuid = vm1.uuid
        }
        retryInSecs {
            assert 4 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()
        }

        // successfully reboot vm
        rebootVmInstance {
            uuid = vm1.uuid
        }
        retryInSecs {
            assert 4 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()
        }
    }

    void testUngenerateMdevDevicesFromNonVirtualizedPciDevice() {
        def pci = Q.New(PciDeviceVO.class).eq(PciDeviceVO_.virtStatus, PciDeviceVirtStatus.VFIO_MDEV_VIRTUALIZABLE).limit(1).find()
        assert pci != null

        // cannot un-virtualize pci devices that are not virtualized
        expect(AssertionError.class) {
            ungenerateMdevDevices {
                pciDeviceUuid = pci.uuid
            }
        }
    }

    void testUngenerateMdevDevicesThatAreStillAttached() {
        stopVmInstance {
            uuid = vm1.uuid
        }
        startVmInstance {
            uuid = vm1.uuid
        }
        def mdev = Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).limit(1).find()
        assert mdev != null
        assert mdev.vmInstanceUuid == vm1.uuid

        // cannot un-virtualize pci devices that still have mdev devices attached to vm
        expect(AssertionError.class) {
            ungenerateMdevDevices {
                pciDeviceUuid = mdev.parentUuid
            }
        }
    }

    void testUngenerateMdevDevices() {
        stopVmInstance {
            uuid = vm1.uuid
        }
        assert 0 == Q.New(MdevDeviceVO.class).eq(MdevDeviceVO_.status, MdevDeviceStatus.Attached).count()

        ungenerateMdevDevices {
            pciDeviceUuid = m60InCluster1.uuid
        }
        assert 4 == dbf.count(MdevDeviceVO.class)

        ungenerateMdevDevices {
            pciDeviceUuid = m60InCluster2.uuid
        }
        assert 0 == dbf.count(MdevDeviceVO.class)

        assert 0 == Q.New(PciDeviceVO.class).eq(PciDeviceVO_.virtStatus, PciDeviceVirtStatus.VFIO_MDEV_VIRTUALIZED).count()
    }

    @Override
    void clean() {
        env.delete()
    }
}
