package org.zstack.test.integration.premium.kvm.upgrade

import org.springframework.http.HttpEntity
import org.zstack.core.Platform
import org.zstack.core.ansible.AnsibleConstant
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.cloudbus.EventCallback
import org.zstack.core.cloudbus.EventFacade
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.core.upgrade.AgentVersionVO
import org.zstack.core.upgrade.AgentVersionVO_
import org.zstack.core.upgrade.UpgradeChecker
import org.zstack.core.upgrade.UpgradeGlobalConfig
import org.zstack.header.Constants
import org.zstack.header.host.ConfigPrimaryVmReply
import org.zstack.header.host.ConnectHostMsg
import org.zstack.header.host.HostCanonicalEvents
import org.zstack.header.host.HostConstant
import org.zstack.header.host.HostStatus
import org.zstack.header.host.HostVO
import org.zstack.header.host.HostVO_
import org.zstack.header.host.PingHostMsg
import org.zstack.header.message.MessageReply
import org.zstack.header.storage.primary.ConnectPrimaryStorageMsg
import org.zstack.header.storage.primary.ConnectPrimaryStorageReply
import org.zstack.header.storage.primary.PingPrimaryStorageMsg
import org.zstack.header.storage.primary.PingPrimaryStorageReply
import org.zstack.header.storage.primary.PrimaryStorageConstant
import org.zstack.header.storage.primary.PrimaryStorageStatus
import org.zstack.header.storage.primary.PrimaryStorageVO
import org.zstack.header.storage.primary.PrimaryStorageVO_
import org.zstack.header.storage.primary.RecalculatePrimaryStorageCapacityMsg
import org.zstack.header.storage.primary.RecalculatePrimaryStorageCapacityReply
import org.zstack.header.vm.VmInstanceConstant
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmInstanceVO
import org.zstack.header.vm.VmInstanceVO_
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.kvm.KVMGlobalConfig
import org.zstack.kvm.KVMHostAsyncHttpCallMsg
import org.zstack.kvm.KVMHostAsyncHttpCallReply
import org.zstack.network.service.virtualrouter.PingVirtualRouterVmMsg
import org.zstack.network.service.virtualrouter.ReconnectVirtualRouterVmMsg
import org.zstack.network.service.virtualrouter.VirtualRouterCommands
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.network.service.virtualrouter.VirtualRouterMetadataOperator
import org.zstack.sdk.AgentVersionInventory
import org.zstack.sdk.ClusterInventory
import org.zstack.sdk.HostInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.LongJobInventory
import org.zstack.sdk.LongJobState
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.ReconnectHostAction
import org.zstack.sdk.ReconnectVirtualRouterAction
import org.zstack.sdk.VirtualRouterOfferingInventory
import org.zstack.sdk.VirtualRouterVmInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.ZoneInventory
import org.zstack.storage.primary.PrimaryStorageCapacityRecalculator
import org.zstack.storage.primary.PrimaryStorageManagerImpl
import org.zstack.storage.primary.local.LocalStorageKvmBackend
import org.zstack.storage.primary.local.LocalStorageRecalculateCapacityMsg
import org.zstack.storage.primary.local.LocalStorageRecalculateCapacityReply
import org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackend
import org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands
import org.zstack.storage.primary.sharedblock.SharedBlockKvmBackend
import org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands
import org.zstack.storage.primary.smp.KvmBackend
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.NfsPrimaryStorageSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.utils.SizeUtils
import org.zstack.utils.gson.JSONObjectUtil

import java.util.concurrent.atomic.AtomicInteger

import static java.util.UUID.randomUUID

class GrayscaleUpgradeCase extends PremiumSubCase {
	EnvSpec env
	CloudBus bus
	L3NetworkInventory l3
	VmInstanceInventory vm
	DatabaseFacade dbf
	UpgradeChecker upgradeChecker

	@Override
	void setup() {
		useSpring(PremiumTest.springSpec)
	}

	@Override
	void environment() {
		env = PremiumEnv.twoHostsOneVmBasicEnv()
	}

	@Override
	void test() {
		bus = bean(CloudBus.class)
		env.create {
			dbf = bean(DatabaseFacade.class)
			upgradeChecker = bean(UpgradeChecker.class)
			KVMGlobalConfig.VM_SYNC_ON_HOST_PING.updateValue(true)
			testVirtualRouterWhenOpenGrayscaleUpgrade()
			testHostWhenOpenGrayscaleUpgrade()
			testKvmAgentBasedStorages()
		}
	}

	@Override
	void clean() {
		env.delete()
	}

	void testVirtualRouterWhenOpenGrayscaleUpgrade() {
		def offer = env.inventoryByName("vr") as VirtualRouterOfferingInventory

		env.afterSimulator(VirtualRouterConstant.VR_PING) { VirtualRouterCommands.PingRsp rsp, HttpEntity<String> e ->
			rsp.version = "4.7.11"
			rsp.success = true
			return rsp
		}

		VirtualRouterVmInventory vpc1 = createVpcVRouter {
			name = "vpc-1"
			virtualRouterOfferingUuid = offer.uuid
		}

		updateGlobalConfig {
			category = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.category
			name = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.name
			value = true
		}

		List<VirtualRouterVmInventory> virtualRouterVms = queryVirtualRouterVm {

		}

		for (VirtualRouterVmInventory vr : virtualRouterVms) {
			def pingMsg = new PingVirtualRouterVmMsg()
			pingMsg.virtualRouterVmUuid = vr.uuid
			bus.makeTargetServiceIdByResourceUuid(pingMsg, VmInstanceConstant.SERVICE_ID, vr.uuid)
			MessageReply pingReply = bus.call(pingMsg)
			assert pingReply.success
		}

		retryInSecs {
			AgentVersionInventory agentVersionInventory = queryAgentVersion {
				conditions = ["uuid=${vpc1.uuid}".toString()]
			}[0] as AgentVersionInventory

			assert agentVersionInventory.agentType == VirtualRouterConstant.VIRTUAL_ROUTER_PROVIDER_TYPE
			assert agentVersionInventory.currentVersion == "4.7.11"
			assert agentVersionInventory.expectVersion == new VirtualRouterMetadataOperator().getManagementVersion()
		}

		env.preSimulator(VirtualRouterConstant.VR_INIT) { HttpEntity<String> e ->
			upgradeChecker.updateAgentVersion(vpc1.uuid, VirtualRouterConstant.VIRTUAL_ROUTER_PROVIDER_TYPE, new VirtualRouterMetadataOperator().getManagementVersion(), new VirtualRouterMetadataOperator().getManagementVersion())
		}

		env.afterSimulator(VirtualRouterConstant.VR_PING) { VirtualRouterCommands.PingRsp rsp, HttpEntity<String> e ->
			VirtualRouterCommands.PingCmd pingCmd = JSONObjectUtil.toObject(e.body, VirtualRouterCommands.PingCmd.class)
			if (pingCmd.uuid == vpc1.uuid) {
				rsp.uuid = vpc1.uuid
				rsp.version = new VirtualRouterMetadataOperator().getManagementVersion()
			}
			rsp.success = true
			return rsp
		}

		boolean flag = false
		env.afterSimulator(VirtualRouterConstant.VR_INIT) { VirtualRouterCommands.InitRsp rsp, HttpEntity<String> e ->
			flag = true
			return rsp
		}

		def reconnectVirtualRouterVmMsg = new ReconnectVirtualRouterVmMsg()
		reconnectVirtualRouterVmMsg.setVirtualRouterVmUuid(vpc1.uuid);
		bus.makeTargetServiceIdByResourceUuid(reconnectVirtualRouterVmMsg, VmInstanceConstant.SERVICE_ID, vpc1.uuid);
		bus.call(reconnectVirtualRouterVmMsg)
		retryInSecs {
			assert !flag
		}

		flag = false
		ReconnectVirtualRouterAction action = new ReconnectVirtualRouterAction()
		action.vmInstanceUuid = vpc1.uuid
		action.sessionId = adminSession()
		ReconnectVirtualRouterAction.Result ret = action.call()
		assert ret.error == null

		retryInSecs {
			assert flag
			AgentVersionInventory agentVersionInventory = queryAgentVersion {
				conditions = ["uuid=${vpc1.uuid}".toString()]
			}[0] as AgentVersionInventory

			assert agentVersionInventory.agentType == VirtualRouterConstant.VIRTUAL_ROUTER_PROVIDER_TYPE
			assert agentVersionInventory.currentVersion == new VirtualRouterMetadataOperator().getManagementVersion()
			assert agentVersionInventory.expectVersion == new VirtualRouterMetadataOperator().getManagementVersion()
		}

		// check close grayScale upgrade
		updateGlobalConfig {
			category = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.category
			name = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.name
			value = false
		}
		retryInSecs {
			assert queryAgentVersion {}.size() == 0
		}

		flag = false
		reconnectVirtualRouterVmMsg = new ReconnectVirtualRouterVmMsg()
		reconnectVirtualRouterVmMsg.setVirtualRouterVmUuid(vpc1.uuid);
		bus.makeTargetServiceIdByResourceUuid(reconnectVirtualRouterVmMsg, VmInstanceConstant.SERVICE_ID, vpc1.uuid);
		bus.call(reconnectVirtualRouterVmMsg)
		retryInSecs {
			assert flag
		}

		destroyVmInstance {
			uuid = vpc1.uuid
		}
	}

	void testHostWhenOpenGrayscaleUpgrade() {
		HostInventory host = env.inventoryByName('kvm1')

		updateGlobalConfig {
			category = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.category
			name = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.name
			value = true
		}

		assert UpgradeGlobalConfig.GRAYSCALE_UPGRADE.value(Boolean.class)
		assert upgradeChecker.grayUpgradeConfigMap.isEmpty()
		// load upgrade scale json
		upgradeChecker.start()
		assert !upgradeChecker.grayUpgradeConfigMap.isEmpty()

		env.afterSimulator(KVMConstant.KVM_PING_PATH) { KVMAgentCommands.PingResponse rsp, HttpEntity<String> e ->
			KVMAgentCommands.PingCmd pingCmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.PingCmd.class)
			rsp.version = "4.7.21"
			rsp.success = true
			return rsp
		}

		List<HostInventory> hosts = queryHost {

		}

		def called = false
		env.afterSimulator(KVMConstant.KVM_VM_SYNC_PATH) { rsp, HttpEntity<String> entity ->
			called = true
			return rsp
		}

		for (HostInventory h : hosts) {
			called = false
			def pingMsg = new PingHostMsg()
			pingMsg.hostUuid = h.uuid
			bus.makeTargetServiceIdByResourceUuid(pingMsg, HostConstant.SERVICE_ID, h.uuid)
			MessageReply pingReply = bus.call(pingMsg)
			assert pingReply.success
			retryInSecs {
				assert called
			}
		}

		retryInSecs {
			AgentVersionInventory agentVersionInventory = queryAgentVersion {
				conditions = ["uuid=${host.uuid}".toString()]
			}[0] as AgentVersionInventory

			assert agentVersionInventory.agentType == AnsibleConstant.KVM_AGENT_NAME
			assert agentVersionInventory.currentVersion == "4.7.21"
			assert agentVersionInventory.expectVersion == dbf.getDbVersion()
		}

		env.afterSimulator(KVMConstant.KVM_PING_PATH) { KVMAgentCommands.PingResponse rsp, HttpEntity<String> e ->
			KVMAgentCommands.PingCmd pingCmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.PingCmd.class)
			if (pingCmd.hostUuid == host.uuid) {
				rsp.hostUuid = host.uuid
				rsp.version = dbf.getDbVersion()
			}
			rsp.success = true
			return rsp
		}

		env.preSimulator(KVMConstant.KVM_CONNECT_PATH) { HttpEntity<String> e ->
			KVMAgentCommands.ConnectCmd cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.ConnectCmd.class)
			upgradeChecker.updateAgentVersion(cmd.hostUuid, AnsibleConstant.KVM_AGENT_NAME, dbf.getDbVersion(), dbf.getDbVersion())
		}

		boolean flag = false
		env.afterSimulator(KVMConstant.KVM_CONNECT_PATH) { KVMAgentCommands.AgentResponse rsp, HttpEntity<String> e ->
			flag = true
			return rsp
		}

		// confirm inner connect host is not allowed during gray scale upgrade
		def connectHostmsg = new ConnectHostMsg()
		connectHostmsg.uuid = host.uuid
		bus.makeTargetServiceIdByResourceUuid(connectHostmsg, HostConstant.SERVICE_ID, host.uuid)
		bus.call(connectHostmsg)
		retryInSecs {
			assert !flag
		}

		// pick up some command to test gray scale upgrade checker
//		"org.zstack.storage.primary.smp.KvmBackend$CreateEmptyVolumeCmd": {
//			"kvmHostAddons": "3.10.38",
//			"name": "3.10.38",
//			"installPath": "3.10.38",
//			"backingFile": "4.8.10",
//			"mountPoint": "3.10.38",
//			"primaryStorageUuid": "3.10.38",
//			"size": "3.10.38",
//			"volumeUuid": "3.10.38"
//		}
		KVMHostAsyncHttpCallMsg message = new KVMHostAsyncHttpCallMsg()
		KVMAgentCommands.TakeVmConsoleScreenshotCmd cmd = new KVMAgentCommands.TakeVmConsoleScreenshotCmd()
		cmd.vmUuid = Platform.getUuid()
		message.setCommand(cmd)
		message.setHostUuid(host.uuid)
		message.setNoStatusCheck(true)
		message.setPath(KVMConstant.TAKE_VM_CONSOLE_SCREENSHOT_PATH)
		bus.makeTargetServiceIdByResourceUuid(message, HostConstant.SERVICE_ID, host.uuid)
		KVMHostAsyncHttpCallReply reply = bus.call(message).castReply()
		assert reply.error != null

		KVMAgentCommands.VmSyncCmd vmSyncCmd = new KVMAgentCommands.VmSyncCmd()
		message = new KVMHostAsyncHttpCallMsg()
		message.setCommand(vmSyncCmd)
		message.setHostUuid(host.uuid)
		message.setNoStatusCheck(true)
		message.setPath(KVMConstant.KVM_VM_SYNC_PATH)
		bus.makeTargetServiceIdByResourceUuid(message, HostConstant.SERVICE_ID, host.uuid)
		reply = bus.call(message).castReply()
		assert reply.error == null

		def updateHostOsCalled = false
		env.afterSimulator(KVMConstant.KVM_UPDATE_HOST_OS_PATH) { rsp, HttpEntity<String> entity ->
			updateHostOsCalled = true
			return rsp
		}
		// confirm api update one host in cluster is allow during gray scale upgrade
		expect(AssertionError.class) {
			updateClusterOS {
				uuid = host.clusterUuid
				hostUuid = host.uuid
			} as LongJobInventory
		}
		assert !updateHostOsCalled

		// confirm api update whole cluster os is allowed
		expect(AssertionError.class) {
			updateClusterOS {
				uuid = host.clusterUuid
			} as LongJobInventory
		}
		assert !updateHostOsCalled

		// test vm lifecycles during grayscale upgrade
		testVmLifeCycles(false, true, false, null)

		// confirm api reconnect host is allowed during gray scale upgrade
		flag = false
		ReconnectHostAction action = new ReconnectHostAction()
		action.uuid = host.getUuid()
		action.sessionId = adminSession()
		ReconnectHostAction.Result ret = action.call()
		assert ret.error == null
		retryInSecs {
			assert flag
			AgentVersionInventory agentVersionInventory = queryAgentVersion {
				conditions = ["uuid=${host.uuid}".toString()]
			}[0] as AgentVersionInventory

			assert agentVersionInventory.agentType == AnsibleConstant.KVM_AGENT_NAME
			assert agentVersionInventory.currentVersion == dbf.getDbVersion()
			assert agentVersionInventory.expectVersion == dbf.getDbVersion()
		}

		testVmLifeCycles(false, false, true, host.uuid.toString())

		// test host agent version null will be treat as disconnected
		env.afterSimulator(KVMConstant.KVM_PING_PATH) { KVMAgentCommands.PingResponse rsp, HttpEntity<String> e ->
			KVMAgentCommands.PingCmd pingCmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.PingCmd.class)
			rsp.version = null
			rsp.success = true
			return rsp
		}

		def hostDisconnectedCalled = false
		HostInventory host2 = env.inventoryByName("kvm2") as HostInventory
		def eventFacade = bean(EventFacade.class)
		def eventCallback = new EventCallback() {
			@Override
			protected void run(Map tokens, Object data) {
				HostCanonicalEvents.HostDisconnectedData d = (HostCanonicalEvents.HostDisconnectedData) data
				if (d.hostUuid == host2.uuid) {
					hostDisconnectedCalled = true
				}
			}
		}
		eventFacade.on(HostCanonicalEvents.HOST_DISCONNECTED_PATH, eventCallback)

		def pingMsg = new PingHostMsg()
		pingMsg.hostUuid = host2.uuid
		bus.makeTargetServiceIdByResourceUuid(pingMsg, HostConstant.SERVICE_ID, pingMsg.hostUuid)
		MessageReply pingReply = bus.call(pingMsg)
		assert pingReply.success

		retryInSecs {
			assert hostDisconnectedCalled
		}
		eventFacade.off(eventCallback)

		env.afterSimulator(KVMConstant.KVM_PING_PATH) { KVMAgentCommands.PingResponse rsp, HttpEntity<String> e ->
			return rsp
		}

		reconnectHost {
			uuid = host2.uuid
		}

		LongJobInventory longJob = updateClusterOS {
			uuid = host.clusterUuid
			hostUuid = host.uuid
		} as LongJobInventory
		retryInSecs {
			longJob = queryLongJob {
				conditions = ["uuid=${longJob.uuid}"]
			}[0] as LongJobInventory
			assert longJob.state == LongJobState.Succeeded
		}
		assert updateHostOsCalled

		// confirm api update whole cluster os is allowed
		expect(AssertionError.class) {
			updateClusterOS {
				uuid = host.clusterUuid
			} as LongJobInventory
		}

		// check close grayScale upgrade
		env.preSimulator(KVMConstant.KVM_CONNECT_PATH) { HttpEntity<String> e ->
		}
		updateGlobalConfig {
			category = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.category
			name = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.name
			value = false
		}

		flag = false
		connectHostmsg = new ConnectHostMsg()
		connectHostmsg.uuid = host.uuid
		bus.makeTargetServiceIdByResourceUuid(connectHostmsg, HostConstant.SERVICE_ID, host.uuid)
		bus.call(connectHostmsg)
		retryInSecs {
			assert flag
			assert queryAgentVersion {}.size() == 0
		}

		message = new KVMHostAsyncHttpCallMsg()
		cmd = new KVMAgentCommands.TakeVmConsoleScreenshotCmd()
		cmd.vmUuid = Platform.getUuid()
		message.setCommand(cmd)
		message.setHostUuid(host.uuid)
		message.setNoStatusCheck(true)
		message.setPath(KVMConstant.TAKE_VM_CONSOLE_SCREENSHOT_PATH)
		bus.makeTargetServiceIdByResourceUuid(message, HostConstant.SERVICE_ID, host.uuid)
		reply = bus.call(message).castReply()
		assert reply.error == null

		vmSyncCmd = new KVMAgentCommands.VmSyncCmd()
		message = new KVMHostAsyncHttpCallMsg()
		message.setCommand(vmSyncCmd)
		message.setHostUuid(host.uuid)
		message.setNoStatusCheck(true)
		message.setPath(KVMConstant.KVM_VM_SYNC_PATH)
		bus.makeTargetServiceIdByResourceUuid(message, HostConstant.SERVICE_ID, host.uuid)
		reply = bus.call(message).castReply()
		assert reply.error == null

		updateHostOsCalled = false
		longJob = updateClusterOS {
			uuid = host.clusterUuid
			hostUuid = host.uuid
		} as LongJobInventory
		retryInSecs {
			longJob = queryLongJob {
				conditions = ["uuid=${longJob.uuid}"]
			}[0] as LongJobInventory
			assert longJob.state == LongJobState.Succeeded
		}
		assert updateHostOsCalled
	}

	void mockAllVpcAgentVersions() {
		env.afterSimulator(VirtualRouterConstant.VR_PING) { VirtualRouterCommands.PingRsp rsp, HttpEntity<String> e ->
			rsp.version = "4.7.11"
			rsp.success = true
			return rsp
		}

		List<VirtualRouterVmInventory> virtualRouterVms = queryVirtualRouterVm {

		}

		for (VirtualRouterVmInventory vr : virtualRouterVms) {
			def pingMsg = new PingVirtualRouterVmMsg()
			pingMsg.virtualRouterVmUuid = vr.uuid
			bus.makeTargetServiceIdByResourceUuid(pingMsg, VmInstanceConstant.SERVICE_ID, vr.uuid)
			MessageReply pingReply = bus.call(pingMsg)
			assert pingReply.success
		}
	}

	void testVmLifeCycles(boolean created, boolean migrate, boolean destroyed, String avoidHostUuid) {
		mockAllVpcAgentVersions()
		InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
		ImageInventory image = env.inventoryByName("image1")
		L3NetworkInventory l3 = env.inventoryByName("l3")
		VmInstanceInventory vm = env.inventoryByName("vm")

		// create vm path
		expect(AssertionError.class) {
			String hostUuid = null
			if (avoidHostUuid != null) {
				hostUuid = Q.New(HostVO.class)
					.select(HostVO_.uuid)
					.notEq(HostVO_.uuid, avoidHostUuid)
					.limit(1)
					.findValue()
			}

			vm = createVmInstance {
				name = "vm-test"
				instanceOfferingUuid = instanceOffering.uuid
				imageUuid = image.uuid
				l3NetworkUuids = [l3.uuid]
				delegate.hostUuid = hostUuid
			} as VmInstanceInventory
		}

		// start stop vm path
		stopVmInstance {
			uuid = vm.uuid
		}

		vm = startVmInstance {
			uuid = vm.uuid
		} as VmInstanceInventory

		vm = rebootVmInstance {
			uuid = vm.uuid
		} as VmInstanceInventory

		if (migrate) {
			HostInventory host = queryHost {
				conditions = ["uuid!=${vm.hostUuid}"]
			}[0] as HostInventory
			vm = migrateVm {
				vmInstanceUuid = vm.uuid
				hostUuid = host.uuid
			} as VmInstanceInventory
		} else {
			vm = queryVmInstance {
				conditions = ["uuid=${vm.uuid}"]
			}[0] as VmInstanceInventory
			AgentVersionVO agent = Q.New(AgentVersionVO.class)
					.eq(AgentVersionVO_.uuid, vm.hostUuid)
					.find()
			if (agent.currentVersion != agent.expectVersion) {
				HostInventory host = queryHost {
					conditions = ["uuid!=${vm.hostUuid}"]
				}[0] as HostInventory
				vm = migrateVm {
					vmInstanceUuid = vm.uuid
					hostUuid = host.uuid
				} as VmInstanceInventory
			}
		}

		boolean called = false
		env.afterSimulator(KVMConstant.KVM_DESTROY_VM_PATH) { rsp, HttpEntity<String> entity ->
			called = true
			return rsp
		}
		// recover vm path
		destroyVmInstance {
			uuid = vm.uuid
		}

		if (destroyed) {
			assert called
			assert Q.New(VmInstanceVO.class)
					.eq(VmInstanceVO_.uuid, vm.uuid)
					.eq(VmInstanceVO_.state, VmInstanceState.Destroyed)
					.isExists()

			// just control plane recover no agent requirements
			recoverVmInstance {
				uuid = vm.uuid
			}

			startVmInstance {
				uuid = vm.uuid
			}
		} else {
			assert !called
			assert Q.New(VmInstanceVO.class)
					.eq(VmInstanceVO_.uuid, vm.uuid)
					.eq(VmInstanceVO_.state, VmInstanceState.Unknown)
					.isExists()

			// not destroyed vm recover will failed
			expect(AssertionError.class) {
				recoverVmInstance {
					uuid = vm.uuid
				}
			}
		}

		env.afterSimulator(KVMConstant.KVM_VM_SYNC_PATH) { rsp, HttpEntity<String> e ->
			def hostUuid = e.getHeaders().getFirst(Constants.AGENT_HTTP_HEADER_RESOURCE_UUID)

			if (hostUuid != vm.hostUuid) {
				return rsp
			} else {
				List<String> vmUuids = Q.New(VmInstanceVO.class)
						.select(VmInstanceVO_.uuid)
						.eq(VmInstanceVO_.hostUuid, vm.hostUuid)
						.listValues()

				vmUuids.each { vmUuid ->
					rsp.states[(vmUuid)] = KVMConstant.KvmVmState.Running.toString()
				}
			}

			return rsp
		}

		reconnectHost {
			uuid = vm.hostUuid
		}
		assert Q.New(VmInstanceVO.class)
				.eq(VmInstanceVO_.uuid, vm.uuid)
				.eq(VmInstanceVO_.state, VmInstanceState.Running)
				.isExists()

		// test vm console
		requestConsoleAccess {
			vmInstanceUuid = vm.uuid
		}
	}

	void testNoAgentVersionGrayscaleUpgradeWithStorageAgentCallChecker(PrimaryStorageInventory ps, AtomicInteger agentCallCounter, boolean pingPsNoAgentCalled) {
		updateGlobalConfig {
			category = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.category
			name = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.name
			value = true
		}

        List<HostInventory> hosts = queryHost {}

		// let one host have agent version
		def pingMsg = new PingHostMsg()
		pingMsg.hostUuid = hosts[0].uuid
		bus.makeTargetServiceIdByResourceUuid(pingMsg, HostConstant.SERVICE_ID, pingMsg.hostUuid)
		MessageReply pingReply = bus.call(pingMsg)
		assert pingReply.success

		def connectMessage = notifyWhenReceivedMessage(ConnectPrimaryStorageReply.class)
		def recalculateCapacity = notifyWhenReceivedMessage(RecalculatePrimaryStorageCapacityReply.class)
		PrimaryStorageManagerImpl primaryStorageManager = bean(PrimaryStorageManagerImpl.class)
		primaryStorageManager.managementNodeReady()

		retryInSecs {
			connectMessage.assertCalledOnce()
			recalculateCapacity.assertCalled()
		}

		assert agentCallCounter.get() == 1
		assert !Q.New(PrimaryStorageVO.class)
				.eq(PrimaryStorageVO_.uuid, ps.uuid)
				.eq(PrimaryStorageVO_.status, PrimaryStorageStatus.Disconnected)
				.isExists()

		agentCallCounter.set(0)
		PingPrimaryStorageMsg msg = new PingPrimaryStorageMsg()
		msg.setPrimaryStorageUuid(ps.uuid)
		bus.makeTargetServiceIdByResourceUuid(msg, PrimaryStorageConstant.SERVICE_ID, msg.getPrimaryStorageUuid())
		bus.call(msg)

		if (pingPsNoAgentCalled) {
			assert agentCallCounter.get() == 0
		} else {
			assert agentCallCounter.get() == 1
		}
		assert !Q.New(PrimaryStorageVO.class)
				.eq(PrimaryStorageVO_.uuid, ps.uuid)
				.eq(PrimaryStorageVO_.status, PrimaryStorageStatus.Disconnected)
				.isExists()

		connectMessage.delete()
		recalculateCapacity.delete()

		updateGlobalConfig {
			category = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.category
			name = UpgradeGlobalConfig.GRAYSCALE_UPGRADE.name
			value = false
		}
	}

	void testKvmAgentBasedStorages() {
		ZoneInventory zone = env.inventoryByName("zone")
		ClusterInventory cluster = env.inventoryByName("cluster")
		PrimaryStorageInventory ps = env.inventoryByName("local")
		AtomicInteger agentCallCounter = new AtomicInteger(0)
		env.afterSimulator(LocalStorageKvmBackend.CHECK_INITIALIZED_FILE) { rsp, HttpEntity<String> e ->
			agentCallCounter.incrementAndGet()
			return rsp
		}

		testNoAgentVersionGrayscaleUpgradeWithStorageAgentCallChecker(ps, agentCallCounter, false)

		detachPrimaryStorageFromCluster {
			clusterUuid = cluster.uuid
			primaryStorageUuid = ps.uuid
		}

		deletePrimaryStorage {
			uuid = ps.uuid
		}

		env.simulator(NfsPrimaryStorageKVMBackend.MOUNT_PRIMARY_STORAGE_PATH) { HttpEntity<java.lang.String> e, EnvSpec espec ->
			def rsp = new NfsPrimaryStorageKVMBackendCommands.MountAgentResponse()
			rsp.totalCapacity = SizeUtils.sizeStringToBytes("10T")
			rsp.availableCapacity = SizeUtils.sizeStringToBytes("5T")
			return rsp
		}

		env.simulator(NfsPrimaryStorageKVMBackend.REMOUNT_PATH) { HttpEntity<java.lang.String> e, EnvSpec espec ->
			def rsp = new NfsPrimaryStorageKVMBackendCommands.NfsPrimaryStorageAgentResponse()
			rsp.totalCapacity = SizeUtils.sizeStringToBytes("10T")
			rsp.availableCapacity = SizeUtils.sizeStringToBytes("5T")
			return rsp
		}

		ps = addNfsPrimaryStorage {
			name = "nfs"
			url = "127.0.0.3:/nfs2"
			zoneUuid = zone.uuid
		} as PrimaryStorageInventory

		attachPrimaryStorageToCluster {
			primaryStorageUuid = ps.uuid
			clusterUuid = cluster.uuid
		}

		agentCallCounter.set(0)
		env.afterSimulator(NfsPrimaryStorageKVMBackend.REMOUNT_PATH) { rsp, HttpEntity<String> e ->
			agentCallCounter.incrementAndGet()
			return rsp
		}
		env.afterSimulator(NfsPrimaryStorageKVMBackend.PING_PATH) { rsp, HttpEntity<String> e ->
			agentCallCounter.incrementAndGet()
			return rsp
		}
		testNoAgentVersionGrayscaleUpgradeWithStorageAgentCallChecker(ps, agentCallCounter, false)

		detachPrimaryStorageFromCluster {
			clusterUuid = cluster.uuid
			primaryStorageUuid = ps.uuid
		}

		deletePrimaryStorage {
			uuid = ps.uuid
		}

		env.simulator(KvmBackend.CONNECT_PATH) {
			return new KvmBackend.ConnectRsp()
		}
		ps = addSharedMountPointPrimaryStorage {
			name = "test-smp"
			url = "/mount_point"
			zoneUuid = zone.uuid
		} as PrimaryStorageInventory

		attachPrimaryStorageToCluster {
			primaryStorageUuid = ps.uuid
			clusterUuid = cluster.uuid
		}

		agentCallCounter.set(0)
		env.afterSimulator(KvmBackend.CONNECT_PATH) { rsp, HttpEntity<String> e ->
			agentCallCounter.incrementAndGet()
			return rsp
		}
		testNoAgentVersionGrayscaleUpgradeWithStorageAgentCallChecker(ps, agentCallCounter, true)

		detachPrimaryStorageFromCluster {
			clusterUuid = cluster.uuid
			primaryStorageUuid = ps.uuid
		}

		deletePrimaryStorage {
			uuid = ps.uuid
		}

		ps = addSharedBlockGroupPrimaryStorage {
			name = "test-sblk"
			diskUuids = [randomUUID() as java.lang.String]
			zoneUuid = zone.uuid
		} as PrimaryStorageInventory

		attachPrimaryStorageToCluster {
			primaryStorageUuid = ps.uuid
			clusterUuid = cluster.uuid
		}

		agentCallCounter.set(0)
		env.afterSimulator(SharedBlockKvmCommands.CONNECT_PATH) { rsp, HttpEntity<String> e ->
			agentCallCounter.incrementAndGet()
			return rsp
		}
		env.afterSimulator(SharedBlockKvmCommands.PING_PATH) { rsp, HttpEntity<String> e ->
			agentCallCounter.incrementAndGet()
			return rsp
		}
		testNoAgentVersionGrayscaleUpgradeWithStorageAgentCallChecker(ps, agentCallCounter, false)

		detachPrimaryStorageFromCluster {
			clusterUuid = cluster.uuid
			primaryStorageUuid = ps.uuid
		}

		deletePrimaryStorage {
			uuid = ps.uuid
		}
	}
}

