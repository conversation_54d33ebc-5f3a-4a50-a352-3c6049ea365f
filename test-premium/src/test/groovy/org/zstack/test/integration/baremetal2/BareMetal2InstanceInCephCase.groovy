package org.zstack.test.integration.baremetal2

import org.springframework.http.HttpEntity
import org.zstack.baremetal2.BareMetal2GlobalConfig
import org.zstack.baremetal2.BareMetal2SystemTags
import org.zstack.baremetal2.chassis.BareMetal2ChassisConstant
import org.zstack.baremetal2.chassis.BareMetal2ChassisState
import org.zstack.baremetal2.chassis.BareMetal2ChassisStatus
import org.zstack.baremetal2.chassis.BareMetal2ChassisVO
import org.zstack.baremetal2.chassis.BareMetal2ChassisVO_
import org.zstack.baremetal2.chassis.ipmi.BareMetal2IpmiChassisHelper
import org.zstack.baremetal2.gateway.BareMetal2GatewayCommands
import org.zstack.baremetal2.gateway.BareMetal2GatewayConstant
import org.zstack.baremetal2.gateway.BareMetal2GatewayVO
import org.zstack.baremetal2.instance.BareMetal2InstanceConstant
import org.zstack.baremetal2.instance.BareMetal2InstanceStatus
import org.zstack.baremetal2.instance.BareMetal2InstanceVO
import org.zstack.baremetal2.instance.BareMetal2InstanceVO_
import org.zstack.baremetal2.instance.PingBareMetal2InstanceThroughGatewayMsg
import org.zstack.baremetal2.instance.PingBareMetal2InstanceThroughGatewayReply
import org.zstack.compute.vm.VmGlobalConfig
import org.zstack.compute.vm.VmSystemTags
import org.zstack.core.Platform
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.cloudbus.CloudBusGson
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.host.HostState
import org.zstack.header.message.JsonSchemaBuilder
import org.zstack.header.message.Message
import org.zstack.header.storage.primary.AllocatePrimaryStorageMsg
import org.zstack.header.storage.primary.AllocatePrimaryStorageReply
import org.zstack.header.vm.StartVmOnHypervisorMsg
import org.zstack.header.vm.StartVmOnHypervisorReply
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmInstanceVO
import org.zstack.header.volume.VolumeStatus
import org.zstack.header.volume.VolumeType
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

import java.util.concurrent.TimeUnit

import static org.zstack.utils.BeanUtils.getProperty
import static org.zstack.utils.BeanUtils.setProperty

/**
 * Created by GuoYi on 03/22/21.
 */
class BareMetal2InstanceInCephCase extends PremiumSubCase {
    EnvSpec env
    BareMetal2InstanceInventory bm
    String rootVolumeUuid
    String dataVolumeUuid

    @Override
    void setup() {
        spring {
            useSpring(BareMetal2Test.springSpec)
        }
    }

    @Override
    void environment() {
        env = makeEnv {
            zone {
                name = "zone"

                diskOffering {
                    name = "diskOffering"
                    diskSize = SizeUnit.GIGABYTE.toByte(10)
                }

                bareMetal2ProvisionNetwork {
                    name = "provision_net_1"
                    dhcpInterface = "eth0"
                    dhcpRangeStartIp = "***********0"
                    dhcpRangeEndIp = "***********00"
                    dhcpRangeNetmask = "*************"
                    dhcpRangeGateway = "***********"
                }

                cephPrimaryStorage {
                    name = "ceph-ps"
                    description = "Test"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(100)
                    url = "ceph://pri"
                    fsid = "78f218d9-f525-435f-8a40-3618d1772a64"
                    monUrls = ["root:password@localhost/?monPort=7777"]
                }

                cluster {
                    name = "kvm_cluster_1"

                    kvm {
                        name = "kvm-1"
                        managementIp = "127.0.0.10"
                        username = "root"
                        password = "password"
                    }
                }

                bareMetal2Cluster {
                    name = "bm2_cluster_1"

                    bareMetal2Gateway {
                        name = "gateway-1"
                        managementIp = "127.0.0.11"
                        username = "root"
                        password = "password"
                    }

                    bareMetal2Chassis {
                        name = "chassis-1"
                        ipmiAddress = "*********"
                        ipmiUsername = "username"
                        ipmiPassword = "password"

                        hardwareInfo {
                            info = "{'bootMode':'UEFI', 'architecture':'x86_64', 'cpuModelName':'Intel i7-6700K', 'cpuNum':'8', 'memorySize':'33421254656'," +
                                    "'nics':[{'nicMac':'40:8d:5c:f7:8d:60', 'nicSpeed':'1000Mbps', 'isProvisionNic':'true', 'nicName': 'eth0'}]}"
                        }
                    }

                    bareMetal2Chassis {
                        name = "chassis-2"
                        ipmiAddress = "*********"
                        ipmiUsername = "username"
                        ipmiPassword = "password"
                    }

                    attachPrimaryStorage("ceph-ps")
                    attachProvisionNetworks("provision_net_1")
                }

                bareMetal2Cluster {
                    name = "bm2_cluster_2"

                    bareMetal2Gateway {
                        name = "gateway-2"
                        managementIp = "127.0.0.12"
                        username = "root"
                        password = "password"
                    }

                    bareMetal2Chassis {
                        name = "chassis-3"
                        ipmiAddress = "*********"
                        ipmiUsername = "username"
                        ipmiPassword = "password"

                        hardwareInfo {
                            info = "{'bootMode':'UEFI', 'architecture':'x86_64', 'cpuModelName':'Intel i7-6700K', 'cpuNum':'8', 'memorySize':'33421254656'," +
                                    "'nics':[{'nicMac':'40:8d:5c:f7:8d:61', 'nicSpeed':'1000Mbps', 'isProvisionNic':'true', 'nicName': 'eth0'}]}"
                        }
                    }

                    bareMetal2Chassis {
                        name = "chassis-4"
                        ipmiAddress = "*********"
                        ipmiUsername = "username"
                        ipmiPassword = "password"
                    }

                    attachPrimaryStorage("ceph-ps")
                    attachProvisionNetworks("provision_net_1")
                }

                attachBackupStorage("imagestore")
            }

            imageStore {
                name = "imagestore"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "vm_iso"
                    mediaType = "ISO"
                    format = "iso"
                    url = "http://zstack.org/download/test.iso"
                }

                image {
                    name = "vm_raw"
                    format = "raw"
                    url = "http://zstack.org/download/test.raw"
                }

                image {
                    name = "bm_raw"
                    format = "raw"
                    url = "http://zstack.org/download/test.raw"
                    systemTags = ["baremetal2", "bootMode::UEFI"]
                }

                image {
                    name = "bm_qcow2"
                    format = "qcow2"
                    url = "http://zstack.org/download/test.qcow2"
                    systemTags = ["baremetal2", "bootMode::UEFI"]
                }
            }
        }
    }

    @Override
    void test() {
        env.create {
            initEnv()

            testCreateBareMetal2InstanceWithErrors()
            testCreateBareMetal2InstanceRollback()
            testCreateBareMetal2Instance()
            testQueryBareMetal2Instance()
            testChangeInstanceBootMode()
            testChangeInstancePassword()
            testStopBareMetal2Instance()
            testStartBareMetal2Instance()
            testRebootBareMetal2Instance()
            testReconnectBareMetal2Instance()
            testPowerOffBareMetal2Chassis()
            testDestroyBareMetal2Instance()
            testRestoreBareMetal2Instance()
            testExpungeBareMetal2Instance()
        }
    }

    static void initEnv() {
        BareMetal2GlobalConfig.BAREMETAL2_INSTANCE_PING_INTERVAL.updateValue(1)
    }

    void testCreateBareMetal2InstanceWithErrors() {
        def vmImage1 = env.inventoryByName("vm_iso") as ImageInventory
        def vmImage2 = env.inventoryByName("vm_raw") as ImageInventory
        def bmImage1 = env.inventoryByName("bm_raw") as ImageInventory
        def bmImage2 = env.inventoryByName("bm_qcow2") as ImageInventory
        def vmCluster = env.inventoryByName("kvm_cluster_1") as ClusterInventory
        def bmCluster = env.inventoryByName("bm2_cluster_1") as ClusterInventory
        def chassis1 = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory
        def gateway1 = env.inventoryByName("gateway-1") as BareMetal2GatewayInventory
        def gateway2 = env.inventoryByName("gateway-2") as BareMetal2GatewayInventory

        // not baremetal2 image
        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_1"
                imageUuid = vmImage1.uuid
                clusterUuid = bmCluster.uuid
                chassisUuid = chassis1.uuid
            }
        }

        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_2"
                imageUuid = vmImage2.uuid
                clusterUuid = bmCluster.uuid
                chassisUuid = chassis1.uuid
            }
        }

        // not baremetal2 cluster
        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_3"
                imageUuid = bmImage1.uuid
                clusterUuid = vmCluster.uuid
                chassisOfferingUuid = chassis1.chassisOfferingUuid
            }
        }

        // chassis and gateway not in same cluster
        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_4"
                imageUuid = bmImage2.uuid
                gatewayUuid = gateway2.uuid
                chassisUuid = chassis1.uuid
            }
        }

        // gateway disabled
        changeBareMetal2GatewayState {
            uuid = gateway1.uuid
            stateEvent = "disable"
        }

        def gw = dbFindByUuid(gateway1.uuid, BareMetal2GatewayVO.class) as BareMetal2GatewayVO
        assert gw.state == HostState.Disabled

        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_5"
                imageUuid = bmImage1.uuid
                clusterUuid = bmCluster.uuid
                gatewayUuid = gateway1.uuid
                chassisUuid = chassis1.uuid
            }
        }

        changeBareMetal2GatewayState {
            uuid = gateway1.uuid
            stateEvent = "enable"
        }

        gw = dbFindByUuid(gateway1.uuid, BareMetal2GatewayVO.class) as BareMetal2GatewayVO
        assert gw.state == HostState.Enabled

        // chassis disabled
        changeBareMetal2ChassisState {
            uuid = chassis1.uuid
            stateEvent = "disable"
        }

        def chassis = dbFindByUuid(chassis1.uuid, BareMetal2ChassisVO.class) as BareMetal2ChassisVO
        assert chassis.state == BareMetal2ChassisState.Disabled

        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_6"
                imageUuid = bmImage1.uuid
                clusterUuid = bmCluster.uuid
                chassisUuid = chassis1.uuid
            }
        }

        changeBareMetal2ChassisState {
            uuid = chassis1.uuid
            stateEvent = "enable"
        }

        chassis = dbFindByUuid(chassis1.uuid, BareMetal2ChassisVO.class) as BareMetal2ChassisVO
        assert chassis.state == BareMetal2ChassisState.Enabled

        // neither chassisUuid nor chassisOfferingUuid is set
        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_7"
                imageUuid = bmImage1.uuid
                clusterUuid = bmCluster.uuid
            }
        }

        // both chassisUuid and chassisOfferingUuid are set
        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_8"
                imageUuid = bmImage1.uuid
                clusterUuid = bmCluster.uuid
                chassisUuid = chassis1.uuid
                chassisOfferingUuid = chassis1.chassisOfferingUuid
            }
        }

        // cannot decide zoneUuid
        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_9"
                imageUuid = bmImage1.uuid
                chassisOfferingUuid = chassis1.chassisOfferingUuid
            }
        }
    }

    void testCreateBareMetal2InstanceRollback() {
        def image_qcow2 = env.inventoryByName("bm_qcow2") as ImageInventory
        def cluster = env.inventoryByName("bm2_cluster_1") as ClusterInventory
        def chassis = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory

        env.message(AllocatePrimaryStorageMsg.class) { AllocatePrimaryStorageMsg msg, CloudBus bus ->
            AllocatePrimaryStorageReply reply = new AllocatePrimaryStorageReply()
            reply.setError(Platform.operr("on purpose"))
            bus.reply(msg, reply)
        }

        expect(AssertionError.class) {
            createBareMetal2Instance {
                name = "BM_1"
                imageUuid = image_qcow2.uuid
                clusterUuid = cluster.uuid
                chassisUuid = chassis.uuid
            } as BareMetal2InstanceInventory
        }

        retryInSecs {
            assert !Q.New(BareMetal2InstanceVO.class).isExists()
            assert Q.New(BareMetal2ChassisVO.class)
                    .eq(BareMetal2ChassisVO_.uuid, chassis.uuid)
                    .eq(BareMetal2ChassisVO_.status, BareMetal2ChassisStatus.Available)
                    .isExists()
        }

        env.cleanMessageHandlers()
    }

    void testCreateBareMetal2Instance() {
        def image_raw = env.inventoryByName("bm_raw") as ImageInventory
        def image_qcow2 = env.inventoryByName("bm_qcow2") as ImageInventory
        def zone = env.inventoryByName("zone") as ZoneInventory
        def cluster = env.inventoryByName("bm2_cluster_1") as ClusterInventory
        def chassis = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory
        def gateway = env.inventoryByName("gateway-1") as BareMetal2GatewayInventory
        def cephps = env.inventoryByName("ceph-ps") as PrimaryStorageInventory
        def diskOffering = env.inventoryByName("diskOffering") as DiskOfferingInventory

        // create bm with chassis
        bm = createBareMetal2Instance {
            name = "BM_1"
            imageUuid = image_raw.uuid
            clusterUuid = cluster.uuid
            chassisUuid = chassis.uuid
            dataDiskOfferingUuids = [diskOffering.uuid]
            systemTags = ["autoReleaseBareMetal2Chassis"]
        } as BareMetal2InstanceInventory

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.imageUuid == image_raw.uuid
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.lastGatewayUuid == null
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == null
            assert bmVO.chassisOfferingUuid == null
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
            assert bmVO.hypervisorType == BareMetal2GatewayConstant.BM2_HYPERVISOR_TYPE
            assert bmVO.vmNics.size() == 0
            assert bmVO.allVolumes.size() == 2
            assert (bmVO.allVolumes[0] as VolumeVO).primaryStorageUuid == cephps.uuid
            assert VmSystemTags.BOOT_MODE.getTokenByResourceUuid(bm.uuid, VmSystemTags.BOOT_MODE_TOKEN) == "UEFI"
            assert BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)
        }

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "disable"
        }
        assert !BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        stopVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == null
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Allocated
        }

        env.message(StartVmOnHypervisorMsg.class) { StartVmOnHypervisorMsg msg, CloudBus bus ->
            StartVmOnHypervisorReply reply = new StartVmOnHypervisorReply()
            reply.setError(Platform.operr("on purpose"))
            bus.reply(msg, reply)
        }

        expect(AssertionError.class) {
            startBareMetal2Instance {
                uuid = bm.uuid
            }
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == null
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Allocated
        }

        env.cleanMessageHandlers()

        startBareMetal2Instance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == null
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
        }

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "disable"
        }
        assert !BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "enable"
        }
        assert BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "enable"
        }
        assert BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "disable"
        }
        assert !BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "enable"
        }
        assert BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        stopVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == null
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == null
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available
        }

        expect(AssertionError.class) {
            startBareMetal2Instance {
                uuid = bm.uuid
            }
        }

        startBareMetal2Instance {
            uuid = bm.uuid
            chassisUuid = chassis.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == null
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
        }

        rootVolumeUuid = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, bm.uuid)
                .eq(VolumeVO_.type, VolumeType.Root)
                .select(VolumeVO_.uuid)
                .findValue()

        dataVolumeUuid = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, bm.uuid)
                .eq(VolumeVO_.type, VolumeType.Data)
                .select(VolumeVO_.uuid)
                .limit(1)
                .findValue()

        VmGlobalConfig.VM_DELETION_POLICY.updateValue("Direct")
        VmGlobalConfig.DELETE_DATA_VOLUME_ON_VM_DESTROY.updateValue("true")

        destroyVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO == null
            assert dbFindByUuid(rootVolumeUuid, VolumeVO.class) as VolumeVO == null

            def dv = dbFindByUuid(dataVolumeUuid, VolumeVO.class) as VolumeVO
            assert dv != null
            assert dv.status == VolumeStatus.Deleted

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available
        }

        // create bm instance with chassis offering and zone
        bm = createBareMetal2Instance {
            name = "BM_1"
            imageUuid = image_qcow2.uuid
            zoneUuid = zone.uuid
            chassisOfferingUuid = chassis.chassisOfferingUuid
            dataDiskOfferingUuids = [diskOffering.uuid]
        } as BareMetal2InstanceInventory

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.imageUuid == image_qcow2.uuid
            assert bmVO.zoneUuid == zone.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
            assert bmVO.hypervisorType == BareMetal2GatewayConstant.BM2_HYPERVISOR_TYPE
            assert bmVO.vmNics.size() == 0
            assert bmVO.allVolumes.size() == 2
            assert (bmVO.allVolumes[0] as VolumeVO).primaryStorageUuid == cephps.uuid
            assert VmSystemTags.BOOT_MODE.getTokenByResourceUuid(bm.uuid, VmSystemTags.BOOT_MODE_TOKEN) == "UEFI"
        }

        rootVolumeUuid = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, bm.uuid)
                .eq(VolumeVO_.type, VolumeType.Root)
                .select(VolumeVO_.uuid)
                .findValue()

        dataVolumeUuid = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, bm.uuid)
                .eq(VolumeVO_.type, VolumeType.Data)
                .select(VolumeVO_.uuid)
                .limit(1)
                .findValue()

        VmGlobalConfig.VM_DELETION_POLICY.updateValue("Delay")
        VmGlobalConfig.DELETE_DATA_VOLUME_ON_VM_DESTROY.updateValue("false")

        destroyVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO != null
            assert bmVO.state == VmInstanceState.Destroyed
            assert bmVO.status == BareMetal2InstanceStatus.Disconnected
            assert bmVO.getRootVolume().uuid == rootVolumeUuid
            assert !bmVO.getAllDiskVolumes().contains(dataVolumeUuid)

            assert dbFindByUuid(rootVolumeUuid as String, VolumeVO.class) as VolumeVO != null
            assert dbFindByUuid(dataVolumeUuid as String, VolumeVO.class) as VolumeVO != null
        }

        expungeVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO == null
            assert dbFindByUuid(rootVolumeUuid, VolumeVO.class) as VolumeVO == null
            assert dbFindByUuid(dataVolumeUuid, VolumeVO.class) as VolumeVO != null

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available
        }

        // create bm instance with chassis offering and cluster
        bm = createBareMetal2Instance {
            name = "BM_1"
            imageUuid = image_qcow2.uuid
            clusterUuid = cluster.uuid
            chassisOfferingUuid = chassis.chassisOfferingUuid
            dataDiskOfferingUuids = [diskOffering.uuid]
            systemTags = ["autoReleaseBareMetal2Chassis"]
        } as BareMetal2InstanceInventory

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.imageUuid == image_qcow2.uuid
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.lastGatewayUuid == null
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == null
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
            assert bmVO.hypervisorType == BareMetal2GatewayConstant.BM2_HYPERVISOR_TYPE
            assert bmVO.vmNics.size() == 0
            assert bmVO.allVolumes.size() == 2
            assert (bmVO.allVolumes[0] as VolumeVO).primaryStorageUuid == cephps.uuid
            assert VmSystemTags.BOOT_MODE.getTokenByResourceUuid(bm.uuid, VmSystemTags.BOOT_MODE_TOKEN) == "UEFI"

            assert 1L == Q.New(VmInstanceVO.class).count()
            assert 1L == Q.New(BareMetal2InstanceVO.class).count()
        }

        rootVolumeUuid = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, bm.uuid)
                .eq(VolumeVO_.type, VolumeType.Root)
                .select(VolumeVO_.uuid)
                .findValue()

        dataVolumeUuid = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, bm.uuid)
                .eq(VolumeVO_.type, VolumeType.Data)
                .select(VolumeVO_.uuid)
                .limit(1)
                .findValue()

        assert BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        stopVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == null
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available
        }

        startBareMetal2Instance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
        }

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "disable"
        }

        assert !BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        stopVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Allocated
        }

        startBareMetal2Instance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
        }

        updateBareMetal2Instance {
            uuid = bm.uuid
            autoReleaseChassisEvent = "enable"
        }
    }

    void testQueryBareMetal2Instance() {
        def inv = queryBareMetal2Instance {}[0] as BareMetal2InstanceInventory
        assert inv.provisionNic != null
        assert inv.provisionNic.uuid == bm.uuid
    }

    void testChangeInstanceBootMode() {
        expect(AssertionError.class) {
            setVmBootMode {
                uuid = bm.uuid
                bootMode = "Legacy"
            }
        }

        expect(AssertionError.class) {
            deleteVmBootMode {
                uuid = bm.uuid
            }
        }

        assert VmSystemTags.BOOT_MODE.getTokenByResourceUuid(bm.uuid, VmSystemTags.BOOT_MODE_TOKEN) == "UEFI"
    }

    void testChangeInstancePassword() {
        BareMetal2GatewayCommands.ChangeInstanceSystemPasswordCmd changePasswordCmd = null
        env.afterSimulator(BareMetal2InstanceConstant.CHANGE_BM_INSTANCE_SYSTEM_PASSWORD_PATH) { rsp, HttpEntity<String> e ->
            changePasswordCmd = JSONObjectUtil.toObject(e.body, BareMetal2GatewayCommands.ChangeInstanceSystemPasswordCmd.class)
            return rsp
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
        }

        changeBareMetal2InstancePassword {
            uuid = bm.uuid
            username = "root"
            password = "new_password"
        }

        assert changePasswordCmd != null
        assert changePasswordCmd.getBmInstance().getUuid() == bm.uuid
        assert changePasswordCmd.getUsername() == "root"
        assert changePasswordCmd.getPassword() == "new_password"
    }

    void testStopBareMetal2Instance() {
        def gateway = env.inventoryByName("gateway-1") as BareMetal2GatewayInventory
        def chassis = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory

        assert BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        updateGlobalConfig {
            category = BareMetal2GlobalConfig.CATEGORY
            name = BareMetal2GlobalConfig.CHECK_CHASSIS_POWER_STATUS_TIMEOUT.name
            value = 1
        }

        boolean instanceCfgDeleted = false
        env.afterSimulator(BareMetal2GatewayConstant.DELETE_CONFIGURATIONS_FOR_INSTANCE_PATH) { rsp, HttpEntity<String> e ->
            instanceCfgDeleted = true
            return rsp
        }

        // simulate chassis still power on
        def helper = bean(BareMetal2IpmiChassisHelper.class)
        helper.setMockedPowerStatus(BareMetal2ChassisConstant.CHASSIS_POWER_ON)

        expect(AssertionError.class) {
            stopVmInstance {
                uuid = bm.uuid
            }
        }

        assert !instanceCfgDeleted

        // recover the simulation
        helper.setMockedPowerStatus(null)

        stopVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == null
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available

            assert instanceCfgDeleted
        }
    }

    void testStartBareMetal2Instance() {
        BareMetal2GlobalConfig.BAREMETAL2_INSTANCE_PING_INTERVAL.updateValue(60)

        def cluster = env.inventoryByName("bm2_cluster_1") as ClusterInventory
        def chassis = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory
        def gateway = env.inventoryByName("gateway-1") as BareMetal2GatewayInventory

        env.message(StartVmOnHypervisorMsg.class) { StartVmOnHypervisorMsg msg, CloudBus bus ->
            StartVmOnHypervisorReply reply = new StartVmOnHypervisorReply()
            reply.setError(Platform.operr("on purpose"))
            bus.reply(msg, reply)
        }

        expect(AssertionError.class) {
            startBareMetal2Instance {
                uuid = bm.uuid
            }
        }

        reconnectBareMetal2Instance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == null
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available
        }

        env.cleanMessageHandlers()

        // chassis offering already been set
        expect(AssertionError.class) {
            startBareMetal2Instance {
                uuid = bm.uuid
                chassisOfferingUuid = chassis.chassisOfferingUuid
            }
        }

        // chassis offering not exist
        expect(AssertionError.class) {
            startBareMetal2Instance {
                uuid = bm.uuid
                chassisOfferingUuid = Platform.getUuid()
            }
        }

        // chassis and chassis offering set at the same time
        expect(AssertionError.class) {
            startBareMetal2Instance {
                uuid = bm.uuid
                chassisUuid = chassis.uuid
                chassisOfferingUuid = chassis.chassisOfferingUuid
            }
        }

        // simulate bm2 instance chassis offering uuid is null
        SQL.New(BareMetal2InstanceVO.class)
                .eq(BareMetal2InstanceVO_.uuid, bm.uuid)
                .set(BareMetal2InstanceVO_.chassisOfferingUuid, null)
                .update()

        startBareMetal2Instance {
            uuid = bm.uuid
            chassisOfferingUuid = chassis.chassisOfferingUuid
        }

        reconnectBareMetal2Instance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.clusterUuid == cluster.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Allocated
        }

        stopVmInstance {
            uuid = bm.uuid
        }

        reconnectBareMetal2Instance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == null
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available
        }

        // test json
        StartVmOnHypervisorMsg smsg = null
        env.message(StartVmOnHypervisorMsg.class) { StartVmOnHypervisorMsg msg, CloudBus bus ->
            smsg = msg
            bus.reply(smsg, new StartVmOnHypervisorReply())
        }

        startBareMetal2Instance {
            uuid = bm.uuid
        }

        assert smsg != null
        smsg.putHeaderEntry(CloudBus.HEADER_SCHEMA, new JsonSchemaBuilder(smsg).build())
        String jsonString = CloudBusGson.toJson(smsg)
        Message msg = CloudBusGson.fromJson(jsonString)
        Map raw = JSONObjectUtil.toObject(jsonString, LinkedHashMap.class)
        raw = (Map) raw.values().iterator().next()
        Map<String, String> schema = msg.getHeaderEntry(CloudBus.HEADER_SCHEMA)
        assert schema != null && !schema.isEmpty()
        List<String> paths = new ArrayList<>(schema.keySet())

        for (String p : paths) {
            Object dst = getProperty(msg, p)
            String type = schema.get(p)
            if (dst.getClass().getName() == type) {
                continue
            }

            Class clz = Class.forName(type)
            setProperty(msg, p, rehashObject(getProperty(raw, p), clz))
            dst = getProperty(msg, p)
            assert dst.getClass().getName() == type
        }

        reconnectBareMetal2Instance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.clusterUuid == cluster.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Allocated
        }

        BareMetal2GlobalConfig.BAREMETAL2_INSTANCE_PING_INTERVAL.updateValue(1)
    }

    static <T> T rehashObject(Object obj, Class<T> clazz) {
        return CloudBusGson.fromJson(CloudBusGson.toJson(obj), clazz)
    }

    void testRebootBareMetal2Instance() {
        def cluster = env.inventoryByName("bm2_cluster_1") as ClusterInventory
        def chassis = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory
        def gateway = env.inventoryByName("gateway-1") as BareMetal2GatewayInventory

        env.message(StartVmOnHypervisorMsg.class) { StartVmOnHypervisorMsg msg, CloudBus bus ->
            StartVmOnHypervisorReply reply = new StartVmOnHypervisorReply()
            reply.setError(Platform.operr("on purpose"))
            bus.reply(msg, reply)
        }

        expect(AssertionError.class) {
            rebootVmInstance {
                uuid = bm.uuid
            }
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.clusterUuid == cluster.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Running

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Allocated
        }

        env.cleanMessageHandlers()

        rebootVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.clusterUuid == cluster.uuid
            assert bmVO.chassisUuid == chassis.uuid
            assert bmVO.gatewayUuid == gateway.uuid
            assert bmVO.hostUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Allocated
        }
    }

    void testReconnectBareMetal2Instance() {
        BareMetal2GlobalConfig.BAREMETAL2_INSTANCE_PING_INTERVAL.updateValue(60)

        env.message(PingBareMetal2InstanceThroughGatewayMsg.class) { PingBareMetal2InstanceThroughGatewayMsg msg, CloudBus bus ->
            PingBareMetal2InstanceThroughGatewayReply reply = new PingBareMetal2InstanceThroughGatewayReply()
            reply.setError(Platform.operr("on purpose"))
            bus.reply(msg, reply)
        }

        expect(AssertionError.class) {
            reconnectBareMetal2Instance {
                uuid = bm.uuid
            }
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Disconnected
        }

        env.cleanMessageHandlers()

        BareMetal2GlobalConfig.BAREMETAL2_INSTANCE_PING_INTERVAL.updateValue(1)

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.state == VmInstanceState.Running
            assert bmVO.status == BareMetal2InstanceStatus.Connected
        }
    }

    void testPowerOffBareMetal2Chassis() {
        def gateway = env.inventoryByName("gateway-1") as BareMetal2GatewayInventory
        def chassis = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory

        powerOffBareMetal2Chassis {
            uuid = chassis.uuid
        }

        assert BareMetal2SystemTags.AUTO_RELEASE_BAREMETAL2_CHASSIS.hasTag(bm.uuid)

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO.chassisUuid == null
            assert bmVO.lastChassisUuid == chassis.uuid
            assert bmVO.chassisOfferingUuid == chassis.chassisOfferingUuid
            assert bmVO.hostUuid == null
            assert bmVO.gatewayUuid == null
            assert bmVO.lastGatewayUuid == gateway.uuid
            assert bmVO.state == VmInstanceState.Stopped

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available
        }
    }

    void testDestroyBareMetal2Instance() {
        assert VmGlobalConfig.VM_DELETION_POLICY.value() == "Delay"
        assert VmGlobalConfig.DELETE_DATA_VOLUME_ON_VM_DESTROY.value() == "false"

        destroyVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO != null
            assert bmVO.state == VmInstanceState.Destroyed
            assert bmVO.status == BareMetal2InstanceStatus.Disconnected
            assert bmVO.getRootVolume().uuid == rootVolumeUuid
            assert !bmVO.getAllDiskVolumes().contains(dataVolumeUuid)

            assert dbFindByUuid(rootVolumeUuid as String, VolumeVO.class) as VolumeVO != null
            assert dbFindByUuid(dataVolumeUuid as String, VolumeVO.class) as VolumeVO != null
        }
    }

    void testRestoreBareMetal2Instance() {
        recoverVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO != null
            assert bmVO.state == VmInstanceState.Stopped
            assert bmVO.status == BareMetal2InstanceStatus.Disconnected
            assert bmVO.getRootVolume().uuid == rootVolumeUuid
            assert !bmVO.getAllDiskVolumes().contains(dataVolumeUuid)

            assert dbFindByUuid(rootVolumeUuid as String, VolumeVO.class) as VolumeVO != null
            assert dbFindByUuid(dataVolumeUuid as String, VolumeVO.class) as VolumeVO != null
        }
    }

    void testExpungeBareMetal2Instance() {
        def chassis = env.inventoryByName("chassis-1") as BareMetal2ChassisInventory

        assert VmGlobalConfig.VM_DELETION_POLICY.value() == "Delay"
        assert VmGlobalConfig.DELETE_DATA_VOLUME_ON_VM_DESTROY.value() == "false"

        VmGlobalConfig.VM_EXPUNGE_PERIOD.updateValue(2)
        VmGlobalConfig.VM_EXPUNGE_INTERVAL.updateValue(1)

        destroyVmInstance {
            uuid = bm.uuid
        }

        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO != null
            assert bmVO.state == VmInstanceState.Destroyed
            assert bmVO.status == BareMetal2InstanceStatus.Disconnected
            assert bmVO.getRootVolume().uuid == rootVolumeUuid
            assert !bmVO.getAllDiskVolumes().contains(dataVolumeUuid)

            assert dbFindByUuid(rootVolumeUuid as String, VolumeVO.class) as VolumeVO != null
            assert dbFindByUuid(dataVolumeUuid as String, VolumeVO.class) as VolumeVO != null
        }

        // auto expunge
        retryInSecs {
            def bmVO = dbFindByUuid(bm.uuid, BareMetal2InstanceVO.class) as BareMetal2InstanceVO
            assert bmVO == null
            assert dbFindByUuid(rootVolumeUuid, VolumeVO.class) as VolumeVO == null
            assert dbFindByUuid(dataVolumeUuid, VolumeVO.class) as VolumeVO != null

            def chassisVO = dbFindByUuid(chassis.uuid, BareMetal2ChassisVO.class)
            assert chassisVO.status == BareMetal2ChassisStatus.Available

            assert 0L == Q.New(VmInstanceVO.class).count()
            assert 0L == Q.New(BareMetal2InstanceVO.class).count()
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}