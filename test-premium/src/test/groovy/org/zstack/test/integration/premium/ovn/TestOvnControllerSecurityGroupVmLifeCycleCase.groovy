package org.zstack.test.integration.premium.ovn

import org.springframework.http.HttpEntity
import org.zstack.compute.cluster.MevocoClusterGlobalConfig
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.header.host.NetworkInterfaceType
import org.zstack.header.network.l2.L2NetworkConstant
import org.zstack.network.l2.L2NetworkSystemTags
import org.zstack.network.ovn.OvnControllerCommands
import org.zstack.network.securitygroup.APIAddSecurityGroupRuleMsg
import org.zstack.network.securitygroup.SecurityGroupRuleProtocolType
import org.zstack.network.securitygroup.SecurityGroupRuleType
import org.zstack.network.securitygroup.SecurityGroupTo
import org.zstack.network.securitygroup.VmNicSecurityTO
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.utils.network.IPv6Constants

/**
 * <AUTHOR>
 * @date 2025/03/21
 */
class TestOvnControllerSecurityGroupVmLifeCycleCase extends PremiumSubCase {
    EnvSpec env
    VmInstanceInventory vm1, vm2
    SecurityGroupInventory sg_1, sg_2

    @Override
    void setup() {
        useSpring(OvnTest.springSpec)
    }

    @Override
    void environment() {
        env = OvnTestEnv.BasicVpc()
    }

    @Override
    void test() {
        env.create {
            prepare()
            TestSecurityGroupVmLifeCycle()
            TestSecurityGroupVmNic()
            cleanEnv()
        }
    }

    void prepare() {
        ZoneInventory zone = env.inventoryByName("zone")
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")
        SdnControllerInventory ovn = env.inventoryByName("ovn")
        ClusterInventory cluster = env.inventoryByName("cluster")
        ImageInventory image = env.inventoryByName("image1")
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")

        updateGlobalConfig {
            category = MevocoClusterGlobalConfig.CATEGORY
            name = MevocoClusterGlobalConfig.HUGEPAGE_ENABLE.name
            value = "true"
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->

            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("ens3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:8e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv4 = new HostNetworkInterfaceInventory()
            inv4.setInterfaceName("ens4")
            inv4.setSpeed(10000L)
            inv4.setCarrierActive(true)
            inv4.setMac("ac:1f:6b:93:6c:8f")
            inv4.setPciDeviceAddress("0e:02.0")
            inv4.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            reply.nics = [inv3, inv4] as List<HostNetworkInterfaceInventory>

            return reply;
        }

        reconnectHost {
            uuid = h1.uuid
        }
        reconnectHost {
            uuid = h2.uuid
        }

        ovn = sdnControllerAddHost {
            sdnControllerUuid = ovn.uuid
            hostUuid = h1.uuid
            nicNames = ["ens3"]
            vtepIp = "***********"
            netmask = "*************"
        }

        sdnControllerAddHost {
            sdnControllerUuid = ovn.uuid
            hostUuid = h2.uuid
            nicNames = ["ens3"]
            vtepIp = "*************"
            netmask = "*************"
        }

        L2VlanNetworkInventory ovnl2 = createL2VlanNetwork {
            name = "l2-ovn"
            zoneUuid = zone.uuid
            vlan = 1000
            vSwitchType = "OvnDpdk"
            systemTags = [L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.instantiateTag(
                    [(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN): ovn.uuid])]
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = ovnl2.uuid
            clusterUuid = cluster.uuid
        }

        L3NetworkInventory l3_1 = createL3Network {
            category = "Private"
            l2NetworkUuid = ovnl2.uuid
            name = "ovn-l3-1"
        }

        attachNetworkServiceToL3Network {
            l3NetworkUuid = l3_1.uuid
            networkServices = ['Ovn':['DHCP', 'SecurityGroup']]
        }

        addIpRangeByNetworkCidr {
            name = "address-pool-1"
            l3NetworkUuid = l3_1.uuid
            networkCidr = "************/24"
        }

        addIpv6RangeByNetworkCidr {
            name = "ipv6Range"
            l3NetworkUuid = l3_1.uuid
            networkCidr = "2025:3:24:1::/64"
            addressMode = IPv6Constants.SLAAC
        }

        L2VlanNetworkInventory ovnl2_2 = createL2VlanNetwork {
            name = "l2-ovn-2"
            zoneUuid = zone.uuid
            vlan = 1001
            vSwitchType = "OvnDpdk"
            systemTags = [L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.instantiateTag(
                    [(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN): ovn.uuid])]
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = ovnl2_2.uuid
            clusterUuid = cluster.uuid
        }

        L3NetworkInventory l3_2 = createL3Network {
            category = "Private"
            l2NetworkUuid = ovnl2_2.uuid
            name = "ovn-l3-2"
        }

        attachNetworkServiceToL3Network {
            l3NetworkUuid = l3_2.uuid
            networkServices = ['Ovn':['DHCP', 'SecurityGroup']]
        }

        addIpRangeByNetworkCidr {
            name = "address-pool-2"
            l3NetworkUuid = l3_2.uuid
            networkCidr = "************/24"
        }

        addIpv6RangeByNetworkCidr {
            name = "ipv6Range"
            l3NetworkUuid = l3_2.uuid
            networkCidr = "2025:3:24:2::/64"
            addressMode = IPv6Constants.SLAAC
        }

        sg_1 = createSecurityGroup {
            name = "sg-ovn-1"
            vSwitchType = L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK
            systemTags = ["SdnControllerUuid::${ovn.uuid}".toString()]
        }
        sg_2 = createSecurityGroup {
            name = "sg-ovn-2"
            vSwitchType = L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK
            systemTags = ["SdnControllerUuid::${ovn.uuid}".toString()]
        }

        // add rule
        APIAddSecurityGroupRuleMsg.SecurityGroupRuleAO rule1 = new APIAddSecurityGroupRuleMsg.SecurityGroupRuleAO()
        rule1.allowedCidr = "***********/24"
        rule1.type = SecurityGroupRuleType.Ingress.toString()
        rule1.protocol = SecurityGroupRuleProtocolType.TCP.toString()
        rule1.startPort = 300
        rule1.endPort = 310
        APIAddSecurityGroupRuleMsg.SecurityGroupRuleAO rule2 = new APIAddSecurityGroupRuleMsg.SecurityGroupRuleAO()
        rule2.allowedCidr = "***********/24"
        rule2.type = SecurityGroupRuleType.Ingress.toString()
        rule2.protocol = SecurityGroupRuleProtocolType.UDP.toString()
        rule2.startPort = 300
        rule2.endPort = 310

        sg_1 = addSecurityGroupRule {
            securityGroupUuid = sg_1.uuid
            rules = [rule1, rule2]
        }
    }

    void TestSecurityGroupVmLifeCycle() {
        SdnControllerInventory ovn = env.inventoryByName("ovn")
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")
        ImageInventory image = env.inventoryByName("image1")
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        L3NetworkInventory ovn_l3 = queryL3Network { conditions = ["name=ovn-l3-1"]} [0]

        def cmd = null as OvnControllerCommands.VmNicRefreshSecurityGroupCmd
        env.afterSimulator(OvnControllerCommands.OVN_SECURITY_GROUP_PATH) { rsp, HttpEntity<String> e ->
            cmd = JSONObjectUtil.toObject(e.body, OvnControllerCommands.VmNicRefreshSecurityGroupCmd.class)
            return rsp
        }

        // create vm with sg
        vm1 = createVmInstance {
            name = "ovn-vm-1"
            instanceOfferingUuid = instanceOffering.uuid
            hostUuid = h1.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [ovn_l3.uuid]
            systemTags = [String.format("l3::%s::SecurityGroupUuids::%s,%s", ovn_l3.uuid, sg_1.uuid, sg_2.uuid)]
        }
        assert cmd != null
        assert cmd.vmNicTOs.size() == 1
        VmNicSecurityTO nicTo = cmd.vmNicTOs.get(0)
        assert nicTo.securityGroupRefs.size() == 2
        assert nicTo.securityGroupRefs[sg_1.uuid] == 1
        assert nicTo.securityGroupRefs[sg_2.uuid] == 2
        assert cmd.groups.size() == 2
        SecurityGroupTo group1, group2
        for (SecurityGroupTo group : cmd.groups) {
            if (group.securityGroupUuid == sg_1.uuid) {
                group1 = group
            } else {
                group2 = group
            }
        }
        assert group1 != null
        assert group2 != null

        //reboot vm will not resent sg command
        cmd = null as OvnControllerCommands.VmNicRefreshSecurityGroupCmd
        rebootVmInstance {
            uuid = vm1.uuid
        }
        assert cmd == null

        stopVmInstance {
            uuid = vm1.uuid
        }
        assert cmd == null

        vm1 = startVmInstance {
            uuid = vm1.uuid
        }
        assert cmd == null

        migrateVm {
            vmInstanceUuid = vm1.uuid
            hostUuid = (vm1.hostUuid == h1.uuid ? h2.uuid : h1.uuid)
        }
        assert cmd == null

        CloneVmInstanceResult result = cloneVmInstance {
            names = ["vm-c1"]
            vmInstanceUuid = vm1.uuid
        }
        assert cmd != null
        assert cmd.vmNicTOs.size() == 1
        nicTo = cmd.vmNicTOs.get(0)
        assert nicTo.securityGroupRefs.size() == 2
        assert nicTo.securityGroupRefs[sg_1.uuid] == 1
        assert nicTo.securityGroupRefs[sg_2.uuid] == 2
        assert cmd.groups.size() == 2
        for (SecurityGroupTo group : cmd.groups) {
            if (group.securityGroupUuid == sg_1.uuid) {
                group1 = group
            } else {
                group2 = group
            }
        }
        assert group1 != null
        assert group2 != null

        cmd = null as OvnControllerCommands.VmNicRefreshSecurityGroupCmd
        destroyVmInstance {
            uuid = vm1.uuid
        }
        assert cmd != null
        assert cmd.vmNicTOs.size() == 1
        nicTo = cmd.vmNicTOs.get(0)
        assert nicTo.securityGroupRefs.size() == 0
        assert cmd.groups.size() == 2
    }

    void TestSecurityGroupVmNic() {
        SdnControllerInventory ovn = env.inventoryByName("ovn")
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")
        ImageInventory image = env.inventoryByName("image1")
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        L3NetworkInventory ovn_l3 = queryL3Network { conditions = ["name=ovn-l3-1"]} [0]
        L3NetworkInventory ovn_l3_2 = queryL3Network { conditions = ["name=ovn-l3-2"]} [0]

        def cmd = null as OvnControllerCommands.VmNicRefreshSecurityGroupCmd
        env.afterSimulator(OvnControllerCommands.OVN_SECURITY_GROUP_PATH) { rsp, HttpEntity<String> e ->
            cmd = JSONObjectUtil.toObject(e.body, OvnControllerCommands.VmNicRefreshSecurityGroupCmd.class)
            return rsp
        }

        // create vm without sg
        vm1 = createVmInstance {
            name = "ovn-vm-2"
            instanceOfferingUuid = instanceOffering.uuid
            hostUuid = h1.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [ovn_l3.uuid]
         }
        assert cmd == null

        detachL3NetworkFromVm {
            vmNicUuid = vm1.vmNics.get(0).uuid
        }
        assert cmd == null

        vm1 = attachL3NetworkToVm {
            vmInstanceUuid = vm1.uuid
            l3NetworkUuid = ovn_l3.uuid
            systemTags = [String.format("l3::%s::SecurityGroupUuids::%s,%s", ovn_l3.uuid, sg_1.uuid, sg_2.uuid)]
        }
        assert cmd != null
        assert cmd.vmNicTOs.size() == 1
        VmNicSecurityTO nicTo = cmd.vmNicTOs.get(0)
        assert nicTo.securityGroupRefs.size() == 2
        assert nicTo.securityGroupRefs[sg_1.uuid] == 1
        assert nicTo.securityGroupRefs[sg_2.uuid] == 2
        assert cmd.groups.size() == 2
        SecurityGroupTo group1, group2
        for (SecurityGroupTo group : cmd.groups) {
            if (group.securityGroupUuid == sg_1.uuid) {
                group1 = group
            } else {
                group2 = group
            }
        }
        assert group1 != null
        assert group2 != null

        cmd = null as OvnControllerCommands.VmNicRefreshSecurityGroupCmd
        vm1 = queryVmInstance { conditions = ["uuid=${vm1.uuid}"]} [0]
        detachL3NetworkFromVm {
            vmNicUuid = vm1.vmNics.get(0).uuid
        }
        assert cmd != null
        assert cmd.vmNicTOs.size() == 1
        nicTo = cmd.vmNicTOs.get(0)
        assert nicTo.securityGroupRefs.size() == 0
        assert cmd.groups.size() == 2
        for (SecurityGroupTo group : cmd.groups) {
            if (group.securityGroupUuid == sg_1.uuid) {
                group1 = group
            } else {
                group2 = group
            }
        }
        assert group1 != null
        assert group2 != null

        vm1 = attachL3NetworkToVm {
            vmInstanceUuid = vm1.uuid
            l3NetworkUuid = ovn_l3.uuid
            systemTags = [String.format("l3::%s::SecurityGroupUuids::%s,%s", ovn_l3.uuid, sg_1.uuid, sg_2.uuid)]
        }
        cmd = null as OvnControllerCommands.VmNicRefreshSecurityGroupCmd
        changeVmNicNetwork {
            vmNicUuid = vm1.vmNics.get(0).uuid
            destL3NetworkUuid = ovn_l3_2.uuid
        }
        assert cmd != null
        assert cmd.vmNicTOs.size() == 1
        nicTo = cmd.vmNicTOs.get(0)
        assert nicTo.securityGroupRefs.size() == 0
        assert cmd.groups.size() == 2
        for (SecurityGroupTo group : cmd.groups) {
            if (group.securityGroupUuid == sg_1.uuid) {
                group1 = group
            } else {
                group2 = group
            }
        }
        assert group1 != null
        assert group2 != null
    }

    void cleanEnv() {
        List<SdnControllerInventory> invs = querySdnController {}
        for (SdnControllerInventory inv : invs) {
            removeSdnController {
                uuid = inv.uuid
            }
        }
        List<SecurityGroupInventory> sgInvs = querySecurityGroup {}
        for (SecurityGroupInventory inv : sgInvs) {
            deleteSecurityGroup {
                uuid = inv.uuid
            }
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}
