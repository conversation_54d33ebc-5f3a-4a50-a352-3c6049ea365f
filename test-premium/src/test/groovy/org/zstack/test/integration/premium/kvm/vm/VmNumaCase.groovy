package org.zstack.test.integration.premium.kvm.vm

import org.springframework.http.HttpEntity
import org.zstack.compute.vm.MevocoVmSystemTags
import org.zstack.compute.vm.VmGlobalConfig
import org.zstack.compute.vm.VmSystemTags
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.host.HostNumaNodeVO
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.tag.SystemTagVO
import org.zstack.header.tag.SystemTagVO_
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.userdata.UserdataConstant
import org.zstack.resourceconfig.ResourceConfigVO
import org.zstack.resourceconfig.ResourceConfigVO_
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

class VmNumaCase extends PremiumSubCase {
    EnvSpec env
    VmInstanceInventory vm
    HostInventory host1
    HostInventory host2
    HostInventory host3
    HostInventory host4
    HostInventory host5
    HostInventory host6
    HostInventory host7
    HostInventory host8
    PrimaryStorageInventory ps1
    PrimaryStorageInventory ps2
    InstanceOfferingInventory instance
    L3NetworkInventory l3
    ImageInventory image
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = makeEnv {
            zone {
                name = "zone"

                attachBackupStorage("imagery")

                smpPrimaryStorage {
                    name = "smp"
                    url = "/smp_ps"
                }

                smpPrimaryStorage {
                    name = "smp2"
                    url = "/smp_ps2"
                }

                cluster {
                    name = "cluster1"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }

                    kvm {
                        name = "kvm3"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }

                    kvm {
                        name = "kvm4"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }

                    kvm {
                        name = "kvm5"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }
                    attachPrimaryStorage("smp")
                    attachL2Network("l2")
                }

                cluster {
                    name = "cluster2"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }

                    kvm {
                        name = "kvm6"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }

                    kvm {
                        name = "kvm7"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }

                    attachPrimaryStorage("smp")
                    attachL2Network("l2")
                }

                cluster {
                    name = "cluster3"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm8"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalCpu = 16
                    }

                    attachPrimaryStorage("smp2")
                    attachL2Network("l2")
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }
            }

            imageStore {
                name = "imagery"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                    platform = "Linux"
                }
            }

            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 8
            }
        }
    }

    @Override
    void test() {
        env.create {
            host1 = env.inventoryByName("kvm1") as HostInventory
            host2 = env.inventoryByName("kvm2") as HostInventory
            host3 = env.inventoryByName("kvm3") as HostInventory
            host4 = env.inventoryByName("kvm4") as HostInventory
            host5 = env.inventoryByName("kvm5") as HostInventory
            host6 = env.inventoryByName("kvm6") as HostInventory
            host7 = env.inventoryByName("kvm7") as HostInventory
            host8 = env.inventoryByName("kvm8") as HostInventory
            ps1 = env.inventoryByName("smp") as PrimaryStorageInventory
            ps2 = env.inventoryByName("smp2") as PrimaryStorageInventory
            instance = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
            l3 = env.inventoryByName("l3") as L3NetworkInventory
            image = env.inventoryByName("image1") as ImageInventory
            dbf = bean(DatabaseFacade.class)

            testCreateVmWithoutHostNumaNodes()
            addNumaNode()
            testCreateVmWithoutCpuPinning()
            testCreateVmSuccess()
            testCandidateHost()
            testVmNumaTopology()
            testVmNumaApi()
            testCloneVm()
            testStartVmWithErrorCpuPinning()
            testStartVmWithHotPlug()
            testNormalAccountPermission()
            testCreateVmWithNuma()
        }
    }

    @Override
    void clean() {
        env.delete()

    }

    void testCreateVmWithNuma(){
        VmGlobalConfig.NUMA.updateValue(false)
        env.cleanSimulatorHandlers()
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { KVMAgentCommands.StartVmResponse rsp, HttpEntity<String> e ->
            KVMAgentCommands.StartVmCmd cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.StartVmCmd.class)
            assert cmd.isUseNuma()
            return rsp
        }

        def vm = createVmInstance {
            name = "vm"
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
            clusterUuid = host1.clusterUuid
            systemTags = [VmSystemTags.NUMA.getTagFormat()]
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
        } as VmInstanceInventory


        assert Q.New(ResourceConfigVO.class)
                .eq(ResourceConfigVO_.category, VmGlobalConfig.NUMA.category)
                .eq(ResourceConfigVO_.resourceUuid, vm.uuid)
                .eq(ResourceConfigVO_.value, Boolean.TRUE.toString())
                .eq(ResourceConfigVO_.name, VmGlobalConfig.NUMA.name)
                .isExists()

        deleteVm(vm.uuid)

        VmGlobalConfig.NUMA.updateValue(true)

        vm = createVmInstance {
            name = "vm"
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
            clusterUuid = host1.clusterUuid
            systemTags = [VmSystemTags.NUMA.getTagFormat()]
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
        } as VmInstanceInventory


        assert Q.New(ResourceConfigVO.class)
                .eq(ResourceConfigVO_.category, VmGlobalConfig.NUMA.category)
                .eq(ResourceConfigVO_.resourceUuid, vm.uuid)
                .eq(ResourceConfigVO_.value, Boolean.TRUE.toString())
                .eq(ResourceConfigVO_.name, VmGlobalConfig.NUMA.name)
                .isExists()
        deleteVm(vm.uuid)
    }

    void deleteVm(String vmUuid) {
        destroyVmInstance {
            uuid = vmUuid
        }

        expungeVmInstance {
            uuid = vmUuid
        }
    }


    void testCreateVmWithoutHostNumaNodes() {
        instance = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        l3 = env.inventoryByName("l3") as L3NetworkInventory
        image = env.inventoryByName("image1") as ImageInventory
        String cpuPinning = "0-3:0-3;4-7:12"
        expect(AssertionError.class) {
            createVmInstance {
                name = "vm1"
                systemTags = [MevocoVmSystemTags.VM_CPU_PINNING.instantiateTag([(MevocoVmSystemTags.VM_CPU_PINNING_TOKEN): cpuPinning]),
                              MevocoVmSystemTags.VM_NUMA_ENABLE.instantiateTag([(MevocoVmSystemTags.VM_NUMA_ENABLE_TOKEN): true])]
                l3NetworkUuids = [l3.uuid]
                imageUuid = image.uuid
                instanceOfferingUuid = instance.uuid
                clusterUuid = host1.clusterUuid
            }
        }

    }

    void addNumaNode() {
        giveHostNuma(host1.uuid, 0, "10,15", "0,1,2,3,4,5,6,7", 12345L)
        giveHostNuma(host1.uuid, 1, "15,10", "8,9,10,11,12,13,14,15", 23451L)

        giveHostNuma(host2.uuid, 0, "10", "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15", 24690L)

        giveHostNuma(host3.uuid, 0, "10,15", "0,1,2,3,4,5,6,7", 12345L)
        giveHostNuma(host3.uuid, 1, "15,10", "8,9,10,11,12,13,14,15", 12345L)


        giveHostNuma(host4.uuid, 0, "10,15,20", "0,1,2,3,4,5,6,7", 12345L)
        giveHostNuma(host4.uuid, 1, "15,10,20", "8,9,10,11,12,13,14,15", 23451L)
        giveHostNuma(host4.uuid, 2, "15,10,20", "16,17,18,19,20,21,22", 23451L)

        giveHostNuma(host5.uuid, 1, "10,15", "0,1,2,3,4,5,6,7", 12345L)
        giveHostNuma(host5.uuid, 0, "15,10", "8,9,10,11,12,13,14,15", 23451L)

        giveHostNuma(host6.uuid, 0, "10,15", "0,1,2,3,4,5,6,7", 23451L)
        giveHostNuma(host6.uuid, 1, "15,10", "8,9,10,11,12,13,14,15", 23451L)

        giveHostNuma(host7.uuid, 0, "10,10", "0,1,2,3,4,5,6,7", 23451L)
        giveHostNuma(host7.uuid, 1, "10,10", "8,9,10,11,12,13,14,15", 23451L)

        giveHostNuma(host8.uuid, 0, "10,15", "0,1,2,3,4,5,6,7", 12345L)
        giveHostNuma(host8.uuid, 1, "15,10", "8,9,10,11,12,13,14,15", 23451L)
    }

    void testCreateVmWithoutCpuPinning() {
        instance = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        l3 = env.inventoryByName("l3") as L3NetworkInventory
        image = env.inventoryByName("image1") as ImageInventory
        expect(AssertionError.class) {
            createVmInstance {
                name = "vm2"
                systemTags = [MevocoVmSystemTags.VM_NUMA_ENABLE.instantiateTag([(MevocoVmSystemTags.VM_NUMA_ENABLE_TOKEN): true])]
                l3NetworkUuids = [l3.uuid]
                imageUuid = image.uuid
                instanceOfferingUuid = instance.uuid
                hostUuid = host1.uuid
            }
        }

    }

    void testCreateVmSuccess() {
        String cpuPinning = "0-3:0-3;4-7:12"
        KVMAgentCommands.StartVmCmd cmd
        env.simulator(KVMConstant.KVM_START_VM_PATH) { HttpEntity<String> e ->
            cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.StartVmCmd.class)
            return new KVMAgentCommands.StartVmResponse()
        }
        vm = createVmInstance {
            name = "vm"
            systemTags = [MevocoVmSystemTags.VM_CPU_PINNING.instantiateTag([(MevocoVmSystemTags.VM_CPU_PINNING_TOKEN): cpuPinning]),
                          MevocoVmSystemTags.VM_NUMA_ENABLE.instantiateTag([(MevocoVmSystemTags.VM_NUMA_ENABLE_TOKEN): true])]
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
            instanceOfferingUuid = instance.uuid
            hostUuid = host1.uuid
        } as VmInstanceInventory

        assert cmd.addons["numaNodes"].get(0)["nodeID"] == 0
        assert cmd.addons["numaNodes"].get(0)["cpus"] == "0,1,2,3"
        assert cmd.addons["numaNodes"].get(0)["memorySize"] == "4294967296"
        assert cmd.addons["numaNodes"].get(0)["distance"] == new ArrayList<String>(Arrays.asList("10", "15"))
        assert cmd.addons["numaNodes"].get(0)["hostNodeID"] == 0
        assert cmd.addons["numaNodes"].get(1)["nodeID"] == 1
        assert cmd.addons["numaNodes"].get(1)["cpus"] == "4,5,6,7"
        assert cmd.addons["numaNodes"].get(1)["memorySize"] == "4294967296"
        assert cmd.addons["numaNodes"].get(1)["distance"] == new ArrayList<String>(Arrays.asList("15", "10"))
        assert cmd.addons["numaNodes"].get(1)["hostNodeID"] == 1
    }


    void testCandidateHost() {
        def hostCandidates = getHostCandidatesForVmMigration {
            vmInstanceUuid = vm.uuid
            dstPrimaryStorageUuid = ps1.uuid
        } as List<HostInventory>

        assert hostCandidates.size() == 3

        def hostCandidates2 = getHostCandidatesForVmMigration {
            vmInstanceUuid = vm.uuid
            dstPrimaryStorageUuid = ps2.uuid
        } as List<HostInventory>

        assert hostCandidates2.size() == 1
    }

    void testVmNumaTopology() {
        GetVmvNUMATopologyResult tp
        tp = getVmvNUMATopology {
            uuid = vm.uuid
        } as GetVmvNUMATopologyResult
        assert tp.topology.get(0)["CPUsID"] == new ArrayList<String>(Arrays.asList("0", "1", "2", "3"))
        assert tp.topology.get(0)["nodeID"] == 0
        assert tp.topology.get(0)["phyNodeID"] == 0
        assert tp.topology.get(0)["memSize"] == 4294967296
        assert tp.topology.get(0)["distance"] == new ArrayList<String>(Arrays.asList("10", "15"))
        assert tp.topology.get(1)["CPUsID"] == new ArrayList<String>(Arrays.asList("4", "5", "6", "7"))
        assert tp.topology.get(1)["nodeID"] == 1
        assert tp.topology.get(1)["phyNodeID"] == 1
        assert tp.topology.get(1)["memSize"] == 4294967296
        assert tp.topology.get(1)["distance"] == new ArrayList<String>(Arrays.asList("15", "10"))
    }

    void testVmNumaApi() {
        setVmNuma {
            enable = false
            uuid = vm.uuid
        }

        assert getVmNuma {
            uuid = vm.uuid
        }["enable"] == false

        setVmNuma {
            enable = true
            uuid = vm.uuid
        }

        assert getVmNuma {
            uuid = vm.uuid
        }["enable"] == true


        def systemTagUuid = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, vm.uuid).like(SystemTagVO_.tag, "vmNumaEnable::%").select(SystemTagVO_.uuid).findValue()

        updateSystemTagVmNuma(systemTagUuid as String, false)

        assert getVmNuma {
            uuid = vm.uuid
        }["enable"] == false

        updateSystemTagVmNuma(systemTagUuid as String, true)

        assert getVmNuma {
            uuid = vm.uuid
        }["enable"] == true


    }

    void testCloneVm() {
        def results = cloneVmInstance {
            names = ["clone-by-vm"]
            vmInstanceUuid = vm.uuid
        } as CloneVmInstanceResult
        assert Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, results.result.inventories[0].inventory.uuid).like(SystemTagVO_.tag, "vmNumaEnable::%").select(SystemTagVO_.uuid).findValue()
        destroyVmInstance {
            uuid = results.result.inventories[0].inventory.uuid
        }
        expungeVmInstance {
            uuid = results.result.inventories[0].inventory.uuid
        }
    }

    void testStartVmWithHotPlug() {
        updateResourceConfig {
            name = VmGlobalConfig.NUMA.name
            category = VmGlobalConfig.NUMA.category
            value = (!VmGlobalConfig.NUMA.updateValue(true)).toString()
            resourceUuid = vm.uuid
        }

        expect(AssertionError.class) {
            setVmNuma {
                enable = true
                uuid = vm.uuid
            }
        }
    }

    void testStartVmWithErrorCpuPinning() {
        stopVmInstance {
            uuid = vm.uuid
        }

        updateCpuPinning("0-5:12;6:1-15;")

        setVmNuma {
            enable = true
            uuid = vm.uuid
        }

        expect(AssertionError.class) {
            startVmInstance {
                uuid = vm.uuid
                clusterUuid = host1.clusterUuid
            }
        }

    }

    void testNormalAccountPermission() {
        createAccount {
            name = "test"
            password = "password"
        }

        def session = logInByAccount {
            accountName = "test"
            password = "password"
        } as SessionInventory


        def normalVm = createVmInstance {
            name = "normalVm"
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
            instanceOfferingUuid = instance.uuid
            clusterUuid = host1.clusterUuid
        } as VmInstanceInventory

        expect(AssertionError.class) {
            setVmNuma {
                enable = true
                sessionId = session.uuid
                uuid = normalVm.getUuid()
            }
        }

        destroyVmInstance {
            uuid = normalVm.getUuid()
        }
        expungeVmInstance {
            uuid = normalVm.getUuid()
        }
    }

    private void updateSystemTagVmNuma(String systemTagUuid, boolean vmNumaEnable) {
        updateSystemTag {
            uuid = systemTagUuid
            tag = MevocoVmSystemTags.VM_NUMA_ENABLE.instantiateTag([(MevocoVmSystemTags.VM_NUMA_ENABLE_TOKEN): vmNumaEnable])
        }
    }

    private void updateCpuPinning(String cpuPinning) {
        updateSystemTag {
            uuid = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, vm.uuid).like(SystemTagVO_.tag, "vmCpuPinning::%").select(SystemTagVO_.uuid).findValue()
            tag = MevocoVmSystemTags.VM_CPU_PINNING.instantiateTag([(MevocoVmSystemTags.VM_CPU_PINNING_TOKEN): cpuPinning])
        }
    }

    private void giveHostNuma(String hostUuid, Integer nodeID, String nodeDistance, String nodeCPUs, Long nodeMemSize) {
        HostNumaNodeVO node = new HostNumaNodeVO()
        node.setNodeID(nodeID)
        node.setNodeDistance(nodeDistance)
        node.setNodeCPUs(nodeCPUs)
        node.setNodeMemSize(nodeMemSize)
        node.setHostUuid(hostUuid)
        dbf.persist(node)
    }
}