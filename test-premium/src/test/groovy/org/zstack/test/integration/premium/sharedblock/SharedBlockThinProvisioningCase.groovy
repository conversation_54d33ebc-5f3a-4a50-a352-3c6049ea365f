package org.zstack.test.integration.premium.sharedblock

import org.springframework.http.HttpEntity
import org.zstack.core.Platform
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.identity.AccountResourceRefVO
import org.zstack.header.identity.AccountResourceRefVO_
import org.zstack.header.storage.primary.PrimaryStorageVO
import org.zstack.header.tag.SystemTagVO
import org.zstack.header.tag.SystemTagVO_
import org.zstack.header.volume.VolumeProvisioningStrategy
import org.zstack.header.volume.VolumeType
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.mevoco.MevocoGlobalConfig
import org.zstack.sdk.*
import org.zstack.storage.backup.VolumeBackupKvmCommands
import org.zstack.storage.backup.imagestore.AllocateUploadWorkspaceMsg
import org.zstack.storage.backup.imagestore.AllocateUploadWorkspaceReply
import org.zstack.storage.backup.imagestore.ImportImageMsg
import org.zstack.storage.backup.imagestore.ImportImageReply
import org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands
import org.zstack.storage.volume.VolumeSystemTags
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.utils.CollectionDSL.e
import static org.zstack.utils.CollectionDSL.map

/**
 * Create by weiwang at 2018/5/7 
 */
class SharedBlockThinProvisioningCase extends PremiumSubCase {
    EnvSpec env
    L3NetworkInventory pubL3
    InstanceOfferingInventory offeringInventory
    ImageInventory imageInventory
    ImageInventory dataVolumeImageInventory
    DiskOfferingInventory diskOfferingInventory
    VmInstanceInventory vmInstanceInventory
    SharedBlockGroupPrimaryStorageInventory sblk
    SharedBlockGroupPrimaryStorageInventory sblk1
    BackupStorageInventory bs1

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = makeEnv {
            account {
                name = "test1"
                password = "password"
            }

            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            imageStore {
                name = "imagestore"
                username = "root"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "imagestore-image"
                    url = "http://zstack.org/download/test.qcow2"
                }
                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }

                image {
                    name = "datavolume-image"
                    url = "http://zstack.org/download/testdisk.qcow2"
                    mediaType = "DataVolumeTemplate"
                }
            }

            zone {
                name = "zone"
                description = "test"

                sbgPrimaryStorage {
                    name="sharedblock-ps"
                    description="Test"
                    diskUuids=["0121520F-55D2-4541-9345-887B9074A157"]
                }

                sbgPrimaryStorage {
                    name="sharedblock-ps1"
                    description="Test1"
                    diskUuids=["0121520F-55D2-4541-9345-887B9074A158"]
                }

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("sharedblock-ps")
                    attachPrimaryStorage("sharedblock-ps1")
                    attachL2Network("l2")
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                diskOffering {
                    name = "diskOffering"
                    diskSize = SizeUnit.GIGABYTE.toByte(10)
                }

                attachBackupStorage("imagestore")
            }
        }
    }

    @Override
    void test() {
        env.create {
            pubL3 = env.inventoryByName("pubL3")
            offeringInventory = env.inventoryByName("instanceOffering")
            imageInventory = env.inventoryByName("imagestore-image")
            diskOfferingInventory = env.inventoryByName("diskOffering")
            sblk = env.inventoryByName("sharedblock-ps")
            sblk1 = env.inventoryByName("sharedblock-ps1")
            bs1 = env.inventoryByName("imagestore")
            dataVolumeImageInventory = env.inventoryByName("datavolume-image")

            testCreateVmThinProvision()
            testMemorySnapshotThickProvisioning()
            testCreateVolumeThinProvision()
            testCloneVmThinProvision()
            testChangeImageThinProvision()
            testRevertVolumeThinProvision()
            testReimageVmInstance()

            testCreatePrimaryStorageThinProvision()
        }
    }

    void testCreateVmThinProvision() {
        def createVolumeFromCacheCmd = new SharedBlockKvmCommands.CreateVolumeFromCacheCmd() as SharedBlockKvmCommands.CreateVolumeFromCacheCmd
        env.simulator(SharedBlockKvmCommands.CREATE_VOLUME_FROM_CACHE_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.CreateVolumeFromCacheCmd.class)
            createVolumeFromCacheCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }
        MevocoGlobalConfig.ENABLE_QCOW2_EXTENDED_L2.updateValue(true)

        def createEmptyVolumeCmd = new SharedBlockKvmCommands.CreateEmptyVolumeCmd() as SharedBlockKvmCommands.CreateEmptyVolumeCmd
        env.simulator(SharedBlockKvmCommands.CREATE_EMPTY_VOLUME_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.CreateEmptyVolumeCmd.class)
            assert ((String)cmd.kvmHostAddons.get("qcow2Options")).contains(" -o extended_l2=on ")
            assert ((String)cmd.kvmHostAddons.get("qcow2Options")).contains(" -o cluster_size=")
            createEmptyVolumeCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }

        def vm1 = createVmInstance {
            name = "testVm1"
            instanceOfferingUuid = offeringInventory.uuid
            imageUuid = imageInventory.uuid
            l3NetworkUuids = [pubL3.uuid]
            dataDiskOfferingUuids = [diskOfferingInventory.uuid]
            primaryStorageUuidForRootVolume = sblk.uuid
            rootVolumeSystemTags = ["volumeProvisioningStrategy::ThinProvisioning"]
            dataVolumeSystemTags = ["volumeProvisioningStrategy::ThinProvisioning", "capability::virtio-scsi"]
            systemTags = ["primaryStorageUuidForDataVolume::${sblk1.uuid}".toString()]
        } as VmInstanceInventory

        assert createVolumeFromCacheCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert createVolumeFromCacheCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()
        assert createEmptyVolumeCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert createEmptyVolumeCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()

        def wwn = ""
        def dataVolume = vm1.allVolumes.stream().filter{ v -> v.uuid != vm1.rootVolumeUuid }.findFirst().get() as VolumeInventory
        def tags = querySystemTag {conditions = ["resourceUuid=${dataVolume.uuid}"]} as List<TagInventory>
        for (TagInventory tag : tags) {
            if (tag.tag.contains("kvm::volume::")) {
                wwn = tag.tag.split("::")[-1]
            }
        }
        assert wwn != ""

        vmInstanceInventory = vm1
    }

    void testCreateVolumeThinProvision() {
        createDataVolumeFromVolumeTemplate {
            imageUuid = dataVolumeImageInventory.uuid
            name = "testVol1"
            primaryStorageUuid = sblk.uuid
        }

        def volume = createDataVolumeFromVolumeTemplate {
            imageUuid = dataVolumeImageInventory.uuid
            name = "testVol2"
            primaryStorageUuid = sblk.uuid
            systemTags = ["volumeProvisioningStrategy::ThinProvisioning"]
        }

        changeVolumeState {
            uuid = volume.uuid
            stateEvent = "disable"
        }

        deleteDataVolume {
            uuid = volume.uuid
        }

        SQL.New(AccountResourceRefVO.class).eq(AccountResourceRefVO_.resourceUuid, volume.uuid).hardDelete()

        expungeDataVolume {
            uuid = volume.uuid
        }
    }

    void testCloneVmThinProvision() {
        stopVmInstance {
            uuid = vmInstanceInventory.uuid
        }

        def createVolumeFromCacheCmd = new SharedBlockKvmCommands.CreateVolumeFromCacheCmd() as SharedBlockKvmCommands.CreateVolumeFromCacheCmd
        env.simulator(SharedBlockKvmCommands.CREATE_VOLUME_FROM_CACHE_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.CreateVolumeFromCacheCmd.class)
            createVolumeFromCacheCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }

        def createEmptyVolumeCmd = new SharedBlockKvmCommands.CreateEmptyVolumeCmd() as SharedBlockKvmCommands.CreateEmptyVolumeCmd
        env.simulator(SharedBlockKvmCommands.CREATE_EMPTY_VOLUME_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.CreateEmptyVolumeCmd.class)
            createEmptyVolumeCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }

        def vm2 = cloneVmInstance {
            vmInstanceUuid = vmInstanceInventory.uuid
            names = ["testClone1"]
            primaryStorageUuidForRootVolume = sblk1.uuid
            primaryStorageUuidForDataVolume = sblk.uuid
            full = true
            rootVolumeSystemTags = ["volumeProvisioningStrategy::ThinProvisioning"]
            dataVolumeSystemTags = ["volumeProvisioningStrategy::ThinProvisioning"]
        }.result.inventories[0].inventory

        assert createVolumeFromCacheCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert createVolumeFromCacheCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()
        assert createEmptyVolumeCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert createEmptyVolumeCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()


        def wwn = ""
        def rootVolume = vm2.allVolumes.stream().filter{ v -> v.uuid == vm2.rootVolumeUuid }.findFirst().get() as VolumeInventory
        def dataVolume = vm2.allVolumes.stream().filter{ v -> v.uuid != vm2.rootVolumeUuid }.findFirst().get() as VolumeInventory
        def tags = querySystemTag {conditions = ["resourceUuid=${dataVolume.uuid}"]} as List<TagInventory>
        for (TagInventory tag : tags) {
            if (tag.tag.contains("kvm::volume::")) {
                wwn = tag.tag.split("::")[-1]
            }
        }
        assert wwn != ""

        assert rootVolume.primaryStorageUuid == sblk1.uuid
        assert dataVolume.primaryStorageUuid == sblk.uuid

        startVmInstance {
            uuid = vmInstanceInventory.uuid
        }
    }

    void testCreatePrimaryStorageThinProvision() {
        def createEmptyVolumeCmd = new SharedBlockKvmCommands.CreateEmptyVolumeCmd() as SharedBlockKvmCommands.CreateEmptyVolumeCmd
        env.simulator(SharedBlockKvmCommands.CREATE_EMPTY_VOLUME_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.CreateEmptyVolumeCmd.class)
            createEmptyVolumeCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }

        createSystemTag {
            resourceType = PrimaryStorageVO.class.simpleName
            resourceUuid = sblk1.uuid
            tag = "primaryStorageVolumeProvisioningStrategy::ThinProvisioning"
        }

        def vol = createDataVolume {
            name = "sblk-data1"
            primaryStorageUuid = sblk1.uuid
            diskOfferingUuid = diskOfferingInventory.uuid
        } as VolumeInventory

        assert createEmptyVolumeCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert createEmptyVolumeCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()

        def tags = querySystemTag {
            conditions = [
                    "resourceUuid=${vol.uuid}".toString(),
                    "tag=${VolumeSystemTags.VOLUME_PROVISIONING_STRATEGY_TOKEN}::${VolumeProvisioningStrategy.ThinProvisioning}".toString()
            ]
        } as List<SystemTagInventory>
        assert tags.size() == 1
    }

    void testChangeImageThinProvision() {
        stopVmInstance {
            uuid = vmInstanceInventory.uuid
        }

        def createVolumeFromCacheCmd = new SharedBlockKvmCommands.CreateVolumeFromCacheCmd() as SharedBlockKvmCommands.CreateVolumeFromCacheCmd
        env.simulator(SharedBlockKvmCommands.CREATE_VOLUME_FROM_CACHE_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.CreateVolumeFromCacheCmd.class)
            createVolumeFromCacheCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }
        def vm1 = changeVmImage {
            vmInstanceUuid = vmInstanceInventory.uuid
            imageUuid = imageInventory.uuid
        } as VmInstanceInventory

        assert VolumeSystemTags.VOLUME_PROVISIONING_STRATEGY.hasTag(vm1.rootVolumeUuid, VolumeVO.class)
        assert createVolumeFromCacheCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert createVolumeFromCacheCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()

        startVmInstance {
            uuid = vmInstanceInventory.uuid
        }
    }

    void testRevertVolumeThinProvision() {
        env.message(AllocateUploadWorkspaceMsg.class) {
            AllocateUploadWorkspaceMsg msg, CloudBus bus ->
                def reply = new AllocateUploadWorkspaceReply()
                reply.setUploadWorkspace(Platform.getUuid())
                bus.reply(msg, reply)
        }

        env.message(ImportImageMsg.class) {
            ImportImageMsg msg, CloudBus bus ->
                def reply = new ImportImageReply()
                reply.setInstallPath("zstore://this/first-backup")
                reply.setSize(1L)
                bus.reply(msg, reply)
        }

        env.simulator(VolumeBackupKvmCommands.TAKE_VOLUME_BACKUP_PATH) { HttpEntity<String> entity, EnvSpec spec ->
            VolumeBackupKvmCommands.TakeBackupResponse rsp = new VolumeBackupKvmCommands.TakeBackupResponse()
            rsp.setBackupFile("/this/first-back")
            rsp.setBitmap("zsbitmap0")
            rsp.setParentInstallPath("zstore://this/parent")
            rsp.setSuccess(true)
            return rsp
        }

        def dataVolume = vmInstanceInventory.allVolumes.stream().filter{ v -> v.uuid != vmInstanceInventory.rootVolumeUuid }.findFirst().get() as VolumeInventory

        def backupInventory = createVolumeBackup {
            name = "bak1"
            volumeUuid = dataVolume.uuid
            backupStorageUuid = bs1.uuid
        } as VolumeBackupInventory

        stopVmInstance {
            uuid = vmInstanceInventory.uuid
        }

        def resizeVolumeCmd = new SharedBlockKvmCommands.ResizeVolumeCmd() as SharedBlockKvmCommands.ResizeVolumeCmd
        env.simulator(SharedBlockKvmCommands.RESIZE_VOLUME_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.ResizeVolumeCmd.class)
            resizeVolumeCmd = cmd
            return new SharedBlockKvmCommands.AgentRsp()
        }
        revertVolumeFromVolumeBackup {
            uuid = backupInventory.uuid
        }
        assert resizeVolumeCmd.addons["thinProvisioningInitializeSize"] == ((Long)1024**3)*5
        assert resizeVolumeCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()
    }

    void testReimageVmInstance() {
        def reverCmd = new SharedBlockKvmCommands.RevertVolumeFromSnapshotCmd() as SharedBlockKvmCommands.RevertVolumeFromSnapshotCmd
        env.simulator(SharedBlockKvmCommands.REVERT_VOLUME_FROM_SNAPSHOT_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, SharedBlockKvmCommands.RevertVolumeFromSnapshotCmd.class)
            reverCmd = cmd
            def rsp = new SharedBlockKvmCommands.RevertVolumeFromSnapshotRsp()
            rsp.size = 10737418240l
            rsp.newVolumeInstallPath = cmd.installPath
            return rsp
        }
        reimageVmInstance {
            vmInstanceUuid = vmInstanceInventory.uuid
        }
        assert reverCmd.addons["thinProvisioningInitializeSize"] == ((Long) 1024**3) * 5
        assert reverCmd.provisioning == VolumeProvisioningStrategy.ThinProvisioning.toString()
    }

    @Override
    void clean() {
        env.delete()
    }

    def void testMemorySnapshotThickProvisioning() {
        VolumeSnapshotGroupInventory group = createVolumeSnapshotGroup {
            name = "test-slb-thickProvisioning"
            rootVolumeUuid = vmInstanceInventory.getRootVolumeUuid()
            withMemory = true
        } as VolumeSnapshotGroupInventory

        def volumeUuid = group.volumeSnapshotRefs.find { ref -> ref.volumeType == VolumeType.Memory.toString() }.volumeUuid
        assert volumeUuid != null
        assert Q.New(SystemTagVO.class)
                .eq(SystemTagVO_.resourceType, VolumeVO.class.getSimpleName())
                .eq(SystemTagVO_.resourceUuid, volumeUuid)
                .eq(SystemTagVO_.tag, VolumeSystemTags.VOLUME_PROVISIONING_STRATEGY
                        .instantiateTag(map(e(VolumeSystemTags.VOLUME_PROVISIONING_STRATEGY_TOKEN, VolumeProvisioningStrategy.ThickProvisioning.toString()))))
                .isExists()

        deleteVolumeSnapshotGroup {
            uuid = group.uuid
        }

        SQL.New(VolumeVO.class).eq(VolumeVO_.uuid, volumeUuid).delete();
    }
}
