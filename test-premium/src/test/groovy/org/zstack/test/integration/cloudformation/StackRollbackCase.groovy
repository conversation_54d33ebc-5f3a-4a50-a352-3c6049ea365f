package org.zstack.test.integration.cloudformation

import org.apache.commons.io.FileUtils
import org.springframework.http.HttpEntity
import org.zstack.cloudformation.ResourceStackStatus
import org.zstack.core.db.Q
import org.zstack.network.service.virtualrouter.VirtualRouterCommands
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.utils.data.SizeUnit
import org.zstack.header.cloudformation.CloudFormationStackResourceRefVO
import org.zstack.header.cloudformation.CloudFormationStackResourceRefVO_
import org.zstack.header.cloudformation.CloudFormationStackEventVO
import org.zstack.header.cloudformation.ResourceStackVO
import org.zstack.header.cloudformation.ResourceStackVO_
import org.zstack.header.vpc.VpcRouterVmInventory
import org.zstack.kvm.KVMConstant
import org.zstack.header.vm.VmInstanceState
import org.zstack.sdk.CloudFormationStackEventInventory
import org.zstack.sdk.CreateResourceStackAction
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.HostInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L2NetworkInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.ResourceStackInventory
import org.zstack.sdk.VirtualRouterOfferingInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.ZoneInventory
import org.zstack.storage.primary.local.LocalStorageKvmBackend
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.utils.path.PathUtil
/**
 * Created by mingjian.deng on 2018/6/13.
 */
class StackRollbackCase extends PremiumSubCase {
    EnvSpec env
    String content
    String param
    ResourceStackInventory inventory

    @Override
    void environment() {
        env = CloudFormationEnv.createL3()
    }

    @Override
    void test() {
        env.create {
            prepareFailed()
            createStackFailed()
            createStackFailedWithRollback()

            prepareFailedCheck()
            createStackErrorCheck()
            createStackErrorCheckWithRollback()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    void prepareFailed() {
        File dataFile = PathUtil.findFileOnClassPath("cloudformation/Failed.json")
        File paramFile = PathUtil.findFileOnClassPath("cloudformation/Failed-Parameters.json")
        content = FileUtils.readFileToString(dataFile)
        def tmp = FileUtils.readFileToString(paramFile)
        def image = env.inventoryByName("image") as ImageInventory
        def instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        def diskOffering = env.inventoryByName("diskOffering") as DiskOfferingInventory
        def l3 = env.inventoryByName("l3") as L3NetworkInventory
        def ps = env.inventoryByName("local") as PrimaryStorageInventory
        def host = env.inventoryByName("kvm") as HostInventory

        param = tmp.replace("imageUuid-replace", image.uuid).replace("l3NetworkUuid-replace", l3.uuid).
                replace("instanceOfferingUuid-replace", instanceOffering.uuid).
                replace("diskOfferingUuid-replace", diskOffering.uuid).replace("ps-replace", ps.uuid).
                replace("host-replace", host.uuid)

    }
    
    void createStackFailed() {
        env.simulator(LocalStorageKvmBackend.CREATE_EMPTY_VOLUME_PATH) { HttpEntity<String> e, EnvSpec espec ->
            def cmd = JSONObjectUtil.toObject(e.getBody(), LocalStorageKvmBackend.CreateEmptyVolumeCmd.class)
            def rsp = new LocalStorageKvmBackend.CreateEmptyVolumeRsp()
            if (cmd.name == "empty-volume") {
                rsp.error = "mock failed!"
            }
            return rsp
        }

        def action = new CreateResourceStackAction()
        action.sessionId = adminSession()
        action.name = "stack"
        action.templateContent = content
        action.parameters = param
        action.rollback = false
        def result = action.call()
        assert result.error != null

        ResourceStackVO vo = Q.New(ResourceStackVO.class).eq(ResourceStackVO_.name, "stack").find()

        assert !vo.enableRollback
        assert vo.status == ResourceStackStatus.Failed
        assert vo.reason != null
        assert Q.New(CloudFormationStackResourceRefVO.class).count() == 2

        def vm = queryVmInstance {} as List<VmInstanceInventory>
        assert vm.size() == 2
        assert vm[0].state == VmInstanceState.Running.toString()
        assert vm[1].state == VmInstanceState.Running.toString()

        def events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 6

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Start"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 3

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Finish"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 2

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Failed"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 1

        deleteResourceStack {
            uuid = vo.uuid
        }
        vm = queryVmInstance {} as List<VmInstanceInventory>
        assert vm.size() == 1
    }

    void createStackFailedWithRollback() {
        env.simulator(LocalStorageKvmBackend.CREATE_EMPTY_VOLUME_PATH) { HttpEntity<String> e, EnvSpec espec ->
            def cmd = JSONObjectUtil.toObject(e.getBody(), LocalStorageKvmBackend.CreateEmptyVolumeCmd.class)
            def rsp = new LocalStorageKvmBackend.CreateEmptyVolumeRsp()
            if (cmd.name == "empty-volume") {
                rsp.error = "mock failed!"
            }
            return rsp
        }

        def action = new CreateResourceStackAction()
        action.sessionId = adminSession()
        action.name = "stack-rollback"
        action.templateContent = content
        action.parameters = param
        action.rollback = true
        def result = action.call()
        assert result.error != null

        def list = queryResourceStack {
            conditions=["name=stack-rollback"]
        } as List<ResourceStackInventory>

        assert list[0].enableRollback
        assert list[0].status == ResourceStackStatus.Rollbacked.toString()
        assert list[0].reason != null

        def vm = queryVmInstance {} as List<VmInstanceInventory>
        assert vm.size() == 1

        assert Q.New(CloudFormationStackResourceRefVO.class).eq(CloudFormationStackResourceRefVO_.stackUuid, list[0].uuid).count() == 0

        def events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 14

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "actionStatus=Start"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 3

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "actionStatus=Finish"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 2

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "actionStatus=Failed"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 1

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "actionStatus=RollbackStart"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 4

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "actionStatus=RollbackFinish"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 4


        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "action=CreateVmInstanceAction"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 4

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "action=CreateDataVolumeAction"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 2

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "action=DestroyVmInstanceAction"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 4

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${list[0].uuid}", "action=ExpungeVmInstanceAction"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 4

        deleteResourceStack {
            uuid = list[0].uuid
        }
    }

    void prepareFailedCheck() {
        env.cleanSimulatorHandlers()
        def pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        def image = env.inventoryByName("image") as ImageInventory
        def zone = env.inventoryByName("zone") as ZoneInventory
        def vr = createVirtualRouterOffering {
            name = "virtualRouterOffering"
            memorySize = SizeUnit.MEGABYTE.toByte(512)
            cpuNum = 2
            managementNetworkUuid = pubL3.uuid
            publicNetworkUuid = pubL3.uuid
            imageUuid = image.uuid
            zoneUuid = zone.uuid
        } as VirtualRouterOfferingInventory
        File dataFile = PathUtil.findFileOnClassPath("cloudformation/ErrorCheck.json")
        File paramFile = PathUtil.findFileOnClassPath("cloudformation/ErrorCheck-Parameters.json")
        content = FileUtils.readFileToString(dataFile)
        def tmp = FileUtils.readFileToString(paramFile)
        def instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        def l2 = env.inventoryByName("l2") as L2NetworkInventory

        param = tmp.replace("imageUuid-replace", image.uuid).replace("l2NetworkUuid-replace", l2.uuid).
                replace("instanceOfferingUuid-replace", instanceOffering.uuid).
                replace("virtualRouterOfferingUuid-replace", vr.uuid)
    }

    void createStackErrorCheck() {
        env.simulator(KVMConstant.KVM_ATTACH_NIC_PATH) { HttpEntity<String> entity, EnvPremiumSpec spec ->
            VirtualRouterCommands.ConfigureNicRsp rsp = new VirtualRouterCommands.ConfigureNicRsp()
            rsp.success = false
            rsp.error = "mock failed!"
            return rsp
        }

        def action = new CreateResourceStackAction()
        action.sessionId = adminSession()
        action.name = "stack-fail"
        action.templateContent = content
        action.parameters = param
        action.rollback = false
        def result = action.call()
        assert result.error != null
        assert result.error.details.contains("mock failed!")


        ResourceStackVO vo = Q.New(ResourceStackVO.class).eq(ResourceStackVO_.name, "stack-fail").find()

        assert !vo.enableRollback
        assert vo.status == ResourceStackStatus.Failed
        assert vo.reason != null
        assert vo.reason.contains("mock failed!")
        assert Q.New(CloudFormationStackResourceRefVO.class).count() == 2

        def list_l3 = queryL3Network {
            conditions=["type=L3VpcNetwork"]
        } as List<L3NetworkInventory>
        assert list_l3.size() == 1
        assert list_l3[0].getName() == "VPC-web"

        def list_router = queryVpcRouter {} as List<VpcRouterVmInventory>
        assert list_router.size() == 1
        assert list_router[0].getName() == "VPC-web"

        def events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 10

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Start"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 5

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Finish"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 4

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Failed"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 1

        deleteResourceStack {
            uuid = vo.getUuid()
        }

        assert Q.New(CloudFormationStackResourceRefVO.class).count() == 0
        assert Q.New(CloudFormationStackEventVO.class).count() == 0
    }

    void createStackErrorCheckWithRollback() {
        env.simulator(KVMConstant.KVM_ATTACH_NIC_PATH) { HttpEntity<String> entity, EnvPremiumSpec spec ->
            VirtualRouterCommands.ConfigureNicRsp rsp = new VirtualRouterCommands.ConfigureNicRsp()
            rsp.success = false
            rsp.error = "mock failed!"
            return rsp
        }

        def action = new CreateResourceStackAction()
        action.sessionId = adminSession()
        action.name = "stack-rollback"
        action.templateContent = content
        action.parameters = param
        action.rollback = true
        def result = action.call()
        assert result.error != null
        assert result.error.details.contains("mock failed!")

        ResourceStackVO vo = Q.New(ResourceStackVO.class).eq(ResourceStackVO_.name, "stack-rollback").find()

        assert vo.enableRollback
        assert vo.status == ResourceStackStatus.Rollbacked
        assert vo.reason != null
        assert vo.reason.contains("mock failed!")
        assert Q.New(CloudFormationStackResourceRefVO.class).count() == 0

        def events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 14

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Start"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 5

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Finish"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 4

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=Failed"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 1

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=RollbackStart"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 2

        events = queryEventFromResourceStack {
            conditions=["stackUuid=${vo.uuid}", "actionStatus=RollbackFinish"]
        } as List<CloudFormationStackEventInventory>
        assert events.size() == 2

        deleteResourceStack {
            uuid = vo.getUuid()
        }

        assert Q.New(CloudFormationStackResourceRefVO.class).count() == 0
        assert Q.New(CloudFormationStackEventVO.class).count() == 0
    }
}
