package org.zstack.test.integration.hybrid.network.connection

import org.zstack.core.cloudbus.CloudBus
import org.zstack.header.aliyun.network.connection.AliyunRouterInterfaceStatus
import org.zstack.header.aliyun.network.connection.ConnectRouterInterfaceMsg
import org.zstack.header.aliyun.network.connection.ConnectRouterInterfaceReply
import org.zstack.header.aliyun.network.connection.CreateRouterInterfaceMsg
import org.zstack.header.aliyun.network.connection.CreateRouterInterfaceReply
import org.zstack.header.aliyun.network.connection.UpdateRouterInterfaceMsg
import org.zstack.header.aliyun.network.connection.UpdateRouterInterfaceReply
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vpc.VpcConstants
import org.zstack.ipsec.IPsecConstants
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.test.integration.hybrid.HybridTest
import org.zstack.test.integration.hybrid.network.vpc.VpcEnv
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.hybrid.HybridPremiumSpec
import org.zstack.testlib.premium.hybrid.HybridTestConstant
import org.zstack.utils.data.SizeUnit
import org.zstack.vrouterRoute.VRouterRouteConstants

/**
 * Created by mingjian.deng on 17/6/29.
 */
class ConnectionEnv {
    static HybridPremiumSpec createRegion() {
        return HybridTest.makeEnv {
            hybridAccount {
                name = HybridTestConstant.testHybridAccountName
                key = HybridTestConstant.testAccountKey
                secret = HybridTestConstant.testAccountSecret
                type = "aliyun"
            }
            dataCenter {
                regionId = "cn-hangzhou"
                type = "aliyun"
                description = "createDataCenter test"
                name = HybridTestConstant.testDataCenterName
            }
            identityZone {
                useDc(HybridTestConstant.testDataCenterName)
                zoneId = "cn-hangzhou-c"
                type = "aliyun"
                description = "Test-Identity-Zone"
                name = HybridTestConstant.testIdentityZoneName
            }
        }
    }

    static HybridPremiumSpec createVpc() {
        return HybridTest.makeEnv (createRegion()) {
            ecsVpc {
                name = HybridTestConstant.testEcsVpcName
                description = "Test-Vpc"
                cidrBlock = "***********/16"
                vpcId = HybridTestConstant.testEcsVpcId
                vRouterName = HybridTestConstant.testEcsVRouterName

                useDc(HybridTestConstant.testDataCenterName)
            }
        }
    }

    static HybridPremiumSpec createAP() {
        return HybridTest.makeEnv (createVpc()) {
            ap {
                accessPointId = "ap-cn-shanghai-bs-B"
                type = "VPC"
                name = HybridTestConstant.testAccessPointName
                hostOperator = "China-Net"
                description = "Test-Access-Point"
                status = "recommended"
                useDc(HybridTestConstant.testDataCenterName)
            }
        }
    }

    static HybridPremiumSpec createVBR() {
        return HybridTest.makeEnv (createAP()) {
            vbr {
                vbrId = HybridTestConstant.testEcsVBRId
                status = "active"
                vlanId = "1508"
                vlanInterfaceId = "Test-Vlan-Interface-id"
                circuitCode = ""
                localGatewayIp = "*********"
                peerGatewayIp = "*********"
                peeringSubnetMask = "***************"
                physicalConnectionId = "Test-Physical-Connection-Id"
                physicalConnectionStatus = "Enabled"
                name = HybridTestConstant.testEcsVBRName
                description = "Test-Border-Router"
                useDc(HybridTestConstant.testDataCenterName)
                useAP(HybridTestConstant.testAccessPointName)
            }
        }
    }

    static HybridPremiumSpec createRI() {
        return HybridTest.makeEnv (createVBR()) {
            ri {
                description = "Test-Router-Interface-1"
                name = "VBR-Ri-Name"
                spec = "Large.1"
                routerType = "VBR"
                useAP(HybridTestConstant.testAccessPointName)
                useDc(HybridTestConstant.testDataCenterName)
                useRouter(HybridTestConstant.testEcsVBRName)
            }

            ri {
                description = "Test-Router-Interface-2"
                name = "VRouter-Ri-Name"
                routerType = "VRouter"
                useAP(HybridTestConstant.testAccessPointName)
                useDc(HybridTestConstant.testDataCenterName)
                useRouter(HybridTestConstant.testEcsVpcName)
            }

        }
    }

    static HybridPremiumSpec createL3() {
        return HybridTest.makeEnv (createRI()) {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image"
                    url = "http://zstack.org/download/vr1.qcow2"
                }

                image {
                    name = "vr-image"
                    url = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm-1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalCpu = 20
                        totalMem = SizeUnit.GIGABYTE.toByte(20)
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"
                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }


                        ip {
                            startIp = "**************"
                            endIp = "***************"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "l3-1"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "pubL3"
                        category = "Public"

                        ip {
                            startIp = "************0"
                            endIp = "************00"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }
                }

                attachBackupStorage("sftp")

                virtualRouterOffering {
                    name = "vr"
                    memory = SizeUnit.GIGABYTE.toByte(1)
                    cpu = 1
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr-image")
                }
            }

            vm {
                name = "vm-1"
                useImage("image")
                useL3Networks("l3")
                useInstanceOffering("instanceOffering")
            }
        }
    }

    static void mockmessage(EnvSpec env) {
        VpcEnv.mockmessage(env)
        env.message(CreateRouterInterfaceMsg.class) { CreateRouterInterfaceMsg msg, CloudBus bus ->
            def reply = new CreateRouterInterfaceReply()
            reply.routerInterfaceId = msg.name + "-Id"
            reply.status = AliyunRouterInterfaceStatus.idle.toString()
            bus.reply(msg, reply)
        }

        env.message(UpdateRouterInterfaceMsg.class) { UpdateRouterInterfaceMsg msg, CloudBus bus ->
            def reply = new UpdateRouterInterfaceReply()
            bus.reply(msg, reply)
        }

        env.message(ConnectRouterInterfaceMsg.class) { ConnectRouterInterfaceMsg msg, CloudBus bus ->
            def reply = new ConnectRouterInterfaceReply()
            reply.status = AliyunRouterInterfaceStatus.active.toString()
            bus.reply(msg, reply)
        }
    }
}
