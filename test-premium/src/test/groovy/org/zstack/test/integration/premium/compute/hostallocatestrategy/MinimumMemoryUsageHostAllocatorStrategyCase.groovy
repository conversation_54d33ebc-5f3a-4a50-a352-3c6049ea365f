package org.zstack.test.integration.premium.compute.hostallocatestrategy

import org.zstack.pluginpremium.compute.allocator.HostAllocatorConstant
import org.zstack.pluginpremium.compute.allocator.HostAllocatorSystemTags
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.util.TProxy
import org.zstack.utils.data.SizeUnit
import org.zstack.zwatch.datatype.Datapoint
import org.zstack.zwatch.datatype.MetricQueryObject
import org.zstack.zwatch.driver.DatabaseDriver
import org.zstack.zwatch.namespace.HostNamespace
import org.zstack.zwatch.prometheus.PrometheusDatabaseDriver

import java.util.concurrent.atomic.AtomicInteger

/**
 * Created by lining on 2018/03/05.
 */

class MinimumMemoryUsageHostAllocatorStrategyCase extends PremiumSubCase{
    PrometheusDatabaseDriver driver
    List<Datapoint> currentDatapoints = []
    EnvSpec env
    TProxy proxy

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        driver = bean(PrometheusDatabaseDriver.class)

        proxy = new TProxy(driver).mockMethod("query") { MetricQueryObject queryObject ->
            return currentDatapoints
        }

        HostNamespace ns = bean(HostNamespace.class)
        def p = proxy.protect(ns, "driver")
        onCleanExecute { p.recover() }
        ns.driver = proxy as DatabaseDriver

        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
                allocatorStrategy = HostAllocatorConstant.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_TYPE
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image"
                    url  = "http://zstack.org/download/test.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }
                }

                attachBackupStorage("sftp")
            }

        }
    }

    @Override
    void test() {
        env.create {
            testVmCreatedWithTheStrategy()

            testMinimumMemoryUsageHostAllocatorStrategyMode()
        }
    }

    void testVmCreatedWithTheStrategy(){

        ImageInventory image = env.inventoryByName("image")
        L3NetworkInventory l3 = env.inventoryByName("l3")
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        HostInventory host1 = env.inventoryByName("kvm1")
        HostInventory host2 = env.inventoryByName("kvm2")

        currentDatapoints = [
                new Datapoint(value: 1, time: 1, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host1.uuid]),
                new Datapoint(value: 10,  time: 1, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
                new Datapoint(value: 1, time: 5, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host1.uuid]),
                new Datapoint(value: 10, time: 5, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
        ]
        for (int i = 0; i < 5; i ++) {
            VmInstanceInventory newVm = createVmInstance {
                name = "newVm"
                instanceOfferingUuid = instanceOffering.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                clusterUuid = host1.clusterUuid
            }
            assert newVm.hostUuid == host1.uuid
        }


        currentDatapoints = [
                new Datapoint(value: 10, time: 1, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host1.uuid]),
                new Datapoint(value: 10,  time: 1, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
                new Datapoint(value: 10, time: 5, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host1.uuid]),
                new Datapoint(value: 5, time: 5, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
        ]

        AtomicInteger count = new AtomicInteger(0)

        List<Thread> threads = []
        int times = 5
        for (int i = 0; i < times; i ++) {
            def thread = Thread.start {
                VmInstanceInventory newVm = createVmInstance {
                    name = "newVm"
                    instanceOfferingUuid = instanceOffering.uuid
                    imageUuid = image.uuid
                    l3NetworkUuids = [l3.uuid]
                    clusterUuid = host1.clusterUuid
                }
                assert newVm.hostUuid == host2.uuid
                count.addAndGet(1)
            }

            threads.add(thread)
        }

        threads.each { it.join() }

        // confirm batch creation of vms' host is allocated by minimum memory
        assert count.get() == times

        currentDatapoints = [
                new Datapoint(value: 10, time: 1, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host1.uuid]),
                new Datapoint(value: 10,  time: 1, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
                new Datapoint(value: 5, time: 5, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
        ]
        for (int i = 0; i < 5; i ++) {
            VmInstanceInventory newVm = createVmInstance {
                name = "newVm"
                instanceOfferingUuid = instanceOffering.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                clusterUuid = host1.clusterUuid
            }
            assert newVm.hostUuid == host2.uuid
        }


        currentDatapoints = [
                new Datapoint(value: 10,  time: 1, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
                new Datapoint(value: 5, time: 5, labels: [(HostNamespace.LabelNames.HostUuid.toString()): host2.uuid]),
        ]
        for (int i = 0; i < 5; i ++) {
            VmInstanceInventory newVm = createVmInstance {
                name = "newVm"
                instanceOfferingUuid = instanceOffering.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
                clusterUuid = host1.clusterUuid
            }
            assert newVm.hostUuid == host2.uuid
        }

    }

    void testMinimumMemoryUsageHostAllocatorStrategyMode() {
        proxy.mockMethod("query") { MetricQueryObject queryObject ->
            throw new Exception("expect error")
        }

        ImageInventory image = env.inventoryByName("image")
        L3NetworkInventory l3 = env.inventoryByName("l3")
        InstanceOfferingInventory noMode = env.inventoryByName("instanceOffering")

        expect (AssertionError.class) {
            createInstanceOffering {
                name = "instanceOffering"
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                allocatorStrategy = HostAllocatorConstant.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_TYPE
                systemTags = [HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE.instantiateTag([(HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE_TOKEN): "soft1"])]
            }
        }

        InstanceOfferingInventory softMode = createInstanceOffering {
            name = "instanceOffering"
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            allocatorStrategy = HostAllocatorConstant.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_TYPE
            systemTags = [HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE.instantiateTag([(HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE_TOKEN): HostAllocatorConstant.HOST_ALLOCATOR_STRATEGY_MODE_SOFT])]
        }

        VmInstanceInventory newVm = createVmInstance {
            name = "newVm"
            instanceOfferingUuid = softMode.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
        }

        newVm = createVmInstance {
            name = "newVm"
            instanceOfferingUuid = noMode.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
        }

        InstanceOfferingInventory hardMode = createInstanceOffering {
            name = "instanceOffering"
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            allocatorStrategy = HostAllocatorConstant.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_TYPE
            systemTags = [HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE.instantiateTag([(HostAllocatorSystemTags.MINIMUM_MEMORY_USAGE_HOST_ALLOCATOR_STRATEGY_MODE_TOKEN): HostAllocatorConstant.HOST_ALLOCATOR_STRATEGY_MODE_HARD])]
        }

        expect (AssertionError.class) {
            createVmInstance {
                name = "newVm"
                instanceOfferingUuid = hardMode.uuid
                imageUuid = image.uuid
                l3NetworkUuids = [l3.uuid]
            }
        }

    }

    @Override
    void clean() {
        env.delete()
    }
}
