package org.zstack.test.integration.premium.kvm.vm

import org.springframework.http.HttpEntity
import org.zstack.compute.vm.VmSystemTags
import org.zstack.core.Platform
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.Q
import org.zstack.core.errorcode.ErrorFacade
import org.zstack.core.timeout.ApiTimeoutGlobalProperty
import org.zstack.core.timeout.ApiTimeoutManagerImpl
import org.zstack.header.host.HostStateEvent
import org.zstack.header.identity.AccountResourceRefVO
import org.zstack.header.identity.AccountResourceRefVO_
import org.zstack.header.image.ImageConstant
import org.zstack.header.image.ImagePlatform
import org.zstack.header.image.ImageVO
import org.zstack.header.message.MessageReply
import org.zstack.header.storage.snapshot.VolumeSnapshotTreeVO
import org.zstack.header.storage.snapshot.VolumeSnapshotTreeVO_
import org.zstack.header.storage.snapshot.VolumeSnapshotVO
import org.zstack.header.storage.snapshot.VolumeSnapshotVO_
import org.zstack.header.vm.APIChangeVmImageMsg
import org.zstack.header.vm.ChangeVmImageOverlayMsg
import org.zstack.header.vm.ChangeVmImageReply
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmInstanceVO
import org.zstack.header.vm.VmInstanceVO_
import org.zstack.header.volume.OverwriteVolumeMsg
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.image.ImageSystemTags
import org.zstack.mevoco.MevocoSystemTags
import org.zstack.sdk.*
import org.zstack.storage.backup.sftp.SftpBackupStorageCommands
import org.zstack.storage.backup.sftp.SftpBackupStorageConstant
import org.zstack.storage.primary.local.LocalStorageKvmBackend
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.BackupStorageSpec
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.core.Platform.operr
import static org.zstack.utils.CollectionDSL.e
import static org.zstack.utils.CollectionDSL.map

/**
 * Created by GuoYi on 17-11-4.
 */
class ChangeVmImageOnLocalStorageCase extends PremiumSubCase {
    EnvSpec env

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(4)
                cpu = 1
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(10)
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.iso"
                    mediaType = ImageConstant.ImageMediaType.ISO.toString()
                    platform = ImagePlatform.Linux.toString()
                }

                image {
                    name = "image2"
                    url = "http://zstack.org/download/test2.qcow2"
                    delegate.size = SizeUnit.GIGABYTE.toByte(10)
                    platform = ImagePlatform.Windows.toString()
                }

                image {
                    name = "image3"
                    url = "http://zstack.org/download/test3.qcow2"
                    size = SizeUnit.GIGABYTE.toByte(10)
                    platform = ImagePlatform.Linux.toString()
                }

                image {
                    name = "image-qga"
                    url = "http://zstack.org/download/test3.qcow2"
                    size = SizeUnit.GIGABYTE.toByte(10)
                    platform = ImagePlatform.Linux.toString()
                    systemTags = ["qemuga"]
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                    system = true
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(32)
                        totalCpu = 8
                    }

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(32)
                        totalCpu = 8
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(100)
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "***********"
                            endIp = "***********0"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                attachBackupStorage("sftp")
            }

            vm {
                name = "vm"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useRootDiskOffering("diskOffering")
                useDiskOfferings("diskOffering")
                useL3Networks("pubL3")
                useHost("kvm1")
            }
        }
    }

    @Override
    void test() {
        env.create {
            testGetImageCandidatesForVmToChange()
            testChangeVmImage()
            testChangeVmImageFailed()
            testChangeVmImageWithQGA()
            testChangeVmImageLastHostDisabled()
            testChangeVmImageOverlayMsgTimeout()
            testChangeVmImageFailWithAPITimeout()
        }
    }

    void testChangeVmImageFailed() {
        env.message(OverwriteVolumeMsg.class){ msg, bus ->
            def reply = new MessageReply()
            reply.setError(operr("on purpose"))
            bus.reply(msg, reply)
        }

        VmInstanceInventory vm = env.inventoryByName("vm") as VmInstanceInventory
        ImageInventory image3 = env.inventoryByName("image3") as ImageInventory

        expectError {
            changeVmImage {
                sessionId = adminSession()
                vmInstanceUuid = vm.getUuid()
                imageUuid = image3.getUuid()
            }
        }

        vm = vm = queryVmInstance {
            conditions = ["uuid=${vm.uuid}"]
        }[0]

        assert vm.imageUuid != null
        env.cleanMessageHandlers()
    }

    void testGetImageCandidatesForVmToChange() {
        VmInstanceInventory vm = env.inventoryByName("vm") as VmInstanceInventory

        List<ImageInventory> candidates = getImageCandidatesForVmToChange {
            sessionId = adminSession()
            vmInstanceUuid = vm.getUuid()
        }

        assert candidates != null && 3 == candidates.size()
    }

    void testChangeVmImage() {
        VmInstanceInventory vm = env.inventoryByName("vm") as VmInstanceInventory
        ImageInventory image1 = env.inventoryByName("image1") as ImageInventory

        def bs = env.inventoryByName("sftp") as BackupStorageInventory

        def called = false

        env.simulator(SftpBackupStorageConstant.DOWNLOAD_IMAGE_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.getBody(), SftpBackupStorageCommands.DownloadCmd.class)
            BackupStorageSpec bsSpec = spec.specByUuid(cmd.uuid)

            def rsp = new SftpBackupStorageCommands.DownloadResponse()
            rsp.size = SizeUnit.GIGABYTE.toByte(10)
            rsp.actualSize = SizeUnit.GIGABYTE.toByte(10)
            rsp.availableCapacity = bsSpec.availableCapacity
            rsp.totalCapacity = bsSpec.totalCapacity
            called = true
            return rsp
        }

        ImageInventory image2 = addImage {
            name = "sized-image"
            url = "http://my-site/foo.qcow2"
            backupStorageUuids = [bs.uuid]
            format = ImageConstant.QCOW2_FORMAT_STRING
            platform = ImagePlatform.Windows.toString()

            systemTags = [
                    ImageSystemTags.BOOT_MODE.instantiateTag(map(e(ImageSystemTags.BOOT_MODE_TOKEN, "UEFI")))
            ]
        }

        assert called

	    ImageInventory image3 = env.inventoryByName("image3") as ImageInventory
        def newRootVolumeUuid = Platform.getUuid()

        // before change vm image
        VolumeVO oldVolume = dbFindByUuid(vm.getRootVolumeUuid(), VolumeVO.class)
        assert oldVolume.getVmInstanceUuid() == vm.getUuid()
        assert oldVolume.getRootImageUuid() == image1.getUuid()
        assert vm.getRootVolumeUuid() == oldVolume.getUuid()
        assert vm.getPlatform() == ImagePlatform.Linux.toString()

        // create snapshot
        def snap = createVolumeSnapshot {
            volumeUuid = vm.getRootVolumeUuid()
            name = "sp"
        } as VolumeSnapshotInventory

        stopVmInstance {
            sessionId = adminSession()
            uuid = vm.getUuid()
        }

        def beforeChangeImage = getPrimaryStorageCapacity {
            primaryStorageUuids = [oldVolume.primaryStorageUuid]
        } as GetPrimaryStorageCapacityResult

        def deletePathCalled = false
        def deleteFolderCalled = false
        LocalStorageKvmBackend.DeleteBitsCmd deleteCmd = null
        env.simulator(LocalStorageKvmBackend.DELETE_BITS_PATH) { HttpEntity<String> e, EnvSpec spec ->
            deleteCmd = JSONObjectUtil.toObject(e.getBody(),  LocalStorageKvmBackend.DeleteBitsCmd.class)
            deletePathCalled = true
            return new LocalStorageKvmBackend.DeleteBitsRsp()
        }

        env.simulator(LocalStorageKvmBackend.DELETE_DIR_PATH) {
            deleteFolderCalled = true
            return new LocalStorageKvmBackend.DeleteBitsRsp()
        }

        String createPath = null
        env.simulator(LocalStorageKvmBackend.CREATE_VOLUME_FROM_CACHE_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.getBody(),  LocalStorageKvmBackend.CreateVolumeFromCacheCmd.class)
            createPath = cmd.installUrl
            return new LocalStorageKvmBackend.CreateVolumeFromCacheRsp()
        }

        String oldInstallPath = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, oldVolume.uuid).select(VolumeVO_.installPath).findValue()
        changeVmImage {
            sessionId = adminSession()
            vmInstanceUuid = vm.getUuid()
            imageUuid = image2.getUuid()
            resourceUuid = newRootVolumeUuid
        }

        assert deleteCmd.path == oldInstallPath
        assert deleteCmd.path.contains(oldVolume.uuid + "/snapshots/" + snap.uuid + ".qcow2")
        assert null == Q.New(VmInstanceVO.class)
                .eq(VmInstanceVO_.uuid, vm.getUuid())
                .select(VmInstanceVO_.hostUuid)
                .findValue()

        retryInSecs {
            def afterChanged = getPrimaryStorageCapacity {
                primaryStorageUuids = [oldVolume.primaryStorageUuid]
            } as GetPrimaryStorageCapacityResult

            assert beforeChangeImage.availableCapacity + oldVolume.getSize() == afterChanged.availableCapacity + image2.getSize()
        }
        // called delete path but not folder
        assert deletePathCalled
        assert !deleteFolderCalled

        env.cleanSimulatorHandlers()

        // after change vm image and before start vm
        VmInstanceVO newVm = dbFindByUuid(vm.getUuid(), VmInstanceVO.class)
        assert newVm.getUuid() == vm.getUuid()
        assert newVm.getState() == VmInstanceState.Stopped
        assert newVm.getRootVolume() != null
        assert newVm.getRootVolumeUuid() != null
        assert newVm.getRootVolumeUuid() == oldVolume.getUuid()
        assert newVm.getImageUuid() == image2.getUuid()
        assert newVm.getPlatform() == ImagePlatform.Windows.toString()
        assert newVm.getLastHostUuid() == vm.getHostUuid()

        VolumeVO newVolume = dbFindByUuid(newVm.getRootVolumeUuid(), VolumeVO.class)
        assert newVolume.getUuid() == newVm.getRootVolumeUuid()
        assert newVolume.getVmInstanceUuid() == newVm.getUuid()
        assert newVolume.getPrimaryStorageUuid() == oldVolume.getPrimaryStorageUuid()

        // check accountResourceRef
        assert Q.New(AccountResourceRefVO).eq(AccountResourceRefVO_.resourceUuid, oldVolume.uuid).isExists()

        assert newVolume.getInstallPath() != oldVolume.getInstallPath()
        assert newVolume.installPath.contains(oldVolume.uuid)
        assert newVolume.installPath == createPath
        assert newVolume.uuid == oldVolume.uuid

        // check boot mode
        assert VmSystemTags.BOOT_MODE.getTag(vm.getUuid()) == "bootMode::UEFI"

	    changeVmImage {
		    sessionId = adminSession()
		    vmInstanceUuid = vm.getUuid()
		    imageUuid = image3.getUuid()
	    }

	    VmInstanceVO newVm2 = dbFindByUuid(vm.getUuid(), VmInstanceVO.class)
	    assert newVm2.getRootVolume() != null
	    assert newVm2.getRootVolumeUuid() != null
	    assert newVm2.getRootVolumeUuid() != newRootVolumeUuid

	    VolumeVO newVolume2 = dbFindByUuid(newVm2.getRootVolumeUuid(), VolumeVO.class)
	    assert newVolume2.getUuid() == newVm2.getRootVolumeUuid()
	    assert newVolume2.getVmInstanceUuid() == newVm2.getUuid()
	    assert newVolume2.getPrimaryStorageUuid() == newVolume.getPrimaryStorageUuid()

	    assert Q.New(AccountResourceRefVO).eq(AccountResourceRefVO_.resourceUuid, newVolume2.getUuid()).isExists()
	    assert Q.New(AccountResourceRefVO).eq(AccountResourceRefVO_.resourceUuid, newVolume.uuid).isExists()

        assert Q.New(VolumeSnapshotVO.class).eq(VolumeSnapshotVO_.volumeUuid, vm.getRootVolumeUuid()).isExists()
        assert !Q.New(VolumeSnapshotVO.class).eq(VolumeSnapshotVO_.latest, true).eq(VolumeSnapshotVO_.volumeUuid, vm.getRootVolumeUuid()).isExists()
        assert !Q.New(VolumeSnapshotTreeVO.class).eq(VolumeSnapshotTreeVO_.current, true).eq(VolumeSnapshotTreeVO_.volumeUuid, vm.getRootVolumeUuid()).isExists()

        // check boot mode
        assert VmSystemTags.BOOT_MODE.getTag(vm.getUuid()) == "bootMode::Legacy"

        // after start vm
        startVmInstance {
            sessionId = adminSession()
            uuid = vm.getUuid()
        }

        newVm = dbFindByUuid(vm.getUuid(), VmInstanceVO.class)
        assert newVm.getState() == VmInstanceState.Running
        assert newVm.getHostUuid() == vm.getHostUuid()

        // after start vm
        stopVmInstance {
            sessionId = adminSession()
            uuid = vm.getUuid()
        }

        reimageVmInstance {
            vmInstanceUuid = vm.uuid
        }
    }

    void testChangeVmImageLastHostDisabled() {
        VmInstanceInventory vmInv = env.inventoryByName("vm") as VmInstanceInventory
        VmInstanceVO vmVO = dbFindByUuid(vmInv.getUuid(), VmInstanceVO.class)
        ImageInventory image2 = env.inventoryByName("image2") as ImageInventory
        ImageInventory image3 = env.inventoryByName("image3") as ImageInventory
        HostInventory host1 = env.inventoryByName("kvm1") as HostInventory

        // before change vm image
        VolumeVO oldVolume = dbFindByUuid(vmVO.getRootVolumeUuid(), VolumeVO.class)
        assert oldVolume.getVmInstanceUuid() == vmVO.getUuid()
        assert oldVolume.getRootImageUuid() == image3.getUuid()
        assert vmVO.getRootVolumeUuid() == oldVolume.getUuid()
        assert vmVO.getPlatform() == ImagePlatform.Linux.toString()

        stopVmInstance {
            sessionId = adminSession()
            uuid = vmVO.getUuid()
        }

        changeHostState {
            sessionId = adminSession()
            uuid = host1.getUuid()
            stateEvent = HostStateEvent.disable
        }

        expect(AssertionError.class) {
            changeVmImage {
                sessionId = adminSession()
                vmInstanceUuid = vmVO.getUuid()
                imageUuid = image2.getUuid()
            }
        }
    }

    void testChangeVmImageWithQGA() {
        VmInstanceInventory vmInventory = env.inventoryByName("vm") as VmInstanceInventory
        ImageInventory imageQGA = env.inventoryByName("image-qga") as ImageInventory
        ImageInventory image3 = env.inventoryByName("image3") as ImageInventory

        assert !VmSystemTags.VM_INJECT_QEMUGA.hasTag(vmInventory.uuid, VmInstanceVO.class)
        assert !VmSystemTags.VM_INJECT_QEMUGA.hasTag(image3.uuid, ImageVO.class)
        assert VmSystemTags.VM_INJECT_QEMUGA.hasTag(imageQGA.uuid, ImageVO.class)

        stopVmInstance {
            sessionId = adminSession()
            uuid = vmInventory.uuid
        }

        changeVmImage {
            sessionId = adminSession()
            vmInstanceUuid = vmInventory.uuid
            imageUuid = imageQGA.uuid
        }
        assert VmSystemTags.VM_INJECT_QEMUGA.hasTag(vmInventory.uuid, VmInstanceVO.class)

        changeVmImage {
            sessionId = adminSession()
            vmInstanceUuid = vmInventory.uuid
            imageUuid = image3.uuid
        }
        assert !VmSystemTags.VM_INJECT_QEMUGA.hasTag(vmInventory.uuid, VmInstanceVO.class)
    }

    void testChangeVmImageOverlayMsgTimeout() {
        long msgTimeout = 0

        env.message(ChangeVmImageOverlayMsg.class) { ChangeVmImageOverlayMsg msg, CloudBus bus ->
            def reply = new ChangeVmImageReply()
            msgTimeout = msg.getTimeout()
            bus.reply(msg, reply)
        }

        ImageInventory image3 = env.inventoryByName("image3") as ImageInventory
        ImageInventory image2 = env.inventoryByName("image2") as ImageInventory
        VmInstanceInventory vm = env.inventoryByName("vm") as VmInstanceInventory
        HostInventory host1 = env.inventoryByName("kvm1") as HostInventory

        changeHostState {
            sessionId = adminSession()
            uuid = host1.getUuid()
            stateEvent = HostStateEvent.enable
        }

        stopVmInstance {
            sessionId = adminSession()
            uuid = vm.getUuid()
        }

        ChangeVmImageAction action = new ChangeVmImageAction()
        action.vmInstanceUuid = vm.getUuid()
        action.imageUuid = image2.getUuid()
        action.sessionId = adminSession()
        ChangeVmImageAction.Result result = action.call()

        // msg anotation: @DefaultTimeout(timeunit = TimeUnit.HOURS, value = 24)
        // so 24(hour) * 60(minute) * 60(second) * 1000 (ms) =  86400000
        assert msgTimeout == 86400000
    }

    void testChangeVmImageFailWithAPITimeout() {
        env.cleanSimulatorHandlers()

        ErrorFacade errf = bean(ErrorFacade.class)
        env.message(ChangeVmImageOverlayMsg.class) { ChangeVmImageOverlayMsg msg, CloudBus bus ->
            def reply = new ChangeVmImageReply()
            sleep(10000)
            bus.reply(msg, reply)
        }

        ImageInventory image3 = env.inventoryByName("image3") as ImageInventory
        ImageInventory image2 = env.inventoryByName("image2") as ImageInventory
        VmInstanceInventory vm = env.inventoryByName("vm") as VmInstanceInventory
        HostInventory host1 = env.inventoryByName("kvm1") as HostInventory

        changeHostState {
            sessionId = adminSession()
            uuid = host1.getUuid()
            stateEvent = HostStateEvent.enable
        }

        ApiTimeoutGlobalProperty.MINIMAL_TIMEOUT = 500

        updateGlobalConfig {
            category = ApiTimeoutManagerImpl.APITIMEOUT_GLOBAL_CONFIG_TYPE
            name = APIChangeVmImageMsg.class.getName()
            value = "5000"
        }

        assert dbFindByUuid(vm.uuid, VmInstanceVO.class).getImageUuid() == image3.getUuid()

        stopVmInstance {
            sessionId = adminSession()
            uuid = vm.getUuid()
        }

        ChangeVmImageAction action = new ChangeVmImageAction()
        action.vmInstanceUuid = vm.getUuid()
        action.imageUuid = image2.getUuid()
        action.sessionId = adminSession()
        ChangeVmImageAction.Result result = action.call()
        assert result.error != null
        assert result.error.description.contains("timeout")


        VmInstanceVO vmvo = dbFindByUuid(vm.uuid, VmInstanceVO.class)
        VolumeVO volume = dbFindByUuid(vmvo.rootVolumeUuid, VolumeVO.class) as VolumeVO
        assert vmvo.getImageUuid() == image3.getUuid()
        assert vmvo.getRootVolumeUuid() == volume.getUuid()
        assert volume.getRootImageUuid() == image3.getUuid()
        assert volume.getVmInstanceUuid() == vmvo.getUuid()

        env.cleanMessageHandlers()

        updateGlobalConfig {
            category = ApiTimeoutManagerImpl.APITIMEOUT_GLOBAL_CONFIG_TYPE
            name = APIChangeVmImageMsg.class.getName()
            value = "1800000"
        }

        ApiTimeoutGlobalProperty.MINIMAL_TIMEOUT = "5m"
    }

    @Override
    void clean() {
        env.delete()
    }
}
