package org.zstack.test.integration.premium.kvm.vm

import org.springframework.http.HttpEntity

/**
 * Created by lining on 2019/01/10.
 */
import org.zstack.compute.vm.VmSystemTags
import org.zstack.header.image.ImageConstant
import org.zstack.header.network.l3.L3NetworkVO
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.sriov.VmVfNicConstant
import org.zstack.header.vm.VmInstanceConstant
import org.zstack.header.vm.VmNicDriverType
import org.zstack.header.vm.VmNicParam
import org.zstack.header.vm.VmNicState
import org.zstack.network.service.flat.FlatDhcpBackend
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.flat.FlatUserdataBackend
import org.zstack.network.service.userdata.UserdataConstant
import org.zstack.sdk.CloneVmInstanceInventory
import org.zstack.sdk.CloneVmInstanceResult
import org.zstack.sdk.FreeIpInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L2NetworkInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.VmCdRomInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VmNicInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.sdk.ImageInventory
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.utils.network.IPv6Constants

class CloneVmCdRomCase extends PremiumSubCase{

    EnvSpec env

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env{
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            imageStore {
                name = "imagestore"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "image"
                    url  = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "iso"
                    url  = "http://zstack.org/download/test.iso"
                    format = ImageConstant.ISO_FORMAT_STRING.toString()
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/zstack"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }

                        ipv6 {
                            name = "ipv6-Stateless-DHCP"
                            networkCidr = "2024:05:30:1::/64"
                            addressMode = "Stateless-DHCP"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }
                    }
                }

                attachBackupStorage("imagestore")
            }
        }
    }

    @Override
    void test() {
        env.create {
            testCdRomWhenCloneVM()
            testCloneWithoutIpam()
        }
    }

    void testCdRomWhenCloneVM(){
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        L2NetworkInventory l2 = env.inventoryByName("l2")
        L3NetworkInventory l3 = env.inventoryByName("l3")
        L3NetworkInventory pubL3 = env.inventoryByName("pubL3")
        ImageInventory image = env.inventoryByName("image")
        ImageInventory iso = env.inventoryByName("iso")

        createSystemTag {
            resourceType = "L2NetworkVO"
            resourceUuid = l2.uuid
            tag = 'enableSRIOV'
        }

        VmInstanceInventory noCdRomVm = createVmInstance {
            name = "vm"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
            systemTags = [
                    "${VmSystemTags.CD_ROM_LIST_TOKEN}::${VmInstanceConstant.NONE_CDROM}::${VmInstanceConstant.NONE_CDROM}::${VmInstanceConstant.NONE_CDROM}".toString()
            ]
        }
        VmNicInventory orginalNic = noCdRomVm.vmNics.get(0)
        CloneVmInstanceResult cloneResult = cloneVmInstance {
            vmInstanceUuid = noCdRomVm.uuid
            names = ["test_clone"]
        }

        /* clonevm with specified IP by systemTag */
        def freeIp4s = getFreeIp {
            l3NetworkUuid = l3.getUuid()
            ipVersion = IPv6Constants.IPv4
            limit = 1
        } as List<FreeIpInventory>
        String ip1 = freeIp4s.get(0).getIp()
        cloneResult = cloneVmInstance {
            vmInstanceUuid = noCdRomVm.uuid
            names = ["test_clone"]
            systemTags = [String.format("staticIp::%s::%s", l3.uuid, ip1)]
        }
        VmInstanceInventory vm = cloneResult.result.inventories.get(0).inventory
        assert vm.getVmNics().get(0).ip == ip1

        /* clonevm with specified IP by VmNicPara */
        freeIp4s = getFreeIp {
            l3NetworkUuid = l3.getUuid()
            ipVersion = IPv6Constants.IPv4
            limit = 1
        } as List<FreeIpInventory>
        ip1 = freeIp4s.get(0).getIp()

        List<FreeIpInventory> freeIp6s = getFreeIp {
            l3NetworkUuid = l3.getUuid()
            ipVersion = IPv6Constants.IPv6
            limit = 1
        } as List<FreeIpInventory>
        String ip61 = freeIp6s.get(0).getIp()
        VmNicParam param = new VmNicParam()
        param.mac = "00:02:03:04:05:06"
        param.l3NetworkUuid = l3.uuid
        param.ip = ip1
        param.ip6 = ip61
        param.defaultNic = true
        param.driverType = orginalNic.driverType
        param.vmNicType = VmInstanceConstant.VIRTUAL_NIC_TYPE
        param.state = VmNicState.disable.toString()
        cloneResult = cloneVmInstance {
            vmInstanceUuid = noCdRomVm.uuid
            names = ["test_clone"]
            vmNicParams = [param]
        }
        vm = cloneResult.result.inventories.get(0).inventory
        VmNicInventory newNic = vm.vmNics.get(0)
        assert newNic.mac == "00:02:03:04:05:06"
        assert newNic.ip == ip1
        assert newNic.state == VmNicState.disable.toString()
        assert newNic.type == VmInstanceConstant.VIRTUAL_NIC_TYPE

        /* clonevm with change l3 by VmNicPara */
        freeIp4s = getFreeIp {
            l3NetworkUuid = l3.getUuid()
            ipVersion = IPv6Constants.IPv4
            limit = 1
        } as List<FreeIpInventory>
        ip1 = freeIp4s.get(0).getIp()
        freeIp4s = getFreeIp {
            l3NetworkUuid = pubL3.getUuid()
            ipVersion = IPv6Constants.IPv4
            limit = 1
        } as List<FreeIpInventory>
        String ip2 = freeIp4s.get(0).getIp()

        param = new VmNicParam()
        param.l3NetworkUuid = l3.uuid
        param.ip = ip1
        param.defaultNic = true
        param.driverType = VmNicDriverType.E1000_KVM.toString()
        param.vmNicType = orginalNic.type
        param.state = VmNicState.disable.toString()

        VmNicParam param2 = new VmNicParam()
        param2.l3NetworkUuid = pubL3.uuid
        param2.ip = ip2

        cloneResult = cloneVmInstance {
            vmInstanceUuid = noCdRomVm.uuid
            names = ["test_clone"]
            vmNicParams = [param, param2]
        }
        vm = cloneResult.result.inventories.get(0).inventory
        assert vm.vmNics.size() == 2
        assert vm.vmNics.get(0).l3NetworkUuid != vm.vmNics.get(1).l3NetworkUuid
        assert vm.defaultL3NetworkUuid == l3.uuid
        for (VmNicInventory nic : vm.vmNics) {
            if (nic.l3NetworkUuid == l3.uuid) {
                assert nic.ip == ip1
                assert nic.state == VmNicState.disable.toString()
                assert nic.driverType == VmNicDriverType.E1000_KVM.toString()
            } else {
                assert nic.l3NetworkUuid == pubL3.uuid
                assert nic.ip == ip2
                assert nic.state == VmNicState.enable.toString()
                assert nic.driverType == VmNicDriverType.VIRTIO.toString()
            }
        }

        /* clone 10 vms with change l3 by VmNicPara */
        freeIp4s = getFreeIp {
            l3NetworkUuid = l3.getUuid()
            ipVersion = IPv6Constants.IPv4
            limit = 1
        } as List<FreeIpInventory>
        ip1 = freeIp4s.get(0).getIp()
        freeIp4s = getFreeIp {
            l3NetworkUuid = pubL3.getUuid()
            ipVersion = IPv6Constants.IPv4
            limit = 1
        } as List<FreeIpInventory>
        ip2 = freeIp4s.get(0).getIp()

        param = new VmNicParam()
        param.l3NetworkUuid = l3.uuid
        param.ip = ip1
        param.defaultNic = true
        param.driverType = orginalNic.driverType
        param.vmNicType = orginalNic.type
        param.state = VmNicState.disable.toString()

        param2 = new VmNicParam()
        param2.l3NetworkUuid = pubL3.uuid
        param2.ip = ip2
        param2.defaultNic = false
        param2.driverType = orginalNic.driverType
        param2.vmNicType = orginalNic.type
        param2.state = VmNicState.enable.toString()

        cloneResult = cloneVmInstance {
            vmInstanceUuid = noCdRomVm.uuid
            names = ["test_clone0", "test_clone1", "test_clone2", "test_clone3", "test_clone4",
                     "test_clone5", "test_clone6", "test_clone7", "test_clone8", "test_clone9"]
            vmNicParams = [param, param2]
        }
        assert cloneResult.result.inventories.size() == 10

        VmInstanceInventory oneEmptyCdRomVm = createVmInstance {
            name = "vm"
            instanceOfferingUuid = vm.instanceOfferingUuid
            imageUuid = image.uuid
            l3NetworkUuids = [vm.defaultL3NetworkUuid]
            systemTags = [
                    "${VmSystemTags.CD_ROM_LIST_TOKEN}::empty::none::none".toString()
            ]
        }
        cloneResult = cloneVmInstance {
            vmInstanceUuid = oneEmptyCdRomVm.uuid
            names = ["test_clone"]
        }
        vm = cloneResult.result.inventories.get(0).inventory
        assert 1 == vm.vmCdRoms.size()
        VmCdRomInventory vmCdRomInventory = vm.vmCdRoms.get(0)
        assert 0 == vmCdRomInventory.deviceId
        assert null == vmCdRomInventory.isoUuid


        oneEmptyCdRomVm = createVmInstance {
            name = "vm"
            instanceOfferingUuid = vm.instanceOfferingUuid
            imageUuid = image.uuid
            l3NetworkUuids = [vm.defaultL3NetworkUuid]
            systemTags = [
                    "${VmSystemTags.CD_ROM_LIST_TOKEN}::${iso.uuid}::empty::none".toString()
            ]
        }
        cloneResult = cloneVmInstance {
            vmInstanceUuid = oneEmptyCdRomVm.uuid
            names = ["test_clone"]
        }
        vm = cloneResult.result.inventories.get(0).inventory
        assert 2 == vm.vmCdRoms.size()

        vmCdRomInventory = vm.vmCdRoms.find { VmCdRomInventory i -> i.isoUuid != null }
        assert 0 == vmCdRomInventory.deviceId
        assert iso.uuid == vmCdRomInventory.isoUuid
        assert null != vmCdRomInventory.isoInstallPath

        vmCdRomInventory = vm.vmCdRoms.find { VmCdRomInventory i -> i.isoUuid == null }
        assert 1 == vmCdRomInventory.deviceId
        assert null == vmCdRomInventory.isoUuid
    }

    void testCloneWithoutIpam() {
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        L2NetworkInventory l2 = env.inventoryByName("l2")
        ImageInventory image = env.inventoryByName("image")

        L3NetworkInventory l3_2 = createL3Network {
            category = "Private"
            l2NetworkUuid = l2.uuid
            name = "l3-2"
        }

        VmInstanceInventory vm = createVmInstance {
            name = "vm-no-ipam"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3_2.uuid]
        }
        VmNicInventory nic1 = vm.vmNics.get(0)

        VmNicParam param = new VmNicParam()
        param.l3NetworkUuid = l3_2.uuid
        param.ip6 = "2024:6:18:86:1::11"
        param.defaultNic = true
        param.driverType = nic1.driverType
        param.vmNicType = nic1.type

        expect(AssertionError) {
            cloneVmInstance {
                vmInstanceUuid = vm.uuid
                names = ["vm-ip6"]
                vmNicParams = [param]
            }
        }

        param.prefix6 = "80"
        CloneVmInstanceResult cloneResult = cloneVmInstance {
            vmInstanceUuid = vm.uuid
            names = ["vm-ip6"]
            vmNicParams = [param]
        }
        CloneVmInstanceInventory vmIp6 = cloneResult.result.inventories.get(0)

        param = new VmNicParam()
        param.l3NetworkUuid = l3_2.uuid
        param.ip = "************"
        param.ip6 = "2024:6:18:86:1::12"
        param.prefix6 = "80"
        param.defaultNic = true
        param.driverType = nic1.driverType
        param.vmNicType = nic1.type

        expect(AssertionError) {
            cloneVmInstance {
                vmInstanceUuid = vm.uuid
                names = ["vm-ip46"]
                vmNicParams = [param]
            }
        }

        param.netmask = "*************"
        cloneResult = cloneVmInstance {
            vmInstanceUuid = vm.uuid
            names = ["vm-ip46"]
            vmNicParams = [param]
        }
        CloneVmInstanceInventory vmIp46 = cloneResult.result.inventories.get(0)

        param = new VmNicParam()
        param.l3NetworkUuid = l3_2.uuid
        param.ip = "************"
        param.defaultNic = true
        param.driverType = nic1.driverType
        param.vmNicType = nic1.type

        expect(AssertionError) {
            cloneVmInstance {
                vmInstanceUuid = vm.uuid
                names = ["vm-ip46"]
                vmNicParams = [param]
            }
        }

        param.netmask = "*************"
        cloneResult = cloneVmInstance {
            vmInstanceUuid = vm.uuid
            names = ["vm-ip46"]
            vmNicParams = [param]
        }
        CloneVmInstanceInventory vmIp4 = cloneResult.result.inventories.get(0)

        FlatDhcpBackend.BatchPrepareDhcpCmd cmd
        env.afterSimulator(FlatDhcpBackend.BATCH_PREPARE_DHCP_PATH) { rsp, HttpEntity<String> e1 ->
            cmd = JSONObjectUtil.toObject(e1.body, FlatDhcpBackend.BatchPrepareDhcpCmd.class)
            assert null != cmd
            return rsp
        }

        attachNetworkServiceToL3Network {
            l3NetworkUuid = l3_2.uuid
            networkServices = ['Flat':['DHCP', 'Userdata']]
        }
        /* because there is ipv4 gateway, or ipv6 ,range no dhcp apply */
        assert cmd == null

        FlatUserdataBackend.ApplyUserdataCmd userdataCmd = null
        env.afterSimulator(FlatUserdataBackend.APPLY_USER_DATA) { rsp, HttpEntity<String> e1 ->
            userdataCmd = JSONObjectUtil.toObject(e1.body, FlatUserdataBackend.ApplyUserdataCmd.class)
            return rsp
        }

        rebootVmInstance {
            uuid = vmIp46.inventory.uuid
        }
        assert userdataCmd != null
        assert userdataCmd.userdata.netmask == "*************"
    }

    @Override
    void clean() {
        env.delete()
    }
}
