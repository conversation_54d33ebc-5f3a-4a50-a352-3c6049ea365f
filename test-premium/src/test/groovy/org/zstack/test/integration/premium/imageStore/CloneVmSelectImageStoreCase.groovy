package org.zstack.test.integration.premium.imageStore

import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.image.ImageBackupStorageRefVO
import org.zstack.header.image.ImageBackupStorageRefVO_
import org.zstack.header.image.ImageVO
import org.zstack.header.storage.backup.BackupStorageStateEvent
import org.zstack.header.volume.VolumeVO
import org.zstack.mevoco.MevocoGlobalConfig
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit

/**
 * Created by Qi Le on 2020/10/20
 */
class CloneVmSelectImageStoreCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf
    VmInstanceInventory vm
    BackupStorageInventory bs1, bs2

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = ImageStoreEnv.twoImageStoreTwoClusterAndTwoLocalPS()
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            MevocoGlobalConfig.DELETE_TEMP_IMAGES.updateValue(false)
            bs1 = env.inventoryByName("imagestore-1") as BackupStorageInventory
            bs2 = env.inventoryByName("imagestore-2") as BackupStorageInventory
            prepare()
            testDisableOneBsCloneVm()
            mockVmImageLargerThanBs1()
            testCloneVm()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    void prepare() {
        ImageInventory img = env.inventoryByName("image")
        ImageVO image = dbf.findByUuid(img.uuid, ImageVO.class)
        image.actualSize = SizeUnit.GIGABYTE.toByte(12)
        image.size = SizeUnit.GIGABYTE.toByte(12)
        dbf.update(image)

        InstanceOfferingInventory ins = env.inventoryByName("instanceOffering")
        L3NetworkInventory l3 = env.inventoryByName("l3")
        ClusterInventory cluster = env.inventoryByName("cluster-1")
        DiskOfferingInventory diskof = env.inventoryByName("diskOffering")
        vm = createVmInstance {
            name = "vm"
            instanceOfferingUuid = ins.uuid
            imageUuid = img.uuid
            l3NetworkUuids = [l3.uuid]
            clusterUuid = cluster.uuid
            dataDiskOfferingUuids = [diskof.uuid]
        } as VmInstanceInventory
    }

    void testDisableOneBsCloneVm() {
        cloneVmInstance {
            vmInstanceUuid = vm.uuid
            names = ["cvm-1"]
        }

        assert 2 == Q.New(ImageBackupStorageRefVO.class).eq(ImageBackupStorageRefVO_.backupStorageUuid, bs1.uuid).count()

        changeBackupStorageState {
            uuid = bs1.uuid
            stateEvent = BackupStorageStateEvent.disable.toString()
        }

        cloneVmInstance {
            vmInstanceUuid = vm.uuid
            names = ["cvm-1"]
        }

        // image is not in bs1 which origin image located, so it will be created in bs2
        assert 1 == Q.New(ImageBackupStorageRefVO.class).eq(ImageBackupStorageRefVO_.backupStorageUuid, bs2.uuid).count()
    }

    void mockVmImageLargerThanBs1() {
        String diskUuid = vm.allVolumes.stream().find({ vol -> vol.uuid != vm.rootVolumeUuid }).uuid
        VolumeVO disk = dbf.findByUuid(diskUuid, VolumeVO.class)
        disk.actualSize = SizeUnit.GIGABYTE.toByte(20)
        dbf.update(disk)
    }

    void testCloneVm() {
        cloneVmInstance {
            vmInstanceUuid = vm.uuid
            names = ["cvm-1"]
        }
        assert 2 == Q.New(ImageBackupStorageRefVO.class).eq(ImageBackupStorageRefVO_.backupStorageUuid, bs2.uuid).count()

        cloneVmInstance {
            vmInstanceUuid = vm.uuid
            names = ["cvm-2"]
            full = true
        }
        assert 4 == Q.New(ImageBackupStorageRefVO.class).eq(ImageBackupStorageRefVO_.backupStorageUuid, bs2.uuid).count()
    }
}
