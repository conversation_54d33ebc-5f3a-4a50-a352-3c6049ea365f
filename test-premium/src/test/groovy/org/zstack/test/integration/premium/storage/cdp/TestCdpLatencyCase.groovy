package org.zstack.test.integration.premium.storage.cdp

import com.google.gson.Gson
import org.springframework.http.HttpEntity
import org.zstack.core.Platform
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.cloudbus.CloudBusCallBack
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.agent.AgentResponse
import org.zstack.header.message.MessageReply
import org.zstack.header.storage.cdp.APICreateVmFromCdpBackupMsg
import org.zstack.header.storage.cdp.APIRevertVmFromCdpBackupMsg
import org.zstack.header.storage.cdp.CdpBackupStorageConstant
import org.zstack.header.storage.cdp.CdpPolicyEO
import org.zstack.header.storage.cdp.CdpTaskResourceRefVO
import org.zstack.header.storage.cdp.CdpTaskVO
import org.zstack.header.storage.cdp.CdpTaskVO_
import org.zstack.header.storage.cdp.CdpVolumeHistoryVO
import org.zstack.header.storage.cdp.EnableCdpTaskMsg
import org.zstack.header.storage.cdp.EnableCdpTaskReply
import org.zstack.header.storage.cdp.MergeDataOnBackupStorageMsg
import org.zstack.header.storage.cdp.SyncCdpTaskMirrorLatencyOnHostMsg
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.license.LicenseInfo
import org.zstack.license.LicenseType
import org.zstack.header.longjob.LongJobVO
import org.zstack.sdk.*
import org.zstack.sdk.zwatch.alarm.ActionParam
import org.zstack.sdk.zwatch.datatype.Label
import org.zstack.sdk.zwatch.datatype.Operator
import org.zstack.storage.backup.BackupStorageGlobalConfig
import org.zstack.storage.backup.VolumeMetaDataMaker
import org.zstack.storage.cdp.CdpBackupKvmCommands
import org.zstack.storage.cdp.CdpBackupServerCommands
import org.zstack.storage.cdp.CdpGlobalConfig
import org.zstack.storage.primary.imagestore.ceph.CephPrimaryStorageImageStoreBackend
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.util.TProxy
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.zwatch.alarm.AlarmStatus
import org.zstack.zwatch.alarm.AlarmVO
import org.zstack.zwatch.alarm.AlarmVO_
import org.zstack.zwatch.alarm.sns.SNSActionFactory
import org.zstack.zwatch.datatype.Datapoint
import org.zstack.zwatch.datatype.EmergencyLevel
import org.zstack.zwatch.datatype.MetricQueryObject
import org.zstack.zwatch.migratedb.EventRecordsVO
import org.zstack.zwatch.migratedb.EventRecordsVO_
import org.zstack.zwatch.namespace.CdpTaskNamespace
import org.zstack.zwatch.prometheus.PrometheusDatabaseDriver
import org.zstack.zwatch.ruleengine.ComparisonOperator

import javax.persistence.metamodel.SingularAttribute
import java.nio.charset.StandardCharsets
import java.time.OffsetDateTime

class TestCdpLatencyCase extends PremiumSubCase {
    EnvPremiumSpec env
    VmInstanceInventory vm
    VmInstanceInventory vm1
    VmInstanceInventory vm2
    VmInstanceInventory vm3
    CdpTaskNamespace ns
    CdpPolicyInventory policyInventory
    CdpTaskInventory taskInventory
    CdpTaskInventory taskInventory1
    CdpTaskInventory taskInventory2
    CdpTaskInventory taskInventory3
    L3NetworkInventory l3Network
    InstanceOfferingInventory instanceOffering
    String cdpTaskUuid = Platform.uuid
    ImageStoreBackupStorageInventory backupStorageInventory
    List<Datapoint> currentDatapoints = []
    String BASE_URL = "/test-http-endpoint"
    PrometheusDatabaseDriver driver
    def bandwidth = 100
    Gson gson
    CloudBus bus
    HostInventory host

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        ns = bean(CdpTaskNamespace.class)
        bus = bean(CloudBus.class)
        driver = bean(PrometheusDatabaseDriver.class)

        currentDatapoints = [
                new Datapoint(value:350000, labels: [(CdpTaskNamespace.EventLabelNames.CdpTaskUuid.toString()):cdpTaskUuid]),
                new Datapoint(value:500000, labels: [(CdpTaskNamespace.EventLabelNames.CdpTaskUuid.toString()):cdpTaskUuid]),
        ]

        TProxy proxy = new TProxy(driver).mockMethod("query") {
            MetricQueryObject queryObject ->
                return currentDatapoints
        }

        def p = proxy.protect(ns, "driver")
        onCleanExecute {
            p.recover()
        }
        ns.driver = proxy as PrometheusDatabaseDriver

        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            imageStore {
                name = "bs"
                username = "root"
                password = "password"
                hostname = "hostname1"
                url = "/data"

                image {
                    name = "imagestore-image"
                    url = "http://zstack.org/download/test.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cephPrimaryStorage {
                    name = "ceph-1"
                    description = "Test"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(100)
                    fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                    monUrls = ["root:password@localhost/?monPort=7777"]
                }

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("ceph-1")
                    attachL2Network("l2")
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "************"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                diskOffering {
                    name = "diskOffering"
                    diskSize = SizeUnit.GIGABYTE.toByte(1)
                }

                attachBackupStorage("bs")
            }

            vm {
                name = "vm"
                systemTags = ["bootMode::UEFI", "vmMachineType::q35"]
                useInstanceOffering("instanceOffering")
                useImage("imagestore-image")
                useL3Networks("pubL3")
            }

            vm {
                name = "vm1"
                systemTags = ["bootMode::UEFI", "vmMachineType::q35"]
                useInstanceOffering("instanceOffering")
                useImage("imagestore-image")
                useL3Networks("pubL3")
            }

            vm {
                name = "vm2"
                systemTags = ["bootMode::UEFI", "vmMachineType::q35"]
                useInstanceOffering("instanceOffering")
                useImage("imagestore-image")
                useL3Networks("pubL3")
            }

            vm {
                name = "vm3"
                systemTags = ["bootMode::UEFI", "vmMachineType::q35"]
                useInstanceOffering("instanceOffering")
                useImage("imagestore-image")
                useL3Networks("pubL3")
            }

            sns {
                topic {
                    name = "topic"
                }

                httpEndpoint {
                    name = "http"
                    url = "http://127.0.0.1:8989${BASE_URL}"

                    subscribe("topic")
                }
            }

            zwatch {
                event {
                    namespace = ns.getName()
                    eventName = CdpTaskNamespace.CdpTaskStatusAbnormallyChanged.name
                    emergencyLevel = EmergencyLevel.Emergent.name()

                    useTopic("topic")
                }
            }
        }
    }

    @Override
    void test() {
        env.create {
            vm = env.inventoryByName("vm") as VmInstanceInventory
            vm1 = env.inventoryByName("vm1") as VmInstanceInventory
            vm2 = env.inventoryByName("vm2") as VmInstanceInventory
            vm3 = env.inventoryByName("vm3") as VmInstanceInventory
            l3Network = env.inventoryByName("pubL3") as L3NetworkInventory
            backupStorageInventory = env.inventoryByName("bs") as ImageStoreBackupStorageInventory
            instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
            host = env.inventoryByName("kvm1") as HostInventory

            CreateCdpTask()
            createCdpTaskCheckDefaults()
            testCreateCdpTaskMaxLatencyAlarm()
            testAlarmWhenTaskOverMaxLatency()
            testUpdateCdpTaskLatency()
            testLastLatencyWhenTaskWithBorderlineMaxLatency()
            testCheckLatencyWhenMergingData()
            testEnableCdpTaskWhenStopBecauseOverLatency()
            testAutoEnableCdpTaskWhenExceedsMaxAbnormallyEnableCount()
            testCreateVmFromCdpBackup()
            testCdpStatusWhenMergingData()
            testCdpStatusChangedWhenDataMerging()
            testCdpStatusWhenVmResume()
        }
    }

    void CreateCdpTask() {
        long allowedVmNum = 5

        licenseAddOns.clear()
        LicenseInfo cdp = new LicenseInfo()
        cdp.uuid = Platform.getUuid()
        cdp.user = "zstack"
        cdp.product = "cdp"
        cdp.vmNum = allowedVmNum
        cdp.issueTime = OffsetDateTime.now()
        cdp.licenseType = LicenseType.AddOn
        cdp.expireTime = OffsetDateTime.now().plusDays(1000)
        cdp.init()
        licenseAddOns.add(cdp)

        reloadLicense {
        }

        expectError {
            policyInventory = createCdpPolicy {
                name = "MyCdpPolicyName"
                description = "MyCdpPolicyDescription"
                retentionTimePerDay = 30
                recoveryPointPerSecond = 1
                dailyRpSinceDay = 30
            } as CdpPolicyInventory
        }

        // add create CDP policy
        policyInventory = createCdpPolicy {
            name = "MyCdpPolicyName"
            description = "MyCdpPolicyDescription"
            retentionTimePerDay = 30
            recoveryPointPerSecond = 1
        } as CdpPolicyInventory

        // test create CDP task
        taskInventory = createCdpTask {
            name = "My Task"
            taskType = "VM"
            policyUuid = policyInventory.uuid
            backupStorageUuid = backupStorageInventory.uuid
            resourceUuids = [vm.uuid]
            backupBandwidth = bandwidth
            maxCapacity = 10000000000
            maxLatency = 600000
        } as CdpTaskInventory
    }

    void createCdpTaskCheckDefaults() {
        // test create CDP task
        taskInventory3 = createCdpTask {
            name = "My Task Check Defaults"
            taskType = "VM"
            policyUuid = policyInventory.uuid
            backupStorageUuid = backupStorageInventory.uuid
            resourceUuids = [vm3.uuid]
            backupBandwidth = bandwidth
            maxCapacity = 1000
        } as CdpTaskInventory
        assert taskInventory3.maxLatency == 600000
    }

    void testCreateCdpTaskMaxLatencyAlarm() {
        //test create
        createAlarm {
            name = "test-max-latency"
            comparisonOperator = ComparisonOperator.GreaterThan.toString()
            period = 2
            namespace = ns.getName()
            metricName = ns.CdpTaskLatency.name
            threshold = 300
            labels = [new Label(key:CdpTaskNamespace.EventLabelNames.CdpTaskUuid.toString(), op:
                    Operator.Regex.toString(), value:String.format("%s", taskInventory.uuid))]
            actions = [new ActionParam(actionType:SNSActionFactory.type.toString(), actionUuid:
                    env.inventoryByName("topic").uuid)]
        }

        sleep(3000)
        retryInSecs (8){
            assert Q.New(AlarmVO.class)
                    .eq(AlarmVO_.metricName, ns.CdpTaskLatency.name)
                    .eq(AlarmVO_.status, AlarmStatus.Alarm)
                    .count() >= 1
        }
    }

    void testAlarmWhenTaskOverMaxLatency() {
        def count = 0

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.CDP_TASK_EXTERNAL_SNAPSHOT_INTERVAL.name
            value = "1500"
        }

        env.simulator(BASE_URL) {
            HttpEntity<String> e ->
                logger.debug("received an event ${e.body}")
                count++
        }

        env.simulator(CdpBackupServerCommands.REST_API_ADD_VOLUMES_TO_NBD_SERVER_PATH) {
            String volumeUuid = "265f04d5569141b9833869951714525c"
            Map<String, String> volumeMap = new IdentityHashMap<String, String>()
            volumeMap.put(volumeUuid, "nbd-" + volumeUuid)

            CdpBackupServerCommands.AddVolumesToStartNbdServerRsp rsp = new CdpBackupServerCommands.AddVolumesToStartNbdServerRsp()
            rsp.nbdPort = 8172
            rsp.nbdVols = volumeMap
            rsp.success = true
            return rsp
        }

        Integer externalSnapshotInterval = 0
        Integer backupBandwidth = 0
        env.afterSimulator(CdpBackupServerCommands.REST_API_RUN_CDP_TASK_WITH_POLICY_PATH) { rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, CdpBackupServerCommands.RunCdpTaskWithPolicyCmd.class)
            externalSnapshotInterval = cmd.externalSnapshotInterval
            backupBandwidth = cmd.backupBandwidth
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_TAKE_VOLUME_MIRROR_PATH) {
            def rsp = new AgentResponse()
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (90000000), "device2": (300000), "device4": (120000000)]
            def vmMaxLatencyMap = [(vm.uuid): maxMap]
            def minMap = ["device1": (10000000), "device2": (100000), "device4": (100000)]
            def vmMinLatencyMap = [(vm.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        enableCdpTask {
            uuid = taskInventory.uuid
        } as CdpTaskInventory

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "2"
        }

        CdpTaskInventory inv2 = queryCdpTask {
            conditions = ["uuid=${taskInventory.uuid}"]
        }[0] as CdpTaskInventory
        assert inv2.lastLatency != 120000000
        assert inv2.status != CdpTaskStatus.Stopped
        assert inv2.state == CdpTaskState.Enabled

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "1"
        }

        retryInSecs {
            assert externalSnapshotInterval == 1500
            assert backupBandwidth == Q.New(CdpTaskVO.class).eq(CdpTaskVO_.uuid, taskInventory.uuid).select(CdpTaskVO_.backupBandwidth).findValue()
            inv2 = queryCdpTask {
                conditions = ["uuid=${taskInventory.uuid}"]
            }[0] as CdpTaskInventory
            assert inv2.lastLatency == 120000000
            assert inv2.status == CdpTaskStatus.Stopped
            assert inv2.state == CdpTaskState.Enabled
        }

        retryInSecs {
            List<EventRecordsVO> eventsVO = Q.New(EventRecordsVO.class)
                    .eq(EventRecordsVO_.name as SingularAttribute, CdpTaskNamespace.CdpTaskStatusAbnormallyChanged.name).list()
            assert eventsVO.size() > 0
            assert eventsVO.get(0).getLabels() != null
            assert count >= 1
        }

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()
    }

    void EnableCdpTask(){
        taskInventory1 = createCdpTask {
            name = "My Task1"
            taskType = "VM"
            policyUuid = policyInventory.uuid
            backupStorageUuid = backupStorageInventory.uuid
            resourceUuids = [vm1.uuid]
            backupBandwidth = bandwidth
            maxCapacity = 1000
            maxLatency = 30000
        } as CdpTaskInventory

        taskInventory2 = createCdpTask {
            name = "My Task2"
            taskType = "VM"
            policyUuid = policyInventory.uuid
            backupStorageUuid = backupStorageInventory.uuid
            resourceUuids = [vm2.uuid]
            backupBandwidth = bandwidth
            maxCapacity = 1000
            maxLatency = 10000
        } as CdpTaskInventory

        env.simulator(CdpBackupServerCommands.REST_API_ADD_VOLUMES_TO_NBD_SERVER_PATH) {
            String volumeUuid = "265f04d5569141b9833869951714525c"
            Map<String, String> volumeMap = new IdentityHashMap<String, String>()
            volumeMap.put(volumeUuid, "nbd-" + volumeUuid)

            CdpBackupServerCommands.AddVolumesToStartNbdServerRsp rsp = new CdpBackupServerCommands.AddVolumesToStartNbdServerRsp()
            rsp.nbdPort = 8172
            rsp.nbdVols = volumeMap
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (9300), "device2": (40000), "device4": (4000)]
            def minMap = ["device1": (9000), "device2": (3000), "device4": (30000)]
            def vmMaxLatencyMap = [(vm.uuid): minMap, (vm1.uuid): maxMap]
            def vmMinLatencyMap = [(vm.uuid): minMap, (vm1.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        enableCdpTask {
            uuid = taskInventory.uuid
        } as CdpTaskInventory

        enableCdpTask {
            uuid = taskInventory1.uuid
        } as CdpTaskInventory

        enableCdpTask {
            uuid = taskInventory2.uuid
        } as CdpTaskInventory

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()
    }

    void testUpdateCdpTaskLatency(){
        updateCdpTask {
            uuid = taskInventory.uuid
            maxLatency = 700000
        }

        CdpTaskInventory inv4 = queryCdpTask {
            conditions = ["uuid=${taskInventory.uuid}"]
        }[0] as CdpTaskInventory
        assert inv4.maxLatency == 700000
    }

    void testLastLatencyWhenTaskWithBorderlineMaxLatency() {
        EnableCdpTask()

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (800000), "device2": (300000), "device4": (12000)]
            def vmMaxLatencyMap = [(vm.uuid): maxMap]
            def minMap = ["device1": (600000), "device2": (1000), "device4": (10000)]
            def vmMinLatencyMap = [(vm.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "4"
        }

        retryInSecs (8){
            CdpTaskInventory inv = queryCdpTask {
                conditions = ["uuid=${taskInventory.uuid}"]
            }[0] as CdpTaskInventory
            assert inv.lastLatency == 600000
        }

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()
    }

    void testCheckLatencyWhenMergingData() {
        CdpTaskInventory inv = queryCdpTask {
            conditions = ["uuid=${taskInventory1.uuid}"]
        }[0] as CdpTaskInventory
        assert inv.status == CdpTaskStatus.Running

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (9300), "device2": (Long.MAX_VALUE), "device4": (4000)]
            def minMap = ["device1": (9000), "device2": (3000), "device4": (Long.MAX_VALUE)]
            def vmMaxLatencyMap = [(vm.uuid): maxMap]
            def vmMinLatencyMap = [(vm.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        def duringStopVm = true
        def stopVmCalled = false
        env.afterSimulator(CdpBackupServerCommands.REST_API_MERGE_DATA_PATH) {rsp, HttpEntity<String> e ->
            stopVmCalled = true
            while (duringStopVm) {
                sleep(1000)
            }
            return rsp
        }

        def mmsg = new MergeDataOnBackupStorageMsg()
        mmsg.backupStorageUuid = backupStorageInventory.uuid
        bus.makeTargetServiceIdByResourceUuid(mmsg, CdpBackupStorageConstant.SERVICE_ID, backupStorageInventory.uuid)
        bus.send(mmsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
            }
        })

        def smsg = new SyncCdpTaskMirrorLatencyOnHostMsg()
        smsg.hostUuid = host.uuid
        smsg.vmUuids = [vm.uuid]
        bus.makeTargetServiceIdByResourceUuid(smsg, CdpBackupStorageConstant.SERVICE_ID, host.uuid)
        bus.call(smsg)
        duringStopVm = false

        retryInSecs {
            assert stopVmCalled
        }
        sleep(2000)

        CdpTaskInventory inv2 = queryCdpTask {
            conditions = ["uuid=${taskInventory.uuid}"]
        }[0] as CdpTaskInventory
        assert inv2.status == CdpTaskStatus.Running

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()
    }

    void testEnableCdpTaskWhenStopBecauseOverLatency(){
        env.simulator(CdpBackupServerCommands.REST_API_ADD_VOLUMES_TO_NBD_SERVER_PATH) {
            String volumeUuid = "265f04d5569141b9833869951714525c"
            Map<String, String> volumeMap = new IdentityHashMap<String, String>()
            volumeMap.put(volumeUuid, "nbd-" + volumeUuid)

            CdpBackupServerCommands.AddVolumesToStartNbdServerRsp rsp = new CdpBackupServerCommands.AddVolumesToStartNbdServerRsp()
            rsp.nbdPort = 8172
            rsp.nbdVols = volumeMap
            rsp.success = true
            return rsp
        }

        def flag = false
        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_BLOCKJOB_STATUS) {
            def rsp = new AgentResponse()
            flag = true
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (9300), "device2": (Long.MAX_VALUE), "device4": (4000)]
            def minMap = ["device1": (9000), "device2": (3000), "device4": (Long.MAX_VALUE)]
            def vmMaxLatencyMap = [(vm.uuid): minMap]
            def vmMinLatencyMap = [(vm.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        enableCdpTask {
            uuid = taskInventory.uuid
        } as CdpTaskInventory

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "1"
        }

        CdpTaskInventory inv = queryCdpTask {
            conditions = ["uuid=${taskInventory.uuid}"]
        }[0] as CdpTaskInventory
        assert !flag
        assert inv.status != CdpTaskStatus.Stopped

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "3"
        }

        retryInSecs (8){
            inv = queryCdpTask {
                conditions = ["uuid=${taskInventory.uuid}"]
            }[0] as CdpTaskInventory
            assert flag
            assert inv.status == CdpTaskStatus.Stopped
            assert inv.state == CdpTaskState.Enabled
        }

        flag = false
        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "2"
        }

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.MONITOR_INTERVAL.name
            value = "1"
        }

        retryInSecs{
            assert !flag
        }

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()
    }

    void testAutoEnableCdpTaskWhenExceedsMaxAbnormallyEnableCount(){
        env.simulator(CdpBackupServerCommands.REST_API_PREPARE_CDP_PATH) {
            def rsp = new AgentResponse()
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupServerCommands.REST_API_ADD_VOLUMES_TO_NBD_SERVER_PATH) {
            String volumeUuid = "265f04d5569141b9833869951714525c"
            Map<String, String> volumeMap = new IdentityHashMap<String, String>()
            volumeMap.put(volumeUuid, "nbd-" + volumeUuid)

            CdpBackupServerCommands.AddVolumesToStartNbdServerRsp rsp = new CdpBackupServerCommands.AddVolumesToStartNbdServerRsp()
            rsp.nbdPort = 8172
            rsp.nbdVols = volumeMap
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (9300), "device2": (800000), "device4": (4000)]
            def minMap = ["device1": (9000), "device2": (3000), "device4": (850000)]
            def vmMaxLatencyMap = [(vm.uuid): minMap, (vm1.uuid): maxMap]
            def vmMinLatencyMap = [(vm.uuid): minMap, (vm1.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        enableCdpTask {
            uuid = taskInventory.uuid
        } as CdpTaskInventory

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "5"
        }

        retryInSecs (8){
            CdpTaskInventory inv2 = queryCdpTask {
                conditions = ["uuid=${taskInventory.uuid}"]
            }[0] as CdpTaskInventory
            assert inv2.status == CdpTaskStatus.Stopped
            assert inv2.state == CdpTaskState.Enabled
        }

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (800000), "device2": (300000), "device4": (12000)]
            def vmMaxLatencyMap = [(vm.uuid): maxMap]
            def minMap = ["device1": (1000000), "device2": (1000), "device4": (10000)]
            def vmMinLatencyMap = [(vm.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "10"
        }

        retryInSecs (8){
            CdpTaskInventory inv = queryCdpTask {
                conditions = ["uuid=${taskInventory.uuid}"]
            }[0] as CdpTaskInventory
            assert inv.lastLatency == 850000
            assert inv.status == CdpTaskStatus.Stopped
            assert inv.state == CdpTaskState.Enabled
        }

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()

        env.simulator(CdpBackupServerCommands.REST_API_PREPARE_CDP_PATH) {
            def rsp = new AgentResponse()
            rsp.success = false
            return rsp
        }

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.MONITOR_INTERVAL.name
            value = "3"
        }

        retryInSecs (8){
            CdpTaskInventory inv2 = queryCdpTask {
                conditions = ["uuid=${taskInventory.uuid}"]
            }[0] as CdpTaskInventory
            assert inv2.status == CdpTaskStatus.Failed
            assert inv2.state == CdpTaskState.Enabled
        }

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()

        def enableFlag = false
        def disableFlag = false
        env.simulator(CdpBackupKvmCommands.REST_API_STOP_VOLUME_MIRROR_PATH) {
            def rsp = new AgentResponse()
            disableFlag = true
            while (!enableFlag) {
                sleep(5)
            }
            rsp.success = true
            return rsp
        }

        Thread.start {
            disableCdpTask {
                uuid = taskInventory.uuid
            }
        }

        while (!disableFlag) {
            sleep(5)
        }

        Thread.start {
            enableCdpTask {
                uuid = taskInventory.uuid
            }
        }

        CdpTaskInventory inv = queryCdpTask {
            conditions = ["uuid=${taskInventory.uuid}"]
        }[0] as CdpTaskInventory
        assert inv.status != CdpTaskStatus.Running

        enableFlag = true
        env.cleanSimulatorHandlers()
    }

    void testCreateVmFromCdpBackup(){
        gson = new Gson()
        Integer nbdPort = 1122
        String exportName = "expt"

        updateCdpTask {
            uuid = taskInventory3.uuid
            maxLatency = 55550000
        }

        enableCdpTask {
            uuid = taskInventory3.uuid
        }

        List<String> existingVolumes = Q.New(VolumeVO.class).eq(VolumeVO_.vmInstanceUuid, vm.uuid).select(VolumeVO_.uuid).listValues()
        List<String> existingDataVolumes = existingVolumes.findAll { (it != vm.rootVolumeUuid) }
        String rootVolumeMetadata = Base64.getEncoder().encodeToString(
                VolumeMetaDataMaker.generateRootVolumeMetadata(vm.rootVolumeUuid, existingDataVolumes)
                        .getBytes(StandardCharsets.UTF_8))

        mergeDataOnBackupStorage {
            backupStorageUuid = backupStorageInventory.uuid
        }

        env.simulator(CdpBackupServerCommands.REST_API_EXPORT_RECOVERY_POINTS_PATH) {
            def rsp = new CdpBackupServerCommands.ExportVolumesRsp()
            rsp.nbdPort = nbdPort
            rsp.nbdVols = [:]
            rsp.nbdSizes = [:]
            List<Tuple> t = Q.New(VolumeVO.class).eq(VolumeVO_.vmInstanceUuid, vm.uuid)
                    .select(VolumeVO_.uuid, VolumeVO_.size)
                    .listTuple()
            t.eachWithIndex { item, idx ->
                rsp.nbdSizes.put(item.get(0, String.class), item.get(1, Long.class) > 1 ? item.get(1, Long.class) - 1 : 1000)
                rsp.nbdVols.put(item.get(0, String.class), exportName + idx)
            }
            if (rsp.metadata == null) rsp.metadata = new HashMap<>()
            rsp.metadata.put(vm.rootVolumeUuid, rootVolumeMetadata)
            rsp.success = true
            return rsp
        }

        def diskOffering = env.inventoryByName("diskOffering")
        def data1 = createDataVolume {
            name = "data1"
            diskOfferingUuid = diskOffering.uuid
        }

        attachDataVolumeToVm {
            vmInstanceUuid = vm.uuid
            delegate.volumeUuid = data1.uuid
        }

        env.afterSimulator(CephPrimaryStorageImageStoreBackend.RESIZE_VOLUME_PATH) { CephPrimaryStorageImageStoreBackend.ResizeVolumeRsp rsp, HttpEntity<String> e ->
            rsp.setSize(SizeUnit.GIGABYTE.toByte(2))
            return rsp
        }

        def msg = new APICreateVmFromCdpBackupMsg()
        msg.setName("from-cdp-backup")
        msg.setGroupId(1)
        msg.setCdpTaskUuid(taskInventory.uuid)
        msg.setInstanceOfferingUuid(instanceOffering.uuid)
        msg.setL3NetworkUuids([ l3Network.uuid ])
        msg.setSystemTags(null)
        def job = submitLongJob {
            jobName = msg.getClass().getSimpleName()
            jobData = gson.toJson(msg)
            description = "create-vm-from-cdp-backup"
            sessionId = adminSession()
            apiId = Platform.getUuid()
        } as LongJobInventory

        assert job.getJobName() == msg.getClass().getSimpleName()
        assert job.state == org.zstack.sdk.LongJobState.Running

        retryInSecs() {
            def vo = dbFindByUuid(job.getUuid(), LongJobVO.class)
            assert vo.state.toString() == org.zstack.header.longjob.LongJobState.Succeeded.toString()
        }

        stopVmInstance {
            uuid = vm.uuid
        }

        msg = new APIRevertVmFromCdpBackupMsg()
        msg.setVmInstanceUuid(vm.uuid)
        msg.setBackupStorageUuid(backupStorageInventory.uuid)
        msg.setGroupId(1)
        msg.setUseExistingVolume(false)

        job = submitLongJob {
            jobName = msg.getClass().getSimpleName()
            jobData = gson.toJson(msg)
            description = "revert-vm-from-cdp-backup-recover-volume"
            sessionId = adminSession()
            apiId = Platform.getUuid()
        } as LongJobInventory

        assert job.getJobName() == msg.getClass().getSimpleName()
        assert job.state == org.zstack.sdk.LongJobState.Running

        retryInSecs() {
            def vo = dbFindByUuid(job.getUuid(), LongJobVO.class)
            assert vo.state.toString() == org.zstack.header.longjob.LongJobState.Succeeded.toString()
        }

        destroyVmInstance {
            uuid = vm.uuid
        }

        expungeVmInstance {
            uuid = vm.uuid
        }

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()
    }

    void testCdpStatusWhenMergingData(){
        disableCdpTask {
            uuid = taskInventory2.uuid
        }

        env.simulator(CdpBackupServerCommands.REST_API_PREPARE_CDP_PATH) {
            def rsp = new AgentResponse()
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupServerCommands.REST_API_ADD_VOLUMES_TO_NBD_SERVER_PATH) {
            String volumeUuid = "265f04d5569141b9833869951714525c"
            Map<String, String> volumeMap = new IdentityHashMap<String, String>()
            volumeMap.put(volumeUuid, "nbd-" + volumeUuid)

            CdpBackupServerCommands.AddVolumesToStartNbdServerRsp rsp = new CdpBackupServerCommands.AddVolumesToStartNbdServerRsp()
            rsp.nbdPort = 8172
            rsp.nbdVols = volumeMap
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupServerCommands.REST_API_RUN_CDP_TASK_WITH_POLICY_PATH) {
            def rsp = new AgentResponse()
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_TAKE_VOLUME_MIRROR_PATH) {
            def rsp = new AgentResponse()
            rsp.success = true
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (9300), "device2": (40000), "device4": (4000)]
            def minMap = ["device1": (9000), "device2": (3000), "device4": (30000)]
            def vmMaxLatencyMap = [(vm.uuid): minMap, (vm1.uuid): maxMap]
            def vmMinLatencyMap = [(vm.uuid): minMap, (vm1.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        env.afterSimulator(CdpBackupServerCommands.REST_API_GET_CDP_STORAGE_USAGE_PATH) { rsp, HttpEntity<String> e ->
            CdpBackupServerCommands.GetCdpStorageCmd cmd = JSONObjectUtil.toObject(e.body, CdpBackupServerCommands.GetCdpStorageCmd.class)
            rsp.cdpStatus = "running"
            rsp.storageUsage = 1

            if (cmd.vmUuid == vm2.uuid) {
                rsp.cdpStatus = "dataMerging"
            }
            return rsp
        }

        expect(AssertionError.class) {
            enableCdpTask {
                uuid = taskInventory2.uuid
            } as CdpTaskInventory
        }

        assert Q.New(CdpTaskVO.class).eq(CdpTaskVO_.uuid, taskInventory2.uuid).eq(CdpTaskVO_.status, org.zstack.header.storage.cdp.CdpTaskStatus.DataMerging).isExists()
        //'My Task' is stopped because the vm is deleted
        def stopTaskCount = Q.New(CdpTaskVO.class).eq(CdpTaskVO_.status, org.zstack.header.storage.cdp.CdpTaskStatus.Stopped).count()

        env.cleanAfterSimulatorHandlers()
        env.cleanSimulatorHandlers()

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_MIRROR_LATENCY_BOUNDARY) {
            def rsp = new CdpBackupKvmCommands.QueryMirrorLatencyBoundaryResponse()
            def maxMap = ["device1": (800000), "device2": (300000), "device4": (12000)]
            def vmMaxLatencyMap = [(vm3.uuid): maxMap]
            def minMap = ["device1": (1000000), "device2": (1000), "device4": (10000)]
            def vmMinLatencyMap = [(vm3.uuid): minMap]
            rsp.vmCurrentMaxCdpLatencyInfos = vmMaxLatencyMap
            rsp.vmCurrentMinCdpLatencyInfos = vmMinLatencyMap
            rsp.success = true
            return rsp
        }

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.QUERY_LATENCY_MONITOR_INTERVAL.name
            value = "8"
        }

        retryInSecs() {
            assert Q.New(CdpTaskVO.class).eq(CdpTaskVO_.status, org.zstack.header.storage.cdp.CdpTaskStatus.Stopped).count() == stopTaskCount
            CdpTaskInventory inv = queryCdpTask {
                conditions = ["uuid=${taskInventory3.uuid}"]
            }[0] as CdpTaskInventory
            assert inv.lastLatency == 800000
        }

        disableCdpTask {
            uuid = taskInventory3.uuid
        }

        enableCdpTask {
            uuid = taskInventory3.uuid
        }

        retryInSecs() {
            CdpTaskInventory inv = queryCdpTask {
                conditions = ["uuid=${taskInventory3.uuid}"]
            }[0] as CdpTaskInventory
            assert inv.lastLatency == 0
        }
    }

    void testCdpStatusChangedWhenDataMerging() {
        env.cleanAfterSimulatorHandlers()

        env.afterSimulator(CdpBackupServerCommands.REST_API_GET_CDP_STORAGE_USAGE_PATH) { rsp, HttpEntity<String> e ->
            CdpBackupServerCommands.GetCdpStorageCmd cmd = JSONObjectUtil.toObject(e.body, CdpBackupServerCommands.GetCdpStorageCmd.class)
            rsp.cdpStatus = "running"
            rsp.storageUsage = 1

            if (cmd.vmUuid == vm2.uuid) {
                rsp.cdpStatus = "stopped"
            }
            return rsp
        }

        env.simulator(CdpBackupKvmCommands.REST_API_QUERY_VOLUME_MIRROR_PATH) {
            def rsp = new CdpBackupKvmCommands.QueryVolumesMirrorResponse()
            rsp.mirrorVolumes = []
            rsp.success = true
            return rsp
        }

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.MONITOR_INTERVAL.name
            value = "10"
        }

        BackupStorageGlobalConfig.PING_INTERVAL.updateValue(1)

        retryInSecs {
            assert Q.New(CdpTaskVO.class).eq(CdpTaskVO_.uuid, taskInventory2.uuid).eq(CdpTaskVO_.status, org.zstack.header.storage.cdp.CdpTaskStatus.Running).isExists()
        }
    }

    void testCdpStatusWhenVmResume() {
        env.afterSimulator(CdpBackupServerCommands.REST_API_GET_CDP_STORAGE_USAGE_PATH) { rsp, HttpEntity<String> e ->
            CdpBackupServerCommands.GetCdpStorageCmd cmd = JSONObjectUtil.toObject(e.body, CdpBackupServerCommands.GetCdpStorageCmd.class)
            if (cmd.vmUuid == vm2.uuid) {
                rsp.cdpStatus = "stopped"
            }
            rsp.storageUsage = 10
            return rsp
        }

        enableCdpTask {
            uuid = taskInventory2.uuid
        } as CdpTaskInventory

        SQL.New(CdpTaskVO.class).set(CdpTaskVO_.status, org.zstack.header.storage.cdp.CdpTaskStatus.Stopped).eq(CdpTaskVO_.uuid, taskInventory2.uuid).update()

        pauseVmInstance {
            uuid = vm2.uuid
        }

        resumeVmInstance {
            uuid = vm2.uuid
        }

        retryInSecs {
            Q.New(CdpTaskVO.class).eq(CdpTaskVO_.uuid, taskInventory2.uuid).eq(CdpTaskVO_.status, org.zstack.header.storage.cdp.CdpTaskStatus.Running).isExists()
        }

        def msgTimeout
        env.message(MergeDataOnBackupStorageMsg.class) { MergeDataOnBackupStorageMsg msg, CloudBus bus ->
            msgTimeout = msg.getTimeout()
        }

        def mmsg = new MergeDataOnBackupStorageMsg()
        mmsg.backupStorageUuid = backupStorageInventory.uuid
        bus.makeTargetServiceIdByResourceUuid(mmsg, CdpBackupStorageConstant.SERVICE_ID, backupStorageInventory.uuid)
        bus.send(mmsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
            }
        })

        retryInSecs {
            assert msgTimeout == 259200000
        }

        env.message(EnableCdpTaskMsg.class) { EnableCdpTaskMsg msg, CloudBus bus ->
            def reply = new EnableCdpTaskReply()
            msgTimeout = msg.getTimeout()
            bus.reply(msg, reply)
        }

        updateGlobalConfig {
            category = CdpGlobalConfig.CATEGORY
            name = CdpGlobalConfig.MONITOR_INTERVAL.name
            value = "1"
        }

        retryInSecs (8){
            assert msgTimeout == 129600000
        }
    }

    @Override
    void clean() {
        SQL.New(CdpTaskResourceRefVO.class).hardDelete()
        SQL.New(CdpVolumeHistoryVO.class).hardDelete()
        SQL.New(LongJobVO.class).hardDelete()
        SQL.New(CdpTaskVO.class).hardDelete()
        SQL.New(CdpPolicyEO.class).hardDelete()
        SQL.New(AlarmVO.class).hardDelete()
        licenseAddOns.clear()
        env.delete()
    }
}
