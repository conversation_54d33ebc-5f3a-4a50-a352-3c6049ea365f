package org.zstack.test.integration.premium.kvm.vm.virtualizer

import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.SQL
import org.zstack.header.host.CpuArchitecture
import org.zstack.header.host.HostVO
import org.zstack.header.host.HostVO_
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.kvm.KVMHostVO
import org.zstack.kvm.KVMHostVO_
import org.zstack.kvm.hypervisor.HypervisorMetadataCollectorForTest
import org.zstack.kvm.hypervisor.KvmHypervisorInfoManager
import org.zstack.kvm.hypervisor.datatype.HostOsCategoryVO
import org.zstack.sdk.HostInventory
import org.zstack.sdk.HostOsCategoryInventory
import org.zstack.sdk.HypervisorVersionState
import org.zstack.sdk.KvmHostHypervisorMetadataInventory
import org.zstack.sdk.VirtualizerInfo
import org.zstack.sdk.VirtualizerInfoInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase

class VirtualizerInfoCase extends PremiumSubCase{
    EnvSpec env
    DatabaseFacade db
    HypervisorMetadataCollectorForTest collector
    KvmHypervisorInfoManager manager
    HostInventory kvm
    VmInstanceInventory vm

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        db = bean(DatabaseFacade.class)
        manager = bean(KvmHypervisorInfoManager.class)
        collector = bean(HypervisorMetadataCollectorForTest.class)
        env = PremiumEnv.oneHostOneVmTwoL3BasicEnv()
    }

    @Override
    void test() {
        env = env.create {
            prepare()
            testVirtualizerInfoWhenOsIsKylin()
            testGetVirtualizerInfoWhenVmIsStopped()
        }
    }

    void prepare() {
        kvm = env.inventoryByName("kvm") as HostInventory
        vm = env.inventoryByName("vm") as VmInstanceInventory
        SQL.New(HostOsCategoryVO.class).delete()
    }

    void testVirtualizerInfoWhenOsIsKylin() {
        def expectVersion = "4.1.0-33.p02.ky10"
        collector.setFolderScannerSimulator { path ->
            def d1 = new HypervisorMetadataCollectorForTest.HypervisorMetadataDefinitionForTest()
            d1.architecture = CpuArchitecture.aarch64.name()
            d1.osReleaseSimpleVersion = "ky10sp2"

            def d2 = new HypervisorMetadataCollectorForTest.HypervisorMetadataDefinitionForTest()
            d2.architecture = CpuArchitecture.aarch64.name()
            d2.osReleaseSimpleVersion = "ky10sp2"
            return [d1, d2]
        }
        def count = 0
        collector.setCollectMetadataSimulator { definition ->
            def distname = "kylin"
            def version = "10"
            def id = count++ == 0 ? "ZStack" : "Sword"

            return """
qemu-kvm.version: $expectVersion
platform.distname: $distname
platform.version: $version
platform.id: $id
""".toString()
        }
        manager.refreshMetadata()
        assert count == 2

        def categories = queryHostOsCategory {} as List<HostOsCategoryInventory>
        assert categories.size() == 1

        assert categories[0].architecture == CpuArchitecture.aarch64.name()
        assert categories[0].osReleaseVersion == "kylin 10"
        assert !categories[0].metadataList.empty
        assert (categories[0].metadataList[0] as KvmHostHypervisorMetadataInventory).version == expectVersion

        env.cleanSimulatorHandlers()

        env.afterSimulator(KVMConstant.KVM_HOST_FACT_PATH) { KVMAgentCommands.HostFactResponse rsp ->
            rsp.virtualizerInfo.version = expectVersion
            return rsp
        }
        env.afterSimulator(KVMConstant.GET_VIRTUALIZER_INFO_PATH) { KVMAgentCommands.GetVirtualizerInfoRsp rsp ->
            rsp.hostInfo.version = expectVersion
            rsp.vmInfoList.each { info -> info.version = "4.2.0-630.for.test" }
            return rsp
        }

        assert env.verifySimulator(KVMConstant.GET_VIRTUALIZER_INFO_PATH, 0)
        assert env.verifySimulator(KVMConstant.KVM_HOST_FACT_PATH, 0)
        reconnectHost {
            delegate.uuid = kvm.uuid
        }
        retryInSecs {
            assert env.verifySimulator(KVMConstant.KVM_HOST_FACT_PATH, 1)
            assert env.verifySimulator(KVMConstant.GET_VIRTUALIZER_INFO_PATH, 1)
        }

        SQL.New(KVMHostVO.class)
                .eq(KVMHostVO_.uuid, kvm.uuid)
                .set(KVMHostVO_.osDistribution, "kylin")
                .set(KVMHostVO_.osRelease, "ZStack")
                .set(KVMHostVO_.osVersion, "10")
                .update()
        SQL.New(HostVO.class)
                .eq(HostVO_.uuid, kvm.uuid)
                .set(HostVO_.architecture, CpuArchitecture.aarch64.toString())
                .update()

        def vInfo = getVirtualizerInfo {
            delegate.uuids = [kvm.uuid]
        } as List<VirtualizerInfoInventory>
        assert vInfo.size() == 1
        assert vInfo[0].infoList.size() == 1
        def vDetail = vInfo[0].infoList[0] as VirtualizerInfo
        assert vDetail.hypervisor == "qemu-kvm"
        assert vDetail.currentVersion == expectVersion
        assert vDetail.expectVersion == expectVersion
        assert vDetail.matchState == HypervisorVersionState.Matched

        vInfo = getVirtualizerInfo {
            delegate.uuids = [vm.uuid]
        } as List<VirtualizerInfoInventory>
        assert vInfo.size() == 1
        assert vInfo[0].infoList.size() == 1
        vDetail = vInfo[0].infoList[0] as VirtualizerInfo
        assert vDetail.hypervisor == "qemu-kvm"
        assert vDetail.currentVersion == "4.2.0-630.for.test"
        assert vDetail.expectVersion == expectVersion
        assert vDetail.matchState == HypervisorVersionState.Unmatched
    }

    void testGetVirtualizerInfoWhenVmIsStopped() {
        stopVmInstance {
            delegate.uuid = vm.uuid
        }

        def vInfo = getVirtualizerInfo {
            delegate.uuids = [vm.uuid]
        } as List<VirtualizerInfoInventory>
        assert vInfo.isEmpty()
    }

    @Override
    void clean() {
        env.delete()
    }
}