package org.zstack.test.integration.premium.vpc.networkService

import org.zstack.core.db.DatabaseFacade
import org.zstack.header.network.l3.L3NetworkConstant
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.ipsec.IPsecConstants
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.lb.LoadBalancerConstants
import org.zstack.network.service.virtualrouter.VirtualRouterSystemTags
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.LoadBalancerInventory
import org.zstack.sdk.LoadBalancerListenerInventory
import org.zstack.sdk.VipInventory
import org.zstack.sdk.VirtualRouterOfferingInventory
import org.zstack.sdk.VirtualRouterVmInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VmNicInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.TestPremium
import org.zstack.utils.data.SizeUnit
import org.zstack.header.vpc.VpcConstants
import org.zstack.vrouterRoute.VRouterRouteConstants

class VpcSeparateVrLbCase extends PremiumSubCase {

    EnvPremiumSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = TestPremium.makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3-1"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "************0"
                            endIp = "**************"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-2"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-3"
                        type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE.toString()

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "l3-4"
                        type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE.toString()

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     NetworkServiceType.Centralized_DNS.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                        }
                    }

                    l3Network {
                        name = "pubL3-1"
                        category = "Public"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr-offering"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3-1")
                    usePublicL3Network("pubL3-1")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }
        }
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            // wont support separate vr lb
//            testLoadBalancerInVpc()
//            testAddVmNicWithOutVrToLb()
//            testLbWithRecreateVpcVr()
        }
    }

    void testLoadBalancerInVpc() {
        def offering = env.inventoryByName("vr-offering") as VirtualRouterOfferingInventory
        def l3_1 = env.inventoryByName("l3-1") as L3NetworkInventory
        def l3_2 = env.inventoryByName("l3-2") as L3NetworkInventory
        def l3_3 = env.inventoryByName("l3-3") as L3NetworkInventory
        def l3_4 = env.inventoryByName("l3-4") as L3NetworkInventory
        def pubL3_1 = env.inventoryByName("pubL3-1") as L3NetworkInventory
        def instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        def image = env.inventoryByName("image1") as ImageInventory

        def vpcVr = createVpcVRouter {
            name = "vpc-vr-1"
            virtualRouterOfferingUuid = offering.uuid
        } as VirtualRouterVmInventory

        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        def vm1 = createVmInstance {
            name = "TestVm1"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3_1.uuid, l3_2.uuid, l3_3.uuid, l3_4.uuid]
            defaultL3NetworkUuid = l3_1.uuid
        } as VmInstanceInventory

        def vm2 = createVmInstance {
            name = "TestVm2"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3_1.uuid, l3_2.uuid, l3_3.uuid, l3_4.uuid]
            defaultL3NetworkUuid = l3_1.uuid
        } as VmInstanceInventory

        def vip1 = createVip {
            name = "TestVip1"
            l3NetworkUuid = pubL3_1.uuid
        } as VipInventory

        def lb1 = createLoadBalancer {
            name = "TestLoadBalancer1"
            vipUuid = vip1.uuid
            systemTags=["separateVirtualRouterVm"]
        } as LoadBalancerInventory

        def listener1 = createLoadBalancerListener {
            loadBalancerUuid = lb1.uuid
            name = "listener1"
            instancePort = 22
            loadBalancerPort = 22
            protocol = LoadBalancerConstants.LB_PROTOCOL_TCP
        } as LoadBalancerListenerInventory

        def listener2 = createLoadBalancerListener {
            loadBalancerUuid = lb1.uuid
            name = "listener2"
            instancePort = 222
            loadBalancerPort = 222
            protocol = LoadBalancerConstants.LB_PROTOCOL_TCP
        } as LoadBalancerListenerInventory

        def result1 = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert result1.size() == 6

        addVmNicToLoadBalancer {
            vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid]
            listenerUuid = listener1.uuid
        }

        def vrs = queryVirtualRouterVm {} as List<VirtualRouterVmInventory>
        def dedicatedVrs = new ArrayList<VirtualRouterVmInventory>()
        for (v in vrs) {
            if (VirtualRouterSystemTags.DEDICATED_ROLE_VR.hasTag(v.uuid)) {
                dedicatedVrs.add(v)
            }
        }
        assert dedicatedVrs.size() == 1
        assert dedicatedVrs[0].vmNics.size() == 2

        result1 = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        def result2 = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        def result3 = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert [result1.collect{ it.uuid }.toSet(),
                result2.collect{ it.uuid }.toSet(),
                result3.collect{ it.uuid }.toSet()].each { it == [vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid].toSet() }

        addVmNicToLoadBalancer {
            vmNicUuids = [vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid]
            listenerUuid = listener1.uuid
        }

        expect(AssertionError) {
            addVmNicToLoadBalancer {
                vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_3.uuid }.uuid]
                listenerUuid = listener1.uuid
            }
        }

        expect(AssertionError) {
            addVmNicToLoadBalancer {
                vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_2.uuid }.uuid]
                listenerUuid = listener2.uuid
            }
        }

        expect(AssertionError) {
            addVmNicToLoadBalancer {
                vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_4.uuid }.uuid]
                listenerUuid = listener1.uuid
            }
        }
    }

    void testAddVmNicWithOutVrToLb() {
        def l3_1 = env.inventoryByName("l3-1") as L3NetworkInventory
        def l3_2 = env.inventoryByName("l3-2") as L3NetworkInventory
        def vpcVr = queryVirtualRouterVm { conditions=["name=vpc-vr-1"] }[0] as VirtualRouterVmInventory
        def vm1 = queryVmInstance { conditions=["name=TestVm1"] }[0] as VmInstanceInventory
        def vm2 = queryVmInstance { conditions=["name=TestVm2"] }[0] as VmInstanceInventory
        def listener1 = queryLoadBalancerListener { conditions=["name=listener1"] }[0] as LoadBalancerListenerInventory

        destroyVmInstance { uuid=vpcVr.uuid }
        expect(AssertionError) {
            addVmNicToLoadBalancer {
                vmNicUuids = [vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_2.uuid }.uuid]
                listenerUuid = listener1.uuid
            }
        }

        def result = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert result.size() == 0

        removeVmNicFromLoadBalancer {
            vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid,
                          vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid]
            listenerUuid = listener1.uuid
        }
        result = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert result.size() == 2
    }

    def testLbWithRecreateVpcVr() {
        def offering = env.inventoryByName("vr-offering") as VirtualRouterOfferingInventory
        def l3_1 = env.inventoryByName("l3-1") as L3NetworkInventory
        def l3_2 = env.inventoryByName("l3-2") as L3NetworkInventory
        def l3_3 = env.inventoryByName("l3-3") as L3NetworkInventory
        def vm1 = queryVmInstance { conditions=["name=TestVm1"] }[0] as VmInstanceInventory
        def vm2 = queryVmInstance { conditions=["name=TestVm2"] }[0] as VmInstanceInventory
        def listener1 = queryLoadBalancerListener { conditions=["name=listener1"] }[0] as LoadBalancerListenerInventory

        def vpcVr = createVpcVRouter {
            name = "vpc-vr-1"
            virtualRouterOfferingUuid = offering.uuid
        } as VirtualRouterVmInventory

        def vrs = queryVirtualRouterVm {} as List<VirtualRouterVmInventory>
        def dedicatedVrs = new ArrayList<VirtualRouterVmInventory>()
        for (v in vrs) {
            if (VirtualRouterSystemTags.DEDICATED_ROLE_VR.hasTag(v.uuid)) {
                dedicatedVrs.add(v)
            }
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_2.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        def result = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert result.collect{ it.uuid }.toSet() == [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid,
                 vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid].toSet()

        expect(AssertionError) {
            addVmNicToLoadBalancer {
                vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_2.uuid }.uuid]
                listenerUuid = listener1.uuid
            }
        }

        destroyVmInstance { uuid = dedicatedVrs[0].uuid }

        result = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert result.collect{ it.uuid }.toSet() == [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid,
                 vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid].toSet()

        addVmNicToLoadBalancer {
            vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid]
            listenerUuid = listener1.uuid
        }

        result = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert result.collect{ it.uuid }.toSet() == [vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid].toSet()

        expect(AssertionError) {
            addVmNicToLoadBalancer {
                vmNicUuids = [vm1.vmNics.find{ nic -> nic.l3NetworkUuid == l3_3.uuid }.uuid]
                listenerUuid = listener1.uuid
            }
        }

        attachL3NetworkToVm {
            l3NetworkUuid = l3_1.uuid
            vmInstanceUuid = vpcVr.uuid
        }

        result = getCandidateVmNicsForLoadBalancer { listenerUuid = listener1.uuid } as List<VmNicInventory>
        assert result.collect{ it.uuid }.toSet() == [vm2.vmNics.find{ nic -> nic.l3NetworkUuid == l3_1.uuid }.uuid].toSet()
    }

    @Override
    void clean() {
        env.delete()
    }
}
