package org.zstack.test.integration.premium.kvm.vm.affinitygroup

import org.springframework.http.HttpEntity
import org.zstack.core.db.Q
import org.zstack.header.affinitygroup.AffinityGroupSystemTags
import org.zstack.header.affinitygroup.AffinityGroupUsageVO
import org.zstack.header.affinitygroup.AffinityGroupUsageVO_
import org.zstack.header.affinitygroup.AffinityGroupVO
import org.zstack.header.affinitygroup.AffinityGroupVO_
import org.zstack.header.image.ImageConstant
import org.zstack.header.vm.VmInstanceVO
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.HttpError
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

import static java.util.Arrays.asList

/**
 * Created by shixin on 2017/11/03.
 */
class TestAffinityGroupAPIFailCase extends PremiumSubCase {
    EnvSpec env

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(4)
                cpu = 1
            }
            diskOffering {
                name =  "diskOffering"
                diskSize =  SizeUnit.GIGABYTE.toByte(10)
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                    size = SizeUnit.GIGABYTE.toByte(10)
                }

                image {
                    name = "iso"
                    url  = "http://zstack.org/download/test.iso"
                    format = ImageConstant.ISO_FORMAT_STRING.toString()
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm"
                        managementIp = "localhost"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(32)
                        totalCpu = 8
                    }

                    kvm {
                        name = "kvm-1"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(32)
                        totalCpu = 8
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(100)
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                attachBackupStorage("sftp")
            }
        }
    }

    @Override
    void test() {
        env.create {
            TestCreateVmFailedWithAffinityGroup()
            TestCloneVmFailedWithAffinityGroup()
        }
    }

    void TestCreateVmFailedWithAffinityGroup() {
        L3NetworkInventory l3 = env.inventoryByName("l3") as L3NetworkInventory
        InstanceOfferingInventory offering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        ImageInventory image = env.inventoryByName("image1") as ImageInventory

        KVMAgentCommands.StartVmCmd cmd = null
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { KVMAgentCommands.StartVmResponse rsp, HttpEntity<String> e ->
            throw new HttpError(403, "on purpose")
        }

        def ag = createAffinityGroup {
            name = "test-affinity-group"
            policy = "antiHard"
        } as AffinityGroupInventory

        CreateVmInstanceAction action = new CreateVmInstanceAction()
        action.name = "test"
        action.l3NetworkUuids = [l3.uuid]
        action.instanceOfferingUuid = offering.uuid
        action.imageUuid = image.uuid
        action.systemTags = [String.format("%s::%s", AffinityGroupSystemTags.AFFINITY_GROUP_UUID_TOKEN, ag.uuid)]
        action.sessionId = adminSession()
        CreateVmInstanceAction.Result res = action.call()
        assert res.error != null
        assert Q.New(AffinityGroupUsageVO.class).eq(AffinityGroupUsageVO_.affinityGroupUuid, ag.uuid).count() == 0

        deleteAffinityGroup {
            uuid = ag.getUuid()
        }
        assert Q.New(AffinityGroupVO.class).eq(AffinityGroupVO_.uuid, ag.uuid).find() == null
    }

    void TestCloneVmFailedWithAffinityGroup() {
        L3NetworkInventory l3 = env.inventoryByName("l3") as L3NetworkInventory
        InstanceOfferingInventory offering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        ImageInventory image = env.inventoryByName("image1") as ImageInventory

        KVMAgentCommands.StartVmCmd cmd = null
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { KVMAgentCommands.StartVmResponse rsp, HttpEntity<String> e ->
            cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.StartVmCmd.class)
            return rsp
        }

        def vm = createVmInstance {
            name = "test"
            l3NetworkUuids = [l3.uuid]
            instanceOfferingUuid = offering.uuid
            imageUuid = image.uuid
            sessionId = adminSession()
        } as VmInstanceInventory

        def ag = createAffinityGroup {
            name = "test-affinity-group"
            policy = "antiHard"
        } as AffinityGroupInventory

        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { KVMAgentCommands.StartVmResponse rsp, HttpEntity<String> e ->
            throw new HttpError(403, "on purpose")
        }

        CloneVmInstanceAction action = new CloneVmInstanceAction()
        action.names = asList("clone-vm")
        action.vmInstanceUuid = vm.uuid
        action.sessionId = adminSession()
        CloneVmInstanceAction.Result res = action.call()
        assert res.error != null
        assert Q.New(AffinityGroupUsageVO.class).eq(AffinityGroupUsageVO_.affinityGroupUuid, ag.uuid).count() == 0

        deleteAffinityGroup {
            uuid = ag.getUuid()
        }
        assert Q.New(AffinityGroupVO.class).eq(AffinityGroupVO_.uuid, ag.uuid).find() == null
    }
}

