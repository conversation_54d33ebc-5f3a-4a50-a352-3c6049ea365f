package org.zstack.test.integration.premium.storage.volume

import org.springframework.http.HttpEntity
import org.zstack.compute.host.HostGlobalConfig
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.allocator.AllocateHostReply
import org.zstack.header.allocator.DesignatedAllocateHostMsg
import org.zstack.header.host.HostVO
import org.zstack.header.host.HostVO_
import org.zstack.header.image.ImageVO
import org.zstack.header.image.ImageVO_
import org.zstack.header.storage.backup.SyncImageSizeOnBackupStorageMsg
import org.zstack.header.storage.backup.SyncImageSizeOnBackupStorageReply
import org.zstack.header.storage.primary.PrimaryStorageHostRefVO
import org.zstack.header.storage.primary.PrimaryStorageHostRefVO_
import org.zstack.header.storageDevice.LunVO
import org.zstack.header.storageDevice.ScsiLunHostRefVO
import org.zstack.header.storageDevice.ScsiLunHostRefVO_
import org.zstack.header.storageDevice.ScsiLunVmInstanceRefVO
import org.zstack.header.storageDevice.ScsiLunVmInstanceRefVO_
import org.zstack.header.tag.SystemTagVO
import org.zstack.header.tag.SystemTagVO_
import org.zstack.header.vm.APICreateVmInstanceMsg
import org.zstack.header.vm.InstantiateNewCreatedVmInstanceMsg
import org.zstack.header.vm.InstantiateNewCreatedVmInstanceReply
import org.zstack.header.vm.StartVmInstanceMsg
import org.zstack.header.vm.StartVmInstanceReply
import org.zstack.header.vm.VmCreationStrategy
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmInstanceVO
import org.zstack.header.vm.VmInstanceVO_
import org.zstack.header.volume.CreateDataVolumeFromVolumeTemplateMsg
import org.zstack.header.volume.CreateDataVolumeFromVolumeTemplateReply
import org.zstack.header.volume.InstantiateVolumeMsg
import org.zstack.header.volume.InstantiateVolumeReply
import org.zstack.header.volume.VolumeConstant
import org.zstack.header.volume.VolumeStatus
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.kvm.KVMSystemTags
import org.zstack.resourceconfig.ResourceConfigVO
import org.zstack.resourceconfig.ResourceConfigVO_
import org.zstack.sdk.*
import org.zstack.storage.device.StorageDeviceKvmCommands
import org.zstack.storage.device.iscsi.IscsiLunStruct
import org.zstack.storage.device.iscsi.IscsiTargetStruct
import org.zstack.storage.primary.local.LocalStorageKvmBackend
import org.zstack.storage.primary.local.LocalStorageResourceRefVO
import org.zstack.storage.primary.local.LocalStorageResourceRefVO_
import org.zstack.storage.volume.VolumeSystemTags
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.ZoneSpec
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.SizeUtils
import org.zstack.utils.data.SizeUnit

import java.util.stream.Collectors

import static org.zstack.core.Platform.operr

class ShareableVolumeOnMultiPrimaryStorageCase extends PremiumSubCase {
    EnvSpec env
    VmInstanceInventory vmInv
    DiskOfferingInventory diskOfferingInventory
    VolumeInventory volumeInventory
    SharedBlockGroupPrimaryStorageInventory sbg
    PrimaryStorageInventory local
    L3NetworkInventory l3
    HostInventory host
    ImageInventory image
    BackupStorageInventory imageStore
    ZoneInventory zone
    ImageInventory dataTemplate
    ClusterInventory cluster2
    HostInventory hostOfCluster2
    PrimaryStorageInventory localPsOfCluster2

    @Override
    void setup() {

    }

    @Override
    void environment() {
        env = PremiumEnv.oneVmLocalStorageSharedblockEnv()
    }

    @Override
    void test() {
        env.create {
            vmInv = env.inventoryByName("vm")
            diskOfferingInventory = env.inventoryByName("diskOffering")
            sbg = env.inventoryByName("sharedblock-ps")
            local = env.inventoryByName("local") as PrimaryStorageInventory
            l3 = env.inventoryByName("pubL3") as L3NetworkInventory
            host = env.inventoryByName("kvm1") as HostInventory
            image = env.inventoryByName("image") as ImageInventory
            imageStore = env.inventoryByName("imagestore") as BackupStorageInventory
            zone = env.inventoryByName("zone") as ZoneInventory

            testAttachShareableVolumeToVM()
            testCreateVmWithDiskAOs()
            testCreateVmWithDiskAOsAndRollback()
            testCreateVmWithDiskAOAndValidateAPI()
            testCreateVmWithoutPS()
            testCreateVmWithSCSILun()
            testCreateVmWithoutConsideringImageVirtio()
            testCreateVmAndStartVm()
            testCreateVmWithoutImage()
            testValidatePsWhenCreateVmWithDiskAO()
            testValidateNeedToAttachVolumeWhenCreateVmWithDiskAO()
        }
    }

    void testAttachShareableVolumeToVM() {
        volumeInventory = createDataVolume {
            diskOfferingUuid = diskOfferingInventory.uuid
            name = "shareable"
            systemTags = [VolumeSystemTags.SHAREABLE.getTagFormat(), KVMSystemTags.VOLUME_VIRTIO_SCSI.getTagFormat()]
        }

        VolumeVO vo = dbFindByUuid(volumeInventory.uuid, VolumeVO.class)
        assert vo.isShareable()
        assert !vo.isAttached()

        VolumeInventory vol = attachDataVolumeToVm {
            volumeUuid = volumeInventory.uuid
            vmInstanceUuid = vmInv.uuid
        } as VolumeInventory

        assert vol.primaryStorageUuid == sbg.uuid
    }

    void testCreateVmWithDiskAOs() {
        boolean called = false
        env.message(SyncImageSizeOnBackupStorageMsg.class) {
            SyncImageSizeOnBackupStorageMsg msg, CloudBus bus ->
                SyncImageSizeOnBackupStorageReply reply = new SyncImageSizeOnBackupStorageReply()
                reply.setActualSize(SizeUnit.GIGABYTE.toByte(5))
                reply.setSize(SizeUnit.GIGABYTE.toByte(15))
                bus.reply(msg, reply)
                called = true
        }
        syncImageSize {
            uuid = image.uuid
        }
        assert called

        String readAndWrite = "qos::read=1024000,write=1024000,readIOPS=222,writeIOPS=222"
        String readAndValue = "read=1024000,write=1024000,readIOPS=222,writeIOPS=222"
        String specifyVolumeName = "specifyVolumeName"

        VolumeInventory dataVolumeToAttach = createDataVolume {
            name = "data-to-attach"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = local.uuid
            systemTags = ["localStorage::hostUuid::" + host.uuid]
        } as VolumeInventory
        def imageTemplate = createDataVolumeTemplateFromVolume {
            name = "data-image-from-root-vol"
            volumeUuid = dataVolumeToAttach.uuid
            backupStorageUuids = [imageStore.uuid]
        } as ImageInventory
        dataTemplate = imageTemplate
        DiskOfferingInventory diskOfferingInv = createDiskOffering {
            name = "data"
            diskSize = SizeUnit.GIGABYTE.toByte(2)
        } as DiskOfferingInventory
        HostInventory host2 = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm2"
            managementIp = "*********"
            clusterUuid = host.clusterUuid
        } as HostInventory
        HostInventory host3 = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm3"
            managementIp = "*********"
            clusterUuid = host.clusterUuid
        } as HostInventory

        List<String> volumeSystemTags = ["capability::virtio-scsi",
                                         "volumeProvisioningStrategy::ThickProvisioning",
                                         "resourceConfig::kvm::vm.cacheMode::none",
                                         "resourceConfig::mevoco::aio.native::true",
                                         readAndWrite]

        def vm = createVmInstance {
            name = "vm-with-diskAOs"
            l3NetworkUuids = [l3.uuid]
            clusterUuid = host.clusterUuid
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            imageUuid = image.uuid
            rootVolumeSystemTags = ["volumeProvisioningStrategy::ThickProvisioning",
                                    "resourceConfig::kvm::vm.cacheMode::none",
                                    "resourceConfig::mevoco::aio.native::true",
                                    readAndWrite]
            primaryStorageUuidForRootVolume = local.uuid
            hostUuid = host.uuid
            diskAOs = getDiskAOs(volumeSystemTags, imageTemplate.uuid, dataVolumeToAttach.uuid, diskOfferingInv.uuid)
        } as VmInstanceInventory

        // check root volume tag
        VolumeVO rootVolume = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, vm.rootVolumeUuid).find()
        List<String> tags = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, vm.rootVolumeUuid).select(SystemTagVO_.tag).listValues()
        tags.contains("volumeProvisioningStrategy::ThickProvisioning")
        List<String> vmTags = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, vm.uuid).select(SystemTagVO_.tag).listValues()
        assert vmTags.contains("driver::virtio")
        assert rootVolume.volumeQos == readAndValue
        assert rootVolume.primaryStorageUuid == local.uuid
        String volume1HostUuid = Q.New(LocalStorageResourceRefVO.class)
                .eq(LocalStorageResourceRefVO_.resourceUuid, vm.rootVolumeUuid).select(LocalStorageResourceRefVO_.hostUuid).findValue()
        assert volume1HostUuid == host.uuid
        checkResourceConfigTags(vm.rootVolumeUuid)

        // create data volume from size
        VolumeVO volume1 = Q.New(VolumeVO.class).eq(VolumeVO_.vmInstanceUuid, vm.getUuid()).eq(VolumeVO_.size, SizeUnit.GIGABYTE.toByte(10).longValue()).find()
        tags = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, volume1.uuid).select(SystemTagVO_.tag).listValues()
        assert tags.contains("volumeProvisioningStrategy::ThickProvisioning")
        assert tags.contains("capability::virtio-scsi")
        assert !volume1.isShareable()
        assert volume1.volumeQos == readAndValue
        assert volume1.primaryStorageUuid == local.uuid
        assert volume1HostUuid == Q.New(LocalStorageResourceRefVO.class)
                .eq(LocalStorageResourceRefVO_.resourceUuid, volume1.uuid).select(LocalStorageResourceRefVO_.hostUuid).findValue()
        checkResourceConfigTags(volume1.uuid)
        assert volume1.name.contains(specifyVolumeName)
        assert volume1.size == SizeUnit.GIGABYTE.toByte(10)

        // create data volume from diskOfferingUuid
        VolumeVO volume2 = Q.New(VolumeVO.class).eq(VolumeVO_.vmInstanceUuid, vm.getUuid()).eq(VolumeVO_.diskOfferingUuid, diskOfferingInv.uuid).find()
        tags = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, volume2.uuid).select(SystemTagVO_.tag).listValues()
        assert tags.contains("volumeProvisioningStrategy::ThickProvisioning")
        assert tags.contains("capability::virtio-scsi")
        assert volume2.volumeQos == readAndValue
        assert volume1HostUuid == Q.New(LocalStorageResourceRefVO.class)
                .eq(LocalStorageResourceRefVO_.resourceUuid, volume2.uuid).select(LocalStorageResourceRefVO_.hostUuid).findValue()
        checkResourceConfigTags(volume2.uuid)
        assert volume2.name.contains("1")
        assert volume2.size == diskOfferingInv.getDiskSize()

        // create data volume from imageTemplate
        VmInstanceVO vmVO = Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, vm.uuid).find()
        VolumeVO volume3 = vmVO.getAllDataVolumes().stream()
                .filter({ it -> ![volume1.uuid, volume2.uuid, dataVolumeToAttach.uuid].contains(it.uuid) })
                .collect(Collectors.toList()).get(0)
        tags = Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, volume3.uuid).select(SystemTagVO_.tag).listValues()
        assert tags.contains("capability::virtio-scsi")
        assert tags.contains("volumeProvisioningStrategy::ThickProvisioning")
        assert volume3.volumeQos == readAndValue
        assert volume3.primaryStorageUuid == local.uuid
        assert volume1HostUuid == Q.New(LocalStorageResourceRefVO.class)
                .eq(LocalStorageResourceRefVO_.resourceUuid, volume3.uuid).select(LocalStorageResourceRefVO_.hostUuid).findValue()
        checkResourceConfigTags(volume3.uuid)
        assert volume3.name.contains("2")

        // attach data volume
        assert vmVO.getAllDataVolumes().stream().map({ it -> it.getUuid() }).collect(Collectors.toList()).contains(dataVolumeToAttach.uuid)
    }

    void testCreateVmWithDiskAOsAndRollback() {
        Long volumeCounts = Q.New(VolumeVO.class).count()
        Long vmCounts = Q.New(VmInstanceVO.class).count()

        String readAndWrite = "qos::read=1024000,write=1024000,readIOPS=222,writeIOPS=222"
        String readAndValue = "read=1024000,write=1024000,readIOPS=222,writeIOPS=222"

        VolumeInventory dataVolumeToAttach = createDataVolume {
            name = "data-to-attach"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = local.uuid
            systemTags = ["localStorage::hostUuid::" + host.uuid]
        } as VolumeInventory
        def imageTemplate = createDataVolumeTemplateFromVolume {
            name = "data-image-from-root-vol"
            volumeUuid = dataVolumeToAttach.uuid
            backupStorageUuids = [imageStore.uuid]
        } as ImageInventory
        DiskOfferingInventory diskOfferingInv = createDiskOffering {
            name = "data"
            diskSize = SizeUnit.GIGABYTE.toByte(2)
        } as DiskOfferingInventory
        List<String> volumeSystemTags = ["capability::virtio-scsi",
                                         "volumeProvisioningStrategy::ThickProvisioning",
                                         "resourceConfig::kvm::vm.cacheMode::none",
                                         "resourceConfig::mevoco::aio.native::true",
                                         readAndWrite]

        // test failed to start vm, delete all volumes and vm
        env.message(StartVmInstanceMsg.class) { StartVmInstanceMsg msg, CloudBus bus ->
            def reply = new StartVmInstanceReply()
            reply.setError(operr("on purpose"))

            assert volumeCounts == Q.New(VolumeVO.class).count() - 5
            assert vmCounts == Q.New(VmInstanceVO.class).count() - 1
            assert Q.New(VolumeVO.class).eq(VolumeVO_.uuid, dataVolumeToAttach.uuid)
                    .select(VolumeVO_.vmInstanceUuid).findValue() == msg.getVmInstanceUuid()

            bus.reply(msg, reply)
        }
        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                l3NetworkUuids = [l3.uuid]
                clusterUuid = host.clusterUuid
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                rootVolumeSystemTags = ["volumeProvisioningStrategy::ThickProvisioning",
                                        "resourceConfig::kvm::vm.cacheMode::none",
                                        "resourceConfig::mevoco::aio.native::true",
                                        readAndWrite]
                primaryStorageUuidForRootVolume = local.uuid
                hostUuid = host.uuid
                diskAOs = getDiskAOs(volumeSystemTags, imageTemplate.uuid, dataVolumeToAttach.uuid, diskOfferingInv.uuid)
            } as VmInstanceInventory
        }
        assert Q.New(VolumeVO.class).eq(VolumeVO_.uuid, dataVolumeToAttach.uuid)
                .select(VolumeVO_.vmInstanceUuid).findValue() == null
        assert volumeCounts == Q.New(VolumeVO.class).count()
        assert vmCounts == Q.New(VmInstanceVO.class).count()


        // test failed to create-data-volume and allocate-primaryStorage
        env.message(InstantiateVolumeMsg.class) { InstantiateVolumeMsg msg, CloudBus bus ->
            def reply = new InstantiateVolumeReply()
            reply.setError(operr("on purpose"))
            bus.reply(msg, reply)
        }
        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                l3NetworkUuids = [l3.uuid]
                clusterUuid = host.clusterUuid
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                rootVolumeSystemTags = ["volumeProvisioningStrategy::ThickProvisioning",
                                        "resourceConfig::kvm::vm.cacheMode::none",
                                        "resourceConfig::mevoco::aio.native::true",
                                        readAndWrite]
                primaryStorageUuidForRootVolume = local.uuid
                hostUuid = host.uuid
                diskAOs = getDiskAOs(volumeSystemTags, imageTemplate.uuid, dataVolumeToAttach.uuid, diskOfferingInv.uuid)
            } as VmInstanceInventory
        }
        assert vmCounts == Q.New(VmInstanceVO.class).count()

        // test failed to instantiate-data-volume-from-template
        env.message(CreateDataVolumeFromVolumeTemplateMsg.class) { CreateDataVolumeFromVolumeTemplateMsg msg, CloudBus bus ->
            def reply = new CreateDataVolumeFromVolumeTemplateReply()
            reply.setError(operr("on purpose"))
            bus.reply(msg, reply)
        }
        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                l3NetworkUuids = [l3.uuid]
                clusterUuid = host.clusterUuid
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                rootVolumeSystemTags = ["volumeProvisioningStrategy::ThickProvisioning",
                                        "resourceConfig::kvm::vm.cacheMode::none",
                                        "resourceConfig::mevoco::aio.native::true",
                                        readAndWrite]
                primaryStorageUuidForRootVolume = local.uuid
                hostUuid = host.uuid
                diskAOs = getDiskAOs(volumeSystemTags, imageTemplate.uuid, dataVolumeToAttach.uuid, diskOfferingInv.uuid)
            } as VmInstanceInventory
        }
        assert vmCounts == Q.New(VmInstanceVO.class).count()
    }

    void testCreateVmWithDiskAOAndValidateAPI() {
        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags = ["driver::virtio"]

        // Cannot set the following properties at the same time
        CreateVmInstanceAction vmAction = new CreateVmInstanceAction()
        vmAction.name = "vm-with-diskAOs"
        vmAction.cpuNum = 1
        vmAction.imageUuid = image.uuid
        vmAction.sessionId = adminSession()
        vmAction.l3NetworkUuids = [l3.uuid]
        vmAction.hostUuid = host.uuid
        vmAction.memorySize = SizeUnit.GIGABYTE.toByte(1)

        APICreateVmInstanceMsg.DiskAO diskAO = new APICreateVmInstanceMsg.DiskAO()
        diskAO.size = SizeUnit.GIGABYTE.toByte(10)
        diskAO.diskOfferingUuid = "diskOfferingUuid"
        diskAO.templateUuid = "imageTemplateUuid"
        diskAO.sourceUuid = "volumeUuid"
        vmAction.diskAOs = [rootDiskAO, diskAO]
        String err = vmAction.call().error.getDetails()
        assert err.toString().contains("size")
        assert err.toString().contains("diskOfferingUuid")
        assert err.toString().contains("templateUuid")
        assert err.toString().contains("sourceUuid")

        // Need to set one of the following properties, and can only be one of them
        CreateVmInstanceAction vmAction1 = new CreateVmInstanceAction()
        vmAction1.name = "vm-with-diskAOs"
        vmAction1.cpuNum = 1
        vmAction1.imageUuid = image.uuid
        vmAction1.sessionId = adminSession()
        vmAction1.l3NetworkUuids = [l3.uuid]
        vmAction1.hostUuid = host.uuid
        vmAction1.memorySize = SizeUnit.GIGABYTE.toByte(1)

        diskAO = new APICreateVmInstanceMsg.DiskAO()
        vmAction1.diskAOs = [rootDiskAO, diskAO]
        err = vmAction1.call().error.getDetails()
        assert err.toString().contains("size")
        assert err.toString().contains("diskOfferingUuid")
        assert err.toString().contains("templateUuid")
        assert err.toString().contains("sourceUuid")

        // success to create vm with size
        CreateVmInstanceAction vmAction2 = new CreateVmInstanceAction()
        vmAction2.name = "vm-with-diskAOs"
        vmAction2.cpuNum = 1
        vmAction2.imageUuid = image.uuid
        vmAction2.sessionId = adminSession()
        vmAction2.l3NetworkUuids = [l3.uuid]
        vmAction2.hostUuid = host.uuid
        vmAction2.memorySize = SizeUnit.GIGABYTE.toByte(1)
    }

    void testCreateVmWithoutPS() {
        env.cleanSimulatorAndMessageHandlers()
        DiskOfferingInventory diskOfferingInv = createDiskOffering {
            name = "data"
            diskSize = SizeUnit.GIGABYTE.toByte(2)
        } as DiskOfferingInventory

        List<APICreateVmInstanceMsg.DiskAO> diskAOList = []

        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags = ["driver::virtio"]
        diskAOList.add(rootDiskAO)

        APICreateVmInstanceMsg.DiskAO diskAO5 = new APICreateVmInstanceMsg.DiskAO()
        diskAO5.size = SizeUnit.GIGABYTE.toByte(10)
        diskAOList.add(diskAO5)

        APICreateVmInstanceMsg.DiskAO diskAO6 = new APICreateVmInstanceMsg.DiskAO()
        diskAO6.diskOfferingUuid = diskOfferingInv.uuid
        diskAOList.add(diskAO6)

        def vm = createVmInstance {
            name = "vm-with-diskAOs"
            l3NetworkUuids = [l3.uuid]
            clusterUuid = host.clusterUuid
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            imageUuid = image.uuid
            primaryStorageUuidForRootVolume = local.uuid
            hostUuid = host.uuid
            diskAOs = diskAOList
        } as VmInstanceInventory

        assert Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, vm.uuid)
                .select(VmInstanceVO_.state).findValue() == VmInstanceState.Running
    }

    void testCreateVmWithSCSILun() {
        env.cleanSimulatorAndMessageHandlers()

        // prepare scsiLun
        IscsiLunStruct s1 = new IscsiLunStruct()
        s1.wwids = ["14f504e46494c455277466d4362662d566641302d49393041"]
        s1.type = "disk"
        s1.wwn = ""
        s1.model = "VIRTUAL-DISK"
        s1.vendor = "OPNFILER"
        s1.hctl = "4:0:0:0"
        s1.serial = "4f504e46494c455277466d4362662d566641302d49393041"
        s1.path = "ip-************:3260-iscsi-iqn.2018-02.io.zstack:tsn.00004-lun-0"
        s1.size = 44023414784l
        env.simulator(StorageDeviceKvmCommands.ISCSI_LOGIN_PATH) { HttpEntity<String> entity, EnvPremiumSpec spec ->
            def rsp = new StorageDeviceKvmCommands.IscsiLoginRsp() as StorageDeviceKvmCommands.IscsiLoginRsp
            IscsiTargetStruct ss1 = new IscsiTargetStruct()
            ss1.iqn = "iqn.2018-02.io.zstack:tsn.00004"
            ss1.iscsiLunStructList = [s1]
            rsp.iscsiTargetStructList = [ss1]
            return rsp
        }
        def iscsiServer1 = addIscsiServer {
            ip = "************"
            port = 3260
        } as IscsiServerInventory
        assert iscsiServer1.iscsiTargets == null || iscsiServer1.iscsiTargets.isEmpty()
        attachIscsiServerToCluster {
            uuid = iscsiServer1.uuid
            clusterUuid = host.clusterUuid
        }
        def scsiluns = queryScsiLun {} as List<ScsiLunInventory>
        assert scsiluns.size() == 1
        assert scsiluns[0].source != null
        assert Q.New(ScsiLunHostRefVO.class).count() == 3

        // create vm with scsiLun
        List<APICreateVmInstanceMsg.DiskAO> diskAOList = []

        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags = ["driver::virtio"]
        diskAOList.add(rootDiskAO)

        APICreateVmInstanceMsg.DiskAO scsiLunDiskAO = new APICreateVmInstanceMsg.DiskAO()
        scsiLunDiskAO.sourceUuid = scsiluns[0].uuid
        scsiLunDiskAO.sourceType = LunVO.class.getSimpleName()
        diskAOList.add(scsiLunDiskAO)

        APICreateVmInstanceMsg.DiskAO diskAO1 = new APICreateVmInstanceMsg.DiskAO()
        diskAO1.size = SizeUnit.GIGABYTE.toByte(10)
        diskAOList.add(diskAO1)

        // attach to scsiLun
        def vm = createVmInstance {
            name = "vm-with-diskAOs"
            l3NetworkUuids = [l3.uuid]
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            imageUuid = image.uuid
            hostUuid = host.uuid
            diskAOs = diskAOList
        } as VmInstanceInventory
        ScsiLunVmInstanceRefVO ref = Q.New(ScsiLunVmInstanceRefVO.class).eq(ScsiLunVmInstanceRefVO_.scsiLunUuid, scsiluns[0].uuid)
                .eq(ScsiLunVmInstanceRefVO_.vmInstanceUuid, vm.uuid).find()
        assert !ref.attachMultipath

        // delete vm and check ScsiLunVmInstanceRefVO
        destroyVmInstance {
            uuid = vm.uuid
        }
        expungeVmInstance {
            uuid = vm.uuid
        }
        assert !Q.New(ScsiLunVmInstanceRefVO.class).eq(ScsiLunVmInstanceRefVO_.scsiLunUuid, scsiluns[0].uuid)
                .eq(ScsiLunVmInstanceRefVO_.vmInstanceUuid, vm.uuid).isExists()

        // test failed to start vm and check ScsiLunVmInstanceRefVO
        env.message(StartVmInstanceMsg.class) { StartVmInstanceMsg msg, CloudBus bus ->
            def reply = new StartVmInstanceReply()
            reply.setError(operr("on purpose"))

            assert Q.New(ScsiLunVmInstanceRefVO.class).eq(ScsiLunVmInstanceRefVO_.scsiLunUuid, scsiluns[0].uuid).isExists()

            bus.reply(msg, reply)
        }
        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                l3NetworkUuids = [l3.uuid]
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                hostUuid = host.uuid
                diskAOs = diskAOList
            } as VmInstanceInventory
        }
        assert !Q.New(ScsiLunVmInstanceRefVO.class).eq(ScsiLunVmInstanceRefVO_.scsiLunUuid, scsiluns[0].uuid).isExists()

        // validate attach scsiLun to vm and check sbg
        env.cleanSimulatorAndMessageHandlers()

        PrimaryStorageInventory sblk = addSharedBlockGroupPrimaryStorage {
            name = "sblk"
            sessionId = adminSession()
            diskUuids = [scsiluns[0].wwid]
            zoneUuid = zone.uuid
        } as PrimaryStorageInventory

        attachPrimaryStorageToCluster {
            primaryStorageUuid = sblk.uuid
            clusterUuid = host.clusterUuid
        }

        CreateVmInstanceAction vmAction = new CreateVmInstanceAction()
        vmAction.name = "vm-with-diskAOs"
        vmAction.cpuNum = 1
        vmAction.memorySize = SizeUnit.GIGABYTE.toByte(1)
        vmAction.imageUuid = image.uuid
        vmAction.sessionId = adminSession()
        vmAction.l3NetworkUuids = [l3.uuid]
        vmAction.hostUuid = host.uuid
        vmAction.diskAOs = [rootDiskAO, scsiLunDiskAO]
        String err = vmAction.call().error.getDetails()
        assert err.contains("is already attach to primary storage")

        // validate attach scsiLun to vm and check cluster
        def cluster3 = createCluster {
            name = "cluster3"
            hypervisorType = "KVM"
            zoneUuid = zone.uuid
        } as ClusterInventory
        def hostOfCluster3 = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm3"
            managementIp = "*********"
            clusterUuid = cluster3.uuid
        } as HostInventory

        IscsiLunStruct s2 = new IscsiLunStruct()
        s2.wwids = ["14f504e46494c455277466d4362662d566641302d49393241"]
        s2.type = "disk"
        s2.wwn = ""
        s2.model = "VIRTUAL-DISK"
        s2.vendor = "OPNFILER"
        s2.hctl = "1:0:0:0"
        s2.serial = "4f504e42494c455277466d4362662d566641302d49392041"
        s2.path = "ip-************:3260-iscsi-iqn.2018-02.io.zstack:tsn.00004-lun-0"
        s2.size = 44023214784l
        env.simulator(StorageDeviceKvmCommands.ISCSI_LOGIN_PATH) { HttpEntity<String> entity, EnvPremiumSpec spec ->
            def rsp = new StorageDeviceKvmCommands.IscsiLoginRsp() as StorageDeviceKvmCommands.IscsiLoginRsp
            IscsiTargetStruct ss2 = new IscsiTargetStruct()
            ss2.iqn = "iqn.2018-01.io.zstack:tsn.00004"
            ss2.iscsiLunStructList = [s2]
            rsp.iscsiTargetStructList = [ss2]
            return rsp
        }
        def iscsiServer2 = addIscsiServer {
            ip = "************"
            port = 3260
        } as IscsiServerInventory
        attachIscsiServerToCluster {
            uuid = iscsiServer2.uuid
            clusterUuid = cluster3.uuid
        }
        assert Q.New(ScsiLunHostRefVO.class).eq(ScsiLunHostRefVO_.hostUuid, hostOfCluster3.uuid).count() == 1
        String scsiLunUuid = Q.New(ScsiLunHostRefVO.class).eq(ScsiLunHostRefVO_.hostUuid, hostOfCluster3.uuid)
                .select(ScsiLunHostRefVO_.scsiLunUuid).findValue()

        scsiLunDiskAO = new APICreateVmInstanceMsg.DiskAO()
        scsiLunDiskAO.sourceUuid = scsiLunUuid
        scsiLunDiskAO.sourceType = LunVO.class.getSimpleName()
        diskAOList.add(scsiLunDiskAO)

        vmAction = new CreateVmInstanceAction()
        vmAction.name = "vm-with-diskAOs"
        vmAction.cpuNum = 1
        vmAction.memorySize = SizeUnit.GIGABYTE.toByte(1)
        vmAction.imageUuid = image.uuid
        vmAction.sessionId = adminSession()
        vmAction.l3NetworkUuids = [l3.uuid]
        vmAction.hostUuid = host.uuid
        vmAction.diskAOs = [rootDiskAO, scsiLunDiskAO]
        err = vmAction.call().error.getDetails()
        assert err.contains("are not attach to the cluster of host")

        detachIscsiServerFromCluster {
            uuid = iscsiServer1.uuid
            clusterUuid = host.clusterUuid
        }
        deleteIscsiServer {
            uuid = iscsiServer1.uuid
        }
        detachIscsiServerFromCluster {
            uuid = iscsiServer2.uuid
            clusterUuid = cluster3.uuid
        }
        deleteIscsiServer {
            uuid = iscsiServer2.uuid
        }
    }

    void testCreateVmWithoutConsideringImageVirtio() {
        env.cleanSimulatorAndMessageHandlers()
        updateImage {
            uuid = image.uuid
            virtio = true
        }

        // virtio = false
        List<APICreateVmInstanceMsg.DiskAO> diskAOList = []
        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags
        diskAOList.add(rootDiskAO)

        def vm = createVmInstance {
            name = "vm-with-diskAOs"
            l3NetworkUuids = [l3.uuid]
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            imageUuid = image.uuid
            hostUuid = host.uuid
            diskAOs = diskAOList
        } as VmInstanceInventory

        assert !Q.New(SystemTagVO.class).eq(SystemTagVO_.resourceUuid, vm.uuid)
                .eq(SystemTagVO_.tag, "driver::virtio").isExists()
    }

    void testCreateVmAndStartVm() {
        env.cleanSimulatorAndMessageHandlers()

        List<APICreateVmInstanceMsg.DiskAO> diskAOList = []
        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags
        diskAOList.add(rootDiskAO)

        def vm = createVmInstance {
            name = "vm-with-diskAOs"
            l3NetworkUuids = [l3.uuid]
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            imageUuid = image.uuid
            hostUuid = host.uuid
            diskAOs = diskAOList
            strategy = "InstantStart"
        } as VmInstanceInventory

        assert Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, vm.uuid)
                .select(VmInstanceVO_.state).findValue() == VmInstanceState.Running

        // do not stop vm when create vm without data volume
        env.message(InstantiateNewCreatedVmInstanceMsg.class) { InstantiateNewCreatedVmInstanceMsg msg, CloudBus bus ->
            def reply = new InstantiateNewCreatedVmInstanceReply()
            reply.setError(operr("on purpose"))
            assert msg.getStrategy() == VmCreationStrategy.InstantStart.toString()
            bus.reply(msg, reply)
        }
        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                l3NetworkUuids = [l3.uuid]
                clusterUuid = host.clusterUuid
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                hostUuid = host.uuid
                diskAOs = diskAOList
                strategy = VmCreationStrategy.InstantStart.toString()
            } as VmInstanceInventory
        }

        // stop vm when create vm with data volume
        env.message(InstantiateNewCreatedVmInstanceMsg.class) { InstantiateNewCreatedVmInstanceMsg msg, CloudBus bus ->
            def reply = new InstantiateNewCreatedVmInstanceReply()
            reply.setError(operr("on purpose"))
            assert msg.getStrategy() == VmCreationStrategy.CreateStopped.toString()
            bus.reply(msg, reply)
        }
        APICreateVmInstanceMsg.DiskAO diskAO2 = new APICreateVmInstanceMsg.DiskAO()
        diskAO2.primaryStorageUuid = local.uuid
        diskAO2.size = 1024
        diskAOList.add(diskAO2)
        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                l3NetworkUuids = [l3.uuid]
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                hostUuid = host.uuid
                diskAOs = diskAOList
                strategy = VmCreationStrategy.InstantStart.toString()
            } as VmInstanceInventory
        }
    }

    void testCreateVmWithoutImage() {
        env.cleanSimulatorAndMessageHandlers()

        boolean called = false
        env.message(SyncImageSizeOnBackupStorageMsg.class) { SyncImageSizeOnBackupStorageMsg msg, CloudBus bus ->
            SyncImageSizeOnBackupStorageReply reply = new SyncImageSizeOnBackupStorageReply()
            reply.setActualSize(SizeUnit.GIGABYTE.toByte(5))
            reply.setSize(SizeUnit.GIGABYTE.toByte(13))
            bus.reply(msg, reply)
            called = true
        }
        syncImageSize {
            uuid = dataTemplate.uuid
        }
        assert called

        ImageVO dataTemplateVO = Q.New(ImageVO.class).eq(ImageVO_.uuid, dataTemplate.uuid).find()
        assert dataTemplateVO.size == SizeUnit.GIGABYTE.toByte(13)
        assert dataTemplateVO.actualSize == SizeUnit.GIGABYTE.toByte(5)

        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"

        String volumeName = String.format("data-volume-from-%s", dataTemplate.uuid)
        APICreateVmInstanceMsg.DiskAO diskAO = new APICreateVmInstanceMsg.DiskAO()
        diskAO.templateUuid = dataTemplate.uuid
        diskAO.name = volumeName

        def vm = createVmInstance {
            name = "vm-with-diskAOs"
            l3NetworkUuids = [l3.uuid]
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            hostUuid = host.uuid
            diskAOs = [rootDiskAO, diskAO]
            strategy = "InstantStart"
            rootDiskSize = SizeUnit.GIGABYTE.toByte(10)
        } as VmInstanceInventory

        VmInstanceVO vmVO = Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, vm.uuid).find()
        assert vmVO.getAllDataVolumes().size() == 1
        VolumeVO dataVolumeVO = new ArrayList<>(vmVO.getAllDataVolumes()).get(0)
        assert dataVolumeVO.getName() == volumeName
        assert dataVolumeVO.getPrimaryStorageUuid() == vmVO.getRootVolume().getPrimaryStorageUuid()
    }

    void testValidatePsWhenCreateVmWithDiskAO() {
        env.cleanSimulatorAndMessageHandlers()

        cluster2 = createCluster {
            name = "cluster2"
            hypervisorType = "KVM"
            zoneUuid = zone.uuid
        } as ClusterInventory
        hostOfCluster2 = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm12"
            managementIp = "*********"
            clusterUuid = cluster2.uuid
        } as HostInventory
        localPsOfCluster2 = addLocalPrimaryStorage {
            name = "localPS"
            url = "/localPS"
            zoneUuid = zone.uuid
        } as PrimaryStorageInventory
        env.simulator(LocalStorageKvmBackend.INIT_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new LocalStorageKvmBackend.InitRsp()
            rsp.totalCapacity = SizeUtils.sizeStringToBytes("100G")
            rsp.availableCapacity = SizeUtils.sizeStringToBytes("50G")
            rsp.localStorageUsedCapacity = 0
            return rsp
        }
        attachPrimaryStorageToCluster {
            primaryStorageUuid = localPsOfCluster2.uuid
            clusterUuid = cluster2.uuid
        }

        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags = ["driver::virtio"]

        // 1 primaryStorage of disk are not in the same cluster
        // cluster1
        APICreateVmInstanceMsg.DiskAO diskAO = new APICreateVmInstanceMsg.DiskAO()
        diskAO.primaryStorageUuid = sbg.uuid
        diskAO.size = SizeUnit.GIGABYTE.toByte(1)
        // cluster2
        APICreateVmInstanceMsg.DiskAO diskAO1 = new APICreateVmInstanceMsg.DiskAO()
        diskAO1.primaryStorageUuid = localPsOfCluster2.uuid
        diskAO1.size = SizeUnit.GIGABYTE.toByte(1)
        // cluster1
        APICreateVmInstanceMsg.DiskAO diskAO2 = new APICreateVmInstanceMsg.DiskAO()
        diskAO2.primaryStorageUuid = local.uuid
        diskAO2.size = SizeUnit.GIGABYTE.toByte(1)

        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                sessionId = adminSession()
                l3NetworkUuids = [l3.uuid]
                hostUuid = hostOfCluster2.uuid
                diskAOs = [rootDiskAO, diskAO, diskAO1, diskAO2]
            } as VmInstanceInventory
        }

        // 2 primaryStorage of root and primaryStorage of data disk are not in the same cluster
        // cluster1
        diskAO = new APICreateVmInstanceMsg.DiskAO()
        diskAO.primaryStorageUuid = sbg.uuid
        diskAO.size = SizeUnit.GIGABYTE.toByte(10)

        expectError {
            createVmInstance {
                name = "vm-with-diskAOs"
                cpuNum = 1
                memorySize = SizeUnit.GIGABYTE.toByte(1)
                imageUuid = image.uuid
                sessionId = adminSession()
                hostUuid = hostOfCluster2.uuid
                // cluster2
                primaryStorageUuidForRootVolume = localPsOfCluster2.uuid
                diskAOs = [rootDiskAO, diskAO]
            } as VmInstanceInventory
        }
    }

    void testValidateNeedToAttachVolumeWhenCreateVmWithDiskAO() {
        env.cleanSimulatorAndMessageHandlers()
        HostGlobalConfig.PING_HOST_INTERVAL.updateValue(Integer.MAX_VALUE)

        VolumeInventory localPsOfCluster2Volume = createDataVolume {
            name = "localPsOfCluster2Volume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = localPsOfCluster2.uuid
            systemTags = ["localStorage::hostUuid::" + hostOfCluster2.uuid]
        } as VolumeInventory

        VolumeInventory sbgDisableVolume = createDataVolume {
            name = "sbgVolume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = sbg.uuid
        } as VolumeInventory
        changeVolumeState {
            uuid = sbgDisableVolume.uuid
            stateEvent = "disable"
        }

        VolumeInventory sbgDeletedVolume = createDataVolume {
            name = "sbgVolume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = sbg.uuid
        } as VolumeInventory
        deleteDataVolume {
            uuid = sbgDeletedVolume.uuid
        }

        VolumeInventory sbgAttachedVolume = createDataVolume {
            name = "sbgVolume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = sbg.uuid
        } as VolumeInventory
        attachDataVolumeToVm {
            volumeUuid = sbgAttachedVolume.uuid
            vmInstanceUuid = vmInv.uuid
        }

        VolumeInventory sbgNotInstantiatedVolume = createDataVolume {
            name = "sbgVolume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = sbg.uuid
        } as VolumeInventory
        SQL.New(VolumeVO.class).eq(VolumeVO_.uuid, sbgNotInstantiatedVolume.uuid)
                .set(VolumeVO_.status, VolumeStatus.Creating).update()

        VolumeInventory sbgVolume1 = createDataVolume {
            name = "sbgVolume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = sbg.uuid
        } as VolumeInventory

        VolumeInventory sbgVolume2 = createDataVolume {
            name = "sbgVolume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = sbg.uuid
        } as VolumeInventory

        VolumeInventory sbgVolume3 = createDataVolume {
            name = "sbgVolume"
            diskOfferingUuid = diskOfferingInventory.uuid
            primaryStorageUuid = sbg.uuid
        } as VolumeInventory

        SQL.New(PrimaryStorageHostRefVO.class).eq(PrimaryStorageHostRefVO_.hostUuid, host.uuid)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, sbg.uuid)
                .set(PrimaryStorageHostRefVO_.status, org.zstack.header.storage.primary.PrimaryStorageHostStatus.Disconnected)
                .update()

        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags = ["driver::virtio"]

        CreateVmInstanceAction vmAction = new CreateVmInstanceAction()
        vmAction.name = "vm-with-diskAOs"
        vmAction.cpuNum = 1
        vmAction.memorySize = SizeUnit.GIGABYTE.toByte(1)
        vmAction.imageUuid = image.uuid
        vmAction.sessionId = adminSession()
        vmAction.l3NetworkUuids = [l3.uuid]
        vmAction.hostUuid = host.uuid

        APICreateVmInstanceMsg.DiskAO localPsOfCluster2VolumeDiskAO = new APICreateVmInstanceMsg.DiskAO()
        localPsOfCluster2VolumeDiskAO.sourceType = VolumeVO.class.getSimpleName()
        localPsOfCluster2VolumeDiskAO.sourceUuid = localPsOfCluster2Volume.uuid

        APICreateVmInstanceMsg.DiskAO attachRootVolumeDiskAO = new APICreateVmInstanceMsg.DiskAO()
        attachRootVolumeDiskAO.sourceType = VolumeVO.class.getSimpleName()
        attachRootVolumeDiskAO.sourceUuid = vmInv.rootVolumeUuid

        APICreateVmInstanceMsg.DiskAO sbgDisableVolumeDiskAO = new APICreateVmInstanceMsg.DiskAO()
        sbgDisableVolumeDiskAO.sourceType = VolumeVO.class.getSimpleName()
        sbgDisableVolumeDiskAO.sourceUuid = sbgDisableVolume.uuid

        APICreateVmInstanceMsg.DiskAO sbgDeletedVolumeDiskAO = new APICreateVmInstanceMsg.DiskAO()
        sbgDeletedVolumeDiskAO.sourceType = VolumeVO.class.getSimpleName()
        sbgDeletedVolumeDiskAO.sourceUuid = sbgDeletedVolume.uuid

        APICreateVmInstanceMsg.DiskAO sbgAttachedVolumeDiskAO = new APICreateVmInstanceMsg.DiskAO()
        sbgAttachedVolumeDiskAO.sourceType = VolumeVO.class.getSimpleName()
        sbgAttachedVolumeDiskAO.sourceUuid = sbgAttachedVolume.uuid

        APICreateVmInstanceMsg.DiskAO sbgNotInstantiatedVolumeDiskAO = new APICreateVmInstanceMsg.DiskAO()
        sbgNotInstantiatedVolumeDiskAO.sourceType = VolumeVO.class.getSimpleName()
        sbgNotInstantiatedVolumeDiskAO.sourceUuid = sbgNotInstantiatedVolume.uuid

        APICreateVmInstanceMsg.DiskAO primaryStorageHostStatusDiskAO = new APICreateVmInstanceMsg.DiskAO()
        primaryStorageHostStatusDiskAO.sourceType = VolumeVO.class.getSimpleName()
        primaryStorageHostStatusDiskAO.sourceUuid = sbgVolume2.uuid

        vmAction.diskAOs = [rootDiskAO, localPsOfCluster2VolumeDiskAO, attachRootVolumeDiskAO, sbgDisableVolumeDiskAO,
                            sbgDeletedVolumeDiskAO, sbgAttachedVolumeDiskAO, sbgNotInstantiatedVolumeDiskAO, primaryStorageHostStatusDiskAO]

        String err = vmAction.call().error.getDetails()
        assert err.contains("已被禁用，不能挂载")
        assert err.contains("运行的VM")
        assert err.contains("ROOT-for-vm")
        assert err.contains("已经被删除")
        assert err.contains("当前状态是")
        assert err.contains("不能再次加载")
        assert err.contains("没有可用集群")

        APICreateVmInstanceMsg.DiskAO theSameVolumeDiskAO1 = new APICreateVmInstanceMsg.DiskAO()
        theSameVolumeDiskAO1.sourceType = VolumeVO.class.getSimpleName()
        theSameVolumeDiskAO1.sourceUuid = sbgVolume3.uuid

        APICreateVmInstanceMsg.DiskAO theSameVolumeDiskAO2 = new APICreateVmInstanceMsg.DiskAO()
        theSameVolumeDiskAO2.sourceType = VolumeVO.class.getSimpleName()
        theSameVolumeDiskAO2.sourceUuid = sbgVolume3.uuid

        vmAction.diskAOs = [rootDiskAO, theSameVolumeDiskAO1, theSameVolumeDiskAO2]
        err = vmAction.call().error.getDetails()
        assert err.contains("duplicate volume uuids")

        SQL.New(PrimaryStorageHostRefVO.class).eq(PrimaryStorageHostRefVO_.hostUuid, host.uuid)
                .eq(PrimaryStorageHostRefVO_.primaryStorageUuid, sbg.uuid)
                .set(PrimaryStorageHostRefVO_.status, org.zstack.header.storage.primary.PrimaryStorageHostStatus.Connected)
                .update()
        HostGlobalConfig.PING_HOST_INTERVAL.updateValue(60)
    }

    private List<APICreateVmInstanceMsg.DiskAO> getDiskAOs(List<String> volumeSystemTags, String imageTemplateUuid, String volumeUuid, String diskOfferingUuid) {
        List<APICreateVmInstanceMsg.DiskAO> diskAOs = []

        APICreateVmInstanceMsg.DiskAO rootDiskAO = new APICreateVmInstanceMsg.DiskAO()
        rootDiskAO.boot = true
        rootDiskAO.platform = "Linux"
        rootDiskAO.guestOsType = "Centos 6"
        rootDiskAO.architecture = "x86_64"
        rootDiskAO.systemTags = ["driver::virtio"]
        diskAOs.add(rootDiskAO)

        // create data volume from size
        APICreateVmInstanceMsg.DiskAO diskAO1 = new APICreateVmInstanceMsg.DiskAO()
        diskAO1.primaryStorageUuid = local.uuid
        diskAO1.size = SizeUnit.GIGABYTE.toByte(10)
        diskAO1.systemTags = volumeSystemTags
        diskAO1.name = "specifyVolumeName"
        diskAOs.add(diskAO1)

        // create data volume from diskOfferingUuid
        APICreateVmInstanceMsg.DiskAO diskAO2 = new APICreateVmInstanceMsg.DiskAO()
        diskAO2.primaryStorageUuid = local.uuid
        diskAO2.diskOfferingUuid = diskOfferingUuid
        diskAO2.systemTags = volumeSystemTags
        diskAOs.add(diskAO2)

        // create data volume from imageTemplate
        APICreateVmInstanceMsg.DiskAO diskAO3 = new APICreateVmInstanceMsg.DiskAO()
        diskAO3.primaryStorageUuid = local.uuid
        diskAO3.templateUuid = imageTemplateUuid
        diskAO3.systemTags = volumeSystemTags
        diskAOs.add(diskAO3)

        // attach data volume
        APICreateVmInstanceMsg.DiskAO diskAO4 = new APICreateVmInstanceMsg.DiskAO()
        diskAO4.sourceType = "VolumeVO"
        diskAO4.sourceUuid = volumeUuid
        diskAOs.add(diskAO4)

        return diskAOs
    }

    private static void checkResourceConfigTags(String resourceUuid) {
        assert Q.New(ResourceConfigVO.class).eq(ResourceConfigVO_.value, "none").eq(ResourceConfigVO_.resourceType, VolumeVO.class.getSimpleName())
                .eq(ResourceConfigVO_.category, "kvm").eq(ResourceConfigVO_.resourceUuid, resourceUuid).isExists()
        assert Q.New(ResourceConfigVO.class).eq(ResourceConfigVO_.value, "true").eq(ResourceConfigVO_.resourceType, VolumeVO.class.getSimpleName())
                .eq(ResourceConfigVO_.category, "mevoco").eq(ResourceConfigVO_.resourceUuid, resourceUuid).isExists()
    }

    @Override
    void clean() {
        env.delete()
    }
}
