package org.zstack.test.integration.ai

import io.kubernetes.client.openapi.models.V1LoadBalancerIngress
import io.kubernetes.client.openapi.models.V1LoadBalancerStatus
import io.kubernetes.client.openapi.models.V1ServiceStatus
import org.springframework.http.HttpEntity
import org.zstack.ai.AIConstants
import org.zstack.ai.AIGlobalConfig
import org.zstack.ai.BentoBasedModelStorageBackend
import org.zstack.ai.ModelServiceBackendType
import org.zstack.ai.entity.DeploymentStatus
import org.zstack.ai.entity.ModelCenterVO
import org.zstack.ai.entity.ModelServiceInstanceGroupStatus
import org.zstack.ai.entity.ModelServiceType
import org.zstack.ai.entity.ApplicationDevelopmentServiceVO
import org.zstack.ai.message.SyncAINginxConfigurationMsg
import org.zstack.ai.message.SyncAINginxConfigurationReply
import org.zstack.ai.service.AppDevelopmentServiceFactory
import org.zstack.container.message.CreateDeploymentMsg
import org.zstack.container.message.CreateDeploymentReply
import org.zstack.container.message.CreateKubernetesServiceMsg
import org.zstack.container.message.CreateKubernetesServiceReply
import org.zstack.container.message.DeleteDeploymentMsg
import org.zstack.container.message.DeleteDeploymentReply
import org.zstack.container.message.DeleteKubernetesServiceMsg
import org.zstack.container.message.DeleteKubernetesServiceReply
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.host.HostStatus
import org.zstack.header.identity.AccountConstant
import org.zstack.header.identity.AccountResourceRefVO
import org.zstack.header.identity.AccountResourceRefVO_
import org.zstack.header.identity.SharedResourceVO
import org.zstack.header.identity.SharedResourceVO_
import org.zstack.iam2.container.IAM2ContainerQuotaGlobalConfig
import org.zstack.iam2.rbac.IAM2RolePolicyStatementHelper
import org.zstack.kvm.KVMConstant
import org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend
import org.zstack.pciDevice.PciDeviceTOExampleMaker
import org.zstack.sdk.*
import org.zstack.sdk.iam2.entity.IAM2ProjectInventory
import org.zstack.sdk.iam2.entity.IAM2VirtualIDInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.HttpError
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

class ApplicationServiceCase extends PremiumSubCase {
    EnvSpec env
    ModelCenterInventory modelCenter
    ModelInventory modelInventory
    ModelServiceInventory modelServiceInventory
    PciDeviceSpecInventory nvSpec
    PciDeviceInventory nvidiaDevice
    PrimaryStorageInventory ps1
    Long modelSize
    Integer cpu, memory, gpuMem
    String serviceYaml
    ImageInventory image
    ZoneInventory zone
    L3NetworkInventory l3, pubL3

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = AIModelEnv.FourHostsTwoClusterEnv()
    }

    def commandList = []

    @Override
    void test() {
        env.create {
            reloadLicense {}
            prepareMock()
            prepareEnv()
            testAppDevelopmentServiceToCloud()
        }
    }

    String generateUTMockPath(String requestPath) {
        String mockPath = requestPath
        if (!requestPath.startsWith("/")) {
            mockPath = "/" + requestPath;
        }
        return String.format("%s%s", AIConstants.UT_MOCK_PATH_PREFIX, mockPath)
    }

    void prepareEnv() {
        image = env.inventoryByName("image1") as ImageInventory
        zone = env.inventoryByName("zone") as ZoneInventory
        l3 = env.inventoryByName("l3") as L3NetworkInventory
        pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        cpu = 1
        memory = 1024
        gpuMem = 2048000

        modelCenter = queryModelCenter {
            conditions = ["uuid=2cc1f4d6b8014b44912f92bb242e9675"]
        }[0] as ModelCenterInventory

        updateModelCenter {
            uuid = modelCenter.uuid
            storageNetworkUuid = l3.uuid
            serviceNetworkUuid = pubL3.uuid
            managementIp = "127.0.0.1"
            managementPort = 8989
            containerRegistry = "127.0.0.1:5443/registry"
            containerNetwork = "ai-container-network"
            containerStorageNetwork = "ai-container-staorage-network"
        } as ModelCenterInventory

        modelServiceInventory = queryModelService {
            conditions = ["uuid=c6aab22271us44aqt44ih692fc5825cb"]
        }[0] as ModelServiceInventory

        updateModelService {
            uuid = modelServiceInventory.uuid
            vmImageUuid = image.uuid
        }

        modelSize = 14.39 * 1024 * 1024 * 1024

        serviceYaml = """services:
  - name: qwen-chat:2b34xhrmqwhomjkd
    ports:
      - 3000
    livez: /livez
    readyz: /readyz
env:
  - key:value
  - key:value
distro:
  packages: vim,nfs-utils
python:
  requirements_txt: ./requirements.txt
  index_url: https://pypi.tuna.tsinghua.edu.cn/simple
  trusted_host: pypi.tuna.tsinghua.edu.cn"""
    }

    void prepareMock() {
        env.message(SyncAINginxConfigurationMsg) { SyncAINginxConfigurationMsg msg, CloudBus bus ->
            def reply = new SyncAINginxConfigurationReply()
            bus.reply(msg, reply)
        }

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadModelResponse()
            rsp.size = "14.39 GiB"
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.tag = "qwen--qwen-chat:n3ucnjrnicyqz6xk"
            rsp.module = ""
            rsp.pipeline_tag = "text-generation"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.CREATE_NGINX_RULE) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.DELETE_NGINX_RULE) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE) { HttpEntity<String> entity, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(entity.body, BentoBasedModelStorageBackend.UploadModelServiceCmd.class)
            if (cmd.source == "HuggingFace") {
                assert cmd.repository != null
            }

            def rsp = new BentoBasedModelStorageBackend.UploadModelServiceResponse()
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            rsp.size = 1024
            return rsp
        }

        env.simulator(generateUTMockPath("/livez")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        env.simulator(generateUTMockPath("/readyz")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        // application type of service do not have livez test it's default route
        env.simulator(generateUTMockPath("/")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        env.simulator(generateUTMockPath("/apps")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_DATASET) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadDatasetResponse()
            rsp.installPath = "/path/to/test"
            rsp.size = 1024
            rsp.success = true
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.CONFIG_SET) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.ConfigUpdateResponse()
            return rsp
        }

        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_GPU(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_AUDIO(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        env.simulator(generateUTMockPath("/readyz")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        List<KVMHostInventory> allHosts = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : allHosts) {
            updateHostIommuState {
                delegate.uuid = host.uuid
                delegate.state = "Enabled"
            }
            reconnectHost {
                delegate.uuid = host.uuid
            }
        }
        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
        }

        nvSpec = queryPciDeviceSpec {
            delegate.conditions = ["vendorId=10de", "deviceId=128b", "subvendorId=10de", "subdeviceId=118b"]
        }[0] as PciDeviceSpecInventory

        nvidiaDevice = queryPciDevice {
            delegate.conditions = ["vendorId=10de", "deviceId=128b", "subvendorId=10de", "subdeviceId=118b"]
        }[0] as PciDeviceInventory

        AIGlobalConfig.SERVICE_STARTUP_CHECK_INTERVAL_SECONDS.updateValue(1)

        BackupStorageInventory bs = env.inventoryByName("sftp")

        // add image to use different image cache
        def image = addImage {
            resourceUuid = "ac3d9582f1c9483d8ec9fc0b9bcbb51c"
            name = "qcow2"
            url  = "http://zstack.org/download/test.iso"
            format = "qcow2"
            backupStorageUuids = [bs.uuid]
        } as ImageInventory
    }

    void testAppDevelopmentServiceToCloud() {
        // query application model service
        List<ModelServiceInventory> modelServices = queryModelService {
            conditions = ["type=${ModelServiceType.App}".toString()]
        } as List<ModelServiceInventory>
        assert modelServices.size() == 3

        modelServices.each {
            assert Q.New(SharedResourceVO.class)
                    .eq(SharedResourceVO_.resourceUuid, it.uuid)
                    .eq(SharedResourceVO_.toPublic, true)
                    .isExists()
        }

        // query application deployment
        List<ApplicationDevelopmentServiceInventory> apps = queryApplicationDevelopmentService {
        } as List<ApplicationDevelopmentServiceInventory>
        assert apps.size() == 0

        int totalApplicationNumber = apps.size()

        // test deployment failure and application status rollback
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { rsp, HttpEntity<String> e ->
            throw new HttpError(504, "on purpose")
        }
        DeployAppDevelopmentServiceAction action = new DeployAppDevelopmentServiceAction()
        action.uuid = modelServices[0].uuid
        action.name = "test-application-failed-create-vm"
        action.zoneUuid = zone.uuid
        action.type = ModelServiceBackendType.VirtualMachine.toString()
        action.sessionId = adminSession()
        DeployAppDevelopmentServiceAction.Result result = action.call()
        assert result.error != null
        apps = queryApplicationDevelopmentService {
        } as List<ApplicationDevelopmentServiceInventory>
        apps.every {
            assert it.deploymentStatus == DeploymentStatus.UNDEPLOYED.toString()
            assert it.service.name != null
        }
        env.cleanAfterSimulatorHandlers()

        // test start vm hang and check application deploying status
        boolean hangOnStart = true
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { rsp, HttpEntity<String> e ->
            while (hangOnStart) {
                sleep(100)
            }

            return rsp
        }

        action = new DeployAppDevelopmentServiceAction()
        action.uuid = modelServiceInventory.uuid
        action.name = "test-application-develop-service"
        action.zoneUuid = zone.uuid
        action.type = ModelServiceBackendType.VirtualMachine.toString()
        action.sessionId = adminSession()
        result = null

        def dThread = Thread.start {
            result = action.call()
        }

        retryInSecs {
            assert queryApplicationDevelopmentService {
                conditions = ["deploymentStatus=${DeploymentStatus.UNDEPLOYED}"]
            }.size() == totalApplicationNumber

            assert queryApplicationDevelopmentService {
                conditions = ["deploymentStatus=${DeploymentStatus.DEPLOYING}"]
            }.size() == 1

            assert zqlQuery("query ApplicationDevelopmentService where deploymentStatus = '${DeploymentStatus.UNDEPLOYED}' or deploymentStatus = '${DeploymentStatus.DEPLOYING}'".toString())
                    .size() == 1

            assert queryApplicationDevelopmentService {
                conditions = ["deploymentStatus=${DeploymentStatus.DEPLOYED}"]
            }.size() == 0
        }

        hangOnStart = false
        dThread.join()
        assert result.error == null
        assert result.value.inventory != null
        assert result.value.app != null

        assert queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.UNDEPLOYED}"]
        }.size() == 0

        assert queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.DEPLOYING}"]
        }.size() == 0

        assert queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.DEPLOYED}"]
        }.size() == 1

        GetMaaSUsageResult maaSUsage = getMaaSUsage {

        }
        List<MaaSUsage> maasUsages = maaSUsage.usages
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.App.toString().toLowerCase()}.model.service.instance.group" && usage.value == 1 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.FineTune.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.Endpoint.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.ModelEval.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }

        ModelServiceInstanceGroupInventory group = result.value.inventory
        assert Q.New(AccountResourceRefVO.class).eq(AccountResourceRefVO_.resourceUuid, group.uuid).isExists()
        assert group.modelServiceUuid == modelServiceInventory.uuid
        assert group.instances.size() == 1
        assert (group.instances[0] as ModelServiceInstanceInventory).vm.vmNics.size() == 2
        assert group.status == ModelServiceInstanceGroupStatus.Running.toString()
        assert group.instances.every {
            it.status == ModelServiceInstanceGroupStatus.Running.toString()
        }
        assert group.type == ModelServiceBackendType.VirtualMachine.toString()
        assert group.name == "test-application-develop-service"

        ModelServiceInstanceInventory instance = group.instances.get(0)
        assert instance.getUrlMaps() != null
        assert instance.getUrlMaps().get("oneApiUrl") != null

        ApplicationDevelopmentServiceInventory app = result.value.app
        assert app.deploymentStatus == DeploymentStatus.DEPLOYED.toString()
        assert app.modelServiceUuid == group.modelServiceUuid
        assert app.uuid == group.uuid

        def livezCalled = false
        env.afterSimulator(generateUTMockPath("/")) { rsp, HttpEntity<String> e ->
            livezCalled = true
            return null
        }
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.updateValue(1)
        retryInSecs {
            assert livezCalled
        }
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.updateValue(60)

        ModelServiceInstanceInventory modelServiceInstanceInventory = group.instances.get(0)
        assert modelServiceInstanceInventory.internalUrl.contains("192.168.100")
        assert modelServiceInstanceInventory.url.contains("12.16.10")
        ModelServiceInstanceGroupInventory inv = updateModelServiceInstanceGroup {
            uuid = group.uuid
            name = "update-test-service-name"
            description = "update-test-service-description"
        } as ModelServiceInstanceGroupInventory

        assert inv.name == "update-test-service-name"
        assert inv.description == "update-test-service-description"

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        // query application deployment
        apps = queryApplicationDevelopmentService {
        } as List<ApplicationDevelopmentServiceInventory>
        apps.every {
            assert it.deploymentStatus == DeploymentStatus.UNDEPLOYED.toString()
        }

        // test create iam2 project
        IAM2ProjectInventory project = createIAM2Project {
            name = "application-project"
        } as IAM2ProjectInventory

        IAM2VirtualIDInventory vid = createIAM2VirtualID {
            name = "vid"
            password = "password"
        }

        addIAM2VirtualIDsToProject {
            virtualIDUuids = [vid.uuid]
            projectUuid = project.uuid
        }

        def session = loginIAM2VirtualID {
            name = "vid"
            password = "password"
        }

        session = loginIAM2Project {
            projectName = "application-project"
            sessionId = session.uuid
        }

        apps = queryApplicationDevelopmentService {
            sessionId = session.uuid
        } as List<ApplicationDevelopmentServiceInventory>
        apps.every {assert it.deploymentStatus == DeploymentStatus.UNDEPLOYED.toString() }
        assert apps.size() == totalApplicationNumber
        def projectAppUuids = apps.uuid
        assert projectAppUuids.size() == totalApplicationNumber

        assert queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.UNDEPLOYED}"]
            sessionId = session.uuid
        }.size() == totalApplicationNumber

        apps = queryApplicationDevelopmentService {
        } as List<ApplicationDevelopmentServiceInventory>
        apps.every {assert it.deploymentStatus == DeploymentStatus.UNDEPLOYED.toString() }
        assert apps.size() == totalApplicationNumber

        assert queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.UNDEPLOYED}"]
        }.size() == totalApplicationNumber

        projectAppUuids.removeAll(apps.uuid)
        assert projectAppUuids.size() == totalApplicationNumber

        addRolesToIAM2VirtualID {
            roleUuids = [IAM2RolePolicyStatementHelper.PROJECT_ADMIN_ROLE_UUID]
            virtualIDUuid = vid.uuid
            projectUuid = project.uuid
        }

        // deploy public model service
        action = new DeployAppDevelopmentServiceAction()
        action.uuid = "c6aab22271us44aqt44ih692fc5825cb"
        action.name = "test-application-develop-service"
        action.description = "test-application-develop-service-description"
        action.zoneUuid = zone.uuid
        action.type = ModelServiceBackendType.VirtualMachine.toString()
        action.sessionId = session.uuid
        result = action.call()
        assert result.error == null
        assert result.value.inventory != null
        assert result.value.inventory.modelServiceUuid == "c6aab22271us44aqt44ih692fc5825cb"
        assert result.value.inventory.description == "test-application-develop-service-description"

        // find application service by own session expect to be succeed
        apps = queryApplicationDevelopmentService {
            sessionId = session.uuid
        }
        action.uuid = apps[0].modelServiceUuid
        result = action.call()

        retryInSecs {
            assert result.value.inventory != null
            assert result.value.app != null
        }

        apps = queryApplicationDevelopmentService {
            conditions = ["uuid=${apps[0].uuid}"]
            sessionId = session.uuid
        } as List<ApplicationDevelopmentServiceInventory>
        apps.every {assert it.deploymentStatus == DeploymentStatus.DEPLOYED.toString() }
        assert apps.size() == 1

        maaSUsage = getMaaSUsage {
            sessionId = session.uuid
        }
        maasUsages = maaSUsage.usages
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.App.toString().toLowerCase()}.model.service.instance.group" && usage.value == 2 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.FineTune.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.Endpoint.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.ModelEval.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }

        maaSUsage = getMaaSUsage {
        }
        maasUsages = maaSUsage.usages
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.App.toString().toLowerCase()}.model.service.instance.group" && usage.value == 2 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.FineTune.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.Endpoint.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }
        assert maasUsages.any { usage -> usage.getName() == "${ModelServiceType.ModelEval.toString().toLowerCase()}.model.service.instance.group" && usage.value == 0 }


        assert queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.DEPLOYED}"]
            sessionId = session.uuid
        }.size() == 2

        deleteModelServiceInstanceGroup {
            uuid = result.value.inventory.uuid
            sessionId = session.uuid
        }

        assert queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.UNDEPLOYED}"]
            sessionId = session.uuid
        }.size() == totalApplicationNumber

        // test concurrent deploy one application should only
        // end with one instance group deployed
        apps = queryApplicationDevelopmentService {
            sessionId = session.uuid
        }
        assert apps.size() == 1
        assert apps.collect { it.modelServiceUuid }.toSet().size() == 1

        def groupSizeBefore = queryModelServiceInstanceGroup {

        }.size()

        for (int i = 0; i < 5; i++) {
            DeployAppDevelopmentServiceAction actionInThread = new DeployAppDevelopmentServiceAction()
            actionInThread.uuid = apps[0].modelServiceUuid
            actionInThread.name = "test-multi-threads-deployments"
            actionInThread.zoneUuid = zone.uuid
            actionInThread.type = ModelServiceBackendType.VirtualMachine.toString()
            actionInThread.sessionId = session.uuid
            result = actionInThread.call()
        }

        retryInSecs {
            apps = queryApplicationDevelopmentService {
                conditions = ["deploymentStatus=${DeploymentStatus.DEPLOYED}"]
                sessionId = session.uuid
            }
            assert apps.size() == 5
        }

        ModelServiceInstanceGroupInventory modelServiceGroup = queryModelServiceInstanceGroup {
            conditions = ["modelServiceUuid=${apps[0].modelServiceUuid}"]
            sessionId = session.uuid
        }[0] as ModelServiceInstanceGroupInventory
        assert modelServiceGroup.instances[0].vm.name != null

        // query by deploy type vm
        def groups = queryModelServiceInstanceGroup {
            conditions = ["type=${ModelServiceBackendType.VirtualMachine}".toString()]
        } as  List<ModelServiceInstanceGroupInventory>
        assert groups.size() == 5

        // query by deploy type container
        groups = queryModelServiceInstanceGroup {
            conditions = ["type=${ModelServiceBackendType.Container}".toString()]
        } as  List<ModelServiceInstanceGroupInventory>
        assert groups.size() == 0

        // query status in running
        groups = queryModelServiceInstanceGroup {
            conditions = ["status=${ModelServiceInstanceGroupStatus.Running}".toString()]
        } as  List<ModelServiceInstanceGroupInventory>
        assert groups.size() == 5

        // query status in stopped
        groups = queryModelServiceInstanceGroup {
            conditions = ["status=${ModelServiceInstanceGroupStatus.Unknown}".toString()]
        } as  List<ModelServiceInstanceGroupInventory>
        assert groups.size() == 0

        // zql query by admin account
        groups = zqlQuery("query  modelserviceinstancegroup where uuid in (query accountResourceRef.resourceUuid where accountUuid='${AccountConstant.INITIAL_SYSTEM_ADMIN_UUID}')".toString())
        assert groups.size() == 0

        // zql query by user
        groups = zqlQuery("query modelserviceinstancegroup where uuid in (query accountResourceRef.resourceUuid where accountUuid='${session.accountUuid}')".toString())
        assert groups.size() == 5

        // zql query by model service name
        groups = zqlQuery("query modelserviceinstancegroup where modelServiceType='App' and modelServiceUuid in (query modelservice.uuid where uuid='${apps[0].modelServiceUuid}')".toString())
        assert groups.size() == 5

        // zql query by model service name
        groups = zqlQuery("query modelserviceinstancegroup where modelServiceType='App' and modelServiceUuid in (query modelservice.uuid where name='test')".toString())
        assert groups.size() == 0

        // delete all groups
        groups = queryModelServiceInstanceGroup {
        } as List<ModelServiceInstanceGroupInventory>

        assert groups.size() > 1
        deleteModelServiceInstanceGroups {
            uuids = groups.uuid
        }

        // deploy with container
        env.message(DeleteDeploymentMsg) { DeleteDeploymentMsg msg, CloudBus bus ->
            def reply = new DeleteDeploymentReply()
            bus.reply(msg, reply)
        }

        env.message(DeleteKubernetesServiceMsg) { DeleteKubernetesServiceMsg msg, CloudBus bus ->
            def reply = new DeleteKubernetesServiceReply()
            bus.reply(msg, reply)
        }

        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)
            bus.reply(msg, reply)
        }

        env.message(CreateKubernetesServiceMsg) { CreateKubernetesServiceMsg msg, CloudBus bus ->
            def reply = new CreateKubernetesServiceReply()
            def service = msg.getService()

            def serviceName = service.getMetadata().getName()
            def status = new V1ServiceStatus()
            def loadBalancerStatus = new V1LoadBalancerStatus()
            def ingress = Collections.singletonList(
                    new V1LoadBalancerIngress().ip("**********")
            )
            if (serviceName.contains('storage')) {
                ingress = Collections.singletonList(
                        new V1LoadBalancerIngress().ip("**********")
                )
                reply.setUrl("http://**********:3000")
            } else {
                reply.setUrl("http://**********:3000")
            }
            loadBalancerStatus.setIngress(ingress)
            status.setLoadBalancer(loadBalancerStatus)
            service.setStatus(status)
            reply.setService(service)
            bus.reply(msg, reply)
        }

        apps = queryApplicationDevelopmentService {
        } as List<ApplicationDevelopmentServiceInventory>
        DeployAppDevelopmentServiceAction actionInThread = new DeployAppDevelopmentServiceAction()
        actionInThread.uuid = "c6aab22271us44aqt44ih692fc5825cb"
        actionInThread.name = "test-app-deployment-with-container"
        actionInThread.zoneUuid = zone.uuid
        actionInThread.type = ModelServiceBackendType.Container.toString()
        actionInThread.sessionId = adminSession()
        result = actionInThread.call()
        assert result.error == null
        apps = queryApplicationDevelopmentService {
            conditions = ["deploymentStatus=${DeploymentStatus.DEPLOYED}"]
            sessionId = adminSession()
        }
        assert apps.size() == 1
        deleteModelServiceInstanceGroup {
            uuid = apps[0].uuid
        }

        // check admin query apps is 3
        apps = queryApplicationDevelopmentService {
        } as List<ApplicationDevelopmentServiceInventory>
        assert apps.size() == totalApplicationNumber

        deleteIAM2Project {
            uuid = project.uuid
        }
        expungeIAM2Project {
            uuid = project.uuid
        }

        // check project's apps will be deleted not adopt by admin
        apps = queryApplicationDevelopmentService {
        } as List<ApplicationDevelopmentServiceInventory>
        assert apps.size() == totalApplicationNumber
    }

    @Override
    void clean() {
        env.delete()
    }
}
