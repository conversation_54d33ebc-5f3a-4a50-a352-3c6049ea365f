package org.zstack.test.integration.premium.storage.volume

import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.Q
import org.zstack.core.thread.ThreadFacadeImpl
import org.zstack.header.host.ResizeVolumeOnKvmMsg
import org.zstack.header.host.ResizeVolumeOnHypervisorReply
import org.zstack.header.storage.primary.AllocatePrimaryStorageSpaceMsg
import org.zstack.header.storage.primary.AllocatePrimaryStorageSpaceReply
import org.zstack.header.storage.primary.TakeSnapshotMsg
import org.zstack.header.storage.primary.TakeSnapshotReply
import org.zstack.header.storage.snapshot.CreateVolumeSnapshotMsg
import org.zstack.header.storage.snapshot.CreateVolumeSnapshotReply
import org.zstack.header.storage.snapshot.VolumeSnapshotInventory
import org.zstack.header.storage.snapshot.VolumeSnapshotState
import org.zstack.header.storage.snapshot.VolumeSnapshotStatus
import org.zstack.header.vm.MigrateVmMsg
import org.zstack.header.vm.MigrateVmReply
import org.zstack.header.vm.StartVmOnHypervisorMsg
import org.zstack.header.vm.StartVmOnHypervisorReply
import org.zstack.header.storage.primary.ResizeVolumeOnPrimaryStorageMsg
import org.zstack.header.storage.primary.ResizeVolumeOnPrimaryStorageReply
import org.zstack.header.volume.SyncVolumeSizeMsg
import org.zstack.header.volume.SyncVolumeSizeReply
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.kvm.KVMSystemTags
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.ResizeDataVolumeAction
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VolumeInventory
import org.zstack.storage.volume.VolumeSystemTags
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.SizeUtils
import org.zstack.utils.data.SizeUnit

/**
 * Created by camile on 17-11-7.
 */
class ResizeDataVolumeBaseCase extends PremiumSubCase {

    EnvSpec env
    VmInstanceInventory vm
    VmInstanceInventory vm2
    VolumeInventory shareableVolume
    VolumeInventory dataVolume
    PrimaryStorageInventory ps
    DiskOfferingInventory diskOffering
    ThreadFacadeImpl thdf
    long _size

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
            }
            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(20)
                systemTags = ["volumeTotalBandwidth::10485760"]
            }
            zone {
                name = "zone"
                cluster {
                    name = "test-cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "ceph-mon"
                        managementIp = "localhost"
                        username = "root"
                        password = "password"
                        usedMem = 1000
                        totalCpu = 10
                    }
                    kvm {
                        name = "host"
                        username = "root"
                        password = "password"
                        usedMem = 1000
                        totalCpu = 10
                    }

                    attachPrimaryStorage("ceph-pri")
                    attachL2Network("l2")
                }
                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"



                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }
                }

                cephPrimaryStorage {
                    name = "ceph-pri"
                    description = "Test"
                    totalCapacity = SizeUnit.GIGABYTE.toByte(300)
                    availableCapacity = SizeUnit.GIGABYTE.toByte(300)
                    url = "ceph://pri"
                    fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                    monUrls = ["root:password@localhost/?monPort=7777"]

                }


                attachBackupStorage("ceph-bk")
            }

            cephBackupStorage {
                name = "ceph-bk"
                description = "Test"
                totalCapacity = SizeUnit.GIGABYTE.toByte(300)
                availableCapacity = SizeUnit.GIGABYTE.toByte(300)
                url = "/bk"
                fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                monUrls = ["root:password@localhost/?monPort=7777"]

                image {
                    name = "test-iso"
                    url = "http://zstack.org/download/test.iso"
                }
                image {
                    name = "image"
                    url = "http://zstack.org/download/image.qcow2"
                }
            }

            vm {
                name = "vm1"
                useCluster("test-cluster")
                useHost("host")
                useL3Networks("l3")
                useInstanceOffering("instanceOffering")
                useRootDiskOffering("diskOffering")
                useImage("image")

            }

            vm {
                name = "vm2"
                useCluster("test-cluster")
                useHost("host")
                useL3Networks("l3")
                useInstanceOffering("instanceOffering")
                useRootDiskOffering("diskOffering")
                useImage("image")

            }
        }
    }

    @Override
    void test() {
        env.create {
            init()
            testResizeShareableVolumeSuccess()
            testResizeShareableVolumeSuccessAfterAttached()
            testResizeShareableVolumeSuccessBecaseAttachedVmStopped()
            testResizeDataVolumeSuccess()
            testResizeDataVolumeWhenVmRunningSuccess()
            testResizeVolumeAttachedMultipleVm()
            testResizeRootVolumeAndCreateSnapshotTaskInSyncChain()
            testResizeRootVolumeAndStartVmInSyncChain()
        }

    }

    void testResizeShareableVolumeSuccess() {
        String tag = VolumeSystemTags.SHAREABLE.getTagFormat()
        String tag2 = KVMSystemTags.VOLUME_VIRTIO_SCSI.getTagFormat()
        ps = env.inventoryByName("ceph-pri") as PrimaryStorageInventory
        diskOffering = env.inventoryByName("diskOffering") as DiskOfferingInventory
        vm = env.inventoryByName("vm1")
        vm2 = env.inventoryByName("vm2")

        shareableVolume = createDataVolume {
            name = "share"
            diskOfferingUuid = diskOffering.uuid
            systemTags = Arrays.asList(tag, tag2)
            primaryStorageUuid = ps.uuid
        }

        _size = SizeUtils.sizeStringToBytes("21G")

        resizeDataVolume {
            uuid = shareableVolume.uuid
            size = _size
        }

        assert (dbFindByUuid(shareableVolume.uuid, VolumeVO.class) as VolumeVO).size == _size
    }

    void testResizeShareableVolumeSuccessAfterAttached() {
        attachDataVolumeToVm {
            vmInstanceUuid = vm.uuid
            volumeUuid = shareableVolume.uuid
        }

        _size = SizeUtils.sizeStringToBytes("22G")

        ResizeDataVolumeAction resizeDataVolume = new ResizeDataVolumeAction()
        resizeDataVolume.uuid = shareableVolume.uuid
        resizeDataVolume.size = _size
        resizeDataVolume.sessionId = adminSession()
        assert resizeDataVolume.call().error != null
        assert (dbFindByUuid(shareableVolume.uuid, VolumeVO.class) as VolumeVO).size != _size
    }

    void testResizeShareableVolumeSuccessBecaseAttachedVmStopped() {
        stopVmInstance {
            uuid = vm.uuid
        }

        _size = SizeUtils.sizeStringToBytes("23G")
        ResizeDataVolumeAction resizeDataVolume = new ResizeDataVolumeAction()
        resizeDataVolume.uuid = shareableVolume.uuid
        resizeDataVolume.size = _size
        resizeDataVolume.sessionId = adminSession()
        assert resizeDataVolume.call().error == null
        assert (dbFindByUuid(shareableVolume.uuid, VolumeVO.class) as VolumeVO).size == _size
    }

    void testResizeDataVolumeSuccess() {
        dataVolume = createDataVolume {
            name = "dataVolume"
            diskOfferingUuid = diskOffering.uuid
            primaryStorageUuid = ps.uuid
        }

        _size = SizeUtils.sizeStringToBytes("24G")

        resizeDataVolume {
            uuid = dataVolume.uuid
            size = _size
        }
        assert (dbFindByUuid(dataVolume.uuid, VolumeVO.class) as VolumeVO).size == _size
    }

    void testResizeDataVolumeFailed() {
        env.simulator(MevocoKVMConstant.RESIZE_VOLUME) {
            def rsp = new MevocoKVMAgentCommands.ResizeVolumeResponse()
            rsp.success = false
            return rsp
        }

        def volumeSize = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, dataVolume.uuid).select(VolumeVO_.size).findValue()

        expect(AssertionError.class) {
            resizeDataVolume {
                uuid = dataVolume.uuid
                size = 12345678L
            }
        }
        assert Q.New(VolumeVO.class).eq(VolumeVO_.uuid, dataVolume.uuid).select(VolumeVO_.size).findValue() == volumeSize

        env.simulator(MevocoKVMConstant.RESIZE_VOLUME) {
            return new MevocoKVMAgentCommands.ResizeVolumeResponse()
        }
    }

    void testResizeDataVolumeWhenVmRunningSuccess() {
        startVmInstance {
            uuid = vm.uuid
        }

        attachDataVolumeToVm {
            vmInstanceUuid = vm.uuid
            volumeUuid = dataVolume.uuid
        }
        _size = SizeUtils.sizeStringToBytes("25G")

        ResizeDataVolumeAction resizeDataVolume = new ResizeDataVolumeAction()
        resizeDataVolume.uuid = dataVolume.uuid
        resizeDataVolume.size = _size
        resizeDataVolume.sessionId = adminSession()
        assert resizeDataVolume.call().error == null
        assert (dbFindByUuid(dataVolume.uuid, VolumeVO.class) as VolumeVO).size == _size
    }

    void testResizeVolumeAttachedMultipleVm() {
        attachDataVolumeToVm {
            vmInstanceUuid = vm2.uuid
            volumeUuid = shareableVolume.uuid
        }

        stopVmInstance {
            uuid = vm.uuid
        }

        _size = SizeUtils.sizeStringToBytes("24G")

        ResizeDataVolumeAction resizeDataVolume = new ResizeDataVolumeAction()
        resizeDataVolume.uuid = shareableVolume.uuid
        resizeDataVolume.size = _size
        resizeDataVolume.sessionId = adminSession()
        assert resizeDataVolume.call().error != null
        assert (dbFindByUuid(shareableVolume.uuid, VolumeVO.class) as VolumeVO).size != _size

        stopVmInstance {
            uuid = vm2.uuid
        }
        _size = SizeUtils.sizeStringToBytes("25G")
        resizeDataVolume = new ResizeDataVolumeAction()
        resizeDataVolume.uuid = shareableVolume.uuid
        resizeDataVolume.size = _size
        resizeDataVolume.sessionId = adminSession()
        assert resizeDataVolume.call().error == null
        assert (dbFindByUuid(shareableVolume.uuid, VolumeVO.class) as VolumeVO).size == _size
    }

    void testResizeRootVolumeAndCreateSnapshotTaskInSyncChain() {
        _size = SizeUtils.sizeStringToBytes("30G")
        long handleCreateSnapshotMsgTime = 0,
             replyCreateSnapshotMsgTime = 0,
             handleResizeVolumeMsgTime = 0,
             replyResizeVolumeMsgTime = 0

        env.message(ResizeVolumeOnPrimaryStorageMsg.class) { ResizeVolumeOnPrimaryStorageMsg msg, CloudBus bus ->
            handleResizeVolumeMsgTime = System.currentTimeMillis()
            ResizeVolumeOnPrimaryStorageReply reply = new ResizeVolumeOnPrimaryStorageReply()
            msg.volume.size = msg.size
            msg.volume.primaryStorageUuid = msg.primaryStorageUuid
            reply.setVolume(msg.volume)
            replyResizeVolumeMsgTime = System.currentTimeMillis()
            bus.reply(msg, reply)
        }

        env.message(TakeSnapshotMsg.class) { TakeSnapshotMsg msg, CloudBus bus ->
            handleCreateSnapshotMsgTime = System.currentTimeMillis()
            VolumeSnapshotInventory inventory = new VolumeSnapshotInventory()
            TakeSnapshotReply reply = new TakeSnapshotReply()
            inventory.setUuid(Platform.getUuid())
            inventory.setName("test")
            inventory.setDescription("test-description")
            inventory.setVolumeUuid(Platform.getUuid())
            inventory.treeUuid = msg.struct.current.uuid
            inventory.treeUuid = msg.struct.parent.uuid
            inventory.setFormat(dataVolume.format)
            inventory.primaryStorageUuid = ps.uuid
            inventory.setState(VolumeSnapshotState.Enabled.toString())
            inventory.setStatus(VolumeSnapshotStatus.Ready.toString())
            inventory.setVolumeType(dataVolume.type)

            reply.setInventory(inventory)
            reply.newVolumeInstallPath = "/test/snapshot"
            replyCreateSnapshotMsgTime = System.currentTimeMillis()
            bus.reply(msg, reply)
        }

        def threads = []
        def thread1 = Thread.start {
            resizeDataVolume {
                uuid = dataVolume.uuid
                size = _size
            }
        }
        def thread2 = Thread.start {
            createVolumeSnapshot {
                volumeUuid = dataVolume.uuid
                name = "test"
                description = "test-description"
            }
        }
        threads.add(thread1)
        threads.add(thread2)
        threads.each { it.join() }

        assert (handleCreateSnapshotMsgTime > replyResizeVolumeMsgTime) || (handleResizeVolumeMsgTime > replyCreateSnapshotMsgTime): "create snapshot task and resize volume task does not execute in parallel"
    }

    void testResizeRootVolumeAndStartVmInSyncChain() {
        stopVmInstance {
            uuid = vm.uuid
        }

        _size = SizeUtils.sizeStringToBytes("40G")
        long handleStartMsgTime = 0,
             replyStartMsgTime = 0,
             handleResizeVolumeMsgTime = 0,
             replyResizeVolumeMsgTime = 0

        env.message(StartVmOnHypervisorMsg.class) { StartVmOnHypervisorMsg msg, CloudBus bus ->
            handleResizeVolumeMsgTime = System.currentTimeMillis()
            StartVmOnHypervisorReply reply = new StartVmOnHypervisorReply()
            replyResizeVolumeMsgTime = System.currentTimeMillis()
            bus.reply(msg, reply)
        }

        env.message(MigrateVmMsg.class) { MigrateVmMsg msg, CloudBus bus ->
            handleStartMsgTime = System.currentTimeMillis()
            MigrateVmReply reply = new MigrateVmReply()
            replyStartMsgTime = System.currentTimeMillis()
            bus.reply(msg, reply)
        }

        def threads = []
        def thread1 = Thread.start {
            resizeRootVolume {
                uuid = dataVolume.uuid
                size = _size
            }
        }
        def thread2 = Thread.start {
            startVmInstance {
                uuid = vm.uuid
            }
        }
        threads.add(thread1)
        threads.add(thread2)
        threads.each { it.join() }

        assert (handleStartMsgTime > replyResizeVolumeMsgTime) || (handleResizeVolumeMsgTime > replyStartMsgTime): "migrate vm task and resize volume task does not execute in parallel"
    }

    @Override
    void clean() {
        env.delete()
    }

    private void init() {
        initBean()
        hookMsg()
    }

    private void initBean() {
        thdf = bean(ThreadFacadeImpl.class)
    }

    private void hookMsg() {
        env.message(CreateVolumeSnapshotMsg.class) { CreateVolumeSnapshotMsg msg, CloudBus bus ->
            CreateVolumeSnapshotReply reply = new CreateVolumeSnapshotReply()
            bus.reply(msg, reply)
        }
        env.message(ResizeVolumeOnPrimaryStorageMsg.class) { ResizeVolumeOnPrimaryStorageMsg msg, CloudBus bus ->
            ResizeVolumeOnPrimaryStorageReply reply = new ResizeVolumeOnPrimaryStorageReply()
            msg.volume.size = msg.size
            msg.volume.primaryStorageUuid = msg.primaryStorageUuid
            reply.setVolume(msg.volume)
            bus.reply(msg, reply)
        }

        env.message(ResizeVolumeOnKvmMsg.class) { ResizeVolumeOnKvmMsg msg, CloudBus bus ->
            ResizeVolumeOnHypervisorReply reply = new ResizeVolumeOnHypervisorReply()
            bus.reply(msg, reply)
        }

        env.message(SyncVolumeSizeMsg.class) { SyncVolumeSizeMsg msg, CloudBus bus ->
            SyncVolumeSizeReply reply = new SyncVolumeSizeReply()
            reply.size = _size
            bus.reply(msg, reply)
        }
    }
}