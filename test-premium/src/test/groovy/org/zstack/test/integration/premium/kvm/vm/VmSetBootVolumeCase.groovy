package org.zstack.test.integration.premium.kvm.vm

import org.springframework.http.HttpEntity
import org.zstack.core.db.Q
import org.zstack.header.storage.snapshot.VolumeSnapshotVO
import org.zstack.header.storage.snapshot.VolumeSnapshotVO_
import org.zstack.header.storage.snapshot.group.VolumeSnapshotGroupRefVO
import org.zstack.header.storage.snapshot.group.VolumeSnapshotGroupRefVO_
import org.zstack.header.vm.VmInstanceVO
import org.zstack.header.vm.VmInstanceVO_
import org.zstack.header.volume.VolumeType
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VolumeInventory
import org.zstack.sdk.VolumeSnapshotGroupInventory
import org.zstack.sdk.VolumeSnapshotGroupRefInventory
import org.zstack.storage.primary.local.LocalStorageKvmBackend
import org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackend
import org.zstack.storage.primary.smp.KvmBackend
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.woodpecker.subsystem.storage.VFileNode
import org.zstack.woodpecker.subsystem.storage.primarystorage.local.LocalStorageView
import org.zstack.woodpecker.subsystem.storage.primarystorage.nfs.NfsStorageView

/**
 * Created by MaJin on 2021/1/7.
 */
class VmSetBootVolumeCase extends PremiumSubCase {
    EnvPremiumSpec env
    VmInstanceInventory vmOnLocal, vmOnNfs
    VolumeInventory originRootVolOnLocal, dataVolOnLocal
    VolumeInventory originRootVolOnNfs, dataVolOnNfs
    Map<String, VolumeSnapshotGroupRefInventory> snapshots = new HashMap<>()
    Map<String, VolumeInventory> volumes = new HashMap<>()

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = makeEnv(PremiumEnv.twoClusterLocalAndNfsEnv()) {
            vm {
                name = "nfs-vm"
                useCluster("cluster-2")
                useL3Networks("l3")
                useInstanceOffering("instanceOffering")
                useRootDiskOffering("diskOffering")
                useDiskOfferings("diskOffering")
                useImage("image")
            }
        } as EnvPremiumSpec
    }

    @Override
    void test() {
        env.create {
            vmOnLocal = env.inventoryByName("test-vm-1") as VmInstanceInventory
            vmOnNfs = env.inventoryByName("nfs-vm") as VmInstanceInventory
            prepareLocalStorageEnv()
            prepareNfsStorageEnv()
            setBootVolumeOnRunningVm()
            setBootVolumeOnStoppedVm(vmOnLocal, originRootVolOnLocal, dataVolOnLocal)
            env.viewCheck()
            setBootVolumeOnStoppedVm(vmOnNfs, originRootVolOnNfs, dataVolOnNfs)
            env.viewCheck()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    void prepareLocalStorageEnv() {
        for (vol in vmOnLocal.allVolumes) {
            if (vol.uuid == vmOnLocal.rootVolumeUuid) {
                originRootVolOnLocal = vol
                volumes.put(originRootVolOnLocal.uuid, originRootVolOnLocal)
            } else {
                dataVolOnLocal = vol
                volumes.put(dataVolOnLocal.uuid, dataVolOnLocal)
            }
        }

        def group = createVolumeSnapshotGroup {
            name = "test"
            rootVolumeUuid = vmOnLocal.rootVolumeUuid
        } as VolumeSnapshotGroupInventory

        group.volumeSnapshotRefs.forEach{snapshot -> snapshots.put(snapshot.volumeUuid, snapshot)}

        def vols = queryVolume {} as List<VolumeInventory>
        vols.forEach{vol -> volumes.put(vol.uuid, vol)}
    }

    void prepareNfsStorageEnv() {
        for (vol in vmOnNfs.allVolumes) {
            if (vol.uuid == vmOnNfs.rootVolumeUuid) {
                originRootVolOnNfs = vol
                volumes.put(originRootVolOnNfs.uuid, originRootVolOnNfs)
            } else {
                dataVolOnNfs = vol
                volumes.put(dataVolOnNfs.uuid, dataVolOnNfs)
            }
        }

        def group = createVolumeSnapshotGroup {
            name = "test"
            rootVolumeUuid = vmOnNfs.rootVolumeUuid
        } as VolumeSnapshotGroupInventory

        group.volumeSnapshotRefs.forEach{snapshot -> snapshots.put(snapshot.volumeUuid, snapshot)}

        def vols = queryVolume {} as List<VolumeInventory>
        vols.forEach{vol -> volumes.put(vol.uuid, vol)}
    }

    void setBootVolumeOnRunningVm() {
        setVmBootVolume {
            vmInstanceUuid = vmOnLocal.uuid
            volumeUuid = originRootVolOnLocal.uuid
        }
        assertRootVolumeIs(originRootVolOnLocal.uuid)
        assertBootFrom(originRootVolOnLocal.uuid)
        rebootVmInstance {
            uuid = vmOnLocal.uuid
        }
        assertRootVolumeIs(originRootVolOnLocal.uuid)

        expectError {
            setVmBootVolume {
                vmInstanceUuid = vmOnLocal.uuid
                volumeUuid = dataVolOnLocal
            }
        }

        assertRootVolumeIs(originRootVolOnLocal.uuid)
        assertBootFrom(originRootVolOnLocal.uuid)
        rebootVmInstance {
            uuid = vmOnLocal.uuid
        }
    }


    void setBootVolumeOnStoppedVm(VmInstanceInventory vm, VolumeInventory originRootVol, dataVol) {
        stopVmInstance {
            uuid = vm.uuid
        }

        assertRootVolumeIs(originRootVol.uuid)
        setVmBootVolume {
            vmInstanceUuid = vm.uuid
            volumeUuid = originRootVol.uuid
        }
        assertRootVolumeIs(originRootVol.uuid)

        simulatorLinkErrorFinally()
        expectError {
            setVmBootVolume {
                vmInstanceUuid = vm.uuid
                volumeUuid = dataVol.uuid
            }
        }

        env.cleanFinalSimulatorHandlers()
        setVmBootVolume {
            vmInstanceUuid = vm.uuid
            volumeUuid = dataVol.uuid
        }

        assertRootVolumeIs(dataVol.uuid)
        assertBootFrom(dataVol.uuid)
    }

    void assertBootFrom(String volumeUuid) {
        env.simulator(KVMConstant.KVM_START_VM_PATH){ HttpEntity<String> e ->
            KVMAgentCommands.StartVmCmd cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.StartVmCmd.class)
            assert cmd.rootVolume.volumeUuid == volumeUuid
            assert cmd.rootVolume.deviceId == 0
            return new KVMAgentCommands.StartVmResponse()
        }
    }

    void assertRootVolumeIs(String volumeUuid) {
        String psUuid
        if (volumeUuid == dataVolOnLocal.uuid || volumeUuid == originRootVolOnLocal.uuid) {
            psUuid = dataVolOnLocal.primaryStorageUuid
        } else {
            psUuid = dataVolOnNfs.primaryStorageUuid
        }

        assert Q.New(VolumeVO.class).select(VolumeVO_.uuid)
                .eq(VolumeVO_.type, VolumeType.Root)
                .eq(VolumeVO_.primaryStorageUuid, psUuid)
                .findValue() == volumeUuid

        assert Q.New(VolumeSnapshotVO.class).select(VolumeSnapshotVO_.volumeUuid)
                .eq(VolumeSnapshotVO_.volumeType, VolumeType.Root.toString())
                .eq(VolumeVO_.primaryStorageUuid, psUuid)
                .findValue() == volumeUuid

        assert Q.New(VmInstanceVO.class).select(VmInstanceVO_.rootVolumeUuid)
                .listValues().contains(volumeUuid)

        String volPath = Q.New(VolumeVO.class).select(VolumeVO_.installPath)
                .eq(VolumeVO_.primaryStorageUuid, psUuid)
                .eq(VolumeVO_.type, VolumeType.Root)
                .findValue()
        assert volumes[volumeUuid].installPath.replace("dataVolumes", "rootVolumes") == volPath
        String snapshotPath = Q.New(VolumeSnapshotVO.class).select(VolumeSnapshotVO_.primaryStorageInstallPath)
                .eq(VolumeSnapshotVO_.volumeType, VolumeType.Root.toString())
                .eq(VolumeSnapshotVO_.primaryStorageUuid, psUuid)
                .findValue()
        assert snapshots[volumeUuid].volumeSnapshotInstallPath.replace("dataVolumes", "rootVolumes") == snapshotPath
        String snapshotGroupRefPath = Q.New(VolumeSnapshotGroupRefVO.class).select(VolumeSnapshotGroupRefVO_.volumeSnapshotInstallPath)
                .eq(VolumeSnapshotGroupRefVO_.volumeUuid, volumeUuid)
                .eq(VolumeSnapshotGroupRefVO_.volumeType, VolumeType.Root.toString())
                .findValue()
        assert snapshotGroupRefPath == snapshotPath
    }

    private void simulatorLinkErrorFinally() {
        env.hijackSimulator(LocalStorageKvmBackend.HARD_LINK_VOLUME) { rsp, HttpEntity<String> e ->
            throw new Exception("on purpose")
        }

        env.hijackSimulator(NfsPrimaryStorageKVMBackend.HARD_LINK_VOLUME) { rsp, HttpEntity<String> e ->
            throw new Exception("on purpose")
        }

        env.hijackSimulator(KvmBackend.HARD_LINK_VOLUME) { rsp, HttpEntity<String> e ->
            throw new Exception("on purpose")
        }
    }
}
