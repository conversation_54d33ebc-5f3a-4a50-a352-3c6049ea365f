package org.zstack.test.integration.aliyun.storage.nas

import org.zstack.aliyun.nas.filesystem.AliyunMountTargetStatus
import org.zstack.aliyun.nas.filesystem.AliyunNasAccessGroupVO
import org.zstack.aliyun.nas.filesystem.AliyunNasAccessRule
import org.zstack.aliyun.nas.filesystem.AliyunNasAccessRuleVO
import org.zstack.aliyun.nas.filesystem.AliyunNasFileSystemVO
import org.zstack.aliyun.nas.filesystem.AliyunNasMountTargetVO
import org.zstack.aliyun.nas.message.AliyunNasAccessGroupProperty
import org.zstack.aliyun.nas.message.AliyunNasAccessRuleProperty
import org.zstack.aliyun.nas.message.AliyunNasFileSystemProperty
import org.zstack.aliyun.nas.message.AliyunNasMountTargetProperty
import org.zstack.aliyun.nas.message.GetAliyunNasAccessGroupMsg
import org.zstack.aliyun.nas.message.GetAliyunNasAccessGroupReply
import org.zstack.aliyun.nas.message.GetAliyunNasAccessRuleMsg
import org.zstack.aliyun.nas.message.GetAliyunNasAccessRuleReply
import org.zstack.aliyun.nas.message.GetAliyunNasFileSystemMsg
import org.zstack.aliyun.nas.message.GetAliyunNasFileSystemReply
import org.zstack.aliyun.nas.message.GetAliyunNasMountTargetMsg
import org.zstack.aliyun.nas.message.GetAliyunNasMountTargetReply
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.Q
import org.zstack.license.LicenseType
import org.zstack.sdk.DataCenterInventory
import org.zstack.test.integration.aliyun.storage.AliyunStorageBaseEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
/**
 * Created by mingjian.deng on 2018/5/11.
 */
class AliyunNasSyncCase extends PremiumSubCase {
    EnvSpec env
    DataCenterInventory dc

    @Override
    void environment() {
        env = AliyunStorageBaseEnv.createDatacenter()
    }

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
        spring {
            hybrid()
            plugins()
        }
    }

    @Override
    void test() {
        env.create {
            prepare()
            mock1()
            testSyncDataCenter(1)
            mock2()
            testSyncDataCenter(0)
        }
    }

    void prepare() {
        dc = env.inventoryByName("guizhou") as DataCenterInventory
    }

    void mock1() {
        env.message(GetAliyunNasFileSystemMsg.class) { GetAliyunNasFileSystemMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasFileSystemReply()
            def property = new AliyunNasFileSystemProperty()
            property.description = "description"
            property.fileSystemId = "123456789"
            property.storageType = "Capacity"
            property.protocol = "NFS"
            property.createDate = "2017-05-27T15:43:06CST"
            reply.propertyList = [property]
            bus.reply(msg, reply)
        }

        env.message(GetAliyunNasMountTargetMsg.class) { GetAliyunNasMountTargetMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasMountTargetReply()
            def target = new AliyunNasMountTargetProperty()
            target.mountDomain = "nas.cn-shanghai.com"
            target.status = AliyunMountTargetStatus.Active.name()
            target.accessGroupName = "access"
            reply.propertyList = [target]
            bus.reply(msg, reply)
        }

        env.message(GetAliyunNasAccessGroupMsg.class) { GetAliyunNasAccessGroupMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasAccessGroupReply()
            def property = new AliyunNasAccessGroupProperty()
            property.ruleCount = 1
            property.name = "access"
            property.description = "description2"
            property.networkType = "VPC"
            reply.propertyList = [property]
            bus.reply(msg, reply)
        }

        env.message(GetAliyunNasAccessRuleMsg.class) { GetAliyunNasAccessRuleMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasAccessRuleReply()
            def rule = new AliyunNasAccessRuleProperty()
            rule.cidr = "***********/16"
            rule.priority = 2
            rule.rule = AliyunNasAccessRule.RDWR.name()
            rule.ruleId = "1"
            rule.userAccess = "no_squash"
            reply.propertyList = [rule]
            bus.reply(msg, reply)
        }
    }

    void mock2() {
        env.message(GetAliyunNasFileSystemMsg.class) { GetAliyunNasFileSystemMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasFileSystemReply()
            bus.reply(msg, reply)
        }

        env.message(GetAliyunNasMountTargetMsg.class) { GetAliyunNasMountTargetMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasMountTargetReply()
            bus.reply(msg, reply)
        }

        env.message(GetAliyunNasAccessGroupMsg.class) { GetAliyunNasAccessGroupMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasAccessGroupReply()
            bus.reply(msg, reply)
        }

        env.message(GetAliyunNasAccessRuleMsg.class) { GetAliyunNasAccessRuleMsg msg, CloudBus bus ->
            def reply = new GetAliyunNasAccessRuleReply()
            bus.reply(msg, reply)
        }
    }

    void testSyncDataCenter(int count) {
        syncDataCenterFromRemote {
            uuid = dc.uuid
        }

        assert Q.New(AliyunNasFileSystemVO.class).count() == count
        assert Q.New(AliyunNasMountTargetVO.class).count() == count
        assert Q.New(AliyunNasAccessGroupVO.class).count() == count
        assert Q.New(AliyunNasAccessRuleVO.class).count() == count
    }
}
