package org.zstack.test.integration.premium.license

import org.zstack.core.Platform
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.allocator.HostCapacityVO
import org.zstack.header.allocator.HostCapacityVO_
import org.zstack.license.*
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.utils.data.SizeUnit

import java.time.OffsetDateTime

class CheckArmLicenseCase extends LicenseCaseStub {
    EnvSpec env
    DatabaseFacade dbf
    LicenseInfo arm64
    LicenseManagerImpl licenseManager

    @Override
    void clean() {
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = makeEnv {
            zone {
                name = "zone"

                bareMetal2ProvisionNetwork {
                    name = "provision_net_1"
                    dhcpInterface = "enp0s0f0"
                    dhcpRangeStartIp = "***********0"
                    dhcpRangeEndIp = "*************"
                    dhcpRangeNetmask = "*************"
                    dhcpRangeGateway = "***********"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"
                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }
                }

                sbgPrimaryStorage {
                    name = "sharedblock-ps-1"
                    description = "Test"
                    diskUuids = ["0121520F-55D2-4541-9345-887B9074A157"]
                }

                cluster {
                    name = "x86_cluster_1"
                    hypervisorType = "KVM"

                    kvm {
                        name = "x86_host_1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(100)
                        totalCpu = 32
                        cpuSockets = 2
                    }

                    kvm {
                        name = "x86_host_2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(100)
                        totalCpu = 32
                        cpuSockets = 2
                    }

                    attachPrimaryStorage("sharedblock-ps-1")
                    attachL2Network("l2")
                }

                cluster {
                    name = "arm64_cluster_1"
                    hypervisorType = "KVM"
                    architecture = "aarch64"

                    kvm {
                        name = "arm64_host_1"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(100)
                        totalCpu = 32
                        cpuSockets = 2
                    }

                    attachPrimaryStorage("sharedblock-ps-1")
                    attachL2Network("l2")
                }

                bareMetal2Cluster {
                    name = "bm2_cluster_1"

                    bareMetal2Gateway {
                        name = "gateway-1"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(100)
                        totalCpu = 32
                        cpuSockets = 2
                    }

                    bareMetal2Chassis {
                        name = "chassis_1"
                        ipmiAddress = "*********"
                        ipmiUsername = "username"
                        ipmiPassword = "password"

                        hardwareInfo {
                            info = "{'bootMode':'UEFI', 'architecture':'x86_64', 'cpuModelName':'Intel i7-6700K', 'cpuNum':'8', 'memorySize':'33421254656'," +
                                    "'nics':[{'nicMac':'40:8d:5c:f7:8d:60', 'nicSpeed':'1000Mbps', 'isProvisionNic':'true', 'nicName': 'eth0'}]}"
                        }
                    }

                    bareMetal2Chassis {
                        name = "chassis_2"
                        ipmiAddress = "*********"
                        ipmiUsername = "username"
                        ipmiPassword = "password"

                        hardwareInfo {
                            info = "{'bootMode':'UEFI', 'architecture':'x86_64', 'cpuModelName':'Intel i7-6700K', 'cpuNum':'8', 'memorySize':'33421254656'," +
                                    "'nics':[{'nicMac':'40:8d:5c:f7:8d:61', 'nicSpeed':'1000Mbps', 'isProvisionNic':'true', 'nicName': 'eth0'}]}"
                        }
                    }

                    attachPrimaryStorage("sharedblock-ps-1")
                    attachProvisionNetworks("provision_net_1")
                }

                attachBackupStorage("imagestore")
            }

            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 8
            }

            imageStore {
                name = "imagestore"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "x86_qcow2"
                    format = "qcow2"
                    url = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "arm64_qcow2"
                    format = "qcow2"
                    url = "http://zstack.org/download/test.qcow2"
                    architecture = "aarch64"
                }

                image {
                    name = "bm_raw"
                    format = "raw"
                    url = "http://zstack.org/download/test.raw"
                    systemTags = ["baremetal2", "bootMode::UEFI"]
                }
            }

            vm {
                name = "X86_VM_1"
                useInstanceOffering("instanceOffering")
                useImage("x86_qcow2")
                useL3Networks("l3")
                useHost("x86_host_1")
            }

            vm {
                name = "X86_VM_2"
                useInstanceOffering("instanceOffering")
                useImage("x86_qcow2")
                useL3Networks("l3")
                useHost("x86_host_2")
            }

            vm {
                name = "ARM64_VM_1"
                useInstanceOffering("instanceOffering")
                useImage("arm64_qcow2")
                useL3Networks("l3")
                useHost("arm64_host_1")
            }

            bareMetal2Instance {
                name = "BM_1"
                useImage("bm_raw")
                useChassis("chassis_1")
            }
        }
    }

    void testCreateArmVmCheckArmLicenseCapacity() {
        ImageInventory image = env.inventoryByName("arm64_qcow2") as ImageInventory
        L3NetworkInventory l3 = env.inventoryByName("l3") as L3NetworkInventory
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory

        arm64 = new LicenseInfo()
        arm64.uuid = Platform.getUuid()
        arm64.user = "zstack"
        arm64.product = "arm64"
        arm64.vmNum = 1
        arm64.issueTime = OffsetDateTime.now()
        arm64.expireTime = OffsetDateTime.now().plusDays(1000)
        arm64.licenseType = LicenseType.AddOn
        arm64.init()

        createVmInstance {
            name = "new_arm_vm"
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
            instanceOfferingUuid = instanceOffering.uuid
        }

        licenseAddOns.removeIf({ it.modules != null && it.modules.contains(AddOnModuleType.Arm64.name) })

        licenseAddOns.add(arm64)

        reloadLicense {}

        CreateVmInstanceAction action = new CreateVmInstanceAction()
        action.name = "new_arm_vm"
        action.imageUuid = image.uuid
        action.l3NetworkUuids = [l3.uuid]
        action.instanceOfferingUuid = instanceOffering.uuid
        action.sessionId = adminSession()
        CreateVmInstanceAction.Result result = action.call()
        assert result.error != null
        assert result.error.code == LicenseErrors.LICENSE_NOT_PERMITTED.toString()

        arm64.vmNum = 4
        reloadLicense {}
        result = action.call()
        assert result.error == null
        testArmLicenseCapacity(AddOnModuleType.Arm64)

        arm64.vmNum = null
        arm64.cpuNum = 10000
        reloadLicense {}
        result = action.call()
        assert result.error == null
        testArmLicenseCapacity(AddOnModuleType.Arm64)
    }

    void testAddKvmHostCheckArmLicense() {
        arm64.cpuNum = licenseManager.getArm64CpuSocketsNum()
        reloadLicense {}

        ClusterInventory cluster = env.inventoryByName("arm64_cluster_1")

        AddKVMHostAction action = new AddKVMHostAction()
        action.username = "root"
        action.password = "password"
        action.clusterUuid = cluster.uuid
        action.name = "test arm license"
        action.managementIp = "127.0.0.111"
        action.sessionId = adminSession()
        AddKVMHostAction.Result result = action.call()
        assert result.error != null
        assert result.error.code == LicenseErrors.LICENSE_NOT_PERMITTED.toString()

        arm64.cpuNum = 4
        reloadLicense {}
        result = action.call()
        assert result.error == null
        testArmLicenseCapacity(AddOnModuleType.Arm64)

        license {
            cpuNum = 4
            hostNum = null
        }
        arm64.cpuNum = null
        arm64.hostNum = Long.valueOf(licenseManager.getArm64HostNum()).toInteger()
        reloadLicense {}
        action.managementIp = "127.0.0.112"
        result = action.call()
        assert result.error != null
        assert result.error.code == LicenseErrors.LICENSE_NOT_PERMITTED.toString()

        arm64.hostNum = 10
        reloadLicense {}
        result = action.call()
        assert result.error == null
        testArmLicenseCapacity(AddOnModuleType.Arm64)

        arm64.cpuNum = null
        arm64.hostNum = null
        license {
            cpuNum = 20
            hostNum = null
        }

        reloadLicense {}
        String hostUuid = Platform.getUuid()
        action.managementIp = "127.0.0.113"
        action.resourceUuid = hostUuid
        result = action.call()
        assert result.error != null
        assert result.error.code == LicenseErrors.LICENSE_NOT_PERMITTED.toString()

        license {
            cpuNum = 20
            hostNum = null
            prodInfo = LicenseProduct.valueOf("XinChuang.Cloud", LicenseType.Paid)
        }
        reloadLicense {}
        org.zstack.sdk.LicenseInventory inv = getLicenseInfo {}
        Long availableCpu = inv.availableCpuNum
        result = action.call()
        assert result.error == null

        inv = getLicenseInfo {}
        Long availableCpu1 = inv.availableCpuNum
        HostCapacityVO h = Q.New(HostCapacityVO.class).eq(HostCapacityVO_.uuid, hostUuid).find()
        assert availableCpu1 == availableCpu - h.cpuSockets
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            licenseManager = bean(LicenseManagerImpl.class)
            testCreateArmVmCheckArmLicenseCapacity()
            testAddKvmHostCheckArmLicense()
        }
    }
}
