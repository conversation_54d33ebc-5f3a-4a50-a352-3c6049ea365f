package org.zstack.test.integration.premium.storage.migration

import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase

/**
 * Created by LiangHanYu on 2020/9/18 10:14
 */
class LocalGetCandidateHostInCombinatorialPsCase extends PremiumSubCase {
    EnvSpec env

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = PremiumEnv.twoClusterFifteenPsOneimageStoreBsEnv()
    }

    @Override
    void test() {
        env.create {
            GetCandidateHostInCombinatorialPsCase op = new GetCandidateHostInCombinatorialPsCase()
            op.srcPS = env.inventoryByName("local-ps") as PrimaryStorageInventory
            op.env = env
            op.prepare()
            op.dstPss = Arrays.asList(op.nfsPS2, op.cephPS2, op.smpPS2, op.sbgPS2)
            op.testGetCandidateHostForVmMigrationInCombinatorialPsAction()
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}
