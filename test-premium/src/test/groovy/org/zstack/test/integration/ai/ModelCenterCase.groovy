package org.zstack.test.integration.ai

import io.kubernetes.client.openapi.models.V1Deployment
import io.kubernetes.client.openapi.models.V1EnvVar
import io.kubernetes.client.openapi.models.V1LoadBalancerIngress
import io.kubernetes.client.openapi.models.V1LoadBalancerStatus
import io.kubernetes.client.openapi.models.V1ServiceStatus
import org.apache.commons.lang.StringUtils
import org.springframework.http.HttpEntity
import org.springframework.web.util.UriComponentsBuilder
import org.zstack.ai.AIConfigYaml
import org.zstack.ai.AIConstants
import org.zstack.ai.AIGlobalConfig
import org.zstack.ai.AIModelManagerImpl
import org.zstack.ai.AISystemTags
import org.zstack.ai.BentoBasedModelStorageBackend
import org.zstack.ai.ModelServiceBackendType
import org.zstack.ai.SystemModels
import org.zstack.ai.entity.ModelCenterStatus
import org.zstack.ai.entity.ModelCenterVO
import org.zstack.ai.entity.ModelCenterVO_
import org.zstack.ai.entity.ModelEvaluationTaskStatus
import org.zstack.ai.entity.ModelEvaluationTaskVO
import org.zstack.ai.entity.ModelEvaluationTaskVO_
import org.zstack.ai.entity.ModelServiceGroupDatasetRefVO
import org.zstack.ai.entity.ModelServiceGroupDatasetRefVO_
import org.zstack.ai.entity.ModelServiceGroupModelServiceRefVO
import org.zstack.ai.entity.ModelServiceGroupModelServiceRefVO_
import org.zstack.ai.entity.ModelServiceInstanceGroupVO
import org.zstack.ai.entity.ModelServiceInstanceGroupVO_
import org.zstack.ai.entity.ModelServiceInstanceVO
import org.zstack.ai.entity.ModelServiceInstanceVO_
import org.zstack.ai.entity.ModelServiceRefVO
import org.zstack.ai.entity.ModelServiceRefVO_
import org.zstack.ai.entity.ModelServiceVO
import org.zstack.ai.entity.ModelServiceVO_
import org.zstack.ai.entity.ModelType
import org.zstack.ai.entity.ModelVO
import org.zstack.ai.entity.ModelVO_
import org.zstack.ai.entity.ModelServiceInstanceGroupStatus
import org.zstack.ai.entity.ModelServiceType
import org.zstack.ai.evaluation.ModelEvaluationTaskTracker
import org.zstack.ai.message.APIAddModelMsg
import org.zstack.ai.message.APIAddModelServiceMsg
import org.zstack.ai.message.AddModelLongJob
import org.zstack.ai.message.AddModelMsg
import org.zstack.ai.message.AddModelServiceMsg
import org.zstack.ai.message.AddModelServiceLongJob
import org.zstack.ai.message.ModelCenterStorageType
import org.zstack.ai.message.ModelServiceFramework
import org.zstack.ai.message.StorageCapacity
import org.zstack.ai.message.SyncAINginxConfigurationMsg
import org.zstack.ai.message.SyncAINginxConfigurationReply
import org.zstack.ai.service.ModelEvaluationCommands
import org.zstack.compute.vm.VmInstanceBase
import org.zstack.container.message.*
import org.zstack.core.Platform
import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.core.thread.ThreadFacade
import org.zstack.ha.HaSystemTags
import org.zstack.ha.VmHaLevel
import org.zstack.header.host.HostStatus
import org.zstack.header.identity.AccountConstant
import org.zstack.header.identity.AccountResourceRefVO
import org.zstack.header.identity.AccountResourceRefVO_
import org.zstack.header.image.ImageStateEvent
import org.zstack.header.rest.RESTConstant
import org.zstack.header.rest.RESTFacade
import org.zstack.header.storage.primary.PrimaryStorageState
import org.zstack.header.tag.UserTagVO
import org.zstack.header.tag.UserTagVO_
import org.zstack.header.vm.KvmReportVmShutdownEventMsg
import org.zstack.header.vm.VmInstanceVO
import org.zstack.header.vm.VmInstanceVO_
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.longjob.LongJobManagerImpl
import org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend
import org.zstack.pciDevice.PciDeviceSystemTags
import org.zstack.pciDevice.PciDeviceTOExampleMaker
import org.zstack.pciDevice.gpu.GpuDeviceVO
import org.zstack.pciDevice.gpu.GpuDeviceVO_
import org.zstack.sdk.*
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.HttpError
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.zdfs.ZdfsBase

import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

import static org.zstack.core.Platform.operr
/**
 * <AUTHOR>
 * @date 2024/6/13 16:50
 */
class ModelCenterCase extends PremiumSubCase {
    EnvSpec env
    ModelCenterInventory modelCenter
    PciDeviceSpecInventory nvSpec
    PciDeviceInventory nvidiaDevice
    PrimaryStorageInventory ps1
    Long modelSize
    String serviceYaml
    boolean useApiParamCheckBootUpTimeout = false
    Integer globalApiBootUpTimeout = 1024

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = AIModelEnv.FourHostsTwoClusterEnv()
    }

    def commandList = []

    @Override
    void test() {
        env.create {
            reloadLicense {}
            prepareMock()
            testModelCenterLifecycle()
            testModelLifecycle()
            testModelServiceLifecycle()
            testAddModelServiceLongJob()
            testAutoBindModelToModelService()
            testDeployModelServiceToKubernetesCluster()
        }
    }

    String generateUTMockPath(String requestPath) {
        String mockPath = requestPath
        if (!requestPath.startsWith("/")) {
            mockPath = "/" + requestPath;
        }
        return String.format("%s%s", AIConstants.UT_MOCK_PATH_PREFIX, mockPath)
    }

    void prepareMock() {
        modelSize = 14.39 * 1024 * 1024 * 1024
        serviceYaml = """services:
  - name: qwen-chat:2b34xhrmqwhomjkd
    ports:
      - 3000
    livez: /livez
    readyz: /readyz
env:
  - key:value
  - key2:value2"""
        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadModelResponse()
            rsp.size = "14.39 GiB"
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.tag = "qwen--qwen-chat:n3ucnjrnicyqz6xk"
            rsp.module = ""
            rsp.pipeline_tag = "text-generation"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE) { HttpEntity<String> entity, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(entity.body, BentoBasedModelStorageBackend.UploadModelServiceCmd.class)
            if (cmd.source == "HuggingFace") {
                assert cmd.repository != null
            }

            def rsp = new BentoBasedModelStorageBackend.UploadModelServiceResponse()
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            if (cmd.source == "HuggingFace") {
                rsp.dockerImage = "internal.repo/dockerimage"
            }
            rsp.size = 1024
            return rsp
        }

        env.simulator(generateUTMockPath("/livez")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        env.simulator(generateUTMockPath("/readyz")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_DATASET) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadDatasetResponse()
            rsp.installPath = "/path/to/test"
            rsp.size = 1024
            rsp.success = true
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.CONFIG_SET) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.ConfigUpdateResponse()
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.CREATE_NGINX_RULE) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.DELETE_NGINX_RULE) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_GPU(),
                    PciDeviceTOExampleMaker.INTEL_INTEGRATED_AUDIO(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        env.message(SyncAINginxConfigurationMsg) { SyncAINginxConfigurationMsg msg, CloudBus bus ->
            def reply = new SyncAINginxConfigurationReply()
            bus.reply(msg, reply)
        }

        List<KVMHostInventory> allHosts = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : allHosts) {
            updateHostIommuState {
                delegate.uuid = host.uuid
                delegate.state = "Enabled"
            }
            reconnectHost {
                delegate.uuid = host.uuid
            }
        }
        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
        }

        nvSpec = queryPciDeviceSpec {
            delegate.conditions = ["vendorId=10de", "deviceId=128b", "subvendorId=10de", "subdeviceId=118b"]
        }[0] as PciDeviceSpecInventory

        nvidiaDevice = queryPciDevice {
            delegate.conditions = ["vendorId=10de", "deviceId=128b", "subvendorId=10de", "subdeviceId=118b"]
        }[0] as PciDeviceInventory

        AIGlobalConfig.SERVICE_STARTUP_CHECK_INTERVAL_SECONDS.updateValue(1)

        def systemModelCenter = queryModelCenter {
            conditions = ["uuid=2cc1f4d6b8014b44912f92bb242e9675"]
        }[0] as ModelCenterInventory

        def reloadNginx = false
        env.message(SyncAINginxConfigurationMsg) { SyncAINginxConfigurationMsg msg, CloudBus bus ->
            def reply = new SyncAINginxConfigurationReply()
            reloadNginx = msg.reloadMNNginx
            bus.reply(msg, reply)
        }

        updateModelCenter {
            uuid = systemModelCenter.uuid
            managementIp = "127.0.0.1"
            managementPort = 8989
            containerRegistry = "127.0.0.1:5443/registry"
        } as ModelCenterInventory

        assert reloadNginx == true
    }

    boolean isValidDnsName(String name) {
        if (name == null || name.isEmpty() || name.length() > 253) {
            return false
        }

        def pattern = /^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$/
        return name ==~ pattern
    }

    void checkDeploymentInfo(V1Deployment deploy, Integer cpu, Integer memory, Integer gpuMem, Map<String, String> envValues) {
        Integer serviceBootUpTime= AIGlobalConfig.SERVICE_STARTUP_TIMES_IN_SECONDS.value(Integer.class)
        checkDeployInfo(deploy, cpu, memory, gpuMem, serviceBootUpTime, envValues)
    }

    void checkDeployInfo(V1Deployment deploy, Integer cpu, Integer memory, Integer gpuMem, Integer serviceBootUpTime, Map<String, String> envValues) {
        def cpuQuantity = deploy.spec.template.spec.containers.get(0).resources.limits.get("cpu")
        def memQuantity = deploy.spec.template.spec.containers.get(0).resources.limits.get("memory")
        def gpuMemQuantity = deploy.spec.template.spec.containers.get(0).resources.limits.get("nvidia.com/gpumem")
        assert cpuQuantity.getNumber().toInteger() == cpu
        assert memQuantity.getNumber().toInteger() == memory
        assert gpuMemQuantity.getNumber() == gpuMem

        def privileged = deploy.spec.template.spec.containers.get(0).securityContext.privileged
        assert privileged == false
        if (!envValues.isEmpty()) {
            assert deploy.spec.template.spec.containers.get(0).env.size() == envValues.size()
            envValues.each {
                assert deploy.spec.template.spec.containers.get(0).env.find { env -> env.name == it.key && env.value == it.value } != null
            }
        }
        assert deploy.spec.template.spec.containers.get(0).env.find { it.name == "AI_YAML_STRING" } != null
        assert deploy.spec.template.spec.containers.get(0).env.find { it.name == "JUPYTER_PORT" } != null
    }

    void testAddModelServiceLongJob() {
        ImageInventory image = env.inventoryByName("image1") as ImageInventory
        def called = false
        def expectedTimeoutInSeconds = TimeUnit.HOURS.toSeconds(72)

        // add model service by long job
        APIAddModelServiceMsg jobMessage = new APIAddModelServiceMsg();
        jobMessage.name = "test1"
        jobMessage.description = "description1"
        jobMessage.modelCenterUuid = modelCenter.uuid
        jobMessage.installPath = "/example/install/path"
        jobMessage.startCommand = "exampleStartCommand --option value"
        jobMessage.dockerImage = "aiworker:latest"
        jobMessage.vmImageUuid = image.uuid
        jobMessage.yaml = serviceYaml
        jobMessage.requestCpu = 1
        jobMessage.requestMemory = 1024
        jobMessage.framework = ModelServiceFramework.Transformers.toString()

        def modelServiceUuid = Platform.getUuid()
        jobMessage.resourceUuid = modelServiceUuid

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE) { HttpEntity<String> entity, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(entity.body, BentoBasedModelStorageBackend.UploadModelServiceCmd.class)
            if (cmd.source == "HuggingFace") {
                assert cmd.repository != null
            }

            def rsp = new BentoBasedModelStorageBackend.UploadModelServiceResponse()
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            rsp.size = 1024
            return rsp
        }

        def hangOnUpload = true
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE) { rsp, HttpEntity<String> e ->
            while (hangOnUpload) {
                sleep(200)
            }

            return rsp
        }

        def progress = [0, 50, 100]
        AtomicInteger counter = new AtomicInteger(0)
        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE_PROGRESS) { HttpEntity<String> entity, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(entity.body, BentoBasedModelStorageBackend.ModelUploadProgressCmd.class)
            assert cmd.taskUuid != null
            def rsp = new BentoBasedModelStorageBackend.UploadProgressResponse()
            rsp.success = true
            int index = counter.getAndIncrement()
            index = index >= 2 ? 2 : index
            rsp.progress = progress.getAt(index)
            called = true
            return rsp
        }
        expectedTimeoutInSeconds = TimeUnit.HOURS.toMillis(72)

        def addModelServiceSent = false
        LongJobManagerImpl longJobManager = bean(LongJobManagerImpl.class)
        assert longJobManager.longJobClasses.contains(AddModelServiceLongJob.class.toString())
        def cleanup = notifyWhenReceivedMessage(AddModelServiceMsg.class) { AddModelServiceMsg msg ->
            assert msg.getTimeout() == expectedTimeoutInSeconds
            addModelServiceSent = true
        }
        def job = submitLongJob {
            jobName = jobMessage.getClass().getSimpleName()
            jobData = JSONObjectUtil.toJsonString(jobMessage)
        } as LongJobInventory

        retryInSecs {
            assert addModelServiceSent
        }

        cleanup()

        ConcurrentLinkedQueue<TaskProgressInventory> progressList = new ConcurrentLinkedQueue<>()
        retryInSecs(15) {
            TaskProgressInventory process = getTaskProgress {
                apiId = job.apiId
            }[0] as TaskProgressInventory
            progressList.add(process)

            assert process.content == "100"
        }

        assert progressList != null

        hangOnUpload = false
        retryInSecs {
            job = queryLongJob {
                conditions = ["uuid=${job.uuid}"]
            }[0] as LongJobInventory

            assert job.state == LongJobState.Succeeded
        }

        org.zstack.ai.entity.ModelServiceInventory modelServiceFromLongJob = JSONObjectUtil
                .toObject(job.getJobResult(), org.zstack.ai.entity.ModelServiceInventory.class)
        assert modelServiceFromLongJob.name == "test1"
        assert modelServiceFromLongJob.description == "description1"
        assert modelServiceFromLongJob.modelCenterUuid == modelCenter.uuid
        assert modelServiceFromLongJob.framework == ModelServiceFramework.Transformers.toString()
        assert queryAudit {
            conditions = ["resourceUuid=${modelServiceFromLongJob.uuid}", "apiName=${APIAddModelServiceMsg.class.name}".toString()]
        }.size() == 1

        List<String> progressContents = progressList.collect { it.getContent() }
        Set<String> resultSet = new HashSet<>(progressContents)
        assert resultSet.contains("0")
        assert resultSet.contains("100")

        AtomicInteger integer = new AtomicInteger(0)
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE_PROGRESS) { rsp, HttpEntity<String> e ->
            integer.incrementAndGet()
            return rsp
        }

        // test upload api failure
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE) { rsp, HttpEntity<String> e ->
            throw new HttpError(504, "on purpose")
        }
        job = submitLongJob {
            jobName = jobMessage.getClass().getSimpleName()
            jobData = JSONObjectUtil.toJsonString(jobMessage)
        } as LongJobInventory
        retryInSecs {
            job = queryLongJob {
                conditions = ["uuid=${job.uuid}"]
            }[0] as LongJobInventory

            assert job.state == LongJobState.Failed
        }

        retryInSecs {
            assert integer.get() >= 1
        }
        integer.set(0)

        // test upload return but success = false
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE) { BentoBasedModelStorageBackend.UploadModelServiceResponse rsp, HttpEntity<String> e ->
            rsp.success = false
            rsp.error = "no purpose"
            return rsp
        }
        job = submitLongJob {
            jobName = jobMessage.getClass().getSimpleName()
            jobData = JSONObjectUtil.toJsonString(jobMessage)
        } as LongJobInventory
        retryInSecs {
            job = queryLongJob {
                conditions = ["uuid=${job.uuid}"]
            }[0] as LongJobInventory

            assert job.state == LongJobState.Failed
        }
        retryInSecs {
            assert integer.get() >=1
        }
        integer.set(0)
        env.cleanAfterSimulatorHandlers()
    }

    void testDeployModelServiceToKubernetesCluster() {
        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadModelResponse()
            rsp.size = "14.39 GiB"
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.tag = "qwen--qwen-chat:n3ucnjrnicyqz6xk"
            rsp.module = ""
            rsp.pipeline_tag = "text-generation"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            rsp.introduction = "test introduction"
            return rsp
        }

        TagPatternInventory tag = queryTag {
            conditions = ["name=AI::LLM"]
        }[0] as TagPatternInventory

        ModelInventory m = addModel {
            name = "test"
            description = "description"
            modelCenterUuid = modelCenter.uuid
            installPath = "file:///root/Qwen--qwen-chat"
            parameters = "test parameters"
            logo = "BASE64logoString"
            version = "1.0.0"
            vendor = "ZStack"
            size = modelSize
            introduction = "This is md5 format string"
            tagUuids = [tag.uuid]
        } as ModelInventory

        assert m.introduction == "test introduction"

        ImageInventory image = env.inventoryByName("image1") as ImageInventory
        ZoneInventory zone = env.inventoryByName("zone") as ZoneInventory
        L3NetworkInventory l3 = env.inventoryByName("l3") as L3NetworkInventory
        L3NetworkInventory pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        def cpu = 1
        def memory = 1024
        def gpuMem = 2048000
        Map<String, String> envValues = new HashMap<String, String>()

        ModelServiceInventory ms = addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            requestCpu = cpu
            requestMemory = memory
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
            tagUuids = [tag.uuid]
        } as ModelServiceInventory

        ms = bindModelToService {
            modelUuid = m.uuid
            modelServiceUuid = ms.uuid
        } as ModelServiceInventory

        env.message(DeleteDeploymentMsg) { DeleteDeploymentMsg msg, CloudBus bus ->
            def reply = new DeleteDeploymentReply()
            bus.reply(msg, reply)
        }

        env.message(DeleteKubernetesServiceMsg) { DeleteKubernetesServiceMsg msg, CloudBus bus ->
            def reply = new DeleteKubernetesServiceReply()
            bus.reply(msg, reply)
        }

        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            def imageName = deploy.spec.template.spec.containers.get(0).image
            assert imageName == "127.0.0.1:5443/registry/aiworker:latest"
            checkDeploymentInfo(deploy, cpu, memory, gpuMem, envValues)
            reply.setDeployment(deploy)
            bus.reply(msg, reply)
        }

        env.message(CreateKubernetesServiceMsg) { CreateKubernetesServiceMsg msg, CloudBus bus ->
            def reply = new CreateKubernetesServiceReply()
            def service = msg.getService()

            def serviceName = service.getMetadata().getName()
            if (!isValidDnsName(serviceName)) {
                reply.setSuccess(false)
                reply.setError("Service name '${serviceName}' does not conform to DNS format")
                bus.reply(msg, reply)
                return
            }
            def status = new V1ServiceStatus()
            def loadBalancerStatus = new V1LoadBalancerStatus()
            def ingress = Collections.singletonList(
                    new V1LoadBalancerIngress().ip("**********")
            )
            loadBalancerStatus.setIngress(ingress)
            status.setLoadBalancer(loadBalancerStatus)
            service.setStatus(status)
            reply.setService(service)
            reply.setUrl("http:://172.28.16:3000")
            bus.reply(msg, reply)
        }

        env.simulator(generateUTMockPath("/readyz")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        useApiParamCheckBootUpTimeout = true
        def group = deployModelService {
            uuid = ms.uuid
            name = "test-container-service-deployment-1"
            vmImageUuid = image.uuid
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = cpu
            type = ModelServiceBackendType.Container.toString()
            memorySize = memory
            l3NetworkUuids = [l3.uuid]
            systemTags = ["hami::gpuMemory::2048000".toString(), "modelServiceMode::develop"]
            serviceBootUptime = globalApiBootUpTimeout
        } as ModelServiceInstanceGroupInventory
        useApiParamCheckBootUpTimeout = false

        assert group != null
        assert group.modelServiceUuid == ms.uuid
        assert group.instances.size() == 1
        assert group.status == ModelServiceInstanceGroupStatus.Running.toString()
        assert group.modelUuid == m.uuid
        assert group.type == ModelServiceBackendType.Container.toString()
        assert AISystemTags.AI_MODEL_CLASSIFICATION.hasTag(group.getUuid())

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        assert !Q.New(ModelServiceInstanceVO.class)
                .eq(ModelServiceInstanceVO_.modelServiceGroupUuid, group.uuid)
                .isExists()

        ms = addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
            tagUuids = [tag.uuid]
        } as ModelServiceInventory

        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            def imageName = deploy.spec.template.spec.containers.get(0).image
            assert imageName == "127.0.0.1:5443/registry/aiworker:latest"
            checkDeploymentInfo(deploy, cpu, memory, gpuMem, envValues)
            reply.setDeployment(deploy)
            bus.reply(msg, reply)
        }

        group = null
        group = deployModelService {
            uuid = ms.uuid
            name = "test-container-service-deployment-2"
            vmImageUuid = image.uuid
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = cpu
            memorySize = memory
            type = ModelServiceBackendType.Container.toString()
            l3NetworkUuids = [l3.uuid]
            systemTags = ["hami::gpuMemory::2048000".toString()]
        } as ModelServiceInstanceGroupInventory
        assert group != null

        deleteModelService {
            uuid = ms.uuid
        }

        assert Q.New(ModelServiceInstanceGroupVO.class)
                .eq(ModelServiceInstanceGroupVO_.uuid, group.uuid)
                .isExists()

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        envValues.put("key", "value")
        envValues.put("key1", "value1")
        def ms1 = addModelService {
            name = "test"
            description = "description"
            yaml = """services:
  - name: qwen-chat:2b34xhrmqwhomjkd
    ports:
      - 3000
    livez: /livez
    readyz: /readyz
    serviceBootupTime: 30
env:
  - key:value
  - key1:value1"""
            vmImageUuid = image.uuid
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker"
            tagUuids = [tag.uuid]
        } as ModelServiceInventory

        AIConfigYaml testConfigYaml = AIConfigYaml.fromYaml(ms1.getYaml())
        assert testConfigYaml.getEnv().size() == 2
        assert testConfigYaml.getEnv().get(0) == "key:value"
        assert testConfigYaml.getEnv().get(1) == "key1:value1"

        DatasetInventory dataset = createDataset {
            name = "test"
            description = "test2"
            url = "http://test/path"
            modelCenterUuid = modelCenter.uuid
            systemTags = [
                    "dataset::usage::scenarios::App",
                    "dataset::datatype::Text"
            ]
        } as DatasetInventory

        def uuidInYaml = ""

        def instanceUuidInYaml = ""
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            def imageName = deploy.spec.template.spec.containers.get(0).image
            assert imageName == "127.0.0.1:5443/registry/aiworker"

            // 获取环境变量
            def envVars = deploy.spec.template.spec.containers.get(0).env

            // 检查特定环境变量
            def yamlString = envVars.find { it.name == "AI_YAML_STRING" }
            assert yamlString != null

            def configYaml = AIConfigYaml.fromYaml(yamlString.value)
            assert configYaml != null

            def servicePath = configYaml.getServices().get(0).getInstallPath()
            assert servicePath == ms1.installPath

            def startCmd = configYaml.getServices().get(0).getStartCommand()
            assert startCmd == ms1.startCommand

            def mountPath = configYaml.getMountPaths().get(0)
            assert mountPath == modelCenter.url

            def modelPath = configYaml.getModels().get(0).getInstallPath()
            assert modelPath == m.installPath

            def modelName = configYaml.getModels().get(0).getName()
            assert modelName == m.name

            def datasetPath = configYaml.getDatasets().get(0).getInstallPath()
            assert datasetPath == dataset.installPath

            def datasetName = configYaml.getDatasets().get(0).getName()
            assert datasetName == dataset.name

            def serviceBootupTime = configYaml.getServices().get(0).getServiceBootupTime()
            checkDeployInfo(deploy, cpu, memory, gpuMem, serviceBootupTime, envValues)

            def framework = configYaml.getFramework()
            assert framework == Q.New(ModelServiceVO.class)
                    .select(ModelServiceVO_.framework)
                    .eq(ModelServiceVO_.uuid, ms1.uuid)
                    .findValue()

            instanceUuidInYaml = configYaml.getInstanceUuid()

            assert configYaml.getUuid() != null
            uuidInYaml = configYaml.getUuid()

            def modelTags = configYaml.getModelTags()
            if (modelTags == null) {
                modelTags = new ArrayList<String>()
            }

            List<String> modelTagsFromDb = AISystemTags.AI_MODEL_CLASSIFICATION.getTags(uuidInYaml)
            assert modelTagsFromDb.containsAll(modelTags)

            reply.setDeployment(deploy)
            bus.reply(msg, reply)
        }

        group = null
        envValues.clear()
        group = deployModelService {
            uuid = ms1.uuid
            name = "test-container-service-deployment-3"
            vmImageUuid = image.uuid
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = cpu
            datasetUuids = [dataset.uuid]
            memorySize = memory
            type = ModelServiceBackendType.Container.toString()
            l3NetworkUuids = [l3.uuid]
            systemTags = ["hami::gpuMemory::2048000".toString()]
        } as ModelServiceInstanceGroupInventory
        retryInSecs {
            assert group != null
        }
        assert uuidInYaml == group.uuid
        def instance = group.getInstances().get(0)
        assert instance.uuid == instanceUuidInYaml

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        updateModelCenter {
            uuid = modelCenter.uuid
            containerRegistry = "127.0.0.1:5443/registry/"
        }

        ms = addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker"
            tagUuids = [tag.uuid]
        } as ModelServiceInventory

        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            def imageName = deploy.spec.template.spec.containers.get(0).image
            assert imageName == "127.0.0.1:5443/registry/aiworker"
            checkDeploymentInfo(deploy, cpu, memory, gpuMem, envValues)
            reply.setDeployment(deploy)
            bus.reply(msg, reply)
        }

        group = null
        group = deployModelService {
            uuid = ms.uuid
            name = "test-container-service-deployment-4"
            vmImageUuid = image.uuid
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = cpu
            memorySize = memory
            type = ModelServiceBackendType.Container.toString()
            l3NetworkUuids = [l3.uuid]
            systemTags = ["hami::gpuMemory::2048000".toString()]
        } as ModelServiceInstanceGroupInventory
        assert group != null

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            def imageName = deploy.spec.template.spec.containers.get(0).image
            assert imageName == "127.0.0.1:5443/registry/dockerImage:latest"
            checkDeploymentInfo(deploy, 2, 2048, gpuMem, envValues)
            reply.setDeployment(deploy)
            bus.reply(msg, reply)
        }

        group = deployModelService {
            uuid = ms.uuid
            name = "test-container-service-deployment-5"
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 2
            memorySize = 2048
            dockerImage = "127.0.0.1:5443/registry/dockerImage:latest"
            type = ModelServiceBackendType.Container.toString()
            systemTags = ["hami::gpuMemory::2048000".toString()]
        } as ModelServiceInstanceGroupInventory

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        // check rollback, mock no ip is allocated to ld
        env.message(CreateKubernetesServiceMsg) { CreateKubernetesServiceMsg msg, CloudBus bus ->
            def reply = new CreateKubernetesServiceReply()
            def service = msg.getService()

            def serviceName = service.getMetadata().getName()
            if (!isValidDnsName(serviceName)) {
                reply.setSuccess(false)
                reply.setError("Service name '${serviceName}' does not conform to DNS format")
                bus.reply(msg, reply)
                return
            }
            def status = new V1ServiceStatus()
            def loadBalancerStatus = new V1LoadBalancerStatus()
            def ingress = Collections.singletonList(
                    new V1LoadBalancerIngress()
            )
            loadBalancerStatus.setIngress(ingress)
            status.setLoadBalancer(loadBalancerStatus)
            service.setStatus(status)
            reply.setService(service)
            bus.reply(msg, reply)
        }

        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            def imageName = deploy.spec.template.spec.containers.get(0).image
            assert imageName == "127.0.0.1:5443/registry/aiworker"
            checkDeploymentInfo(deploy, cpu, memory, gpuMem, envValues)

            reply.setDeployment(deploy)
            bus.reply(msg, reply)
        }

        def deleteServiceCalled = false
        def deleteDeployCalled = false

        // no url in reply msg, need to clean resource
        env.message(DeleteDeploymentMsg) { DeleteDeploymentMsg msg, CloudBus bus ->
            def reply = new DeleteDeploymentReply()
            assert msg.getDeploymentName() != null
            assert msg.getNamespace() != null
            deleteDeployCalled = true
            bus.reply(msg, reply)
        }

        env.message(DeleteKubernetesServiceMsg) { DeleteKubernetesServiceMsg msg, CloudBus bus ->
            def reply = new DeleteKubernetesServiceReply()
            assert msg.getServiceName() != null
            assert msg.getNamespace() != null
            deleteServiceCalled = true
            bus.reply(msg, reply)
        }

        group = null
        expectError {
            group = deployModelService {
                uuid = ms.uuid
                name = "test-container-service-deployment-6"
                vmImageUuid = image.uuid
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                cpuNum = cpu
                memorySize = memory
                type = ModelServiceBackendType.Container.toString()
                l3NetworkUuids = [l3.uuid]
                systemTags = ["hami::gpuMemory::2048000".toString()]
            } as ModelServiceInstanceGroupInventory
        }
        assert group == null
        assert deleteDeployCalled == true
        assert deleteServiceCalled == true
        assert Q.New(ModelServiceInstanceVO.class).count() == 0

        // test image disabled will failed to deploy
        changeImageState {
            uuid = image.uuid
            stateEvent = ImageStateEvent.disable.toString()
        }

        expectError {
            deployModelService {
                uuid = ms.uuid
                name = "test-container-service-deployment-6"
                vmImageUuid = image.uuid
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                cpuNum = cpu
                memorySize = memory
                type = ModelServiceBackendType.Container.toString()
                l3NetworkUuids = [l3.uuid]
                systemTags = ["hami::gpuMemory::2048000".toString()]
            } as ModelServiceInstanceGroupInventory
        }

        changeImageState {
            uuid = image.uuid
            stateEvent = ImageStateEvent.enable.toString()
        }

        // test image deleted will failed to deploy
        deleteImage {
            uuid = image.uuid
        }

        expectError {
            deployModelService {
                uuid = ms.uuid
                name = "test-container-service-deployment-6"
                vmImageUuid = image.uuid
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                cpuNum = cpu
                memorySize = memory
                type = ModelServiceBackendType.Container.toString()
                l3NetworkUuids = [l3.uuid]
                systemTags = ["hami::gpuMemory::2048000".toString()]
            } as ModelServiceInstanceGroupInventory
        }

        recoverImage {
            imageUuid = image.uuid
        }

        // Pending 状态 - 资源不足
        def errorMessage = ""
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)
            errorMessage = String.format("The pod of deploy %s is not ready in %s seconds. Last status: Pod status: Pending, Reason: Insufficient resources (CPU, memory)",
                    deploy.metadata.name, String.valueOf(msg.getTimeout()))
            reply.setError(operr(errorMessage))
            bus.reply(msg, reply)
        }

        def action = new DeployModelServiceAction()
        action.sessionId = adminSession()
        action.uuid = ms.uuid
        action.name = "test-container-service-deployment-7"
        action.zoneUuid = zone.uuid
        action.modelUuid = m.uuid
        action.cpuNum = 2
        action.memorySize = 2048
        action.dockerImage = "127.0.0.1:5443/registry/dockerImage:latest"
        action.type = ModelServiceBackendType.Container.toString()
        action.systemTags = ["hami::gpuMemory::2048000".toString()]
        DeployModelServiceAction.Result actionResult = action.call()
        assert actionResult.error.details == errorMessage

        // Pending 状态 - 镜像下载中
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)

            errorMessage = String.format("The pod of deploy %s is not ready in %s seconds. Last status: Pod status: Pending, Reason: ContainerCreating, State: Waiting: Image is still downloading",
                    deploy.metadata.name, String.valueOf(msg.getTimeout()))
            reply.setError(operr(errorMessage))
            bus.reply(msg, reply)
        }
        actionResult = action.call()
        assert actionResult.error.details == errorMessage

        // Failed 状态 - 容器非零退出
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)

            errorMessage = String.format("The pod of deploy %s is not ready in %s seconds. Last status: Pod status: Failed, Reason: Container exited with non-zero status",
                    deploy.metadata.name, String.valueOf(msg.getTimeout()))
            reply.setError(operr(errorMessage))
            bus.reply(msg, reply)
        }

        actionResult = action.call()
        assert actionResult.error.details == errorMessage

        // Unknown 状态 - 节点通信问题
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)

            errorMessage = String.format("The pod of deploy %s is not ready in %s seconds. Last status: Pod status: Unknown, Reason: Node communication failure",
                    deploy.metadata.name, String.valueOf(msg.getTimeout()))
            reply.setError(operr(errorMessage))
            bus.reply(msg, reply)
        }
        actionResult = action.call()
        assert actionResult.error.details == errorMessage

        // CrashLoopBackOff 状态
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)

            errorMessage = String.format("The pod of deploy %s is not ready in %s seconds. Last status: Pod status: CrashLoopBackOff, Reason: Container repeatedly failing to start",
                    deploy.metadata.name, String.valueOf(msg.getTimeout()))
            reply.setError(operr(errorMessage))
            bus.reply(msg, reply)
        }
        actionResult = action.call()
        assert actionResult.error.details == errorMessage

        // ImagePullBackOff 状态
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)

            errorMessage = String.format("The pod of deploy %s is not ready in %s seconds. Last status: Pod status: ImagePullBackOff, Reason: Unable to pull image",
                    deploy.metadata.name, String.valueOf(msg.getTimeout()))
            reply.setError(operr(errorMessage))

            bus.reply(msg, reply)
        }
        actionResult = action.call()
        assert actionResult.error.details == errorMessage

        // Terminating 状态
        env.message(CreateDeploymentMsg) { CreateDeploymentMsg msg, CloudBus bus ->
            def reply = new CreateDeploymentReply()
            def deploy = msg.getDeployment()
            reply.setDeployment(deploy)

            errorMessage = String.format("The pod of deploy %s is not ready in %s seconds. Last status: Pod status: Terminating, Reason: Pod is being deleted",
                    deploy.metadata.name, String.valueOf(msg.getTimeout()))
            reply.setError(operr(errorMessage))

            bus.reply(msg, reply)
        }
        actionResult = action.call()
        assert actionResult.error.details == errorMessage

        deleteDataset {
            uuid = dataset.uuid
        }
    }

    void testAutoBindModelToModelService() {
        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadModelResponse()
            rsp.size = "14.39 GiB"
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.tag = "qwen--qwen-chat:n3ucnjrnicyqz6xk"
            rsp.module = ""
            rsp.pipeline_tag = "text-generation"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            return rsp
        }

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_SERVICE) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadModelServiceResponse()
            rsp.creation_time = "2024-06-18 15:00:23"
            rsp.success = true
            rsp.installPath = "file:///user/home/<USER>"
            rsp.size = 1024
            return rsp
        }

        TagPatternInventory tag = queryTag {
            conditions = ["name=AI::LLM"]
        }[0] as TagPatternInventory

        ModelInventory m2 = addModel {
            name = "model-for-service-binding2"
            description = "description"
            modelCenterUuid = modelCenter.uuid
            installPath = "file:///root/Qwen--qwen-chat"
            parameters = "test parameters"
            logo = "BASE64logoString"
            version = "1.0.0"
            vendor = "ZStack"
            size = modelSize
            introduction = "This is md5 format string"
        } as ModelInventory

        ModelInventory m3 = addModel {
            name = "model-for-service-binding3"
            description = "description"
            modelCenterUuid = modelCenter.uuid
            installPath = "file:///root/Qwen--qwen-chat"
            parameters = "test parameters"
            logo = "BASE64logoString"
            version = "1.0.0"
            vendor = "ZStack"
            size = modelSize
            introduction = "This is md5 format string"
        } as ModelInventory

        ImageInventory image = env.inventoryByName("image1") as ImageInventory
        ZoneInventory zone = env.inventoryByName("zone") as ZoneInventory
        L3NetworkInventory l3 = env.inventoryByName("l3") as L3NetworkInventory
        L3NetworkInventory pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        ModelServiceInventory ms = addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
            tagUuids = [tag.uuid]
            modelUuids = [m2.uuid, m3.uuid]
        } as ModelServiceInventory

        assert Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelServiceUuid, ms.uuid)
                .count() == 2
        unbindModelFromService {
            modelUuid = m2.uuid
            modelServiceUuid = Platform.getUuid()
        }
        unbindModelFromService {
            modelUuid = Platform.getUuid()
            modelServiceUuid = ms.uuid
        }
        unbindModelFromService {
            modelUuid = m2.uuid
            modelServiceUuid = ms.uuid
        }
        assert Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelServiceUuid, ms.uuid)
                .count() == 1
        unbindModelFromService {
            modelUuid = m3.uuid
            modelServiceUuid = ms.uuid
        }

        ModelServiceInventory ms2 = addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
            tagUuids = [tag.uuid]
        } as ModelServiceInventory

        ModelInventory m = addModel {
            name = "test"
            description = "description"
            modelCenterUuid = modelCenter.uuid
            installPath = "file:///root/Qwen--qwen-chat"
            parameters = "test parameters"
            logo = "BASE64logoString"
            version = "1.0.0"
            vendor = "ZStack"
            size = modelSize
            introduction = "This is md5 format string"
            modelServiceUuids = [ms.uuid, ms2.uuid]
        } as ModelInventory

        assert Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelUuid, m.uuid)
                .count() == 2
        unbindModelFromService {
            modelUuid = m.uuid
            modelServiceUuid = ms.uuid
        }
        assert Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelUuid, m.uuid)
                .count() == 1
        unbindModelFromService {
            modelUuid = m.uuid
            modelServiceUuid = ms2.uuid
        }
        assert Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelUuid, m.uuid)
                .count() == 0

        bindModelToService {
            modelUuid = m.uuid
            modelServiceUuid = ms.uuid
        }
        assert Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelUuid, m.uuid)
                .eq(ModelServiceRefVO_.modelServiceUuid, ms.uuid).count() == 1

        unbindModelFromService {
            modelUuid = m.uuid
            modelServiceUuid = ms.uuid
        }

        assert Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelUuid, m.uuid)
                .eq(ModelServiceRefVO_.modelServiceUuid, ms.uuid).count() == 0
    }

    void testModelServiceLifecycle() {
        // test predefined system service have size
        ModelServiceInventory vllmService = queryModelService {
            conditions = ["uuid=${SystemModels.VLLM_UUID}"]
        }[0] as ModelServiceInventory
        assert vllmService.size == 313971L

        // test add model service
        modelCenter = addModelCenter {
            name = "test"
            description = "description"
            url = "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
            parameters = "test parameters"
            managementIp = "*********"
            managementPort = 8989
        } as ModelCenterInventory

        assert modelCenter.status == ModelCenterStatus.Connected.toString()

        // test model center ping and capacity updates
        AIGlobalConfig.MODEL_CENTER_TRACK_INTERVAL_SECONDS.updateValue(1)
        env.afterSimulator(BentoBasedModelStorageBackend.PING) { rsp, HttpEntity<String> entity ->
            throw new HttpError(504, "on purpose")
        }
        retryInSecs {
            modelCenter = queryModelCenter {
                conditions = ["uuid=${modelCenter.uuid}".toString()]
            }[0] as ModelCenterInventory
            assert modelCenter.status == ModelCenterStatus.Disconnected.toString()
        }
        env.cleanAfterSimulatorHandlers()
        retryInSecs {
            modelCenter = queryModelCenter {
                conditions = ["uuid=${modelCenter.uuid}".toString()]
            }[0] as ModelCenterInventory
            assert modelCenter.status == ModelCenterStatus.Connected.toString()
            assert modelCenter.capacity.datasetUsedCapacity == SizeUnit.GIGABYTE.toByte(10)
        }
        env.afterSimulator(BentoBasedModelStorageBackend.PING) { BentoBasedModelStorageBackend.PingResponse rsp, HttpEntity<String> entity ->
            StorageCapacity capacity = new StorageCapacity()
            capacity.path = "test"
            capacity.capacity = SizeUnit.GIGABYTE.toByte(20)
            rsp.capacity.put(ModelCenterStorageType.DATASETS.name, capacity)

            StorageCapacity capacity2 = new StorageCapacity()
            capacity2.path = "cache"
            capacity2.capacity = SizeUnit.GIGABYTE.toByte(10)
            rsp.capacity.put(ModelCenterStorageType.DOCKER_SYSTEM.name, capacity2)

            return rsp
        }
        retryInSecs {
            modelCenter = queryModelCenter {
                conditions = ["uuid=${modelCenter.uuid}".toString()]
            }[0] as ModelCenterInventory
            assert modelCenter.status == ModelCenterStatus.Connected.toString()
            assert modelCenter.capacity.datasetUsedCapacity == SizeUnit.GIGABYTE.toByte(20)
            assert modelCenter.capacity.cacheUsedCapacity == SizeUnit.GIGABYTE.toByte(30)
        }
        AIGlobalConfig.MODEL_CENTER_TRACK_INTERVAL_SECONDS.updateValue(600)

        ImageInventory image = env.inventoryByName("image1") as ImageInventory
        ZoneInventory zone = env.inventoryByName("zone") as ZoneInventory
        L3NetworkInventory l3 = env.inventoryByName("l3") as L3NetworkInventory
        L3NetworkInventory pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        modelCenter = updateModelCenter {
            uuid = modelCenter.uuid
            storageNetworkUuid = l3.uuid
            serviceNetworkUuid = pubL3.uuid
            containerRegistry = "127.0.0.1:5443/registry"
        } as ModelCenterInventory

        addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
            requestCpu = 1
            requestMemory = 1024
        } as ModelServiceInventory

        // add model service without gpu compute capability
        ModelServiceInventory  modelServiceInventory = addModelService {
            name = "without gpu compute capability"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
        } as ModelServiceInventory

        def modelService = queryModelService {
            conditions = ["uuid=${modelServiceInventory.uuid}", "system=false"]
        }[0] as ModelServiceInventory

        assert modelService.uuid == modelServiceInventory.uuid;
        assert modelService.name == modelServiceInventory.name;
        assert modelService.description == modelServiceInventory.description;
        assert modelService.vmImageUuid == modelServiceInventory.vmImageUuid;
        assert modelService.dockerImage == modelServiceInventory.dockerImage;
        assert modelService.gpuComputeCapability == modelServiceInventory.gpuComputeCapability;
        assert modelService.installPath == modelServiceInventory.installPath;
        assert modelService.startCommand == modelServiceInventory.startCommand;
        assert modelService.pythonVersion == modelServiceInventory.pythonVersion;
        assert modelService.condaVersion == modelServiceInventory.condaVersion;
        assert modelService.system == modelServiceInventory.system;
        assert modelService.modelCenterUuid == modelServiceInventory.modelCenterUuid;
        assert modelService.type == modelServiceInventory.type;
        assert modelService.yaml == modelServiceInventory.yaml;
        assert modelService.framework == modelServiceInventory.framework;
        assert modelService.source == modelServiceInventory.source
        assert modelService.requestCpu == modelServiceInventory.requestCpu;
        assert modelService.requestMemory.equals(modelServiceInventory.requestMemory);
        assert modelService.size == 1024

        // add model service without  vm image uuid
        modelServiceInventory = addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
        } as ModelServiceInventory

        modelService = queryModelService {
            conditions = ["uuid=${modelServiceInventory.uuid}", "system=false"]
        }[0] as ModelServiceInventory

        assert modelService.uuid == modelServiceInventory.uuid;
        assert modelService.name == modelServiceInventory.name;
        assert modelService.description == modelServiceInventory.description;
        assert modelService.vmImageUuid == modelServiceInventory.vmImageUuid;
        assert modelService.dockerImage == modelServiceInventory.dockerImage;
        assert modelService.gpuComputeCapability == modelServiceInventory.gpuComputeCapability;
        assert modelService.installPath == modelServiceInventory.installPath;
        assert modelService.startCommand == modelServiceInventory.startCommand;
        assert modelService.pythonVersion == modelServiceInventory.pythonVersion;
        assert modelService.condaVersion == modelServiceInventory.condaVersion;
        assert modelService.system == modelServiceInventory.system;
        assert modelService.modelCenterUuid == modelServiceInventory.modelCenterUuid;
        assert modelService.type == modelServiceInventory.type;
        assert modelService.yaml == modelServiceInventory.yaml;
        assert modelService.framework == modelServiceInventory.framework;
        assert modelService.source == modelServiceInventory.source;
        assert modelService.requestCpu == modelServiceInventory.requestCpu;
        assert modelService.requestMemory.equals(modelServiceInventory.requestMemory);

        ModelServiceInventory ms = addModelService {
            name = "test"
            description = "description"
            yaml = serviceYaml
            vmImageUuid = image.uuid
            requestCpu = 1
            requestMemory = 1024
            modelCenterUuid = modelCenter.uuid
            gpuComputeCapability = "7.5"  // 示例 GPU 计算能力
            installPath = "/example/install/path"  // 示例安装路径
            startCommand = "exampleStartCommand --option value"  // 示例启动命令
            pythonVersion = "3.11"  // Python 版本
            condaVersion = "4.9.2"  // Conda 版本
            dockerImage = "aiworker:latest"
        } as ModelServiceInventory

        assert ms.name == "test"
        assert ms.description == "description"
        assert ms.yaml == serviceYaml
        assert ms.requestCpu == 1
        assert ms.requestMemory == 1024
        assert ms.type == ModelServiceType.Endpoint.toString()

        assert Q.New(AccountResourceRefVO.class).eq(AccountResourceRefVO_.resourceUuid, ms.uuid).isExists()

        // test query result
        ms = queryModelService {
            conditions = ["uuid=${ms.uuid}"]
        }[0] as ModelServiceInventory

        assert ms.name == "test"
        assert ms.description == "description"
        assert ms.yaml == serviceYaml
        assert ms.requestCpu == 1
        assert ms.requestMemory == 1024

        // test update model service
        ms = updateModelService {
            uuid = ms.uuid
            name = "test1"
            description = "description1"
            yaml = """services:
  - name: qwen-chat:2b34xhrmqwhomjkd
    livez: '/livez'
    readyz: '/readyz'
    ports:
      - 3000
""".toString()
            requestCpu = 2
            requestMemory = 2048
            dockerImage = "example/docker-image:latest"
            vmImageUuid = image.uuid
            gpuComputeCapability = "7.5"
            startCommand = "python start.py"
            pythonVersion = "3.8"
            type = "Endpoint"
            source = "HuggingFace"
        } as ModelServiceInventory

        // 验证更新后的 ModelService
        assert ms.name == "test1"
        assert ms.description == "description1"
        assert ms.modelServiceRefs.isEmpty()
        assert ms.requestCpu == 2
        assert ms.requestMemory == 2048
        // update framework won't change docker image config
        assert ms.dockerImage == "example/docker-image:latest"
        assert ms.vmImageUuid == image.uuid
        assert ms.gpuComputeCapability == "NVIDIA T4 (Compute Capability 7.5)"
        assert ms.startCommand == "python start.py"
        assert ms.pythonVersion == "3.8"
        assert ms.type == "Endpoint"
        assert ms.source == "HuggingFace"

        // update docker image to empty
        updateModelService {
            uuid = ms.uuid
            dockerImage = ""
        }

        // test update model service
        ModelServiceInventory hfMs = addModelService {
            name = "huggingface"
            description = "description1"
            yaml = """services:
  - name: qwen-chat:2b34xhrmqwhomjkd
    livez: '/livez'
    readyz: '/readyz'
    ports:
      - 3000
""".toString()
            requestCpu = 2
            modelCenterUuid = modelCenter.uuid
            installPath = "https://huggingface.co/spaces/turboedit/turbo_edit"
            requestMemory = 2048
            dockerImage = "example/docker-image:latest"
            startCommand = "python start.py"
            type = "Endpoint"
            source = "HuggingFace"
        } as ModelServiceInventory

        // 验证更新后的 ModelService
        assert hfMs.name == "huggingface"
        assert hfMs.description == "description1"
        assert hfMs.requestCpu == 2
        assert hfMs.requestMemory == 2048
        assert hfMs.dockerImage == "internal.repo/dockerimage"
        assert hfMs.startCommand == "python start.py"
        assert hfMs.type == "Endpoint"
        assert hfMs.source == "HuggingFace"

        TagPatternInventory tag = queryTag {
            conditions = ["name=AI::LLM"]
        }[0] as TagPatternInventory
        ModelInventory m = addModel {
            name = "test"
            description = "description"
            modelCenterUuid = modelCenter.uuid
            installPath = "file:///root/Qwen--qwen-chat"
            parameters = "test parameters"
            logo = "BASE64logoString"
            version = "1.0.0"
            vendor = "ZStack"
            size = modelSize
            introduction = "This is md5 format string"
            tagUuids = [tag.uuid]
        } as ModelInventory

        assert Q.New(AccountResourceRefVO.class).eq(AccountResourceRefVO_.resourceUuid, m.uuid).isExists()

        // bind model service to model
        ms = bindModelToService {
            modelUuid = m.uuid
            modelServiceUuid = ms.uuid
        } as ModelServiceInventory

        // confirm query model and model service ref
        ModelInventory query = queryModel {
            conditions = ["uuid=${m.uuid}"]
        }[0] as ModelInventory
        // min gpu memory is larger 2G than model size
        assert query.size + ********** == query.minGpuMemory
        assert query.modelServiceRefs.size() == 1

        ModelServiceInventory query1 = queryModelService {
            conditions = ["uuid=${ms.uuid}"]
        }[0] as ModelServiceInventory
        assert query1.modelServiceRefs.size() == 1

        env.cleanSimulatorHandlers()
        // deploy model service
        boolean hangForTest = true
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { rsp, HttpEntity<String> entity ->
            while (hangForTest) {
                sleep(100)
            }
            return rsp
        }

        def thread = Thread.start {
            retryInSecs {
                assert queryModelServiceInstanceGroup {}.size() == 1
                ModelServiceInstanceGroupInventory testGroup = queryModelServiceInstanceGroup {}[0] as ModelServiceInstanceGroupInventory
                assert testGroup.status == ModelServiceInstanceGroupStatus.Starting.toString()
            }
            hangForTest = false
        }

        boolean hangOnStarting = true
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { rsp, HttpEntity<String> entity ->
            while (hangOnStarting) {
                sleep(100)
            }
            return rsp
        }


        boolean hangOnConfig = true
        env.afterSimulator(BentoBasedModelStorageBackend.CREATE_NGINX_RULE) { rsp, HttpEntity<String> entity ->
            rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            while (hangOnConfig) {
                sleep(100)
            }
            return rsp
        }

        boolean hangOnService = true
        env.simulator(generateUTMockPath("/readyz")) { HttpEntity<String> e, EnvSpec spec ->
            while(hangOnService) {
                sleep(100)
            }
            return null
        }

        env.simulator(generateUTMockPath("/livez")) { HttpEntity<String> e, EnvSpec spec ->
            while(hangOnService) {
                sleep(100)
            }
            return null
        }

        ModelServiceInstanceGroupInventory group
        def deployThread = Thread.start {
            group = deployModelService {
                uuid = ms.uuid
                name = "test-vm-service-deployment"
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                l3NetworkUuids = [modelCenter.serviceNetworkUuid, modelCenter.storageNetworkUuid]
                type = ModelServiceBackendType.VirtualMachine.toString()
                systemTags = ["pciDeviceSpec::${nvSpec.uuid}::1".toString()]
            } as ModelServiceInstanceGroupInventory
        }

        retryInSecs {
            ModelServiceInstanceGroupVO tmpGroup = Q.New(ModelServiceInstanceGroupVO.class)
                    .eq(ModelServiceInstanceGroupVO_.name, "test-vm-service-deployment")
                    .find()
            assert tmpGroup.status == ModelServiceInstanceGroupStatus.Starting
        }
        // update config to do track at once
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.updateValue(1)
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.updateValue(60)

        hangOnStarting = false
        retryInSecs {
            ModelServiceInstanceGroupVO tmpGroup = Q.New(ModelServiceInstanceGroupVO.class)
                    .eq(ModelServiceInstanceGroupVO_.name, "test-vm-service-deployment")
                    .find()
            assert tmpGroup.status == ModelServiceInstanceGroupStatus.ConfigSettingUp
        }
        // update config to do track at once
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.updateValue(1)
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.updateValue(60)

        hangOnConfig = false
        retryInSecs {
            ModelServiceInstanceGroupVO tmpGroup = Q.New(ModelServiceInstanceGroupVO.class)
                    .eq(ModelServiceInstanceGroupVO_.name, "test-vm-service-deployment")
                    .find()
            assert tmpGroup.status == ModelServiceInstanceGroupStatus.ServiceBootingUp
        }
        hangOnService = false

        retryInSecs {
            assert group != null
        }

        assert Q.New(AccountResourceRefVO.class).eq(AccountResourceRefVO_.resourceUuid, group.uuid).isExists()
        assert group.modelServiceUuid == ms.uuid
        assert group.instances.size() == 1
        assert (group.instances[0] as ModelServiceInstanceInventory).vm.vmNics.size() == 2
        assert (group.instances[0] as ModelServiceInstanceInventory).vm.defaultL3NetworkUuid == modelCenter.serviceNetworkUuid
        assert modelCenter.serviceNetworkUuid != null
        assert group.status == ModelServiceInstanceGroupStatus.Running.toString()
        assert group.modelUuid == m.uuid
        assert group.type == ModelServiceBackendType.VirtualMachine.toString()
        assert group.name == "test-vm-service-deployment"

        ModelServiceInstanceGroupInventory inv = updateModelServiceInstanceGroup {
            uuid = group.uuid
            name = "update-test-service-name"
        } as ModelServiceInstanceGroupInventory

        assert inv.name == "update-test-service-name"

        // test do not allow share App modelServiceType group
        SQL.New(ModelServiceInstanceGroupVO.class)
                .set(ModelServiceInstanceGroupVO_.modelServiceType, ModelServiceType.App.toString())
                .eq(ModelServiceInstanceGroupVO_.uuid, group.uuid)
                .update()
        expect(AssertionError.class) {
            shareResource {
                resourceUuids = [group.uuid]
                toPublic = true
            }
        }
        SQL.New(ModelServiceInstanceGroupVO.class)
                .set(ModelServiceInstanceGroupVO_.modelServiceType, ModelServiceType.Endpoint.toString())
                .eq(ModelServiceInstanceGroupVO_.uuid, group.uuid)
                .update()

        // confirm delete model fails
        expect(AssertionError.class) {
            deleteModel {
                uuid = group.getModelUuid()
            }
        }

        env.simulator(BentoBasedModelStorageBackend.DELETE_NGINX_RULE) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        env.simulator(BentoBasedModelStorageBackend.UPLOAD_DATASET) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.UploadDatasetResponse()
            rsp.installPath = "/path/to/test"
            rsp.size = 1024
            rsp.success = true
            return rsp
        }

        group = deployModelService {
            uuid = ms.uuid
            name = "test-vm-service-deployment-without-model"
            vmImageUuid = image.uuid
            zoneUuid = zone.uuid
            type = ModelServiceBackendType.VirtualMachine.toString()
            systemTags = ["pciDeviceSpec::${nvSpec.uuid}::1".toString()]
            l3NetworkUuids = [l3.uuid]
        } as ModelServiceInstanceGroupInventory

        retryInSecs {
            assert group != null
        }

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        DatasetInventory dataset = createDataset {
            name = "test"
            description = "test2"
            url = "http://test/path"
            modelCenterUuid = modelCenter.uuid
            systemTags = [
                    "dataset::usage::scenarios::App",
                    "dataset::datatype::Text"
            ]
        } as DatasetInventory

        // ms use image1 as default, when deploy we use image2 check, instance is using image2
        ImageInventory image2 = env.inventoryByName("image2") as ImageInventory

        group = deployModelService {
            uuid = ms.uuid
            name = "test-service-customize-network-datasets"
            vmImageUuid = image2.uuid
            zoneUuid = zone.uuid
            datasetUuids = [dataset.uuid]
            modelUuid = m.uuid
            type = ModelServiceBackendType.VirtualMachine.toString()
            systemTags = ["pciDeviceSpec::${nvSpec.uuid}::1".toString()]
            l3NetworkUuids = [l3.uuid]
        } as ModelServiceInstanceGroupInventory

        retryInSecs {
            assert group != null
        }
        assert (group.instances[0] as ModelServiceInstanceInventory).vm.imageUuid == image2.uuid

        assert Q.New(ModelServiceGroupDatasetRefVO.class)
                .eq(ModelServiceGroupDatasetRefVO_.datasetUuid, dataset.uuid).isExists()


        PrimaryStorageInventory ps = env.inventoryByName("local1") as PrimaryStorageInventory
        def p = changePrimaryStorageState {
            uuid = ps.uuid
            stateEvent = "disable"
        } as PrimaryStorageInventory

        assert p.state == PrimaryStorageState.Disabled.name()

        expectError {
            group = deployModelService {
                uuid = ms.uuid
                name = "test-service-customize-network-on-ps"
                vmImageUuid = image2.uuid
                primaryStorageUuid = ps.uuid
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                type = ModelServiceBackendType.VirtualMachine.toString()
                systemTags = ["pciDeviceSpec::${nvSpec.uuid}::1".toString()]
                l3NetworkUuids = [l3.uuid]
            } as ModelServiceInstanceGroupInventory
        }

        // check model instance all 200
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.updateValue(1)
        retryInSecs {
            group = queryModelServiceInstanceGroup {
                conditions = ["uuid=${group.uuid}"]
            }[0] as ModelServiceInstanceGroupInventory

            assert group.status == ModelServiceInstanceGroupStatus.Running.toString()
            assert group.instances.every { instance -> instance.status == ModelServiceInstanceGroupStatus.Running.toString() }
        }

        // check model instance status error
        env.simulator(generateUTMockPath("/livez")) { HttpEntity<String> e, EnvSpec spec ->
            throw new HttpError(504, "on purpose")
        }
        retryInSecs {
            group = queryModelServiceInstanceGroup {
                conditions = ["uuid=${group.uuid}"]
            }[0] as ModelServiceInstanceGroupInventory

            assert group.status == ModelServiceInstanceGroupStatus.Unknown.toString()
            assert group.instances.every { instance -> instance.status == ModelServiceInstanceGroupStatus.Unknown.toString() }
        }

        // check model instance status error
        env.simulator(generateUTMockPath("/livez")) { HttpEntity<String> e, EnvSpec spec ->
            throw new RuntimeException("on purpose")
        }
        retryInSecs {
            group = queryModelServiceInstanceGroup {
                conditions = ["uuid=${group.uuid}"]
            }[0] as ModelServiceInstanceGroupInventory

            assert group.status == ModelServiceInstanceGroupStatus.Unknown.toString()
            assert group.instances.every { instance -> instance.status == ModelServiceInstanceGroupStatus.Unknown.toString() }
        }

        env.simulator(generateUTMockPath("/livez")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        queryModel {}

        VmInstanceVO vm = dbFindByUuid(group.instances[0].vmInstanceUuid, VmInstanceVO.class)
        assert vm != null
        assert vm.cpuNum == ms.requestCpu
        assert vm.memorySize == ms.requestMemory

        env.afterSimulator(KVMConstant.KVM_STOP_VM_PATH) { rsp, HttpEntity<String> entity ->
            def cmd = JSONObjectUtil.toObject(entity.body, KVMAgentCommands.StopVmCmd.class)
            commandList.add(cmd)
            return rsp
        }
        commandList.clear()
        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }
        assert !Q.New(VmInstanceVO.class).eq(VmInstanceVO_.uuid, group.instances[0].vmInstanceUuid).isExists()
        assert commandList.size() == 1
        assert commandList[0] instanceof KVMAgentCommands.StopVmCmd

        env.simulator(generateUTMockPath("/livez")) { HttpEntity<String> e, EnvSpec spec ->
            return null
        }

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        retryInSecs {
            nvidiaDevice = queryPciDevice {
                conditions = ["uuid=${nvidiaDevice.uuid}"]
            } [0] as PciDeviceInventory

            assert nvidiaDevice.status == PciDeviceStatus.System || nvidiaDevice.status == PciDeviceStatus.Active
        }

        // customize network and ps
        group = deployModelService {
            uuid = ms.uuid
            name = "test-service-customize-network-and-primary-storage"
            vmImageUuid = image2.uuid
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 4
            memorySize = SizeUnit.GIGABYTE.toByte(4)
            type = ModelServiceBackendType.VirtualMachine.toString()
            l3NetworkUuids = [l3.uuid]
            systemTags = ["pciDevice::${nvidiaDevice.uuid}".toString()]
        } as ModelServiceInstanceGroupInventory

        retryInSecs {
            vm = dbFindByUuid(group.instances[0].vmInstanceUuid, VmInstanceVO.class)
            assert vm != null
            assert vm.cpuNum == 4
            assert vm.memorySize == SizeUnit.GIGABYTE.toByte(4)
            assert vm.cpuNum != ms.requestCpu
            assert vm.memorySize != ms.requestMemory
            assert vm.imageUuid != ms.vmImageUuid
            assert vm.imageUuid == image2.uuid
            assert PciDeviceSystemTags.AUTO_RELEASE_SPEC_RELEATED_PHYSICAL_PCI_DEVICE.hasTag(vm.uuid)
            String level = HaSystemTags.HA.getTokenByResourceUuid(vm.uuid, HaSystemTags.HA_TOKEN)
            assert StringUtils.equals(VmHaLevel.NeverStop.toString(), level)
        }

        def reportKvmEvent = notifyWhenReceivedMessage(KvmReportVmShutdownEventMsg.class)

        assert Q.New(GpuDeviceVO.class).eq(GpuDeviceVO_.vmInstanceUuid, vm.uuid).count() == 1
        env.afterSimulator(KVMConstant.KVM_STOP_VM_PATH) { rsp, HttpEntity<String> entity ->
            def stopVmCmd = JSONObjectUtil.toObject(entity.body, KVMAgentCommands.StopVmCmd.class)

            RESTFacade restFacade = bean(RESTFacade.class)
            UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(restFacade.getBaseUrl())
            ub.path(RESTConstant.COMMAND_CHANNEL_PATH)
            String url = ub.build().toUriString()
            def header = [(RESTConstant.COMMAND_PATH): KVMConstant.KVM_REPORT_VM_SHUTDOWN_EVENT]
            def cmd = new KVMAgentCommands.ReportVmShutdownEventCmd()
            cmd.vmUuid = stopVmCmd.uuid
            restFacade.syncJsonPost(url, JSONObjectUtil.toJsonString(cmd), header, KVMAgentCommands.AgentResponse)

            return rsp

        }
        rebootVmInstance {
            uuid = vm.uuid
        }

        // wait tall vm tasks finished
        retryInSecs {
            ThreadFacade thdf = bean(ThreadFacade.class)
            org.zstack.header.core.progress.ChainInfo currentChainInfo = thdf.getChainTaskInfo("Vm-${vm.uuid}".toString())
            assert currentChainInfo.pendingTask.size() == 0
            assert currentChainInfo.runningTask.size() == 0
        }

        // confirm no changes to pci devices
        assert Q.New(GpuDeviceVO.class).eq(GpuDeviceVO_.vmInstanceUuid, vm.uuid).count() == 1
        reportKvmEvent.assertNotCalled()
        reportKvmEvent.delete()

        // test vm deleted
        group = queryModelServiceInstanceGroup {
            conditions = ["uuid=${group.uuid}"]
        }[0] as ModelServiceInstanceGroupInventory
        assert group.instances.size() == 1

        def vmUuid = group.instances[0].vmInstanceUuid
        expect(AssertionError.class) {
            destroyVmInstance {
                uuid = group.instances[0].vmInstanceUuid
            }
        }

        group = queryModelServiceInstanceGroup {
            conditions = ["uuid=${group.uuid}"]
        }[0] as ModelServiceInstanceGroupInventory
        assert group.instances.size() == 1

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        assert Q.New(ModelServiceInstanceVO.class)
                .eq(ModelServiceInstanceVO_.vmInstanceUuid, vmUuid)
                .count() == 0
        assert Q.New(ModelServiceInstanceVO.class)
                .eq(ModelServiceInstanceVO_.modelServiceGroupUuid, group.uuid)
                .count() == 0

        retryInSecs {
            nvidiaDevice = queryPciDevice {
                conditions = ["uuid=${nvidiaDevice.uuid}"]
            } [0] as PciDeviceInventory

            assert nvidiaDevice.status == PciDeviceStatus.System || nvidiaDevice.status == PciDeviceStatus.Active
        }

        // ui set network as empty list when user use defualt network
        group = deployModelService {
            uuid = ms.uuid
            name = "test-empty-l3-networks"
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 4
            memorySize = SizeUnit.GIGABYTE.toByte(4)
            type = ModelServiceBackendType.VirtualMachine.toString()
            l3NetworkUuids = []
            systemTags = ["pciDevice::${nvidiaDevice.uuid}".toString()]
        } as ModelServiceInstanceGroupInventory

        retryInSecs {
            vm = dbFindByUuid(group.instances[0].vmInstanceUuid, VmInstanceVO.class)
            assert vm != null
            assert vm.cpuNum == 4
            assert vm.memorySize == SizeUnit.GIGABYTE.toByte(4)
            assert vm.cpuNum != ms.requestCpu
            assert vm.memorySize != ms.requestMemory
            assert vm.imageUuid == image.uuid
        }

        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }

        env.simulator(BentoBasedModelStorageBackend.DELETE_MODEL_SERVICE) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        // test model service group dependency
        ModelServiceInstanceGroupInventory subGroup1 = deployModelService {
            uuid = ms.uuid
            name = "test-service-sub-group-1-dep"
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 4
            memorySize = SizeUnit.GIGABYTE.toByte(4)
            type = ModelServiceBackendType.VirtualMachine.toString()
            l3NetworkUuids = []
        } as ModelServiceInstanceGroupInventory

        ModelServiceInstanceGroupInventory subGroup2 = deployModelService {
            uuid = ms.uuid
            name = "test-service-sub-group-2-dep"
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 4
            memorySize = SizeUnit.GIGABYTE.toByte(4)
            type = ModelServiceBackendType.VirtualMachine.toString()
            l3NetworkUuids = []
        } as ModelServiceInstanceGroupInventory

        group = deployModelService {
            uuid = ms.uuid
            name = "test-service-depend-on-sub1-and-sub2"
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 4
            memorySize = SizeUnit.GIGABYTE.toByte(4)
            type = ModelServiceBackendType.VirtualMachine.toString()
            l3NetworkUuids = []
        } as ModelServiceInstanceGroupInventory

        assert Q.New(ModelServiceGroupModelServiceRefVO.class)
                .eq(ModelServiceGroupModelServiceRefVO_.modelServiceInstanceGroupUuid, group.uuid)
                .count() == 0

        deleteModelServiceInstanceGroup {
            uuid = subGroup1.uuid
        }
        deleteModelServiceInstanceGroup {
            uuid = subGroup2.uuid
        }
        deleteModelServiceInstanceGroup {
            uuid = group.uuid
        }
        deleteModelServiceInstanceGroup {
            uuid = subGroup1.uuid
        }
        deleteModelServiceInstanceGroup {
            uuid = subGroup2.uuid
        }

        // test model eval service
        subGroup1 = deployModelService {
            uuid = ms.uuid
            name = "test-service-sub-group-1-eval"
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 4
            memorySize = SizeUnit.GIGABYTE.toByte(4)
            type = ModelServiceBackendType.VirtualMachine.toString()
            l3NetworkUuids = []
        } as ModelServiceInstanceGroupInventory

        subGroup2 = deployModelService {
            uuid = ms.uuid
            name = "test-service-sub-group-2-eval"
            zoneUuid = zone.uuid
            modelUuid = m.uuid
            cpuNum = 4
            memorySize = SizeUnit.GIGABYTE.toByte(4)
            type = ModelServiceBackendType.VirtualMachine.toString()
            l3NetworkUuids = []
        } as ModelServiceInstanceGroupInventory

        // model service type not expected will failed to deploy model eval service
        expect(AssertionError.class) {
            deployModelEvalService {
                uuid = ms.uuid
                name = "test-service-failed-by-not-model-eval-service"
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                taskType = "Capability"
                cpuNum = 4
                memorySize = SizeUnit.GIGABYTE.toByte(4)
                type = ModelServiceBackendType.VirtualMachine.toString()
                l3NetworkUuids = []
                modelServiceGroupUuids = [subGroup1.uuid, subGroup2.uuid]
                limits = 1
                temperature = 1.0
                topK = 5
                topP = 0.9
                maxLength = 200
                maxNewTokens = 1000
                repetitionPenalty = 0.2
            } as ModelServiceInstanceGroupInventory
        }

        updateModelService {
            uuid = ms.uuid
            type = "ModelEval"
        }

        // empty model service group uuid of model eval service is not allowed
        expect(AssertionError.class) {
            deployModelEvalService {
                uuid = ms.uuid
                name = "test-model-eval-without-service-group-failure"
                taskType = "Capability"
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                cpuNum = 4
                memorySize = SizeUnit.GIGABYTE.toByte(4)
                type = ModelServiceBackendType.VirtualMachine.toString()
                l3NetworkUuids = []
                modelServiceGroupUuids = []
                datasetUuids = [dataset.uuid]
                limits = 1
                temperature = 1.0
                topK = 5
                topP = 0.9
                maxLength = 200
                maxNewTokens = 1000
                repetitionPenalty = 0.2
            } as ModelServiceInstanceGroupInventory
        }

        // no datasets model eval is not allowed
        expect(AssertionError.class) {
            deployModelEvalService {
                uuid = ms.uuid
                name = "test-model-eval-service-without-dataset-failure"
                taskType = "Capability"
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                cpuNum = 4
                memorySize = SizeUnit.GIGABYTE.toByte(4)
                type = ModelServiceBackendType.VirtualMachine.toString()
                l3NetworkUuids = []
                modelServiceGroupUuids = [subGroup1.uuid, subGroup2.uuid]
                limits = 1
                temperature = 1.0
                topK = 5
                topP = 0.9
                maxLength = 200
                maxNewTokens = 1000
                repetitionPenalty = 0.2
            } as ModelEvalServiceInstanceGroupInventory
        }

        def startEvaluationCalled = false
        env.simulator("/start_evaluation") { HttpEntity<String> e, EnvSpec spec ->
            startEvaluationCalled = true
            throw new HttpError(504, "on purpose")
        }

        // if failed to submit start evaluation task
        // fail to deploy eval services
        long failedTaskSizeBeforeDeploy = Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Failed)
                .count()
        expect(AssertionError.class) {
            deployModelEvalService {
                uuid = ms.uuid
                name = "test-model-eval-failed-to-start-task-on-agent"
                taskType = "Capability"
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                cpuNum = 4
                memorySize = SizeUnit.GIGABYTE.toByte(4)
                type = ModelServiceBackendType.VirtualMachine.toString()
                l3NetworkUuids = []
                modelServiceGroupUuids = [subGroup1.uuid, subGroup2.uuid]
                datasetUuids = [dataset.uuid]
                limits = 1
                temperature = 1.0
                topK = 5
                topP = 0.9
                maxLength = 200
                maxNewTokens = 1000
                repetitionPenalty = 0.2
            } as ModelEvalServiceInstanceGroupInventory
        }

        retryInSecs {
            assert startEvaluationCalled
            assert Q.New(ModelEvaluationTaskVO.class)
                    .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Failed)
                    .count() == failedTaskSizeBeforeDeploy + 2
        }

        ModelEvaluationCommands.EvaluationResponse response = new ModelEvaluationCommands.EvaluationResponse()
        response.batchSize = 10
        response.status = "running"
        def evaluationDatasetCode = null
        def taskStatusCalled = false
        ModelEvaluationCommands.TaskStatusResponse statusResponse = new ModelEvaluationCommands.TaskStatusResponse()
        statusResponse.status = "running"
        statusResponse.progress = new ModelEvaluationCommands.TaskStatusProgress()
        statusResponse.progress.percentage = 0.1f
        env.simulator("/start_evaluation") { HttpEntity<String> e, EnvSpec spec ->
            ModelEvaluationCommands.EvaluationRequest request = JSONObjectUtil.toObject(e.body, ModelEvaluationCommands.EvaluationRequest.class)
            assert request.chat == null
            assert request.temperature != null
            assert request.model == null
            assert request.limit != null
            assert request.url != null
            assert request.dataset == evaluationDatasetCode

            AITestUtils.mockTaskStatus(env, [request.taskUuid], statusResponse) {
                taskStatusCalled = true
            }

            return response
        }

        // test failure to deploy model service
        expect(AssertionError.class) {
            deployModelEvalService {
                uuid = ms.uuid
                name = "test-model-eval-failed-to-start-with-wrong-tag"
                taskType = "Capability"
                zoneUuid = zone.uuid
                modelUuid = m.uuid
                cpuNum = 4
                memorySize = SizeUnit.GIGABYTE.toByte(4)
                type = ModelServiceBackendType.VirtualMachine.toString()
                l3NetworkUuids = []
                modelServiceGroupUuids = [subGroup1.uuid]
                datasetUuids = [dataset.uuid]
                limits = 1
                temperature = 1.0
                topK = 5
                topP = 0.9
                maxLength = 200
                maxNewTokens = 1000
                repetitionPenalty = 0.2
                systemTags = ["modelServiceMode::123"]
            } as ModelEvalServiceInstanceGroupInventory
        }

        evaluationDatasetCode = dataset.name
        DeployModelEvalServiceAction action = new DeployModelEvalServiceAction()
        action.uuid = ms.uuid
        action.taskType = "Capability"
        action.name = "test-model-eval-success"
        action.zoneUuid = zone.uuid
        action.modelUuid = m.uuid
        action.cpuNum = 4
        action.memorySize = SizeUnit.GIGABYTE.toByte(4)
        action.type = ModelServiceBackendType.VirtualMachine.toString()
        action.l3NetworkUuids = []
        action.modelServiceGroupUuids = [subGroup1.uuid, subGroup2.uuid]
        action.datasetUuids = [dataset.uuid]
        action.limits = 1
        action.temperature = 1.0
        action.topK = 5
        action.topP = 0.9
        action.maxLength = 200
        action.maxNewTokens = 1000
        action.repetitionPenalty = 0.2
        action.sessionId = adminSession()
        action.serviceBootUptime = 1
        action.systemTags = ["modelServiceMode::develop"]
        DeployModelEvalServiceAction.Result sdkResult = action.call()
        assert sdkResult.error == null

        DeployModelEvalServiceResult evalApiResult = sdkResult.value
        ModelEvalServiceInstanceGroupInventory evalGroup = evalApiResult.inventory
        List<ModelEvaluationTaskInventory> tasks = evalApiResult.tasks
        assert Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, evalGroup.uuid)
                .in(ModelEvaluationTaskVO_.status, Arrays.asList(ModelEvaluationTaskStatus.Created, ModelEvaluationTaskStatus.Running))
                .eq(ModelEvaluationTaskVO_.limits, 1)
                .isExists()

        assert evalGroup.limits == 1
        assert evalGroup.temperature == 1.0f
        assert evalGroup.topK == 5
        assert evalGroup.topP == 0.9f
        assert evalGroup.maxLength == 200
        assert evalGroup.maxNewTokens == 1000
        assert evalGroup.repetitionPenalty == 0.2f
        assert tasks.size() == 2
        assert queryModelEvalServiceInstanceGroup {
            conditions = ["uuid=${evalGroup.uuid}".toString()]
        }.size() == 1

        // mock and test task lifecycle
        AIGlobalConfig.MODEL_EVALUATION_TASK_TRACK_INTERVAL_SECONDS.updateValue(1)
        ModelEvaluationTaskTracker tracker = bean(ModelEvaluationTaskTracker.class)
        retryInSecs {
            assert tracker.getResourceUuids().contains(evalGroup.uuid)
            assert taskStatusCalled
        }

        assert Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, evalGroup.uuid)
                .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Running)
                .eq(ModelEvaluationTaskVO_.percentage, 10)
                .isExists()

        String taskStatusString = """{
   "status":"completed",
   "progress":{
      "percentage":1.0,
      "completed_datasets":1,
      "total_datasets":1
   },
   "summary":[
      {
         "name":"Other",
         "score":60.0,
         "leafs":[
            {
               "name":"AX_g",
               "metric":"accuracy",
               "mode":"gen",
               "model_name":"Qwen2-0.5B-Instruct",
               "score":60.0,
               "openai_api_base":"http://172.25.142.3:3000/v1/chat/completions"
            }
         ]
      }
   ],
   "total_score":60.0,
   "copied_path":"/root/bentoml/model_evaluations/results/467d1acd_172.25.142.3_Qwen2-0.5B-Instruct_AX_g"
}"""

        statusResponse = JSONObjectUtil.toObject(taskStatusString, ModelEvaluationCommands.TaskStatusResponse.class)
        AITestUtils.mockTaskStatus(env, tasks.collect { it.uuid }, statusResponse) {
            taskStatusCalled = true
        }
        retryInSecs {
            assert !tracker.getResourceUuids().contains(evalGroup.uuid)
        }

        assert Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, evalGroup.uuid)
                .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Completed)
                .eq(ModelEvaluationTaskVO_.percentage, 100)
                .eq(ModelEvaluationTaskVO_.evaluatedServiceGroupUuid, subGroup1.uuid)
                .isExists()
        assert Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, evalGroup.uuid)
                .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Completed)
                .eq(ModelEvaluationTaskVO_.percentage, 100)
                .eq(ModelEvaluationTaskVO_.evaluatedServiceGroupUuid, subGroup2.uuid)
                .isExists()
        ModelEvaluationTaskVO completedTask = Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, evalGroup.uuid)
                .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Completed)
                .eq(ModelEvaluationTaskVO_.percentage, 100)
                .limit(1)
                .find()

        assert completedTask.status == ModelEvaluationTaskStatus.Completed
        assert completedTask.percentage == 100
        AIModelManagerImpl.EvaluationTaskResult evalResult = JSONObjectUtil.toObject(completedTask.opaque, AIModelManagerImpl.EvaluationTaskResult.class)
        ModelEvaluationCommands.TaskStatusResponse responseInDb = JSONObjectUtil.rehashObject(evalResult.details, ModelEvaluationCommands.TaskStatusResponse.class)
        assert responseInDb.status == statusResponse.status
        assert responseInDb.totalScore == statusResponse.totalScore
        assert responseInDb.progress.percentage == statusResponse.progress.percentage
        assert responseInDb.progress.completedDatasets == statusResponse.progress.completedDatasets
        assert responseInDb.progress.totalDatasets == statusResponse.progress.totalDatasets
        assert responseInDb.summary.name == statusResponse.summary.name
        assert responseInDb.summary.metric == statusResponse.summary.metric
        assert responseInDb.summary.mode == statusResponse.summary.mode
        assert responseInDb.summary.modelName == statusResponse.summary.modelName
        assert responseInDb.summary.score == statusResponse.summary.score
        assert responseInDb.totalScore == 60.0f
        assert responseInDb.resultPath == "file:///root/bentoml/model_evaluations/results/467d1acd_172.25.142.3_Qwen2-0.5B-Instruct_AX_g"
        assert responseInDb.summary[0].name == "Other"
        assert responseInDb.summary[0].score == 60.0f
        assert responseInDb.summary[0].leafs.size() == 1
        assert responseInDb.summary[0].leafs[0].name == "AX_g"
        retryInSecs {
            assert !Q.New(ModelServiceInstanceGroupVO.class)
                    .eq(ModelServiceInstanceGroupVO_.uuid, evalGroup.uuid)
                    .isExists()
        }

        deleteModelServiceInstanceGroup {
            uuid = evalGroup.uuid
        }

        // action
//        action.datasetUuids = ["9c5b0ceb21554e9c9b93b8bfe59572b0"]
//        evaluationDatasetCode = "mbpp"
//        sdkResult = action.call()
//        evalApiResult = sdkResult.value
//        evalGroup = evalApiResult.inventory
//        retryInSecs {
//            assert !Q.New(ModelServiceInstanceGroupVO.class)
//                    .eq(ModelServiceInstanceGroupVO_.uuid, evalGroup.uuid)
//                    .isExists()
//        }

        List<ModelEvaluationTaskInventory> allTasks = queryModelEvaluationTask {
        }
        for (ModelEvaluationTaskInventory deleteTask : allTasks) {
            deleteModelEvaluationTask {
                uuid = deleteTask.uuid
            }
        }

        assert !Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, evalGroup.uuid)
                .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Completed)
                .eq(ModelEvaluationTaskVO_.percentage, 100)
                .isExists()

        deleteModelServiceInstanceGroup {
            uuid = evalGroup.uuid
        }
        deleteModelServiceInstanceGroup {
            uuid = subGroup1.uuid
        }
        deleteModelServiceInstanceGroup {
            uuid = subGroup2.uuid
        }

        // test cases not in normal api lifecycles
        // create batch of no service tasks to track all of them marked as failure
        DatabaseFacade dbf = bean(DatabaseFacade.class)
        tracker.inProcessStatus.each { st ->
            ModelEvaluationTaskVO task = new ModelEvaluationTaskVO()
            task.setUuid(Platform.getUuid())
            task.setName(group.getName())
            task.setModelServiceGroupUuid(group.getUuid())
            task.setEvaluatedServiceGroupUuid(Platform.getUuid())
            task.setDatasetUuid(Platform.getUuid())
            task.setPercentage(0)
            task.setStatus(ModelEvaluationTaskStatus.Creating)
            task.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID)
            dbf.persist(task)
        }

        assert Q.New(ModelEvaluationTaskVO.class).count() == tracker.inProcessStatus.size()
        retryInSecs {
            assert Q.New(ModelEvaluationTaskVO.class)
                    .eq(ModelEvaluationTaskVO_.status, ModelEvaluationTaskStatus.Failed)
                    .count() == tracker.inProcessStatus.size()
        }

        allTasks = queryModelEvaluationTask {
        }
        for (ModelEvaluationTaskInventory deleteTask : allTasks) {
            deleteModelEvaluationTask {
                uuid = deleteTask.uuid
            }
        }

        // test delete model service
        deleteModelService {
            uuid = ms.uuid
        }

        env.simulator(BentoBasedModelStorageBackend.DELETE_MODEL) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        deleteModel {
            uuid = m.uuid
        }

        deleteDataset {
            uuid = dataset.uuid
        }
    }

    void testModelLifecycle() {
        // test add model
        ModelCenterInventory mc = addModelCenter {
            name = "test"
            description = "description"
            url = "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
            parameters = "test parameters"
            managementIp = "*********"
            managementPort = 8989
        } as ModelCenterInventory

        // test model center config set failure
        env.afterSimulator(BentoBasedModelStorageBackend.CONFIG_SET) { rsp, HttpEntity<String> e ->
            throw new HttpError(504, "on purpose")
        }
        expect(AssertionError.class) {
            addModelCenter {
                name = "test"
                description = "description"
                url = "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
                parameters = "test parameters"
                managementIp = "*********"
                managementPort = 8989
            } as ModelCenterInventory
        }
        env.afterSimulator(BentoBasedModelStorageBackend.CONFIG_SET) { rsp, HttpEntity<String> e ->
            return rsp
        }

        // mock storage add model failure
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { rsp, HttpEntity<String> e ->
            rsp.error = "on purpose failure"
            rsp.success = false
            return rsp
        }

        // test add model failed
        expect(AssertionError.class) {
            addModel {
                name = "test"
                description = "description"
                modelCenterUuid = mc.uuid
                installPath = "file:///root/Qwen--qwen-chat"
                parameters = "test parameters"
                logo = "BASE64logoString"
                version = "1.0.0"
                vendor = "ZStack"
                size = 102400
                introduction = "This is md5 format string"
            } as ModelInventory
        }

        env.cleanAfterSimulatorHandlers()

        // test add model with wrong model center uuid
        expect(AssertionError.class) {
            addModel {
                name = "test"
                description = "description"
                modelCenterUuid = Platform.getUuid()
                installPath = "file:///root/Qwen--qwen-chat"
                parameters = "test parameters"
                logo = "BASE64logoString"
                version = "1.0.0"
                vendor = "ZStack"
                size = 102400
                introduction = "This is md5 format string"
            } as ModelInventory
        }

        // test add model with wrong model service uuid
        expect(AssertionError.class) {
            addModel {
                name = "test"
                description = "description"
                modelCenterUuid = mc.uuid
                installPath = "file:///root/Qwen--qwen-chat"
                parameters = "test parameters"
                logo = "BASE64logoString"
                version = "1.0.0"
                vendor = "ZStack"
                size = 102400
                introduction = "This is md5 format string"
                modelServiceUuids = [Platform.getUuid()]
            } as ModelInventory
        }

        // test add model will pass token to agent
        def expectedTimeoutInSeconds = TimeUnit.HOURS.toSeconds(72)
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, BentoBasedModelStorageBackend.UploadModelCmd.class)
            assert cmd.token != null
            assert cmd.timeoutInSeconds == expectedTimeoutInSeconds
            return rsp
        }

        def called = false
        TagPatternInventory tag = queryTag {
            conditions = ["uuid=f2a1b3c4d5e6a7b8c9d0e1f2a3b4c5d6"]
        }[0] as TagPatternInventory
        // test add model success
        ModelInventory model = addModel {
            name = "test"
            description = "description"
            modelCenterUuid = mc.uuid
            installPath = "file:///root/Qwen--qwen-chat"
            parameters = "test parameters"
            logo = "BASE64logoString"
            version = "1.0.0"
            vendor = "ZStack"
            size = modelSize
            introduction = "This is md5 format string"
            tagUuids = [tag.uuid]
            token = "test token"
        } as ModelInventory

        assert model.name == "test"
        assert model.description == "description"
        assert model.modelCenterUuid == mc.uuid
        assert model.installPath == "file:///user/home/<USER>"
        assert model.parameters == "test parameters"
        assert model.size == 15451144847L
        assert model.type == ModelType.Custom.toString()

        env.cleanAfterSimulatorHandlers()

        // test query result
        model = queryModel {
            conditions = ["uuid=${model.uuid}"]
        }[0] as ModelInventory

        assert model.name == "test"
        assert model.description == "description"
        assert model.modelCenterUuid == mc.uuid
        assert model.installPath == "file:///user/home/<USER>"
        assert model.parameters == "test parameters"
        assert model.createDate != null
        assert model.lastOpDate != null

        // test query with tag
        def lastQueryUuid = model.uuid
        model = queryModel {
            conditions = ["__tagUuid__==f2a1b3c4d5e6a7b8c9d0e1f2a3b4c5d6", "uuid=${lastQueryUuid}"]
        }[0] as ModelInventory
        assert model.uuid == lastQueryUuid

        // test update model
        model = updateModel {
            uuid = model.uuid
            name = "test1"
            description = "description1"
            modelCenterUuid = mc.uuid
            installPath = "/models/test1"
            parameters = "test parameters1"
            modelClassifications = ["AI::LLM"]
        } as ModelInventory

        assert model.name == "test1"
        assert model.description == "description1"
        assert model.modelCenterUuid == mc.uuid
        assert model.installPath == "/models/test1"
        assert model.parameters == "test parameters1"
        assert Q.New(UserTagVO.class)
            .eq(UserTagVO_.resourceUuid, model.uuid)
            .eq(UserTagVO_.tag, "AI::LLM")
            .isExists()

        // test update model's type
        model = updateModel {
            uuid = model.uuid
            modelClassifications = ["AI::Audio"]
        } as ModelInventory
        assert Q.New(UserTagVO.class)
                .eq(UserTagVO_.resourceUuid, model.uuid)
                .eq(UserTagVO_.tag, "AI::Audio")
                .isExists()
        assert !Q.New(UserTagVO.class)
                .eq(UserTagVO_.resourceUuid, model.uuid)
                .eq(UserTagVO_.tag, "AI::LLM")
                .isExists()

        // test update model failed with wrong model center
        expect(AssertionError.class) {
            updateModel {
                uuid = model.uuid
                name = "test1"
                description = "description1"
                modelCenterUuid = Platform.getUuid()
                installPath = "/models/test1"
                parameters = "test parameters1"
            } as ModelInventory
        }

        // test delete model failed
        env.simulator(BentoBasedModelStorageBackend.DELETE_MODEL) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.error = "on purpose failure"
            rsp.success = false
            return rsp
        }

        expect(AssertionError.class) {
            deleteModel {
                uuid = model.uuid
            }
        }

        env.simulator(BentoBasedModelStorageBackend.DELETE_MODEL) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, BentoBasedModelStorageBackend.DeleteModelCmd.class)
            assert cmd.uuid != null
            assert Q.New(ModelVO.class)
                    .eq(ModelVO_.version, cmd.uuid)
                    .isExists()
            assert Q.New(ModelVO.class)
                    .eq(ModelVO_.installPath, cmd.installPath)
                    .isExists()
            def rsp = new BentoBasedModelStorageBackend.Response()
            rsp.success = true
            return rsp
        }

        // test delete model
        deleteModel {
            uuid = model.uuid
        }

        assert queryModel {
            conditions = ["uuid=${model.uuid}"]
        }.isEmpty()

        // add model by long job
        APIAddModelMsg jobMessage = new APIAddModelMsg()
        jobMessage.name = "test1"
        jobMessage.description = "description1"
        jobMessage.modelCenterUuid = mc.uuid
        jobMessage.installPath = "/models/test1"
        jobMessage.parameters = "test parameters1"
        def modelUuid = Platform.getUuid()
        jobMessage.resourceUuid = modelUuid

        def hangOnUpload = true
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { rsp, HttpEntity<String> e ->
            while (hangOnUpload) {
                sleep(200)
            }

            return rsp
        }

        def progress = [0, 50, 100]
        AtomicInteger counter = new AtomicInteger(0)
        env.simulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_PROGRESS) { HttpEntity<String> entity, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(entity.body, BentoBasedModelStorageBackend.ModelUploadProgressCmd.class)
            assert cmd.taskUuid != null
            def rsp = new BentoBasedModelStorageBackend.UploadProgressResponse()
            rsp.success = true
            int index = counter.getAndIncrement()
            index = index >= 2 ? 2 : index
            rsp.progress = progress.getAt(index)
            called = true
            return rsp
        }
        expectedTimeoutInSeconds = TimeUnit.HOURS.toMillis(72)

        def addModelSent = false
        LongJobManagerImpl longJobManager = bean(LongJobManagerImpl.class)
        assert longJobManager.longJobClasses.contains(AddModelLongJob.class.toString())
        def cleanup = notifyWhenReceivedMessage(AddModelMsg.class) { AddModelMsg msg ->
            assert msg.getTimeout() == expectedTimeoutInSeconds
            addModelSent = true
        }
        def job = submitLongJob {
            jobName = jobMessage.getClass().getSimpleName()
            jobData = JSONObjectUtil.toJsonString(jobMessage)
        } as LongJobInventory

        retryInSecs {
            assert addModelSent
        }

        cleanup()

        ConcurrentLinkedQueue<TaskProgressInventory> progressList = new ConcurrentLinkedQueue<>()
        retryInSecs(15) {
            TaskProgressInventory process = getTaskProgress {
                apiId = job.apiId
            }[0] as TaskProgressInventory
            progressList.add(process)

            assert process.content == "100"
        }

        assert progressList != null

        hangOnUpload = false
        retryInSecs {
            job = queryLongJob {
                conditions = ["uuid=${job.uuid}"]
            }[0] as LongJobInventory

            assert job.state == LongJobState.Succeeded
        }

        org.zstack.ai.entity.ModelInventory modelFromLongJob = JSONObjectUtil
                .toObject(job.getJobResult(), org.zstack.ai.entity.ModelInventory.class)
        assert modelFromLongJob.name == "test1"
        assert modelFromLongJob.description == "description1"
        assert modelFromLongJob.modelCenterUuid == mc.uuid
        assert modelFromLongJob.parameters == "test parameters1"
        assert queryAudit {
            conditions = ["resourceUuid=${modelFromLongJob.uuid}", "apiName=${APIAddModelMsg.class.name}".toString()]
        }.size() == 1

        List<String> progressContents = progressList.collect { it.getContent() }
        Set<String> resultSet = new HashSet<>(progressContents)
        assert resultSet.contains("100")

        AtomicInteger integer = new AtomicInteger(0)
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL_PROGRESS) { rsp, HttpEntity<String> e ->
            integer.incrementAndGet()
            return rsp
        }

        // test upload api failure
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { rsp, HttpEntity<String> e ->
            throw new HttpError(504, "on purpose")
        }
        job = submitLongJob {
            jobName = jobMessage.getClass().getSimpleName()
            jobData = JSONObjectUtil.toJsonString(jobMessage)
        } as LongJobInventory
        retryInSecs {
            job = queryLongJob {
                conditions = ["uuid=${job.uuid}"]
            }[0] as LongJobInventory

            assert job.state == LongJobState.Failed
        }

        // check agent failure will change model center status
        retryInSecs {
            assert Q.New(ModelCenterVO.class)
                    .eq(ModelCenterVO_.status, ModelCenterStatus.Disconnected)
                    .isExists()
        }

        retryInSecs {
            assert integer.get() >= 1
        }
        integer.set(0)

        // test upload return but success = false
        env.afterSimulator(BentoBasedModelStorageBackend.UPLOAD_MODEL) { BentoBasedModelStorageBackend.UploadModelResponse rsp, HttpEntity<String> e ->
            rsp.success = false
            rsp.error = "no purpose"
            return rsp
        }
        job = submitLongJob {
            jobName = jobMessage.getClass().getSimpleName()
            jobData = JSONObjectUtil.toJsonString(jobMessage)
        } as LongJobInventory
        retryInSecs {
            job = queryLongJob {
                conditions = ["uuid=${job.uuid}"]
            }[0] as LongJobInventory

            assert job.state == LongJobState.Failed
        }
        retryInSecs {
            assert integer.get() >=1
        }
        integer.set(0)
        env.cleanAfterSimulatorHandlers()
    }

    void testModelCenterLifecycle() {
        env.afterSimulator(BentoBasedModelStorageBackend.CONFIG_SET) { rsp, HttpEntity<String> e ->
            def failureRsp = new BentoBasedModelStorageBackend.ConfigUpdateResponse()
            failureRsp.success = false
            return failureRsp
        }

        expect(AssertionError.class) {
            addModelCenter {
                name = "test"
                description = "description"
                url = "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
                parameters = "test parameters"
                managementIp = "*********"
                managementPort = 8989
            }
        }

        env.cleanAfterSimulatorHandlers()
        env.simulator(BentoBasedModelStorageBackend.CONFIG_SET) { HttpEntity<String> entity, EnvSpec spec ->
            def rsp = new BentoBasedModelStorageBackend.ConfigUpdateResponse()
            rsp.success = true
            return rsp
        }

        // test add model center
        ModelCenterInventory mc = addModelCenter {
            name = "test"
            description = "description"
            url = "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
            parameters = "test parameters"
            managementIp = "127.0.0.10"
            managementPort = 8989
        } as ModelCenterInventory

        assert mc.name == "test"
        assert mc.description == "description"
        assert mc.url == "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
        assert mc.parameters == "test parameters"

        assert mc.zdfs != null
        assert mc.zdfs.hostName == "127.0.0.10"
        assert mc.zdfs.sshPort == 22
        assert mc.zdfs.status == "Connected"
        assert mc.zdfs.url == "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
        assert mc.zdfs.storage.type == StorageType.NFS

        def zdfsInv = queryZdfs {conditions=["uuid=${mc.zdfs.uuid}".toString()]}[0] as ZdfsInventory

        zdfsInv = reconnectZdfs {
            uuid = zdfsInv.uuid
        } as ZdfsInventory

        assert zdfsInv.hostName == "127.0.0.10"

        // test query result
        mc = queryModelCenter {
            conditions = ["uuid=${mc.uuid}"]
        }[0] as ModelCenterInventory

        assert mc.name == "test"
        assert mc.description == "description"
        assert mc.url == "redis://:zstack.redis.password@mymaster,************:5000,************:5000,************:5000/0"
        assert mc.parameters == "test parameters"

        env.hijackSimulator(String.format("%s%s", ZdfsBase.API_VERSION, ZdfsBase.PING)) { rsp, HttpEntity<String> e ->
            throw new Exception("on purpose")
        }

        expectError {
            updateModelCenter {
                uuid = mc.uuid
                managementIp = "*********8"
                url = "http://*********:8080"
            }
        }

        def mcInv = queryModelCenter {
            conditions = ["uuid=${mc.uuid}"]
        }[0] as ModelCenterInventory

        assert mcInv.managementIp == mc.managementIp
        assert mcInv.url == mc.url
        assert mcInv.zdfs.hostName == mc.managementIp

        env.cleanFinalSimulatorHandlers()

        // test update model center
        mc = updateModelCenter {
            uuid = mc.uuid
            name = "test1"
            description = "description1"
            url = "redis://:zstack.redis.password@mymaster,************:5111,************:5111,************:5111/0"
            parameters = "test parameters1"
            managementIp = "127.0.0.11"
            managementPort = 8888
        } as ModelCenterInventory

        assert mc.name == "test1"
        assert mc.description == "description1"
        assert mc.url == "redis://:zstack.redis.password@mymaster,************:5111,************:5111,************:5111/0"
        assert mc.parameters == "test parameters1"
        assert mc.managementIp == "127.0.0.11"
        assert mc.managementPort == 8888

        mc = updateModelCenter {
            uuid = mc.uuid
            managementIp = "127.0.0.1"
            managementPort = 8989
        } as ModelCenterInventory
        assert mc.managementIp == "127.0.0.1"
        assert mc.managementPort == 8989

        L3NetworkInventory l3 = env.inventoryByName("l3") as L3NetworkInventory
        L3NetworkInventory pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory

        mc = updateModelCenter {
            uuid = mc.uuid
            storageNetworkUuid = l3.uuid
            serviceNetworkUuid = pubL3.uuid
        } as ModelCenterInventory

        assert mc.storageNetworkUuid == l3.uuid
        assert mc.serviceNetworkUuid == pubL3.uuid
        assert mc.name == "test1"
        assert mc.description == "description1"
        assert mc.url == "redis://:zstack.redis.password@mymaster,************:5111,************:5111,************:5111/0"
        assert mc.parameters == "test parameters1"

        mc = updateModelCenter {
            uuid = mc.uuid
            containerRegistry = "127.0.0.1:5443/registry"
            containerNetwork = "ai-test-network"
        } as ModelCenterInventory
        assert mc.storageNetworkUuid == l3.uuid
        assert mc.serviceNetworkUuid == pubL3.uuid
        assert mc.containerRegistry == "127.0.0.1:5443/registry"
        assert mc.containerNetwork == "ai-test-network"

        // test delete model center
        deleteModelCenter {
            uuid = mc.uuid
        }

        assert queryModelCenter {
            conditions = ["uuid=${mc.uuid}"]
        }.isEmpty()
    }

    @Override
    void clean() {
        env.delete()
    }
}
