package org.zstack.test.integration.premium.imageStore

import org.zstack.core.cloudbus.CloudBus
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.SimpleQuery
import org.zstack.header.image.ImageConstant
import org.zstack.header.image.ImagePlatform
import org.zstack.header.message.MessageReply
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.storage.primary.DownloadImageToPrimaryStorageCacheMsg
import org.zstack.header.storage.primary.ImageCacheVO
import org.zstack.header.storage.primary.ImageCacheVO_
import org.zstack.mevoco.MevocoGlobalConfig
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.sdk.BackupStorageInventory
import org.zstack.sdk.HostInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.storage.primary.local.LocalStorageKvmBackend
import org.zstack.storage.primary.local.LocalStorageUtils
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.LocalStorageSpec
import org.zstack.testlib.Test
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.vfs.VFS
import org.zstack.utils.data.SizeUnit

import java.util.concurrent.TimeUnit

import static org.zstack.core.Platform.operr

/**
 * Created by lining on 2017/3/11.
 */

/**
 * 1. add an image
 * <p>
 * confirm the image distributed to the host1
 * <p>
 * 2. add a new host
 * <p>
 * confirm the image distributed to the host2
 * <p>
 * 3. delete the image cache
 * 4. reconnect the host2
 * <p>
 * confirm the image distributed to the host2
 * <p>
 * 5. reconnect host1
 * <p>
 * confirm no images get re-downloaded
 */
// base on TestMevoco1
class ImageCacheCase extends PremiumSubCase{
    EnvSpec env

    DatabaseFacade dbf

    long volumeBitSize = SizeUnit.GIGABYTE.toByte(50)

    long virtualRouterBitSize = SizeUnit.MEGABYTE.toByte(512)

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = Test.makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
            }

            diskOffering {
                name = 'diskOffering'
                diskSize = volumeBitSize
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                    size = SizeUnit.GIGABYTE.toByte(10)
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm"
                        managementIp = "localhost"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"
                        service {
                            provider = VirtualRouterConstant.PROVIDER_TYPE
                            types = [NetworkServiceType.DHCP.toString(), NetworkServiceType.DNS.toString()]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "**************0"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vr"
                    memory = virtualRouterBitSize
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }
        }
    }

    @Override
    void test() {
        env.create {
            testDistributeImage()
        }
    }

    private void deleteImageCache(ImageCacheVO cacheVO, String localStorageMountPath) {
        LocalStorageUtils.InstallPath path = new LocalStorageUtils.InstallPath();
        path.fullPath = cacheVO.installUrl
        path.disassemble()

        VFS vfs = LocalStorageSpec.vfs(path.hostUuid, localStorageMountPath, env)
        vfs.delete(path.installPath)
        dbf.remove(cacheVO)
    }

    void testDistributeImage(){
        dbf = bean(DatabaseFacade.class)

        MevocoGlobalConfig.DISTRIBUTE_IMAGE.updateValue(true)
        BackupStorageInventory sftp = env.inventoryByName("sftp")
        PrimaryStorageInventory local = env.inventoryByName("local")

        ImageInventory newImageInventory = addImage {
            name = "image"
            platform = ImagePlatform.Linux.toString()
            mediaType = ImageConstant.ImageMediaType.RootVolumeTemplate.toString()
            format = "qcow2"
            url = "http://zstack.org/download/test.qcow2"
            backupStorageUuids = [sftp.uuid]
        }

        assert null != newImageInventory

        HostInventory host1 = env.inventoryByName("kvm")

        retryInSecs {
            assert null != findImageOnHost(local.uuid, newImageInventory.uuid, host1.uuid)
        }

        HostInventory host2 = addKVMHost {
            managementIp = "127.0.0.1"
            name = "kvm2"
            clusterUuid = env.inventoryByName("cluster").uuid
            username = "root"
            password = "password"
        }

        ImageCacheVO cacheVO = null
        retryInSecs {
            cacheVO = findImageOnHost(local.uuid, newImageInventory.uuid, host2.uuid)
            assert null != cacheVO
            assert local.uuid == cacheVO.primaryStorageUuid
            assert newImageInventory.uuid == cacheVO.imageUuid
        }


        deleteImageCache(cacheVO, local.mountPath)

        reconnectHost{
            uuid = host2.uuid
        }

        // wait 3s for recovery image to host
        TimeUnit.SECONDS.sleep(3)
        cacheVO = findImageOnHost(local.uuid, newImageInventory.uuid, host2.uuid)
        assert null != cacheVO

        // if system repeat the download image, the error will be executed
        env.message(DownloadImageToPrimaryStorageCacheMsg.class) { DownloadImageToPrimaryStorageCacheMsg msg, CloudBus bus ->
            def reply = new MessageReply()
            reply.setError(operr("on purpose"))
            bus.reply(msg, reply)
        }

        reconnectHost{
            uuid = host1.uuid
        }
    }

    ImageCacheVO findImageOnHost(String prUuid, String imgUuid, String hostUuid) {
        SimpleQuery<ImageCacheVO> q = dbf.createQuery(ImageCacheVO.class)
        q.add(ImageCacheVO_.imageUuid, SimpleQuery.Op.EQ, imgUuid)
        q.add(ImageCacheVO_.primaryStorageUuid, SimpleQuery.Op.EQ, prUuid)
        List<ImageCacheVO> caches = q.list()
        for (ImageCacheVO c : caches) {
            if (c.getInstallUrl().contains(hostUuid)) {
                return c
            }
        }

        return null
    }

    @Override
    void clean() {
        env.delete()
    }
}
