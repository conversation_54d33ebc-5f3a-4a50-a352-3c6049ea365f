package org.zstack.test.integration.premium.vdpa

import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.cluster.MevocoClusterGlobalConfig
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.db.Q
import org.zstack.header.host.HostStateEvent
import org.zstack.header.host.HostStatus
import org.zstack.header.host.HostVO
import org.zstack.header.host.HostVO_
import org.zstack.header.host.NetworkInterfaceType
import org.zstack.header.vdpa.VmVdpaNicVO
import org.zstack.header.vdpa.VmVdpaNicVO_
import org.zstack.network.hostNetworkInterface.HostNetworkBondingInventory
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceInventory
import org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend
import org.zstack.pciDevice.PciDeviceTOExampleMaker
import org.zstack.sdk.CloneVmInstanceResult
import org.zstack.sdk.ClusterInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.KVMHostInventory
import org.zstack.sdk.L2NetworkInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit

/**
 * <AUTHOR>
 * @date 2021/10/1
 * since
 */
class TestCloneVmWithVdpaNicCase extends PremiumSubCase{
    EnvSpec env
    VmInstanceInventory vm_running

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            cephBackupStorage {
                name = "ceph-bk"
                fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                monUrls = ["root:password@localhost:23", "root:password@127.0.0.1:23"]

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster-ovs"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm"
                        managementIp = "localhost"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("ceph-pri")
                }

                cephPrimaryStorage {
                    name = "ceph-pri"
                    fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                    monUrls = ["root:password@localhost/?monPort=7777", "root:password@127.0.0.1/?monPort=7777"]
                }

                l2NoVlanNetwork {
                    name = "l2-ovs"
                    vSwitchType = "OvsDpdk"
                    physicalInterface = "bond0"
                    l3Network {
                        name = "l3-ovs"
                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }
                }

                attachBackupStorage("ceph-bk")
            }
        }
    }

    @Override
    void test() {
        env.create{
            initEnv()
            TestCloneVmWithVdpa()
        }
    }

    void initEnv() {

        updateGlobalConfig {
            category = "networkService"
            name = "enableVHostUser"
            value = "false"
        }

        ClusterInventory cluster = env.inventoryByName("cluster-ovs")
        KVMHostInventory kvm = env.inventoryByName("kvm")
        L2NetworkInventory l2_ovs = env.inventoryByName("l2-ovs")
        InstanceOfferingInventory offeringInventory = env.inventoryByName("instanceOffering")
        ImageInventory image = env.inventoryByName("image1")
        L3NetworkInventory l3_ovs = env.inventoryByName("l3-ovs")

        // set host under cluster to maintenance mode
        changeHostState {
            uuid = kvm.getUuid()
            stateEvent = HostStateEvent.maintain.toString()
        }

        // enable OVS_DPDK_SUPPORT
        updateResourceConfig {
            category = MevocoClusterGlobalConfig.CATEGORY
            name = MevocoClusterGlobalConfig.HUGEPAGE_ENABLE.name
            value = "true"
            resourceUuid = cluster.uuid
        }
        updateResourceConfig {
            category = MevocoClusterGlobalConfig.CATEGORY
            name = MevocoClusterGlobalConfig.OVS_DPDK_SUPPORT.name
            value = "true"
            resourceUuid = cluster.uuid
        }

        // set host under cluster to enable mode
        changeHostState {
            uuid = kvm.getUuid()
            stateEvent = HostStateEvent.enable.toString()
        }

        reconnectHost {
            uuid = kvm.getUuid()
        }

        HostVO kvmVo = Q.New(HostVO.class).eq(HostVO_.uuid, kvm.getUuid()).find();
        assert kvmVo.getStatus() == HostStatus.Connected

        env.simulator(PciDeviceKvmBackend.GET_PCI_DEVICES) { HttpEntity<String> entity, EnvSpec spec ->
            PciDeviceKvmBackend.GetPciDevicesRsp rsp = new PciDeviceKvmBackend.GetPciDevicesRsp()
            rsp.pciDevicesInfo.addAll(Arrays.asList(
                    PciDeviceTOExampleMaker.PCI_BRIDGE(),
                    PciDeviceTOExampleMaker.MOXA_DEVICE(),
                    PciDeviceTOExampleMaker.GT710B_VIDEO(),
                    PciDeviceTOExampleMaker.GT710B_AUDIO(),
                    PciDeviceTOExampleMaker.MLNX_CX5_VIRTUALIZED_PF1(),
                    PciDeviceTOExampleMaker.MLNX_CX5_VIRTUALIZED_PF2(),
                    PciDeviceTOExampleMaker.MLNX_CX5_VF1(),
                    PciDeviceTOExampleMaker.MLNX_CX5_VF2(),
            ))
            rsp.hostIommuStatus = true
            rsp.setSuccess(true)
            return rsp
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> entity, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory();
            inv1.setInterfaceName("enp101s0f0");
            inv1.setSpeed(10000L);
            inv1.setCarrierActive(true);
            inv1.setMac("ac:1f:6b:93:6c:8c");
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory();
            inv2.setInterfaceName("enp101s0f1");
            inv2.setSpeed(10000L);
            inv2.setCarrierActive(true);
            inv2.setMac("ac:1f:6b:93:6c:8d");
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2];

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.OVS_BONDING_TYPE.toString());
            bond0.setMode("active-backup 1");
            bond0.setXmitHashPolicy("layer 3+4");
            bond0.setSlaves(reply.nics);

            reply.bondings = [bond0]

            return reply;
        }

        List<KVMHostInventory> result = queryHost {} as List<KVMHostInventory>
        for (KVMHostInventory host : result) {
            updateHostIommuState {
                uuid = host.uuid
                state = "Enabled"
            }
            reconnectHost {
                uuid = host.uuid
            }
        }

        retryInSecs(15) {
            assert (queryHost {} as List<KVMHostInventory>).stream().allMatch{ inv -> inv.status == HostStatus.Connected.toString() }
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = l2_ovs.getUuid()
            clusterUuid = cluster.getUuid()
        }

        vm_running = createVmInstance {
            name = "vm-running"
            instanceOfferingUuid = offeringInventory.uuid
            imageUuid = image.uuid
            defaultL3NetworkUuid = l3_ovs.uuid
            l3NetworkUuids = [l3_ovs.uuid]
        } as VmInstanceInventory
    }

    void TestCloneVmWithVdpa() {
        CloneVmInstanceResult cloned = cloneVmInstance {
            names = ["vm-c1", "vm-c2", "vm-c3", "vm-c4"]
            vmInstanceUuid = vm_running.uuid
        }

        assert cloned != null
        assert cloned.result.getNumberOfClonedVm() == 1
        assert Q.New(VmVdpaNicVO.class).notNull(VmVdpaNicVO_.pciDeviceUuid).notNull(VmVdpaNicVO_.srcPath).count() == 2
    }

    @Override
    void clean() {
        env.delete()
    }
}
