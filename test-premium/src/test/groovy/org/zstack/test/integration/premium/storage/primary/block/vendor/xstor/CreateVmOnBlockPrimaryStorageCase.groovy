package org.zstack.test.integration.premium.storage.primary.block.vendor.xstor

import org.springframework.http.HttpEntity
import org.zstack.compute.vm.VmSystemTags
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.errorcode.OperationFailureException
import org.zstack.header.storage.primary.ImageCacheVO
import org.zstack.header.storage.primary.ImageCacheVO_
import org.zstack.header.vm.VmInstanceConstant
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.header.image.ImageConstant
import org.zstack.header.image.ImagePlatform
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.userdata.UserdataConstant
import org.zstack.sdk.*
import org.zstack.storage.primary.block.vendor.xstor.LunMap
import org.zstack.storage.primary.block.vendor.xstor.XStorDevice
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.plugin.block.XStorStorageDeviceHelper
import org.zstack.utils.data.SizeUnit

import javax.servlet.http.HttpServletRequest
import java.util.stream.Collectors

import static org.zstack.core.Platform.operr

/**
 * <AUTHOR>
 * @date 2022/12/19 14:41
 */
class CreateVmOnBlockPrimaryStorageCase extends PremiumSubCase{
    EnvPremiumSpec env
    ZoneInventory zoneInv
    ClusterInventory clusterInventory
    PrimaryStorageInventory ps1, ps2
    XStorStorageDeviceHelper helper
    HostInventory hostInventory
    L3NetworkInventory l3
    ImageInventory image, isoImage, isoImage2, image4
    DiskOfferingInventory diskOffering

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = PremiumTest.makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(4)
                cpu = 1
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(10)
            }

            imageStore {
                name = "imagestore"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.qcow2"
                    platform = "Linux"
                }
                image {
                    name = "image2"
                    url = "http://zstack.org/download/test.iso"
                    mediaType = ImageConstant.ImageMediaType.ISO.toString()
                    platform = ImagePlatform.Linux.toString()
                }
                image {
                    name = "image3"
                    url = "http://zstack.org/download/test.iso"
                    mediaType = ImageConstant.ImageMediaType.ISO.toString()
                    platform = ImagePlatform.Linux.toString()
                }
                image {
                    name = "image4"
                    url = "http://zstack.org/download/test.qcow2"
                    platform = "Linux"
                    size = SizeUnit.GIGABYTE.toByte(3)
                    actualSize = SizeUnit.GIGABYTE.toByte(5)
                }
            }

            zone {
                name = "zone"
                description = "test"
                attachBackupStorage("imagestore")

                cluster {
                    name = "cluster1"
                    hypervisorType = "KVM"
                     kvm {
                         name = "kvm1"
                         managementIp = "127.0.0.1"
                         username = "root"
                         password = "password"
                     }
                    attachL2Network("l2")
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        ip {
                            startIp = "**************"
                            endIp = "***************"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "************"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }
                blockPrimaryStorage {
                    name = "BlockPS1"
                    vendorName = "XStor"
                    metadata = "{'ip':'127.0.0.1','port':8443,'username':'optadmin','password':'password'," +
                            "'accessZones':[{'name':'az','id':1,'subnets':[{'access_zone_id':1,'id':2,'name':'svip','key':2,'svip':'************'}]}]," +
                            "'storagePools':[{'authority_type':'CACHE_MODE_RW','enable_data_cache':'ENABLE_DATA_CACHE_YES','id':3,'key':3," +
                            "'layout':{'disk_parity_num':0,'minNodeNum':0,'node_parity_num':2,'replica_num':3,'stripe_width':1},'name':'pool_2'}]}"
                    url = "block://blockps1"
                }
                blockPrimaryStorage {
                    name = "BlockPS2"
                    vendorName = "XStor"
                    metadata = "{'ip':'*********','port':8443,'username':'optadmin','password':'password'," +
                            "'accessZones':[{'name':'az','id':2,'subnets':[{'access_zone_id':2,'id':3,'name':'svip','key':2,'svip':'************'}]}]," +
                            "'storagePools':[{'authority_type':'CACHE_MODE_RW','enable_data_cache':'ENABLE_DATA_CACHE_YES','id':4,'key':4," +
                            "'layout':{'disk_parity_num':0,'minNodeNum':0,'node_parity_num':2,'replica_num':3,'stripe_width':1},'name':'pool_3'}]}"
                    url = "block://blockps2"
                }
            }
        }
    }

    @Override
    void test() {
        env.create {
            zoneInv = env.inventoryByName("zone")
            clusterInventory = env.inventoryByName("cluster1")
            hostInventory = env.inventoryByName("kvm1")
            l3 = env.inventoryByName("l3")
            image = env.inventoryByName("image1")
            isoImage = env.inventoryByName("image2")
            isoImage2 = env.inventoryByName("image3")
            image4 = env.inventoryByName("image4")
            ps1 = env.inventoryByName("BlockPS1")
            ps2 = env.inventoryByName("BlockPS2")
            diskOffering = env.inventoryByName("diskOffering")
            helper = new XStorStorageDeviceHelper(env)

            attachPrimaryStorageToCluster {
                primaryStorageUuid = ps1.uuid
                clusterUuid = clusterInventory.uuid
            }
            attachPrimaryStorageToCluster {
                primaryStorageUuid = ps2.uuid
                clusterUuid = clusterInventory.uuid
            }

            testCreateVmFromImage()
            testCreateVmFromImageWhenTokenExpires()
            testCreateVmWithImageThatActualSizeLargerThanSize()
            testCreateVmFromIso()
        }
    }

    void testCreateVmFromImageWhenTokenExpires() {
        def called =  false

        env.simulator(getSimulatorPath(XStorDevice.REST_API_GET_LUN_MAP_BY_LUN_ID_PATH)) { HttpServletRequest req, HttpEntity<String> entity, EnvSpec spec ->
            if (called) {
                XStorDevice.QueryLunMapRsp rsp = new XStorDevice.QueryLunMapRsp()
                def id = req.getParameter("ids")
                List<LunMap> lunMapList = helper.lunMaps.values().stream().filter({ it -> it.getSource_id().equals(id) }).collect(Collectors.toList())

                XStorDevice.QueryLunMapResult result = new XStorDevice.QueryLunMapResult()
                result.lun_maps = lunMapList
                rsp.result = result
                rsp.detail_err_msg = ""
                rsp.err_no = 0
                rsp.err_msg = ""
                return  rsp
            } else {
                called = true
                throw new OperationFailureException(operr("NON_AUTHORITATIVE_INFORMATION"))
            }
        }

        createVmInstance {
            name = "vm"
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
            clusterUuid = hostInventory.clusterUuid
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
        } as VmInstanceInventory

        assert helper.loginMap.size() == 1
    }

    private Integer getHostGroupIdFromLunMapKey(String key) {
        XStorDevice.QueryLunRsp rsp = new XStorDevice.QueryLunRsp()
        return  Integer.valueOf(key.split("::").toList().get(1))
    }

    void testCreateVmFromImage() {
        def vm = createVmInstance {
            name = "vm"
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
            clusterUuid = hostInventory.clusterUuid
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
        } as VmInstanceInventory

        def volume = createDataVolume {
            name = "volume"
            diskOffering = diskOffering
            primaryStorageUuid = ps1.uuid
        }

        attachDataVolumeToVm {
            volumeUuid = volume.uuid
            vmInstanceUuid = vm.uuid
        }
    }

    void testCreateVmFromIso() {
        KVMAgentCommands.StartVmCmd startVmCmd
        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { rsp, HttpEntity<String> e ->
            startVmCmd = json(e.body, KVMAgentCommands.StartVmCmd.class)
            assert 3 == startVmCmd.getCdRoms().size()
            KVMAgentCommands.CdRomTO cdRomTO = startVmCmd.getCdRoms().get(0)
            assert isoImage.uuid == cdRomTO.imageUuid
            assert 0 == cdRomTO.deviceId
            assert null != cdRomTO.path

            return rsp
        }

        def vm = createVmInstance {
            name = "vm"
            l3NetworkUuids = [l3.uuid]
            imageUuid = isoImage.uuid
            clusterUuid = hostInventory.clusterUuid
            cpuNum = 1
            rootDiskOfferingUuid = diskOffering.uuid
            memorySize = SizeUnit.GIGABYTE.toByte(1)
            systemTags = [
                    "${VmSystemTags.CD_ROM_LIST_TOKEN}::${isoImage.uuid}::${VmInstanceConstant.EMPTY_CDROM}::${VmInstanceConstant.EMPTY_CDROM}".toString()
            ]
        } as VmInstanceInventory

        assert null != startVmCmd

        KVMAgentCommands.AttachIsoCmd cmd
        env.afterSimulator(KVMConstant.KVM_ATTACH_ISO_PATH) { rsp, HttpEntity<String> e ->
            cmd = json(e.body, KVMAgentCommands.AttachIsoCmd.class)
            assert isoImage2.uuid == cmd.iso.imageUuid
            assert null != cmd.iso.path

            return rsp
        }

        attachIsoToVmInstance {
            isoUuid = isoImage2.uuid
            vmInstanceUuid = vm.uuid
        }

        assert Q.New(ImageCacheVO.class)
                .eq(ImageCacheVO_.imageUuid, isoImage2.uuid)
                .select(ImageCacheVO_.installUrl).findValue() == cmd.iso.path
    }

    void testCreateVmWithImageThatActualSizeLargerThanSize() {
        def vm = createVmInstance {
            name = "vm"
            l3NetworkUuids = [l3.uuid]
            imageUuid = image4.uuid
            clusterUuid = hostInventory.clusterUuid
            cpuNum = 1
            memorySize = SizeUnit.GIGABYTE.toByte(1)
        } as VmInstanceInventory

        def volume = queryVolume {
            conditions = ["uuid=${vm.rootVolumeUuid}"]
        }[0] as VolumeInventory

        assert volume.size == image4.actualSize
    }

    private String getSimulatorPath(String path) {
        return XStorDevice.XSTOR_STORAGE_DEVICE_SIMULATOR_PATH_PREFIX + path
    }

    @Override
    void clean() {
        detachPrimaryStorageFromCluster {
            primaryStorageUuid = ps2.uuid
            clusterUuid = clusterInventory.uuid
        }

        detachPrimaryStorageFromCluster {
            primaryStorageUuid = ps1.uuid
            clusterUuid = clusterInventory.uuid
        }

        SQL.New("delete from BlockScsiLunVO where name like '%image-cache%' ").execute()
        env.delete()
    }
}
