package org.zstack.test.integration.premium.storage.migration

import org.springframework.http.HttpEntity
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.longjob.LongJobVO
import org.zstack.header.storage.snapshot.VolumeSnapshotVO
import org.zstack.header.storage.snapshot.VolumeSnapshotVO_
import org.zstack.header.volume.VolumeType
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.sdk.CephPrimaryStoragePoolInventory
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VolumeInventory
import org.zstack.storage.ceph.primary.CephPrimaryStorageBase
import org.zstack.storage.ceph.primary.CephPrimaryStoragePoolVO
import org.zstack.storage.ceph.primary.CephPrimaryStoragePoolVO_
import org.zstack.test.integration.premium.PremiumEnv
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.CephPrimaryStorageSpec
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

/**
 * Created by GuoYi on 2019-8-14.
 */
class CephToCephMigrateAfterReimageCase extends PremiumSubCase {
    EnvSpec env
    PrimaryStorageInventory srcPs
    PrimaryStorageInventory dstPs
    VmInstanceInventory vm
    ImageInventory image1
    VolumeInventory root
    VolumeInventory data
    List<String> srcSnapshotPaths
    InstanceOfferingInventory instanceOffering
    L3NetworkInventory l3
    DiskOfferingInventory diskOffering

    String ROOT_POOL_TYPE = "Root"
    String DATA_POOL_TYPE = "Data"
    String srcRootPoolName
    String srcDataPoolName
    String NEW_ROOT_POOL = "new_root_pool"
    String NEW_DATA_POOL = "new_data_pool"
    CephPrimaryStoragePoolInventory newRootPoolInv
    CephPrimaryStoragePoolInventory newDataPoolInv

    @Override
    void environment() {
        env = PremiumEnv.twoClusterTwoCephPsTwoCephBsEnv()
    }

    @Override
    void test() {
        env.create {
            prepare()
            reimageVmInstanceAndCreateSnapshots()
            testMigrateVm()
            testMigrateVmBack()
            testMigrateVmToDiffCephAndReimageVm()
            testVmSpecifyPoolAndReimageVm()
            testVmNotSpecifyPoolAndReimageVm()
        }
    }

    @Override
    void clean() {
        SQL.New(LongJobVO.class).delete()
        env.delete()
    }

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    void prepare() {
        vm = env.inventoryByName("test-vm") as VmInstanceInventory
        srcPs = env.inventoryByName("ceph-pri-1") as PrimaryStorageInventory
        dstPs = env.inventoryByName("ceph-pri-2") as PrimaryStorageInventory
        image1 = env.inventoryByName("image1") as ImageInventory

        instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        l3 = env.inventoryByName("l3") as L3NetworkInventory
        diskOffering = env.inventoryByName("diskOffering") as DiskOfferingInventory

        root = queryVolume {
            conditions = ["uuid=${vm.rootVolumeUuid}"]
        }[0] as VolumeInventory

        data = queryVolume {
            conditions = ["vmInstanceUuid=${vm.uuid}","type=Data"]
        }[0] as VolumeInventory

        stopVmInstance {
            uuid = vm.uuid
        }

        srcRootPoolName = Q.New(CephPrimaryStoragePoolVO.class)
                .eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, srcPs.uuid)
                .eq(CephPrimaryStoragePoolVO_.type, ROOT_POOL_TYPE)
                .select(CephPrimaryStoragePoolVO_.poolName)
                .limit(1).findValue();

        srcDataPoolName = Q.New(CephPrimaryStoragePoolVO.class)
                .eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, srcPs.uuid)
                .eq(CephPrimaryStoragePoolVO_.type, DATA_POOL_TYPE)
                .select(CephPrimaryStoragePoolVO_.poolName)
                .limit(1).findValue();
    }

    void reimageVmInstanceAndCreateSnapshots() {
        reimageVmInstance {
            vmInstanceUuid = vm.uuid
        }

        createVolumeSnapshot {
            name = "sn-1"
            volumeUuid = root.uuid
        }

        reimageVmInstance {
            vmInstanceUuid = vm.uuid
        }

        createVolumeSnapshot {
            name = "sn-2"
            volumeUuid = root.uuid
        }

        detachDataVolumeFromVm {
            uuid = data.uuid
        }

        Map<String, List<String>> pathMap = new HashMap<>();
        srcSnapshotPaths = Q.New(VolumeSnapshotVO.class)
                .eq(VolumeSnapshotVO_.volumeUuid, root.uuid)
                .select(VolumeSnapshotVO_.primaryStorageInstallPath)
                .listValues()
        for (String snapshotPath : srcSnapshotPaths) {
            def volumePath = snapshotPath.replace("ceph://", "").split("@")[0]
            def snapshotName = snapshotPath.replace("ceph://", "").split("@")[1]

            if (pathMap.containsKey(volumePath)) {
                pathMap[volumePath].add(snapshotName)
            } else {
                pathMap[volumePath] = new ArrayList<String>()
            }
        }

        env.simulator(CephPrimaryStorageBase.GET_VOLUME_SNAPINFOS_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def cmd = JSONObjectUtil.toObject(e.body, CephPrimaryStorageBase.GetVolumeSnapInfosCmd.class)
            def volumePath = cmd.volumePath.replace("ceph://", "")

            def i = 1
            def infos = []
            for (String snapName : pathMap[volumePath]) {
                def info = new CephPrimaryStorageBase.SnapInfo()
                info.setId(i)
                info.setName(snapName)
                info.setSize(1000)
                infos.add(info)
                i++
            }

            def rsp = new CephPrimaryStorageBase.GetVolumeSnapInfosRsp()
            rsp.setSnapInfos(infos)
            return rsp
        }
    }

    void testMigrateVm() {
        root = queryVolume {
            conditions = ["uuid=${vm.rootVolumeUuid}"]
        }[0] as VolumeInventory

        def dstVol = primaryStorageMigrateVolume {
            volumeUuid = vm.rootVolumeUuid
            dstPrimaryStorageUuid = dstPs.uuid
        } as VolumeInventory

        String srcPool = Q.New(CephPrimaryStoragePoolVO.class).select(CephPrimaryStoragePoolVO_.poolName).eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, srcPs.uuid).eq(CephPrimaryStoragePoolVO_.type, "Root").findValue()
        String dstPool = Q.New(CephPrimaryStoragePoolVO.class).select(CephPrimaryStoragePoolVO_.poolName).eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, dstPs.uuid).eq(CephPrimaryStoragePoolVO_.type, "Root").findValue()
        assert root.installPath.contains(srcPool)
        assert dstVol.installPath.contains(dstPool)
        assert root.installPath.replaceAll(srcPool, dstPool) == dstVol.installPath

        // check dst snapshot path
        def dstSnapshotPaths = Q.New(VolumeSnapshotVO.class)
                .eq(VolumeSnapshotVO_.volumeUuid, root.uuid)
                .select(VolumeSnapshotVO_.primaryStorageInstallPath)
                .listValues() as List<String>
        for (String srcPath : srcSnapshotPaths) {
            assert srcPath.contains(srcPool)
            assert dstSnapshotPaths.contains(srcPath.replace(srcPool, dstPool))
        }
    }

    void testMigrateVmBack() {
        // must failed
        expect(AssertionError.class) {
            primaryStorageMigrateVolume {
                volumeUuid = vm.rootVolumeUuid
                dstPrimaryStorageUuid = srcPs.uuid
            }
        }

        cleanUpTrashOnPrimaryStorage {
            uuid = srcPs.uuid
        }

        primaryStorageMigrateVolume {
            volumeUuid = vm.rootVolumeUuid
            dstPrimaryStorageUuid = srcPs.uuid
        }
    }

    void testMigrateVmToDiffCephAndReimageVm() {
        String srcPoolName = Q.New(CephPrimaryStoragePoolVO.class)
                .eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, srcPs.uuid)
                .eq(CephPrimaryStoragePoolVO_.type, ROOT_POOL_TYPE)
                .select(CephPrimaryStoragePoolVO_.poolName)
                .limit(1).findValue();

        String dstPoolName = Q.New(CephPrimaryStoragePoolVO.class)
                .eq(CephPrimaryStoragePoolVO_.primaryStorageUuid, dstPs.uuid)
                .eq(CephPrimaryStoragePoolVO_.type, ROOT_POOL_TYPE)
                .select(CephPrimaryStoragePoolVO_.poolName)
                .limit(1).findValue();

        // specify volume pool
        VmInstanceInventory vm1 = createVmInstance {
            name = "vm-1"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image1.uuid
            l3NetworkUuids = [l3.uuid]
            dataDiskOfferingUuids = [diskOffering.uuid]
            primaryStorageUuidForRootVolume = srcPs.uuid
            rootVolumeSystemTags = ["ceph::rootPoolName::" + srcPoolName]
            systemTags = ["primaryStorageUuidForDataVolume::" + srcPs.uuid]
            dataVolumeSystemTags = ["ceph::pool::" + srcPoolName]
        } as VmInstanceInventory

        primaryStorageMigrateVm {
            vmInstanceUuid = vm1.uuid
            dstPrimaryStorageUuid = dstPs.uuid
            withDataVolumes = true
            withSnapshots = false
            systemTags = ["ceph::rootPoolName::" + dstPoolName, "ceph::pool::" + dstPoolName]
        }

        VolumeVO rootVolume = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, vm1.getRootVolumeUuid()).find()
        VolumeVO dataVolume = Q.New(VolumeVO.class).eq(VolumeVO_.vmInstanceUuid, vm1.getUuid())
                .eq(VolumeVO_.type, VolumeType.Data).find()

        assert rootVolume.installPath.contains(dstPoolName)
        assert dataVolume.installPath.contains(dstPoolName)

        stopVmInstance {
            uuid = vm1.uuid
        }
        expectError {
            reimageVmInstance {
                vmInstanceUuid = vm1.uuid
            }
        }
    }

    void testVmSpecifyPoolAndReimageVm() {
        CephPrimaryStorageSpec.manuallyAddCephPool(srcPs.uuid, NEW_ROOT_POOL, env)
        CephPrimaryStorageSpec.manuallyAddCephPool(srcPs.uuid, NEW_DATA_POOL, env)

        newRootPoolInv = addCephPrimaryStoragePool {
            poolName = NEW_ROOT_POOL
            primaryStorageUuid = srcPs.uuid
            type = "Root"
        } as CephPrimaryStoragePoolInventory
        newDataPoolInv = addCephPrimaryStoragePool {
            poolName = NEW_DATA_POOL
            primaryStorageUuid = srcPs.uuid
            type = "Data"
        } as CephPrimaryStoragePoolInventory

        // specify volume pool
        VmInstanceInventory vm2 = createVmInstance {
            name = "vm-2"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image1.uuid
            l3NetworkUuids = [l3.uuid]
            dataDiskOfferingUuids = [diskOffering.uuid]
            primaryStorageUuidForRootVolume = srcPs.uuid
            rootVolumeSystemTags = ["ceph::rootPoolName::" + srcRootPoolName]
            systemTags = ["primaryStorageUuidForDataVolume::" + srcPs.uuid]
            dataVolumeSystemTags = ["ceph::pool::" + srcDataPoolName]
        } as VmInstanceInventory

        primaryStorageMigrateVm {
            vmInstanceUuid = vm2.uuid
            dstPrimaryStorageUuid = srcPs.uuid
            withDataVolumes = true
            withSnapshots = false
            systemTags = ["ceph::rootPoolName::" + newRootPoolInv.poolName, "ceph::pool::" + newDataPoolInv.poolName]
        }

        VolumeVO rootVolume = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, vm2.getRootVolumeUuid()).find()
        VolumeVO dataVolume = Q.New(VolumeVO.class).eq(VolumeVO_.vmInstanceUuid, vm2.getUuid())
                .eq(VolumeVO_.type, VolumeType.Data).find()

        assert rootVolume.installPath.contains(newRootPoolInv.poolName)
        assert dataVolume.installPath.contains(newDataPoolInv.poolName)

        stopVmInstance {
            uuid = vm2.uuid
        }
        reimageVmInstance {
            vmInstanceUuid = vm2.uuid
        }

        rootVolume = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, vm2.getRootVolumeUuid()).find()
        assert rootVolume.installPath.contains(newRootPoolInv.poolName)
    }

    void testVmNotSpecifyPoolAndReimageVm() {
        // not specify volume pool
        VmInstanceInventory vm3 = createVmInstance {
            name = "vm-3"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image1.uuid
            l3NetworkUuids = [l3.uuid]
            dataDiskOfferingUuids = [diskOffering.uuid]
            primaryStorageUuidForRootVolume = srcPs.uuid
            systemTags = ["primaryStorageUuidForDataVolume::" + srcPs.uuid]
        } as VmInstanceInventory

        primaryStorageMigrateVm {
            vmInstanceUuid = vm3.uuid
            dstPrimaryStorageUuid = srcPs.uuid
            withDataVolumes = true
            withSnapshots = false
            systemTags = ["ceph::rootPoolName::" + newRootPoolInv.poolName, "ceph::pool::" + newDataPoolInv.poolName]
        }

        VolumeVO rootVolume = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, vm3.getRootVolumeUuid()).find()
        VolumeVO dataVolume = Q.New(VolumeVO.class).eq(VolumeVO_.vmInstanceUuid, vm3.getUuid())
                .eq(VolumeVO_.type, VolumeType.Data).find()

        assert rootVolume.installPath.contains(newRootPoolInv.poolName)
        assert dataVolume.installPath.contains(newDataPoolInv.poolName)

        stopVmInstance {
            uuid = vm3.uuid
        }
        reimageVmInstance {
            vmInstanceUuid = vm3.uuid
        }

        rootVolume = Q.New(VolumeVO.class).eq(VolumeVO_.uuid, vm3.getRootVolumeUuid()).find()
        assert rootVolume.installPath.contains(newRootPoolInv.poolName)
    }
}
