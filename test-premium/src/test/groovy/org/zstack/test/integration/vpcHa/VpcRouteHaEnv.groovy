package org.zstack.test.integration.vpcHa

import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vipQos.VipQosConstants
import org.zstack.header.vpc.VpcConstants
import org.zstack.ipsec.IPsecConstants
import org.zstack.network.securitygroup.SecurityGroupConstant
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.network.service.lb.LoadBalancerConstants
import org.zstack.network.service.portforwarding.PortForwardingConstant
import org.zstack.network.service.userdata.UserdataConstant
import org.zstack.network.service.virtualrouter.vyos.VyosConstants
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.TestPremium
import org.zstack.utils.data.SizeUnit
import org.zstack.network.service.virtualrouter.vyos.VyosKeepalivedCommands
import org.zstack.vrouterRoute.VRouterRouteConstants

/**
 * Created by shixin on 04/20/19.
 */
class VpcRouteHaEnv {
    static EnvSpec VpcRouteHaBasicEnv() {
        return TestPremium.makeEnv {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(1)
                cpu = 1
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image1"
                    url  = "http://zstack.org/download/test.qcow2"
                }

                image {
                    name = "vr"
                    url  = "http://zstack.org/download/vr.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    kvm {
                        name = "kvm3"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("local")
                    attachL2Network("l2")
                    attachL2Network("l2-1")
                }

                localPrimaryStorage {
                    name = "local"
                    url = "/local_ps"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE,
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VipQosConstants.VIPQOS_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                            systemTags = ["flatNetwork::DhcpServer::*************::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "l3-1"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE,
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VipQosConstants.VIPQOS_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                            systemTags = ["flatNetwork::DhcpServer::*************::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "l3-2"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE,
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VipQosConstants.VIPQOS_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                            systemTags = ["flatNetwork::DhcpServer::*************::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "l3-3"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE,
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VipQosConstants.VIPQOS_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                            systemTags = ["flatNetwork::DhcpServer::*************::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "pubL3"
                        category = "Public"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        ip {
                            startIp = "***********"
                            endIp = "***********0"
                            netmask = "*************"
                            gateway = "**********"
                            systemTags = ["flatNetwork::DhcpServer::***********::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "pubL3-1"
                        category = "Public"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        ip {
                            startIp = "***********"
                            endIp = "***********0"
                            netmask = "*************"
                            gateway = "**********"
                            systemTags = ["flatNetwork::DhcpServer::***********::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "pubL3-2"
                        category = "Public"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                            systemTags = ["flatNetwork::DhcpServer::***********::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "pubL3-3"
                        category = "Public"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(),
                                     NetworkServiceType.HostRoute.toString(),
                                     UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        ip {
                            startIp = "***********"
                            endIp = "***********0"
                            netmask = "*************"
                            gateway = "**********"
                            systemTags = ["flatNetwork::DhcpServer::***********::ipUuid::null"]
                        }
                    }

                    l3Network {
                        name = "l3-Statefull-DHCP-1"
                        ipVersion = 6
                        category = "Public"

                        ipv6 {
                            name = "ipv6-Statefull-DHCP-1"
                            networkCidr = "2001:2004::/64"
                            addressMode = "Stateful-DHCP"
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE
                            types = [NetworkServiceType.DHCP.toString(), NetworkServiceType.DNS.toString(), EipConstant.EIP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }
                    }
                }

                l2NoVlanNetwork {
                    name = "l2-1"
                    physicalInterface = "eth1"
                    l3Network {
                        name = "l2-l3"
                        type = VpcConstants.VPC_L3_NETWORK_TYPE

                        service {
                            provider = SecurityGroupConstant.SECURITY_GROUP_PROVIDER_TYPE
                            types = [SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE]
                        }

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString(), UserdataConstant.USERDATA_TYPE_STRING]
                        }

                        service {
                            provider = VyosConstants.PROVIDER_TYPE
                            types = [EipConstant.EIP_NETWORK_SERVICE_TYPE,
                                     NetworkServiceType.DNS.toString(),
                                     NetworkServiceType.SNAT.toString(),
                                     PortForwardingConstant.PORTFORWARDING_NETWORK_SERVICE_TYPE,
                                     LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE.toString(),
                                     IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString(),
                                     VipQosConstants.VIPQOS_NETWORK_SERVICE_TYPE.toString(),
                                     VRouterRouteConstants.VROUTER_ROUTE_NETWORK_SERVICE_TYPE.toString()]
                        }

                        ip {
                            startIp = "*************"
                            endIp = "*************0"
                            netmask = "*************"
                            gateway = "************"
                            systemTags = ["flatNetwork::DhcpServer::*************::ipUuid::null"]
                        }
                    }
                }

                virtualRouterOffering {
                    name = "vro"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3")
                    useImage("vr")
                }

                virtualRouterOffering {
                    name = "vro-1"
                    memory = SizeUnit.MEGABYTE.toByte(512)
                    cpu = 2
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3-1")
                    useImage("vr")
                }

                virtualRouterOffering {
                    name = "vro-fail"
                    memory = SizeUnit.GIGABYTE.toByte(512)
                    cpu = 512
                    useManagementL3Network("pubL3")
                    usePublicL3Network("pubL3-1")
                    useImage("vr")
                }

                attachBackupStorage("sftp")
            }

            simulator(VyosKeepalivedCommands.VYOS_HA_ENABLE_PATH) {
                return new VyosKeepalivedCommands.VyosHaEnableRsp()
            }

            simulator(VyosKeepalivedCommands.SYNC_VPC_ROUTER_HA_PATH) {
                return new VyosKeepalivedCommands.SyncVpcRouterHaRsp()
            }

            simulator(VyosKeepalivedCommands.RESTART_KEEPALIVED_PATH) {
                return new VyosKeepalivedCommands.RestartKeepalivedRsp()
            }
        }
    }
}
