package org.zstack.test.integration.vpcHa

import org.springframework.http.HttpEntity
import org.zstack.core.db.DatabaseFacade
import org.zstack.network.service.virtualrouter.VirtualRouterCommands
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.network.service.virtualrouter.VirtualRouterNicMetaData
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil
/**
 * 测试VPC公网网卡detach/attach后metadata的正确性
 * 复现问题：卸载vpc的公网网卡再重新加载的nic的metadata不包含PUBLIC_NIC_MASK标志，变成了ADDITIONAL_PUBLIC_NIC_MASK
 *
 * 测试两种VPC路由器规格：
 * 1. vro: 公管合一网络配置 (PUBLIC_AND_MANAGEMENT_NIC_MASK)
 * 2. vro-1: 公管分离网络配置 (独立的PUBLIC_NIC_MASK)
 */
class VpcRouterPublicNicMetadataCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(VpcRouterHaTest.springSpec)
    }

    @Override
    void environment() {
        env = VpcRouteHaEnv.VpcRouteHaBasicEnv()
    }

    @Override
    void test() {
        env.create {
            dbf = bean(DatabaseFacade.class)
            testVpcRouterPublicNicMetadataWithPublicManagementSeparated()
            testVpcRouterPublicNicMetadataWithPublicManagementCombined()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    /**
     * 测试公管分离网络配置的VPC路由器
     * vro-1: 管理网络(pubL3) + 公网网络(pubL3-1)
     */
    void testVpcRouterPublicNicMetadataWithPublicManagementSeparated() {
        def offer = env.inventoryByName("vro-1") as VirtualRouterOfferingInventory
        def pubL3_2 = env.inventoryByName("pubL3-2") as L3NetworkInventory

        // 模拟网卡配置命令
        List<VirtualRouterCommands.ConfigureNicCmd> nicCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CONFIGURE_NIC_PATH) { rsp, HttpEntity<String> e ->
            VirtualRouterCommands.ConfigureNicCmd nicCmd = JSONObjectUtil.toObject(e.body, VirtualRouterCommands.ConfigureNicCmd.class)
            nicCmds.add(nicCmd)
            VirtualRouterCommands.ConfigureNicRsp nicRsp = new VirtualRouterCommands.ConfigureNicRsp()
            nicRsp.success = true
            return nicRsp
        }

        // 创建VPC路由器（公管分离配置）
        VirtualRouterVmInventory vpc = createVpcVRouter {
            name = "vpc-test-separated"
            virtualRouterOfferingUuid = offer.uuid
        }

        // 验证初始状态：应该有管理网卡和独立的公网网卡
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]

        // 找到公网网卡（pubL3-1）
        VmNicInventory originalPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3-1").uuid)) {
                originalPublicNic = nic
                break
            }
        }
        assert originalPublicNic != null
        assert originalPublicNic.metaData == VirtualRouterNicMetaData.PUBLIC_NIC_MASK.toString()

        // 第一步：attach额外公网网络
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = pubL3_2.uuid
        }

        // 验证额外公网网卡的metadata
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory additionalPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                additionalPublicNic = nic
                break
            }
        }
        assert additionalPublicNic != null
        assert additionalPublicNic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()

        // 第二步：detach原始公网网卡
        detachL3NetworkFromVm {
            vmNicUuid = originalPublicNic.uuid
        }

        // 第三步：重新attach原始公网网卡（关键测试点）
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = env.inventoryByName("pubL3-1").uuid
        }

        // 验证重新attach后的metadata
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory reattachedPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3-1").uuid)) {
                reattachedPublicNic = nic
                break
            }
        }
        assert reattachedPublicNic != null

        // 关键验证：重新attach的主公网网卡应该仍然是PUBLIC_NIC_MASK
        assert reattachedPublicNic.metaData == VirtualRouterNicMetaData.PUBLIC_NIC_MASK.toString() :
            "重新attach的主公网网卡metadata应该是PUBLIC_NIC_MASK，但实际是: ${reattachedPublicNic.metaData}"

        // 清理
        destroyVmInstance {
            uuid = vpc.uuid
        }
    }

    /**
     * 测试公管合一网络配置的VPC路由器
     * vro: 管理网络和公网网络都是pubL3
     */
    void testVpcRouterPublicNicMetadataWithPublicManagementCombined() {
        def offer = env.inventoryByName("vro") as VirtualRouterOfferingInventory
        def pubL3_2 = env.inventoryByName("pubL3-2") as L3NetworkInventory

        // 创建VPC路由器（公管合一配置）
        VirtualRouterVmInventory vpc = createVpcVRouter {
            name = "vpc-test-combined"
            virtualRouterOfferingUuid = offer.uuid
        }

        // 验证初始状态：应该有公管合一网卡
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]

        // 找到公管合一网卡
        VmNicInventory originalPublicManagementNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3").uuid)) {
                originalPublicManagementNic = nic
                break
            }
        }
        assert originalPublicManagementNic != null
        assert originalPublicManagementNic.metaData == VirtualRouterNicMetaData.PUBLIC_AND_MANAGEMENT_NIC_MASK.toString()

        // 第一步：attach额外公网网络
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = pubL3_2.uuid
        }

        // 验证额外公网网卡的metadata
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory additionalPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                additionalPublicNic = nic
                break
            }
        }
        assert additionalPublicNic != null
        assert additionalPublicNic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()

        // 第二步：detach公管合一网卡
        detachL3NetworkFromVm {
            vmNicUuid = originalPublicManagementNic.uuid
        }

        // 第三步：重新attach公管合一网卡（关键测试点）
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = env.inventoryByName("pubL3").uuid
        }

        // 验证重新attach后的metadata
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory reattachedPublicManagementNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3").uuid)) {
                reattachedPublicManagementNic = nic
                break
            }
        }
        assert reattachedPublicManagementNic != null

        // 关键验证：重新attach的公管合一网卡应该仍然是PUBLIC_AND_MANAGEMENT_NIC_MASK
        assert reattachedPublicManagementNic.metaData == VirtualRouterNicMetaData.PUBLIC_AND_MANAGEMENT_NIC_MASK.toString() :
            "重新attach的公管合一网卡metadata应该是PUBLIC_AND_MANAGEMENT_NIC_MASK，但实际是: ${reattachedPublicManagementNic.metaData}"

        // 验证额外公网网卡的metadata没有变化
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                assert nic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()
                break
            }
        }

        // 清理
        destroyVmInstance {
            uuid = vpc.uuid
        }
    }
}
