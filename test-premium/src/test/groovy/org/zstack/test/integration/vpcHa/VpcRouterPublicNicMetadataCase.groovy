package org.zstack.test.integration.vpcHa

import org.springframework.http.HttpEntity
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.header.vm.VmNicVO
import org.zstack.header.vm.VmNicVO_
import org.zstack.network.service.virtualrouter.VirtualRouterCommands
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.network.service.virtualrouter.VirtualRouterNicMetaData
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

class VpcRouterPublicNicMetadataCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(VpcRouterHaTest.springSpec)
    }

    @Override
    void environment() {
        env = VpcRouteHaEnv.VpcRouteHaBasicEnv()
    }

    @Override
    void test() {
        env.create {
            dbf = bean(DatabaseFacade.class)
            testVpcRouterPublicNicMetadataWithPublicManagementSeparated()
            testVpcRouterPublicNicMetadataWithPublicManagementCombined()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    void testVpcRouterPublicNicMetadataWithPublicManagementSeparated() {
        def offer = env.inventoryByName("vro-1") as VirtualRouterOfferingInventory
        def pubL3_2 = env.inventoryByName("pubL3-2") as L3NetworkInventory

        List<VirtualRouterCommands.ConfigureNicCmd> nicCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CONFIGURE_NIC_PATH) { rsp, HttpEntity<String> e ->
            VirtualRouterCommands.ConfigureNicCmd nicCmd = JSONObjectUtil.toObject(e.body, VirtualRouterCommands.ConfigureNicCmd.class)
            nicCmds.add(nicCmd)
            VirtualRouterCommands.ConfigureNicRsp nicRsp = new VirtualRouterCommands.ConfigureNicRsp()
            nicRsp.success = true
            return nicRsp
        }

        VirtualRouterVmInventory vpc = createVpcVRouter {
            name = "vpc-test-separated"
            virtualRouterOfferingUuid = offer.uuid
        }

        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]

        VmNicInventory originalPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3-1").uuid)) {
                originalPublicNic = nic
                break
            }
        }
        assert originalPublicNic != null
        assert originalPublicNic.metaData == VirtualRouterNicMetaData.PUBLIC_NIC_MASK.toString()

        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = pubL3_2.uuid
        }

        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory additionalPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                additionalPublicNic = nic
                break
            }
        }
        assert additionalPublicNic != null
        assert additionalPublicNic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()

        VmNicVO nicVO = Q.New(VmNicVO.class).eq(VmNicVO_.uuid, originalPublicNic.uuid).find()
        dbf.remove(nicVO)

        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = env.inventoryByName("pubL3-1").uuid
        }

        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory reattachedPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3-1").uuid)) {
                reattachedPublicNic = nic
                break
            }
        }
        assert reattachedPublicNic != null

        assert reattachedPublicNic.metaData == VirtualRouterNicMetaData.PUBLIC_NIC_MASK.toString() :
            "Reattached primary public nic metadata should be PUBLIC_NIC_MASK, but actual is: ${reattachedPublicNic.metaData}"

        destroyVmInstance {
            uuid = vpc.uuid
        }
    }

    void testVpcRouterPublicNicMetadataWithPublicManagementCombined() {
        def offer = env.inventoryByName("vro") as VirtualRouterOfferingInventory
        def pubL3_2 = env.inventoryByName("pubL3-2") as L3NetworkInventory

        VirtualRouterVmInventory vpc = createVpcVRouter {
            name = "vpc-test-combined"
            virtualRouterOfferingUuid = offer.uuid
        }

        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]

        VmNicInventory originalPublicManagementNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3").uuid)) {
                originalPublicManagementNic = nic
                break
            }
        }
        assert originalPublicManagementNic != null
        assert originalPublicManagementNic.metaData == VirtualRouterNicMetaData.PUBLIC_AND_MANAGEMENT_NIC_MASK.toString()

        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = pubL3_2.uuid
        }

        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory additionalPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                additionalPublicNic = nic
                break
            }
        }
        assert additionalPublicNic != null
        assert additionalPublicNic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()

        VmNicVO nicVO = Q.New(VmNicVO.class).eq(VmNicVO_.uuid, originalPublicManagementNic.uuid).find()
        dbf.remove(nicVO)

        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = env.inventoryByName("pubL3").uuid
        }

        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory reattachedPublicManagementNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(env.inventoryByName("pubL3").uuid)) {
                reattachedPublicManagementNic = nic
                break
            }
        }
        assert reattachedPublicManagementNic != null

        assert reattachedPublicManagementNic.metaData == VirtualRouterNicMetaData.PUBLIC_AND_MANAGEMENT_NIC_MASK.toString() :
            "Reattached public-management nic metadata should be PUBLIC_AND_MANAGEMENT_NIC_MASK, but actual is: ${reattachedPublicManagementNic.metaData}"

        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                assert nic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()
                break
            }
        }

        destroyVmInstance {
            uuid = vpc.uuid
        }
    }
}
