package org.zstack.test.integration.vpcHa

import org.springframework.http.HttpEntity
import org.zstack.core.db.DatabaseFacade
import org.zstack.network.service.virtualrouter.VirtualRouterCommands
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.network.service.virtualrouter.VirtualRouterNicMetaData
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil
/**
 * 测试VPC公网网卡detach/attach后metadata的正确性
 * 复现问题：卸载vpc的公网网卡再重新加载的nic的metadata不包含PUBLIC_NIC_MASK标志，变成了ADDITIONAL_PUBLIC_NIC_MASK
 */
class VpcRouterPublicNicMetadataCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(VpcRouterHaTest.springSpec)
    }

    @Override
    void environment() {
        env = VpcRouteHaEnv.VpcRouteHaBasicEnv()
    }

    @Override
    void test() {
        env.create {
            dbf = bean(DatabaseFacade.class)
            testVpcRouterPublicNicMetadataAfterDetachAttach()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    void testVpcRouterPublicNicMetadataAfterDetachAttach() {
        def offer = env.inventoryByName("vro") as VirtualRouterOfferingInventory
        def pubL3 = env.inventoryByName("pubL3-1") as L3NetworkInventory
        def pubL3_2 = env.inventoryByName("pubL3-2") as L3NetworkInventory

        // 创建VPC路由器
        VirtualRouterVmInventory vpc = createVpcVRouter {
            name = "vpc-test"
            virtualRouterOfferingUuid = offer.uuid
        }

        // 验证初始状态：管理网卡应该有PUBLIC_AND_MANAGEMENT_NIC_MASK
        VmNicInventory mgtNic = vpc.getVmNics().get(0)
        assert mgtNic.metaData == VirtualRouterNicMetaData.PUBLIC_AND_MANAGEMENT_NIC_MASK.toString()

        // 模拟网卡配置命令
        List<VirtualRouterCommands.ConfigureNicCmd> nicCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CONFIGURE_NIC_PATH) { rsp, HttpEntity<String> e ->
            VirtualRouterCommands.ConfigureNicCmd nicCmd = JSONObjectUtil.toObject(e.body, VirtualRouterCommands.ConfigureNicCmd.class)
            nicCmds.add(nicCmd)
            VirtualRouterCommands.ConfigureNicRsp nicRsp = new VirtualRouterCommands.ConfigureNicRsp()
            nicRsp.success = true
            return nicRsp
        }

        // 第一步：attach一个公网网络作为主公网网卡
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = pubL3.uuid
        }

        // 验证attach后的网卡metadata
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory publicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3.uuid)) {
                publicNic = nic
                break
            }
        }
        assert publicNic != null
        // 第一个attach的公网网卡应该是PUBLIC_NIC_MASK
        assert publicNic.metaData == VirtualRouterNicMetaData.PUBLIC_NIC_MASK.toString()

        // 第二步：attach另一个公网网络作为额外公网网卡
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = pubL3_2.uuid
        }

        // 验证额外公网网卡的metadata
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory additionalPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                additionalPublicNic = nic
                break
            }
        }
        assert additionalPublicNic != null
        // 额外的公网网卡应该是ADDITIONAL_PUBLIC_NIC_MASK
        assert additionalPublicNic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()

        // 第三步：detach主公网网卡
        detachL3NetworkFromVm {
            vmInstanceUuid = vpc.uuid
            vmNicUuid = publicNic.uuid
        }

        // 验证detach后的状态
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        boolean publicNicExists = false
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3.uuid)) {
                publicNicExists = true
                break
            }
        }
        assert !publicNicExists  // 主公网网卡应该已经被detach

        // 第四步：重新attach主公网网卡（这里会复现问题）
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = pubL3.uuid
        }

        // 验证重新attach后的metadata（这里是关键验证点）
        vpc = queryVpcRouter { conditions = ["uuid=${vpc.uuid}"]} [0]
        VmNicInventory reattachedPublicNic = null
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3.uuid)) {
                reattachedPublicNic = nic
                break
            }
        }
        assert reattachedPublicNic != null

        // 关键验证：重新attach的主公网网卡应该仍然是PUBLIC_NIC_MASK，而不是ADDITIONAL_PUBLIC_NIC_MASK
        // 在修复前，这里会失败，因为会被错误地设置为ADDITIONAL_PUBLIC_NIC_MASK
        assert reattachedPublicNic.metaData == VirtualRouterNicMetaData.PUBLIC_NIC_MASK.toString() : 
            "重新attach的主公网网卡metadata应该是PUBLIC_NIC_MASK，但实际是: ${reattachedPublicNic.metaData}"

        // 验证额外公网网卡的metadata没有变化
        for (VmNicInventory nic : vpc.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(pubL3_2.uuid)) {
                assert nic.metaData == VirtualRouterNicMetaData.ADDITIONAL_PUBLIC_NIC_MASK.toString()
                break
            }
        }

        // 清理：删除VPC路由器
        destroyVmInstance {
            uuid = vpc.uuid
        }
    }
}
