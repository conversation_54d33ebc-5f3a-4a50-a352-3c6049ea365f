package org.zstack.test.integration.premium.storage.volume

import org.springframework.http.HttpEntity
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.db.DatabaseFacade
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.kvm.KVMSystemTags
import org.zstack.mevoco.MevocoGlobalConfig
import org.zstack.network.service.flat.FlatNetworkServiceConstant
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.GetVolumeIoThreadPinResult
import org.zstack.sdk.PrimaryStorageInventory
import org.zstack.sdk.SetVolumeIoThreadPinResult
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VolumeInventory
import org.zstack.storage.volume.MevocoVolumeSystemTags
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.utils.string.GetCpuRangeMethod

import static org.zstack.kvm.KVMConstant.KVM_HOST_FACT_PATH
import static org.zstack.kvm.KVMAgentCommands.HostFactResponse
import static org.zstack.utils.CollectionDSL.e
import static org.zstack.utils.CollectionDSL.map

class DataVolumeIoThreadPinCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf
    VmInstanceInventory vm
    DiskOfferingInventory diskOffering
    DiskOfferingInventory diskOffering2
    PrimaryStorageInventory local
    PrimaryStorageInventory local2

    List<KVMAgentCommands.AgentCommand> calls = new ArrayList<>()


    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        dbf = bean(DatabaseFacade.class)
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(8)
                cpu = 4
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(100)
            }

            diskOffering {
                name = "diskOffering2"
                diskSize = SizeUnit.GIGABYTE.toByte(200)
            }

            sftpBackupStorage {
                name = "sftp"
                url = "/sftp"
                username = "root"
                password = "password"
                hostname = "localhost"

                image {
                    name = "image"
                    url  = "http://zstack.org/download/test.qcow2"
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm"
                        managementIp = "localhost"
                        username = "root"
                        password = "password"
                        systemTags = [
                                KVMSystemTags.QEMU_IMG_VERSION.instantiateTag(map(e(KVMSystemTags.QEMU_IMG_VERSION_TOKEN, "2.4.0"))),
                                KVMSystemTags.LIBVIRT_VERSION.instantiateTag(map(e(KVMSystemTags.LIBVIRT_VERSION_TOKEN, "1.3.5")))
                        ]
                    }

                    kvm {
                        name = "kvm2"
                        managementIp = "*********"
                        username = "root"
                        password = "password"
                        systemTags = [
                                KVMSystemTags.QEMU_IMG_VERSION.instantiateTag(map(e(KVMSystemTags.QEMU_IMG_VERSION_TOKEN, "2.4.0"))),
                                KVMSystemTags.LIBVIRT_VERSION.instantiateTag(map(e(KVMSystemTags.LIBVIRT_VERSION_TOKEN, "1.3.5")))
                        ]
                    }

                    attachPrimaryStorage("local")
                    attachPrimaryStorage("local2")
                    attachL2Network("l2")
                }

                localPrimaryStorage {
                    name = "local"
                    availableCapacity = SizeUnit.TERABYTE.toByte(2)
                    totalCapacity = SizeUnit.TERABYTE.toByte(2)
                    url = "/local_ps"
                }

                localPrimaryStorage {
                    name = "local2"
                    availableCapacity = SizeUnit.TERABYTE.toByte(2)
                    totalCapacity = SizeUnit.TERABYTE.toByte(2)
                    url = "/local_ps2"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "l3"

                        service {
                            provider = FlatNetworkServiceConstant.FLAT_NETWORK_SERVICE_TYPE_STRING
                            types = [NetworkServiceType.DHCP.toString()]
                        }

                        ip {
                            startIp = "*************0"
                            endIp = "*************00"
                            netmask = "*************"
                            gateway = "*************"
                        }
                    }

                }

                attachBackupStorage("sftp")
            }

            vm {
                name = "vm"
                useInstanceOffering("instanceOffering")
                useImage("image")
                useL3Networks("l3")
                useHost("kvm")
            }
        }
    }

    @Override
    void test() {
        env.afterSimulator(KVM_HOST_FACT_PATH) { HostFactResponse rsp ->
            rsp.qemuImgVersion = "2.4.0"
            rsp.libvirtVersion = "1.3.5"
            return rsp
        }

        env.create {
            local = env.inventoryByName("local")
            local2 = env.inventoryByName("local2")
            diskOffering = env.inventoryByName("diskOffering")
            diskOffering2 = env.inventoryByName("diskOffering2")
            vm = env.inventoryByName("vm")

            MevocoGlobalConfig.PRIMARY_STORAGE_OVER_PROVISIONING_RATIO.updateValue(20)
            setCmdCallHook()

            testDataVolumeWithIoThreadPin()
            calls.clear()

            testVirtioScsiDataVolumeIoThreadPinLimit()
            calls.clear()

            testErrorIoThreadPin()
            calls.clear()

            testStoppedVmDataVolumeIoThreadPinOperations()
        }
    }

    void setCmdCallHook() {
        env.afterSimulator(MevocoKVMConstant.SET_VM_IOTHREAD_PIN_PATH) { rsp, HttpEntity<String> e, EnvSpec env ->
            def cmd = JSONObjectUtil.toObject(e.body, MevocoKVMAgentCommands.SetVmIoThreadPinCmd.class)
            assert !cmd.vmUuid.isEmpty()
            assert cmd.ioThreadId > 0
            assert !GetCpuRangeMethod.getCpuRange(cmd.pin).isEmpty()
            calls.add(cmd)
            return rsp
        }

        env.afterSimulator(KVMConstant.KVM_ATTACH_VOLUME) { rsp, HttpEntity<String> e, EnvSpec env ->
            def cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.AttachDataVolumeCmd.class)
            if (cmd.volume.ioThreadId != null) {
                assert cmd.volume.ioThreadId > 0
                assert !cmd.volume.ioThreadPin.isEmpty()
            }
            calls.add(cmd)
            return rsp
        }

        env.afterSimulator(KVMConstant.KVM_DETACH_VOLUME) { rsp, HttpEntity<String> e, EnvSpec env ->
            def cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.DetachDataVolumeCmd.class)
            assert !cmd.getVmUuid().isEmpty()
            assert cmd.getVolume() != null
            calls.add(cmd)
            return rsp
        }

        env.afterSimulator(MevocoKVMConstant.DEL_VM_IOTHREAD_PIN_PATH) { rsp, HttpEntity<String> e, EnvSpec env ->
            def cmd = JSONObjectUtil.toObject(e.body, MevocoKVMAgentCommands.DelVmIoThreadPinCmd.class)
            assert !cmd.getVmUuid().isEmpty()
            assert cmd.getIoThreadId() > 0
            calls.add(cmd)
            return rsp
        }

        env.afterSimulator(MevocoKVMConstant.DEL_VM_SCSI_CONTROLLER_PATH) { rsp, HttpEntity<String> e, EnvSpec env ->
            def cmd = JSONObjectUtil.toObject(e.body, MevocoKVMAgentCommands.DelScsiControllerCmd.class)
            assert !cmd.getVmUuid().isEmpty()
            assert cmd.ioThreadId > 0
            calls.add(cmd)
            return rsp
        }

        env.afterSimulator(MevocoKVMConstant.SET_VM_SCSI_CONTROLLER_PATH) { rsp, HttpEntity<String> e, EnvSpec env ->
            def cmd = JSONObjectUtil.toObject(e.body, MevocoKVMAgentCommands.SetScsiControllerCmd.class)
            assert !cmd.getVmUuid().isEmpty()
            assert cmd.ioThreadId > 0
            calls.add(cmd)
            return rsp
        }

        env.afterSimulator(KVMConstant.KVM_START_VM_PATH) { rsp, HttpEntity<String> e, EnvSpec env ->
            def cmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.StartVmCmd.class)
            def ioThreadIds = []
            for (vol in cmd.getDataVolumes()) {
                if (vol.ioThreadId != null) {
                    assert vol.ioThreadId > 0
                    assert !vol.ioThreadPin.isEmpty()
                    assert !GetCpuRangeMethod.getCpuRange(vol.ioThreadPin).isEmpty()
                    ioThreadIds.add(vol.ioThreadId)
                }
            }
            assert ioThreadIds.size() == cmd.getAddons().get("ioThreadNum")

            calls.add(cmd)
            return rsp
        }
    }

    void testDataVolumeWithIoThreadPin() {
        DiskOfferingInventory disk = env.inventoryByName("diskOffering") as DiskOfferingInventory

        VolumeInventory volume = createDataVolume {
            name = "vol"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        List<VolumeInventory> vols = getVmAttachableDataVolume {
            vmInstanceUuid = vm.uuid
        } as List<VolumeInventory>
        assert vols.size() == 1

        List<VmInstanceInventory> vmInstanceInventoryList = getDataVolumeAttachableVm {
            volumeUuid = volume.uuid
        } as List<VmInstanceInventory>
        assert vmInstanceInventoryList.size() == 1

        GetVolumeIoThreadPinResult ioThreadPinResult = getVolumeIoThreadPin {
            uuid = volume.uuid
        } as GetVolumeIoThreadPinResult
        assert null != ioThreadPinResult
        assert ioThreadPinResult.getIoThreadId().isEmpty()
        assert ioThreadPinResult.getPin().isEmpty()
        assert volume.getUuid().equals(ioThreadPinResult.getVolumeUuid())


        SetVolumeIoThreadPinResult setPinResult = setVolumeIoThreadPin {
            uuid = volume.uuid
            vmUuid = vm.uuid
            ioThreadId = 1
            pin = "0-3"
        } as SetVolumeIoThreadPinResult
        assert null != setPinResult
        assert setPinResult.getIoThreadId() == 1
        assert "0-3".equals(setPinResult.getPin())
        assert volume.getUuid().equals(setPinResult.getVolumeUuid())
        assert MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(volume.getUuid())
        assert calls.size() == 1

        GetVolumeIoThreadPinResult Result = getVolumeIoThreadPin {
            uuid = volume.uuid
        } as GetVolumeIoThreadPinResult
        assert null != Result
        assert "1".equals(Result.getIoThreadId())
        assert "0-3".equals(Result.getPin())
        assert volume.getUuid().equals(Result.getVolumeUuid())
        assert calls.size() == 1

        VolumeInventory vol = attachDataVolumeToVm {
            volumeUuid = volume.uuid
            vmInstanceUuid = vm.uuid
        } as VolumeInventory
        assert calls.size() == 2

        rebootVmInstance {
            uuid = vm.uuid
        }
        assert calls.size() == 3

        GetVolumeIoThreadPinResult pinRes = getVolumeIoThreadPin {
            uuid = volume.uuid
        } as GetVolumeIoThreadPinResult
        assert null != pinRes
        assert "1".equals(pinRes.getIoThreadId())
        assert "0-3".equals(pinRes.getPin())
        assert volume.getUuid().equals(pinRes.getVolumeUuid())
        assert calls.size() == 3

        detachDataVolumeFromVm {
            uuid = volume.uuid
            vmUuid = vm.uuid
        }
        assert !MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(volume.uuid)
        assert calls.size() == 5

        GetVolumeIoThreadPinResult unpinRes = getVolumeIoThreadPin {
            uuid = volume.uuid
        } as GetVolumeIoThreadPinResult
        assert null != unpinRes
        assert "".equals(unpinRes.getIoThreadId())
        assert "".equals(unpinRes.getPin())
        assert volume.getUuid().equals(unpinRes.getVolumeUuid())

        SetVolumeIoThreadPinResult pinResult = setVolumeIoThreadPin {
            uuid = volume.uuid
            vmUuid = vm.uuid
            ioThreadId = 1
            pin = "1"
        } as SetVolumeIoThreadPinResult
        assert MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(volume.uuid)
        assert calls.size() == 6

        GetVolumeIoThreadPinResult getPinRes = getVolumeIoThreadPin {
            uuid = volume.uuid
        } as GetVolumeIoThreadPinResult
        assert null != unpinRes
        assert "1".equals(getPinRes.getIoThreadId())
        assert "1".equals(getPinRes.getPin())
        assert volume.getUuid().equals(getPinRes.getVolumeUuid())

        VolumeInventory attachResult = attachDataVolumeToVm {
            volumeUuid = volume.uuid
            vmInstanceUuid = vm.uuid
        } as VolumeInventory

        assert calls.size() == 7

        SetVolumeIoThreadPinResult r = setVolumeIoThreadPin {
            uuid = volume.uuid
            vmUuid = vm.uuid
            ioThreadId = 1
            pin = ""
        } as SetVolumeIoThreadPinResult
        assert !MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(volume.uuid)
        assert calls.size() == 7

        VolumeInventory vol1 = createDataVolume {
            name = "vol1"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        SetVolumeIoThreadPinResult rsp = setVolumeIoThreadPin {
            uuid = vol1.uuid
            vmUuid = vm.uuid
            ioThreadId = 1
            pin = ""
        } as SetVolumeIoThreadPinResult
        assert !MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(volume.uuid)
        assert calls.size() == 7
    }


    void testVirtioScsiDataVolumeIoThreadPinLimit() {
        DiskOfferingInventory disk = env.inventoryByName("diskOffering") as DiskOfferingInventory
        VolumeInventory vol0 = createDataVolume {
            name = "vol0"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        VolumeInventory vol1 = createDataVolume {
            name = "vol1"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        VolumeInventory vol2 = createDataVolume {
            name = "vol2"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        VolumeInventory vol3 = createDataVolume {
            name = "vol3"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        VolumeInventory vol4 = createDataVolume {
            name = "vol4"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        VolumeInventory vol5 = createDataVolume {
            name = "vol5"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        List<VolumeInventory> vols = Arrays.asList(vol0, vol1, vol2, vol3, vol4)
        int threadId = 1
        for (VolumeInventory vol: vols) {
            SetVolumeIoThreadPinResult res = setVolumeIoThreadPin {
                uuid = vol.uuid
                vmUuid = vm.uuid
                ioThreadId = threadId
                pin = threadId.toString()
            } as SetVolumeIoThreadPinResult
            assert null != res
            assert res.getIoThreadId() == threadId
            assert threadId.toString().equals(res.getPin())
            assert vol.getUuid().equals(res.getVolumeUuid())
            assert MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(vol.uuid)
            threadId += 1
        }
        assert calls.size() == 5

        for (VolumeInventory vol: vols) {
            GetVolumeIoThreadPinResult Result = getVolumeIoThreadPin {
                uuid = vol.uuid
            } as GetVolumeIoThreadPinResult
            assert null != Result
            assert !Result.getIoThreadId().isEmpty()
            assert !Result.getPin().isEmpty()
            assert vol.getUuid().equals(Result.getVolumeUuid())
        }

        for (VolumeInventory vol: vols) {
            VolumeInventory r = attachDataVolumeToVm {
                volumeUuid = vol.uuid
                vmInstanceUuid = vm.uuid
            } as VolumeInventory
        }
        assert calls.size() == 10

        SetVolumeIoThreadPinResult res = setVolumeIoThreadPin {
            uuid = vol5.uuid
            vmUuid = vm.uuid
            ioThreadId = 6
            pin = "6"
        } as SetVolumeIoThreadPinResult
        assert res.getPin().equals("6")
        assert res.getIoThreadId() == 6
        assert res.getVolumeUuid().equals(vol5.uuid)
        assert MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(vol5.uuid)
        assert calls.size() == 11

        VolumeInventory virtioVol = createDataVolume {
            name = "virtioVol"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        SetVolumeIoThreadPinResult virtioVolRes = setVolumeIoThreadPin {
            uuid = virtioVol.uuid
            vmUuid = vm.uuid
            ioThreadId = 7
            pin = "7"
        } as SetVolumeIoThreadPinResult
        assert null != virtioVolRes
        assert 7 == virtioVolRes.getIoThreadId()
        assert "7".equals(virtioVolRes.getPin())
        assert MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(virtioVol.uuid)
        assert calls.size() == 12

        SetVolumeIoThreadPinResult attachedIoThreadPinRes = setVolumeIoThreadPin {
            uuid = vol4.uuid
            vmUuid = vm.uuid
            ioThreadId = 5
            pin = "6"
        } as SetVolumeIoThreadPinResult
        assert null != attachedIoThreadPinRes
        assert 5 == attachedIoThreadPinRes.getIoThreadId()
        assert "6".equals(attachedIoThreadPinRes.getPin())
        assert calls.size() == 13

        expect(AssertionError.class) {
            SetVolumeIoThreadPinResult attachedIoThreadPinDiffRes = setVolumeIoThreadPin {
                uuid = vol4.uuid
                vmUuid = vm.uuid
                ioThreadId = 7
                pin = "6"
            } as SetVolumeIoThreadPinResult
        }
    }

    void testErrorIoThreadPin() {
        expect(AssertionError.class) {
            SetVolumeIoThreadPinResult res = setVolumeIoThreadPin {
                uuid = vm.rootVolumeUuid
                vmUuid = vm.uuid
                ioThreadId = 6
                pin = "6"
            } as SetVolumeIoThreadPinResult
        }

        DiskOfferingInventory disk = env.inventoryByName("diskOffering") as DiskOfferingInventory
        VolumeInventory vol0 = createDataVolume {
            name = "vol0"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        expect(AssertionError.class) {
            SetVolumeIoThreadPinResult res = setVolumeIoThreadPin {
                uuid = vol0.uuid
                vmUuid = vm.uuid
                ioThreadId = 6
                pin = "10"
            } as SetVolumeIoThreadPinResult
        }
    }

    void testStoppedVmDataVolumeIoThreadPinOperations() {
        stopVmInstance {
            uuid = vm.uuid
        }

        DiskOfferingInventory disk = env.inventoryByName("diskOffering") as DiskOfferingInventory
        VolumeInventory testVol = createDataVolume {
            name = "testVol"
            diskOfferingUuid = disk.uuid
        } as VolumeInventory

        GetVolumeIoThreadPinResult ioThreadPinResult = getVolumeIoThreadPin {
            uuid = testVol.uuid
        } as GetVolumeIoThreadPinResult
        assert null != ioThreadPinResult
        assert ioThreadPinResult.getIoThreadId().isEmpty()
        assert ioThreadPinResult.getPin().isEmpty()
        assert testVol.getUuid().equals(ioThreadPinResult.getVolumeUuid())

        SetVolumeIoThreadPinResult setPinResult = setVolumeIoThreadPin {
            uuid = testVol.uuid
            vmUuid = vm.uuid
            ioThreadId = 1
            pin = "0-3"
        } as SetVolumeIoThreadPinResult
        assert null != setPinResult
        assert setPinResult.getIoThreadId() == 1
        assert "0-3".equals(setPinResult.getPin())
        assert testVol.getUuid().equals(setPinResult.getVolumeUuid())
        assert MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(testVol.getUuid())
        assert calls.size() == 0

        GetVolumeIoThreadPinResult res = getVolumeIoThreadPin {
            uuid = testVol.uuid
        } as GetVolumeIoThreadPinResult
        assert null != res
        assert res.getIoThreadId() == "1"
        assert res.getPin() == "0-3"
        assert testVol.getUuid().equals(res.getVolumeUuid())

        setPinResult = setVolumeIoThreadPin {
            uuid = testVol.uuid
            vmUuid = vm.uuid
            ioThreadId = 2
            pin = "4"
        } as SetVolumeIoThreadPinResult
        assert null != setPinResult
        assert setPinResult.getIoThreadId() == 2
        assert "4".equals(setPinResult.getPin())
        assert testVol.getUuid().equals(setPinResult.getVolumeUuid())
        assert MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(testVol.getUuid())
        assert calls.size() == 0

        GetVolumeIoThreadPinResult result = getVolumeIoThreadPin {
            uuid = testVol.uuid
        } as GetVolumeIoThreadPinResult
        assert null != result
        assert result.getIoThreadId() == "2"
        assert result.getPin() == "4"
        assert testVol.getUuid().equals(result.getVolumeUuid())

        SetVolumeIoThreadPinResult clearResult = setVolumeIoThreadPin {
            uuid = testVol.uuid
            vmUuid = vm.uuid
            ioThreadId = 2
            pin = ""
        } as SetVolumeIoThreadPinResult
        assert null != clearResult
        assert clearResult.getIoThreadId() == 2
        assert "".equals(clearResult.getPin())
        assert testVol.getUuid().equals(clearResult.getVolumeUuid())
        assert !MevocoVolumeSystemTags.IO_THREAD_PIN.hasTag(testVol.getUuid())
        assert calls.size() == 0

        GetVolumeIoThreadPinResult clearRes = getVolumeIoThreadPin {
            uuid = testVol.uuid
        } as GetVolumeIoThreadPinResult
        assert null != clearRes
        assert clearRes.getIoThreadId().isEmpty()
        assert clearRes.getPin().isEmpty()
        assert testVol.getUuid().equals(clearRes.getVolumeUuid())

        deleteDataVolume {
            uuid = testVol.uuid
        }

        expungeDataVolume {
            uuid = testVol.uuid
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}