package org.zstack.test.integration.premium.vpc.ipv6

import org.zstack.core.db.DatabaseFacade
import org.zstack.header.network.l3.L3NetworkConstant
import org.zstack.header.network.service.NetworkServiceType
import org.zstack.header.vpc.VpcConstants
import org.zstack.network.service.eip.EipConstant
import org.zstack.network.service.portforwarding.PortForwardingProtocolType
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.network.IPv6Constants

import static java.util.Arrays.asList


/**
 * Created by shixin on 2020/08/04
 */
class VpcIpv6CandiateCase extends PremiumSubCase {

    EnvSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        useSpring(VpcIpv6Test.springSpec)
    }

    @Override
    void environment() {
        env = VpcIpv6Env.BasicVpc()
    }

    @Override
    void test() {
        dbf = bean(DatabaseFacade.class)
        env.create {
            initEnv()
            TestGetEipAttachAbleNic()
            TestGetLbAttachAbleNic()
            TestGetPfAttachAbleNic()
            testAttachL3ToVpcWithIpsec()
        }
    }

    void initEnv() {
        L2NetworkInventory l2_2 = env.inventoryByName("l2-2")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")
        L3NetworkInventory l3_3 = env.inventoryByName("l3-3")
        VirtualRouterOfferingInventory vr = env.inventoryByName("vr-offering")
        ImageInventory image = env.inventoryByName("image1")
        InstanceOfferingInventory offering = env.inventoryByName("instanceOffering")

        /* 3 vpc network: l3-1 is dual stack, l3-2 is ipv6 only, l3-3 ipv4 only */
        addIpv6RangeByNetworkCidr {
            name = "ipr6-1"
            l3NetworkUuid = l3_1.uuid
            networkCidr = "2020:08:04:1::/64"
            addressMode = IPv6Constants.Stateful_DHCP
        }

        deleteIpRange {
            uuid = l3_2.getIpRanges().get(0).uuid
        }
        addIpv6RangeByNetworkCidr {
            name = "ipr6-2"
            l3NetworkUuid = l3_2.uuid
            networkCidr = "2020:08:04:2::/64"
            addressMode = IPv6Constants.Stateful_DHCP
        }

        /* 3 flat network: eth1_1 is dual stack, eth1_2 is ipv6 only, eth1_3 ipv4 only  */
        NetworkServiceProviderInventory networkServiceProvider = queryNetworkServiceProvider {
            delegate.conditions = ["type=Flat"]
        }[0]
        Map<String, List<String>> netServices = new HashMap<>()
        netServices.put(networkServiceProvider.getUuid(),
                [NetworkServiceType.DHCP.toString(),
                 NetworkServiceType.DNS.toString(),
                 EipConstant.EIP_NETWORK_SERVICE_TYPE])

        L3NetworkInventory eth1_1 = createL3Network {
            name = "eth1_1"
            l2NetworkUuid = l2_2.uuid
            type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE.toString()
            category = "Private"
        }
        attachNetworkServiceToL3Network {
            delegate.l3NetworkUuid = eth1_1.uuid
            delegate.networkServices = netServices
        }
        addIpv6RangeByNetworkCidr {
            name = "ipr6-3"
            l3NetworkUuid = eth1_1.uuid
            networkCidr = "2020:08:04:3::/64"
            addressMode = IPv6Constants.Stateful_DHCP
        }
        addIpRangeByNetworkCidr {
            name = "ipr4-1"
            l3NetworkUuid = eth1_1.getUuid()
            networkCidr = "*******/24"
        }

        L3NetworkInventory eth1_2 = createL3Network {
            name = "eth1_2"
            l2NetworkUuid = l2_2.uuid
            type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE.toString()
            category = "Private"
        }
        attachNetworkServiceToL3Network {
            delegate.l3NetworkUuid = eth1_2.uuid
            delegate.networkServices = netServices
        }
        addIpv6RangeByNetworkCidr {
            name = "ipr6-4"
            l3NetworkUuid = eth1_2.uuid
            networkCidr = "2020:08:04:4::/64"
            addressMode = IPv6Constants.Stateful_DHCP
        }

        L3NetworkInventory eth1_3 = createL3Network {
            name = "eth1_3"
            l2NetworkUuid = l2_2.uuid
            type = L3NetworkConstant.L3_BASIC_NETWORK_TYPE.toString()
            category = "Private"
        }
        attachNetworkServiceToL3Network {
            delegate.l3NetworkUuid = eth1_3.uuid
            delegate.networkServices = netServices
        }
        addIpRangeByNetworkCidr {
            name = "ipr4-2"
            l3NetworkUuid = eth1_3.uuid
            networkCidr = "*******/24"
        }

        L3NetworkInventory eth1_4 = createL3Network {
            name = "eth1_4"
            l2NetworkUuid = l2_2.uuid
            type = VpcConstants.VPC_L3_NETWORK_TYPE
            category = "Private"
        }
        attachNetworkServiceToL3Network {
            delegate.l3NetworkUuid = eth1_4.uuid
            delegate.networkServices = netServices
        }
        addIpv6RangeByNetworkCidr {
            name = "ipr6-5"
            l3NetworkUuid = eth1_4.uuid
            networkCidr = "2020:08:12:01::/112"
            addressMode = IPv6Constants.Stateful_DHCP
        }

        VirtualRouterVmInventory vpc = createVpcVRouter {
            name = "vpc"
            virtualRouterOfferingUuid = vr.uuid
        }
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = l3_1.uuid
        }
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = l3_2.uuid
        }
        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = l3_3.uuid
        }

        /* create a vm with 6 nics */
        createVmInstance {
            name = "vm-1"
            imageUuid = image.uuid
            instanceOfferingUuid = offering.uuid
            l3NetworkUuids = [l3_1.uuid, l3_2.uuid, l3_3.uuid, eth1_1.uuid, eth1_2.uuid, eth1_3.uuid]
            defaultL3NetworkUuid = l3_1.uuid
        }
    }

    void TestGetEipAttachAbleNic() {
        L3NetworkInventory pub3_1 = env.inventoryByName("pubL3-1")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")
        L3NetworkInventory l3_2 = env.inventoryByName("l3-2")
        L3NetworkInventory l3_3 = env.inventoryByName("l3-3")

        VipInventory vip4 = createVip {
            name = "vip4"
            l3NetworkUuid = pub3_1.uuid
        }

        EipInventory eip4 = createEip {
            name = "eip4"
            vipUuid = vip4.uuid
        }

        /* 2 ipv4 nic, 2 dual stack nic */
        def eip4AttachAble = getEipAttachableVmNics { eipUuid = eip4.uuid } as List<VmNicInventory>
        assert eip4AttachAble.size() == 4

        L3NetworkInventory eth1_3 = queryL3Network {conditions=["name=eth1_3"]}[0]
        VmInstanceInventory vm = queryVmInstance {conditions=["name=vm-1"]}[0]
        VmNicInventory nicL3_2
        VmNicInventory nic_3
        for (VmNicInventory nic : vm.vmNics) {
            if (nic.l3NetworkUuid == l3_2.uuid) {
                nicL3_2 = nic
            } else if (nic.l3NetworkUuid == eth1_3.uuid) {
                nic_3 = nic
            }
        }
        assert nicL3_2 != null
        assert nic_3 != null
        expect(AssertionError.class) {
            attachEip {
                eipUuid = eip4.uuid
                vmNicUuid = nicL3_2.uuid
            }
        }

        IpRangeInventory ipr6 = addIpv6RangeByNetworkCidr {
            name = "ipr6-5"
            l3NetworkUuid = pub3_1.uuid
            networkCidr = "2020:08:04:6::/64"
            addressMode = IPv6Constants.Stateful_DHCP
        }

        VipInventory vip6 = createVip {
            name = "vip6"
            l3NetworkUuid = pub3_1.uuid
            ipRangeUuid = ipr6.uuid
        }

        EipInventory eip6 = createEip {
            name = "eip6"
            vipUuid = vip6.uuid
        }

        /* 1 flat ipv6 nic, 1 flat dual stack nic */
        /* add vpc ipv6 eip support: l3-1 is dual stack, l3-2 is ipv6 only */
        List<VmNicInventory> eip6AttachAble = getEipAttachableVmNics { eipUuid = eip6.uuid }
        assert eip6AttachAble.size() == 2+2
        /* eip6 can be attached to nic of vpc network  */
//        expect(AssertionError.class) {
//            attachEip {
//                eipUuid = eip6.uuid
//                vmNicUuid = nicL3_2.uuid
//            }
//        }
        /* eip6 can not be attached to ipv4 nic of flat network  */
        expect(AssertionError.class) {
            attachEip {
                eipUuid = eip6.uuid
                vmNicUuid = nic_3.uuid
            }
        }

        L3NetworkInventory eth1_1 = queryL3Network {conditions=["name=eth1_1"]} [0]
        L3NetworkInventory eth1_2 = queryL3Network {conditions=["name=eth1_2"]} [0]

        for (VmNicInventory nic : vm.vmNics) {
            if (nic.l3NetworkUuid == eth1_1.uuid || nic.l3NetworkUuid == eth1_2.uuid || nic.l3NetworkUuid == eth1_3.uuid) {
                detachL3NetworkFromVm {
                    vmNicUuid = nic.uuid
                }
            }
        }

        /* when there is no ipv6 nic in flat network, return 0 */
        /* eip6 can be attached to nic of vpc network, return 2 */
        List<VmNicInventory> eip6AttachAble1 = getEipAttachableVmNics { eipUuid = eip6.uuid }
        assert eip6AttachAble1.size() == 0+2

        attachL3NetworkToVm {
            vmInstanceUuid = vm.uuid
            l3NetworkUuid = eth1_1.uuid
        }
        attachL3NetworkToVm {
            vmInstanceUuid = vm.uuid
            l3NetworkUuid = eth1_2.uuid
        }
        attachL3NetworkToVm {
            vmInstanceUuid = vm.uuid
            l3NetworkUuid = eth1_3.uuid
        }
    }

    void TestGetLbAttachAbleNic() {
        L3NetworkInventory pub3_1 = env.inventoryByName("pubL3-1")

        IpRangeInventory ipr6 = queryIpRange {conditions=["name=ipr6-5"]}[0]

        VipInventory vip4 = createVip {
            name = "lb-vip4"
            l3NetworkUuid = pub3_1.uuid
        }

        LoadBalancerInventory lb1 = createLoadBalancer {
            name = "test-lb-4"
            vipUuid = vip4.uuid
        }

        LoadBalancerListenerInventory listener4 = createLoadBalancerListener {
            protocol = "tcp"
            loadBalancerUuid = lb1.uuid
            loadBalancerPort = 101
            instancePort = 101
            name = "test-listener-1-1"
        }

        List<VmNicInventory> vmNics = getCandidateVmNicsForLoadBalancer {
            listenerUuid = listener4.uuid
        }
        assert vmNics.size() == 3

        VipInventory vip6 = createVip {
            name = "lb-vip4"
            l3NetworkUuid = pub3_1.uuid
            ipRangeUuid = ipr6.uuid
        }

        LoadBalancerInventory lb6 = createLoadBalancer {
            name = "test-lb-4"
            ipv6VipUuid = vip6.uuid
        }

        LoadBalancerListenerInventory listener6 = createLoadBalancerListener {
            protocol = "tcp"
            loadBalancerUuid = lb6.uuid
            loadBalancerPort = 101
            instancePort = 101
            name = "test-listener-6"
        }
        vmNics = getCandidateVmNicsForLoadBalancer {
            listenerUuid = listener6.uuid
        }
    }

    void TestGetPfAttachAbleNic() {
        L3NetworkInventory pub3_1 = env.inventoryByName("pubL3-1")

        IpRangeInventory ipr6 = queryIpRange {conditions=["name=ipr6-5"]}[0]

        VipInventory vip4 = createVip {
            name = "pf-vip4"
            l3NetworkUuid = pub3_1.uuid
        }
        PortForwardingRuleInventory pf4 = createPortForwardingRule {
            name = "pf-4"
            vipUuid = vip4.uuid
            vipPortStart = 3306
            vipPortEnd = 3306
            privatePortEnd = 3306
            privatePortStart = 3306
            protocolType = PortForwardingProtocolType.TCP.toString()
        }

        List<VmNicInventory> vmNics = getPortForwardingAttachableVmNics {
            ruleUuid = pf4.uuid
        }
        assert vmNics.size() == 2

        VipInventory vip6 = createVip {
            name = "pf-vip4"
            l3NetworkUuid = pub3_1.uuid
            ipRangeUuid = ipr6.uuid
        }
        PortForwardingRuleInventory pf6 = createPortForwardingRule {
            name = "pf-4"
            vipUuid = vip6.uuid
            vipPortStart = 3306
            vipPortEnd = 3306
            privatePortEnd = 3306
            privatePortStart = 3306
            protocolType = PortForwardingProtocolType.TCP.toString()
        }

        vmNics = getPortForwardingAttachableVmNics {
            ruleUuid = pf6.uuid
        }
        assert vmNics.size() == 0
    }

    void testAttachL3ToVpcWithIpsec() {
        L3NetworkInventory pub3_1 = env.inventoryByName("pubL3-1")
        L3NetworkInventory l3_1 = env.inventoryByName("l3-1")

        VipInventory vip4_ipsec = createVip {
            name = "ipsec-vip4"
            l3NetworkUuid = pub3_1.uuid
        }

        IPsecConnectionInventory ipsec_1 = createIPsecConnection {
            name = "ipsec-1"
            l3NetworkUuid = l3_1.uuid
            peerAddress = "***************"
            authKey = "test"
            vipUuid = vip4_ipsec.uuid
            peerCidrs = asList("********/24", "********/24")
        }

        VirtualRouterVmInventory vpc = queryVpcRouter { conditions = ["name=vpc"]} [0]
        L3NetworkInventory eth1_4 = queryL3Network {conditions=["name=eth1_4"]} [0]

        attachL3NetworkToVm {
            vmInstanceUuid = vpc.uuid
            l3NetworkUuid = eth1_4.uuid
        }
    }

    @Override
    void clean() {
        env.delete()
    }
}
