package org.zstack.test.integration.zwatch

import org.springframework.http.HttpEntity
import org.zstack.core.Platform
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.core.db.SimpleQuery
import org.zstack.sdk.BackupStorageInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.sns.SNSTopicInventory
import org.zstack.sdk.zwatch.alarm.ActionParam
import org.zstack.sdk.zwatch.alarm.AlarmDataAckInventory
import org.zstack.sdk.zwatch.alarm.AlarmInventory
import org.zstack.sdk.zwatch.alarm.AlertDataAckInventory
import org.zstack.sdk.zwatch.alarm.EventDataAckInventory
import org.zstack.sdk.zwatch.alarm.EventSubscriptionInventory
import org.zstack.sdk.zwatch.datatype.Label
import org.zstack.sdk.zwatch.datatype.Operator
import org.zstack.testlib.controller.BackupStorageController
import org.zstack.testlib.premium.EnvPremiumSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.util.TProxy
import org.zstack.utils.data.SizeUnit
import org.zstack.zwatch.ZWatchConstants
import org.zstack.zwatch.ZWatchGlobalConfig
import org.zstack.zwatch.alarm.AlarmDataAckVO
import org.zstack.zwatch.alarm.AlarmDataAckVO_
import org.zstack.zwatch.alarm.AlertDataAckVO
import org.zstack.zwatch.alarm.AlertDataAckVO_
import org.zstack.zwatch.alarm.EventSubscription
import org.zstack.zwatch.alarm.sns.SNSActionFactory
import org.zstack.zwatch.datatype.EmergencyLevel
import org.zstack.zwatch.driver.DatabaseDriver
import org.zstack.zwatch.namespace.BackupStorageNamespace
import org.zstack.zwatch.namespace.HostNamespace
import org.zstack.zwatch.namespace.VmCountNamespace
import org.zstack.zwatch.namespace.VmNamespace
import org.zstack.zwatch.prometheus.PrometheusDatabaseDriver
import org.zstack.zwatch.ruleengine.ComparisonOperator
import sun.awt.PlatformFont

/**
 * Create by yaoning at 2020/10/20
 */
class AckAlertDataCase extends PremiumSubCase {
    EnvPremiumSpec env

    String HTTP_ENDPOINT_FOR_AlARM = "/http-endpoint"
    String HTTP_ENDPOINT_FOR_EVENT = "/http-endpoint-for-event"
    PrometheusDatabaseDriver driver
    List<org.zstack.sdk.zwatch.datatype.Datapoint> currentDatapoints = []
    VmNamespace vmNamespace

    def vmUuid = Platform.uuid

    @Override
    void setup() {
        useSpring(ZWatchTest.springSpec)
    }

    void environment() {
        driver = bean(PrometheusDatabaseDriver.class)
        vmNamespace = bean(VmNamespace.class)

        TProxy proxy = new TProxy(driver).mockMethod("query") { y, z ->
            return currentDatapoints
        }

        def p = proxy.protect(vmNamespace, "driver")
        onCleanExecute { p.recover() }
        vmNamespace.driver = proxy as DatabaseDriver

        env = makeEnv {
            sns {
                topic {
                    name = "topic"
                }

                httpEndpoint {
                    name = "http"
                    url = "http://127.0.0.1:8989${HTTP_ENDPOINT_FOR_AlARM}"

                    subscribe("topic")
                }

                topic {
                    name = "topic2"
                }

                httpEndpoint {
                    name = "http-event"
                    url = "http://127.0.0.1:8989${HTTP_ENDPOINT_FOR_EVENT}"

                    subscribe("topic2")
                }
            }

            zwatch {
                alarm {
                    name = "alarm"
                    comparisonOperator = ComparisonOperator.GreaterThan.toString()
                    period = 1
                    namespace = vmNamespace.getName()
                    metricName = VmNamespace.CPUUsedUtilization.name
                    threshold = 10
                    label(VmNamespace.LabelNames.VMUuid.toString(), vmUuid)
                    useTopic("topic")
                    repeatInterval = 1
                    emergencyLevel = EmergencyLevel.Important.name()
                }
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(10)
            }

            zone {
                name = "zone"

                attachBackupStorage("imagestore")

                cluster {
                    name = "cluster1"
                    hypervisorType = "KVM"

                    kvm {
                        name = "host"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                    }

                    attachPrimaryStorage("nfs-ps")
                    attachL2Network("l2")
                }

                nfsPrimaryStorage {
                    name = "nfs-ps"
                    url = "127.0.0.1:/nfs_root"
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }
            }

            imageStore {
                name = "imagestore"
                username = "username"
                password = "password"
                hostname = "hostname"
                url = "/data"

                image {
                    name = "image"
                    url = "http://zstack.org/download/test.qcow2"
                    platform = "Linux"
                }

                image {
                    name = "win10"
                    url = "http://download.ms/image/win10.qcow2"
                    platform = "Windows"
                }
            }

            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(4)
                cpu = 4
            }
        }
    }

    @Override
    void test() {
        env.create {
            testAckAlarmData()
            testAckEventData()
            testAckAlarmDataNotEffect()

            testAutoResumeAlertByAlarm()
            testAutoResumeAlertByEvent()

            cleanAlertDataAckVO()
        }
    }

    /**
     * mock alarm resume alert
     * 1. setting EVALUATION_INTERVAL = 1s
     * 2. create a vm
     * 3. create a AckAlarmData
     * 4. trigger alarm
     * 5. assert resume alert
     */
    void testAutoResumeAlertByAlarm() {
        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(1)

        // Create a vm
        def instanceOffering = env.inventoryByName("instanceOffering")
        def l3 = env.inventoryByName("pubL3")
        def image = env.inventoryByName("image")
        vmUuid = Platform.uuid;
        VmInstanceInventory vm = createVmInstance {
            resourceUuid = vmUuid
            name = "vm"
            instanceOfferingUuid = instanceOffering.uuid
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
        }

        // Create a AckAlert
        // set ackPeriodSec: 3S
        AlarmInventory alarm = env.inventoryByName("alarm")
        String dataUuid = Platform.getUuid()
        String resUuid = vmUuid
        ackAlarmData {
            alarmUuid = alarm.uuid
            alertDataUuid = dataUuid
            dataType = ZWatchConstants.ALARM_DATA_TYPE
            resourceUuid = resUuid
            ackPeriodSec = 3
        }

        // trigger alarm
        currentDatapoints = [
                new org.zstack.zwatch.datatype.Datapoint(value: 101, labels: [(VmNamespace.LabelNames.VMUuid.toString()): vmUuid]),
                new org.zstack.zwatch.datatype.Datapoint(value: 102, labels: [(VmNamespace.LabelNames.VMUuid.toString()): vmUuid]),
                new org.zstack.zwatch.datatype.Datapoint(value: 103, labels: [(VmNamespace.LabelNames.VMUuid.toString()): vmUuid]),
        ]
        env.simulator(HTTP_ENDPOINT_FOR_AlARM) { HttpEntity<String> e ->

        }

        // test resume alert
        retryInSecs {
            List<AlertDataAckInventory> ackInventories = queryAlertDataAck {
                conditions = ["alertDataUuid=${dataUuid}".toString()]
            }
            assert ackInventories.size() == 1
            assert ackInventories.get(0).resumeAlert
        }
    }

    /**
     * mock event resume alert
     * 1. setting EVALUATION_INTERVAL = 1s
     * 2. create a event & binding topic
     * 3. create a AckAlarmData
     * 4. trigger event
     * 5. assert resume alert
     */
    void testAutoResumeAlertByEvent() {
        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(1)

        // create a event & binding topic
        BackupStorageInventory bs = env.inventoryByName("imagestore")

        SNSTopicInventory topicInventory = env.inventoryByName("topic2")
        BackupStorageNamespace ns = bean(BackupStorageNamespace.class)
        EventSubscriptionInventory subscription = subscribeEvent {
            namespace = ns.getName()
            eventName = BackupStorageNamespace.BackupStorageDisconnected.name
            actions = [new ActionParam(actionUuid: topicInventory.uuid, actionType: SNSActionFactory.type.toString())]
        }

        // create a ackEventData
        String subscriptionUuid = subscription.uuid
        String dataUuid = Platform.getUuid()
        String resUuid = bs.uuid
        ackEventData {
            eventSubscriptionUuid = subscriptionUuid
            alertDataUuid = dataUuid
            dataType = ZWatchConstants.EVENT_DATA_TYPE
            resourceUuid = resUuid
            ackPeriodSec = 3
        }

        // trigger event
        boolean flag = false
        env.simulator(HTTP_ENDPOINT_FOR_EVENT) { HttpEntity<String> e ->
            flag = true
        }

        // test resume alert
        retryInSecs {
            List<AlertDataAckInventory> ackInventories = queryAlertDataAck {
                conditions = ["alertDataUuid=${dataUuid}".toString()]
            }
            assert ackInventories.size() == 1
            assert ackInventories.resumeAlert
        }
    }

    void testAckAlarmDataNotEffect() {
        AlarmInventory alarmForTestVmCount = createAlarm {
            name = "alarm-test"
            comparisonOperator = ComparisonOperator.GreaterThan.toString()
            namespace = vmNamespace.getName()
            metricName = VmCountNamespace.TotalVMCount.name
            threshold = 2
            repeatInterval = 1
            repeatCount = 1
        }

        ackAlarmData {
            alarmUuid = alarmForTestVmCount.getUuid()
            alertDataUuid = Platform.uuid
            dataType = ZWatchConstants.ALARM_DATA_TYPE
            resourceUuid = ""
            ackPeriodSec = 6000
        }

        assert Q.New(AlarmDataAckVO.class)
                .eq(AlarmDataAckVO_.alarmUuid, alarmForTestVmCount.getUuid())
                .eq(AlarmDataAckVO_.resourceUuid, alarmForTestVmCount.getUuid())
                .find() != null
    }

    void testAckAlarmData() {
        ZWatchGlobalConfig.EVALUATION_INTERVAL.updateValue(1)

        def instanceOffering = env.inventoryByName("instanceOffering")
        def l3 = env.inventoryByName("pubL3")
        def image = env.inventoryByName("image")
        VmInstanceInventory vm = createVmInstance {
            resourceUuid = vmUuid
            name = "vm"
            instanceOfferingUuid = instanceOffering.uuid
            l3NetworkUuids = [l3.uuid]
            imageUuid = image.uuid
        }

        currentDatapoints = [
                new org.zstack.zwatch.datatype.Datapoint(value: 101, labels: [(VmNamespace.LabelNames.VMUuid.toString()): vmUuid]),
                new org.zstack.zwatch.datatype.Datapoint(value: 102, labels: [(VmNamespace.LabelNames.VMUuid.toString()): vmUuid]),
                new org.zstack.zwatch.datatype.Datapoint(value: 103, labels: [(VmNamespace.LabelNames.VMUuid.toString()): vmUuid]),
        ]

        boolean flag = false
        int alarmCount = 0
        env.simulator(HTTP_ENDPOINT_FOR_AlARM) { HttpEntity<String> e ->
            flag = true
            alarmCount ++
        }
        retryInSecs {
            assert flag
        }

        AlarmInventory alarm = env.inventoryByName("alarm")
        String dataUuid = Platform.getUuid()
        String resUuid = vmUuid
        ackAlarmData {
            alarmUuid = alarm.uuid
            alertDataUuid = dataUuid
            dataType = ZWatchConstants.ALARM_DATA_TYPE
            resourceUuid = resUuid
            ackPeriodSec = 6000
        }

        int count = alarmCount
        assert !retryInSecs {
            return alarmCount - count > 1
        }

        List<AlertDataAckInventory> ackInventories = queryAlertDataAck {
            conditions = ["alertDataUuid=${dataUuid}".toString()]
        }
        assert ackInventories.size() == 1

        AlarmDataAckInventory ackInventory = (AlarmDataAckInventory) ackInventories.get(0)
        assert ackInventory.ackDate != null
        assert ackInventory.ackPeriod == 6000
        assert ackInventory.alertType == ZWatchConstants.ALARM_DATA_TYPE
        assert ackInventory.alertDataUuid == dataUuid
        assert ackInventory.alarmUuid == alarm.uuid
        assert ackInventory.resourceUuid == vmUuid
        assert !ackInventory.resumeAlert

        updateAlertDataAck {
            alertDataUuid = dataUuid
            resumeAlert = true
        }
        ackInventory = queryAlertDataAck {
            conditions = ["alertDataUuid=${dataUuid}".toString()]
        }.get(0)
        assert ackInventory.resumeAlert

        count = alarmCount
        retryInSecs {
            assert alarmCount > count
        }

        long lastAckTime = ackInventory.ackDate.getTime()
        ackInventory = ackAlarmData {
            alarmUuid = alarm.uuid
            alertDataUuid = dataUuid
            dataType = ZWatchConstants.ALARM_DATA_TYPE
            resourceUuid = resUuid
            ackPeriodSec = 12000
        }
        assert ackInventory.ackPeriod == 12000
        assert ackInventory.ackDate.getTime() >= lastAckTime
    }

    void testAckEventData() {
        BackupStorageInventory bs = env.inventoryByName("imagestore")

        SNSTopicInventory topicInventory = env.inventoryByName("topic2")
        BackupStorageNamespace ns = bean(BackupStorageNamespace.class)
        EventSubscriptionInventory subscription = subscribeEvent {
            namespace = ns.getName()
            eventName = BackupStorageNamespace.BackupStorageDisconnected.name
            actions = [new ActionParam(actionUuid: topicInventory.uuid, actionType: SNSActionFactory.type.toString())]
        }

        boolean flag = false
        env.simulator(HTTP_ENDPOINT_FOR_EVENT) { HttpEntity<String> e ->
            flag = true
        }

        String subscriptionUuid = subscription.uuid
        String dataUuid = Platform.getUuid()
        String resUuid = bs.uuid
        ackEventData {
            eventSubscriptionUuid = subscriptionUuid
            alertDataUuid = dataUuid
            dataType = ZWatchConstants.EVENT_DATA_TYPE
            resourceUuid = resUuid
            ackPeriodSec = 6000
        }

        List<AlertDataAckInventory> ackInventories = queryAlertDataAck {
            conditions = ["alertDataUuid=${dataUuid}".toString()]
        }
        assert ackInventories.size() == 1

        EventDataAckInventory ackInventory = (EventDataAckInventory) ackInventories.get(0)
        assert ackInventory.ackDate != null
        assert ackInventory.ackPeriod == 6000
        assert ackInventory.alertType == ZWatchConstants.EVENT_DATA_TYPE
        assert ackInventory.alertDataUuid == dataUuid
        assert ackInventory.eventSubscriptionUuid == subscriptionUuid
        assert !ackInventory.resumeAlert

        def controller = new BackupStorageController(env)
        controller.disconnect("imagestore")
        assert !retryInSecs {
            return flag
        }

        long lastAckTime = ackInventory.ackDate.getTime()
        ackInventory = ackEventData {
            eventSubscriptionUuid = subscriptionUuid
            alertDataUuid = dataUuid
            dataType = ZWatchConstants.EVENT_DATA_TYPE
            resourceUuid = resUuid
            ackPeriodSec = 16000
        }
        assert ackInventory.ackPeriod == 16000
        assert ackInventory.ackDate.getTime() >= lastAckTime
    }

    void cleanAlertDataAckVO() {
        SQL.New(AlertDataAckVO.class).hardDelete()
    }

    @Override
    void clean() {
        env.delete()
    }
}

