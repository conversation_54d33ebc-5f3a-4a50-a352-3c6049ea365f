package org.zstack.test.integration.crypto

import org.springframework.http.HttpEntity
import org.zstack.core.Platform
import org.zstack.core.cloudbus.*
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.core.encrypt.EncryptFacade
import org.zstack.core.encrypt.EncryptGlobalConfig
import org.zstack.core.encrypt.EncryptType
import org.zstack.crypto.auth.CryptoAuthenticationGlobalConfig
import org.zstack.crypto.auth.CryptoAuthenticationManagerImpl
import org.zstack.crypto.securitymachine.ConnectSecurityMachineMsg
import org.zstack.crypto.securitymachine.SecurityMachineDeletionMsg
import org.zstack.crypto.securitymachine.SecurityMachineErrors
import org.zstack.crypto.securitymachine.SecurityMachineHeartbeatDetectionReply
import org.zstack.crypto.securitymachine.SecurityMachineTrackerImpl
import org.zstack.crypto.securitymachine.secretresourcepool.ChangeSecretResourcePoolStateMsg
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePoolDeletionMsg
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePool
import org.zstack.crypto.securitymachine.thirdparty.infoSec.InfoSecEncryptDriver
import org.zstack.crypto.securitymachine.thirdparty.infoSec.InfoSecSecretResourcePoolConstant
import org.zstack.crypto.securitymachine.thirdparty.infoSec.InfoSecSecurityMachineConstant
import org.zstack.crypto.securitymachine.thirdparty.infoSec.InfoSecSecretResourcePoolVO
import org.zstack.crypto.securitymachine.thirdparty.infoSec.InfoSecSecretResourcePoolVO_
import org.zstack.crypto.securitymachine.thirdparty.infoSec.InfoSecSecurityMachineVO
import org.zstack.crypto.securitymachine.thirdparty.infoSec.InfoSecSecurityMachineVO_
import org.zstack.header.cluster.APICreateClusterMsg
import org.zstack.header.cluster.CreateClusterMsg
import org.zstack.header.core.encrypt.EncryptConstant
import org.zstack.header.errorcode.ErrorCode
import org.zstack.header.errorcode.ErrorableValue
import org.zstack.header.errorcode.OperationFailureException
import org.zstack.header.errorcode.SysErrors
import org.zstack.header.managementnode.ManagementNodeInventory
import org.zstack.header.managementnode.ManagementNodeState
import org.zstack.header.managementnode.ManagementNodeVO
import org.zstack.header.message.APIEvent
import org.zstack.header.message.MessageReply
import org.zstack.header.securitymachine.SecretResourcePoolConstant
import org.zstack.header.securitymachine.SecretResourcePoolState
import org.zstack.header.securitymachine.SecretResourcePoolVO
import org.zstack.header.securitymachine.SecretResourcePoolVO_
import org.zstack.crypto.securitymachine.SecurityMachineGlobalConfig
import org.zstack.header.securitymachine.SecurityMachineCanonicalEvents
import org.zstack.header.securitymachine.SecurityMachineConstant
import org.zstack.header.securitymachine.SecurityMachineKeyType
import org.zstack.header.securitymachine.SecurityMachineState
import org.zstack.header.securitymachine.SecurityMachineStatus
import org.zstack.header.securitymachine.SecurityMachineVO
import org.zstack.header.securitymachine.SecurityMachineVO_
import org.zstack.header.securitymachine.SecretResourcePoolStateEvent
import org.zstack.crypto.securitymachine.SecurityMachineHeartbeatDetectionMsg
import org.zstack.header.securitymachine.SecurityMachineStateEvent
import org.zstack.sdk.AddInfoSecSecurityMachineAction
import org.zstack.sdk.ApiException
import org.zstack.sdk.ChangeSecurityMachineStateAction
import org.zstack.sdk.DeleteSecretResourcePoolAction
import org.zstack.sdk.DeleteSecurityMachineAction
import org.zstack.sdk.InfoSecSecretResourcePoolInventory
import org.zstack.sdk.InfoSecSecurityMachineInventory
import org.zstack.sdk.SecretResourcePoolInventory
import org.zstack.sdk.SetSecurityMachineKeyAction
import org.zstack.sdk.SecurityMachineEncryptAction
import org.zstack.sdk.ZoneInventory
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.crypto.SecurityMachineSimulator
import org.zstack.testlib.premium.crypto.SimulatorClientFactory
import org.zstack.zwatch.alarm.sns.AbstractTextTemplate
import org.zstack.zwatch.namespace.SecurityMachineNamespace
import org.zstack.zwatch.datatype.EmergencyLevel

import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import java.util.function.Consumer

import static org.zstack.core.Platform.operr

/**
 * Created by LiangHanYu on 2021/11/8 20:39
 */
class SecurityMachineCase extends PremiumSubCase {
    EnvSpec env
    ZoneInventory zoneInv
    EncryptFacade encryptFacade
    InfoSecSecretResourcePoolInventory infoPool
    SecurityMachineSimulator infoSecSimulator
    SimulatorClientFactory clientFactory
    SecurityMachineNamespace securityMachineNamespace
    String BASE_URL = "/test-http-endpoint"
    CloudBus bus
    String testConnectError = "test connect failed on purpose"
    String connectError = "single connect failed on purpose"
    String generateSm4TokenError = "generateSm4Token failed on purpose"
    String generateProtectTokenError = "generateProtectToken failed on purpose"
    String generateHmacTokenError = "generateHmacToken failed on purpose"
    String failDeleteError = "fail to delete on purpose"
    String dec = "Description"
    String fakeModel = "model"
    String fakeName = "updateName"

    @Override
    void setup() {
        useSpring(CryptoTest.springSpec)
    }

    @Override
    void environment() {
        securityMachineNamespace = bean(SecurityMachineNamespace.class)
        env = makeEnv {
            zone {
                name = "zone"
            }

            sns {
                topic {
                    name = "topic"
                }

                httpEndpoint {
                    name = "http"
                    url = "http://127.0.0.1:8989${BASE_URL}"

                    subscribe("topic")
                }
            }

            zwatch {
                event {
                    namespace = securityMachineNamespace.getName()
                    eventName = securityMachineNamespace.SecurityMachineStateChange.name
                    emergencyLevel = EmergencyLevel.Emergent.name()
                    useTopic("topic")
                }
            }

        }
        clientFactory = componentLoader.getComponentByBeanName("InfoSecSimulatorClientFactory") as SimulatorClientFactory
        infoSecSimulator = clientFactory.create() as SecurityMachineSimulator
    }

    @Override
    void test() {
        env.create {
            prepare()
            testSecretResourcePoolLifeCycle()
            testSecretResourcePoolSetKey()
            testSecurityMachineLifeCycle()
            testInternalInfoSecEncryptDriver()
            testTrackerIfNodeJoinOrLeft()
            testBatchDeleteSecurityMachine()
            testSecurityMachineHeartbeatDetect()
            testSecurityMachineEncrypt()
            testDealWithUnknownMessage()
        }
    }

    void prepare() {
        zoneInv = env.inventoryByName("zone")
        bus = bean(CloudBus.class)
        encryptFacade = bean(EncryptFacade)
    }

    void testDealWithUnknownMessage() {
        //test passThrough of SecurityMachineImpl handles UnknownMessage expect fail
        ErrorCode connectError = null
        ConnectSecurityMachineMsg hMsg = new ConnectSecurityMachineMsg()
        hMsg.setSecurityMachineUuid(infoPool.getUuid())
        bus.makeLocalServiceId(hMsg, SecurityMachineConstant.SERVICE_ID)
        bus.send(hMsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
                connectError = reply.getError()
            }
        })
        retryInSecs {
            connectError = null
        }

        //test passThrough of SecretResourcePoolManagerImpl handles UnknownMessage expect fail
        ErrorCode changeError = null
        ChangeSecretResourcePoolStateMsg cMsg = new ChangeSecretResourcePoolStateMsg()
        cMsg.setUuid(zoneInv.getUuid())
        bus.makeLocalServiceId(cMsg, SecretResourcePoolConstant.SERVICE_ID)
        bus.send(cMsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
                changeError = reply.getError()
            }
        })
        retryInSecs {
            changeError = null
        }

        //test SecurityMachine dealWithUnknownMessage expect fail
        APIEvent unknownSecurityMachineAPiEvent = null
        MessageReply unknownSecurityMachineLocalReply = null
        APICreateClusterMsg apiClusterMsg = new APICreateClusterMsg()
        bus.makeLocalServiceId(apiClusterMsg, SecurityMachineConstant.SERVICE_ID)
        bus.send(apiClusterMsg, new Consumer<APIEvent>() {
            @Override
            void accept(APIEvent apiEvent) {
                unknownSecurityMachineAPiEvent = apiEvent
            }
        })
        CreateClusterMsg localClusterMsg = new CreateClusterMsg()
        bus.makeLocalServiceId(localClusterMsg, SecurityMachineConstant.SERVICE_ID)
        bus.send(localClusterMsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
                unknownSecurityMachineLocalReply = reply
            }
        })

        //test SecretResourcePool dealWithUnknownMessage expect fail
        APIEvent unknownSecretResourcePoolAPiEvent = null
        MessageReply unknownSecretResourcePoolLocalReply = null

        apiClusterMsg = new APICreateClusterMsg()
        bus.makeLocalServiceId(apiClusterMsg, SecretResourcePoolConstant.SERVICE_ID)
        bus.send(apiClusterMsg, new Consumer<APIEvent>() {
            @Override
            void accept(APIEvent apiEvent) {
                unknownSecretResourcePoolAPiEvent = apiEvent
            }
        })
        localClusterMsg = new CreateClusterMsg()
        bus.makeLocalServiceId(localClusterMsg, SecretResourcePoolConstant.SERVICE_ID)
        bus.send(localClusterMsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
                unknownSecretResourcePoolLocalReply = reply
            }
        })

        retryInSecs {
            assert unknownSecurityMachineAPiEvent.getError().getCode() == SysErrors.UNKNOWN_MESSAGE_ERROR.toString()
            assert unknownSecurityMachineLocalReply.getError().getCode() == SysErrors.UNKNOWN_MESSAGE_ERROR.toString()

            assert unknownSecretResourcePoolAPiEvent.getError().getCode() == SysErrors.UNKNOWN_MESSAGE_ERROR.toString()
            assert unknownSecretResourcePoolLocalReply.getError().getCode() == SysErrors.UNKNOWN_MESSAGE_ERROR.toString()
        }
    }

    void testInternalInfoSecEncryptDriver() {
        EncryptGlobalConfig.ENCRYPT_DRIVER.updateValue(InfoSecSecurityMachineConstant.SECURITY_MACHINE_TYPE)
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(infoPool.getUuid())

        infoSecSimulator.installSm4EncryptHandler({ text ->
            return ErrorableValue.ofErrorCode(operr(generateSm4TokenError))
        })
        infoSecSimulator.installSm4DecryptHandler({ text ->
            return ErrorableValue.ofErrorCode(operr(generateSm4TokenError))
        })
        //test default encrypt expect fail
        expect(OperationFailureException.class) {
            encryptFacade.encrypt(fakeName)
        }
        //test default decrypt expect return original
        assert fakeName == encryptFacade.decrypt(fakeName)

        infoSecSimulator.installSm4EncryptHandler({ text ->
            return ErrorableValue.of(dec)
        })
        infoSecSimulator.installSm4DecryptHandler({ text ->
            return ErrorableValue.of(fakeModel)
        })
        //test default encrypt expect success
        assert encryptFacade.encrypt(fakeName) == dec
        //test default decrypt expect success
        assert encryptFacade.decrypt(fakeName) == fakeModel

        //test default encrypt expect connect failed due to connection
        infoSecSimulator.installConnectHandler({ uuid ->
            return ErrorableValue.ofErrorCode(operr(connectError))
        })

        expect(OperationFailureException.class) {
            encryptFacade.encrypt(fakeName)
        }

        //test default decrypt expect return original
        assert fakeName == encryptFacade.decrypt(fakeName)

        infoSecSimulator.clearConnectHandler()

        //test resource pool is null expect fail
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue("123")
        expect(OperationFailureException.class) {
            encryptFacade.encrypt(fakeName)
        }

        infoSecSimulator.clearSm4DecryptHandler()
        infoSecSimulator.clearSm4EncryptHandler()
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(infoPool.getUuid())

        //test the algType of encryption and decryption or the data is empty expect fail
        assert encryptFacade.encrypt("", "").error != null
        assert encryptFacade.encrypt(fakeModel, "").error != null
        assert encryptFacade.encrypt("", fakeModel).error != null
        assert encryptFacade.decrypt("", "").error != null
        assert encryptFacade.decrypt(fakeModel, "").error != null
        assert encryptFacade.decrypt("", fakeModel).error != null

        //test the algType of encryption and decryption does not exist expect fail
        assert encryptFacade.encrypt(fakeName, fakeModel).error != null
        assert encryptFacade.decrypt(fakeName, fakeModel).error != null

        //test digest or digestLocal or hmac expect success
        infoSecSimulator.installDigestHandler({ text ->
            return ErrorableValue.of(EncryptType.DIGEST.toString())
        })
        infoSecSimulator.installDigestLocalHandler({ text ->
            return ErrorableValue.of(EncryptType.DIGEST_LOCAL.toString())
        })
        infoSecSimulator.installHmacHandler({ text ->
            return ErrorableValue.of(EncryptType.HMAC.toString())
        })

        assert encryptFacade.encrypt(fakeName, EncryptType.DIGEST.toString()).result == EncryptType.DIGEST.toString()
        assert encryptFacade.encrypt(fakeName, EncryptType.DIGEST_LOCAL.toString()).result == EncryptType.DIGEST_LOCAL.toString()
        assert encryptFacade.encrypt(fakeName, EncryptType.HMAC.toString()).result == EncryptType.HMAC.toString()

        infoSecSimulator.clearDigestHandler()
        infoSecSimulator.clearDigestLocalHandler()
        infoSecSimulator.clearHmacHandler()

        EncryptGlobalConfig.ENCRYPT_DRIVER.updateValue(EncryptConstant.DEFAULT)
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.getDefaultValue())
    }

    void testTrackerIfNodeJoinOrLeft() {
        DatabaseFacade dbf = bean(DatabaseFacade.class)
        SecurityMachineTrackerImpl tracker = bean(SecurityMachineTrackerImpl.class)
        boolean heartbeat = false
        InfoSecSecurityMachineInventory sm1 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = infoPool.getUuid()
            name = "trackerSm1"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '*********'
            password = 'password'
            port = 50000
        }

        def cleanup = notifyWhenReceivedMessage(SecurityMachineHeartbeatDetectionMsg.class) { SecurityMachineHeartbeatDetectionMsg msg ->
            heartbeat = true
        }

        retryInSecs {
            assert heartbeat
        }

        tracker.untrackSecurityMachine(sm1.getUuid())

        sleep(1000)
        heartbeat = false

        sleep(2000)
        assert !heartbeat

        ManagementNodeVO vo = new ManagementNodeVO(
                hostName: "127.0.0.10",
                // mock a future heartbeat
                heartBeat: new Date(System.currentTimeMillis() + TimeUnit.HOURS.toMillis(1)).toTimestamp(),
                uuid: sm1.getUuid(),
                port: 8989,
                state: ManagementNodeState.RUNNING
        )

        tracker.nodeJoin(ManagementNodeInventory.valueOf(vo))

        retryInSecs {
            assert heartbeat
        }

        tracker.untrackSecurityMachine(sm1.getUuid())
        sleep(1000)
        heartbeat = false
        sleep(2000)
        assert !heartbeat

        tracker.nodeLeft(ManagementNodeInventory.valueOf(vo))

        retryInSecs {
            assert heartbeat
        }

        deleteSecurityMachine {
            uuid = sm1.getUuid()
        }

        cleanup()
    }

    void testBatchDeleteSecurityMachine() {
        CryptoAuthenticationGlobalConfig.ENABLE_CCS_CERTIFICATE_AUTH_LOGIN.updateValue(true)
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.updateValue(infoPool.getUuid())
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(infoPool.getUuid())

        def list = []
        InfoSecSecurityMachineInventory sm1 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = infoPool.getUuid()
            name = "batchSm1"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '*********'
            password = 'password'
            port = 50000
        }

        InfoSecSecurityMachineInventory sm2 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = infoPool.getUuid()
            name = "batchSm2"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '*********'
            password = 'password'
            port = 50000
        }

        setSecurityMachineKey {
            uuid = infoPool.uuid
            type = SecurityMachineKeyType.Active.toString()
            tokenName = "TestSM4"
        }

        retryInSecs {
            InfoSecSecurityMachineInventory syncSm1 = querySecurityMachine {
                conditions = ["uuid=${sm1.uuid}"]
            }[0] as InfoSecSecurityMachineInventory
            InfoSecSecurityMachineInventory syncSm2 = querySecurityMachine {
                conditions = ["uuid=${sm2.uuid}"]
            }[0] as InfoSecSecurityMachineInventory
            assert syncSm1.getStatus() == SecurityMachineStatus.Synced.toString()
            assert syncSm2.getStatus() == SecurityMachineStatus.Synced.toString()
        }

        def thread1 = Thread.start {
            deleteSecurityMachine {
                uuid = sm1.getUuid()
            }
        }

        def thread2 = Thread.start {
            deleteSecurityMachine {
                uuid = sm2.getUuid()
            }
        }
        list.add(thread1)
        list.add(thread2)
        list.each { it.join() }

        List<InfoSecSecurityMachineInventory> batchResults = querySecurityMachine {
        } as List<InfoSecSecurityMachineInventory>
        assert batchResults.size() == 1

        CryptoAuthenticationGlobalConfig.ENABLE_CCS_CERTIFICATE_AUTH_LOGIN.updateValue(false)
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.getDefaultValue())
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.getDefaultValue())

        //test delete SecurityMachine expect fail
        env.message(SecurityMachineDeletionMsg.class) { SecurityMachineDeletionMsg msg, CloudBus bus ->
            def reply = new MessageReply()
            reply.setError(operr(failDeleteError))
            bus.reply(msg, reply)
        }

        DeleteSecurityMachineAction failDeleteAction = new DeleteSecurityMachineAction()
        failDeleteAction.uuid = batchResults.get(0).getUuid()
        failDeleteAction.sessionId = adminSession()
        assert failDeleteAction.call().error.getDetails().contains(failDeleteError)

        env.revokeMessage(SecurityMachineDeletionMsg.class, null)
        deleteSecurityMachine {
            uuid = batchResults.get(0).getUuid()
        }
    }

    void testSecurityMachineHeartbeatDetect() {
        InfoSecSecurityMachineInventory detectSm = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = infoPool.getUuid()
            name = "testsm"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '127.0.0.1'
            password = 'password'
            port = 50000
        }
        AtomicInteger count = new AtomicInteger(0)
        def stateChange = false
        EventFacade evtf = bean(EventFacadeImpl.class)
        evtf.on(SecurityMachineCanonicalEvents.SECURITY_MACHINE_STATE_CHANGE_PATH, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                SecurityMachineCanonicalEvents.SecurityMachineStateChangedData d = (SecurityMachineCanonicalEvents.SecurityMachineStateChangedData) data
                if (d.securityMachineUuid == detectSm.uuid && d.newState == SecurityMachineState.Exception.toString()) {
                    stateChange = true
                }
            }
        })

        def fetch = false
        Map data = null
        env.simulator(BASE_URL) { HttpEntity<String> e ->
            data = json(e.body, LinkedHashMap.class)
            if (data[AbstractTextTemplate.PARAM_EVENT_RESOURCE_NAME] == detectSm.getName()) {
                fetch = true
            }
            logger.debug("received an event ${e.body}")
        }

        def cleanup = notifyWhenReceivedMessage(SecurityMachineHeartbeatDetectionMsg.class) { SecurityMachineHeartbeatDetectionMsg msg ->
            if (msg.getSecurityMachineUuid() == detectSm.uuid) {
                count.incrementAndGet()
            }
        }

        retryInSecs {
            assert count.get() > 0
            assert !stateChange
            assert !fetch
        }

        infoSecSimulator.installSingleConnectHandler({ ip, port ->
            return ErrorableValue.ofErrorCode(operr(connectError))
        })

        retryInSecs {
            InfoSecSecurityMachineInventory querySm = querySecurityMachine {
                conditions = ["uuid=${detectSm.uuid}"]
            }[0] as InfoSecSecurityMachineInventory
            assert querySm.getState() == SecurityMachineState.Exception.toString()
            assert stateChange
            assert fetch
        }

        def heartbeatRes = null

        count.set(0)
        stateChange = false
        fetch = false
        retryInSecs {
            assert count.get() > 0
            InfoSecSecurityMachineInventory querySm = querySecurityMachine {
                conditions = ["uuid=${detectSm.uuid}"]
            }[0] as InfoSecSecurityMachineInventory
            assert querySm.getState() == SecurityMachineState.Exception.toString()
            assert !stateChange
            assert !fetch
        }

        //test changeStateCanBeOperated expect fail
        ChangeSecurityMachineStateAction exceptionAction = new ChangeSecurityMachineStateAction();
        exceptionAction.uuid = detectSm.uuid
        exceptionAction.stateEvent = SecurityMachineStateEvent.disable.toString()
        exceptionAction.sessionId = adminSession()
        ChangeSecurityMachineStateAction.Result errorChangeStateRes = exceptionAction.call()
        assert errorChangeStateRes.error.getCode() == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        infoSecSimulator.clearSingleConnectHandler()

        retryInSecs {
            InfoSecSecurityMachineInventory successSm = querySecurityMachine {
                conditions = ["uuid=${detectSm.uuid}"]
            }[0] as InfoSecSecurityMachineInventory
            assert successSm.getState() == SecurityMachineState.Enabled.toString()
        }

        changeSecurityMachineState {
            uuid = detectSm.uuid
            stateEvent = SecurityMachineStateEvent.disable.toString()
        }
        InfoSecSecurityMachineInventory disableSm = querySecurityMachine {
            conditions = ["uuid=${detectSm.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert disableSm.getState() == SecurityMachineState.Disabled.toString()

        changeSecurityMachineState {
            uuid = detectSm.uuid
            stateEvent = SecurityMachineStateEvent.disable.toString()
        }
        disableSm = querySecurityMachine {
            conditions = ["uuid=${detectSm.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert disableSm.getState() == SecurityMachineState.Disabled.toString()

        count.set(0)

        sleep(2000)
        retryInSecs {
            assert count.get() == 0
        }

        //the msg is sent by the internal system, so it is directly constructed and sent
        SecurityMachineHeartbeatDetectionMsg hMsg = new SecurityMachineHeartbeatDetectionMsg()
        hMsg.setSecurityMachineUuid(detectSm.getUuid())
        bus.makeLocalServiceId(hMsg, SecurityMachineConstant.SERVICE_ID)
        bus.send(hMsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
                heartbeatRes = reply.getError()
            }
        })

        retryInSecs {
            assert heartbeatRes != null
        }

        changeSecurityMachineState {
            uuid = detectSm.uuid
            stateEvent = SecurityMachineStateEvent.enable.toString()
        }

        def SecurityMachineHeartbeatDetectionReply heartbeatReply = null
        hMsg = new SecurityMachineHeartbeatDetectionMsg()
        hMsg.setSecurityMachineUuid(detectSm.getUuid())
        bus.makeLocalServiceId(hMsg, SecurityMachineConstant.SERVICE_ID)
        bus.send(hMsg, new CloudBusCallBack(null) {
            @Override
            void run(MessageReply reply) {
                heartbeatReply = (SecurityMachineHeartbeatDetectionReply) reply.castReply()
            }
        })

        retryInSecs {
            assert heartbeatReply.getCurrentSecurityMachineState() == SecurityMachineState.Enabled.toString()
            assert heartbeatReply.isEnabled()
        }

        count.set(0)
        InfoSecSecurityMachineInventory enableSm = querySecurityMachine {
            conditions = ["uuid=${detectSm.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert enableSm.getState() == SecurityMachineState.Enabled.toString()
        retryInSecs {
            assert count.get() > 0
        }

        changeSecurityMachineState {
            uuid = detectSm.uuid
            stateEvent = SecurityMachineStateEvent.enable.toString()
        }
        enableSm = querySecurityMachine {
            conditions = ["uuid=${detectSm.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert enableSm.getState() == SecurityMachineState.Enabled.toString()

        deleteSecurityMachine {
            uuid = detectSm.uuid
        }

        cleanup()
    }

    AddInfoSecSecurityMachineAction addInfoSecSecurityMachine(String zoneUuid, String secretResourcePoolUuid, String name, String model, String type, String managementIp, int port, String password) {
        AddInfoSecSecurityMachineAction action = new AddInfoSecSecurityMachineAction()
        action.zoneUuid = zoneUuid
        action.secretResourcePoolUuid = secretResourcePoolUuid
        action.name = name
        action.model = model
        action.type = type
        action.managementIp = managementIp
        action.password = password
        action.port = port
        action.sessionId = adminSession()
        return action
    }

    void testSecurityMachineLifeCycle() {
        infoPool = createInfoSecSecretResourcePool {
            zoneUuid = zoneInv.getUuid()
            name = "test"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            connectionMode = 2
            heartbeatInterval = 6
        }

        InfoSecSecretResourcePoolInventory infoPool2 = createInfoSecSecretResourcePool {
            zoneUuid = zoneInv.getUuid()
            name = "test2"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            connectionMode = 2
            heartbeatInterval = 6
        }

        SQL.New(SecretResourcePoolVO.class)
                .eq(SecretResourcePoolVO_.uuid, infoPool.uuid)
                .set(SecretResourcePoolVO_.heartbeatInterval, 1).update()

        expect(AssertionError.class) {
            addInfoSecSecurityMachine {
                zoneUuid = zoneInv.getUuid()
                secretResourcePoolUuid = Platform.uuid
                name = "testsm"
                model = "InfoSec"
                type = "CloudSecurityMachine"
                managementIp = '127.0.0.1'
                password = 'password'
                port = 50000
            }
        }

        clientFactory.installTestConnectionHandler({ msg ->
            return operr(testConnectError);
        })

        AddInfoSecSecurityMachineAction testConnectAction = addInfoSecSecurityMachine(zoneInv.getUuid()
                , infoPool.getUuid(), "testConnect", "InfoSec", "CloudSecurityMachine"
                , '127.0.0.1', 50000, 'password')
        AddInfoSecSecurityMachineAction.Result testConnectFailRes = testConnectAction.call()

        assert testConnectFailRes.error.getDetails().contains(testConnectError)
        clientFactory.clearTestConnectionHandler()

        infoSecSimulator.installSingleConnectHandler({ ip, port ->
            return ErrorableValue.ofErrorCode(operr(connectError))
        })

        AddInfoSecSecurityMachineAction singleConnectAction = addInfoSecSecurityMachine(zoneInv.getUuid()
                , infoPool.getUuid(), "testSingleConnect", "InfoSec", "CloudSecurityMachine"
                , '127.0.0.1', 50000, 'password')
        AddInfoSecSecurityMachineAction.Result singleConnectFailRes = singleConnectAction.call()

        assert singleConnectFailRes.error.getDetails().contains(connectError)
        infoSecSimulator.clearSingleConnectHandler()

        AddInfoSecSecurityMachineAction connectAction = addInfoSecSecurityMachine(zoneInv.getUuid()
                , infoPool.getUuid(), "testsm", "InfoSec", "CloudSecurityMachine"
                , '1', 50000, 'password')
        AddInfoSecSecurityMachineAction.Result connectRes = connectAction.call()
        assert connectRes.error != null
        assert connectRes.error.getCode() == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        //test get SecurityMachineClientFactory is null expect fail
        expect(AssertionError.class) {
            addInfoSecSecurityMachine {
                zoneUuid = zoneInv.getUuid()
                secretResourcePoolUuid = infoPool.getUuid()
                name = "fakeModel"
                model = fakeModel
                type = "CloudSecurityMachine"
                managementIp = '127.0.0.1'
                password = 'password'
                port = 50000
            }
        }

        InfoSecSecurityMachineInventory infoSm = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = infoPool.getUuid()
            name = "testsm"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '127.0.0.1'
            password = 'password'
            port = 50000
        }

        expect(AssertionError.class) {
            addInfoSecSecurityMachine {
                zoneUuid = zoneInv.getUuid()
                secretResourcePoolUuid = infoPool.getUuid()
                name = "testsm"
                model = "InfoSec"
                type = "CloudSecurityMachine"
                managementIp = '127.0.0.1'
                password = 'password'
                port = 50000
            }
        }

        List<InfoSecSecurityMachineInventory> results = querySecurityMachine {
        } as List<InfoSecSecurityMachineInventory>

        assert results.size() == 1
        InfoSecSecurityMachineInventory queryResult = results.get(0)
        assert queryResult.name == infoSm.getName()
        assert queryResult.model == InfoSecSecurityMachineConstant.SECURITY_MACHINE_TYPE
        assert queryResult.type == infoSm.getType()
        assert queryResult.state == SecurityMachineState.Enabled.toString()
        assert queryResult.status == SecurityMachineStatus.Unsynced.toString()
        assert queryResult.getPort() == infoSm.getPort()
        assert queryResult.port == 50000

        updateSecurityMachine {
            uuid = infoSm.uuid
            name = fakeName
            description = dec
            model = fakeModel
        }

        InfoSecSecurityMachineInventory updateResult = querySecurityMachine {
        }[0]

        assert updateResult.getName() == fakeName
        assert updateResult.getDescription() == dec
        assert updateResult.getModel() == fakeModel

        expect(AssertionError.class) {
            updateSecurityMachine {
                uuid = infoSm.uuid
                model = "InfoSec"
            }
        }
        SQL.New(SecurityMachineVO.class).eq(SecurityMachineVO_.uuid, infoSm.uuid).set(SecurityMachineVO_.model, "InfoSec").update()

        updateInfoSecSecurityMachine {
            uuid = infoSm.uuid
            port = 10000
        }

        updateInfoSecSecurityMachine {
            uuid = infoSm.uuid
            password = fakeName
        }

        retryInSecs {
            InfoSecSecurityMachineInventory updateInfoResult = querySecurityMachine {
            }[0]
            assert updateInfoResult.getPort() == 10000
            assert Q.New(InfoSecSecurityMachineVO.class)
                    .eq(InfoSecSecurityMachineVO_.uuid, infoSm.uuid)
                    .select(InfoSecSecurityMachineVO_.password).findValue() == fakeName
        }

        changeSecurityMachineState {
            uuid = infoSm.uuid
            stateEvent = SecurityMachineStateEvent.disable.toString()
        }

        String state = Q.New(SecurityMachineVO.class).select(SecurityMachineVO_.state)
                .eq(SecurityMachineVO_.uuid, infoSm.getUuid()).findValue()

        assert state == SecurityMachineState.Disabled.toString()

        changeSecurityMachineState {
            uuid = infoSm.uuid
            stateEvent = SecurityMachineStateEvent.enable.toString()
        }

        def enableResult = querySecurityMachine {
            conditions = ["uuid=${infoSm.uuid}"]
        }[0] as InfoSecSecurityMachineInventory

        assert enableResult.getState() == SecurityMachineState.Enabled.toString()

        CryptoAuthenticationGlobalConfig.ENABLE_CCS_CERTIFICATE_AUTH_LOGIN.updateValue(true)

        //test pool uuid is no set expect fail
        ChangeSecurityMachineStateAction failAction = new ChangeSecurityMachineStateAction();
        failAction.uuid = infoSm.uuid
        failAction.stateEvent = SecurityMachineStateEvent.disable.toString()
        failAction.sessionId = adminSession()
        ChangeSecurityMachineStateAction.Result failRes = failAction.call()
        assert failRes.error.getCode() == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.updateValue(infoPool.getUuid())

        //test checkCryptoFunctionSetupTheResourcePool expect fail
        failAction = new ChangeSecurityMachineStateAction();
        failAction.uuid = infoSm.uuid
        failAction.stateEvent = SecurityMachineStateEvent.disable.toString()
        failAction.sessionId = adminSession()
        failRes = failAction.call()
        assert failRes.error.getCode() == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        expect(AssertionError.class) {
            deleteSecurityMachine {
                uuid = infoSm.getUuid()
            }
        }

        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(infoPool.getUuid())

        InfoSecSecurityMachineInventory infoSm2 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = infoPool2.getUuid()
            name = "testsm2"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '*********'
            password = 'password'
            port = 50000
        }

        changeSecurityMachineState {
            uuid = infoSm2.uuid
            stateEvent = SecurityMachineStateEvent.disable.toString()
        }

        def otherRes = querySecurityMachine {
            conditions = ["uuid=${infoSm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory

        assert otherRes.getState() == SecurityMachineState.Disabled.toString()

        changeSecurityMachineState {
            uuid = infoSm2.uuid
            stateEvent = SecurityMachineStateEvent.enable.toString()
        }

        otherRes = querySecurityMachine {
            conditions = ["uuid=${infoSm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory

        assert otherRes.getState() == SecurityMachineState.Enabled.toString()

        deleteSecurityMachine {
            uuid = infoSm2.getUuid()
        }

        deleteSecretResourcePool {
            uuid = infoPool2.getUuid()
        }

        expect(AssertionError.class) {
            deleteSecurityMachine {
                uuid = infoSm.getUuid()
            }
        }


        updateInfoSecSecretResourcePool {
            uuid = infoPool.getUuid()
            name = fakeName
            activatedToken = "activatedToken"
        }

        ChangeSecurityMachineStateAction errorChangeStateAction = new ChangeSecurityMachineStateAction();
        errorChangeStateAction.uuid = infoSm.uuid
        errorChangeStateAction.stateEvent = SecurityMachineStateEvent.disable.toString()
        errorChangeStateAction.sessionId = adminSession()
        ChangeSecurityMachineStateAction.Result errorChangeStateRes = errorChangeStateAction.call()
        assert errorChangeStateRes.error.code == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        AddInfoSecSecurityMachineAction syncSecurityMachineAction = addInfoSecSecurityMachine(zoneInv.getUuid()
                , infoPool.getUuid(), "syncSecurityMachine", "InfoSec", "CloudSecurityMachine"
                , '*********', 50000, 'password')
        AddInfoSecSecurityMachineAction.Result syncSecurityMachineRes = syncSecurityMachineAction.call()
        assert syncSecurityMachineRes.value.getInventory().status == SecurityMachineStatus.Synced.toString()
        assert syncSecurityMachineRes.value.getInventory().state == SecurityMachineState.Enabled.toString()

        infoSecSimulator.installKeyExistHandler({ keyName ->
            return ErrorableValue.of(false)
        })

        AddInfoSecSecurityMachineAction unsyncSecurityMachineAction = addInfoSecSecurityMachine(zoneInv.getUuid()
                , infoPool.getUuid(), "unsyncSecurityMachine", "InfoSec", "CloudSecurityMachine"
                , '*********', 50000, 'password')
        AddInfoSecSecurityMachineAction.Result unsyncSecurityMachineRes = unsyncSecurityMachineAction.call()
        assert unsyncSecurityMachineRes.value.getInventory().status == SecurityMachineStatus.Unsynced.toString()
        assert unsyncSecurityMachineRes.value.getInventory().state == SecurityMachineState.Enabled.toString()

        infoSecSimulator.clearKeyExistHandler()

        CryptoAuthenticationGlobalConfig.ENABLE_CCS_CERTIFICATE_AUTH_LOGIN.updateValue(false)
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.getDefaultValue())
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.getDefaultValue())

        deleteSecurityMachine {
            uuid = syncSecurityMachineRes.value.getInventory().uuid
        }

        List<InfoSecSecurityMachineInventory> securityMachineResults = querySecurityMachine {
        } as List<InfoSecSecurityMachineInventory>
        assert securityMachineResults.size() == 2

        setSecurityMachineKey {
            uuid = infoPool.uuid
            type = SecurityMachineKeyType.Active.toString()
            tokenName = "TestSM4"
        }

        retryInSecs {
            InfoSecSecretResourcePoolInventory activateRes = querySecretResourcePool {
                conditions = ["uuid=${infoPool.uuid}"]
            }[0] as InfoSecSecretResourcePoolInventory
            assert activateRes.getName() == fakeName
            assert activateRes.getState() == SecretResourcePoolState.Activated.toString()
        }

        deleteSecurityMachine {
            uuid = unsyncSecurityMachineRes.value.getInventory().uuid
        }

        retryInSecs {
            List<InfoSecSecurityMachineInventory> unchangeResults = querySecurityMachine {
            } as List<InfoSecSecurityMachineInventory>

            assert unchangeResults.size() == 1

            InfoSecSecretResourcePoolInventory unchangeRes = querySecretResourcePool {
                conditions = ["uuid=${infoPool.uuid}"]
            }[0] as InfoSecSecretResourcePoolInventory
            assert unchangeRes.getState() == SecretResourcePoolState.Activated.toString()
        }

        deleteSecurityMachine {
            uuid = infoSm.getUuid()
        }

        retryInSecs {
            InfoSecSecretResourcePoolInventory unactivatedRes = querySecretResourcePool {
                conditions = ["uuid=${infoPool.uuid}"]
            }[0] as InfoSecSecretResourcePoolInventory
            assert unactivatedRes.getState() == SecretResourcePoolState.Unactivated.toString()
            assert unactivatedRes.getActivatedToken() == null
            assert unactivatedRes.getProtectToken() == null
            assert unactivatedRes.getHmacToken() == null
        }

        List<InfoSecSecurityMachineInventory> deleteResults = querySecurityMachine {
        } as List<InfoSecSecurityMachineInventory>

        assert deleteResults.isEmpty()
    }

    SetSecurityMachineKeyAction setTokenForSecretResourcePool(String uuid, String type, String tokenName) {
        SetSecurityMachineKeyAction action = new SetSecurityMachineKeyAction()
        action.uuid = uuid
        action.type = type
        action.tokenName = tokenName
        action.sessionId = adminSession()
        return action
    }

    void testSecretResourcePoolSetKey() {
        String testToken = "TestSM4"
        String customToken = "Test"
        String dataProtectToken = "ConfidentialitySM4"
        String hmacToken = "IntegrityHmacSM3"

        InfoSecSecretResourcePoolInventory setKeyInfo = createInfoSecSecretResourcePool {
            zoneUuid = zoneInv.getUuid()
            name = "test"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            connectionMode = 2
            heartbeatInterval = 6
        }

        InfoSecSecretResourcePoolInventory poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getActivatedToken() == null
        assert poolRes.getProtectToken() == null
        assert poolRes.getHmacToken() == null

        SetSecurityMachineKeyAction failAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Active.toString(), testToken)
        assert failAction.call().error.code == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        InfoSecSecurityMachineInventory sm1 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = setKeyInfo.getUuid()
            name = "testSetkey1"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '127.0.0.1'
            password = 'password'
            port = 50000
        }

        InfoSecSecurityMachineInventory smRes1 = querySecurityMachine {
            conditions = ["uuid=${sm1.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert smRes1.getState() == SecurityMachineState.Enabled.toString()
        assert smRes1.getStatus() == SecurityMachineStatus.Unsynced.toString()

        InfoSecSecurityMachineInventory sm2 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = setKeyInfo.getUuid()
            name = "testSetkey2"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '*********'
            password = 'password'
            port = 50000
        }

        InfoSecSecurityMachineInventory smRes2 = querySecurityMachine {
            conditions = ["uuid=${sm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert smRes2.getState() == SecurityMachineState.Enabled.toString()
        assert smRes2.getStatus() == SecurityMachineStatus.Unsynced.toString()

        SetSecurityMachineKeyAction tokenNameFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, "error type", testToken)
        assert tokenNameFailAction.call().error.code == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        infoSecSimulator.installSingleConnectHandler({ ip, port ->
            return ErrorableValue.ofErrorCode(operr(connectError))
        })
        SetSecurityMachineKeyAction generateTokenFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Active.toString(), testToken)
        assert generateTokenFailAction.call().error.details.contains(connectError)
        infoSecSimulator.clearSingleConnectHandler()

        infoSecSimulator.installGenerateSm4Token({ tokenName ->
            return ErrorableValue.ofErrorCode(operr(generateSm4TokenError))
        })
        SetSecurityMachineKeyAction generateSm4TokenFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Active.toString(), testToken)
        assert generateSm4TokenFailAction.call().error.details.contains(generateSm4TokenError)
        infoSecSimulator.clearGenerateSm4Token()

        infoSecSimulator.installGenerateDataProtectTokenHandler({ tokenName ->
            return ErrorableValue.ofErrorCode(operr(generateProtectTokenError))
        })
        SetSecurityMachineKeyAction generateProtectTokenFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Active.toString(), testToken)
        assert generateProtectTokenFailAction.call().error.details.contains(generateProtectTokenError)
        infoSecSimulator.clearGenerateDataProtectTokenHandler()

        infoSecSimulator.installGenerateHmacToken({ tokenName ->
            return ErrorableValue.ofErrorCode(operr(generateHmacTokenError))
        })
        SetSecurityMachineKeyAction generateHmacTokenFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Active.toString(), testToken)
        assert generateHmacTokenFailAction.call().error.details.contains(generateHmacTokenError)
        infoSecSimulator.clearGenerateHmacToken()


        //activate pool Success
        setSecurityMachineKey {
            uuid = setKeyInfo.uuid
            type = SecurityMachineKeyType.Active.toString()
            tokenName = testToken
        }

        InfoSecSecurityMachineInventory syncSm1 = querySecurityMachine {
            conditions = ["uuid=${sm1.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        InfoSecSecurityMachineInventory syncSm2 = querySecurityMachine {
            conditions = ["uuid=${sm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert syncSm1.getStatus() == SecurityMachineStatus.Synced.toString()
        assert syncSm2.getStatus() == SecurityMachineStatus.Synced.toString()

        poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getState() == SecretResourcePoolState.Activated.toString()
        assert poolRes.getActivatedToken() == testToken
        assert poolRes.getProtectToken() == SecretResourcePool.buildDataProtectToken(setKeyInfo.uuid)
        assert poolRes.getHmacToken() == SecretResourcePool.buildHmacToken(setKeyInfo.uuid)

        SQL.New(SecurityMachineVO.class).eq(SecurityMachineVO_.uuid, sm1.uuid).set(SecurityMachineVO_.status, SecurityMachineStatus.Unsynced).update()
        SQL.New(SecurityMachineVO.class).eq(SecurityMachineVO_.uuid, sm2.uuid).set(SecurityMachineVO_.status, SecurityMachineStatus.Unsynced).update()
        SQL.New(SecretResourcePoolVO.class).eq(SecretResourcePoolVO_.uuid, setKeyInfo.uuid).set(SecretResourcePoolVO_.state, SecretResourcePoolState.Unactivated).update()

        //test Manually detect synchronization expect success
        int count = 0
        infoSecSimulator.installKeyExistHandler({ keyName ->
            count++
            if (count % 2 == 0) {
                return ErrorableValue.of(false)
            }
            return ErrorableValue.of(true)
        })
        //if manual detection fails, the status will not be modified
        SetSecurityMachineKeyAction detectAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Active.toString(), testToken)
        assert detectAction.call().value.inventories.size() == 1

        smRes1 = querySecurityMachine {
            conditions = ["uuid=${sm1.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        smRes1 = querySecurityMachine {
            conditions = ["uuid=${sm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert smRes1.getStatus() == SecurityMachineStatus.Unsynced.toString()
        assert smRes1.getStatus() == SecurityMachineStatus.Unsynced.toString()

        infoSecSimulator.installKeyExistHandler({ keyName ->
            return ErrorableValue.of(true)
        })

        //only if the manual detection is all successful, the status will be modified
        detectAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Active.toString(), testToken)
        assert detectAction.call().value.inventories.size() == 0
        smRes1 = querySecurityMachine {
            conditions = ["uuid=${sm1.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        smRes1 = querySecurityMachine {
            conditions = ["uuid=${sm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert smRes1.getStatus() == SecurityMachineStatus.Synced.toString()
        assert smRes1.getStatus() == SecurityMachineStatus.Synced.toString()
        poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getState() == SecretResourcePoolState.Activated.toString()

        SQL.New(SecurityMachineVO.class).eq(SecurityMachineVO_.uuid, sm1.uuid).set(SecurityMachineVO_.status, SecurityMachineStatus.Unsynced).update()
        SQL.New(SecurityMachineVO.class).eq(SecurityMachineVO_.uuid, sm2.uuid).set(SecurityMachineVO_.status, SecurityMachineStatus.Unsynced).update()
        SQL.New(SecretResourcePoolVO.class).eq(SecretResourcePoolVO_.uuid, setKeyInfo.uuid).set(SecretResourcePoolVO_.state, SecretResourcePoolState.Unactivated).update()

        //test check-all-security-machine-token-exist
        SetSecurityMachineKeyAction checkHmacTokenSuccessAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Hmac.toString(), hmacToken)
        assert checkHmacTokenSuccessAction.call().value.inventories.size() == 0
        SetSecurityMachineKeyAction checkProtectTokenSuccessAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Protect.toString(), dataProtectToken)
        assert checkProtectTokenSuccessAction.call().value.inventories.size() == 0

        //test hmac and protect to detect synchronization, regardless of success or failure,
        // will not modify the state of the cryptographic machine and resource pool
        InfoSecSecurityMachineInventory unsyncSm1 = querySecurityMachine {
            conditions = ["uuid=${sm1.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        InfoSecSecurityMachineInventory unsyncSm2 = querySecurityMachine {
            conditions = ["uuid=${sm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert unsyncSm1.getStatus() == SecurityMachineStatus.Unsynced.toString()
        assert unsyncSm2.getStatus() == SecurityMachineStatus.Unsynced.toString()
        poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getState() == SecretResourcePoolState.Unactivated.toString()

        SQL.New(SecretResourcePoolVO.class).eq(SecretResourcePoolVO_.uuid, setKeyInfo.uuid).set(SecretResourcePoolVO_.state, SecretResourcePoolState.Activated).update()

        infoSecSimulator.installSingleConnectHandler({ ip, port ->
            return ErrorableValue.ofErrorCode(operr(connectError))
        })
        SetSecurityMachineKeyAction checkHmacTokenConnectFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Hmac.toString(), hmacToken)
        assert checkHmacTokenConnectFailAction.call().value.inventories.size() == 2
        assert checkHmacTokenConnectFailAction.call().value.inventories.first().status == SecurityMachineStatus.Unsynced.toString()
        SetSecurityMachineKeyAction checkProtectTokenConnectFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Protect.toString(), dataProtectToken)
        assert checkProtectTokenConnectFailAction.call().value.inventories.size() == 2
        assert checkProtectTokenConnectFailAction.call().value.inventories.first().status == SecurityMachineStatus.Unsynced.toString()
        infoSecSimulator.clearSingleConnectHandler()

        infoSecSimulator.installKeyExistHandler({ keyName ->
            return ErrorableValue.of(false)
        })
        SetSecurityMachineKeyAction checkHmacTokenExistFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Hmac.toString(), hmacToken)
        assert checkHmacTokenExistFailAction.call().value.inventories.size() == 2
        SetSecurityMachineKeyAction checkProtectTokenExistFailAction = setTokenForSecretResourcePool(setKeyInfo.uuid, SecurityMachineKeyType.Protect.toString(), dataProtectToken)
        assert checkProtectTokenExistFailAction.call().value.inventories.size() == 2
        infoSecSimulator.clearKeyExistHandler()

        //the test key exists, modify the key name, and then click OK again
        setSecurityMachineKey {
            uuid = setKeyInfo.uuid
            type = SecurityMachineKeyType.Active.toString()
            tokenName = customToken
        }

        unsyncSm1 = querySecurityMachine {
            conditions = ["uuid=${sm1.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        unsyncSm2 = querySecurityMachine {
            conditions = ["uuid=${sm2.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert unsyncSm1.getStatus() == SecurityMachineStatus.Synced.toString()
        assert unsyncSm2.getStatus() == SecurityMachineStatus.Synced.toString()
        poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getState() == SecretResourcePoolState.Activated.toString()
        assert poolRes.getActivatedToken() == customToken

        deleteSecurityMachine {
            uuid = syncSm1.getUuid()
        }

        deleteSecurityMachine {
            uuid = syncSm2.getUuid()
        }

        List<InfoSecSecurityMachineInventory> results = querySecurityMachine {
        } as List<InfoSecSecurityMachineInventory>

        assert results.isEmpty()

        //test first detect sync
        SQL.New(InfoSecSecretResourcePoolVO.class).eq(InfoSecSecretResourcePoolVO_.uuid, setKeyInfo.uuid)
                .set(InfoSecSecretResourcePoolVO_.activatedToken, null)
                .set(InfoSecSecretResourcePoolVO_.protectToken, null)
                .set(InfoSecSecretResourcePoolVO_.hmacToken, null)
                .update()

        poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getState() == SecretResourcePoolState.Unactivated.toString()
        assert poolRes.getActivatedToken() == null
        assert poolRes.getProtectToken() == null
        assert poolRes.getHmacToken() == null

        InfoSecSecurityMachineInventory sm3 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = setKeyInfo.getUuid()
            name = "testSetkey3"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '127.0.0.1'
            password = 'password'
            port = 50000
        }
        InfoSecSecurityMachineInventory sm4 = addInfoSecSecurityMachine {
            zoneUuid = zoneInv.getUuid()
            secretResourcePoolUuid = setKeyInfo.getUuid()
            name = "testSetkey4"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            managementIp = '*********'
            password = 'password'
            port = 50000
        }

        InfoSecSecurityMachineInventory querySm3 = querySecurityMachine {
            conditions = ["uuid=${sm3.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        InfoSecSecurityMachineInventory querySm4 = querySecurityMachine {
            conditions = ["uuid=${sm4.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert querySm3.getStatus() == SecurityMachineStatus.Unsynced.toString()
        assert querySm4.getStatus() == SecurityMachineStatus.Unsynced.toString()

        //Click first to test the connection will not change the status
        setSecurityMachineKey {
            uuid = setKeyInfo.uuid
            type = SecurityMachineKeyType.Active.toString()
            tokenName = testToken
            dryRun = true
        }

        querySm3 = querySecurityMachine {
            conditions = ["uuid=${sm3.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        querySm4 = querySecurityMachine {
            conditions = ["uuid=${sm4.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert querySm3.getStatus() == SecurityMachineStatus.Unsynced.toString()
        assert querySm4.getStatus() == SecurityMachineStatus.Unsynced.toString()

        poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getState() == SecretResourcePoolState.Unactivated.toString()
        assert poolRes.getActivatedToken() == testToken
        assert poolRes.getProtectToken() == SecretResourcePool.buildDataProtectToken(setKeyInfo.uuid)
        assert poolRes.getHmacToken() == SecretResourcePool.buildHmacToken(setKeyInfo.uuid)

        //when you click OK to set the key, the status will be modified.
        //if the tokenName is changed, the key will be regenerated
        setSecurityMachineKey {
            uuid = setKeyInfo.uuid
            type = SecurityMachineKeyType.Active.toString()
            tokenName = customToken
        }

        querySm3 = querySecurityMachine {
            conditions = ["uuid=${sm3.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        querySm4 = querySecurityMachine {
            conditions = ["uuid=${sm4.uuid}"]
        }[0] as InfoSecSecurityMachineInventory
        assert querySm3.getStatus() == SecurityMachineStatus.Synced.toString()
        assert querySm4.getStatus() == SecurityMachineStatus.Synced.toString()

        poolRes = querySecretResourcePool {
            conditions = ["uuid=${setKeyInfo.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory
        assert poolRes.getState() == SecretResourcePoolState.Activated.toString()
        assert poolRes.getActivatedToken() == customToken
        assert poolRes.getProtectToken() == SecretResourcePool.buildDataProtectToken(setKeyInfo.uuid)
        assert poolRes.getHmacToken() == SecretResourcePool.buildHmacToken(setKeyInfo.uuid)

        deleteSecurityMachine {
            uuid = sm3.getUuid()
        }

        deleteSecurityMachine {
            uuid = sm4.getUuid()
        }

        results = querySecurityMachine {
        } as List<InfoSecSecurityMachineInventory>
        assert results.isEmpty()
    }

    void testSecretResourcePoolLifeCycle() {
        expect(AssertionError.class) {
            createInfoSecSecretResourcePool {
                zoneUuid = zoneInv.getUuid()
                name = "test"
                model = "error_model"
                type = "CloudSecurityMachine"
                heartbeatInterval = 6
                connectionMode = 2
            }
        }

        expect(ApiException.class) {
            createInfoSecSecretResourcePool {
                zoneUuid = zoneInv.getUuid()
                name = "test"
                model = "InfoSec"
                type = "CloudSecurityMachine"
                connectionMode = 2
                heartbeatInterval = 1
            }
        }

        List<InfoSecSecretResourcePoolInventory> interceptResults = querySecretResourcePool {
        } as List<InfoSecSecretResourcePoolInventory>

        assert interceptResults.isEmpty()

        InfoSecSecretResourcePoolInventory infoTest = createInfoSecSecretResourcePool {
            zoneUuid = zoneInv.getUuid()
            name = "test"
            model = "InfoSec"
            type = "CloudSecurityMachine"
            connectionMode = 2
            heartbeatInterval = 6
        }
        List<InfoSecSecretResourcePoolInventory> results = querySecretResourcePool {
        } as List<InfoSecSecretResourcePoolInventory>

        assert results.size() == 1
        InfoSecSecretResourcePoolInventory queryResult = results.get(0)
        assert queryResult.name == infoTest.getName()
        assert queryResult.model == InfoSecSecretResourcePoolConstant.SECRET_RESOURCE_POOL_TYPE
        assert queryResult.type == infoTest.getType()
        assert queryResult.state == SecretResourcePoolState.Unactivated.toString()
        assert queryResult.connectionMode == infoTest.getConnectionMode()
        assert queryResult.getActivatedToken() == null
        assert queryResult.getProtectToken() == null
        assert queryResult.getHmacToken() == null

        expect(ApiException.class) {
            SecretResourcePoolInventory updatePool = updateSecretResourcePool {
                uuid = infoTest.uuid
                heartbeatInterval = 2
            }
        }

        updateSecretResourcePool {
            uuid = infoTest.uuid
            name = fakeName
            heartbeatInterval = 7
            description = dec
            model = fakeModel
        }

        InfoSecSecretResourcePoolInventory updateResult = querySecretResourcePool {
        }[0]

        assert updateResult.getName() == fakeName
        assert updateResult.getHeartbeatInterval() == 7
        assert updateResult.getDescription() == dec
        assert updateResult.getModel() == fakeModel

        expect(AssertionError.class) {
            updateSecretResourcePool {
                uuid = infoTest.uuid
                model = "InfoSec"
            }
        }

        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(infoTest.getUuid())
        infoSecSimulator.installDigestHandler({ text ->
            return ErrorableValue.of(EncryptType.DIGEST.toString())
        })

        expect(AssertionError.class) {
            securityMachineEncrypt{
                algType = EncryptType.DIGEST.toString()
                text = fakeName
                sessionId = adminSession()
            }
        }

        infoSecSimulator.clearDigestHandler()
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.getDefaultValue())

        SQL.New(SecretResourcePoolVO.class).eq(SecretResourcePoolVO_.uuid, infoTest.uuid).set(SecretResourcePoolVO_.model, "InfoSec").update()

        InfoSecSecretResourcePoolInventory updateInfoPool = updateInfoSecSecretResourcePool {
            uuid = infoTest.uuid
            connectionMode = 1
            activatedToken = fakeName
            protectToken = fakeName
            hmacToken = fakeName
        }
        InfoSecSecretResourcePoolInventory updateInfoResult = querySecretResourcePool {
        }[0]

        assert updateInfoResult.getConnectionMode() == updateInfoPool.getConnectionMode()
        assert updateInfoResult.getActivatedToken() == fakeName
        assert updateInfoResult.getProtectToken() == fakeName
        assert updateInfoResult.getHmacToken() == fakeName

        SQL.New(InfoSecSecretResourcePoolVO.class).eq(InfoSecSecretResourcePoolVO_.uuid, infoTest.uuid)
                .set(InfoSecSecretResourcePoolVO_.activatedToken, null)
                .set(InfoSecSecretResourcePoolVO_.protectToken, null)
                .set(InfoSecSecretResourcePoolVO_.hmacToken, null)
                .update()

        changeSecretResourcePoolState {
            uuid = infoTest.uuid
            stateEvent = SecretResourcePoolStateEvent.activate.toString()
        }
        String state = Q.New(SecretResourcePoolVO.class).select(SecretResourcePoolVO_.state)
                .eq(SecretResourcePoolVO_.uuid, infoTest.getUuid()).findValue()
        assert state == SecretResourcePoolState.Activated.toString()

        changeSecretResourcePoolState {
            uuid = infoTest.uuid
            stateEvent = SecretResourcePoolStateEvent.unactivate.toString()
        }
        def unactivateResult = querySecretResourcePool {
            conditions = ["uuid=${infoTest.uuid}"]
        }[0] as InfoSecSecretResourcePoolInventory

        assert unactivateResult.getState() == SecretResourcePoolState.Unactivated.toString()

        CryptoAuthenticationGlobalConfig.ENABLE_CCS_CERTIFICATE_AUTH_LOGIN.updateValue(true)

        //test crypto is enable but poolUuid is not set expect fail
        expect(AssertionError.class) {
            deleteSecretResourcePool {
                uuid = infoTest.getUuid()
            }
        }

        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.updateValue(infoTest.getUuid())
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(infoTest.getUuid())

        String connectError = "client connect fail"
        infoSecSimulator.installConnectHandler({ uuid ->
            return ErrorableValue.ofErrorCode(operr(connectError))
        })
        //The result of LoginIAM2VirtualIDAction only returns an error code,
        // not specific error information,
        // so verify ZSTAC-44699 by directly calling initSecurityMachineClient


        def expectError = null
        CryptoAuthenticationManagerImpl manager = bean(CryptoAuthenticationManagerImpl.class)
        try {
            manager.initSecurityMachineClient()
        } catch (OperationFailureException expect) {
            expectError = expect.getMessage()
        }
        assert expectError != null
        assert expectError.contains(connectError)
        infoSecSimulator.clearConnectHandler()

        expect(AssertionError.class) {
            deleteSecretResourcePool {
                uuid = infoTest.getUuid()
            }
        }

        CryptoAuthenticationGlobalConfig.ENABLE_CCS_CERTIFICATE_AUTH_LOGIN.updateValue(false)
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.getDefaultValue())
        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.getDefaultValue())

        SecretResourcePoolInventory poolInventory = changeSecretResourcePoolState {
            uuid = infoTest.uuid
            stateEvent = SecretResourcePoolStateEvent.activate.toString()
        }

        retryInSecs {
            assert poolInventory.getState() == SecretResourcePoolState.Activated.toString()
        }

        //test delete SecretResourcePool expect fail
        env.message(SecretResourcePoolDeletionMsg.class) { SecretResourcePoolDeletionMsg msg, CloudBus bus ->
            def reply = new MessageReply()
            reply.setError(operr(failDeleteError))
            bus.reply(msg, reply)
        }

        DeleteSecretResourcePoolAction failDeleteAction = new DeleteSecretResourcePoolAction()
        failDeleteAction.uuid = infoTest.getUuid()
        failDeleteAction.sessionId = adminSession()
        assert failDeleteAction.call().error.getDetails().contains(failDeleteError)

        env.revokeMessage(SecretResourcePoolDeletionMsg.class, null)
        deleteSecretResourcePool {
            uuid = infoTest.getUuid()
        }

        List<InfoSecSecretResourcePoolInventory> deleteResults = querySecretResourcePool {
        } as List<InfoSecSecretResourcePoolInventory>

        assert deleteResults.isEmpty()
    }

    void testSecurityMachineEncrypt() {
        String encryptText = "test password"
        String digestError = EncryptType.DIGEST.toString() + "error"
        String digestLocalError = EncryptType.DIGEST.toString() + "error"
        String sm4Error = EncryptType.SM4.toString() + "error"
        String hmacError = EncryptType.HMAC.toString() + "error"

        // test SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT is not set
        SecurityMachineEncryptAction action = encryptAction(EncryptType.DIGEST.toString(), encryptText)
        assert action.call().error.getCode() == SecurityMachineErrors.CRYPTO_DISABLE_ERROR.toString()

        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(adminSession())

        // test SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT setting error
        action = encryptAction(EncryptType.DIGEST.toString(), encryptText)
        assert action.call().error.getCode() == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(infoPool.getUuid())

        // test parameter input error
        action = encryptAction("error algType", encryptText)
        assert action.call().error.getCode() == SysErrors.INVALID_ARGUMENT_ERROR.toString()

        // test digest encrypt expected success
        infoSecSimulator.installDigestHandler({ text ->
            return ErrorableValue.of(EncryptType.DIGEST.toString())
        })
        action = encryptAction(EncryptType.DIGEST.toString(), encryptText)
        assert action.call().value.text.contains(EncryptType.DIGEST.toString())

        // test digest encrypt expected fail
        infoSecSimulator.installDigestHandler({ text ->
            return ErrorableValue.ofErrorCode(operr(digestError))
        })
        action = encryptAction(EncryptType.DIGEST.toString(), encryptText)
        assert action.call().error.details.contains(digestError)

        infoSecSimulator.clearDigestHandler()

        // test digest_local encrypt expected success
        infoSecSimulator.installDigestLocalHandler({ text ->
            return ErrorableValue.of(EncryptType.DIGEST_LOCAL.toString())
        })
        action = encryptAction(EncryptType.DIGEST_LOCAL.toString(), encryptText)
        assert action.call().value.text.contains(EncryptType.DIGEST_LOCAL.toString())

        // test digest_local encrypt expected fail
        infoSecSimulator.installDigestLocalHandler({ text ->
            return ErrorableValue.ofErrorCode(operr(digestLocalError))
        })
        action = encryptAction(EncryptType.DIGEST_LOCAL.toString(), encryptText)
        assert action.call().error.details.contains(digestLocalError)

        infoSecSimulator.clearDigestLocalHandler()

        // test sm4 encrypt expected success
        infoSecSimulator.installSm4EncryptHandler({ text ->
            return ErrorableValue.of(EncryptType.SM4.toString())
        })
        action = encryptAction(EncryptType.SM4.toString(), encryptText)
        assert action.call().value.text.contains(EncryptType.SM4.toString())

        // test sm4 encrypt expected fail
        infoSecSimulator.installSm4EncryptHandler({ text ->
            return ErrorableValue.ofErrorCode(operr(sm4Error))
        })
        action = encryptAction(EncryptType.SM4.toString(), encryptText)
        assert action.call().error.details.contains(sm4Error)
        infoSecSimulator.clearSm4EncryptHandler()

        // test hmac encrypt expected success
        infoSecSimulator.installHmacHandler({ text ->
            return ErrorableValue.of(EncryptType.HMAC.toString())
        })
        action = encryptAction(EncryptType.HMAC.toString(), encryptText)
        assert action.call().value.text.contains(EncryptType.HMAC.toString())

        // test hmac encrypt expected fail
        infoSecSimulator.installHmacHandler({ text ->
            return ErrorableValue.ofErrorCode(operr(hmacError))
        })
        action = encryptAction(EncryptType.HMAC.toString(), encryptText)
        assert action.call().error.details.contains(hmacError)
        infoSecSimulator.clearHmacHandler()

        SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.updateValue(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.getDefaultValue())
    }

    SecurityMachineEncryptAction encryptAction(String algType, String text) {
        SecurityMachineEncryptAction action = new SecurityMachineEncryptAction()
        action.algType = algType
        action.text = text
        action.sessionId = adminSession()
        return action
    }

    @Override
    void clean() {
        env.delete()
    }
}
