package org.zstack.test.integration.guesttools

import org.springframework.http.HttpEntity
import org.zstack.compute.host.HostSystemTags
import org.zstack.compute.vm.VmSystemTags
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.SQL
import org.zstack.guesttools.GuestToolsConstant
import org.zstack.guesttools.kvm.GuestToolsKvmCommands
import org.zstack.header.errorcode.SysErrors
import org.zstack.header.host.HostVO
import org.zstack.header.image.ImagePlatform
import org.zstack.header.vm.VmCreationStrategy
import org.zstack.header.vm.VmInstanceState
import org.zstack.header.vm.VmInstanceVO
import org.zstack.header.vm.VmInstanceVO_
import org.zstack.header.volume.VolumeType
import org.zstack.header.volume.VolumeVO
import org.zstack.kvm.KVMConstant
import org.zstack.kvm.KVMSystemTags
import org.zstack.mevoco.MevocoSystemTags
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.kvm.KVMAgentCommands.CheckFileOnHostCmd
import org.zstack.kvm.KVMAgentCommands.CheckFileOnHostResponse

/**
 * Created by GuoYi on 2019-09-19.
 */
class AttachGuestToolsIsoToVmCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf
    VmInstanceInventory vm
    VmInstanceInventory vm3

    @Override
    void setup() {
        spring {
            useSpring(GuestToolsTest.springSpec)
        }
    }

    @Override
    void environment() {
        env = GuestToolsEnv.GuestToolsBasicEnv()
    }

    @Override
    void test() {
        env.create {
            initEnv()
            testDownloadGuestToolsIsoExists()
            testAttachGuestToolsIsoToRunningVm()
            testAttachGuestToolsIsoToStoppedVm()
            testAttachGuestToolsToVmWithVirtIODataVolume()
        }
    }

    void initEnv() {
        dbf = bean(DatabaseFacade.class)
        vm = env.inventoryByName("vm1") as VmInstanceInventory
        assert vm.platform == ImagePlatform.Windows.toString()
        vm3 = env.inventoryByName("vm3") as VmInstanceInventory

        env.simulator(GuestToolsConstant.GET_VM_GUEST_TOOLS_INFO_PATH) {
            GuestToolsKvmCommands.GetVmGuestToolsInfoRsp rsp = new GuestToolsKvmCommands.GetVmGuestToolsInfoRsp()
            rsp.setVersion("1.0.0")
            rsp.setStatus("Running")
            return rsp
        }
    }

    void testDownloadGuestToolsIsoExists() {
        int count = 1

        def exists = true
        env.afterSimulator(KVMConstant.KVM_HOST_CHECK_FILE_PATH) { CheckFileOnHostResponse rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, CheckFileOnHostCmd.class)
            if (exists) {
                rsp.existPaths = Collections.singletonMap(cmd.paths[0], "")
            } else {
                rsp.existPaths = [:]
            }
            ++count
            return rsp
        }

        def tools = getLatestGuestToolsForVm {
            uuid = vm3.uuid
        } as GuestToolsInventory
        assert tools != null
        assert !HostSystemTags.HOST_GUEST_TOOLS.hasTag(vm3.hostUuid, HostVO.class)
        exists = false
        attachGuestToolsIsoToVm {
            uuid = vm3.uuid
        }
        assert HostSystemTags.HOST_GUEST_TOOLS.hasTag(vm3.hostUuid, HostVO.class)

        exists = true
        attachGuestToolsIsoToVm {
            uuid = vm3.uuid
        }
        assert count == 2

        exists = false
        attachGuestToolsIsoToVm {
            uuid = vm3.uuid
        }
        assert count == 3
        env.cleanAfterSimulatorHandlers()
    }

    void testAttachGuestToolsIsoToRunningVm() {
        def vmVO = dbFindByUuid(vm.uuid, VmInstanceVO.class)
        assert vmVO.state == VmInstanceState.Running

        List<GuestToolsKvmCommands.AttachGuestToolsIsoToVmCmd> attachCmds = []
        env.afterSimulator(GuestToolsConstant.ATTACH_GUEST_TOOLS_ISO_TO_VM_PATH) { rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, GuestToolsKvmCommands.AttachGuestToolsIsoToVmCmd.class)
            attachCmds.add(cmd)
            return rsp
        }

        GuestToolsInventory latest = getLatestGuestToolsForVm {
            uuid = vm.uuid
        } as GuestToolsInventory
        assert latest != null
        assert !HostSystemTags.HOST_GUEST_TOOLS.hasTag(vm.hostUuid, HostVO.class)

        List<SystemTagInventory> tags
        tags = queryGuestToolsJustInstalledTags(vm.uuid)
        assert tags.isEmpty()
        tags = queryVirtIOTags(vm.uuid)
        assert tags.isEmpty()

        assert VmSystemTags.SYNC_PORTS.hasTag(vm.uuid)
        attachGuestToolsIsoToVm {
            uuid = vm.uuid
        }
        assert attachCmds.size() == 1
        assert HostSystemTags.HOST_GUEST_TOOLS.hasTag(vm.hostUuid, HostVO.class)
        assert !VmSystemTags.SYNC_PORTS.hasTag(vm.uuid)

        tags = queryGuestToolsJustInstalledTags(vm.uuid)
        assert tags.size() == 1

        attachGuestToolsIsoToVm {
            uuid = vm.uuid
        }
        assert attachCmds.size() == 2

        tags = queryGuestToolsJustInstalledTags(vm.uuid)
        assert tags.size() == 1

        SQL.New(VmInstanceVO.class)
                .eq(VmInstanceVO_.uuid, vm.uuid)
                .set(VmInstanceVO_.state, VmInstanceState.VolumeRecovering)
                .update()

        def recoveringVmVO = dbFindByUuid(vm.uuid, VmInstanceVO.class)
        assert recoveringVmVO.state == VmInstanceState.VolumeRecovering


        def tools = getLatestGuestToolsForVm {
            uuid = vm.uuid
        } as GuestToolsInventory
        assert tools != null

        SQL.New(VmInstanceVO.class)
                .eq(VmInstanceVO_.uuid, vm.uuid)
                .set(VmInstanceVO_.state, VmInstanceState.Running)
                .update()

        // reboot vm to get guest tools info
        // expect: GuestToolsJustInstalled system tag has been deleted;
        // expect: Windows vm has VirtIO system tag
        rebootVmInstance {
            uuid = vm.uuid
        }

        tags = queryGuestToolsJustInstalledTags(vm.uuid)
        assert tags.isEmpty()
        tags = queryVirtIOTags(vm.uuid)
        assert tags.size() == 1
        def virtIOTag = tags[0]

        latest = getLatestGuestToolsForVm {
            uuid = vm.uuid
        } as GuestToolsInventory
        assert latest == null

        def vmInfo = getVmGuestToolsInfo {
            uuid = vm.uuid
        } as GetVmGuestToolsInfoResult
        assert vmInfo.version == "1.0.0"
        assert vmInfo.status == "Running"

        // reboot vm without virtIO system tag
        // expect: The tag of virtIO will no longer be added
        deleteTag {
            delegate.uuid = virtIOTag.uuid
        }

        rebootVmInstance {
            delegate.uuid = vm.uuid
        }

        tags = queryVirtIOTags(vm.uuid)
        assert tags.isEmpty()

        env.cleanAfterSimulatorHandlers()
    }

    void testAttachGuestToolsIsoToStoppedVm() {
        stopVmInstance {
            uuid = vm.uuid
        }

        def vmVO = dbFindByUuid(vm.uuid, VmInstanceVO.class)
        assert vmVO.state == VmInstanceState.Stopped

        expect(AssertionError.class) {
            attachGuestToolsIsoToVm {
                uuid = vm.uuid
            }
        }
    }

    void testAttachGuestToolsToVmWithVirtIODataVolume() {
        def l3 = env.inventoryByName("l3") as L3NetworkInventory
        def winImage = env.inventoryByName("image1") as ImageInventory
        def instanceOffering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory
        def cluster = env.inventoryByName("cluster") as ClusterInventory
        def diskOffering = env.inventoryByName("diskOffering") as DiskOfferingInventory

        GuestToolsKvmCommands.AttachGuestToolsIsoToVmCmd attachCmd = null
        env.afterSimulator(GuestToolsConstant.ATTACH_GUEST_TOOLS_ISO_TO_VM_PATH) { rsp, HttpEntity<String> e ->
            def cmd = JSONObjectUtil.toObject(e.body, GuestToolsKvmCommands.AttachGuestToolsIsoToVmCmd.class)
            attachCmd = cmd
            return rsp
        }

        def vm4 = createVmInstance {
            delegate.name = "vm4"
            delegate.l3NetworkUuids = [l3.uuid]
            delegate.imageUuid = winImage.uuid
            delegate.instanceOfferingUuid = instanceOffering.uuid
            delegate.clusterUuid = cluster.uuid
            delegate.dataDiskOfferingUuids = [diskOffering.uuid]
            delegate.strategy = VmCreationStrategy.CreateStopped.toString()
        } as VmInstanceInventory
        
        def dataVolume = (vm4.allVolumes as List<VolumeInventory>).find { it.type == VolumeType.Data.toString() }
        assert dataVolume != null

        createSystemTag {
            delegate.tag = KVMSystemTags.VOLUME_VIRTIO_SCSI.instantiateTag([:])
            delegate.resourceUuid = dataVolume.uuid
            delegate.resourceType = VolumeVO.class.getSimpleName()
        } as SystemTagInventory

        startVmInstance {
            delegate.uuid = vm4.uuid
        }

        assert !VmSystemTags.VIRTIO.hasTag(vm4.uuid)

        // vm with data volume also could attach guest tools iso
        attachGuestToolsIsoToVm {
            uuid = vm4.uuid
        }

        assert attachCmd.needTempDisk

        rebootVmInstance {
            uuid = vm4.uuid
        }

        // confirm virtio take effect
        assert VmSystemTags.VIRTIO.hasTag(vm4.uuid)
        attachCmd = null

        // attach guest tools to virtio vm
        attachGuestToolsIsoToVm {
            uuid = vm4.uuid
        }

        assert !attachCmd.needTempDisk
    }

    @Override
    void clean() {
        env.delete()
    }

    List<SystemTagInventory> queryGuestToolsJustInstalledTags(String vmUuid) {
        return querySystemTag {
            delegate.conditions = [
                    "resourceUuid=${vmUuid}".toString(),
                    "tag=${MevocoSystemTags.GUEST_TOOLS_HAS_ATTACHED.getTagFormat()}"
            ]
        } as List<SystemTagInventory>
    }

    List<SystemTagInventory> queryVirtIOTags(String vmUuid) {
        return querySystemTag {
            delegate.conditions = [
                    "resourceUuid=${vmUuid}".toString(),
                    "tag=${VmSystemTags.VIRTIO.getTagFormat()}"
            ]
        } as List<SystemTagInventory>
    }
}
