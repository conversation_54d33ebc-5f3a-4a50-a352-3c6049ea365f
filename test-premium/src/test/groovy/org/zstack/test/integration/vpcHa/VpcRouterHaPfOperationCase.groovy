package org.zstack.test.integration.vpcHa

import org.springframework.http.HttpEntity
import org.zstack.appliancevm.ApplianceVmHaStatus
import org.zstack.appliancevm.ApplianceVmSystemTags
import org.zstack.appliancevm.ApplianceVmVO
import org.zstack.appliancevm.ApplianceVmVO_
import org.zstack.core.db.DatabaseFacade
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.vpc.ha.VpcHaGroupNetworkServiceRefVO
import org.zstack.header.vpc.ha.VpcHaGroupNetworkServiceRefVO_
import org.zstack.network.service.virtualrouter.VirtualRouterCommands
import org.zstack.network.service.virtualrouter.VirtualRouterConstant
import org.zstack.sdk.*
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

/**
 * Created by shixin on 2019/06/16.
 */
class VpcRouterHaPfOperationCase extends PremiumSubCase {
    EnvSpec env
    DatabaseFacade dbf

    @Override
    void setup() {
        spring {
            useSpring(VpcRouterHaTest.springSpec)
        }
    }

    @Override
    void environment() {
        env = VpcRouteHaEnv.VpcRouteHaBasicEnv()
    }

    @Override
    void test() {
        env.create {
            dbf = bean(DatabaseFacade.class)
            createEnv()
            testCreateEip()
            testAttachPf()
            testDeletePf()
            testDeleteVip()
            testConcurrentOperation()
            cleanVpcHaGroup()
        }
    }

    @Override
    void clean() {
        env.delete()
    }

    void cleanVpcHaGroup() {
        List<VpcHaGroupInventory> haGroups = queryVpcHaGroup {}
        for (VpcHaGroupInventory ha : haGroups) {
            deleteVpcHaGroup {
                uuid = ha.uuid
            }
        }
    }

    void createEnv() {
        def offer = env.inventoryByName("vro") as VirtualRouterOfferingInventory
        def vpc_l3 = env.inventoryByName("l3") as L3NetworkInventory
        def image = env.inventoryByName("image1") as ImageInventory
        def offering = env.inventoryByName("instanceOffering") as InstanceOfferingInventory

        org.zstack.sdk.VpcHaGroupInventory vpcHa = createVpcHaGroup {
            name = "vpc-ha"
            monitorIps = Arrays.asList("*******", "*******")
        }

        VirtualRouterVmInventory vpc1 = createVpcVRouter {
            name = "vpc-1"
            virtualRouterOfferingUuid = offer.uuid
            systemTags = [String.format("%s::%s", ApplianceVmSystemTags.APPLIANCEVM_HA_UUID_TOKEN, vpcHa.uuid)]
        }
        VirtualRouterVmInventory vpc2 = createVpcVRouter {
            name = "vpc-2"
            virtualRouterOfferingUuid = offer.uuid
            systemTags = [String.format("%s::%s", ApplianceVmSystemTags.APPLIANCEVM_HA_UUID_TOKEN, vpcHa.uuid)]
        }

        /* set vpc1 as master */
        SQL.New(ApplianceVmVO.class).eq(ApplianceVmVO_.uuid, vpc1.uuid)
                .set(ApplianceVmVO_.haStatus, ApplianceVmHaStatus.Master).update()

        attachL3NetworkToVm {
            vmInstanceUuid = vpc1.uuid
            l3NetworkUuid = vpc_l3.uuid
        }

        createVmInstance {
            name = "vm"
            instanceOfferingUuid = offering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [vpc_l3.uuid]
        }
    }

    void testCreateEip() {
        def pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        VmInstanceInventory vm = queryVmInstance {conditions=["name=vm"]} [0]

        VipInventory vip = createVip {
            name = "vip1"
            l3NetworkUuid = pubL3.uuid
        }

        List<VirtualRouterCommands.CreateVipCmd> vipCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.CreateVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.CreateVipCmd.class)
            vipCmds.add(vipCmd)
            return rsp
        }

        List<VirtualRouterCommands.CreatePortForwardingRuleCmd> pfCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.CreatePortForwardingRuleCmd eipCmd = json(entity.getBody(), VirtualRouterCommands.CreatePortForwardingRuleCmd.class)
            pfCmds.add(eipCmd)
            return rsp
        }

        PortForwardingRuleInventory pf = createPortForwardingRule {
            vipUuid = vip.uuid
            vipPortStart = 21L
            protocolType = "UDP"
            name = "pf-1"
            vmNicUuid = vm.vmNics.get(0).uuid
        }

        retryInSecs {
            assert vipCmds.size() == 2
        }
        retryInSecs {
            assert pfCmds.size() == 2
        }

        List<VirtualRouterCommands.RemoveVipCmd> vipRemoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REMOVE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.RemoveVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.RemoveVipCmd.class)
            vipRemoveCmds.add(vipCmd)
            return rsp
        }

        List<VirtualRouterCommands.RevokePortForwardingRuleCmd> pfRmoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REVOKE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.RevokePortForwardingRuleCmd pfCmd = json(entity.getBody(), VirtualRouterCommands.RevokePortForwardingRuleCmd.class)
            pfRmoveCmds.add(pfCmd)
            return rsp
        }

        deletePortForwardingRule {
            uuid = pf.uuid
        }

        retryInSecs {
            assert vipRemoveCmds.size() == 2
        }

        retryInSecs {
            assert pfRmoveCmds.size() == 2
        }
    }

    void testAttachPf() {
        def pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        VmInstanceInventory vm = queryVmInstance {conditions=["name=vm"]} [0]

        VipInventory vip = createVip {
            name = "vip2"
            l3NetworkUuid = pubL3.uuid
        }

        List<VirtualRouterCommands.CreateVipCmd> vipCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.CreateVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.CreateVipCmd.class)
            vipCmds.add(vipCmd)
            return rsp
        }

        List<VirtualRouterCommands.CreatePortForwardingRuleCmd> pfCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.CreatePortForwardingRuleCmd eipCmd = json(entity.getBody(), VirtualRouterCommands.CreatePortForwardingRuleCmd.class)
            pfCmds.add(eipCmd)
            return rsp
        }

        PortForwardingRuleInventory pf = createPortForwardingRule {
            vipUuid = vip.uuid
            vipPortStart = 22L
            protocolType = "UDP"
            name = "pf-2"
        }

        attachPortForwardingRule {
            ruleUuid = pf.uuid
            vmNicUuid = vm.vmNics.get(0).uuid
        }

        retryInSecs {
            assert vipCmds.size() == 2
        }
        retryInSecs {
            assert pfCmds.size() == 2
        }

        List<VirtualRouterCommands.RemoveVipCmd> vipRemoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REMOVE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.RemoveVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.RemoveVipCmd.class)
            vipRemoveCmds.add(vipCmd)
            return rsp
        }

        List<VirtualRouterCommands.RevokePortForwardingRuleCmd> pfRmoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REVOKE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.RevokePortForwardingRuleCmd pfCmd = json(entity.getBody(), VirtualRouterCommands.RevokePortForwardingRuleCmd.class)
            pfRmoveCmds.add(pfCmd)
            return rsp
        }

        detachPortForwardingRule {
            uuid = pf.uuid
        }

        retryInSecs {
            assert vipRemoveCmds.size() == 2
        }
        retryInSecs {
            assert pfRmoveCmds.size() == 2
        }
    }

    void testDeletePf() {
        def pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        VmInstanceInventory vm = queryVmInstance {conditions=["name=vm"]} [0]

        VipInventory vip = createVip {
            name = "vip3"
            l3NetworkUuid = pubL3.uuid
        }

        List<VirtualRouterCommands.CreateVipCmd> vipCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.CreateVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.CreateVipCmd.class)
            vipCmds.add(vipCmd)
            return rsp
        }
        List<VirtualRouterCommands.CreatePortForwardingRuleCmd> pfCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.CreatePortForwardingRuleCmd eipCmd = json(entity.getBody(), VirtualRouterCommands.CreatePortForwardingRuleCmd.class)
            pfCmds.add(eipCmd)
            return rsp
        }

        PortForwardingRuleInventory pf = createPortForwardingRule {
            vipUuid = vip.uuid
            vipPortStart = 21L
            protocolType = "UDP"
            name = "pf-1"
            vmNicUuid = vm.vmNics.get(0).uuid
        }

        retryInSecs {
            assert vipCmds.size() == 2
        }
        retryInSecs {
            assert pfCmds.size() == 2
        }

        List<VirtualRouterCommands.RemoveVipCmd> vipRemoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REMOVE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.RemoveVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.RemoveVipCmd.class)
            vipRemoveCmds.add(vipCmd)
            return rsp
        }
        List<VirtualRouterCommands.RevokePortForwardingRuleCmd> pfRmoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REVOKE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.RevokePortForwardingRuleCmd pfCmd = json(entity.getBody(), VirtualRouterCommands.RevokePortForwardingRuleCmd.class)
            pfRmoveCmds.add(pfCmd)
            return rsp
        }

        deletePortForwardingRule {
            uuid = pf.uuid
        }

        retryInSecs {
            assert vipRemoveCmds.size() == 2
        }
        retryInSecs {
            assert pfRmoveCmds.size() == 2
        }
        assert Q.New(VpcHaGroupNetworkServiceRefVO.class).eq(VpcHaGroupNetworkServiceRefVO_.networkServiceUuid, pf.uuid).count() == 0
    }

    void testDeleteVip() {
        def pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        VmInstanceInventory vm = queryVmInstance {conditions=["name=vm"]} [0]

        VipInventory vip = createVip {
            name = "vip3"
            l3NetworkUuid = pubL3.uuid
        }

        List<VirtualRouterCommands.CreateVipCmd> vipCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.CreateVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.CreateVipCmd.class)
            vipCmds.add(vipCmd)
            return rsp
        }
        List<VirtualRouterCommands.CreatePortForwardingRuleCmd> pfCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_CREATE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.CreatePortForwardingRuleCmd eipCmd = json(entity.getBody(), VirtualRouterCommands.CreatePortForwardingRuleCmd.class)
            pfCmds.add(eipCmd)
            return rsp
        }

        PortForwardingRuleInventory pf = createPortForwardingRule {
            vipUuid = vip.uuid
            vipPortStart = 21L
            protocolType = "UDP"
            name = "pf-1"
            vmNicUuid = vm.vmNics.get(0).uuid
        }

        retryInSecs {
            assert vipCmds.size() == 2
        }
        retryInSecs {
            assert pfCmds.size() == 2
        }

        List<VirtualRouterCommands.RemoveVipCmd> vipRemoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REMOVE_VIP){ rsp, HttpEntity<String> e ->
            VirtualRouterCommands.RemoveVipCmd vipCmd = JSONObjectUtil.toObject(e.body,VirtualRouterCommands.RemoveVipCmd.class)
            vipRemoveCmds.add(vipCmd)
            return rsp
        }
        List<VirtualRouterCommands.RevokePortForwardingRuleCmd> pfRmoveCmds = new ArrayList<>()
        env.afterSimulator(VirtualRouterConstant.VR_REVOKE_PORT_FORWARDING) { rsp, HttpEntity<String> entity ->
            VirtualRouterCommands.RevokePortForwardingRuleCmd pfCmd = json(entity.getBody(), VirtualRouterCommands.RevokePortForwardingRuleCmd.class)
            pfRmoveCmds.add(pfCmd)
            return rsp
        }

        deleteVip {
            uuid = vip.uuid
        }

        retryInSecs {
            assert vipRemoveCmds.size() == 2
        }
        retryInSecs {
            assert pfRmoveCmds.size() == 2
        }
    }

    void testConcurrentOperation() {
        def pubL3 = env.inventoryByName("pubL3") as L3NetworkInventory
        VmInstanceInventory vm = queryVmInstance {conditions=["name=vm"]} [0]

        VipInventory vip = createVip {
            name = "vip-concurrent"
            l3NetworkUuid = pubL3.uuid
        }

        PortForwardingRuleInventory pf = createPortForwardingRule {
            vipUuid = vip.uuid
            vipPortStart = 21L
            protocolType = "UDP"
            name = "pf-pubL3"
        }

        /* all vyos operation wait for 2 seconds */
        env.simulator(VirtualRouterConstant.VR_CREATE_VIP){ HttpEntity<String> e, EnvSpec spec ->
            VirtualRouterCommands.CreateVipRsp rsp = new VirtualRouterCommands.CreateVipRsp()
            sleep(2000)
            return rsp
        }

        env.simulator(VirtualRouterConstant.VR_CREATE_PORT_FORWARDING){ HttpEntity<String> e, EnvSpec spec ->
            VirtualRouterCommands.CreatePortForwardingRuleRsp rsp = new VirtualRouterCommands.CreatePortForwardingRuleRsp()
            sleep(2000)
            return rsp
        }

        env.simulator(VirtualRouterConstant.VR_REMOVE_VIP){ HttpEntity<String> e, EnvSpec spec ->
            VirtualRouterCommands.RemoveVipRsp rsp = new VirtualRouterCommands.RemoveVipRsp()
            sleep(2000)
            return rsp
        }

        env.simulator(VirtualRouterConstant.VR_REVOKE_PORT_FORWARDING){ HttpEntity<String> e, EnvSpec spec ->
            VirtualRouterCommands.RevokePortForwardingRuleRsp rsp = new VirtualRouterCommands.RevokePortForwardingRuleRsp()
            sleep(2000)
            return rsp
        }

        def thread1 = Thread.start {
            attachPortForwardingRule {
                ruleUuid = pf.uuid
                vmNicUuid = vm.vmNics.get(0).uuid
            }
        }
        def thread2 = Thread.start {
            deleteVip {
                uuid = pf.vipUuid
            }
        }

        [thread1, thread2].each {it.join()}

        assert !Q.New(VpcHaGroupNetworkServiceRefVO.class).eq(VpcHaGroupNetworkServiceRefVO_.networkServiceUuid, vip.uuid).isExists()
        assert !Q.New(VpcHaGroupNetworkServiceRefVO.class).eq(VpcHaGroupNetworkServiceRefVO_.networkServiceUuid, pf.uuid).isExists()

    }
}
