package org.zstack.test.unittest.check

import org.apache.bcel.Repository
import org.apache.bcel.classfile.JavaClass
import org.apache.bcel.util.ClassLoaderRepository
import org.junit.Test
import org.zstack.license.LicenseChecker
import org.zstack.license.LicenseConstant
import org.zstack.license.PlatformLicense
import org.zstack.utils.Utils
import org.zstack.utils.logging.CLogger

import java.security.MessageDigest

public class CheckToolSumCase {
    private static final CLogger logger = Utils.getLogger(CheckToolSumCase.class);

    @Test
    public void testToolSumConsistent() {
        Repository.setRepository(new ClassLoaderRepository(PlatformLicense.class.getClassLoader()))
        JavaClass objectClazz = Repository.lookupClass(PlatformLicense.class.getName())
        MessageDigest digest = MessageDigest.getInstance("MD5")
        byte[] bytes = digest.digest(objectClazz.toString().getBytes())
        StringBuilder sb = new StringBuilder()
        for (byte b : bytes) {
            sb.append(String.format("%02x", b))
        }
        String tool1_Sum = sb.toString()
        logger.debug(String.format("please update LicenseConstant TOOL1_SUM: %s", tool1_Sum))

        objectClazz = Repository.lookupClass(LicenseChecker.class.getName())
        digest = MessageDigest.getInstance("MD5")
        bytes = digest.digest(objectClazz.toString().getBytes())
        sb = new StringBuilder()
        for (byte b : bytes) {
            sb.append(String.format("%02x", b))
        }
        String tool2_Sum = sb.toString()
        logger.debug(String.format("please update LicenseConstant TOOL2_SUM: %s", tool2_Sum))
        assert LicenseConstant.TOOL1_SUM.equals(tool1_Sum)
        assert LicenseConstant.TOOL2_SUM.equals(tool2_Sum)
    }
}

