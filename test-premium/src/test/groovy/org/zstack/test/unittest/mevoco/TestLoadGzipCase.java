package org.zstack.test.unittest.mevoco;

import org.junit.Test;
import org.zstack.core.Platform;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.utils.ShellUtils;
import org.zstack.utils.path.PathUtil;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import static org.zstack.core.Platform.operr;

/**
 * Created by kayo on 2018/9/13.
 */
public class TestLoadGzipCase {
    @Test
    public void testLoadGzip() {
        File file = PathUtil.findFileOnClassPath("gzip/test.tar.gz");

        assert file != null;

        byte[] bytes = new byte[(int) file.length()];
        try (InputStream inputStream = new FileInputStream(file)) {
            inputStream.read(bytes);
        } catch (FileNotFoundException e) {
            assert false;
        } catch (IOException e) {
            assert false;
        }

        assert bytes.length != 0;

        String tmpDir = PathUtil.createTempDirectory();
        String filePath = PathUtil.join(tmpDir, Platform.getUuid());

        try (FileOutputStream lout = new FileOutputStream(filePath)) {
            lout.write(bytes);
        } catch (Exception e) {
            throw new OperationFailureException(operr(e.getMessage()));
        }

        ShellUtils.run(String.format("tar zxf %s -C %s", filePath, tmpDir));

        List<String> paths = new ArrayList<>();
        PathUtil.scanFolder(paths, tmpDir);

        assert !paths.isEmpty();

        ShellUtils.run(String.format("rm -rf %s", tmpDir));
    }
}
