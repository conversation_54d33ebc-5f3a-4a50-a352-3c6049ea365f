package org.zstack.test.unittest.zwatch;

import org.junit.Test;

import java.util.regex.Pattern;

public class TestValidateSNSAliyunSmsEndpoint {
    @Test
    public void testPhoneNumber() {
        Pattern aliyunSmsPhoneNumberFormat = Pattern.compile("^(\\+86-)?[1][3456789][0-9]{9}$");

        String sussPhoneNumber = "+86-17717411234";
        assert aliyunSmsPhoneNumberFormat.matcher(sussPhoneNumber).matches();

        String errorPhoneNumber = "+8617717418233";
        assert !aliyunSmsPhoneNumberFormat.matcher(errorPhoneNumber).matches();

        String errorPhoneNumber1 = "8617717418233";
        assert !aliyunSmsPhoneNumberFormat.matcher(errorPhoneNumber1).matches();
    }
}
