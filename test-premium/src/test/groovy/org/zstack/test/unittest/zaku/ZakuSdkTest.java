package org.zstack.test.unittest.zaku;

import edu.emory.mathcs.backport.java.util.Collections;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Node;
import io.kubernetes.client.openapi.models.V1NodeList;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodList;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.zstack.container.entity.KubernetesNodeInventory;
import org.zstack.core.Platform;
import org.zstack.iam2.container.ContainerImageInventory;
import org.zstack.iam2.container.ProjectRepositoryInventory;
import org.zstack.iam2.container.zaku.ZakuApiCaller;
import org.zstack.iam2.container.zaku.ZakuApiStructures;
import org.zstack.iam2.container.zaku.ZakuErrors;
import org.zstack.iam2.container.zaku.ZakuHttpClient;
import org.zstack.iam2.container.zaku.ZakuRequest;
import org.zstack.iam2.container.zaku.ZakuResponse;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

public class ZakuSdkTest {
    protected static final CLogger logger = Utils.getLogger(ZakuSdkTest.class);

    private ZakuHttpClient httpClient;
    private ZakuApiCaller client;

    @Before
    public void setUp() {
        String host = "**************";
        int port = 80;
        String context = "ze";
        String accessKeyId = "UuA673Um52thv2PREkye";
        String accessKeySecret = "mk20hbpIPO4WSmOUDPVFAYHjTZ0hJrxLoOZXLmsK";

        httpClient = new ZakuHttpClient(host, port, context, accessKeyId, accessKeySecret);

        client = new ZakuApiCaller();
        client.setClient(httpClient);
    }

    @Test
    public void testCreateProjectAndSetQuota() {
        ZakuResponse<ZakuApiStructures.ZakuCluster[]> response = client.getClusters();
        assertEquals(200, response.getStatusCode());
        assertTrue(response.isSuccess());

        List<ZakuApiStructures.ZakuCluster> clusters = Arrays.asList(response.getContent());
        assert !clusters.isEmpty();
        assert clusters.get(0).getName() != null;

        String projectUuid = Platform.getUuid();
        String projectName = Platform.getUuid();
        ZakuResponse<ZakuApiStructures.ZakuProject> projectZakuResponse =
                client.createProject(projectUuid,
                        projectName,
                        "测试");
        assertTrue(projectZakuResponse.isSuccess());
        assertNull(projectZakuResponse.getError());

        ZakuApiStructures.ZakuProject project = projectZakuResponse.getContent();
        assertEquals(projectName, project.getName());
        assertEquals(projectUuid, project.getCloudUuid());
        assertTrue(project.isCloudFlag());

        List<ZakuApiStructures.QuotaParam> quotas = new ArrayList<>();
        ZakuApiStructures.QuotaParam quota = new ZakuApiStructures.QuotaParam();
        quota.setName("requests.cpu");
        quotas.add(quota);
        ZakuResponse<ZakuApiStructures.ZakuQuota[]> setQuotaRsp = client.setProjectQuota(projectUuid, clusters.get(0).getId(), quotas);
        assertTrue(setQuotaRsp.isSuccess());
        assertNull(setQuotaRsp.getError());

        ZakuResponse<ZakuApiStructures.ZakuQuota[]> getQuotaRsp = client.getProjectQuota(projectUuid, clusters.get(0).getId());
        assertTrue(getQuotaRsp.isSuccess());
        assertNull(getQuotaRsp.getError());

        projectZakuResponse =
                client.createProject(projectUuid,
                        projectName,
                        "测试");
        assertFalse(projectZakuResponse.isSuccess());
        assertNotNull(projectZakuResponse.getError());

        ZakuResponse<String> deleteResponse =
                client.deleteProject(projectUuid);
        assertTrue(deleteResponse.isSuccess());
        assertNull(deleteResponse.getError());

        deleteResponse =
                client.deleteProject(projectUuid);
        assertFalse(deleteResponse.isSuccess());
        assertNotNull(deleteResponse.getError());
        assertEquals(ZakuErrors.PROJECT_NOT_EXIST.toString(), deleteResponse.getError().getCode());
    }

    @Test
    public void testGetImages() {
        ZakuResponse<ZakuApiStructures.ZakuProject[]> response =
                client.getAuthorizedProjects();
        assertTrue(response.isSuccess());
        assertNotNull(response.getContent());

        List<Integer> projectIds = Arrays.stream(response.getContent()).map(ZakuApiStructures.ZakuProject::getID).collect(Collectors.toList());

        List<ProjectRepositoryInventory> repositoryInventories = new ArrayList<>();
        projectIds.forEach(projectId -> {
            ZakuResponse<ZakuApiStructures.RepositoryPage> response1 = client.getRepositoriesInProject(Long.valueOf(projectId));
            assertTrue(response1.isSuccess());
            assertNotNull(response1.getContent());

            List<ProjectRepositoryInventory> inventories1 = response1.getContent().getResult();
            repositoryInventories.addAll(inventories1);
        });

        repositoryInventories.forEach(repositoryInventory -> {
            Integer limit = 1000;
            Integer start = 0;
            String queryParameters = String.format("?limit=%s&start=%s", String.valueOf(limit), String.valueOf(start));
            ZakuResponse<ZakuApiStructures.ImagePage> response1 = client.getContainerImagesInRegistry(
                    repositoryInventory.getZeProjectID(), repositoryInventory.getID(), queryParameters);
            assertTrue(response1.isSuccess());
            assertNotNull(response1.getContent());

            ZakuApiStructures.ImagePage imagePage = response1.getContent();
            List<ContainerImageInventory> inventories1 = imagePage.getResult();
            inventories1.forEach(containerImageInventory -> {
                String fullName = containerImageInventory.getName();
                String imageName = fullName.replaceAll("/", "%252F");

                ZakuResponse<ZakuApiStructures.ImageTagPage> response2 = client.getContainerImageTags(
                        repositoryInventory.getZeProjectID(), repositoryInventory.getID(), imageName, queryParameters);

                assertTrue(response2.isSuccess());
                assertNotNull(response2.getContent().getResult());
            });
        });
    }

    @Test
    public void testCreateAndDeleteUser() {
        String username = Platform.getUuid().substring(0, 20);
        String userdesc = Platform.getUuid().substring(0, 20);

        ZakuResponse<ZakuApiStructures.ZakuUser> response =
                client.createUser(username, userdesc);
        assertTrue(response.isSuccess());
        assertNull(response.getError());
        assertEquals(username, response.getContent().getUsername());
        assertEquals(userdesc, response.getContent().getDesc());

        // test set admin
        ZakuResponse<String> setAdmin = client.setAdmin(username);
        assertTrue(setAdmin.isSuccess());
        assertNull(setAdmin.getError());

        // test unset admin
        ZakuResponse<String> unsetAdmin = client.unsetAdmin(username);
        assertTrue(unsetAdmin.isSuccess());
        assertNull(unsetAdmin.getError());

        // test create project
        String projectUuid = Platform.getUuid();
        String projectName = Platform.getUuid();
        ZakuResponse<ZakuApiStructures.ZakuProject> projectZakuResponse =
                client.createProject(projectUuid,
                        projectName,
                        "测试");
        assertTrue(projectZakuResponse.isSuccess());
        assertNull(projectZakuResponse.getError());

        ZakuApiStructures.ZakuProject project = projectZakuResponse.getContent();
        assertEquals(projectName, project.getName());
        assertEquals(projectUuid, project.getCloudUuid());
        assertTrue(project.isCloudFlag());

        // add user to project
        ZakuResponse<String> addUser = client.addUsersToProjects(Collections.singletonList(username),
                Collections.singletonList(projectUuid));
        assertTrue(addUser.isSuccess());

        // remove user from project
        ZakuResponse<String> removeUser = client.removeUsersFromProjects(Collections.singletonList(username),
                Collections.singletonList(projectUuid));
        assertTrue(removeUser.isSuccess());

        ZakuResponse<String> deleteResponse =
                client.deleteProject(projectUuid);
        assertTrue(deleteResponse.isSuccess());
        assertNull(deleteResponse.getError());

        ZakuResponse<String> delete =
                client.deleteUser(username);
        assertTrue(delete.isSuccess());
        assertNull(delete.getError());
    }

    @Test
    public void testInvalidRequest() {
        try {
            ZakuRequest invalidRequest = new ZakuRequest(ZakuRequest.HttpMethod.GET, "/invalid/endpoint");
            ZakuResponse<String> response = httpClient.execute(invalidRequest, String.class);

            // Depending on how your client handles errors, you might expect an exception
            // or an error response. Adjust the assertion accordingly.
            assertEquals(404, response.getStatusCode());
        } catch (Exception e) {
            // If you expect an exception for invalid requests, use this assertion instead
            assertTrue("Exception should contain error information", e.getMessage().contains("error"));
        }
    }

    private static final String VGPU_ANNOTATION_KEY = "hami.io/vgpu-devices-allocated";

    private static final String NVIDIA_ANNOTATION_KEY = "hami.io/node-nvidia-register";
    private static final String HYGON_DCU_ANNOTATION_KEY = "hami.io/node-dcu-register";
    private static final String ASCEND_ANNOTATION_KEY = "hami.io/node-register-Ascend910B4";

    private static class VGPUInfo {
        String deviceId;
        String manufacturer;
        int memoryMB;
        int index;

        static VGPUInfo fromString(String s) {
            String[] parts = s.split(",");
            VGPUInfo info = new VGPUInfo();
            info.deviceId = parts[0];
            info.manufacturer = parts[1];
            info.memoryMB = Integer.parseInt(parts[2]);
            info.index = Integer.parseInt(parts[3]);
            return info;
        }

        @Override
        public String toString() {
            return String.format("Device: %s, Manufacturer: %s, Memory: %d MB, Index: %d",
                    deviceId, manufacturer, memoryMB, index);
        }
    }


    private void logVGPUUsage(String nodeName, Map<String, List<VGPUInfo>> podVGPUUsage, Map<String, Integer> totalVGPUUsage) {
        logger.debug("vGPU usage on node " + nodeName + ":");
        logger.debug("------------------------------------");
        podVGPUUsage.forEach((pod, vgpuInfos) -> {
            logger.debug(pod + ":");
            vgpuInfos.forEach(info -> logger.debug("  " + info));
        });
        logger.debug("------------------------------------");
        logger.debug("Total vGPU usage:");
        totalVGPUUsage.forEach((deviceId, memoryMB) ->
                logger.debug("  Device " + deviceId + ": " + memoryMB + " MB"));
    }

    private void getGpuInfoFromNode(V1Node node) {
        Map<String, String> annotations = node.getMetadata().getAnnotations();
        if (annotations == null
                || !annotations.containsKey(NVIDIA_ANNOTATION_KEY)
                || !annotations.containsKey(HYGON_DCU_ANNOTATION_KEY)
                || !annotations.containsKey(ASCEND_ANNOTATION_KEY)) {
            return;
        }

        logger.debug(String.format("NV     from node: %s", annotations.get(NVIDIA_ANNOTATION_KEY)));
        logger.debug(String.format("DCU    from node: %s", annotations.get(HYGON_DCU_ANNOTATION_KEY)));
        logger.debug(String.format("ASCEND from node: %s", annotations.get(ASCEND_ANNOTATION_KEY)));
    }

    private List<VGPUInfo> getVGPUInfoFromPod(V1Pod pod) {
        Map<String, String> annotations = pod.getMetadata().getAnnotations();
        if (annotations == null || !annotations.containsKey(VGPU_ANNOTATION_KEY)) {
            return Collections.emptyList();
        }

        String vgpuAllocation = annotations.get(VGPU_ANNOTATION_KEY);
        logger.debug("vgpu allocation is :" + vgpuAllocation);
        return Arrays.stream(vgpuAllocation.split(";"))
                .map(str -> {
                    str = StringUtils.removeStart(str, ":");
                    str = StringUtils.removeEnd(str, ":");
                    return str;
                })
                .filter(s -> !s.isEmpty())
                .map(VGPUInfo::fromString)
                .collect(Collectors.toList());
    }

    @Test
    public void testTotalGPUs() throws Exception {
        ZakuResponse<ZakuApiStructures.ZakuCluster[]> response = client.getClusters();
        assertEquals(200, response.getStatusCode());
        assertTrue(response.isSuccess());

        List<ZakuApiStructures.ZakuCluster> clusters = Arrays.asList(response.getContent());
        assert !clusters.isEmpty();
        assert clusters.get(0).getName() != null;
        for (ZakuApiStructures.ZakuCluster cluster : clusters) {
            ZakuResponse<ZakuApiStructures.ClusterConfigView> clusterConfigViewZakuResponse = client
                    .getClusterKubeConfig(cluster.getId());
            ApiClient client = ClientBuilder.kubeconfig(KubeConfig.loadKubeConfig(new StringReader(clusterConfigViewZakuResponse.getContent().getKubeConfig()))).build();
            Configuration.setDefaultApiClient(client);
            CoreV1Api api = new CoreV1Api();

            String labelSelector = "zstack";

            api.listNode(null, null, null, null, null, null, null, null, null, null)
                    .getItems()
                    .forEach(this::getGpuInfoFromNode);

            Map<String, List<VGPUInfo>> podVGPUUsage = new HashMap<>();
            Map<String, Integer> totalVGPUUsage = new HashMap<>();
            V1PodList podList = api.listPodForAllNamespaces(null, null, null, labelSelector, null, null, null, null, null, null);
            for (V1Pod pod : podList.getItems()) {
                List<VGPUInfo> vgpuInfos = getVGPUInfoFromPod(pod);
                if (!vgpuInfos.isEmpty()) {
                    String podName = pod.getMetadata().getName();
                    podVGPUUsage.put(podName, vgpuInfos);

                    for (VGPUInfo info : vgpuInfos) {
                        totalVGPUUsage.merge(info.deviceId, info.memoryMB, Integer::sum);
                    }
                }
            }

            logVGPUUsage("test", podVGPUUsage, totalVGPUUsage);
        }
    }
}
