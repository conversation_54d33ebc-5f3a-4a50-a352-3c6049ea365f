package org.zstack.test.unittest.ecsapiadapter.vpc

import com.aliyuncs.vpc.model.v20160428.CreateRouteEntryResponse
import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.vpc.CreateRouteEntry
import org.zstack.sdk.AddVRouterRouteEntryAction
import org.zstack.sdk.AliyunProxyVSwitchInventory
import org.zstack.sdk.AliyunProxyVpcInventory
import org.zstack.sdk.QueryAliyunProxyVSwitchAction
import org.zstack.sdk.QueryAliyunProxyVSwitchResult
import org.zstack.sdk.QueryAliyunProxyVpcAction
import org.zstack.sdk.QueryAliyunProxyVpcResult
import org.zstack.sdk.QueryVRouterRouteTableAction
import org.zstack.sdk.QueryVRouterRouteTableResult
import org.zstack.sdk.QueryVmNicAction
import org.zstack.sdk.QueryVmNicResult
import org.zstack.sdk.VRouterRouteTableInventory
import org.zstack.sdk.VmInstanceInventory
import org.zstack.sdk.VmNicInventory
import org.zstack.sdk.zwatch.alarm.AddActionToAlarmAction
import org.zstack.sdk.zwatch.alarm.AddActionToAlarmResult
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

/**
 * Created by Qi Le on 2019-05-31
 */
class CreateRouteEntryCase {
    String sessionId = ExternalAPIAdapterUtils.randomUUID()
    String requestId = "97efe8c4a6c040b68e52745acd1d0b15"
    String routeTableId = ExternalAPIAdapterUtils.randomUUID()
    String nextHopId = ExternalAPIAdapterUtils.randomUUID()
    String vRouterId = ExternalAPIAdapterUtils.randomUUID()
    String vpcId = ExternalAPIAdapterUtils.randomUUID()
    String nicId1 = ExternalAPIAdapterUtils.randomUUID()
    String nicId2 = ExternalAPIAdapterUtils.randomUUID()
    String l3NetUuid1 = ExternalAPIAdapterUtils.randomUUID()
    String l3NetUuid2 = ExternalAPIAdapterUtils.randomUUID()

    VmNicInventory nicInventory1 = new VmNicInventory(
            uuid: nicId1,
            l3NetworkUuid: l3NetUuid1,
            ip: "***************"
    )

    VmNicInventory nicInventory2 = new VmNicInventory(
            uuid: nicId2,
            l3NetworkUuid: l3NetUuid2,
            ip: "**************"
    )

    VmInstanceInventory vRouterInventory = new VmInstanceInventory(
            uuid: vRouterId,
            name: "wow",
            zoneUuid: ExternalAPIAdapterUtils.randomUUID(),
            vmNics: [nicInventory1, nicInventory2]
    )

    VRouterRouteTableInventory routeTableInventory = new VRouterRouteTableInventory(
            uuid: routeTableId,
            name: "cool",
            attachedRouterRefs: [
                    [
                            virtualRouterVmUuid: vRouterInventory.uuid,
                            routeTableUuid: routeTableId
                    ]
            ]
    )

    @Before
    void prepare() {
        QueryVRouterRouteTableAction.metaClass.call = {
            QueryVRouterRouteTableAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.contains("uuid=${routeTableId}".toString())

            return new QueryVRouterRouteTableAction.Result(
                    value: new QueryVRouterRouteTableResult(
                            inventories: [routeTableInventory]
                    )
            )
        }

        QueryAliyunProxyVpcAction.metaClass.call = {
            QueryAliyunProxyVpcAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.contains("vRouterUuid=${vRouterId}".toString())

            return new QueryAliyunProxyVpcAction.Result(
                    value: new QueryAliyunProxyVpcResult(
                            inventories: [
                                    new AliyunProxyVpcInventory(
                                            uuid: vpcId,
                                            vRouterUuid: vRouterId
                                    )
                            ]
                    )
            )
        }

        QueryAliyunProxyVSwitchAction.metaClass.call = {
            QueryAliyunProxyVSwitchAction self = delegate
            assert self.sessionId == sessionId
            assert self. conditions.contains("aliyunProxyVpcUuid=${vpcId}".toString())

            return new QueryAliyunProxyVSwitchAction.Result(
                    value: new QueryAliyunProxyVSwitchResult(
                            inventories: [
                                    new AliyunProxyVSwitchInventory(
                                            uuid: ExternalAPIAdapterUtils.randomUUID(),
                                            vpcL3NetworkUuid: l3NetUuid1
                                    )
                            ]
                    )
            )
        }

        QueryVmNicAction.metaClass.call = {
            QueryVmNicAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.size() == 1
            assert self.conditions.contains("vmInstanceUuid=${nextHopId}".toString())

            return new QueryVmNicAction.Result(
                    value: new QueryVmNicResult(
                            inventories: [nicInventory1, nicInventory2]
                    )
            )
        }

        AddVRouterRouteEntryAction.metaClass.call = {
            AddVRouterRouteEntryAction self = delegate
            assert self.routeTableUuid == routeTableId

            return new AddActionToAlarmAction.Result(
                    value: new AddActionToAlarmResult()
            )
        }
    }

    @Test
    void testEcsParamToZStackParam() {

        Map ecsAPIParam = [
                "Action" : "CreateRouteEntry",
                "DestinationCidrBlock" : "***************/28",
                "RouteTableId" : routeTableId,
                "NextHopId" : nextHopId,
                "NextHopType": "Instance"
        ]

        CreateRouteEntry api = new CreateRouteEntry(
                sessionId: sessionId,
                requestId: requestId
        )

        api.ecsAPIParamMap = ecsAPIParam
        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map:\n" + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.get("routeTableUuid") == routeTableId
        assert zstackAPIParamMap.get("destination") == "***************/28"
        assert zstackAPIParamMap.get("target") == "***************"
    }

    @Test
    void testZStackRspToEcsRsp() {

        Map ecsAPIParam = [
                "Action" : "CreateRouteEntry",
                "DestinationCidrBlock" : "***************/28",
                "RouteTableId" : routeTableId,
                "NextHopId" : nextHopId,
                "NextHopType" : "Instance"
        ]

        CreateRouteEntry api = new CreateRouteEntry(
                sessionId: sessionId,
                requestId: requestId,
        )

        String res = api.call(ecsAPIParam)

        println res

        CreateRouteEntryResponse resp = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(res, CreateRouteEntryResponse.class)
        assert resp.requestId == api.requestId
    }
}
