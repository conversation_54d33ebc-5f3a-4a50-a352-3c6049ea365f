package org.zstack.test.unittest.ecsapiadapter.snapshot

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.api.ecs.snapshot.ApplyAutoSnapshotPolicy
import org.zstack.sdk.AddSchedulerJobToSchedulerTriggerAction
import org.zstack.sdk.AddSchedulerJobToSchedulerTriggerResult
import org.zstack.sdk.CreateSchedulerJobAction
import org.zstack.sdk.CreateSchedulerJobResult
import org.zstack.sdk.QuerySchedulerJobAction
import org.zstack.sdk.QuerySchedulerJobResult
import org.zstack.sdk.SchedulerJobInventory
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil
import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * @Author: fubang
 * @Date: 2018/5/1
 */
class ApplyAutoSnapshotPolicyCase {
    @Test
    void testZStackRspToEcsRsp() {
        def jobId = "4624114de8234fab815117ec2ff11f89"
        def triggerId = "9025be79396a416690747d9a09782de9"
        CreateSchedulerJobAction.metaClass.call = {
            CreateSchedulerJobAction.Result result = new CreateSchedulerJobAction.Result()
            CreateSchedulerJobResult value = new CreateSchedulerJobResult()
            result.value = value
            value.inventory = new SchedulerJobInventory(uuid: jobId)

            return result
        }
        AddSchedulerJobToSchedulerTriggerAction.metaClass.call = {
            assert delegate.schedulerJobUuid == jobId
            assert delegate.schedulerTriggerUuid == triggerId
            AddSchedulerJobToSchedulerTriggerAction.Result result = new AddSchedulerJobToSchedulerTriggerAction.Result()
            AddSchedulerJobToSchedulerTriggerResult value = new AddSchedulerJobToSchedulerTriggerResult()
            result.value = value

            return result
        }
        Map ecsAPIParam = [
                "Action"              : "ApplyAutoSnapshotPolicy",
                "AutoSnapshotPolicyId": triggerId,
                "DiskIds"             : "['4624114de8234fab815117ec2ff11f89','47ce1d04c1614a139ab3ab3ee56ee564']",
                "AccessKeyId"         : APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        ApplyAutoSnapshotPolicy api = new ApplyAutoSnapshotPolicy()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println JSONObjectUtil.toJsonString(api.zstackAPIParamMap)

        QuerySchedulerJobAction.metaClass.call = {
            QuerySchedulerJobAction self = delegate
            assert self.sessionId == api.sessionId

            return new QuerySchedulerJobAction.Result(
                    value: new QuerySchedulerJobResult(
                            inventories: []
                    )
            )
        }

        def result = api.callZStackAction()

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
    }
}
