package org.zstack.test.unittest.ecsapiadapter.slb

import com.aliyuncs.slb.model.v20140515.CreateLoadBalancerResponse
import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterGlobalProperty
import org.zstack.pluginpremium.externalapiadapter.api.ecs.slb.CreateLoadBalancer
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils.randomUUID

/**
 * Created by <PERSON> <PERSON> on 2019/12/25
 */
class CreateLoadBalancerCase {
    String sessionId = randomUUID()
    String requestId = randomUUID()

    String loadBalancerId = randomUUID()
    String vipUuid = randomUUID()
    String address = "************"
    String vpcId = randomUUID()
    String vSwitchId = randomUUID()

    Map ecsAPIParam = [
            "Action"          : "CreateLoadBalancer",
            "LoadBalancerName": "HeHeDa",
            "Address"         : address,
    ]

    @Before
    void prepare() {
        ExternalAPIAdapterGlobalProperty.PUBLICL3NETWORKUUID = randomUUID()

        VipInventory vip = new VipInventory(
                uuid: vipUuid,
                ip: address,
                l3NetworkUuid: ExternalAPIAdapterGlobalProperty.PUBLICL3NETWORKUUID
        )

        QueryVipAction.metaClass.call = {
            QueryVipAction self = delegate
            assert self.conditions.contains("uuid=$vipUuid".toString())
            QueryVipAction.Result result
            result = new QueryVipAction.Result(
                    value: new QueryVipResult(
                            inventories: [vip]
                    )
            )
            return result
        }

        CreateVipAction.metaClass.call = {
            CreateVipAction self = delegate
            assert self.requiredIp == address
            assert self.sessionId == sessionId
            assert self.l3NetworkUuid == ExternalAPIAdapterGlobalProperty.PUBLICL3NETWORKUUID
            CreateVipAction.Result result
            result = new CreateVipAction.Result(
                    value: new CreateVipResult(
                            inventory: vip
                    )
            )
            return result
        }

        CreateLoadBalancerAction.metaClass.call = {
            CreateLoadBalancerAction self = delegate
            assert self.name == "HeHeDa"
            assert self.vipUuid == vipUuid
            CreateLoadBalancerAction.Result result
            result = new CreateLoadBalancerAction.Result(
                    value: new CreateLoadBalancerResult(
                            inventory: new LoadBalancerInventory(
                                    uuid: self.resourceUuid,
                                    vipUuid: vipUuid,
                                    listeners: [],
                                    name: "HeHeDa"
                            )
                    )
            )
            return result
        }
    }

    @Test
    void testEcsParamToZStackParam() {
        CreateLoadBalancer api = new CreateLoadBalancer(
                sessionId: sessionId,
                requestId: requestId,
                vipUuid: vipUuid,
                slbUuid: loadBalancerId
        )

        Map zstackAPIParam = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map:\n" + JSONObjectUtil.toJsonString(zstackAPIParam)

        assert zstackAPIParam.size() == 5
        assert zstackAPIParam["name"] == "HeHeDa"
        assert zstackAPIParam["vipUuid"] == vipUuid
        assert zstackAPIParam["resourceUuid"] == loadBalancerId
    }

    @Test
    void testZStackRspToEcsRsp() {
        CreateLoadBalancerAction.Result zstackActionResult
        zstackActionResult = new CreateLoadBalancerAction.Result(
                value: new CreateLoadBalancerResult(
                        inventory: new LoadBalancerInventory(
                                uuid: loadBalancerId,
                                name: "HeHeDa",
                                vipUuid: vipUuid,
                                listeners: []
                        )
                )
        )
        CreateLoadBalancer api = new CreateLoadBalancer(
                sessionId: sessionId,
                requestId: requestId,
                vipUuid: vipUuid,
                slbUuid: loadBalancerId,
                zstackAPIParamMap: [
                        "apiId": requestId
                ],
                ecsAPIParamMap: [
                        "VpcId"    : vpcId,
                        "VSwitchId": vSwitchId
                ]
        )
        Map ecsAPIRsp = api.getEcsAPIRsp(zstackActionResult)
        println "ecs api response map: "
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.size() == 8
        assert ecsAPIRsp["LoadBalancerName"] == "HeHeDa"
        assert ecsAPIRsp["RequestId"] == requestId
        assert ecsAPIRsp["Address"] == address
        assert ecsAPIRsp["NetworkType"] == "vpc"
        assert ecsAPIRsp["VpcId"] == vpcId
        assert ecsAPIRsp["VSwitchId"] == vSwitchId
        assert ecsAPIRsp["AddressIPVersion"] == "ipv4"
        assert ecsAPIRsp["LoadBalancerId"] == loadBalancerId
    }

    @Test
    void testCreateLoadBalancer() {
        CreateLoadBalancer api = new CreateLoadBalancer(
                requestId: requestId,
                sessionId: sessionId
        )
        String res = api.call(ecsAPIParam)
        println res
        CreateLoadBalancerResponse response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(res, CreateLoadBalancerResponse.class)
        assert response.requestId == requestId
        assert response.loadBalancerName == "HeHeDa"
        assert response.address == address
        assert response.networkType == "classic"
        assert response.addressIPVersion == "ipv4"
    }
}
