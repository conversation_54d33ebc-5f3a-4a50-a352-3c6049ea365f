package org.zstack.test.unittest.ecsapiadapter.snapshot

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.snapshot.DescribeAutoSnapshotPolicyEx
import org.zstack.sdk.QuerySchedulerJobAction
import org.zstack.sdk.QuerySchedulerJobResult
import org.zstack.sdk.QuerySchedulerTriggerAction
import org.zstack.sdk.QuerySchedulerTriggerResult
import org.zstack.sdk.SchedulerJobInventory
import org.zstack.sdk.SchedulerTriggerInventory
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import java.sql.Timestamp

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * @Author: fubang
 * @Date: 2018/5/1
 */
class DescribeAutoSnapshotPolicyExCase {
    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"              : "DescribeAutoSnapshotPolicyEx",
                "AutoSnapshotPolicyId": "9025be79396a416690747d9a09782de9",
                "PageSize"            : "10",
                "PageNumber"          : "1"
        ]
        DescribeAutoSnapshotPolicyEx api = new DescribeAutoSnapshotPolicyEx()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.size() == 6
        assert zstackAPIParamMap.get(ZSTACK_QUERY_CONDITIONS_KEY) != null
        assert zstackAPIParamMap.get(ZSTACK_QUERY_CONDITIONS_KEY).size == 1
        assert zstackAPIParamMap.get(ZSTACK_QUERY_CONDITIONS_KEY).contains("uuid=9025be79396a416690747d9a09782de9")
        assert zstackAPIParamMap.get(ZSTACK_QUERY_REPLYWITHCOUNT_KEY) == true
        assert zstackAPIParamMap.get(ZSTACK_QUERY_LIMIT_KEY) == ECS_QUERY_API_PAGESIZE_DEFAULT_VALUE
        assert zstackAPIParamMap.get("start") == 0
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"
        assert zstackAPIParamMap.get(ZSTACK_API_APIID_KEY) != null

        ecsAPIParam = [
                "Action"              : "DescribeAutoSnapshotPolicyEx",
                "PageSize"            : "10",
                "PageNumber"          : "1"
        ]
        zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.size() == 6
        assert zstackAPIParamMap.get(ZSTACK_QUERY_CONDITIONS_KEY) != null
        assert zstackAPIParamMap.get(ZSTACK_QUERY_CONDITIONS_KEY).size == 0
    }

    @Test
    void testZStackRspToEcsRsp() {
        QuerySchedulerJobAction.metaClass.call = {
            QuerySchedulerJobAction.Result result = new QuerySchedulerJobAction.Result()
            QuerySchedulerJobResult value = new QuerySchedulerJobResult()
            result.value = value

            value.total = 2
            value.inventories = [new SchedulerJobInventory(), new SchedulerJobInventory()]
            return result
        }

        Map ecsAPIParam = [
                "Action"              : "DescribeAutoSnapshotPolicyEx",
                "RegionId"            : "cn-shanghai",
                "AutoSnapshotPolicyId": "9025be79396a416690747d9a09782de9",
                "PageSize"            : "10",
                "PageNumber"          : "1",
                "AccessKeyId"         : APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        DescribeAutoSnapshotPolicyEx api = new DescribeAutoSnapshotPolicyEx()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        QuerySchedulerTriggerAction.Result result = new QuerySchedulerTriggerAction.Result()
        QuerySchedulerTriggerResult value = new QuerySchedulerTriggerResult()
        result.value = value

        value.total = 1
        Timestamp timestamp = new Timestamp(0, 5, 1, 0, 0, 0, 0)
        Timestamp createTime = new Timestamp(new Date().getTime())
        value.inventories = [
                new SchedulerTriggerInventory(uuid: "9025be79396a416690747d9a09782de9",
                        name: "scheduler",
                        startTime: timestamp,
                        createDate: createTime)]

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
        assert ecsAPIRsp.get("TotalCount") == value.total
        assert ecsAPIRsp.get("PageNumber") == 1
        assert ecsAPIRsp.get("PageSize") == 10
        assert ecsAPIRsp.get("AutoSnapshotPolicies") != null
        assert ecsAPIRsp.get("AutoSnapshotPolicies").get("AutoSnapshotPolicy").size == 1

        Map tmp = ecsAPIRsp.get("AutoSnapshotPolicies").get("AutoSnapshotPolicy").get(0)
        assert tmp.get("AutoSnapshotPolicyId") == "9025be79396a416690747d9a09782de9"
        assert tmp.get("RegionId") == "cn-shanghai"
        assert tmp.get("AutoSnapshotPolicyName") == "scheduler"
        assert tmp.get("TimePoints") == "[\"0\"]"
        assert tmp.get("RepeatWeekdays") == "[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]"
        assert tmp.get("RetentionDays") == -1
        assert tmp.get("DiskNums") == 2
        assert tmp.get("Status") == "Available"
        assert tmp.get("CreationTime") == createTime
    }
}
