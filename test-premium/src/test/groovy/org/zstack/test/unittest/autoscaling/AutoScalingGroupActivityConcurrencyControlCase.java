package org.zstack.test.unittest.autoscaling;

import org.junit.Test;
import org.zstack.autoscaling.group.activity.AutoScalingGroupActivityConcurrencyControl;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by lining on 2018/10/18.
 */
public class AutoScalingGroupActivityConcurrencyControlCase {

    @Test
    public void testConcurrencyControl() throws InterruptedException {
         List<String> tokens = Collections.synchronizedList(new LinkedList<String>());

        int threadCount  = 20;
        Thread[] threads = new Thread[threadCount];
        for (int i = 0; i < threadCount; i++) {
            Thread thread = new Thread(() -> {
                String token = "token_" + new Random().nextInt(2);
                if (AutoScalingGroupActivityConcurrencyControl.addToken(token)) {
                    tokens.add(token);
                }
            });
            threads[i] = thread;
        }

        for (int i = 0; i < threadCount; i++) {
            threads[i].start();
        }

        TimeUnit.SECONDS.sleep(3);
        System.out.println(tokens);
        assert tokens.size() == 2;
        assert tokens.containsAll(Arrays.asList("token_0", "token_1"));
    }

}
