package org.zstack.test.unittest.ecsapiadapter.disk

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.api.ecs.disk.AttachDisk
import org.zstack.sdk.AttachDataVolumeToVmAction
import org.zstack.sdk.AttachDataVolumeToVmResult
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * @Author: fubang
 * @Date: 2018/4/24
 */
class AttachDiskCase {
    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"            : "AttachDisk",
                "InstanceId"        : "ebb57b74d1e94a1db2b50bc5bb1062af",
                "DiskId"            : "9025be79396a416690747d9a09782de9",
                "DeleteWithInstance": "false"
        ]
        AttachDisk api = new AttachDisk()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.size() == 4
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"
        assert zstackAPIParamMap.get(ZSTACK_VM_INSTANCE_UUID) == "ebb57b74d1e94a1db2b50bc5bb1062af"
        assert zstackAPIParamMap.get(ZSTACK_DISK_UUID) == "9025be79396a416690747d9a09782de9"
        assert zstackAPIParamMap.get(ZSTACK_API_APIID_KEY) != null
    }

    @Test
    void testZStackRspToEcsRsp() {
        Map ecsAPIParam = [
                "Action"     : "AttachDisk",
                "InstanceId" : "ebb57b74d1e94a1db2b50bc5bb1062af",
                "DiskId"     : "9025be79396a416690747d9a09782de9",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        AttachDisk api = new AttachDisk()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        AttachDataVolumeToVmAction.Result result = new AttachDataVolumeToVmAction.Result()
        AttachDataVolumeToVmResult value = new AttachDataVolumeToVmResult()
        result.value = value

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
    }
}
