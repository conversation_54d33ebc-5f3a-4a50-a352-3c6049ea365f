package org.zstack.test.unittest.utils;

import org.junit.Test;
import org.zstack.zwatch.utils.ParserUtils;

import java.util.Map;

/**
 * @Author: qiuyu.zhang
 * @Date: 2024/6/4 11:41
 */
public class ParserUtilsCase {
    @Test
    public void testParser() {
        String gpuIdentifyLabel = "::GpuSerialNumber:1322620070289::::HostUuid:2f81278ee5fc4c07a821a9a3c55914d3::::PciDeviceAddress:0000:65:00.0::";
        Map<String, String> map = ParserUtils.parseIdentifyLabel(gpuIdentifyLabel);

        assert map.get("GpuSerialNumber").equals("1322620070289");
        assert map.get("HostUuid").equals("2f81278ee5fc4c07a821a9a3c55914d3");
        assert map.get("PciDeviceAddress").equals("0000:65:00.0");
    }
}
