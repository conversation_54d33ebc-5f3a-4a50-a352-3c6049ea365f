package org.zstack.test.unittest.zwatch;

import org.junit.Test;
import org.zstack.core.Platform;
import org.zstack.zwatch.datatype.Datapoint;
import org.zstack.zwatch.namespace.VmNamespace;

import java.util.*;

/**
 * Created by kayo on 2018/10/19.
 * test generate average with data set including 10k data point 20 resources cost memory: 37.9MB cost time: 10ms
 * test generate average with data set including 100k data point 20 resources cost memory: 437MB (contains 100k data)  cost time: 19ms
 */
public class TestAlarmAverageAlgorithm {
    private List<Datapoint> generateDataSet(int size, int resourceRange, double valueRange) {
        List<String> uuids = new ArrayList<>();
        List<Datapoint> dps = new ArrayList<>();

        Random random = new Random(resourceRange);
        Random valueRandom = new Random(100);

        for (int i = 0; i < resourceRange; i++) {
            uuids.add(Platform.getUuid());
        }

        for (int i = 0; i < size; i++) {
            Map<String, String> labels = new HashMap<>();
            labels.put("cpu", "cpu" + random.nextInt(resourceRange));
            labels.put(VmNamespace.LabelNames.VMUuid.toString(), uuids.get(random.nextInt(resourceRange)));

            Datapoint datapoint = new Datapoint();
            datapoint.setValue(valueRandom.nextDouble());
            datapoint.setLabels(labels);
            dps.add(datapoint);
        }

        return dps;
    }

    @Test
    public void test() {
        List<Datapoint> data = generateDataSet(10000, 20, 100);

        OptionalDouble optional = data.stream().map(Datapoint::getValue).mapToDouble(a -> a)
                .average();

        assert optional.isPresent();
    }
}
