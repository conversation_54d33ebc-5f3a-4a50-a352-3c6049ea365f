package org.zstack.test.unittest.ecsapiadapter.others

import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterGlobalConfig
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterGlobalProperty
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.APIAdapter
import org.zstack.pluginpremium.externalapiadapter.api.APIError
import org.zstack.pluginpremium.externalapiadapter.api.ecs.image.CreateImage
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils

import java.sql.Timestamp

/**
 * Created by lining on 2018/5/8.
 */
class ExternalAPIAdapterExceptionCase {

    @Before
    void prepare() {
        ExternalAPIAdapterGlobalConfig.ENABLE_SIGNATURE_CHECKING.metaClass.value { Class clz ->
            assert clz == Boolean.class
            return true
        }
    }

    @Test
    void testMissingMandatoryParameter() {
        ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL = "127.0.0.1"

        Map ecsAPIParamMap = [
                "Action"     : "DescribeEndpoints",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]

        String result = new APIAdapter().callZStackAPI(ecsAPIParamMap).error
        println(result)

        APIError error = ExternalAPIAdapterUtils.gson.fromJson(result, APIError.class)
        assert error.code == ExternalAPIAdapterConstants.ECSErrorCode.InvalidParameter
        assert error.requestId != null
        assert error.hostId == ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL
        assert error.message.contains("mandatory for processing this request is not supplied")

    }

    @Test
    void testInvalidParameter() {
        ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL = "127.0.0.1"

        Map ecsAPIParamMap = [
                "Action"     : "ErrorActionName",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY,
                "Signature"  : "signature"
        ]

        String result = new APIAdapter().callZStackAPI(ecsAPIParamMap).error
        println(result)

        APIError error = ExternalAPIAdapterUtils.gson.fromJson(result, APIError.class)
        assert error.code == ExternalAPIAdapterConstants.ECSErrorCode.ApiUnsupported
        assert error.requestId != null
        assert error.hostId == ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL
        assert error.message.contains("Unknown action")
    }

    @Test
    void testQueryZStackSDKError() {
        ExternalAPIAdapterGlobalProperty.ZSTACK_ACCOUNT_PASSWORD_ACCESSKEY_ACCESSSECRET = "admin::xxxxx::${APIAdapterTestUtils.ECS_ACCESSKEY}::${APIAdapterTestUtils.ECS_ACCESSSECRET}".toString()

        LogInByAccountAction.metaClass.call = {
            return new LogInByAccountAction.Result(
                    value: new LogInResult(
                            inventory: new SessionInventory(
                                    uuid: ExternalAPIAdapterUtils.randomUUID(),
                                    expiredDate: new Timestamp(new Date().getTime())
                            )
                    )
            )
        }

        QueryVolumeAction.metaClass.call = {
            return new QueryVolumeAction.Result(
                    value: new QueryVolumeResult(
                            inventories: []
                    )
            )
        }

        Map ecsAPIParamMap = [
                "Action"     : CreateImage.class.getSimpleName(),
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY,
                "InstanceId": ExternalAPIAdapterUtils.randomUUID()
        ]
        String signature = ExternalAPIAdapterUtils.makeECSAPISignature(ecsAPIParamMap, APIAdapterTestUtils.ECS_ACCESSSECRET, "GET")
        ecsAPIParamMap.put(ExternalAPIAdapterConstants.ECS_API_SIGNATURE_KEY, signature)


        String result = new APIAdapter().callZStackAPI(ecsAPIParamMap).error
        println(result)

        APIError error = ExternalAPIAdapterUtils.gson.fromJson(result, APIError.class)
        assert error.code == ExternalAPIAdapterConstants.ECSErrorCode.InvalidParameter
        assert error.requestId != null
        assert error.hostId == ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL
        assert error.message == "Parameter \" InstanceId \" processing error, The vm do not exist"
    }

    @Test
    void testCallZStackActionFail() {
        def logInByAccountActionCall = LogInByAccountAction.metaClass.call
        def queryVolumeActionCall = QueryVolumeAction.metaClass.call
        def createRootVolumeTemplateFromRootVolumeActionCall = CreateRootVolumeTemplateFromRootVolumeAction.metaClass.call

        ExternalAPIAdapterGlobalProperty.ZSTACK_ACCOUNT_PASSWORD_ACCESSKEY_ACCESSSECRET = "admin::xxxxx::${APIAdapterTestUtils.ECS_ACCESSKEY}::${APIAdapterTestUtils.ECS_ACCESSSECRET}".toString()

        LogInByAccountAction.metaClass.call = {
            return new LogInByAccountAction.Result(
                    value: new LogInResult(
                            inventory: new SessionInventory(
                                    uuid: ExternalAPIAdapterUtils.randomUUID(),
                                    expiredDate: new Timestamp(new Date().getTime())
                            )
                    )
            )
        }

        QueryVolumeAction.metaClass.call = {
            return new QueryVolumeAction.Result(
                    value: new QueryVolumeResult(
                            inventories: [
                                    new VolumeInventory(
                                            uuid: ExternalAPIAdapterUtils.randomUUID()
                                    )
                            ]
                    )
            )
        }

        Map ecsAPIParamMap = [
                "Action"     : CreateImage.class.getSimpleName(),
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY,
                "InstanceId": ExternalAPIAdapterUtils.randomUUID()
        ]
        String signature = ExternalAPIAdapterUtils.makeECSAPISignature(ecsAPIParamMap, APIAdapterTestUtils.ECS_ACCESSSECRET, "GET")
        ecsAPIParamMap.put(ExternalAPIAdapterConstants.ECS_API_SIGNATURE_KEY, signature)

        String result = new APIAdapter().callZStackAPI(ecsAPIParamMap).error
        println(result)

        APIError error = ExternalAPIAdapterUtils.gson.fromJson(result, APIError.class)
        assert error.code == ExternalAPIAdapterConstants.ECSErrorCode.InternalError
        assert error.requestId != null
        assert error.hostId == ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL
        assert error.message.contains("Internal error occurred")

        LogInByAccountAction.metaClass.call = logInByAccountActionCall
        QueryVolumeAction.metaClass.call = queryVolumeActionCall
        CreateRootVolumeTemplateFromRootVolumeAction.metaClass.call = createRootVolumeTemplateFromRootVolumeActionCall
    }
}
