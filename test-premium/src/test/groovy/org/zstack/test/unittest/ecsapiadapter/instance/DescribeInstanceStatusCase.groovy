package org.zstack.test.unittest.ecsapiadapter.instance

import com.aliyuncs.ecs.model.v20140526.DescribeInstanceStatusResponse
import org.junit.Test
import org.zstack.header.vm.VmInstanceConstant
import org.zstack.header.vm.VmInstanceState
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.instance.DescribeInstanceStatus
import org.zstack.sdk.ClusterInventory
import org.zstack.sdk.QueryClusterAction
import org.zstack.sdk.QueryClusterResult
import org.zstack.sdk.QueryVmInstanceAction
import org.zstack.sdk.QueryVmInstanceResult
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils

/**
 * Created by lining on 06/26/2018.
 */
class DescribeInstanceStatusCase {

    @Test
    void basicTest() {
        String regionUuid = ExternalAPIAdapterUtils.randomUUID()
        String regionId = "ecs.ffff.kkk"
        String zoneUuid = ExternalAPIAdapterUtils.randomUUID()
        String zoneId = "dulu"
        String clusterUuid = ExternalAPIAdapterUtils.randomUUID()
        String clusterId = "AZ-1"
        String vmUuid = ExternalAPIAdapterUtils.randomUUID()
        String vmState = VmInstanceState.Starting.toString()

        Map ecsAPIParam = [
                "Action"     : "DescribeInstanceStatus",
                'RegionId'   : zoneId,
                'ZoneId'     : clusterId,
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]

        DescribeInstanceStatus api = new DescribeInstanceStatus()
        api.setSessionId("123123")
        api.setRequestId("321321")

        QueryVmInstanceAction.metaClass.call = {
            QueryVmInstanceAction self = delegate
            assert self.apiId == api.requestId
            assert self.sessionId == api.sessionId
            assert self.conditions.contains("clusterUuid=$clusterUuid".toString())
            assert self.conditions.contains("type=${VmInstanceConstant.USER_VM_TYPE}".toString())

            return new QueryVmInstanceAction.Result(
                    value: new QueryVmInstanceResult(
                            inventories: [
                                    new VmInstanceInventory(
                                            uuid: vmUuid,
                                            state: vmState
                                    )
                            ],
                            total: 1
                    )
            )
        }

        QueryClusterAction.metaClass.call = {
            QueryClusterAction.Result result
            result = new QueryClusterAction.Result(
                    value: new QueryClusterResult(
                            inventories: [
                                    new ClusterInventory(
                                            uuid: clusterUuid,
                                            name: clusterId
                                    )
                            ]
                    )
            )
            return result
        }

        String resultStr = api.call(ecsAPIParam)
        println(resultStr)

        DescribeInstanceStatusResponse response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(resultStr, DescribeInstanceStatusResponse.class)
        assert response.instanceStatuses.size() == 1
        assert response.instanceStatuses.get(0).status == vmState
        assert response.instanceStatuses.get(0).instanceId == vmUuid
        assert response.totalCount == 1
        assert response.pageNumber == 1
        assert response.pageSize == 10
    }
}
