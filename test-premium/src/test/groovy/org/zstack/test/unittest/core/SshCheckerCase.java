package org.zstack.test.unittest.core;

import org.junit.Before;
import org.junit.Test;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.ansible.SshFileMd5Checker;
import org.zstack.utils.ShellResult;
import org.zstack.utils.ShellUtils;
import org.zstack.utils.ssh.Ssh;
import org.zstack.utils.ssh.SshCmdHelper;

public class SshCheckerCase {
    private SshFileMd5Checker checker;
    private Ssh sshMock;

    @Before
    public void setUp() {
        CoreGlobalProperty.UNIT_TEST_ON = true;

        checker = new SshFileMd5Checker();
        checker.setUsername("root");
        checker.setPassword("password");
        checker.setTargetIp("127.0.0.1");
        checker.setSshPort(22);

    }


    @Test
    public void testNeedDeploy_whenMd5Match() {
        if (!checkLocalPswdCorrect()) {
            return;
        }

        String srcFilePath = "/tmp/srcFile";
        String destFilePath = "/tmp/destFile";
        ShellUtils.runAndReturn("touch " + srcFilePath);
        ShellUtils.runAndReturn("touch " + destFilePath);

        checker.addSrcDestPair(srcFilePath, destFilePath);
        assert !checker.needDeploy();


        ShellUtils.runAndReturn("rm -f " + srcFilePath);
        ShellUtils.runAndReturn("rm -f " + destFilePath);
    }

    public boolean checkLocalPswdCorrect() {
        return ShellUtils.runAndReturn("sshpass -p password ssh 127.0.0.1 true").getRetCode() == 0;
    }

    @Test
    public void testSshWrapCmd() {
        String cmd = "true";

        String afterCmd = SshCmdHelper.wrapSudoCmd(cmd, "testuser", "test;';as;d!@#123");
        ShellResult ret = ShellUtils.runAndReturn(afterCmd);
        assert afterCmd.contains(cmd);
        assert afterCmd.contains("echo 'test;'\\'';as;d!@#123'");
        assert ret.getRetCode() == 0;
        assert ret.getStderr().isEmpty();
        assert ret.getStdout().isEmpty();

        afterCmd = SshCmdHelper.wrapSudoCmd(cmd, "root", "test;';as;d!@#123");
        ret = ShellUtils.runAndReturn(afterCmd);
        assert afterCmd.equals(cmd);
        assert ret.getRetCode() == 0;
        assert ret.getStderr().isEmpty();
        assert ret.getStdout().isEmpty();

        assert SshCmdHelper.removeSensitiveInfoFromCmd(SshCmdHelper.wrapSudoCmd(cmd, "testuser", "test;';as;d!@#123")).contains("***");
    }

}
