package org.zstack.test.unittest.mevoco.volume

import org.junit.Test
import org.zstack.header.volume.VolumeQos
import org.zstack.mevoco.VolumeQosHelper

/**
 * Created by <PERSON> on 2021/12/23
 */
class VolumeQosUnitCase {

    @Test
    void testVolumeQosEquals() {
        VolumeQos q1 = new VolumeQos(1, 1, 1, 1, 1, 1)
        VolumeQos q2 = new VolumeQos(1, 1, 1, 1, 1, 1)
        assert q1.equals(q2)
        q2.readBandwidth = 2
        assert !q1.equals(q2)
        q2.readBandwidth = 1
        q2.writeBandwidth = 2
        assert !q1.equals(q2)
        q2.writeBandwidth = 1
        q2.totalBandwidth = 2
        assert !q1.equals(q2)
        q2.totalBandwidth = 1
        q2.readIOPS = 2
        assert !q1.equals(q2)
        q2.readIOPS = 1
        q2.writeIOPS = 2
        assert !q1.equals(q2)
        q2.writeIOPS = 1
        q2.totalIOPS = 2
        assert !q1.equals(q2)
        assert !q1.equals(new Object())

    }

    @Test
    void testHasQosLimit() {
        VolumeQos q = new VolumeQos()
        assert !VolumeQosHelper.hasQosLimit(q)
        q.totalIOPS = 1
        assert VolumeQosHelper.hasQosLimit(q)
        q.totalIOPS = -1
        assert !VolumeQosHelper.hasQosLimit(q)
        q.readIOPS = 1
        assert VolumeQosHelper.hasQosLimit(q)
        q.readIOPS = -1
        assert !VolumeQosHelper.hasQosLimit(q)
        q.writeIOPS = 1
        assert VolumeQosHelper.hasQosLimit(q)
        q.writeIOPS = -1
        assert !VolumeQosHelper.hasQosLimit(q)
        q.totalBandwidth = 1
        assert VolumeQosHelper.hasQosLimit(q)
        q.totalBandwidth = -1
        assert !VolumeQosHelper.hasQosLimit(q)
        q.readBandwidth = 1
        assert VolumeQosHelper.hasQosLimit(q)
        q.readBandwidth = -1
        assert !VolumeQosHelper.hasQosLimit(q)
        q.writeBandwidth = 1
        assert VolumeQosHelper.hasQosLimit(q)
        q.writeBandwidth = -1
        assert !VolumeQosHelper.hasQosLimit(q)
    }
}
