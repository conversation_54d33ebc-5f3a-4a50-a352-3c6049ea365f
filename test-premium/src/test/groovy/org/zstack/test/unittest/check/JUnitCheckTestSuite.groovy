package org.zstack.test.unittest.check

import org.junit.runner.JUnitCore
import org.junit.runner.Result
import org.junit.runner.RunWith
import org.junit.runner.notification.Failure
import org.junit.runners.Suite
import org.zstack.test.unittest.doc.CheckAPIDocSuccessAndErrorCase
import org.zstack.test.unittest.globalconfig.GenerateGlobalConfigTemplateAndMarkDownCase

import java.util.stream.Collectors

@RunWith(Suite.class)
@Suite.SuiteClasses([
        AuditCheckerCase.class,
        CheckAPIDocSuccessAndErrorCase.class,
        GenerateGlobalConfigTemplateAndMarkDownCase.class,
        RBACCheckCase.class,
        SystemTagCheckerCase.class,
        CheckToolSumCase.class
])
class JUnitCheckTestSuite {
    static void runAllTestCases() {
        Result result = JUnitCore.runClasses(JUnitCheckTestSuite.class)

        List<Failure> failures = result.getFailures()
        if (!failures.isEmpty()) {
            List<String> errors = failures.stream().map{failure -> failure.toString()}.collect(Collectors.toList())
            assert false : "JUnit test fail, " +  errors.toString()
        }
    }
}
