package org.zstack.test.unittest.ansible;

import org.junit.Test;
import org.zstack.appcenter.AppCenterGlobalProperty;
import org.zstack.appcenter.buildsystem.AppBuildSystemDeployArguments;
import org.zstack.appliancevm.ApplianceVmDeployArguments;
import org.zstack.appliancevm.ApplianceVmGlobalProperty;
import org.zstack.baremetal.pxeserver.BaremetalDeployArguments;
import org.zstack.console.ConsoleGlobalProperty;
import org.zstack.console.ConsoleProxyDeployArguments;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.ansible.AnsibleBasicArguments;
import org.zstack.header.baremetal.pxeserver.BaremetalPxeServerGlobalProperty;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.vpc.VpcConstants;
import org.zstack.kvm.KVMGlobalProperty;
import org.zstack.kvm.KVMHostDeployArguments;
import org.zstack.network.service.virtualrouter.VirtualRouterGlobalProperty;
import org.zstack.network.service.virtualrouter.lifecycle.VirtualRouterDeployArguments;
import org.zstack.premium.externalservice.loki.PromtailDeployArguments;
import org.zstack.storage.backup.imagestore.ImageStoreAgentDeployArguments;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageGlobalProperty;
import org.zstack.storage.backup.sftp.SftpBackupStorageDeployArguments;
import org.zstack.storage.backup.sftp.SftpBackupStorageGlobalProperty;
import org.zstack.storage.ceph.CephGlobalProperty;
import org.zstack.storage.ceph.backup.CephBackupStorageDeployArguments;
import org.zstack.storage.ceph.primary.CephPrimaryStorageDeployArguments;
import org.zstack.storage.primary.sharedblock.SharedBlockConstants;
import org.zstack.storage.primary.sharedblock.SharedBlockDeployArguments;
import org.zstack.vpc.VpcDeployArguments;

import java.util.Collections;
import java.util.LinkedHashMap;

import static org.junit.Assert.assertThrows;

public class AnsibleArgumentsCase {
    @Test
    public void test() {
        prepareChronyConfiguration();
        testAppBuildSystemDeployArguments();
        testApplianceVmDeployArguments();
        testBareMetalArguments();
        testCephBackupStorageDeployArguments();
        testCephPrimaryStorageDeployArguments();
        testConsoleProxyDeployArguments();
        testImageStoreAgentDeployArguments();
        testKVMHostDeployArguments();
        testPromtailDeployArguments();
        testSftpBackupStorageDeployArguments();
        testSharedBlockDeployArguments();
        testVirtualRouterDeployArguments();
        testVpcDeployArguments();
        testEmptyChronyConfig();
        testBasicAnsibleArguments();
    }

    private void testBasicAnsibleArguments() {
        AnsibleBasicArguments arguments = new AnsibleBasicArguments();
        arguments.setRemotePort("1080");
        arguments.setRemoteUser("root");
        arguments.setTrustedHost("127.0.0.1");
        arguments.setRemotePass("password");
        arguments.setPipUrl("http:127.0.0.1/pip_url");
        arguments.setYumServer("*********");

        LinkedHashMap map = arguments.toArgumentMap();
        assert map.get("remote_user").equals(arguments.getRemoteUser());
        assert map.get("remote_port").equals(arguments.getRemotePort());
        assert map.get("remote_pass").equals(arguments.getRemotePass());
        assert map.get("pip_url").equals(arguments.getPipUrl());
        assert map.get("yum_server").equals(arguments.getYumServer());
        assert map.get("trusted_host").equals(arguments.getTrustedHost());
    }

    private void testEmptyChronyConfig() {
        CoreGlobalProperty.CHRONY_SERVERS = null;
        assertThrows(CloudRuntimeException.class, SftpBackupStorageDeployArguments::new);

        CoreGlobalProperty.CHRONY_SERVERS = Collections.emptyList();
        assertThrows(CloudRuntimeException.class, SftpBackupStorageDeployArguments::new);
    }

    private void prepareChronyConfiguration() {
        CoreGlobalProperty.SYNC_NODE_TIME = true;
        CoreGlobalProperty.CHRONY_SERVERS = Collections.singletonList("127.0.0.1");
    }

    private void checkChronyInArgMap(LinkedHashMap map) {
        assert map.get("chrony_servers").equals("127.0.0.1");
    }

    private void testVpcDeployArguments() {
        VpcDeployArguments arguments = new VpcDeployArguments();
        Long timeout = 111L;
        arguments.setTimeout(timeout);
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_zsn").equals(VpcConstants.AGENT_PACKAGE_NAME);
        assert argMap.get("tmout") == timeout;
        checkChronyInArgMap(argMap);
        assert argMap.size() == 3;
    }

    private void testVirtualRouterDeployArguments() {
        VirtualRouterGlobalProperty.AGENT_PACKAGE_NAME = "virtualrouter.tar.gz";
        VirtualRouterDeployArguments arguments = new VirtualRouterDeployArguments();
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_virtualrouter").equals(VirtualRouterGlobalProperty.AGENT_PACKAGE_NAME);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 2;
    }

    private void testSharedBlockDeployArguments() {
        SharedBlockDeployArguments arguments = new SharedBlockDeployArguments();
        long freeSpace = 123L;
        arguments.setFreeSpace(freeSpace);
        long increment = 234L;
        arguments.setIncrement(increment);
        long utilizationPercent = 345L;
        arguments.setUtilizationPercent(utilizationPercent);
        long maxLockButNotUsedTimes = 567L;
        arguments.setMaxLockButNotUsedTimes(maxLockButNotUsedTimes);
        int lvLkProtectionPeriodInSec = 111;
        arguments.setLvLkProtectionPeriodInSec(lvLkProtectionPeriodInSec);
        long scanInterval = 678L;
        arguments.setScanInterval(scanInterval);
        String verboseLog = "verbose_log";
        arguments.setVerboseLog(verboseLog);

        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_zsblk").equals(SharedBlockConstants.AGENT_PACKAGE_NAME);
        assert (long) argMap.get("free_spcae") == freeSpace;
        assert (long) argMap.get("increment") == increment;
        assert (long) argMap.get("utilization_percent") == utilizationPercent;
        assert (long) argMap.get("maxLockButNotUsedTimes") == maxLockButNotUsedTimes;
        assert (long) argMap.get("scanInterval") == scanInterval;
        assert (int) argMap.get("lvLkProtectionPeriodInSec") == lvLkProtectionPeriodInSec;
        assert argMap.get("verboseLog").equals(verboseLog);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 8;
    }

    private void testSftpBackupStorageDeployArguments() {
        SftpBackupStorageGlobalProperty.AGENT_PACKAGE_NAME = "sftpbackupstorage.tar.gz";
        SftpBackupStorageDeployArguments arguments = new SftpBackupStorageDeployArguments();
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_sftpbackupstorage").equals(SftpBackupStorageGlobalProperty.AGENT_PACKAGE_NAME);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 2;
    }

    private void testPromtailDeployArguments() {
        PromtailDeployArguments arguments = new PromtailDeployArguments();
        String pkgPromtail = "pkgPromtail";
        arguments.setPkgPromtail(pkgPromtail);
        String srcPrimtailBin = "srcPrimtailBin";
        arguments.setSrcPromtailBin(srcPrimtailBin);
        String dstPrimtailBin = "dstPrimtailBin";
        arguments.setDstPromtailBin(dstPrimtailBin);

        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("src_promtail_bin").equals(srcPrimtailBin);
        assert argMap.get("dst_promtail_bin").equals(dstPrimtailBin);
        assert argMap.get("pkg_promtail").equals(pkgPromtail);
        assert argMap.size() == 3;
    }

    private void testKVMHostDeployArguments() {
        KVMGlobalProperty.AGENT_PACKAGE_NAME = "kvmagent.tar.gz";
        KVMHostDeployArguments arguments = new KVMHostDeployArguments();
        arguments.setInit(Boolean.FALSE.toString());
        arguments.setDisableIp6Tables(Boolean.TRUE.toString());
        arguments.setIsBareMetal2Gateway(Boolean.FALSE.toString());
        arguments.setBridgeDisableIptables(Boolean.TRUE.toString());
        arguments.setEnableSpiceTls(Boolean.TRUE.toString());
        String hostname = "127.0.0.1";
        arguments.setHostname(hostname);
        String skipPackages = "skipPackages";
        arguments.setSkipPackages(skipPackages);
        String updatePackages = "updatePackages";
        arguments.setUpdatePackages(updatePackages);
        String postUrl = "postUrl";
        arguments.setPostUrl(postUrl);

        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_kvmagent").equals(KVMGlobalProperty.AGENT_PACKAGE_NAME);
        assert !Boolean.parseBoolean(String.valueOf(argMap.get("init")));
        assert Boolean.parseBoolean(String.valueOf(argMap.get("skipIpv6")));
        assert Boolean.parseBoolean(String.valueOf(argMap.get("isMini")));
        assert !Boolean.parseBoolean(String.valueOf(argMap.get("isBareMetal2Gateway")));
        assert Boolean.parseBoolean(String.valueOf(argMap.get("bridgeDisableIptables")));
        assert argMap.get("hostname").equals(hostname);
        assert argMap.get("skip_packages").equals(skipPackages);
        assert argMap.get("update_packages").equals(updatePackages);
        assert argMap.get("post_url").equals(postUrl);
        assert !Boolean.parseBoolean(String.valueOf(argMap.get("enable_spice_tls")));
        checkChronyInArgMap(argMap);
        assert argMap.size() == 11;
    }

    private void testImageStoreAgentDeployArguments() {
        ImageStoreBackupStorageGlobalProperty.AGENT_PACKAGE_NAME = "zstack-store.bin";
        ImageStoreAgentDeployArguments arguments = new ImageStoreAgentDeployArguments();
        String client = "false";
        arguments.setClient(client);
        String skipPackages = "skipPackages";
        arguments.setSkipPackages(skipPackages);
        String maxCapacity = "maxCapacity";
        arguments.setMaxCapacity(maxCapacity);
        String rootFS = "rootFS";
        arguments.setFsRootPath(rootFS);
        LinkedHashMap argMap = arguments.toArgumentMap();

        assert argMap.get("pkg_imagestorebackupstorage").equals(ImageStoreBackupStorageGlobalProperty.AGENT_PACKAGE_NAME);
        assert argMap.get("skip_packages").equals(skipPackages);
        assert argMap.get("max_capacity").equals(maxCapacity);
        assert argMap.get("fs_rootpath").equals(rootFS);
        assert argMap.get("client").equals(client);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 6;
    }

    private void testConsoleProxyDeployArguments() {
        ConsoleGlobalProperty.AGENT_PACKAGE_NAME = "consoleproxy.tar.gz";
        ConsoleProxyDeployArguments arguments = new ConsoleProxyDeployArguments();
        int consoleProxyPort = 4900;
        arguments.setHttpConsoleProxyPort(String.valueOf(consoleProxyPort));
        LinkedHashMap argMap = arguments.toArgumentMap();

        assert argMap.get("pkg_consoleproxy").equals(ConsoleGlobalProperty.AGENT_PACKAGE_NAME);
        assert Integer.parseInt((String) argMap.get("http_console_proxy_port")) == consoleProxyPort;
        checkChronyInArgMap(argMap);
        assert argMap.size() == 3;
    }

    private void testCephPrimaryStorageDeployArguments() {
        CephGlobalProperty.PRIMARY_STORAGE_PACKAGE_NAME = "cephprimarystorage.tar.gz";
        CephPrimaryStorageDeployArguments arguments = new CephPrimaryStorageDeployArguments();
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_cephpagent").equals(CephGlobalProperty.PRIMARY_STORAGE_PACKAGE_NAME);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 2;
    }

    private void testCephBackupStorageDeployArguments() {
        CephGlobalProperty.BACKUP_STORAGE_PACKAGE_NAME = "cephbackupstorage.tar.gz";
        CephBackupStorageDeployArguments arguments = new CephBackupStorageDeployArguments();
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_cephbagent").equals(CephGlobalProperty.BACKUP_STORAGE_PACKAGE_NAME);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 2;
    }

    private void testBareMetalArguments() {
        BaremetalPxeServerGlobalProperty.AGENT_PACKAGE_NAME = "baremetalpxeserver.tar.gz";
        BaremetalDeployArguments arguments = new BaremetalDeployArguments();
        String updatePackages = "updatePackages";
        arguments.setUpdatePackages(updatePackages);
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_baremetalpxeserver").equals(BaremetalPxeServerGlobalProperty.AGENT_PACKAGE_NAME);
        assert argMap.get("update_packages").equals(updatePackages);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 3;
    }

    private void testAppBuildSystemDeployArguments() {
        AppCenterGlobalProperty.BUILD_SYSTEM_PACKAGE_NAME = "appbuildsystem.tar.gz";
        AppBuildSystemDeployArguments arguments = new AppBuildSystemDeployArguments();
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_appbuildsystemagent").equals(AppCenterGlobalProperty.BUILD_SYSTEM_PACKAGE_NAME);
        assert argMap.size() == 1;
    }

    private void testApplianceVmDeployArguments() {
        ApplianceVmGlobalProperty.AGENT_PACKAGE_NAME = "appliancevm.tar.gz";
        ApplianceVmDeployArguments arguments = new ApplianceVmDeployArguments();
        LinkedHashMap argMap = arguments.toArgumentMap();
        assert argMap.get("pkg_appliancevm").equals(ApplianceVmGlobalProperty.AGENT_PACKAGE_NAME);
        checkChronyInArgMap(argMap);
        assert argMap.size() == 2;
    }
}
