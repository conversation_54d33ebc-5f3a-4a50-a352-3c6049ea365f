package org.zstack.test.unittest.globalconfig

import org.junit.Test
import org.zstack.rest.sdk.DocumentGenerator
import org.zstack.utils.GroovyUtils

/**
 @Description
  <AUTHOR>
 @create 2021-08-12 15:10
 */
class GenerateGlobalConfigTemplateAndMarkDownCase {

    @Test
    void testTemplateAndMarkDown() {
        DocumentGenerator rg = GroovyUtils.newInstance("scripts/RestDocumentationGenerator.groovy")
        rg.testGlobalConfigTemplateAndMarkDown()
    }

}
