package org.zstack.test.unittest.ecsapiadapter.slb

import com.aliyuncs.slb.model.v20140515.AddBackendServersResponse
import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.api.ecs.slb.AddBackendServers
import org.zstack.pluginpremium.externalapiadapter.datatypes.SLBBackendServer
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils.randomUUID

/**
 * Created by <PERSON> <PERSON> on 2019/12/25
 */
class AddBackendServersCase {
    String sessionId = randomUUID()
    String requestId = randomUUID()
    String loadBalancerId = randomUUID()
    String serverId0 = randomUUID()
    String nicId00 = randomUUID()
    String nicId01 = randomUUID()
    String serverId1 = randomUUID()
    String nicId10 = randomUUID()
    String serverId2 = randomUUID()
    String nicId20 = randomUUID()
    String listenerId = randomUUID()
    String dl3Uuid = randomUUID()
    String l3Uuid = randomUUID()

    List<Map> instances = [
            [
                    "ServerId": serverId0,
                    "Type"    : "ecs",
                    "Weight"  : 30,
            ],
            [
                    "ServerId": serverId1,
                    "Type"    : "ecs",
                    "Weight"  : 50,
            ],
            [
                    "ServerId": nicId20,
                    "Type"    : "eni",
                    "Weight"  : 80,
            ]
    ]

    Map ecsAPIParam = [
            "Action"        : "AddBackendServers",
            "LoadBalancerId": loadBalancerId,
            "BackendServers": JSONObjectUtil.toJsonString(instances),
    ]

    @Before
    void prepare() {
        QueryVmInstanceAction.metaClass.call = {
            QueryVmInstanceAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.first().contains(serverId0) || self.conditions.first().contains(serverId1)
            QueryVmInstanceAction.Result result
            result = new QueryVmInstanceAction.Result(
                    value: new QueryVmInstanceResult(
                            inventories: [
                                    new VmInstanceInventory(
                                            uuid: serverId0,
                                            defaultL3NetworkUuid: dl3Uuid,
                                            vmNics: [
                                                    new VmNicInventory(
                                                            uuid: nicId00,
                                                            l3NetworkUuid: dl3Uuid,
                                                            deviceId: 0
                                                    ),
                                                    new VmNicInventory(
                                                            uuid: nicId01,
                                                            l3NetworkUuid: l3Uuid,
                                                            deviceId: 1
                                                    )
                                            ]
                                    ),
                                    new VmInstanceInventory(
                                            uuid: serverId1,
                                            defaultL3NetworkUuid: dl3Uuid,
                                            vmNics: [
                                                    new VmNicInventory(
                                                            uuid: nicId10,
                                                            l3NetworkUuid: dl3Uuid,
                                                            deviceId: 0
                                                    )
                                            ]
                                    ),
                            ]
                    )
            )
            return result
        }

        QueryLoadBalancerAction.metaClass.call = {
            QueryLoadBalancerAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.contains("uuid=$loadBalancerId".toString())
            QueryLoadBalancerAction.Result result
            result = new QueryLoadBalancerAction.Result(
                    value: new QueryLoadBalancerResult(
                            inventories: [
                                    new LoadBalancerInventory(
                                            uuid: loadBalancerId,
                                            listeners: [
                                                    new LoadBalancerListenerInventory(
                                                            uuid: listenerId
                                                    )
                                            ]
                                    )
                            ]
                    )
            )
            return result
        }

        QueryVmNicAction.metaClass.call = {
            QueryVmNicAction self = delegate
            assert self.sessionId == sessionId
            assert (self.conditions.first().contains(nicId20) || self.conditions.first().contains(nicId00)
                    || self.conditions.first().contains(nicId01) || self.conditions.first().contains(nicId10))
            Map instanceNicMap = [
                    (nicId00): serverId0,
                    (nicId01): serverId0,
                    (nicId10): serverId1,
                    (nicId20): serverId2
            ]
            QueryVmNicAction.Result result
            result = new QueryVmNicAction.Result(
                    value: new QueryVmNicResult(
                            inventories: []
                    )
            )
            String condition = self.conditions.first().substring(6)
            for (String nicId : condition.split(',')) {
                result.value.inventories.add(new VmNicInventory(
                        uuid: nicId,
                        vmInstanceUuid: instanceNicMap[nicId]
                ))
            }
            return result
        }

        CreateSystemTagAction.metaClass.call = {
            CreateSystemTagAction self = delegate
            assert self.sessionId == sessionId
            assert self.resourceType == "LoadBalancerVO"
            assert self.resourceUuid == loadBalancerId
            CreateSystemTagAction.Result result
            result = new CreateSystemTagAction.Result(
                    value: new CreateSystemTagResult(
                            inventory: new SystemTagInventory(
                                    uuid: randomUUID()
                            )
                    )
            )
            return result
        }

        QuerySystemTagAction.metaClass.call = {
            QuerySystemTagAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.containsAll(
                    [
                            "resourceType=LoadBalancerVO",
                            "resourceUuid=$loadBalancerId".toString()
                    ]
            )
            QuerySystemTagAction.Result result
            result = new QuerySystemTagAction.Result(
                    value: new QuerySystemTagResult(
                            inventories: [
                                    new SystemTagInventory(
                                            tag: "backendServerExt::$serverId0::$nicId00::ecs::30".toString()
                                    ),
                                    new SystemTagInventory(
                                            tag: "backendServerExt::$serverId1::$nicId10::ecs::50".toString()
                                    ),
                                    new SystemTagInventory(
                                            tag: "backendServerExt::$serverId2::$nicId20::eni::80".toString()
                                    ),
                            ]
                    )
            )
            return result
        }

        AddVmNicToLoadBalancerAction.metaClass.call = {
            AddVmNicToLoadBalancerAction self = delegate
            assert self.sessionId == sessionId
            assert self.listenerUuid == listenerId
            assert self.vmNicUuids.containsAll([nicId00, nicId10])
            AddVmNicToLoadBalancerAction.Result result
            result = new AddVmNicToLoadBalancerAction.Result()
            return result
        }
    }

    @Test
    void testEcsParamToZStackParam() {
        AddBackendServers api = new AddBackendServers(
                requestId: requestId,
                sessionId: sessionId,
        )
        Map zstackAPIParam = api.getZstackAPIParam(ecsAPIParam)
        println "ZStack api param map:"
        println JSONObjectUtil.toJsonString(zstackAPIParam)
        assert zstackAPIParam.size() == 3
        assert zstackAPIParam["sessionId"] == sessionId
        assert zstackAPIParam["apiId"] == requestId
        assert zstackAPIParam["loadBalancerUuid"] == loadBalancerId
        assert api.servers.size() == 3
        List serverIds = api.servers.stream().map { server -> server.serverId }.collect { it }
        assert serverIds.containsAll([serverId0, serverId1, nicId20])
    }

    @Test
    void testZStackRspToEcsRsp() {
        Map zstackActionResult = [
                "LoadBalancerId": loadBalancerId,
                "BackendServer" : [
                        new SLBBackendServer(
                                serverId: serverId0,
                                type: "ecs",
                                weight: 30
                        ),
                        new SLBBackendServer(
                                serverId: serverId1,
                                type: "ecs",
                                weight: 50
                        ),
                        new SLBBackendServer(
                                serverId: nicId20,
                                type: "eni",
                                weight: 80
                        )
                ]
        ]
        AddBackendServers api = new AddBackendServers(
                requestId: requestId,
                sessionId: sessionId,
                zstackAPIParamMap: ["apiId": requestId]
        )
        Map ecsAPIRsp = api.getEcsAPIRsp(zstackActionResult)
        println "ECS api response map:"
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.size() == 3
        assert ecsAPIRsp["RequestId"] == requestId
        assert ecsAPIRsp["LoadBalancerId"] == loadBalancerId
        Map serversMap = ecsAPIRsp["BackendServers"] as Map
        assert serversMap.size() == 1
        List serversList = serversMap["BackendServer"] as List
        assert serversList.size() == 3
        Map server0 = serversList.first()
        assert server0["Type"] == "ecs"
        assert server0["ServerId"] == serverId0
        assert server0["Weight"] == 30
        Map server1 = serversList[1]
        assert server1["ServerId"] == serverId1
        assert server1["Type"] == "ecs"
        assert server1["Weight"] == 50
        Map server2 = serversList[2]
        assert server2["ServerId"] == nicId20
        assert server2["Type"] == "eni"
        assert server2["Weight"] == 80
    }

    @Test
    void testAddBackendServers() {
        AddBackendServers api = new AddBackendServers(
                sessionId: sessionId,
                requestId: requestId
        )
        String res = api.call(ecsAPIParam)
        println "API Adapter response: "
        println res

        AddBackendServersResponse response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(res, AddBackendServersResponse.class)

        assert response.requestId == requestId
        assert response.loadBalancerId == loadBalancerId
        assert response.backendServers.size() == 3
        assert response.backendServers.first().serverId == serverId0
        assert response.backendServers.first().type == "ecs"
        assert response.backendServers.first().weight == "30"
        assert response.backendServers[1].serverId == serverId1
        assert response.backendServers[1].type == "ecs"
        assert response.backendServers[1].weight == "50"
        assert response.backendServers[2].serverId == nicId20
        assert response.backendServers[2].type == "eni"
        assert response.backendServers[2].weight == "80"
    }
}
