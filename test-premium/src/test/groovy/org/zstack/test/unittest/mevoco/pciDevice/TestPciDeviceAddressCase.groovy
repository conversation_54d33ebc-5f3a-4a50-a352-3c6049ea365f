package org.zstack.test.unittest.mevoco.pciDevice

import org.junit.Test
import org.zstack.pciDevice.PciDeviceAddress

/**
 * Created by <PERSON><PERSON><PERSON> on 08/07/2017.
 */
class TestPciDeviceAddressCase {
    @Test
    void testPciDeviceAddress() {
        PciDeviceAddress addr1 = new PciDeviceAddress("06:00.1")
        assert addr1.getDbdf() == "0000:06:00.1"
        assert addr1.getDomain() == "0000"
        assert addr1.getBus() == "06"
        assert addr1.getSlot() == "00"
        assert addr1.getFunc() == "1"
        assert addr1 == new PciDeviceAddress("0000:06:00.1")
    }
}
