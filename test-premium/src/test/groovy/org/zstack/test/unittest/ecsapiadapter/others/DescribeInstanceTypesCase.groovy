package org.zstack.test.unittest.ecsapiadapter.others

import com.aliyuncs.ecs.model.v20140526.DescribeInstanceTypesResponse
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import org.apache.commons.lang.StringUtils
import org.junit.Before
import org.junit.Test
import org.zstack.mevoco.PremiumGlobalConfig
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterGlobalConfig
import org.zstack.pluginpremium.externalapiadapter.api.ecs.others.DescribeInstanceTypes
import org.zstack.pluginpremium.externalapiadapter.datatypes.GpuSpec
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import java.lang.reflect.Type

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils.randomUUID

/**
 * @Author: fubang* @Date: 2018/4/26
 */
class DescribeInstanceTypesCase {

    @Before
    void prepare() {
        QuerySystemTagAction.metaClass.call = {
            QuerySystemTagAction self = delegate
            assert self.conditions.contains("resourceType=InstanceOfferingVO")
            QuerySystemTagAction.Result result = new QuerySystemTagAction.Result(
                    value: new QuerySystemTagResult(
                            inventories: []
                    )
            )
            return result
        }

        ExternalAPIAdapterGlobalConfig.GPU_SPEC_MAPPING.metaClass.value = {
            return "[\n" +
                    "  {\n" +
                    "    \"ecsGpuSpec\": \"NVIDIA V100 16G\",\n" +
                    "    \"deviceId\": \"7604\",\n" +
                    "    \"vendorId\": \"4318\",\n" +
                    "    \"subDeviceId\": \"4628\",\n" +
                    "    \"pciSpeed\": 8\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"ecsGpuSpec\": \"NVIDIA V100 32G\",\n" +
                    "    \"deviceId\": \"7606\",\n" +
                    "    \"vendorId\": \"4318\",\n" +
                    "    \"subDeviceId\":\"4682\",\n" +
                    "    \"pciSpeed\": 8\n" +
                    "  },\n" +
                    "  {\n" +
                    "    \"ecsGpuSpec\": \"NVIDIA T4\",\n" +
                    "    \"deviceId\": \"7864\",\n" +
                    "    \"vendorId\": \"4318\",\n" +
                    "    \"subDeviceId\":\"4770\",\n" +
                    "    \"pciSpeed\": 8\n" +
                    "  }\n" +
                    "]"
        }
    }

    @Test
    void basicTest() {
        Map ecsAPIParam = [
                "Action"     : "DescribeInstanceTypes",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]

        DescribeInstanceTypes api = new DescribeInstanceTypes()
        api.sessionId = randomUUID()
        api.requestId = randomUUID()

        QueryInstanceOfferingAction.metaClass.call = {
            QueryInstanceOfferingAction self = delegate
            assert self.sessionId == api.sessionId
            assert self.apiId == api.requestId
            assert self.replyWithCount
            assert self.conditions.contains("state=Enabled")
            assert self.conditions.contains("type=UserVm")
            assert self.limit == 100
            assert self.start >= 0

            return new QueryInstanceOfferingAction.Result(
                    value: new QueryInstanceOfferingResult(
                            inventories: [
                                    new InstanceOfferingInventory(
                                            uuid: "d28a0b3898ce41ca9ee4d9a3b9cd7ad9",
                                            cpuNum: 1,
                                            memorySize: 1073741824,
                                            name: "ecs.sn1ne.4xlarge"
                                    ),
                                    new InstanceOfferingInventory(
                                            uuid: "ecc6335d4edb40d696d1990dbf80e385",
                                            cpuNum: 2,
                                            memorySize: 4294967296,
                                            name: "ecs.sn1ne.2xlarge"
                                    )
                            ],
                            total: 10
                    )
            )
        }

        String result = api.call(ecsAPIParam)
        println(result)

        DescribeInstanceTypesResponse response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(result, DescribeInstanceTypesResponse.class)
        assert response.getInstanceTypes().size() == 10
    }

    @Test
    void testZStackRspToEcsRsp() {
        Map ecsAPIParam = [
                "Action"     : "DescribeInstanceTypes",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        DescribeInstanceTypes api = new DescribeInstanceTypes()
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        QueryVmInstanceAction.Result result = new QueryVmInstanceAction.Result()
        QueryVmInstanceResult value = new QueryVmInstanceResult()
        result.value = value

        value.total = 2
        value.inventories = [
                new InstanceOfferingInventory(
                        uuid: "d28a0b3898ce41ca9ee4d9a3b9cd7ad9",
                        name: "hehe",
                        cpuNum: 1,
                        memorySize: 1073741824
                ),
                new InstanceOfferingInventory(
                        uuid: "ecc6335d4edb40d696d1990dbf80e385",
                        name: "hehe",
                        cpuNum: 2,
                        memorySize: 4294967296
                )
        ]

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get("InstanceTypes") != null
        assert ecsAPIRsp.get("InstanceTypes").get("InstanceType").size == 2

        Map tmp = ecsAPIRsp.get("InstanceTypes").get("InstanceType")[0]
        assert tmp != null
        assert tmp.get("InstanceTypeId") == "hehe"
        assert tmp.get("CpuCoreCount") == 1
        assert tmp.get("MemorySize") == 1.0
    }

    @Test
    void testZStackRspToEcsRsp2() {
        Map ecsAPIParam = [
                "Action"     : "DescribeInstanceTypes",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        DescribeInstanceTypes api = new DescribeInstanceTypes()
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        QueryVmInstanceAction.Result result = new QueryVmInstanceAction.Result()
        QueryVmInstanceResult value = new QueryVmInstanceResult()
        result.value = value

        value.total = 0
        value.inventories = []

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get("InstanceTypes") != null
        assert ecsAPIRsp.get("InstanceTypes").get("InstanceType").size == 0
    }

    @Test
    void testInstanceTypeWithGpu() {
        String pciV100Uuid = randomUUID()
        String pciT4Uuid = randomUUID()
        String instanceTypeUuid = randomUUID()
        String instanceTypeId = "ecs.dududu.hehehe"

        QueryPciDeviceSpecAction.metaClass.call = {
            QueryPciDeviceSpecAction self = delegate
            assert self.conditions.contains("type?=GPU_Video_Controller,GPU_3D_Controller")

            return new QueryPciDeviceSpecAction.Result(
                    value: new QueryPciDeviceSpecResult(
                            inventories: [
                                    new PciDeviceSpecInventory(
                                            uuid: pciV100Uuid,
                                            name: "NV Dafahao V100 Nizhideyongyou",
                                            deviceId: "7604",
                                            vendorId: "4318",
                                            subdeviceId: "4628"
                                    ),
                                    new PciDeviceSpecInventory(
                                            uuid: pciT4Uuid,
                                            name: "NV Jiushigui T4 Aimaimaibumaigun",
                                            deviceId: "7864",
                                            vendorId: "4318",
                                            subdeviceId: "4770"
                                    )
                            ]
                    )
            )
        }

        QuerySystemTagAction.metaClass.call = {
            QuerySystemTagAction self = delegate
            assert self.conditions.contains("resourceType=InstanceOfferingVO")
            QuerySystemTagAction.Result result
            result = new QuerySystemTagAction.Result(
                    value: new QuerySystemTagResult(
                            inventories: [
                                    new SystemTagInventory(
                                            uuid: randomUUID(),
                                            resourceType: "InstanceOfferingVO",
                                            resourceUuid: instanceTypeUuid,
                                            tag: "pciDeviceInfo::7604::4318::4628::2".toString()
                                    )
                            ]
                    )
            )
            return result
        }

        Map ecsAPIParam = [
                "Action"     : "DescribeInstanceTypes",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]

        DescribeInstanceTypes api = new DescribeInstanceTypes(
                sessionId: randomUUID(),
                requestId: randomUUID()
        )

        api.ecsAPIParamMap = ecsAPIParam
//        api.gpuSpecs = getGpuSpec()

        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        QueryInstanceOfferingAction.Result result = new QueryInstanceOfferingAction.Result(
                value: new QueryInstanceOfferingResult(
                        inventories: [
                                new InstanceOfferingInventory(
                                        uuid: instanceTypeUuid,
                                        memorySize: 1073741824,
                                        cpuNum: 4,
                                        name: instanceTypeId,
                                )
                        ],
                        total: 1
                )
        )

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        String res = JSONObjectUtil.toJsonString(ecsAPIRsp)
        println res
        DescribeInstanceTypesResponse response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(res, DescribeInstanceTypesResponse.class)
        assert response.getInstanceTypes().size() == 1
        DescribeInstanceTypesResponse.InstanceType type = response.getInstanceTypes().first()
        assert type.instanceTypeId == instanceTypeId
        assert type.GPUAmount == 2
        assert type.GPUSpec == "NVIDIA V100 16G"
        assert type.eniQuantity == 6
        assert type.memorySize == 1.0
        assert type.cpuCoreCount == 4
    }
}
