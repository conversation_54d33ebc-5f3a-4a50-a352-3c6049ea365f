package org.zstack.test.unittest.utils;

import org.apache.sshd.server.SshServer;
import org.apache.sshd.server.auth.password.AcceptAllPasswordAuthenticator;
import org.apache.sshd.server.auth.password.StaticPasswordAuthenticator;
import org.apache.sshd.server.auth.pubkey.AcceptAllPublickeyAuthenticator;
import org.apache.sshd.server.auth.pubkey.StaticPublickeyAuthenticator;
import org.apache.sshd.server.keyprovider.SimpleGeneratorHostKeyProvider;
import org.apache.sshd.server.shell.ProcessShellCommandFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.zstack.utils.ssh.Ssh;
import org.zstack.utils.ssh.SshResult;

import java.io.IOException;

public class SshUtilsCase {
    private SshServer sshd;

    private String hostname = "127.0.0.1";
    private String login = "root";
    private String password = "password";

    @Before
    public void setUp() throws IOException {
        // Init sftp server stuff
        sshd = SshServer.setUpDefaultServer();
        sshd.setHost(hostname);
        sshd.setPasswordAuthenticator(AcceptAllPasswordAuthenticator.INSTANCE);
        sshd.setPublickeyAuthenticator(AcceptAllPublickeyAuthenticator.INSTANCE);
        sshd.setKeyPairProvider(new SimpleGeneratorHostKeyProvider());
        sshd.setCommandFactory(new ProcessShellCommandFactory());

        sshd.start();
    }

    @After
    public void tearDown() throws IOException {
        sshd.stop();
    }

    @Test
    public void testUploadFile() {
        Ssh ssh = new Ssh();
        ssh.setUsername(login)
                .setPassword(password).setPort(sshd.getPort())
                .setHostname(sshd.getHost())
                .setTimeout(10);
        ssh.command("echo test-string");
        SshResult ret = ssh.run();

        assert ret.getReturnCode() == 0;

        assert ssh.getSession().isConnected();

        ssh.close();

        assert !ssh.getSession().isConnected();
    }
}
