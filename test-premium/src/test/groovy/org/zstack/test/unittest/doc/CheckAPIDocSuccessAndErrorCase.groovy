package org.zstack.test.unittest.doc

import org.junit.Test
import org.zstack.header.exception.CloudRuntimeException
import org.zstack.utils.ShellResult
import org.zstack.utils.ShellUtils

import java.nio.file.Paths

class CheckAPIDocSuccessAndErrorCase {
    @Test
    void testApiDocTemplateSuccessAndError() {
        String path = Paths.get("../../").toAbsolutePath().normalize().toString()
        ShellResult res = ShellUtils.runAndReturn("find ${path} -type f -name '*EventDoc_zh_cn.groovy' -o -name '*ReplyDoc_zh_cn.groovy'")
        List<String> list = res.stdout.split("\n")
        list.each {
            ShellResult successRes = ShellUtils.runAndReturn("cat ${it} | grep 'name \"success\"'|wc -l")
            Integer successCount = Integer.valueOf(successRes.stdout.substring(0, successRes.stdout.length() - 1))
            ShellResult errorRes = ShellUtils.runAndReturn("cat ${it} | grep 'name \"error\"'|wc -l")
            Integer errorCount = Integer.valueOf(errorRes.stdout.substring(0, errorRes.stdout.length() - 1))
            if (successCount != 1 || errorCount != 1) {
                throw new CloudRuntimeException("please check 'success' and 'error' property of ${it}. ")
            }
        }
    }

}