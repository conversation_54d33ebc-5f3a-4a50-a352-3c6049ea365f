package org.zstack.test.unittest.ecsapiadapter.vpc

import com.aliyuncs.ecs.model.v20140526.DescribeVpcsResponse
import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.vpc.DescribeVpcs
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import java.sql.Timestamp

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils.randomUUID
/**
 * Created by Qi Le on 2020/1/6
 */
class DescribeVpcsCase {
    String requestId = randomUUID()
    String sessionId = randomUUID()
    String vpcId = randomUUID()
    String vsId = randomUUID()
    String vpcName = "haha"
    String l3NetworkId = randomUUID()
    String vRouterId = randomUUID()
    String cidrBlock = "**********/24"
    String routerTableId = randomUUID()
    String zoneUuid = randomUUID()
    String zoneId = "Zone-1"
    String regionUuid = randomUUID()
    String regionId = "cn-zstack"
    Timestamp date = new Timestamp(System.currentTimeMillis())

    Map ecsAPIParam = [
            "Action" : "DescribeVpcs",
            "VpcId"  : vpcId,
            "VpcName": vpcName
    ]

    @Before
    void prepare() {
        QueryAliyunProxyVpcAction.metaClass.call = {
            QueryAliyunProxyVpcAction self = delegate
            assert self.conditions.containsAll(
                    [
                            "uuid?=$vpcId".toString(),
                            "vpcName=$vpcName".toString()
                    ]
            )
            QueryAliyunProxyVpcAction.Result result
            result = new QueryAliyunProxyVpcAction.Result(
                    value: new QueryAliyunProxyVpcResult(
                            inventories: [
                                    new AliyunProxyVpcInventory(
                                            uuid: vpcId,
                                            vpcName: vpcName,
                                            vRouterUuid: vRouterId,
                                            cidrBlock: cidrBlock,
                                            description: ".",
                                            isDefault: true,
                                            aliyunProxyVSwitches: [
                                                    new AliyunProxyVSwitchInventory(
                                                            uuid: vsId,
                                                            aliyunProxyVpcUuid: vpcId,
                                                            vpcL3NetworkUuid: l3NetworkId,
                                                            status: "Enabled",
                                                            isDefault: true
                                                    )
                                            ],
                                            createDate: date,
                                            lastOpDate: date
                                    )
                            ]
                    )
            )
            return result
        }

        QueryVRouterRouteTableAction.metaClass.call = {
            QueryVRouterRouteTableAction self = delegate
            assert self.conditions.contains("attachedRouterRef.virtualRouterVmUuid=$vRouterId".toString())
            QueryVRouterRouteTableAction.Result result
            result = new QueryVRouterRouteTableAction.Result(
                    value: new QueryVRouterRouteTableResult(
                            inventories: [
                                    new VRouterRouteTableInventory(
                                            uuid: routerTableId
                                    )
                            ]
                    )
            )
            return result
        }

        QueryVpcRouterAction.metaClass.call = {
            QueryVpcRouterAction self = delegate as QueryVpcRouterAction
            assert self.conditions.contains("uuid=$vRouterId".toString())
            QueryVpcRouterAction.Result result
            result = new QueryVpcRouterAction.Result(
                    value: new QueryVpcRouterResult(
                            inventories: [
                                    new VpcRouterVmInventory(
                                            uuid: vRouterId,
                                            state: "Running",
                                            status: "Connected",
                                            clusterUuid: zoneUuid,
                                            zoneUuid: regionUuid
                                    )
                            ]
                    )
            )
            return result
        }

        QueryClusterAction.metaClass.call = {
            QueryClusterAction self = delegate as QueryClusterAction
            assert self.conditions.contains("uuid=$zoneUuid".toString())
            QueryClusterAction.Result result
            result = new QueryClusterAction.Result(
                    value: new QueryClusterResult(
                            inventories: [
                                    new ClusterInventory(
                                            uuid: zoneUuid,
                                            name: zoneId
                                    )
                            ]
                    )
            )
            return result
        }

        QueryZoneAction.metaClass.call = {
            QueryZoneAction self = delegate as QueryZoneAction
            assert self.conditions.contains("uuid=$regionUuid".toString())
            QueryZoneAction.Result result
            result = new QueryZoneAction.Result(
                    value: new QueryZoneResult(
                            inventories: [
                                    new ZoneInventory(
                                            uuid: regionUuid,
                                            name: regionId
                                    )
                            ]
                    )
            )
            return result
        }
    }

    @Test
    void testEcsParamToZStackParam() {
        DescribeVpcs api = new DescribeVpcs(
                sessionId: sessionId,
                requestId: requestId
        )
        Map zstackAPIParam = api.getZstackAPIParam(ecsAPIParam)
        println "ZStack api param map:"
        println JSONObjectUtil.toJsonString(zstackAPIParam)

        assert zstackAPIParam.size() == 6
        assert zstackAPIParam["conditions"].containsAll(
                [
                        "uuid?=$vpcId".toString(),
                        "vpcName=$vpcName".toString()
                ]
        )
    }

    @Test
    void testDescribeVpcs() {
        DescribeVpcs api = new DescribeVpcs(
                requestId: requestId,
                sessionId: sessionId
        )
        String res = api.call(ecsAPIParam)

        println "API Adapter response:"
        println res

        DescribeVpcsResponse response
        response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(res, DescribeVpcsResponse.class)

        assert response.requestId == requestId
        def vpc = response.vpcs.first()
        assert vpc.status == "Available"
        assert vpc.VRouterId == vRouterId
        assert vpc.isDefault
        assert vpc.description == "."
        assert vpc.vpcId == vpcId
        assert vpc.VSwitchIds.first() == vsId
        assert vpc.creationTime == ExternalAPIAdapterUtils.formatIso8601Date(date)
        assert vpc.cidrBlock == cidrBlock
        assert vpc.vpcName == vpcName
    }
}
