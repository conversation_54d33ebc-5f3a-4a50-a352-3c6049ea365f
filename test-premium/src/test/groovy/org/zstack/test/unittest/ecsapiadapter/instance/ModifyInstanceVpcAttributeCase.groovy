package org.zstack.test.unittest.ecsapiadapter.instance

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterGlobalProperty
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.instance.ModifyInstanceVpcAttribute
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.ECS_API_REQUESTID_KEY
import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils.randomUUID

/**
 * Created by lining on 05/31/2018.
 */
class ModifyInstanceVpcAttributeCase {

    @Test
    void testModifyInstanceVpcAttribute() {
        String vpcUuid = randomUUID()
        ExternalAPIAdapterGlobalProperty.VIRTUALROUTEROFFERINGUUID = randomUUID()
        String sessionId = randomUUID()
        String instanceUuid = randomUUID()
        String vSwitchUuid = randomUUID()
        String vpcL3NetworkUuid = randomUUID()
        String defaultL3 = randomUUID()
        String defaultVmNic = randomUUID()

        Map ecsAPIParam = [
                "Action"      : "ModifyInstanceVpcAttribute",
                "InstanceId" :  instanceUuid,
                "VSwitchId" :  vSwitchUuid,
                "PrivateIpAddress" : "***********",
                "AccessKeyId"  : APIAdapterTestUtils.ECS_ACCESSKEY
        ]

        QueryVmInstanceAction.metaClass.call = {
            QueryVmInstanceAction.Result result
            result = new QueryVmInstanceAction.Result(
                    value: new QueryVmInstanceResult(
                            inventories: [
                                    new VmInstanceInventory(
                                            uuid: instanceUuid,
                                            vmNics: [
                                                    new VmNicInventory(
                                                            uuid: randomUUID(),
                                                            l3NetworkUuid: vpcL3NetworkUuid
                                                    )
                                            ]
                                    )
                            ]
                    )
            )
            return result
        }

        SetVmStaticIpAction.metaClass.call = {
            SetVmStaticIpAction.Result result
            result = new SetVmStaticIpAction.Result(
                    value: new SetVmStaticIpResult()
            )
        }

        AliyunProxyVSwitchInventory vSwitchInventory = new AliyunProxyVSwitchInventory(
                uuid: vSwitchUuid,
                vpcL3NetworkUuid: vpcL3NetworkUuid
        )
        QueryAliyunProxyVSwitchAction.metaClass.call = {
            QueryAliyunProxyVSwitchAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.size() == 1
            assert self.conditions.contains("uuid=$vSwitchUuid".toString())

            return new QueryAliyunProxyVSwitchAction.Result(
                    value: new QueryAliyunProxyVSwitchResult(
                        inventories: [vSwitchInventory]
                    )
            )
        }

        AttachL3NetworkToVmAction.metaClass.call = {
            AttachL3NetworkToVmAction self = delegate
            assert self.sessionId == sessionId
            assert self.l3NetworkUuid == vpcL3NetworkUuid
            assert self.staticIp == ecsAPIParam.get("PrivateIpAddress")
            assert self.vmInstanceUuid == instanceUuid

            return new AttachL3NetworkToVmAction.Result(
                    value: new AttachL3NetworkToVmResult(
                            inventory: new VmInstanceInventory(
                                    uuid: instanceUuid,
                                    defaultL3NetworkUuid: defaultL3,
                                    vmNics: [
                                            new VmNicInventory(
                                                    uuid: randomUUID(),
                                                    l3NetworkUuid: randomUUID()
                                            ),
                                            new VmNicInventory(
                                                    uuid: defaultVmNic,
                                                    l3NetworkUuid: defaultL3
                                            )
                                    ]
                            )
                    )
            )
        }

        UpdateVmInstanceAction.metaClass.call = {
            UpdateVmInstanceAction self = delegate
            assert self.uuid == instanceUuid
            assert self.defaultL3NetworkUuid == vpcL3NetworkUuid

            return new UpdateVmInstanceAction.Result(
            )
        }

        DetachL3NetworkFromVmAction.metaClass.call = {
            DetachL3NetworkFromVmAction self = delegate
            assert self.sessionId == sessionId
            assert self.vmNicUuid == defaultVmNic
        }

        ModifyInstanceVpcAttribute api = new ModifyInstanceVpcAttribute()
        api.setSessionId(sessionId)
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")

        String rsp = api.call(ecsAPIParam)
        println "ecs rsp content: " + rsp
        Map ecsAPIRspMap = ExternalAPIAdapterUtils.gson.fromJson(rsp, Map.class)
        assert ecsAPIRspMap.get(ECS_API_REQUESTID_KEY) == api.requestId
    }

}
