package org.zstack.test.unittest.check;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.header.core.workflow.Flow;
import org.zstack.utils.BeanUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.path.PathUtil;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

public class SingletonFlowCheckerCase {
    private static final CLogger logger = Utils.getLogger(SingletonFlowCheckerCase.class);
    private static List<String> skipToCheckClasses = Arrays.asList("");

    @Test
    public void checkSingletonFlowField() {
        long cur = System.currentTimeMillis();
        Set<String> nonFinalFieldExistClasses = new HashSet<>();
        Set<Class<? extends Flow>> subClasses = BeanUtils.reflections.getSubTypesOf(Flow.class).stream()
                .filter(c -> !c.isAnonymousClass()).collect(Collectors.toSet());
        List<String> springConfigXml = PathUtil.scanFolderOnClassPath("springConfigXml");
        StringBuilder sb = new StringBuilder();
        for (String path : springConfigXml) {
            logger.debug("scan spring config xml: " + path);
            String config = PathUtil.readFileToString(path, StandardCharsets.UTF_8);
            sb.append(config);
        }
        String springConfig = sb.toString();

        for (Class<? extends Flow> subClass : subClasses) {
            if (skipToCheckClasses.contains(subClass.getName())) {
                continue;
            }
            logger.debug("check flow " + subClass.getName());
            for (Field declaredField : subClass.getDeclaredFields()) {
                declaredField.setAccessible(true);
                if (declaredField.isAnnotationPresent(Autowired.class) || Modifier.isFinal(declaredField.getModifiers())
                        || declaredField.isSynthetic()){
                    continue;
                }

                if (!springConfig.contains(subClass.getName())) {
                    continue;
                }

                logger.debug(declaredField.getName());
                nonFinalFieldExistClasses.add(subClass.getName());
            }
        }
        if (!nonFinalFieldExistClasses.isEmpty()) {
            logger.debug(String.format("there are classes with non final modifier fields: %s, \n" +
                    "when constructing a singleton flow, you should not define mutable member variables or " +
                    "static variables as they are shared. Please define it as final.", nonFinalFieldExistClasses.toString()));
        }
        logger.debug(String.format("finish to check singleton flow fileds, cost %s ms... ", (System.currentTimeMillis() - cur)));
        assert nonFinalFieldExistClasses.isEmpty();
    }
}
