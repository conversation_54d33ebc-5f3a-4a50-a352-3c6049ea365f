package org.zstack.test.unittest.ecsapiadapter.others

import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.zone.DescribeZones
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.SUPPORT_RESOURCE_INFO

/**
 * Created by lining on 2018/5/4.
 */
class DescribeZonesCase {
    String clusterUuid = ExternalAPIAdapterUtils.randomUUID()
    String clusterId = "AZ-1"

    @Before
    void prepare() {
        QueryClusterAction.metaClass.call = {
            QueryClusterAction.Result result
            result = new QueryClusterAction.Result(
                    value: new QueryClusterResult(
                            inventories: [
                                    new ClusterInventory(
                                            uuid: clusterUuid,
                                            name: clusterId
                                    )
                            ]
                    )
            )
            return result
        }
    }

    @Test
    void testZStackRspToEcsRsp() {
        String instanceOfferingUuid = ExternalAPIAdapterUtils.randomUUID()
        String instanceOfferingUuid2 = ExternalAPIAdapterUtils.randomUUID()

        Map ecsAPIParam = [
                "Action"     : "DescribeZones",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        DescribeZones api = new DescribeZones()
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        QueryClusterAction.Result result = new QueryClusterAction.Result(
                value: new QueryClusterResult(
                        inventories: [
                                new ClusterInventory(
                                        uuid: clusterUuid,
                                        name: clusterId
                                )
                        ]
                )
        )

        QueryInstanceOfferingAction.metaClass.call = {
            QueryInstanceOfferingAction self = delegate
            assert self.sessionId == api.sessionId

            return new QueryInstanceOfferingAction.Result(
                    value: new QueryInstanceOfferingResult(
                            inventories: [
                                    new InstanceOfferingInventory(
                                            uuid: instanceOfferingUuid,
                                            name: "ecs.s2.aaa"
                                    ),
                                    new InstanceOfferingInventory(
                                            uuid: instanceOfferingUuid2,
                                            name: "ecs.s2.bbb"
                                    )
                            ]
                    )
            )
        }

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get("Zones").get("Zone").size == 1

        Map tmp = ecsAPIRsp.get("Zones").get("Zone").get(0)
        assert tmp != null
        assert tmp.get("ZoneId") == clusterId
        assert tmp.get("LocalName") == clusterId
        assert tmp.get("AvailableDiskCategories").get("DiskCategories").containsAll([SUPPORT_RESOURCE_INFO.DISK_CATEGORY])
        assert tmp.get("AvailableResourceCreation").get("ResourceTypes").containsAll(["Instance", "Disk"])
        assert tmp.get("AvailableInstanceTypes").get("InstanceTypes") == ["ecs.s2.aaa", "ecs.s2.bbb"]
        assert tmp.get("AvailableVolumeCategories").get("VolumeCategories").containsAll([SUPPORT_RESOURCE_INFO.DISK_CATEGORY])
        Map resourcesInfo = tmp.get("AvailableResources").get("ResourcesInfo").get(0)
        assert resourcesInfo != null
        assert resourcesInfo.get("IoOptimized")
        assert resourcesInfo.get("SystemDiskCategories").get("supportedSystemDiskCategory") == [SUPPORT_RESOURCE_INFO.DISK_CATEGORY]
        assert resourcesInfo.get("InstanceTypes").get("supportedInstanceType") == ["ecs.s2.aaa", "ecs.s2.bbb"]
        assert resourcesInfo.get("InstanceTypeFamilies").get("supportedInstanceTypeFamily") == [SUPPORT_RESOURCE_INFO.INSTANCE_TYPE_FAMILY]
        assert resourcesInfo.get("DataDiskCategories").get("supportedDataDiskCategory") == [SUPPORT_RESOURCE_INFO.DISK_CATEGORY]
        assert resourcesInfo.get("InstanceGenerations").get("supportedInstanceGeneration") == [SUPPORT_RESOURCE_INFO.INSTANCE_TYPE_GENERATION]
        assert resourcesInfo.get("NetworkTypes").get("supportedNetworkCategory") == ["vpc", "classic"]
    }
}
