package org.zstack.test.unittest.code

import org.junit.Test
import org.zstack.testlib.tool.TermsGenerator

class GenerateTermsFromJpaEntitiesCase {
    @Test
    void testGenerateTermFromJpa() {
        String replaceTo = null
        if (System.getProperty("replaceTo") != null) {
            replaceTo = System.getProperty("replaceTo")
        }

        TermsGenerator.generateTermsFromJpaEntities(replaceTo)
    }
}
