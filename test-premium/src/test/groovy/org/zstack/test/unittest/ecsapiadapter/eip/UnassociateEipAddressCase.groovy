package org.zstack.test.unittest.ecsapiadapter.eip

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.eip.UnassociateEipAddress
import org.zstack.sdk.DetachEipAction
import org.zstack.sdk.DetachEipResult
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * @Author: fubang
 * @Date: 2018/5/28
 */
class UnassociateEipAddressCase {
    String eipId = ExternalAPIAdapterUtils.randomUUID()

    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"              : "UnassociateEipAddress",
                "AllocationId"        : eipId
        ]

        UnassociateEipAddress api = new UnassociateEipAddress(
                sessionId: ExternalAPIAdapterUtils.randomUUID(),
                requestId: ExternalAPIAdapterUtils.randomUUID()
        )

        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.size() == 3
        assert zstackAPIParamMap.get("uuid") == eipId
        assert zstackAPIParamMap.get(ZSTACK_API_APIID_KEY) == api.requestId
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == api.sessionId
    }

    @Test
    void testZStackRspToEcsRsp() {
        Map ecsAPIParam = [
                "Action"              : "UnassociateEipAddress",
                "AllocationId"        : eipId,
                "AccessKeyId"         : APIAdapterTestUtils.ECS_ACCESSKEY
        ]

        UnassociateEipAddress api = new UnassociateEipAddress(
                sessionId: ExternalAPIAdapterUtils.randomUUID(),
                requestId: ExternalAPIAdapterUtils.randomUUID(),
                ecsAPIParamMap: ecsAPIParam
        )
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        DetachEipAction.Result result = new DetachEipAction.Result(
                value: new DetachEipResult()
        )

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.size() == 1
        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) == api.requestId
    }
}
