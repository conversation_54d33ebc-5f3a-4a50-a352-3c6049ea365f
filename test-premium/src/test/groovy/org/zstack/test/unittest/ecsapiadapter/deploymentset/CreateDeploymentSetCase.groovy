package org.zstack.test.unittest.ecsapiadapter.deploymentset

import com.aliyuncs.ecs.model.v20140526.CreateDeploymentSetResponse
import org.junit.Test
import org.zstack.header.affinitygroup.AffinityGroupPolicy
import org.zstack.header.affinitygroup.AffinityGroupType
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.deploymentset.CreateDeploymentSet
import org.zstack.pluginpremium.externalapiadapter.typeconvertor.DeploymentSetStategy
import org.zstack.sdk.AffinityGroupInventory
import org.zstack.sdk.CreateAffinityGroupAction
import org.zstack.sdk.CreateAffinityGroupResult
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * Created by lining on 2018/4/28.
 */
class CreateDeploymentSetCase {

    @Test
    void basicTest() {
        Map ecsAPIParam = [
                "Action"           : "CreateDeploymentSet",
                "RegionId"         : "cn-hangzhou",
                "ZoneId"           : "cn-hangzhou-d",
                "DeploymentSetName": "name",
                "Domain"           : "Default",
                "Granularity"      : "Host",
                "Description"      : "desc"
        ]

        CreateDeploymentSet api = new CreateDeploymentSet()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")

        String resourceUuid = ExternalAPIAdapterUtils.randomUUID()
        CreateAffinityGroupAction.metaClass.call = {
            CreateAffinityGroupAction self = delegate
            assert self.sessionId == api.sessionId
            assert self.apiId == api.requestId
            assert self.policy == "antiSoft"
            assert self.name == ecsAPIParam.get("DeploymentSetName")
            assert self.description == ecsAPIParam.get("Description")
            assert self.type == "host"

            return new CreateAffinityGroupAction.Result(
                    value: new CreateAffinityGroupResult(
                            inventory: new AffinityGroupInventory(
                                    uuid: resourceUuid
                            )
                    )
            )
        }

        String result = api.call(ecsAPIParam)
        println(result)

        CreateDeploymentSetResponse response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(result, CreateDeploymentSetResponse.class)
        assert response.deploymentSetId == resourceUuid
        assert response.requestId == api.requestId
    }

    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"           : "CreateDeploymentSet",
                "RegionId"         : "cn-hangzhou",
                "ZoneId"           : "cn-hangzhou-d",
                "DeploymentSetName": "name",
                "Domain"           : "Default",
                "Strategy"         : "StrictDispersion",
                "Granularity"      : "Host",
                "Description"      : "desc"
        ]
        CreateDeploymentSet api = new CreateDeploymentSet()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")

        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)
        assert zstackAPIParamMap.size() == 6
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"
        assert zstackAPIParamMap.get("name") == "name"
        assert zstackAPIParamMap.get(ZSTACK_API_DESCRIPTION_KEY) == "desc"
        assert zstackAPIParamMap.get("policy") == DeploymentSetStategy.STRICTDISPERSION.zstackValue
        assert zstackAPIParamMap.get("type") == "host"
        assert zstackAPIParamMap.get(ZSTACK_API_APIID_KEY) != null
    }

    @Test
    void testZStackRspToEcsRsp() {
        CreateAffinityGroupAction.Result result = new CreateAffinityGroupAction.Result()
        CreateAffinityGroupResult value = new CreateAffinityGroupResult()
        result.value = value

        AffinityGroupInventory affinityGroup = new AffinityGroupInventory()
        affinityGroup.setUuid("123123123123123")
        affinityGroup.setName("affinity-group-test")
        affinityGroup.setDescription("affinity group for test")
        affinityGroup.setPolicy(AffinityGroupPolicy.ANTISOFT.toString())
        affinityGroup.setType(AffinityGroupType.HOST.toString())
        affinityGroup.setVersion("1.0")
        value.inventory = affinityGroup

        CreateDeploymentSet api = new CreateDeploymentSet()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = [:]
        api.zstackAPIParamMap = api.getZstackAPIParam([:])

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
        assert ecsAPIRsp.get("DeploymentSetId") == "123123123123123"
    }

}
