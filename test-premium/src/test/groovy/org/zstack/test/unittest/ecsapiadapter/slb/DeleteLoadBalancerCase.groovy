package org.zstack.test.unittest.ecsapiadapter.slb

import com.aliyuncs.slb.model.v20140515.DeleteLoadBalancerResponse
import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.api.ecs.slb.DeleteLoadBalancer
import org.zstack.sdk.*
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils.randomUUID

/**
 * Created by <PERSON> on 2019/12/27
 */
class DeleteLoadBalancerCase {
    String requestId = randomUUID()
    String sessionId = randomUUID()
    String loadBalancerId = randomUUID()
    String vipUuid = randomUUID()

    Map ecsAPIParam = [
            "Action"        : "DeleteLoadBalancer",
            "LoadBalancerId": loadBalancerId,
    ]

    @Before
    void prepare() {
        QueryLoadBalancerAction.metaClass.call = {
            QueryLoadBalancerAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.contains("uuid=$loadBalancerId".toString())
            QueryLoadBalancerAction.Result result
            result = new QueryLoadBalancerAction.Result(
                    value: new QueryLoadBalancerResult(
                            inventories: [
                                    new LoadBalancerInventory(
                                            uuid: loadBalancerId,
                                            vipUuid: vipUuid
                                    )
                            ]
                    )
            )
            return result
        }

        DeleteVipAction.metaClass.call = { Completion completion ->
            DeleteVipAction self = delegate
            assert self.sessionId == sessionId
            assert self.uuid == vipUuid
            DeleteVipAction.Result result
            result = new DeleteVipAction.Result(
                    value: new DeleteVipResult()
            )
            completion.complete(result)
        }
    }

    @Test
    void testEcsParamToZStackParam() {
        DeleteLoadBalancer api = new DeleteLoadBalancer(
                requestId: requestId,
                sessionId: sessionId
        )
        Map zstackAPIParam = api.getZstackAPIParam(ecsAPIParam)
        println "ZStack api param map:"
        println JSONObjectUtil.toJsonString(zstackAPIParam)

        assert zstackAPIParam["uuid"] == vipUuid
    }

    @Test
    void testDeleteLoadBalancer() {
        DeleteLoadBalancer api = new DeleteLoadBalancer(
                requestId: requestId,
                sessionId: sessionId
        )
        String res = api.call(ecsAPIParam)

        println "API Adapter response:"
        println res

        DeleteLoadBalancerResponse response
        response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(res, DeleteLoadBalancerResponse.class)
        assert response.requestId == requestId
    }
}
