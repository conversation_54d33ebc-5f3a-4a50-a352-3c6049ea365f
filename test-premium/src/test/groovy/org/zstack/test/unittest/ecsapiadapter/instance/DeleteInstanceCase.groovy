package org.zstack.test.unittest.ecsapiadapter.instance

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants
import org.zstack.pluginpremium.externalapiadapter.api.ecs.instance.DeleteInstance
import org.zstack.sdk.DestroyVmInstanceAction
import org.zstack.sdk.DestroyVmInstanceResult
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

/**
 * @Author: fubang
 * @Date: 2018/4/23
 */
class DeleteInstanceCase {
    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"    : "DeleteInstance",
                "InstanceId": "ebb57b74d1e94a1db2b50bc5bb1062af",
        ]
        DeleteInstance api = new DeleteInstance()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setEcsAPIParamDefaultValue(ecsAPIParam)
        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.size() == 3
        assert zstackAPIParamMap.get(ExternalAPIAdapterConstants.ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"
        assert zstackAPIParamMap.get(ExternalAPIAdapterConstants.ZSTACK_UUID) == "ebb57b74d1e94a1db2b50bc5bb1062af"
        assert zstackAPIParamMap.get(ExternalAPIAdapterConstants.ZSTACK_API_APIID_KEY) != null
    }

    @Test
    void testZStackRspToEcsRsp() {
        Map ecsAPIParam = [
                "Action"    : "DeleteInstance",
                "InstanceId": "ebb57b74d1e94a1db2b50bc5bb1062af",
                "AccessKeyId" : APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        DeleteInstance api = new DeleteInstance()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        DestroyVmInstanceAction.Result result = new DestroyVmInstanceAction.Result()
        DestroyVmInstanceResult value = new DestroyVmInstanceResult()
        result.value = value

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ExternalAPIAdapterConstants.ECS_API_REQUESTID_KEY) != null
    }
}
