package org.zstack.test.unittest.ecsapiadapter.disk

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.api.ecs.disk.ResetDisk
import org.zstack.sdk.RevertVolumeFromSnapshotAction
import org.zstack.sdk.RevertVolumeFromSnapshotResult
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*
/**
 * @Author: fubang
 * @Date: 2018/4/25
 */
class ResetDiskCase {
    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"    : "ResetDisk",
                "SnapshotId": "ebb57b74d1e94a1db2b50bc5bb1062af"
        ]
        ResetDisk api = new ResetDisk()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.size() ==3
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"
        assert zstackAPIParamMap.get(ZSTACK_UUID) == "ebb57b74d1e94a1db2b50bc5bb1062af"
    }

    @Test
    void testZStackRspToEcsRsp() {
        Map ecsAPIParam = [
                "Action"     : "ResetDisk",
                "SnapshotId": "ebb57b74d1e94a1db2b50bc5bb1062af",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        ResetDisk api = new ResetDisk()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        RevertVolumeFromSnapshotAction.Result result = new RevertVolumeFromSnapshotAction.Result()
        RevertVolumeFromSnapshotResult value = new RevertVolumeFromSnapshotResult()
        result.value = value

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
    }
}
