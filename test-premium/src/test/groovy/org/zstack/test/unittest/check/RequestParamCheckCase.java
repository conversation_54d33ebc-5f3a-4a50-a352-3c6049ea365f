package org.zstack.test.unittest.check;

import org.junit.Test;
import org.springframework.http.HttpMethod;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.message.APIMessage;
import org.zstack.header.rest.RestRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/8 14:54
 */
public class RequestParamCheckCase {

    @Test
    public void testRequestParam() {
        List<String> apis = new ArrayList<>();
        APIMessage.apiMessageClasses.forEach(messageClass -> {
            RestRequest at = (RestRequest) messageClass.getAnnotation(RestRequest.class);
            if (at != null && at.method().equals(HttpMethod.POST)) {
                if(at.parameterName() == null || at.parameterName().equals("")) {
                    apis.add(messageClass.getName());
                }
            }
        });
        if (apis.size() != 0) {
            throw new CloudRuntimeException(String.format("failed:%s of RestRequest do not exist parameterName param with HTTP POST method," +
                    "please add paramName='params'", apis));
        }
    }
}
