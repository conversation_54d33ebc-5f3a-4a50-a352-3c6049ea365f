package org.zstack.test.unittest.mevoco

import org.junit.Test
import org.zstack.compute.cpuPinning.CpuRangeSet

class CpuSetCase {

    @Test
    void testCpuRangeSet(){
        assert CpuRangeSet.originValueOf("1-8,^2,^4-6,10") == new HashSet<Long>([1,3,7,8,10])
        expectException({CpuRangeSet.originValueOf("1,^1")})
        expectException({CpuRangeSet.originValueOf("^1")})
        assert CpuRangeSet.valueOf("1-8,^2,^4-6,10").toString() == "1,3,7-8,10"
        assert CpuRangeSet.valueOf([1L,3L,7L,8L,10L]).toString() == "1,3,7-8,10"
    }


    static void expectException(Closure c){
        boolean called = false
        try {
            c.run()
        } catch (Exception ignored) {
            called = true
        }
        assert called
    }
}
