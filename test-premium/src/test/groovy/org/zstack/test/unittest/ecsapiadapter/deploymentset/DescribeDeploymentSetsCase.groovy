package org.zstack.test.unittest.ecsapiadapter.deploymentset

import org.junit.Test
import org.zstack.header.affinitygroup.AffinityGroupPolicy
import org.zstack.header.affinitygroup.AffinityGroupType
import org.zstack.pluginpremium.externalapiadapter.api.ecs.deploymentset.DescribeDeploymentSets
import org.zstack.sdk.AffinityGroupInventory
import org.zstack.sdk.AffinityGroupUsageInventory
import org.zstack.sdk.QueryAffinityGroupAction
import org.zstack.sdk.QueryAffinityGroupResult
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * Created by lining on 2018/4/28.
 */
class DescribeDeploymentSetsCase {

    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"      : "DescribeDeploymentSets",
                "RegionId" : "cn-hangzhou",
                "DeploymentSetIds" : "['123', '213', '214']",
                "DeploymentSetName" : "name",
                "Domain" : "default",
                "Granularity" : "Host",
                "Strategy" : "LooseDispersion",
        ]
        DescribeDeploymentSets api = new DescribeDeploymentSets()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")

        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)
        assert zstackAPIParamMap.size() == 6 // apiId, sessionId, conditions, replyWithCount, limit, start
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"

        List conditions = zstackAPIParamMap.get(ZSTACK_QUERY_CONDITIONS_KEY)
        assert conditions.size() == 5

        assert conditions.contains("name=name")
        assert !conditions.contains("description=desc2")
        assert conditions.contains("uuid?=123,213,214")
        assert conditions.contains("type=HOST")
        assert conditions.contains("policy=antiSoft")
    }

    @Test
    void testZStackRspToEcsRsp() {
        QueryAffinityGroupAction.Result result = new QueryAffinityGroupAction.Result()
        QueryAffinityGroupResult value = new QueryAffinityGroupResult()
        result.value = value

        AffinityGroupInventory affinityGroup = new AffinityGroupInventory()
        affinityGroup.setUuid("123123123123123")
        affinityGroup.setName("affinity-group-test")
        affinityGroup.setDescription("affinity group for test")
        affinityGroup.setPolicy(AffinityGroupPolicy.ANTISOFT.toString())
        affinityGroup.setType(AffinityGroupType.HOST.toString())
        affinityGroup.setVersion("1.0")
        affinityGroup.usages = [
                new AffinityGroupUsageInventory(
                        uuid: "a3f1r53af18s3f13ea48f1",
                        affinityGroupUuid: "123123123123123",
                        resourceUuid: "lshgao56sd1f36sad9vnwukj",
                        resourceType: "VmInstanceVO"
                )
        ]
        value.inventories = [affinityGroup]

        DescribeDeploymentSets api = new DescribeDeploymentSets()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = [
                (ECS_QUERY_API_PAGESIZE_KEY) : "10",
                (ECS_QUERY_API_PAGENUMBER_KEY) : "1"
        ]
        api.zstackAPIParamMap = api.getZstackAPIParam([:])

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
        assert ecsAPIRsp.get("DeploymentSets").get("DeploymentSet").size() == 1

        Map deploymentSet = ecsAPIRsp.get("DeploymentSets").get("DeploymentSet").get(0)
        assert deploymentSet.get("DeploymentSetId") == "123123123123123"
        assert deploymentSet.get("DeploymentSetName") == "affinity-group-test"
        assert deploymentSet.get("DeploymentSetDescription") == "affinity group for test"
        assert deploymentSet.get("Granularity") == "Host"
        assert deploymentSet.get("InstanceAmount") == 1
        assert deploymentSet.get("Strategy") == "LooseDispersion"
    }

}
