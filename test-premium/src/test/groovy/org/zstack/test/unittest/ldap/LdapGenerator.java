package org.zstack.test.unittest.ldap;

import org.springframework.ldap.core.NameAwareAttribute;
import org.zstack.core.Platform;

import java.util.*;

public class LdapGenerator {
    private static final int CN_NUM = 100000;
    private static final Random cnRandom = new Random(100);
    private static final int ATTR_RANDOM = 1000;
    private static final Random attrRandom = new Random(100);

    public static final String WINDOWS_AD = "WINDOWS_AD";
    public static final String OPEN_LDAP = "OPEN_LDAP";

    public static Object generateOrganizationObject(String dn, List<String> randomAttributeIDs, String type) {
        Map<String, Object> formatMap = new HashMap<>();
        List<NameAwareAttribute> attributes = new ArrayList<>();
        NameAwareAttribute classAwareAttribute = new NameAwareAttribute("objectClass", "person");
        attributes.add(classAwareAttribute);

        if (WINDOWS_AD.equals(type)) {
            NameAwareAttribute dnAwareAttribute = new NameAwareAttribute("distinguishedName", dn);
            attributes.add(dnAwareAttribute);
        } else {
            NameAwareAttribute dnAwareAttribute = new NameAwareAttribute("entryDN", dn);
            attributes.add(dnAwareAttribute);

            NameAwareAttribute guidAttribute = new NameAwareAttribute("entryUUID", Platform.getUuid());
            attributes.add(guidAttribute);
        }

        if (randomAttributeIDs != null && !randomAttributeIDs.isEmpty()) {
            for (String attrID : randomAttributeIDs) {
                NameAwareAttribute nameAwareAttribute = new NameAwareAttribute(attrID, attrRandom.nextInt(ATTR_RANDOM));
                attributes.add(nameAwareAttribute);
            }
        }

        formatMap.put("dn", dn);
        formatMap.put("attributes", attributes);

        return formatMap;
    }

    public static Object generateGroupObject(String dn, List<String> randomAttributeIDs, String type) {
        Map<String, Object> formatMap = new HashMap<>();
        List<NameAwareAttribute> attributes = new ArrayList<>();
        NameAwareAttribute classAwareAttribute = new NameAwareAttribute("objectClass", "group");
        attributes.add(classAwareAttribute);

        if (WINDOWS_AD.equals(type)) {
            NameAwareAttribute dnAwareAttribute = new NameAwareAttribute("distinguishedName", dn);
            attributes.add(dnAwareAttribute);
        } else {
            NameAwareAttribute dnAwareAttribute = new NameAwareAttribute("entryDN", dn);
            attributes.add(dnAwareAttribute);

            NameAwareAttribute guidAttribute = new NameAwareAttribute("entryUUID", Platform.getUuid());
            attributes.add(guidAttribute);
        }

        if (randomAttributeIDs != null && !randomAttributeIDs.isEmpty()) {
            for (String attrID : randomAttributeIDs) {
                NameAwareAttribute nameAwareAttribute = new NameAwareAttribute(attrID, attrRandom.nextInt(ATTR_RANDOM));
                attributes.add(nameAwareAttribute);
            }
        }

        formatMap.put("dn", dn);
        formatMap.put("attributes", attributes);

        return formatMap;
    }

    public static List<Object> generateLdapUserEntrySet(String baseDN, int size, List<String> randomAttributeIDs, String type) {
        List<String> existDNEnties = new ArrayList<>();
        List<Object> objects = new ArrayList<>();

        for (int i = 0; i < size; i++) {
            String dn = null;
            while (true) {
                dn = generateDNFromConstant(baseDN);

                if (existDNEnties.contains(dn)) {
                    continue;
                }

                existDNEnties.add(dn);

                break;
            }

            Object obj = generateOrganizationObject(dn, randomAttributeIDs, type);
            objects.add(obj);
        }

        return objects;
    }

    // example: cn=Temp,ou=Groups,dc=example,dc=com
    public static Object generateLdapUserEntry(String dn, List<String> randomAttributeIDs, String type) {
        Map<String, Object> formatMap = new HashMap<>();

        formatMap.put("dn", generateDNFromConstant(dn));

        List<NameAwareAttribute> attributes = new ArrayList<>();
        NameAwareAttribute classAwareAttribute = new NameAwareAttribute("objectClass", "person");
        attributes.add(classAwareAttribute);

        if (WINDOWS_AD.equals(type)) {
            NameAwareAttribute dnAttribute = new NameAwareAttribute("distinguishedName", dn);
            attributes.add(dnAttribute);
        } else {
            NameAwareAttribute dnAttribute = new NameAwareAttribute("entryDN", dn);
            attributes.add(dnAttribute);

            NameAwareAttribute guidAttribute = new NameAwareAttribute("entryUUID", Platform.getUuid());
            attributes.add(guidAttribute);
        }

        if (randomAttributeIDs != null && !randomAttributeIDs.isEmpty()) {
            for (String attrID : randomAttributeIDs) {
                NameAwareAttribute nameAwareAttribute = new NameAwareAttribute(attrID, attrRandom.nextInt(ATTR_RANDOM));
                attributes.add(nameAwareAttribute);
            }
        }

        formatMap.put("attributes", attributes);

        return formatMap;
    }

    public static String generateDNFromConstant(String baseDN) {
        StringBuilder sb = new StringBuilder();

        sb.append("cn=");
        String cn = String.valueOf(cnRandom.nextInt(CN_NUM));
        sb.append(cn);
        sb.append(",");

        sb.append(baseDN);

        return sb.toString();
    }
}
