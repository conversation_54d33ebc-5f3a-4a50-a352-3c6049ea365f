package org.zstack.test.unittest.check;

import org.junit.Test;
import org.zstack.core.config.schema.GuestOsCategory;
import org.zstack.core.config.schema.GuestOsCharacter;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.utils.path.PathUtil;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class GuestOsConfigCheckCase {
    @Test
    public void testConfigKeepConsistent() {
        GuestOsCategory categoryConfigs;
        File guestOsCategoryFile = PathUtil.findFileOnClassPath("guestOs/guestOsCategory.xml");
        try {
            JAXBContext context = JAXBContext.newInstance("org.zstack.core.config.schema");
            Unmarshaller unmarshaller = context.createUnmarshaller();
            categoryConfigs = (GuestOsCategory) unmarshaller.unmarshal(guestOsCategoryFile);
        } catch (Exception e){
            throw new CloudRuntimeException(e);
        }

        GuestOsCharacter characterConfigs;
        File guestOsCharacterFile = PathUtil.findFileOnClassPath("guestOs/guestOsCharacter.xml");
        try {
            JAXBContext context = JAXBContext.newInstance("org.zstack.core.config.schema");
            Unmarshaller unmarshaller = context.createUnmarshaller();
            characterConfigs = (GuestOsCharacter) unmarshaller.unmarshal(guestOsCharacterFile);
        } catch (Exception e){
            throw new CloudRuntimeException(e);
        }

        Map<String, GuestOsCharacter.Config> characterConfigMap = new HashMap<>();
        characterConfigs.getOsInfo().forEach(c -> characterConfigMap.put(c.getOsRelease(), c));
        Map<String, GuestOsCategory.Config> categoryConfigMap = new HashMap<>();
        categoryConfigs.getOsInfo().forEach(c -> categoryConfigMap.put(c.getOsRelease(), c));

        Set<String> keyOnlyInCharacter = new HashSet<>(characterConfigMap.keySet());
        keyOnlyInCharacter.removeAll(categoryConfigMap.keySet());
        StringBuilder errorMsg = new StringBuilder();
        if (!keyOnlyInCharacter.isEmpty()) {
            for (String k : keyOnlyInCharacter) {
                errorMsg.append(String.format("osRelease %s only in guestOsCharacter.xml\n", k));
            }
        }

        assert errorMsg.length() == 0 : errorMsg.toString();
    }
}
