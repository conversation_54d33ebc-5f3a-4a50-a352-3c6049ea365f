package org.zstack.test.unittest.ecsapiadapter.snapshot

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.api.ecs.snapshot.CancelAutoSnapshotPolicy
import org.zstack.sdk.DeleteSchedulerJobAction
import org.zstack.sdk.DeleteSchedulerJobResult
import org.zstack.sdk.QuerySchedulerJobAction
import org.zstack.sdk.QuerySchedulerJobResult
import org.zstack.sdk.QuerySchedulerTriggerAction
import org.zstack.sdk.QuerySchedulerTriggerResult
import org.zstack.sdk.RemoveSchedulerJobFromSchedulerTriggerAction
import org.zstack.sdk.RemoveSchedulerJobFromSchedulerTriggerResult
import org.zstack.sdk.SchedulerJobInventory
import org.zstack.sdk.SchedulerTriggerInventory
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * @Author: fubang
 * @Date: 2018/5/1
 */
class CancelAutoSnapshotPolicyCase {
    @Test
    void testZStackRspToEcsRsp() {
        def triggerId = "4624114de8234fab815117ec2ff11f89"
        def jobId = "9025be79396a416690747d9a09782de9"
        QuerySchedulerTriggerAction.metaClass.call = {
            QuerySchedulerTriggerAction.Result result = new QuerySchedulerTriggerAction.Result()
            QuerySchedulerTriggerResult value = new QuerySchedulerTriggerResult()
            result.value = value
            value.inventories = [new SchedulerTriggerInventory(uuid: triggerId)]

            return result
        }
        QuerySchedulerJobAction.metaClass.call = {
            QuerySchedulerJobAction.Result result = new QuerySchedulerJobAction.Result()
            QuerySchedulerJobResult value = new QuerySchedulerJobResult()
            result.value = value

            value.inventories = [new SchedulerJobInventory(uuid: jobId)]
            return result
        }

        RemoveSchedulerJobFromSchedulerTriggerAction.metaClass.call = {
            assert delegate.schedulerJobUuid == jobId
            assert delegate.schedulerTriggerUuid == triggerId
            RemoveSchedulerJobFromSchedulerTriggerAction.Result result = new RemoveSchedulerJobFromSchedulerTriggerAction.Result()
            RemoveSchedulerJobFromSchedulerTriggerResult value = new RemoveSchedulerJobFromSchedulerTriggerResult()
            result.value = value

            return result
        }

        DeleteSchedulerJobAction.metaClass.call = {
            assert delegate.uuid == jobId
            DeleteSchedulerJobAction.Result result = new DeleteSchedulerJobAction.Result()
            DeleteSchedulerJobResult value = new DeleteSchedulerJobResult()
            result.value = value

            return result
        }

        Map ecsAPIParam = [
                "Action"     : "CancelAutoSnapshotPolicy",
                "DiskIds"    : "['4624114de8234fab815117ec2ff11f89','47ce1d04c1614a139ab3ab3ee56ee564']",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        CancelAutoSnapshotPolicy api = new CancelAutoSnapshotPolicy()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        def result = api.callZStackAction()

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
    }
}
