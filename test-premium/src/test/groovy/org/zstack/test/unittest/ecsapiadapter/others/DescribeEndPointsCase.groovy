package org.zstack.test.unittest.ecsapiadapter.others

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterGlobalProperty
import org.zstack.pluginpremium.externalapiadapter.api.ecs.others.DescribeEndpoints
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.ECS_API_REQUESTID_KEY

/**
 * Created by lining on 2018/5/04.
 */
class DescribeEndPointsCase {
    @Test
    void testZStackRspToEcsRsp() {
        ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_REGIONID = "cn-shanghai"
        ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL = "ecs-cn-hangzhou.aliyuncs.com"

        Map ecsAPIParam = [
                "Action"     : "DescribeEndpoints",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        DescribeEndpoints api = new DescribeEndpoints()
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        Map ecsAPIRsp = api.getEcsAPIRsp(api.callZStackAction())
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) == api.requestId
        assert ecsAPIRsp.get("Endpoints").get("Endpoint").size == 1
        assert ecsAPIRsp.get("Success") == true

        Map tmp = ecsAPIRsp.get("Endpoints").get("Endpoint").get(0)
        assert tmp.get("Protocols").get("Protocols").get(0) == "HTTP"
        assert tmp.get("Namespace") == "26842"
        assert tmp.get("SerivceCode") == "ecs"
        assert tmp.get("Id") == ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_REGIONID
        assert tmp.get("Endpoint") == ExternalAPIAdapterGlobalProperty.ECS_ENDPOINT_URL
    }
}
