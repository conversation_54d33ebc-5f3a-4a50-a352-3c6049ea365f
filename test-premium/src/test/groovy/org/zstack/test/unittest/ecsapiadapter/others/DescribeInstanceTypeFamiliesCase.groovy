package org.zstack.test.unittest.ecsapiadapter.others

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants
import org.zstack.pluginpremium.externalapiadapter.api.ecs.others.DescribeInstanceTypeFamilies
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil
import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*


/**
 * @Author: fubang
 * @Date: 2018/4/30
 */
class DescribeInstanceTypeFamiliesCase {
    @Test
    void testZStackRspToEcsRsp() {
        Map ecsAPIParam = [
                "Action"     : "DescribeInstanceTypeFamilies",
                "AccessKeyId": APIAdapterTestUtils.ECS_ACCESSKEY,
                "ignoreSignature" : "true",
                "Signature" : "abc"
        ]
        DescribeInstanceTypeFamilies api = new DescribeInstanceTypeFamilies()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        def result = api.callZStackAction()

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null
        assert ecsAPIRsp.get("InstanceTypeFamilies") != null
        assert ecsAPIRsp.get("InstanceTypeFamilies").get("InstanceTypeFamily") != null
        assert ecsAPIRsp.get("InstanceTypeFamilies").get("InstanceTypeFamily").size() == 1

        Map tmp = ecsAPIRsp.get("InstanceTypeFamilies").get("InstanceTypeFamily").get(0)
        assert tmp.get("InstanceTypeFamilyId") == ExternalAPIAdapterConstants.SUPPORT_RESOURCE_INFO.INSTANCE_TYPE_FAMILY
        assert tmp.get("Generation") == ExternalAPIAdapterConstants.SUPPORT_RESOURCE_INFO.INSTANCE_TYPE_GENERATION
    }
}
