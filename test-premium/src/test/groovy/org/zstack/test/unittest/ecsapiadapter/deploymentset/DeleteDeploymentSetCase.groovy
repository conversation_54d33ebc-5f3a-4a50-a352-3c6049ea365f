package org.zstack.test.unittest.ecsapiadapter.deploymentset

import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.deploymentset.DeleteDeploymentSet
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * Created by lining on 2018/4/29.
 */
class DeleteDeploymentSetCase {

    @Test
    void testEcsParamToZStackParam() {
        String deploymentSetId = ExternalAPIAdapterUtils.randomUUID()

        Map ecsAPIParam = [
                "Action" : "DeleteDeploymentSet",
                "RegionId" : "cn-hangzhou",
                "DeploymentSetId" : deploymentSetId
        ]
        DeleteDeploymentSet api = new DeleteDeploymentSet()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")

        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)
        assert zstackAPIParamMap.size() == 3
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"
        assert zstackAPIParamMap.get("uuid") == deploymentSetId
        assert zstackAPIParamMap.get(ZSTACK_API_APIID_KEY) != null
    }
}
