package org.zstack.test.unittest.sns;

import org.junit.Test;
import org.zstack.utils.DomainUtils;

public class DomainCase {

    private String url1 = "http://www.baidu.com?parma1=aa&&parama2=xxx";
    private String url2 = "https://www.baidu.com?parma1=aa&&parama2=xxx";
    private String url3 = "smtp.163.com";
    private String url4 = "localhost:5000";

    @Test
    public void testGetDomain() {
        String domain = DomainUtils.getDomain(url1);
        assert domain.equals("www.baidu.com");

        domain = DomainUtils.getDomain(url2);
        assert domain.equals("www.baidu.com");

        domain = DomainUtils.getDomain(url3);
        assert domain.equals("smtp.163.com");

        domain = DomainUtils.getDomain(url4);
        assert domain.equals("localhost");
    }
}
