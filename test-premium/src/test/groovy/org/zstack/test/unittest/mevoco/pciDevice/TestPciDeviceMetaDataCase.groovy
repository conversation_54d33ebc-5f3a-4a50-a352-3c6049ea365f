package org.zstack.test.unittest.mevoco.pciDevice

import org.junit.Test
import org.zstack.pciDevice.PciDeviceMetaData
import org.zstack.pciDevice.PciDeviceMetaDataEntry

/**
 * Created by weiwang on 08/07/2017.
 */
class TestPciDeviceMetaDataCase {
    @Test
    void testPciDeviceMetaData() {
        PciDeviceMetaData p1 = new PciDeviceMetaData("abc:Equal:1;bac:Equal:2")
        assert p1.metaDataEntries.size() == 2

        assert p1.metaDataEntries[0].key == "abc"
        assert p1.metaDataEntries[0].op == PciDeviceMetaDataEntry.PciDeviceMetaDataOperator.Equal
        assert p1.metaDataEntries[0].value == "1"
        assert p1.metaDataEntries[0] == new PciDeviceMetaDataEntry("abc:Equal:1")

        PciDeviceMetaData p2 = new PciDeviceMetaData("abc:Equal:1;bac:Equal:2")
        PciDeviceMetaData p3 = new PciDeviceMetaData("abc:Equal:1;cb_a:Equal:2")
        PciDeviceMetaData p4 = new PciDeviceMetaData("abc:Equal:1;bac:Equal:3")
        PciDeviceMetaData p5 = new PciDeviceMetaData("abc:Unequal:1")
        PciDeviceMetaData p6 = new PciDeviceMetaData("abc:Unequal:2")
        PciDeviceMetaData p7 = new PciDeviceMetaData()

        assert p1.matches(p1)
        assert p1.matches(p2)
        assert p1.matches(p3)
        assert !p1.matches(p4)
        assert !p1.matches(p5)
        assert p1.matches(p6)
        assert p1.matches(p7)
        assert p1.matches(null)
        assert !p7.matches(p1)
        assert p7.matches(p5)
    }
}
