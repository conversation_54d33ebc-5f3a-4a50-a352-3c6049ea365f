package org.zstack.test.unittest.ecsapiadapter.vpc

import com.aliyuncs.vpc.model.v20160428.CreateRouteTableResponse
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.vpc.CreateRouteTable
import org.zstack.sdk.AliyunProxyVpcInventory
import org.zstack.sdk.AttachVRouterRouteTableToVRouterAction
import org.zstack.sdk.AttachVRouterRouteTableToVRouterResult
import org.zstack.sdk.CreateVRouterRouteTableAction
import org.zstack.sdk.CreateVRouterRouteTableResult
import org.zstack.sdk.QueryAliyunProxyVpcAction
import org.zstack.sdk.QueryAliyunProxyVpcResult
import org.zstack.sdk.VRouterRouteTableInventory
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

/**
 * Created by Qi Le on 2019-05-31
 */
class CreateRouteTableCase {
    @Test
    void testEcsParamToZStackParam() {
        String vpcId = ExternalAPIAdapterUtils.randomUUID()

        Map ecsAPIParam = [
                "Action": "CreateRouteTable",
                "RouteTableName" : "routeTable",
                "VpcId" : vpcId
        ]

        CreateRouteTable api = new CreateRouteTable(
                sessionId: ExternalAPIAdapterUtils.randomUUID(),
                requestId: "97efe8c4a6c040b68e52745acd1d0b15"
        )

        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: \n" + JSONObjectUtil.toJsonString(zstackAPIParamMap)
        assert zstackAPIParamMap.get("name") == "routeTable"
    }

    @Test
    void testZStackRspToEcsRsp() {
        String vpcId = ExternalAPIAdapterUtils.randomUUID()
        String sessionId = ExternalAPIAdapterUtils.randomUUID()
        String requestId = ExternalAPIAdapterUtils.randomUUID()
        String routeTableId = ExternalAPIAdapterUtils.randomUUID()
        String vRouterId = ExternalAPIAdapterUtils.randomUUID()

        Map ecsAPIParam = [
                "Action" : "CreateRouteTable",
                "VpcId" : vpcId
        ]

        CreateRouteTable api = new CreateRouteTable(
                sessionId: sessionId,
                requestId: requestId
        )
        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        api.zstackAPIParamMap = zstackAPIParamMap

        CreateVRouterRouteTableAction.metaClass.call = {
            CreateVRouterRouteTableAction self = delegate
            assert self.sessionId == sessionId

            return new CreateVRouterRouteTableAction.Result(
                    value: new CreateVRouterRouteTableResult(
                            inventory: new VRouterRouteTableInventory(
                                    uuid: routeTableId,
                                    name: zstackAPIParamMap.get("name")
                            )
                    )
            )
        }

        QueryAliyunProxyVpcAction.metaClass.call = {
            QueryAliyunProxyVpcAction self = delegate
            assert self.sessionId == sessionId
            assert self.conditions.size() == 1
            assert self.conditions.contains("uuid=$vpcId".toString())

            return new QueryAliyunProxyVpcAction.Result(
                    value: new QueryAliyunProxyVpcResult(
                            inventories: [
                                    new AliyunProxyVpcInventory(
                                            uuid: vpcId,
                                            vpcName: "IAMCOOL",
                                            cidrBlock: "***********/16",
                                            vRouterUuid: vRouterId,
                                            isDefault: true
                                    )
                            ]
                    )
            )
        }

        AttachVRouterRouteTableToVRouterAction.metaClass.call = {
            AttachVRouterRouteTableToVRouterAction self = delegate
            assert self.sessionId == sessionId
            assert self.routeTableUuid == routeTableId
            assert self.virtualRouterVmUuid == vRouterId

            return new AttachVRouterRouteTableToVRouterAction.Result(
                    value: new AttachVRouterRouteTableToVRouterResult()
            )
        }

        String result = api.call(ecsAPIParam)

        println result

        CreateRouteTableResponse response = APIAdapterTestUtils.parseJsonStringToECSAPIResponse(result, CreateRouteTableResponse.class)
        assert response.requestId == api.requestId
        assert response.routeTableId == routeTableId
    }
}
