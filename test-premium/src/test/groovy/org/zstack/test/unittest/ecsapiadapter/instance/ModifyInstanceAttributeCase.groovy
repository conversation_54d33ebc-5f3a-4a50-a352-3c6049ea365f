package org.zstack.test.unittest.ecsapiadapter.instance

import org.junit.Before
import org.junit.Test
import org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterUtils
import org.zstack.pluginpremium.externalapiadapter.api.ecs.instance.ModifyInstanceAttribute
import org.zstack.sdk.ChangeVmPasswordAction
import org.zstack.sdk.ChangeVmPasswordResult
import org.zstack.sdk.CreateSystemTagAction
import org.zstack.sdk.CreateSystemTagResult
import org.zstack.sdk.QuerySystemTagAction
import org.zstack.sdk.QuerySystemTagResult
import org.zstack.sdk.SetVmHostnameAction
import org.zstack.sdk.SetVmHostnameResult
import org.zstack.sdk.SystemTagInventory
import org.zstack.sdk.UpdateVmInstanceAction
import org.zstack.sdk.UpdateVmInstanceResult
import org.zstack.sdk.VmInstanceInventory
import org.zstack.test.unittest.ecsapiadapter.APIAdapterTestUtils
import org.zstack.utils.gson.JSONObjectUtil

import static org.zstack.pluginpremium.externalapiadapter.ExternalAPIAdapterConstants.*

/**
 * @Author: fubang
 * @Date: 2018/4/23
 */
class ModifyInstanceAttributeCase {
    String vmId = ExternalAPIAdapterUtils.randomUUID()
    String password = "zstack123"
    String hostName = "newhost"
    String userdata = new String(Base64.getEncoder().encode("#cloud-config ".getBytes()))

    def changeVmPassword = false
    def setVmHostname = false
    def querySystemTag = false
    def createSystemTag = false

    @Before
    void prepare() {
        ChangeVmPasswordAction.metaClass.call = {
            assert delegate.uuid == vmId
            assert delegate.account == "root"
            assert delegate.password == password

            changeVmPassword = true
            ChangeVmPasswordAction.Result result = new ChangeVmPasswordAction.Result()
            ChangeVmPasswordResult value = new ChangeVmPasswordResult()
            result.value = value
            return result
        }

        SetVmHostnameAction.metaClass.call = {
            assert delegate.uuid == vmId
            assert delegate.hostname == hostName

            setVmHostname = true
            SetVmHostnameAction.Result result = new SetVmHostnameAction.Result()
            SetVmHostnameResult value = new SetVmHostnameResult()
            result.value = value
            return result
        }

        QuerySystemTagAction.metaClass.call = {
            querySystemTag = true

            QuerySystemTagAction.Result result = new QuerySystemTagAction.Result()
            QuerySystemTagResult value = new QuerySystemTagResult()
            result.value = value

            value.total = 1
            value.inventories = [
                    new SystemTagInventory(
                            uuid: ExternalAPIAdapterUtils.randomUUID(),
                            tag: "usbRedirect::false")
            ]

            return result
        }

        CreateSystemTagAction.metaClass.call = {
            assert delegate.resourceUuid == vmId
            assert delegate.tag == "userdata::$userdata".toString()

            createSystemTag = true
            CreateSystemTagAction.Result result = new CreateSystemTagAction.Result()
            CreateSystemTagResult value = new CreateSystemTagResult()
            result.value = value
            return result
        }

    }

    @Test
    void testEcsParamToZStackParam() {
        Map ecsAPIParam = [
                "Action"      : "ModifyInstanceAttribute",
                "InstanceId"  : "ebb57b74d1e94a1db2b50bc5bb1062af",
                "InstanceName": "vm-vm",
                "Description" : "this is some description about the vm",
                "AccessKeyId" : APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        ModifyInstanceAttribute api = new ModifyInstanceAttribute()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        Map zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        println "zstack api param map: " + JSONObjectUtil.toJsonString(zstackAPIParamMap)

        assert zstackAPIParamMap.size() == 5
        assert zstackAPIParamMap.get(ZSTACK_UUID) == "ebb57b74d1e94a1db2b50bc5bb1062af"
        assert zstackAPIParamMap.get(ZSTACK_SESSIONID_KEY) == "97efe8c4a6c040b68e52745acd1d0b15"
        assert zstackAPIParamMap.get(ZSTACK_NAME) == "vm-vm"
        assert zstackAPIParamMap.get(ZSTACK_API_DESCRIPTION_KEY) == "this is some description about the vm"
        assert zstackAPIParamMap.get(ZSTACK_API_APIID_KEY) != null

        ecsAPIParam = [
                "Action"      : "ModifyInstanceAttribute",
                "InstanceId"  : "ebb57b74d1e94a1db2b50bc5bb1062af",
                "SessionId"   : "97efe8c4a6c040b68e52745acd1d0b15",
                "InstanceName": "vm-vm-vm",
                "AccessKeyId" : APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)
        assert zstackAPIParamMap.get(ZSTACK_NAME) == "vm-vm-vm"
    }

    @Test
    void testZStackRspToEcsRsp() {
        Map ecsAPIParam = [
                "Action"      : "ModifyInstanceAttribute",
                "InstanceId"  : vmId,
                "SessionId"   : "97efe8c4a6c040b68e52745acd1d0b15",
                "InstanceName": "vm-vm",
                "Description" : "this is some description about the vm",
                "Password"    : password,
                "HostName"    : hostName,
                "UserData"    : userdata,
                "AccessKeyId" : APIAdapterTestUtils.ECS_ACCESSKEY
        ]
        ModifyInstanceAttribute api = new ModifyInstanceAttribute()
        api.setSessionId("97efe8c4a6c040b68e52745acd1d0b15")
        api.setRequestId("97efe8c4a6c040b68e52745acd1d0b15")
        api.ecsAPIParamMap = ecsAPIParam
        api.zstackAPIParamMap = api.getZstackAPIParam(ecsAPIParam)

        UpdateVmInstanceAction.Result result = new UpdateVmInstanceAction.Result()
        UpdateVmInstanceResult value = new UpdateVmInstanceResult(
                inventory: new VmInstanceInventory(
                        uuid: vmId,
                        name: "vm-vm",
                        description: "this is some description about the vm",
                        platform: "Linux",
                        state: "Running"
                )
        )
        result.value = value

        Map ecsAPIRsp = api.getEcsAPIRsp(result)
        println JSONObjectUtil.toJsonString(ecsAPIRsp)

        assert ecsAPIRsp.get(ECS_API_REQUESTID_KEY) != null

        api.afterCallZStackAction(result)

        // lining123
        // todo
        //assert changeVmPassword
        assert setVmHostname
        assert querySystemTag
        assert createSystemTag
    }
}
