package org.zstack.test.api

import org.springframework.http.HttpEntity
import org.zstack.header.image.ImageConstant
import org.zstack.header.image.ImagePlatform
import org.zstack.sdk.ClusterInventory
import org.zstack.sdk.CreateVmInstanceResult
import org.zstack.sdk.DestroyVmInstanceResult
import org.zstack.sdk.DiskOfferingInventory
import org.zstack.sdk.ExpungeVmInstanceResult
import org.zstack.sdk.ImageInventory
import org.zstack.sdk.InstanceOfferingInventory
import org.zstack.sdk.L3NetworkInventory
import org.zstack.sdk.QueryVmInstanceResult
import org.zstack.test.integration.premium.PremiumTest
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.testlib.premium.http.HttpUtil
import org.zstack.utils.data.SizeUnit
import org.zstack.utils.gson.JSONObjectUtil

class TestVmCase extends PremiumSubCase {
    EnvSpec env

    @Override
    void setup() {
        useSpring(PremiumTest.springSpec)
    }

    @Override
    void environment() {
        env = env {
            instanceOffering {
                name = "instanceOffering"
                memory = SizeUnit.GIGABYTE.toByte(4)
                cpu = 1
            }

            diskOffering {
                name = "diskOffering"
                diskSize = SizeUnit.GIGABYTE.toByte(10)
            }

            cephBackupStorage {
                name = "ceph-bs"
                totalCapacity = SizeUnit.GIGABYTE.toByte(100)
                availableCapacity = SizeUnit.GIGABYTE.toByte(100)
                url = "/bs"
                fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                monUrls = ["root:password@localhost/?monPort=7777"]

                image {
                    name = "image1"
                    url = "http://zstack.org/download/test.iso"
                    mediaType = ImageConstant.ImageMediaType.ISO.toString()
                    platform = ImagePlatform.Linux.toString()
                }

                image {
                    name = "image2"
                    url = "http://zstack.org/download/test2.qcow2"
                    size = SizeUnit.GIGABYTE.toByte(10)
                    platform = ImagePlatform.Windows.toString()
                }

                image {
                    name = "image3"
                    url = "http://zstack.org/download/test3.qcow2"
                    size = SizeUnit.GIGABYTE.toByte(10)
                    platform = ImagePlatform.Linux.toString()
                }

                image {
                    name = "vr"
                    url = "http://zstack.org/download/vr.qcow2"
                    system = true
                }
            }

            zone {
                name = "zone"
                description = "test"

                cluster {
                    name = "cluster"
                    hypervisorType = "KVM"

                    kvm {
                        name = "kvm1"
                        managementIp = "127.0.0.1"
                        username = "root"
                        password = "password"
                        totalMem = SizeUnit.GIGABYTE.toByte(32)
                        totalCpu = 8
                    }

                    attachPrimaryStorage("ceph-ps")
                    attachL2Network("l2")
                }

                cephPrimaryStorage {
                    name = "ceph-ps"
                    fsid = "7ff218d9-f525-435f-8a40-3618d1772a64"
                    monUrls = ["root:password@localhost/?monPort=7777", "root:password@127.0.0.1/?monPort=7777"]
                }

                l2NoVlanNetwork {
                    name = "l2"
                    physicalInterface = "eth0"

                    l3Network {
                        name = "pubL3"

                        ip {
                            startIp = "**********0"
                            endIp = "**********00"
                            netmask = "*************"
                            gateway = "**********"
                        }
                    }
                }

                attachBackupStorage("ceph-bs")
            }

            vm {
                name = "vm"
                useInstanceOffering("instanceOffering")
                useImage("image1")
                useRootDiskOffering("diskOffering")
                useDiskOfferings("diskOffering")
                useL3Networks("pubL3")
                useHost("kvm1")
            }
        }

    }

    @Override
    void test() {
        env.create {
            testVm()
        }
    }

    void testVm() {

        // Query VM
        Map<String, Object> res = HttpUtil.request(HttpUtil.BASE + "/vm-instances", "GET", null, false, adminSession())

        assert res != null
        assert res.size() != null

        def result = JSONObjectUtil.toObject((String) res.get("result"), QueryVmInstanceResult.class)
        assert result != null

        // Create VM
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        ImageInventory image = env.inventoryByName("image2")
        L3NetworkInventory l3Network = env.inventoryByName("pubL3")
        DiskOfferingInventory diskOffering = env.inventoryByName("diskOffering")
        ClusterInventory cluster = env.inventoryByName("cluster")

        String body1 = "{\"params\":{\"name\":\"vm-api\",\"instanceOfferingUuid\":\"" + instanceOffering.uuid + "\",\"imageUuid\":\"" + image.uuid + "\",\"l3NetworkUuids\":[\"" + l3Network.uuid + "\"],\"dataDiskOfferingUuids\":[\"" + diskOffering.uuid + "\"],\"clusterUuid\":\"" + cluster.uuid + "\",\"description\":\"this is a vm\",\"strategy\":\"InstantStart\"},\"systemTags\":[],\"userTags\":[]}"
        HttpEntity<String> entity1 = new HttpEntity<>(body1, null)
        Map<String, Object> res1 = HttpUtil.request(HttpUtil.BASE + "/vm-instances", "POST", entity1, false, adminSession())
        assert res1 != null
        assert res1.size() != null

        def result1 = JSONObjectUtil.toObject((String) res1.get("result"), CreateVmInstanceResult.class)
        assert result1 != null

        // delete VM
        Map<String, Object> res2 = HttpUtil.request(HttpUtil.BASE + "/vm-instances/" + result1.inventory.uuid, "DELETE", null, false, adminSession())

        assert res2 != null
        assert res2.size() != null

        DestroyVmInstanceResult result2 = JSONObjectUtil.toObject((String) res2.get("result"), DestroyVmInstanceResult.class)
        assert result2 == null

        // expunge VM
        String body3 = "{\"expungeVmInstance\":{}}"
        HttpEntity<String> entity3 = new HttpEntity<>(body3, null)
        Map<String, Object> res3 = HttpUtil.request(HttpUtil.BASE + "/vm-instances/" + result1.inventory.uuid + "/actions", "PUT", entity3, false, adminSession())

        assert res3 != null
        assert res3.size() != null

        ExpungeVmInstanceResult result3 = JSONObjectUtil.toObject((String) res3.get("result"), ExpungeVmInstanceResult.class)
        assert result3 == null
    }

    @Override
    void clean() {
        env.delete()
    }
}
