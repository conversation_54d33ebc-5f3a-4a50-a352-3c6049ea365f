package org.zstack.header.cbt

import org.zstack.header.cbt.CbtTaskInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "停止CBT任务的返回结果"

	ref {
		name "CBT任务的详细信息"
		path "org.zstack.header.cbt.APIDisableCbtTaskEvent.inventory"
		desc "null"
		type "CbtTaskInventory"
		since "5.3.28"
		clz CbtTaskInventory.class
	}
	field {
		name "success"
		desc "成功"
		type "boolean"
		since "5.3.28"
	}
	ref {
		name "error"
		path "org.zstack.header.cbt.APIDisableCbtTaskEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.3.28"
		clz ErrorCode.class
	}
}
