package org.zstack.sdk;



public class EcsInstanceInventory  {

    public java.lang.String uuid;
    public void setUuid(java.lang.String uuid) {
        this.uuid = uuid;
    }
    public java.lang.String getUuid() {
        return this.uuid;
    }

    public java.lang.String localVmInstanceUuid;
    public void setLocalVmInstanceUuid(java.lang.String localVmInstanceUuid) {
        this.localVmInstanceUuid = localVmInstanceUuid;
    }
    public java.lang.String getLocalVmInstanceUuid() {
        return this.localVmInstanceUuid;
    }

    public java.lang.String ecsInstanceId;
    public void setEcsInstanceId(java.lang.String ecsInstanceId) {
        this.ecsInstanceId = ecsInstanceId;
    }
    public java.lang.String getEcsInstanceId() {
        return this.ecsInstanceId;
    }

    public java.lang.String name;
    public void setName(java.lang.String name) {
        this.name = name;
    }
    public java.lang.String getName() {
        return this.name;
    }

    public java.lang.String ecsStatus;
    public void setEcsStatus(java.lang.String ecsStatus) {
        this.ecsStatus = ecsStatus;
    }
    public java.lang.String getEcsStatus() {
        return this.ecsStatus;
    }

    public java.lang.Long cpuCores;
    public void setCpuCores(java.lang.Long cpuCores) {
        this.cpuCores = cpuCores;
    }
    public java.lang.Long getCpuCores() {
        return this.cpuCores;
    }

    public java.lang.Long memorySize;
    public void setMemorySize(java.lang.Long memorySize) {
        this.memorySize = memorySize;
    }
    public java.lang.Long getMemorySize() {
        return this.memorySize;
    }

    public java.lang.String ecsInstanceType;
    public void setEcsInstanceType(java.lang.String ecsInstanceType) {
        this.ecsInstanceType = ecsInstanceType;
    }
    public java.lang.String getEcsInstanceType() {
        return this.ecsInstanceType;
    }

    public java.lang.Long ecsBandWidth;
    public void setEcsBandWidth(java.lang.Long ecsBandWidth) {
        this.ecsBandWidth = ecsBandWidth;
    }
    public java.lang.Long getEcsBandWidth() {
        return this.ecsBandWidth;
    }

    public java.lang.String ecsRootVolumeId;
    public void setEcsRootVolumeId(java.lang.String ecsRootVolumeId) {
        this.ecsRootVolumeId = ecsRootVolumeId;
    }
    public java.lang.String getEcsRootVolumeId() {
        return this.ecsRootVolumeId;
    }

    public java.lang.String ecsRootVolumeCategory;
    public void setEcsRootVolumeCategory(java.lang.String ecsRootVolumeCategory) {
        this.ecsRootVolumeCategory = ecsRootVolumeCategory;
    }
    public java.lang.String getEcsRootVolumeCategory() {
        return this.ecsRootVolumeCategory;
    }

    public java.lang.Long ecsRootVolumeSize;
    public void setEcsRootVolumeSize(java.lang.Long ecsRootVolumeSize) {
        this.ecsRootVolumeSize = ecsRootVolumeSize;
    }
    public java.lang.Long getEcsRootVolumeSize() {
        return this.ecsRootVolumeSize;
    }

    public java.lang.String privateIpAddress;
    public void setPrivateIpAddress(java.lang.String privateIpAddress) {
        this.privateIpAddress = privateIpAddress;
    }
    public java.lang.String getPrivateIpAddress() {
        return this.privateIpAddress;
    }

    public java.lang.String publicIpAddress;
    public void setPublicIpAddress(java.lang.String publicIpAddress) {
        this.publicIpAddress = publicIpAddress;
    }
    public java.lang.String getPublicIpAddress() {
        return this.publicIpAddress;
    }

    public java.lang.String ecsVSwitchUuid;
    public void setEcsVSwitchUuid(java.lang.String ecsVSwitchUuid) {
        this.ecsVSwitchUuid = ecsVSwitchUuid;
    }
    public java.lang.String getEcsVSwitchUuid() {
        return this.ecsVSwitchUuid;
    }

    public java.lang.String ecsImageUuid;
    public void setEcsImageUuid(java.lang.String ecsImageUuid) {
        this.ecsImageUuid = ecsImageUuid;
    }
    public java.lang.String getEcsImageUuid() {
        return this.ecsImageUuid;
    }

    public java.lang.String ecsSecurityGroupUuid;
    public void setEcsSecurityGroupUuid(java.lang.String ecsSecurityGroupUuid) {
        this.ecsSecurityGroupUuid = ecsSecurityGroupUuid;
    }
    public java.lang.String getEcsSecurityGroupUuid() {
        return this.ecsSecurityGroupUuid;
    }

    public java.lang.String identityZoneUuid;
    public void setIdentityZoneUuid(java.lang.String identityZoneUuid) {
        this.identityZoneUuid = identityZoneUuid;
    }
    public java.lang.String getIdentityZoneUuid() {
        return this.identityZoneUuid;
    }

    public java.lang.String chargeType;
    public void setChargeType(java.lang.String chargeType) {
        this.chargeType = chargeType;
    }
    public java.lang.String getChargeType() {
        return this.chargeType;
    }

    public java.sql.Timestamp expireDate;
    public void setExpireDate(java.sql.Timestamp expireDate) {
        this.expireDate = expireDate;
    }
    public java.sql.Timestamp getExpireDate() {
        return this.expireDate;
    }

    public java.sql.Timestamp createDate;
    public void setCreateDate(java.sql.Timestamp createDate) {
        this.createDate = createDate;
    }
    public java.sql.Timestamp getCreateDate() {
        return this.createDate;
    }

    public java.sql.Timestamp lastOpDate;
    public void setLastOpDate(java.sql.Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
    public java.sql.Timestamp getLastOpDate() {
        return this.lastOpDate;
    }

    public java.lang.String description;
    public void setDescription(java.lang.String description) {
        this.description = description;
    }
    public java.lang.String getDescription() {
        return this.description;
    }

}
