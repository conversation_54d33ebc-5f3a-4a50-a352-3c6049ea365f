package org.zstack.crypto.securitymachine;

import org.zstack.header.message.MessageReply;

/**
 * Created by LiangHanYu on 2021/11/15 18:17
 */
public class SecurityMachineHeartbeatDetectionReply extends MessageReply {
    private boolean enabled;
    private String currentSecurityMachineState;

    public String getCurrentSecurityMachineState() {
        return currentSecurityMachineState;
    }

    public void setCurrentSecurityMachineState(String currentSecurityMachineState) {
        this.currentSecurityMachineState = currentSecurityMachineState;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}