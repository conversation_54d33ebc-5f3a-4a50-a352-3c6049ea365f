package org.zstack.crypto.securitymachine.secretresourcepool;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.db.Q;
import org.zstack.core.thread.AsyncThread;
import org.zstack.core.thread.AsyncTimer;
import org.zstack.header.Component;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.managementnode.ManagementNodeChangeListener;
import org.zstack.header.managementnode.ManagementNodeInventory;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.message.MessageReply;
import org.zstack.header.securitymachine.SecretResourcePoolConstant;
import org.zstack.header.securitymachine.SecretResourcePoolType;
import org.zstack.header.securitymachine.SecretResourcePoolVO;
import org.zstack.header.securitymachine.SecretResourcePoolVO_;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by LiangHanYu on 2022/8/23 17:16
 */
public class SecretResourcePoolTrackerImpl implements SecretResourcePoolTracker, ManagementNodeChangeListener, Component, ManagementNodeReadyExtensionPoint {
    private final static CLogger logger = Utils.getLogger(SecretResourcePoolTrackerImpl.class);

    private Map<String, SecretResourcePoolTrackerImpl.Tracker> trackers = new HashMap<>();
    private final static Long interval = 6L;

    @Autowired
    private ResourceDestinationMaker destMaker;
    @Autowired
    private CloudBus bus;
    @Autowired
    protected EventFacade evtf;


    @Override
    public void managementNodeReady() {
        reScanSecretResourcePool();
    }

    @Override
    public void trackSecretResourcePool(String SecretResourcePoolUuid) {
        SecretResourcePoolTrackerImpl.Tracker t = trackers.get(SecretResourcePoolUuid);
        if (t != null) {
            t.cancel();
        }

        t = new SecretResourcePoolTrackerImpl.Tracker(SecretResourcePoolUuid);
        trackers.put(SecretResourcePoolUuid, t);

        if (CoreGlobalProperty.UNIT_TEST_ON) {
            t.start();
        } else {
            t.startRightNow();
        }

        logger.debug(String.format("starting tracking SecretResourcePool[uuid:%s]", SecretResourcePoolUuid));
    }

    @Override
    public void untrackSecretResourcePool(String SecretResourcePoolUuid) {
        SecretResourcePoolTrackerImpl.Tracker t = trackers.get(SecretResourcePoolUuid);
        if (t != null) {
            t.cancel();
        }
        trackers.remove(SecretResourcePoolUuid);
        logger.debug(String.format("stop tracking SecretResourcePool[uuid:%s]", SecretResourcePoolUuid));
    }

    @Override
    public void trackSecretResourcePool(Collection<String> SecretResourcePoolUuids) {
        SecretResourcePoolUuids.forEach(this::trackSecretResourcePool);
    }

    private class Tracker extends AsyncTimer {
        private final CLogger logger = Utils.getLogger(SecretResourcePoolTrackerImpl.class);

        private String uuid;
        private Integer heartbeatInterval;

        Tracker(String uuid) {
            super(TimeUnit.SECONDS, interval);
            this.uuid = uuid;
            heartbeatInterval = Q.New(SecretResourcePoolVO.class).select(SecretResourcePoolVO_.heartbeatInterval)
                    .eq(SecretResourcePoolVO_.uuid, uuid).findValue();
            if (heartbeatInterval == null) {
                throw new CloudRuntimeException(String.format("secret resource pool[uuid:%s] is deleted, why you submit a tracker for it???", uuid));
            }
            super.period = heartbeatInterval.longValue();
            __name__ = String.format("secret-resource-pool-tracker-%s", uuid);
        }

        @Override
        protected void execute() {
            track();
        }

        private void track() {
            SecretResourcePoolType poolType = Q.New(SecretResourcePoolVO.class).select(SecretResourcePoolVO_.type)
                    .eq(SecretResourcePoolVO_.uuid, uuid).findValue();
            if (poolType == null) {
                logger.debug(String.format("secret resource pool[uuid:%s] seems to be deleted, stop tracking it", uuid));
                return;
            }

            if (!(poolType == SecretResourcePoolType.CloudSecurityResourceService || poolType == SecretResourcePoolType.Plugin)) {
                logger.debug(String.format("Health track currently only supports resource pools of type [%s], but received type [%s].",
                        String.join(", ", Arrays.asList(
                                SecretResourcePoolType.CloudSecurityResourceService.toString(),
                                SecretResourcePoolType.Plugin.toString()
                        )), poolType));
                return;
            }

            SecretResourcePoolHeartbeatDetectionMsg msg = new SecretResourcePoolHeartbeatDetectionMsg();
            msg.setSecretResourcePoolUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(msg, SecretResourcePoolConstant.SERVICE_ID, uuid);
            bus.send(msg, new CloudBusCallBack(null) {
                @Override
                public void run(MessageReply reply) {
                    continueToRunThisTimer();
                }
            });
        }
    }

    private void reScanSecretResourcePool() {
        reScanSecretResourcePool(false);
    }

    private void reScanSecretResourcePool(boolean skipExisting) {
        if (!skipExisting) {
            new HashSet<>(trackers.values()).forEach(SecretResourcePoolTrackerImpl.Tracker::cancel);
        }

        List<String> SecretResourcePoolUuids = Q.New(SecretResourcePoolVO.class)
                .select(SecretResourcePoolVO_.uuid)
                .listValues();
        Set<String> toTrack = SecretResourcePoolUuids.stream().filter(SecretResourcePoolUuid -> {
            if (skipExisting) {
                return destMaker.isManagedByUs(SecretResourcePoolUuid) && !trackers.containsKey(SecretResourcePoolUuid);
            } else {
                return destMaker.isManagedByUs(SecretResourcePoolUuid);
            }
        }).collect(Collectors.toSet());

        trackSecretResourcePool(toTrack);
    }

    @Override
    @AsyncThread
    public void nodeJoin(ManagementNodeInventory inv) {
        reScanSecretResourcePool();
    }

    @Override
    public void nodeLeft(ManagementNodeInventory inv) {
        reScanSecretResourcePool();
    }

    @Override
    public void iAmDead(ManagementNodeInventory inv) {

    }

    @Override
    public void iJoin(ManagementNodeInventory inv) {

    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }
}

