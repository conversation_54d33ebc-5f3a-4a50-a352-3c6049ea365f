package org.zstack.crypto.securitymachine.secretresourcepool;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.Q;
import org.zstack.crypto.auth.CryptoAuthenticationGlobalConfig;
import org.zstack.crypto.securitymachine.SecurityMachineGlobalConfig;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APIDeleteSecretResourcePoolMsg;
import org.zstack.crypto.securitymachine.api.securitymachine.APISetSecurityMachineKeyMsg;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.message.APIMessage;
import org.zstack.header.securitymachine.*;

import static org.zstack.core.Platform.argerr;

/**
 * Created by LiangHanYu on 2021/11/21 15:05
 */
class SecretResourcePoolApiInterceptor implements ApiMessageInterceptor {
    @Autowired
    private CloudBus bus;

    private void setServiceId(APIMessage msg) {
        if (msg instanceof SecretResourcePoolMessage) {
            SecretResourcePoolMessage smsg = (SecretResourcePoolMessage) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, SecretResourcePoolConstant.SERVICE_ID, smsg.getSecretResourcePoolUuid());
        }
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIDeleteSecretResourcePoolMsg) {
            validate((APIDeleteSecretResourcePoolMsg) msg);
        } else if (msg instanceof APISetSecurityMachineKeyMsg) {
            validate((APISetSecurityMachineKeyMsg) msg);
        }

        setServiceId(msg);
        return msg;
    }

    private void validate(APISetSecurityMachineKeyMsg msg) {
        if (Q.New(SecurityMachineVO.class)
                .eq(SecurityMachineVO_.secretResourcePoolUuid, msg.getUuid())
                .eq(SecurityMachineVO_.state, SecurityMachineState.Enabled)
                .count() < 1) {
            throw new ApiMessageInterceptionException(argerr("there is no security machine that can be activated"));
        }

        if (!EnumUtils.isValidEnum(SecurityMachineKeyType.class, msg.getType())) {
            throw new ApiMessageInterceptionException(argerr("invalid token type %s, only supports %s.", msg.getType(), StringUtils.join(SecurityMachineKeyType.values(), ',')));
        }
    }

    private void validate(APIDeleteSecretResourcePoolMsg msg) {
        //if identity authentication or data protection is enabled,
        //you need to check whether the resource pool is in use when deleting.
        if (!CryptoAuthenticationGlobalConfig.ENABLE_CCS_CERTIFICATE_AUTH_LOGIN.value(Boolean.class)) {
            return;
        }

        //if the identity authentication and data protection function is enabled, the corresponding resource pool cannot be deleted
        String poolForAuth = SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.value(String.class);
        if (poolForAuth.equals(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.getDefaultValue())) {
            throw new ApiMessageInterceptionException(argerr("the identity authentication function is enabled but the corresponding resource pool is not set," +
                    " please re-enable the function and try again", msg.getSecretResourcePoolUuid()));
        }
        if (poolForAuth.equals(msg.getSecretResourcePoolUuid())) {
            throw new ApiMessageInterceptionException(argerr("cannot delete the resource pool %s when in use", msg.getSecretResourcePoolUuid()));
        }

        //TODO data protection
    }
}
