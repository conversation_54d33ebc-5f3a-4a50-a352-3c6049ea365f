package org.zstack.crypto.securitymachine.thirdparty.haitai;

import org.zstack.header.securitymachine.SecretResourcePoolVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * Created by LiangHanYu on 2022/7/27 16:17
 */
@StaticMetamodel(HaiTaiSecretResourcePoolVO.class)
public class HaiTaiSecretResourcePoolVO_ extends SecretResourcePoolVO_ {
    public static volatile SingularAttribute<HaiTaiSecretResourcePoolVO, String> managementIp;
    public static volatile SingularAttribute<HaiTaiSecretResourcePoolVO, Integer> port;
    public static volatile SingularAttribute<HaiTaiSecretResourcePoolVO, String> realm;
}
