package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.crypto.securitymachine.AddInfoSecSecurityMachineMessage;
import org.zstack.crypto.securitymachine.AddSecurityMachineMessage;
import org.zstack.crypto.securitymachine.SecurityMachine;
import org.zstack.crypto.securitymachine.SecurityMachineFactory;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.securitymachine.*;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import static org.zstack.core.Platform.operr;

/**
 * Created by LiangHanYu on 2021/11/16 16:10
 */
public class InfoSecSecurityMachineFactory implements SecurityMachineFactory {
    private static final CLogger logger = Utils.getLogger(InfoSecSecurityMachineFactory.class);
    @Autowired
    private DatabaseFacade dbf;

    @Override
    public String getSecurityMachineModel() {
        return InfoSecSecurityMachineConstant.SECURITY_MACHINE_TYPE;
    }

    @Override
    public SecurityMachineVO createSecurityMachine(SecurityMachineVO vo, AddSecurityMachineMessage msg) {
        if (!(msg instanceof AddInfoSecSecurityMachineMessage)) {
            throw new OperationFailureException(operr("security machine[uuid:%s] model is not %s", msg.getName(), vo.getModel()));
        }

        AddInfoSecSecurityMachineMessage addInfoSecSecurityMachineMessage = (AddInfoSecSecurityMachineMessage) msg;
        InfoSecSecurityMachineVO ivo = new InfoSecSecurityMachineVO(vo);
        ivo.setPort(addInfoSecSecurityMachineMessage.getPort());
        ivo.setPassword(addInfoSecSecurityMachineMessage.getPassword());
        ivo = dbf.persistAndRefresh(ivo);
        return ivo;
    }

    @Override
    public SecurityMachineInventory getSecurityMachineInventory(String uuid) {
        InfoSecSecurityMachineVO vo = dbf.findByUuid(uuid, InfoSecSecurityMachineVO.class);
        return vo == null ? null : InfoSecSecurityMachineInventory.valueOf(vo);
    }

    @Override
    public SecurityMachine getSecurityMachine(SecurityMachineVO vo) {
        InfoSecSecurityMachineVO infoSecSecurityMachineVO = dbf.findByUuid(vo.getUuid(), InfoSecSecurityMachineVO.class);
        return new InfoSecSecurityMachineBase(infoSecSecurityMachineVO);
    }
}
