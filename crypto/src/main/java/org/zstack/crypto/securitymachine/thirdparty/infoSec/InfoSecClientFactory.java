package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.zstack.crypto.securitymachine.*;
import org.zstack.crypto.securitymachine.mockclient.MockInfoSecClient;
import org.zstack.crypto.securitymachine.mockclient.MockSecurityMachineClientGlobalProperty;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorableValue;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import static org.zstack.core.Platform.operr;

/**
 * Created by <PERSON><PERSON><PERSON> on 21/11/18
 */
public class InfoSecClientFactory implements SecurityMachineClientFactory {
    private static final CLogger logger = Utils.getLogger(InfoSecClientFactory.class);
    protected static MockInfoSecClient mockInfoSecClient;

    @Override
    public SecurityMachineClient create() {
        if (!MockSecurityMachineClientGlobalProperty.ENABLE_MOCK) {
            return new InfoSecClient();
        }

        if (mockInfoSecClient == null) {
            synchronized (MockInfoSecClient.class) {
                if (mockInfoSecClient == null) {
                    mockInfoSecClient = new MockInfoSecClient();
                }
            }
        }
        return mockInfoSecClient;
    }

    @Override
    public String getType() {
        return InfoSecSecurityMachineConstant.SECURITY_MACHINE_TYPE;
    }

    @Override
    public ErrorCode testConnection(AddSecurityMachineMessage message) {
        AddInfoSecSecurityMachineMessage infoMsg = (AddInfoSecSecurityMachineMessage) message;

        try (SecurityMachineClient agent = create()) {
            ErrorableValue<Boolean> res = agent.connect(infoMsg.getManagementIp(), infoMsg.getPort(), infoMsg.getPassword());
            return res.isSuccess() ? null : res.error;
        } catch (Exception ignored) {
            return operr(ignored.getMessage(), "InfoSecClientFactory testConnection failed");
        }
    }
}
