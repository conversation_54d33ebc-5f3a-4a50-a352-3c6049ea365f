package org.zstack.crypto.securitymachine.thirdparty.sansec;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.message.GsonTransient;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.search.Inventory;
import org.zstack.header.search.Parent;
import org.zstack.header.securitymachine.SecurityMachineInventory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by LiangHanYu on 2021/11/3 16:06
 */
@PythonClassInventory
@Inventory(mappingVOClass = SanSecSecurityMachineVO.class, collectionValueOfMethod = "valueOf1",
        parent = {@Parent(inventoryClass = SecurityMachineInventory.class, type = SanSecConstant.SECURITY_MACHINE_TYPE)})
public class SanSecSecurityMachineInventory extends SecurityMachineInventory {
    private Integer port;
    @GsonTransient
    @APINoSee
    private String password;

    protected SanSecSecurityMachineInventory(SanSecSecurityMachineVO vo) {
        super(vo);
        this.setPort(vo.getPort());
        this.setPassword(vo.getPassword());
    }

    public SanSecSecurityMachineInventory() {
    }

    public static SanSecSecurityMachineInventory valueOf(SanSecSecurityMachineVO vo) {
        return new SanSecSecurityMachineInventory(vo);
    }

    public static List<SanSecSecurityMachineInventory> valueOf1(Collection<SanSecSecurityMachineVO> vos) {
        List<SanSecSecurityMachineInventory> invs = new ArrayList<SanSecSecurityMachineInventory>();
        for (SanSecSecurityMachineVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }
}
