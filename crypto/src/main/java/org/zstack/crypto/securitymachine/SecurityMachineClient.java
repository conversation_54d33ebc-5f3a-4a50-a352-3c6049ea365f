package org.zstack.crypto.securitymachine;

import org.apache.commons.codec.digest.DigestUtils;
import org.zstack.crypto.datatype.MSEnvelope;
import org.zstack.header.errorcode.ErrorableValue;
import org.zstack.license.CipherUtil;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.List;

import static org.zstack.core.Platform.err;
import static org.zstack.core.Platform.operr;
import static org.zstack.crypto.securitymachine.SecurityMachineErrors.OPERATION_NOT_SUPPORTED;

/**
 * Created by LiangHanYu on 2021/10/28 13:58
 */
public abstract class SecurityMachineClient implements AutoCloseable {
    static final CLogger logger = Utils.getLogger(SecurityMachineClient.class);

    final public static String sm4DecryptNullError = "sm4Decrypt plain is null";
    final public static String sm4EncryptNullError = "sm4Encrypt plain is null";
    final public static String digestNullError = "digest input is null";
    final public static String digestLocalNullError = "digestLocal input is null";
    final public static String certNullError = "cert is null";
    final public static String hmacNullError = "hmac plain is null";
    final public static String attachedVerifyNullError = "attachedVerify input is null";
    protected String mod = "CBC";
    protected String padding = "PKCS7Padding";

    /**
     * Return the cipher text signed by the security machine
     *
     * @param input  characters that need to be signed
     * @param certId certificate string
     */
    public ErrorableValue<String> attachedSignature(String input, String certId) {
        return ErrorableValue.of(null);
    }

    /**
     * Return to the original text after unsigned by the security machine
     *
     * @param input Signature cipher text
     */
    public abstract ErrorableValue<AttachVerifyPair> attachedVerify(String input);

    /**
     * Return SM3 encrypted cipher text
     *
     * @param input the original text
     */
    public ErrorableValue<String> digest(String input){
        return ErrorableValue.of(input);
    }

    /**
     * Return the cipher text after SM3 local encryption without the security machine service,
     *
     * @param input the original text
     */
    public ErrorableValue<String> digestLocal(String input) {
        try {
            return ErrorableValue.of(DigestUtils.sha1Hex(input));// support SM3、SHA-1、SHA-256 algType
        } catch (Exception e) {
            String errorText = "localDigest encrypt failed, detail: %s";
            logger.warn(String.format(errorText, e.getMessage()));
            return ErrorableValue.ofErrorCode(operr(errorText, e.getMessage()));
        }
    }

    /**
     * Parse the certificate
     *
     * @param cert Certificate byte[]
     * @return X509Certificate object
     */
    public ErrorableValue<X509Certificate> genericCertificate(byte[] cert) {
        if (cert == null) {
            logger.warn(certNullError);
            return ErrorableValue.ofErrorCode(operr(certNullError));
        }
        String head = "-----BEGIN CERTIFICATE-----";
        String tail = "-----END CERTIFICATE-----";
        String certString = new String(cert, StandardCharsets.UTF_8).replace("\r", "").replaceAll("\n", "");
        if (certString.contains(head)) {
            certString = certString.replaceFirst(head, "").replaceFirst(tail, "");
        }
        if (logger.isTraceEnabled()) {
            logger.trace(String.format("genericCertificate cert is: %s", certString));
        }

        try {
            CipherUtil cutil = new CipherUtil();
            return ErrorableValue.of(cutil.parseX509Certificate(certString));
        } catch (Exception e) {
            String errorText = "generate certificate failed, detail: %s";
            logger.warn(String.format(errorText, e.getMessage()));
            return ErrorableValue.ofErrorCode(operr(errorText, e.getMessage()));
        }
    }

    /**
     * SM4 encryption of the string
     *
     * @param plain the original text
     */
    public abstract ErrorableValue<String> sm4Encrypt(String plain);

    /**
     * SM4 decryption of the string
     *
     * @param plain the Cipher text
     */
    public abstract ErrorableValue<String> sm4Decrypt(String plain);

    /**
     * Encrypt the string with hmac
     *
     * @param plain the original text
     */
    public abstract ErrorableValue<String> hmac(String plain);

    /**
     * Encrypt large files with hmac
     *
     * @param stream file stream
     */
    public ErrorableValue<byte[]> largeFileHmac(InputStream stream) {
        return null;
    }

    /**
     * Connect remote security machine according to the resource pool uuid
     *
     * @param uuid the secret resource pool uuid
     */
    public abstract ErrorableValue<Boolean> connect(String uuid);

    /**
     * Connect remote security machine according to a security machine
     *
     * @param ip       IP address used to connect to the security machine
     * @param password password required to connect to the security machine
     * @param port     port used to connect to the security machine
     */
    public ErrorableValue<Boolean> connect(String ip, int port, String password){
        return ErrorableValue.of(true);
    }

    /**
     * Connect remote security machine according to a security machine
     *
     * @param params Parameters used to connect to the client. different manufacturers pass in different parameters, just use them according to the incoming order when using them
     */
    public ErrorableValue<Boolean> connect(List<String> params){
        return ErrorableValue.of(true);
    }

    /**
     * Get the type of the current security machine client
     *
     * @return the current type of the current cipher machine client. eg: InfoSec
     */
    public abstract String getType();

    /**
     * Check if the key exists on the server
     */
    public ErrorableValue<Boolean> isSecretKeyExist(String keyLabel){
        return ErrorableValue.of(true);
    }

    /**
     * Generate a token with a custom name and custom algorithm type
     *
     * @param tokenName the name of the generated token
     * @param algType   the algType of the generated token
     */
    public ErrorableValue<String> generateToken(String tokenName, String algType) {
        return null;
    }

    /**
     * Generate a Sm4 token with a default name
     */
    public ErrorableValue<String> generateSm4Token(String tokenName) {
        return null;
    }

    /**
     * Generate a dataProtect token with a default name
     */
    public ErrorableValue<String> generateDataProtectToken(String tokenName) {
        return null;
    }

    /**
     * Generate a hmac token with a default name
     */
    public ErrorableValue<String> generateHmacToken(String tokenName) {
        return null;
    }

    /**
     * execute different token generation logic according to the incoming type
     */
    public ErrorableValue<String> generateToken(String keyType) {
        return null;
    }

    public ErrorableValue<String> backupToken() {
        return null;
    }

    public void setSm4EncryptionMode(String encryptionMode) {
        mod = encryptionMode;
    }

    public void setSm4PaddingMode(String paddingMode) {
        padding = paddingMode;
    }

    public void close() {
    }

    public ErrorableValue<MSEnvelope> decryptMSEnvelope(String cipherText, String encryptSubjectDN) {
        return operationNotSupportedValue("failed to decrypt MS envelope: type=%s", getType());
    }

    static <T> ErrorableValue<T> operationNotSupportedValue(String text, Object... args) {
        return ErrorableValue.ofErrorCode(err(OPERATION_NOT_SUPPORTED, text, args));
    }
}
