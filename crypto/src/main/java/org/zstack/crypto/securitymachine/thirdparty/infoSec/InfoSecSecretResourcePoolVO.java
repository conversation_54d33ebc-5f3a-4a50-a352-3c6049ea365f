package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.zstack.header.securitymachine.SecretResourcePoolVO;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.PrimaryKeyJoinColumn;
import javax.persistence.Table;

/**
 * Created by LiangHanYu on 2021/10/28 11:25
 */
@Entity
@Table
@PrimaryKeyJoinColumn(name = "uuid", referencedColumnName = "uuid")
public class InfoSecSecretResourcePoolVO extends SecretResourcePoolVO {
    @Column
    private Integer connectionMode;
    /**
     * @desc sdk client test whether the security machine can provide the key
     * name of the encryption and decryption service
     */
    @Column
    private String activatedToken;
    /**
     * @desc the key name used by the sdk client to perform data protection
     */
    @Column
    private String protectToken;
    /**
     * @desc the key name used by the sdk client to perform hmac
     */
    @Column
    private String hmacToken;
    @Column
    private String encryptPublicKey;
    @Column
    private String encryptSubjectDN;

    public InfoSecSecretResourcePoolVO() {
    }

    public InfoSecSecretResourcePoolVO(SecretResourcePoolVO vo) {
        super(vo);
    }

    public Integer getConnectionMode() {
        return connectionMode;
    }

    public void setConnectionMode(Integer connectionMode) {
        this.connectionMode = connectionMode;
    }

    public String getActivatedToken() {
        return activatedToken;
    }

    public void setActivatedToken(String activatedToken) {
        this.activatedToken = activatedToken;
    }

    public String getProtectToken() {
        return protectToken;
    }

    public void setProtectToken(String protectToken) {
        this.protectToken = protectToken;
    }

    public String getHmacToken() {
        return hmacToken;
    }

    public void setHmacToken(String hmacToken) {
        this.hmacToken = hmacToken;
    }

    public String getEncryptPublicKey() {
        return encryptPublicKey;
    }

    public void setEncryptPublicKey(String encryptPublicKey) {
        this.encryptPublicKey = encryptPublicKey;
    }

    public String getEncryptSubjectDN() {
        return encryptSubjectDN;
    }

    public void setEncryptSubjectDN(String encryptSubjectDN) {
        this.encryptSubjectDN = encryptSubjectDN;
    }
}