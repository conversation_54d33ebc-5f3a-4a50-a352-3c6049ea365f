package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.crypto.securitymachine.secretresourcepool.CreateInfoSecSecretResourcePoolMessage;
import org.zstack.crypto.securitymachine.secretresourcepool.CreateSecretResourcePoolMessage;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePool;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePoolFactory;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.securitymachine.*;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import static org.zstack.core.Platform.operr;

/**
 * Created by LiangHanYu on 2021/11/5 13:31
 */
public class InfoSecSecretResourcePoolFactory implements SecretResourcePoolFactory {
    private static final CLogger logger = Utils.getLogger(InfoSecSecretResourcePoolFactory.class);
    @Autowired
    private DatabaseFacade dbf;

    @Override
    public String getSecretResourcePoolModel() {
        return InfoSecSecurityMachineConstant.SECURITY_MACHINE_TYPE;
    }

    @Override
    public SecretResourcePoolVO createSecretResourcePool(SecretResourcePoolVO vo, CreateSecretResourcePoolMessage msg) {
        if (!(msg instanceof CreateInfoSecSecretResourcePoolMessage)) {
            throw new OperationFailureException(operr("secretResourcePool[uuid:%s] model is not %s", msg.getResourceUuid(), vo.getModel()));
        }

        CreateInfoSecSecretResourcePoolMessage createInfoSecSecretResourcePoolMessage = (CreateInfoSecSecretResourcePoolMessage) msg;
        InfoSecSecretResourcePoolVO ivo = new InfoSecSecretResourcePoolVO(vo);
        ivo.setConnectionMode(createInfoSecSecretResourcePoolMessage.getConnectionMode());
        ivo = dbf.persistAndRefresh(ivo);
        return ivo;
    }

    @Override
    public SecretResourcePoolInventory getSecretResourcePoolInventory(String uuid) {
        InfoSecSecretResourcePoolVO vo = dbf.findByUuid(uuid, InfoSecSecretResourcePoolVO.class);
        return vo == null ? null : InfoSecSecretResourcePoolInventory.valueOf(vo);
    }

    @Override
    public SecretResourcePool getSecretResourcePool(SecretResourcePoolVO vo) {
        InfoSecSecretResourcePoolVO infoSecSecretResourcePoolVO = dbf.findByUuid(vo.getUuid(), InfoSecSecretResourcePoolVO.class);
        return new InfoSecSecretResourcePoolBase(infoSecSecretResourcePoolVO);
    }
}