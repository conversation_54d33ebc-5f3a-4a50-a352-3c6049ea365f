package org.zstack.crypto.securitymachine;

public enum SecurityMachineErrors {
    NO_AVAILABLE_MACHINES(3001),
    CLIENT_CONNECT_ERROR(3002),
    CLIENT_DIGEST_ERROR(3003),
    CLIENT_SM4_ERROR(3004),
    CLIENT_HMAC_ERROR(3005),
    CRYPTO_DISABLE_ERROR(3006),
    OPERATION_NOT_SUPPORTED(3007),
    ;

    public final String code;

    SecurityMachineErrors(int id) {
        code = String.format("SM.%s", id);
    }

    @Override
    public String toString() {
        return code;
    }
}
