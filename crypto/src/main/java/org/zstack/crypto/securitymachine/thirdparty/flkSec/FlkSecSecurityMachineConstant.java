package org.zstack.crypto.securitymachine.thirdparty.flkSec;

import org.zstack.header.configuration.PythonClass;

/**
 * Created by <PERSON>H<PERSON><PERSON><PERSON> on 2023/2/3 11:22
 */
public interface FlkSecSecurityMachineConstant {
    @PythonClass
    String SECURITY_MACHINE_TYPE = "FlkSec";

    String FLKSEC_HEARTBEAT_PATH = "/service/health";
    String FLKSEC_DIGEST_PATH = "/api/crypto/hash";
    String FLKSEC_GENERATE_SM4_KEY_PATH = "/api/crypto/getSymmetricKey";
    String FLKSEC_SYMM_ENCRYPT_PATH = "/api/crypto/sysEncrypt";
    String FLKSEC_SYMM_DECRYPT_PATH = "/api/crypto/sysDecrypt";
    String FLKSEC_HMAC_PATH = "/api/crypto/hmac/sm3hmac";
    String FLKSEC_GENERATE_HMAC_KEY_PATH = "/api/crypto/genRandom";
    String FLKSEC_ATTACHED_VERIFY_PATH = "/api/crypto/analysis/p7signature";
    String FLKSEC_ANALYSIS_MESSAGE_PATH = "/svs/api/analysis/message";

    final static String internalEncryptText = "internalEncryptText";

    public static class FlkSecResponse {
        private String code;
        private String msg;
        private String data;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }
    }

    public static class FlkSecAttachedVerifyServerCmd {
        private String p7Signature;
        private int type; //Optional: 1、cert base64，2、original text.

        public FlkSecAttachedVerifyServerCmd() {
        }

        public FlkSecAttachedVerifyServerCmd(String p7Signature) {
            this.p7Signature = p7Signature;
        }

        public String getP7Signature() {
            return p7Signature;
        }

        public void setP7Signature(String p7Signature) {
            this.p7Signature = p7Signature;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    public static class FlkSecCmd {
        private boolean plainIsEncode;
        private String data;


        public FlkSecCmd() {
        }

        public FlkSecCmd(String data) {
            this.data = data;
            plainIsEncode = false;
        }

        public boolean isPlainIsEncode() {
            return plainIsEncode;
        }

        public void setPlainIsEncode(boolean plainIsEncode) {
            this.plainIsEncode = plainIsEncode;
        }

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }
    }

    public static class FlkSecHmacCmd extends FlkSecCmd {
        private String key;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }

    public static class FlkSecEncryptCmd extends FlkSecHmacCmd {
        private String algorithmType;
        private String encMode;
        private String iv;
        private String padMode;

        public String getAlgorithmType() {
            return algorithmType;
        }

        public void setAlgorithmType(String algorithmType) {
            this.algorithmType = algorithmType;
        }

        public String getEncMode() {
            return encMode;
        }

        public void setEncMode(String encMode) {
            this.encMode = encMode;
        }

        public String getIv() {
            return iv;
        }

        public void setIv(String iv) {
            this.iv = iv;
        }

        public String getPadMode() {
            return padMode;
        }

        public void setPadMode(String padMode) {
            this.padMode = padMode;
        }
    }

    public static class FlkSecDecryptCmd {
        private String algorithmType;
        private String encryptData;
        private String encMode;
        private String iv;
        private String padMode;
        private String key;
        private boolean plainIsEncode;

        public String getAlgorithmType() {
            return algorithmType;
        }

        public void setAlgorithmType(String algorithmType) {
            this.algorithmType = algorithmType;
        }

        public String getEncryptData() {
            return encryptData;
        }

        public void setEncryptData(String encryptData) {
            this.encryptData = encryptData;
        }

        public String getEncMode() {
            return encMode;
        }

        public void setEncMode(String encMode) {
            this.encMode = encMode;
        }

        public String getIv() {
            return iv;
        }

        public void setIv(String iv) {
            this.iv = iv;
        }

        public String getPadMode() {
            return padMode;
        }

        public void setPadMode(String padMode) {
            this.padMode = padMode;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public boolean isPlainIsEncode() {
            return plainIsEncode;
        }

        public void setPlainIsEncode(boolean plainIsEncode) {
            this.plainIsEncode = plainIsEncode;
        }
    }

    public static class FlkSecGetSm4KeyCmd {
        private String algorithmType;

        public String getAlgorithmType() {
            return algorithmType;
        }

        public void setAlgorithmType(String algorithmType) {
            this.algorithmType = algorithmType;
        }
    }

    public static class FlkSecGetHmacKeyCmd {
        private int length;

        public FlkSecGetHmacKeyCmd() {
            length = 16;
        }

        public int getLength() {
            return length;
        }

        public void setLength(int length) {
            this.length = length;
        }
    }

    class FlkSecAnalysisMessageCmd {
        private String version = "";
        private String reqType = "";
        private int reqTime = 0;
        private FlkSecAnalysisMessageRequestCmd request;

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getReqType() {
            return reqType;
        }

        public void setReqType(String reqType) {
            this.reqType = reqType;
        }

        public int getReqTime() {
            return reqTime;
        }

        public void setReqTime(int reqTime) {
            this.reqTime = reqTime;
        }

        public FlkSecAnalysisMessageRequestCmd getRequest() {
            return request;
        }

        public void setRequest(FlkSecAnalysisMessageRequestCmd request) {
            this.request = request;
        }
    }

    class FlkSecAnalysisMessageRequestCmd {
        private String signedMessage;
        private int type; //Optional: 1.original text, 2.cert base64

        public FlkSecAnalysisMessageRequestCmd() {
        }

        public FlkSecAnalysisMessageRequestCmd(String signedMessage, int type) {
            this.signedMessage = signedMessage;
            this.type = type;
        }

        public String getSignedMessage() {
            return signedMessage;
        }

        public void setSignedMessage(String signedMessage) {
            this.signedMessage = signedMessage;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }

    class FlkSecAnalysisMessageResponse {
        class FlkSecAnalysisMessageRespond {
            private int respValue;
            private String data;
            private String message;

            public int getRespValue() {
                return respValue;
            }

            public void setRespValue(int respValue) {
                this.respValue = respValue;
            }

            public String getData() {
                return data;
            }

            public void setData(String data) {
                this.data = data;
            }

            public String getMessage() {
                return message;
            }

            public void setMessage(String message) {
                this.message = message;
            }
        }

        private String version;
        private String respTime;
        private String respType;
        private FlkSecAnalysisMessageRespond respond;

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getRespTime() {
            return respTime;
        }

        public void setRespTime(String respTime) {
            this.respTime = respTime;
        }

        public String getRespType() {
            return respType;
        }

        public void setRespType(String respType) {
            this.respType = respType;
        }

        public FlkSecAnalysisMessageRespond getRespond() {
            return respond;
        }

        public void setRespond(FlkSecAnalysisMessageRespond respond) {
            this.respond = respond;
        }
    }
}
