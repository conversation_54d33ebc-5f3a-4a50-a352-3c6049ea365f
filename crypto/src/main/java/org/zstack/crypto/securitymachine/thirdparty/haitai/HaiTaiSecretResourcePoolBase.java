package org.zstack.crypto.securitymachine.thirdparty.haitai;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.crypto.securitymachine.SecurityMachineErrors;
import org.zstack.crypto.securitymachine.api.securitymachine.APISetSecurityMachineKeyMsg;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePool;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePoolBase;
import org.zstack.crypto.securitymachine.thirdparty.haitai.HaiTaiSecretResourcePoolConstant.HaiTaiCheckStatusResponse;
import org.zstack.crypto.securitymachine.thirdparty.haitai.HaiTaiSecretResourcePoolConstant.HaiTaiServiceCheckData;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.securitymachine.SecretResourcePoolInventory;
import org.zstack.header.securitymachine.SecretResourcePoolVO;
import org.zstack.header.securitymachine.SecurityMachineInventory;
import org.zstack.utils.gson.JSONObjectUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.zstack.core.Platform.err;
import static org.zstack.core.Platform.operr;
import static org.zstack.crypto.securitymachine.thirdparty.haitai.HaiTaiClient.successCode;

/**
 * Created by LiangHanYu on 2022/7/27 16:18
 */
public class HaiTaiSecretResourcePoolBase extends SecretResourcePoolBase implements SecretResourcePool {
    @Autowired
    private RESTFacade restf;

    protected HaiTaiSecretResourcePoolBase(SecretResourcePoolVO self) {
        super(self);
    }

    @Override
    protected void setTokenForSecretResourcePool(APISetSecurityMachineKeyMsg msg, ReturnValueCompletion<List<SecurityMachineInventory>> completion) {
        completion.success(new ArrayList<>());
    }

    private HaiTaiSecretResourcePoolVO getSelf() {
        return (HaiTaiSecretResourcePoolVO) self;
    }

    @Override
    protected SecretResourcePoolInventory getSelfInventory() {
        return HaiTaiSecretResourcePoolInventory.valueOf(getSelf());
    }

    @Override
    protected void handleApiMessage(APIMessage msg) {
        super.handleApiMessage(msg);
    }

    @Override
    protected void handleLocalMessage(Message msg) {
        super.handleLocalMessage(msg);
    }

    @Override
    protected void heartbeatDetectHook(Completion completion) {
        Map<String, String> headers = new HashMap<>();
        headers.put("version", HaiTaiSecretResourcePoolConstant.version);
        headers.put("Authorization", String.format("Digest algo=sm3 realm=%s", getSelf().getRealm()));
        String url = String.format("http://%s:%s%s", getSelf().getManagementIp(), getSelf().getPort(), HaiTaiClient.heartbeatUrl);
        HaiTaiCheckStatusResponse rsp = restf.syncJsonPost(url, null, headers, HaiTaiCheckStatusResponse.class);
        if (!successCode.equals(rsp.getCode())) {
            completion.fail(operr(rsp.getMessage()));
            return;
        }
        ArrayList<HaiTaiServiceCheckData> checkServices = JSONObjectUtil.toCollection(rsp.getData(), ArrayList.class, HaiTaiServiceCheckData.class);
        AtomicBoolean isServerError = new AtomicBoolean(false);
        String errorServerName = null;
        checkServices.forEach(server -> {
            if (!server.getCheckStatus().equals("0")) {
                isServerError.set(true);
                return;
            }
        });
        if (isServerError.get()) {
            completion.fail(err(SecurityMachineErrors.CLIENT_CONNECT_ERROR, "some services are unavailable, heartbeat detection failed"));
            return;
        }
        completion.success();
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }
}
