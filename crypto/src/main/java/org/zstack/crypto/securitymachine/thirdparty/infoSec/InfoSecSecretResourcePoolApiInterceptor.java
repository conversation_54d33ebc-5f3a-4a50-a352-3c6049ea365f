package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.zstack.crypto.securitymachine.api.secretresourcepool.APICreateInfoSecSecretResourcePoolMsg;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.message.APIMessage;

import static org.zstack.core.Platform.argerr;

/**
 * Created by LiangHanYu on 2021/11/17 17:58
 */
public class InfoSecSecretResourcePoolApiInterceptor implements ApiMessageInterceptor {
    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreateInfoSecSecretResourcePoolMsg) {
            validate((APICreateInfoSecSecretResourcePoolMsg) msg);
        }

        return msg;
    }

    private void validate(APICreateInfoSecSecretResourcePoolMsg msg) {
        if (!msg.getModel().equals(InfoSecSecretResourcePoolConstant.SECRET_RESOURCE_POOL_TYPE)) {
            throw new ApiMessageInterceptionException(argerr("currently does not support the creation of %s resource pools", msg.getModel()));
        }
    }
}
