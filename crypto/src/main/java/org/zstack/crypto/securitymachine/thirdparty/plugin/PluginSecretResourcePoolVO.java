package org.zstack.crypto.securitymachine.thirdparty.plugin;

import org.zstack.header.core.external.plugin.PluginDriverVO;
import org.zstack.header.securitymachine.SecretResourcePoolVO;
import org.zstack.header.vo.ForeignKey;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.PrimaryKeyJoinColumn;
import javax.persistence.Table;

/**
 * Created by LiangHanYu on 2022/7/27 16:17
 */
@Entity
@Table
@PrimaryKeyJoinColumn(name = "uuid", referencedColumnName = "uuid")
public class PluginSecretResourcePoolVO extends SecretResourcePoolVO {
    @Column
    private String properties;

    @Column
    @ForeignKey(parentEntityClass = PluginDriverVO.class,
            parentKey = "uuid",
            onDeleteAction = ForeignKey.ReferenceOption.SET_NULL)
    private String pluginDriverUuid;

    public PluginSecretResourcePoolVO() {
    }

    public PluginSecretResourcePoolVO(SecretResourcePoolVO vo) {
        super(vo);
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getPluginDriverUuid() {
        return pluginDriverUuid;
    }

    public void setPluginDriverUuid(String pluginDriverUuid) {
        this.pluginDriverUuid = pluginDriverUuid;
    }
}