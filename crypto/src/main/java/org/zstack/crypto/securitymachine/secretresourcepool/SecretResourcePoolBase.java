package org.zstack.crypto.securitymachine.secretresourcepool;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.crypto.securitymachine.ChangeSecurityMachineStatusMsg;
import org.zstack.crypto.securitymachine.SecurityMachineHelper;
import org.zstack.crypto.securitymachine.SecurityMachineManager;
import org.zstack.crypto.securitymachine.api.secretresourcepool.*;
import org.zstack.crypto.securitymachine.api.securitymachine.APISetSecurityMachineKeyEvent;
import org.zstack.crypto.securitymachine.api.securitymachine.APISetSecurityMachineKeyMsg;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.securitymachine.*;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.zstack.core.Platform.err;

/**
 * Created by LiangHanYu on 2021/11/4 17:48
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public abstract class SecretResourcePoolBase extends AbstractSecretResourcePool {
    protected static final CLogger logger = Utils.getLogger(SecretResourcePoolBase.class);
    protected SecretResourcePoolVO self;
    protected final String id;

    @Autowired
    protected CloudBus bus;
    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    protected EventFacade evtf;
    @Autowired
    protected SecurityMachineManager securityMachineMgr;
    @Autowired
    protected SecurityMachineHelper securityMachineHelper;
    @Autowired
    private SecretResourcePoolTrackerImpl tracker;


    protected SecretResourcePoolBase(SecretResourcePoolVO self) {
        this.self = self;
        id = SecretResourcePool.buildId(self.getUuid());
    }

    protected void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIChangeSecretResourcePoolStateMsg) {
            handle((APIChangeSecretResourcePoolStateMsg) msg);
        } else if (msg instanceof APISetSecurityMachineKeyMsg) {
            handle((APISetSecurityMachineKeyMsg) msg);
        } else if (msg instanceof APIUpdateSecretResourcePoolMsg) {
            handle((APIUpdateSecretResourcePoolMsg) msg);
        } else if (msg instanceof APIDeleteSecretResourcePoolMsg) {
            handle((APIDeleteSecretResourcePoolMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APISetSecurityMachineKeyMsg msg) {
        APISetSecurityMachineKeyEvent event = new APISetSecurityMachineKeyEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("set-token-for-secret-resource-pool-%s", msg.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                setTokenForSecretResourcePool(msg, new ReturnValueCompletion<List<SecurityMachineInventory>>(msg) {
                            @Override
                            public void success(List<SecurityMachineInventory> returnValue) {
                                event.setInventories(returnValue);
                                bus.publish(event);
                                chain.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                event.setError(errorCode);
                                bus.publish(event);
                                chain.next();
                            }
                        });
            }

            @Override
            public String getName() {
                return String.format("set-token-for-secret-resource-pool-%s", msg.getUuid());
            }
        });
    }

    protected void setTokenForSecretResourcePool(APISetSecurityMachineKeyMsg msg, ReturnValueCompletion<List<SecurityMachineInventory>> completion) {
        final FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("activate-secret-resource-pool-%s", msg.getUuid()));

        chain.then(new ShareFlow() {
            List<SecurityMachineVO> securityMachineVOList = Q.New(SecurityMachineVO.class)
                    .eq(SecurityMachineVO_.secretResourcePoolUuid, msg.getSecretResourcePoolUuid())
                    .list();

            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = "change-the-status-of-all-security-machines";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        new While<>(securityMachineVOList).each((vo, whileComplection) -> {
                            ChangeSecurityMachineStatusMsg cmsg = new ChangeSecurityMachineStatusMsg();
                            cmsg.setUuid(vo.getUuid());
                            cmsg.setStatusEvent(SecurityMachineStatusEvent.sync.toString());
                            bus.makeTargetServiceIdByResourceUuid(cmsg, SecurityMachineConstant.SERVICE_ID, cmsg.getSecurityMachineUuid());
                            bus.send(cmsg, new CloudBusCallBack(whileComplection) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess() && reply.getError() != null) {
                                        logger.debug(String.format("failed to security machine status from %s to %s, because %s", vo.getStatus(), SecurityMachineStatusEvent.sync.toString(), reply.getError()));
                                        whileComplection.addError(reply.getError());
                                    }

                                    whileComplection.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (!errorCodeList.getCauses().isEmpty()) {
                                    trigger.fail(errorCodeList.getCauses().get(0));
                                    return;
                                }
                                trigger.next();
                            }
                        });
                    }

                    @Override
                    public boolean skip(Map data) {
                        return msg.isDryRun();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("change-resource-pool-%s-state", msg.getUuid());

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        ChangeSecretResourcePoolStateMsg cmsg = new ChangeSecretResourcePoolStateMsg();
                        cmsg.setUuid(msg.getUuid());
                        cmsg.setStateEvent(SecretResourcePoolStateEvent.activate.toString());
                        bus.makeTargetServiceIdByResourceUuid(cmsg, SecretResourcePoolConstant.SERVICE_ID, cmsg.getSecretResourcePoolUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                trigger.next();
                            }
                        });
                    }

                    @Override
                    public boolean skip(Map data) {
                        return msg.isDryRun();
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success(new ArrayList<>());
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private void handle(APIDeleteSecretResourcePoolMsg msg) {
        deleteSecretResourcePoolByApiMessage(msg);
    }

    private void deleteSecretResourcePoolByApiMessage(APIDeleteSecretResourcePoolMsg msg) {
        final APIDeleteSecretResourcePoolEvent evt = new APIDeleteSecretResourcePoolEvent(msg.getId());

        final String issuer = SecretResourcePoolVO.class.getSimpleName();
        final List<SecretResourcePoolInventory> ctx = Collections.singletonList(SecretResourcePoolInventory.valueOf(self));

        if (self.getState() == SecretResourcePoolState.Activated) {
            changeState(SecretResourcePoolStateEvent.unactivate);
        }
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-secret-resource-pool-%s", msg.getUuid()));
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.done(new FlowDoneHandler(msg) {
            private void complete() {
                bus.publish(evt);
            }

            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new Completion(msg) {
                    @Override
                    public void success() {
                        complete();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        complete();
                    }
                });
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                evt.setError(err(SysErrors.DELETE_RESOURCE_ERROR, errCode, errCode.getDetails()));
                bus.publish(evt);
            }
        }).start();
    }

    private void handle(APIChangeSecretResourcePoolStateMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                APIChangeSecretResourcePoolStateEvent evt = new APIChangeSecretResourcePoolStateEvent(msg.getId());

                SecretResourcePoolStateEvent stateEvent = SecretResourcePoolStateEvent.valueOf(msg.getStateEvent());
                SecretResourcePoolInventory inv = changeState(stateEvent);
                evt.setInventory(inv);
                bus.publish(evt);
                chain.next();
            }

            @Override
            public String getName() {
                return String.format("change-secret-resource-pool-%s-state", msg.getUuid());
            }
        });
    }

    private SecretResourcePoolInventory changeState(SecretResourcePoolStateEvent event) {
        SecretResourcePoolState currentState = self.getState();
        SecretResourcePoolState next = AbstractSecretResourcePool.getNextState(self.getState(), event);

        self.setState(next);
        self = dbf.updateAndRefresh(self);
        SecretResourcePoolInventory inv = SecretResourcePoolInventory.valueOf(self);
        logger.debug(String.format("secret resource pool %s[uuid: %s] changed state from %s to %s", self.getName(), self.getUuid(), currentState, self.getState()));
        return inv;
    }


    private void handle(APIUpdateSecretResourcePoolMsg msg) {
        SecretResourcePoolVO vo = updateSecretResourcePool(msg);
        if (vo != null) {
            self = dbf.updateAndRefresh(vo);
        }
        APIUpdateSecretResourcePoolEvent evt = new APIUpdateSecretResourcePoolEvent(msg.getId());
        evt.setInventory(getSelfInventory());
        bus.publish(evt);
    }

    protected SecretResourcePoolVO updateSecretResourcePool(APIUpdateSecretResourcePoolMsg msg) {
        boolean update = false;
        if (msg.getName() != null) {
            self.setName(msg.getName());
            update = true;
        }
        if (msg.getDescription() != null) {
            self.setDescription(msg.getDescription());
            update = true;
        }
        if (msg.getModel() != null) {
            self.setModel(msg.getModel());
            update = true;
        }
        if (msg.getHeartbeatInterval() != null) {
            self.setHeartbeatInterval(msg.getHeartbeatInterval());
            update = true;
        }

        return update ? self : null;
    }

    protected abstract SecretResourcePoolInventory getSelfInventory();

    protected void handleLocalMessage(Message msg) {
        if (msg instanceof SecretResourcePoolDeletionMsg) {
            handle((SecretResourcePoolDeletionMsg) msg);
        } else if (msg instanceof ChangeSecretResourcePoolStateMsg) {
            handle((ChangeSecretResourcePoolStateMsg) msg);
        } else if (msg instanceof SecretResourcePoolHeartbeatDetectionMsg) {
            handle((SecretResourcePoolHeartbeatDetectionMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(SecretResourcePoolHeartbeatDetectionMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                doHeartbeatDetection(msg, new ReturnValueCompletion<SecretResourcePoolHeartbeatDetectionReply>(msg, chain) {
                    @Override
                    public void success(SecretResourcePoolHeartbeatDetectionReply reply) {
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        SecretResourcePoolHeartbeatDetectionReply reply = new SecretResourcePoolHeartbeatDetectionReply();
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("do-heartbeat-detect-secret-resource-pool-%s", msg.getSecretResourcePoolUuid());
            }
        });
    }

    private void doHeartbeatDetection(final SecretResourcePoolHeartbeatDetectionMsg msg, ReturnValueCompletion<SecretResourcePoolHeartbeatDetectionReply> completion) {
        final SecretResourcePoolHeartbeatDetectionReply reply = new SecretResourcePoolHeartbeatDetectionReply();
        /* retry 3 times */
        List<Integer> steps = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            steps.add(i);
        }

        new While<>(steps).each((currentStep, compl) -> heartbeatDetectHook(new Completion(compl) {
            @Override
            public void success() {
                compl.allDone();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.warn(String.format(" heartbeat detect secret resource pool failed (%d/%d): %s", currentStep, steps.size(), errorCode.getDetails()));
                compl.addError(errorCode);
                compl.done();
            }
        })).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (errorCodeList.getCauses().size() != steps.size()) {
                    changeStatus(SecretResourcePoolStatus.Connected, null);
                    reply.setEnabled(true);
                    reply.setCurrentSecretResourcePoolStatus(self.getStatus().toString());
                    completion.success(reply);
                    return;
                }


                changeStatus(SecretResourcePoolStatus.Disconnected, errorCodeList.getCauses().get(0).getDetails());
                reply.setEnabled(false);
                reply.setCurrentSecretResourcePoolStatus(self.getStatus().toString());
                completion.success(reply);
            }
        });
    }

    protected abstract void heartbeatDetectHook(Completion completion);

    protected boolean changeStatus(SecretResourcePoolStatus status, String reason) {
        self = dbf.reload(self);
        if (status == self.getStatus()) {
            return false;
        }

        SecretResourcePoolStatus oldStatus = self.getStatus();
        self.setStatus(status);
        self = dbf.updateAndRefresh(self);

        if (!SecretResourcePoolStatus.Connected.toString().equals(status.toString())) {
            SecretResourcePoolCanonicalEvents.SecretResourcePoolStatusChangedData data = new SecretResourcePoolCanonicalEvents.SecretResourcePoolStatusChangedData();
            data.setSecretResourcePoolUuid(self.getUuid());
            data.setOldStatus(oldStatus.toString());
            data.setNewStatus(status.toString());
            evtf.fire(SecretResourcePoolCanonicalEvents.SECRET_RESOURCE_POOL_STATUS_CHANGE_PATH, data);
        }

        logger.debug(String.format("the secret resource pool[uuid:%s, name:%s] changed status from %s to %s",
                self.getUuid(), self.getName(), oldStatus, status));

        return true;
    }

    private void handle(ChangeSecretResourcePoolStateMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                ChangeSecretResourcePoolStateReply reply = new ChangeSecretResourcePoolStateReply();
                SecretResourcePoolInventory inv = changeState(SecretResourcePoolStateEvent.valueOf(msg.getStateEvent()));
                reply.setInventory(inv);
                bus.reply(msg, reply);
                chain.next();
            }

            @Override
            public String getName() {
                return String.format("change-secret-resource-pool-%s-state", msg.getUuid());
            }
        });
    }

    private void handle(SecretResourcePoolDeletionMsg msg) {
        deleteHook();
        tracker.untrackSecretResourcePool(self.getUuid());
        SecretResourcePoolDeletionReply reply = new SecretResourcePoolDeletionReply();
        bus.reply(msg, reply);
    }

    @Override
    protected void deleteHook() {
    }

    @Override
    public String getId() {
        return id;
    }
}
