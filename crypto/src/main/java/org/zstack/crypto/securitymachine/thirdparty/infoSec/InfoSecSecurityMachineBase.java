package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.apache.commons.lang.StringUtils;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.crypto.securitymachine.*;
import org.zstack.crypto.securitymachine.api.securitymachine.APIUpdateInfoSecSecurityMachineMsg;
import org.zstack.crypto.securitymachine.api.securitymachine.APIUpdateSecurityMachineMsg;
import org.zstack.header.core.Completion;
import org.zstack.header.errorcode.ErrorableValue;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.securitymachine.SecurityMachineInventory;
import org.zstack.header.securitymachine.SecurityMachineStatusEvent;
import org.zstack.header.securitymachine.SecurityMachineVO;

import static org.zstack.core.Platform.operr;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/17 11:34
 */
public class InfoSecSecurityMachineBase extends SecurityMachineBase implements SecurityMachine {

    InfoSecSecurityMachineBase(SecurityMachineVO self) {
        super(self);
    }

    @Override
    protected SecurityMachineVO updateSecurityMachine(APIUpdateSecurityMachineMsg msg) {
        if (!(msg instanceof APIUpdateInfoSecSecurityMachineMsg)) {
            return super.updateSecurityMachine(msg);
        }

        InfoSecSecurityMachineVO vo = (InfoSecSecurityMachineVO) super.updateSecurityMachine(msg);
        vo = vo == null ? getSelf() : vo;

        APIUpdateInfoSecSecurityMachineMsg umsg = (APIUpdateInfoSecSecurityMachineMsg) msg;
        if (umsg.getPassword() != null) {
            vo.setPassword(umsg.getPassword());
        }
        if (umsg.getPort() != null) {
            vo.setPort(umsg.getPort());
        }
        return vo;
    }

    private InfoSecSecurityMachineVO getSelf() {
        return (InfoSecSecurityMachineVO) self;
    }

    private void setAllTokenForSecretResourcePool(String activatedToken, String protectToken, String hmacToken) {
        SQL.New(InfoSecSecretResourcePoolVO.class).eq(InfoSecSecretResourcePoolVO_.uuid, self.getSecretResourcePoolUuid())
                .set(InfoSecSecretResourcePoolVO_.activatedToken, activatedToken)
                .set(InfoSecSecretResourcePoolVO_.protectToken, protectToken)
                .set(InfoSecSecretResourcePoolVO_.hmacToken, hmacToken)
                .update();
    }

    @Override
    protected void deleteHook(Completion completion) {
        if (SecurityMachineHelper.existOtherSecurityMachine(self.getSecretResourcePoolUuid(), self.getUuid())) {
            completion.success();
            return;
        }

        setAllTokenForSecretResourcePool(null, null, null);

        resetSecretResourcePoolState(completion);
    }

    @Override
    protected SecurityMachineInventory getSelfInventory() {
        return InfoSecSecurityMachineInventory.valueOf(getSelf());
    }

    @Override
    protected void handleApiMessage(APIMessage msg) {
        super.handleApiMessage(msg);
    }

    @Override
    protected void handleLocalMessage(Message msg) {
        super.handleLocalMessage(msg);
    }

    @Override
    protected ErrorableValue<Boolean> isConnected(SecurityMachineClient client) {
        return client.connect(getSelf().getManagementIp(), getSelf().getPort(), getSelf().getPassword());
    }

    @Override
    protected void executeConnectActions(ConnectSecurityMachineInfo info, SecurityMachineClient client, Completion complete) {
        ErrorableValue<Boolean> res = client.connect(getSelf().getManagementIp(), getSelf().getPort(), getSelf().getPassword());
        if (!res.isSuccess()) {
            complete.fail(res.error);
            return;
        }
        String testToken = Q.New(InfoSecSecretResourcePoolVO.class)
                .select(InfoSecSecretResourcePoolVO_.activatedToken)
                .eq(InfoSecSecretResourcePoolVO_.uuid, getSelf().getSecretResourcePoolUuid())
                .findValue();
        if (StringUtils.isEmpty(testToken)) {
            complete.success();
            return;
        }

        ErrorableValue<Boolean> existRes = client.isSecretKeyExist(testToken);
        if (existRes.result) {
            changeConnectionStatus(SecurityMachineStatusEvent.sync);
        }

        if (!info.isNewAdded() && !existRes.result) {
            complete.fail(operr("the security machine [%s] failed to manually detect synchronization, please confirm whether the security machine has synchronized the key!", self.getManagementIp()));
            return;
        }

        complete.success();
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }
}
