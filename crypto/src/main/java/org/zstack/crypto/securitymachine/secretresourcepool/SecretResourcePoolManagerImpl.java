package org.zstack.crypto.securitymachine.secretresourcepool;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.Platform;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SQLBatch;
import org.zstack.crypto.securitymachine.SecurityMachineGlobalConfig;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APICreateSecretResourcePoolEvent;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APICreateSecretResourcePoolMsg;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APIGetSignatureServerEncryptPublicKeyMsg;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APIGetSignatureServerEncryptPublicKeyReply;
import org.zstack.header.AbstractService;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.securitymachine.*;
import org.zstack.license.AddOnModuleType;
import org.zstack.license.AfterLicenseChangeExtensionPoint;
import org.zstack.license.LicenseManager;
import org.zstack.sns.system.SNSSystemAlarmTopicManager;
import org.zstack.tag.TagManager;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.zwatch.alarm.*;
import org.zstack.zwatch.alarm.sns.SNSActionFactory;
import org.zstack.zwatch.alarm.system.AlarmSystemTagUtils;
import org.zstack.zwatch.datatype.EmergencyLevel;
import org.zstack.zwatch.namespace.SecretResourcePoolNamespace;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.zstack.zwatch.alarm.system.SystemAlarmManager.*;

/**
 * Created by LiangHanYu on 2021/11/4 16:54
 */
public class SecretResourcePoolManagerImpl extends AbstractService implements SecretResourcePoolManager, AfterLicenseChangeExtensionPoint {
    private static final CLogger logger = Utils.getLogger(SecretResourcePoolManagerImpl.class);
    @Autowired
    private CloudBus bus;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    private LicenseManager licenseMgr;
    @Autowired
    private SecretResourcePoolNamespace secretResourcePoolNamespace;
    @Autowired
    private SecretResourcePoolTrackerImpl tracker;

    private Map<String, SecretResourcePoolFactory> secretResourcePoolFactories = new ConcurrentHashMap<>();

    @Override
    public SecretResourcePoolFactory getSecretResourcePoolFactory(String secretResourcePoolModel) {
        SecretResourcePoolFactory factory = secretResourcePoolFactories.get(secretResourcePoolModel);
        if (factory == null) {
            throw new CloudRuntimeException("No factory for secretResourcePool, check your crypto.xml");
        }
        return factory;
    }

    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof SecretResourcePoolMessage) {
            passThrough((SecretResourcePoolMessage) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APICreateSecretResourcePoolMsg) {
            handle((APICreateSecretResourcePoolMsg) msg);
        } else if (msg instanceof APIGetSignatureServerEncryptPublicKeyMsg) {
            handle((APIGetSignatureServerEncryptPublicKeyMsg) msg);
        } else if (msg instanceof SecretResourcePoolMessage) {
            passThrough((SecretResourcePoolMessage) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }

    }

    private SecretResourcePool findSecretResourcePool(String resourcePoolUuid) {
        SecretResourcePoolVO vo = dbf.findByUuid(resourcePoolUuid, SecretResourcePoolVO.class);
        if (vo == null) {
            ErrorCode err = Platform.err(SysErrors.RESOURCE_NOT_FOUND, "cannot find SecretResourcePool[uuid:%s], it may have been deleted", resourcePoolUuid);
            throw new OperationFailureException(err);
        }

        SecretResourcePoolFactory factory = this.getSecretResourcePoolFactory(vo.getModel());
        return factory.getSecretResourcePool(vo);
    }

    private void passThrough(SecretResourcePoolMessage msg) {
        findSecretResourcePool(msg.getSecretResourcePoolUuid()).handleMessage((Message) msg);
    }

    private void handle(APICreateSecretResourcePoolMsg msg) {
        final APICreateSecretResourcePoolEvent evt = new APICreateSecretResourcePoolEvent(msg.getId());

        doCreateSecretResourcePool(msg, new ReturnValueCompletion<SecretResourcePoolInventory>(msg) {
            @Override
            public void success(SecretResourcePoolInventory inventory) {
                evt.setInventory(inventory);
                bus.publish(evt);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                evt.setError(errorCode);
                bus.publish(evt);
            }
        });
    }

    private void handle(APIGetSignatureServerEncryptPublicKeyMsg msg) {
        APIGetSignatureServerEncryptPublicKeyReply reply = new APIGetSignatureServerEncryptPublicKeyReply();

        String resourcePoolUuid = SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.value(String.class);
        if (SecurityMachineGlobalConfig.NONE.equals(resourcePoolUuid)) {
            reply.setPublicKey(null);
            bus.reply(msg, reply);
            return;
        }

        try {
            reply.setPublicKey(findKey(resourcePoolUuid, SecurityMachineKeyType.EncryptPublicKey));
        } catch (RuntimeException ignored) {
            // return publicKey=null
        }

        bus.reply(msg, reply);
    }

    private void doCreateSecretResourcePool(CreateSecretResourcePoolMessage msg, ReturnValueCompletion<SecretResourcePoolInventory> completion) {
        final SecretResourcePoolVO svo = new SecretResourcePoolVO();

        if (msg.getResourceUuid() != null) {
            svo.setUuid(msg.getResourceUuid());
        } else {
            svo.setUuid(Platform.getUuid());
        }
        svo.setName(msg.getName());
        svo.setDescription(msg.getDescription());
        svo.setModel(msg.getModel());
        svo.setType(SecretResourcePoolType.valueOf(msg.getType()));
        svo.setZoneUuid(msg.getZoneUuid());
        svo.setHeartbeatInterval(msg.getHeartbeatInterval());
        svo.setState(SecretResourcePoolState.Unactivated);
        svo.setStatus(SecretResourcePoolStatus.Connected);
        svo.setAbility(StringUtils.isEmpty(msg.getAbility()) ? SecretResourcePoolAbility.All : SecretResourcePoolAbility.valueOf(msg.getAbility()));

        final SecretResourcePoolFactory factory = getSecretResourcePoolFactory(msg.getModel());
        final SecretResourcePoolVO vo = factory.createSecretResourcePool(svo, msg);

        if (msg instanceof APICreateSecretResourcePoolMsg) {
            tagMgr.createTagsFromAPICreateMessage((APICreateSecretResourcePoolMsg) msg, vo.getUuid(), SecretResourcePoolVO.class.getSimpleName());
        }

        SecretResourcePoolInventory inv = factory.getSecretResourcePoolInventory(vo.getUuid());
        logger.debug(String.format("successfully create secretResourcePool[name:%s, model:%s, uuid:%s]", inv.getName(), vo.getModel(), vo.getUuid()));
        tracker.trackSecretResourcePool(inv.getUuid());
        completion.success(inv);
    }

    @Override
    public String findKey(String resourcePoolUuid, SecurityMachineKeyType key) {
        return findSecretResourcePool(resourcePoolUuid).findByKey(key);
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(SecretResourcePoolConstant.SERVICE_ID);
    }

    @Override
    public boolean start() {
        populateExtensions();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    private void populateExtensions() {
        for (SecretResourcePoolFactory f : pluginRgty.getExtensionList(SecretResourcePoolFactory.class)) {
            SecretResourcePoolFactory old = secretResourcePoolFactories.get(f.getSecretResourcePoolModel());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate SecretResourcePoolFactory[%s, %s] for SecretResourcePoolFactory type[%s]",
                        old.getClass().getName(), f.getClass().getName(), f.getSecretResourcePoolModel()));
            }
            secretResourcePoolFactories.put(f.getSecretResourcePoolModel(), f);
        }
    }


    @Override
    public void afterChangeLicense() {
        if (licenseMgr.checkAddonAvailability(AddOnModuleType.CryptoCompliance.name) == null) {
            operationWhenCryptoExists();
        }
    }

    private void operationWhenCryptoExists() {
        new SQLBatch() {
            @Override
            protected void scripts() {
                EventSubscriptionVO secretResourcePoolStatusChangeAlarm = findByUuid(SECRET_RESOURCE_POOL_STATUS_CHANGE_EVENT_ALARM_UUID, EventSubscriptionVO.class);
                if (secretResourcePoolStatusChangeAlarm == null) {
                    secretResourcePoolStatusChangeAlarm = new EventSubscriptionVO();
                    secretResourcePoolStatusChangeAlarm.setUuid(SECRET_RESOURCE_POOL_STATUS_CHANGE_EVENT_ALARM_UUID);
                    secretResourcePoolStatusChangeAlarm.setName(SECRET_RESOURCE_POOL_STATUS_CHANGE_EVENT_ALARM_NAME);
                    secretResourcePoolStatusChangeAlarm.setEventName(SecretResourcePoolNamespace.SecretResourcePoolStatusChange.getName());
                    secretResourcePoolStatusChangeAlarm.setNamespace(secretResourcePoolNamespace.getName());
                    secretResourcePoolStatusChangeAlarm.setState(EventSubscriptionState.Enabled);
                    secretResourcePoolStatusChangeAlarm.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
                    secretResourcePoolStatusChangeAlarm.setEmergencyLevel(EmergencyLevel.Emergent);
                    persist(secretResourcePoolStatusChangeAlarm);

                    if (!q(EventSubscriptionActionVO.class).eq(EventSubscriptionActionVO_.subscriptionUuid, SECRET_RESOURCE_POOL_STATUS_CHANGE_EVENT_ALARM_UUID)
                            .eq(AlarmActionVO_.actionUuid, SNSSystemAlarmTopicManager.SYSTEM_ALARM_TOPIC_UUID)
                            .isExists()) {
                        EventSubscriptionActionVO action = new EventSubscriptionActionVO();
                        action.setActionUuid(SNSSystemAlarmTopicManager.SYSTEM_ALARM_TOPIC_UUID);
                        action.setActionType(SNSActionFactory.type.toString());
                        action.setSubscriptionUuid(SECRET_RESOURCE_POOL_STATUS_CHANGE_EVENT_ALARM_UUID);
                        persist(action);
                    }
                }

                AlarmSystemTagUtils.persistSystemTagOfLanguage(secretResourcePoolStatusChangeAlarm,
                        EventSubscriptionSystemTags.CN,
                        SECRET_RESOURCE_POOL_STATUS_CHANGE_EVENT_ALARM_NAME_CN);

                flush();
            }
        }.execute();
    }
}
