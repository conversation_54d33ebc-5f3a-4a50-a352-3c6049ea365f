package org.zstack.crypto.securitymachine.thirdparty.flkSec;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;
import org.zstack.header.search.Parent;
import org.zstack.header.securitymachine.SecretResourcePoolInventory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by LiangHanYu on 2023/2/3 11:22
 */
@PythonClassInventory
@Inventory(mappingVOClass = FlkSecSecretResourcePoolVO.class, collectionValueOfMethod = "valueOf1",
        parent = {@Parent(inventoryClass = SecretResourcePoolInventory.class, type = FlkSecSecretResourcePoolConstant.SECRET_RESOURCE_POOL_TYPE)})
public class FlkSecSecretResourcePoolInventory extends SecretResourcePoolInventory {
    private String encryptResult;

    private String activatedToken;

    private String protectToken;

    private String hmacToken;

    private String ukeyType;

    protected FlkSecSecretResourcePoolInventory(FlkSecSecretResourcePoolVO vo) {
        super(vo);
        this.setEncryptResult(vo.getEncryptResult());
        this.setActivatedToken(vo.getActivatedToken());
        this.setProtectToken(vo.getProtectToken());
        this.setHmacToken(vo.getHmacToken());
        this.setUkeyType(vo.getUkeyType());
    }

    public FlkSecSecretResourcePoolInventory() {
    }

    public static FlkSecSecretResourcePoolInventory valueOf(FlkSecSecretResourcePoolVO vo) {
        return new FlkSecSecretResourcePoolInventory(vo);
    }

    public static List<FlkSecSecretResourcePoolInventory> valueOf1(Collection<FlkSecSecretResourcePoolVO> vos) {
        List<FlkSecSecretResourcePoolInventory> invs = new ArrayList<FlkSecSecretResourcePoolInventory>();
        for (FlkSecSecretResourcePoolVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }

    public String getUkeyType() {
        return ukeyType;
    }

    public void setUkeyType(String ukeyType) {
        this.ukeyType = ukeyType;
    }

    public String getEncryptResult() {
        return encryptResult;
    }

    public void setEncryptResult(String encryptResult) {
        this.encryptResult = encryptResult;
    }

    public String getActivatedToken() {
        return activatedToken;
    }

    public void setActivatedToken(String activatedToken) {
        this.activatedToken = activatedToken;
    }

    public String getProtectToken() {
        return protectToken;
    }

    public void setProtectToken(String protectToken) {
        this.protectToken = protectToken;
    }

    public String getHmacToken() {
        return hmacToken;
    }

    public void setHmacToken(String hmacToken) {
        this.hmacToken = hmacToken;
    }
}