package org.zstack.crypto.securitymachine.thirdparty.sansec;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.encrypt.EncryptDriverType;
import org.zstack.crypto.securitymachine.SecurityMachineHelper;
import org.zstack.crypto.securitymachine.thirdparty.EncryptBaseDriver;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

/**
 * Created by LiangHanYu on 2021/11/14 18:19
 */
public class SanSecEncryptDriver extends EncryptBaseDriver {
    private static final CLogger logger = Utils.getLogger(SanSecEncryptDriver.class);
    @Autowired
    protected SecurityMachineHelper securityMachineHelper;

    @Override
    public EncryptDriverType getDriverType() {
        return new EncryptDriverType(SanSecConstant.SECURITY_MACHINE_TYPE);
    }
}
