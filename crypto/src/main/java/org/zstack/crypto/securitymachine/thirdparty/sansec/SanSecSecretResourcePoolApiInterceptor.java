package org.zstack.crypto.securitymachine.thirdparty.sansec;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.crypto.securitymachine.SecurityMachineHelper;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APICreateSanSecSecretResourcePoolMsg;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.APIMessage;
import org.zstack.header.securitymachine.SecretResourcePoolType;

import java.util.ArrayList;
import java.util.List;

import static org.zstack.core.Platform.argerr;

/**
 * Created by LiangHanYu on 2021/11/17 17:58
 */
public class SanSecSecretResourcePoolApiInterceptor implements ApiMessageInterceptor {
    @Autowired
    protected SecurityMachineHelper securityMachineHelper;

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreateSanSecSecretResourcePoolMsg) {
            validate((APICreateSanSecSecretResourcePoolMsg) msg);
        }

        return msg;
    }

    private void validate(APICreateSanSecSecretResourcePoolMsg msg) {
        if (!msg.getModel().equals(SanSecConstant.SECURITY_MACHINE_TYPE)) {
            throw new ApiMessageInterceptionException(argerr("currently does not support the creation of %s resource pools", msg.getModel()));
        }

        if (!SecretResourcePoolType.CloudSecurityResourceService.toString().equals(msg.getType())) {
            return;
        }

        List<String> connectParas = new ArrayList<>();
        connectParas.add(msg.getManagementIp());
        connectParas.add(String.valueOf(msg.getPort()));
        connectParas.add(msg.getUsername());
        connectParas.add(msg.getPassword());
        connectParas.add(msg.getSm3Key());
        connectParas.add(msg.getSm4Key());

        ErrorCode clientError = securityMachineHelper.checkClientAllServerAvailable(msg.getModel(), msg.getType(), connectParas);
        if (clientError != null) {
            throw new ApiMessageInterceptionException(clientError);
        }
    }
}
