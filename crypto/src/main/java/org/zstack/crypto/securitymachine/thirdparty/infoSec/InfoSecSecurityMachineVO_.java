package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.zstack.header.securitymachine.SecurityMachineVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * Created by LiangHanYu on 2021/10/28 17:24
 */
@StaticMetamodel(InfoSecSecurityMachineVO.class)
public class InfoSecSecurityMachineVO_ extends SecurityMachineVO_ {
    public static volatile SingularAttribute<InfoSecSecurityMachineVO, String> password;
    public static volatile SingularAttribute<InfoSecSecurityMachineVO, Integer> port;
}