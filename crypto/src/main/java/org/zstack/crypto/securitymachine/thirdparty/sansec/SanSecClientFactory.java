package org.zstack.crypto.securitymachine.thirdparty.sansec;

import org.zstack.crypto.securitymachine.AddSecurityMachineMessage;
import org.zstack.crypto.securitymachine.SecurityMachineClient;
import org.zstack.crypto.securitymachine.SecurityMachineClientFactory;
import org.zstack.crypto.securitymachine.mockclient.MockInfoSecClient;
import org.zstack.crypto.securitymachine.mockclient.MockSecurityMachineClientGlobalProperty;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorableValue;
import org.zstack.header.securitymachine.SecretResourcePoolType;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import static org.zstack.core.Platform.operr;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.header.rest.RESTFacade;
/**
 * Created by <PERSON><PERSON><PERSON> on 21/11/18
 */
public class SanSecClientFactory implements SecurityMachineClientFactory {
    private static final CLogger logger = Utils.getLogger(SanSecClientFactory.class);
    @Autowired
    private RESTFacade restf;
    protected static MockInfoSecClient mockSanSecClient;

    @Override
    public SecurityMachineClient create() {
        if (!MockSecurityMachineClientGlobalProperty.ENABLE_MOCK) {
            return new SanSecClient();
        }

        if (mockSanSecClient == null) {
            synchronized (MockInfoSecClient.class) {
                if (mockSanSecClient == null) {
                    mockSanSecClient = new MockInfoSecClient();
                }
            }
        }
        return mockSanSecClient;
    }

    public SecurityMachineClient create(String type) {
        if (!MockSecurityMachineClientGlobalProperty.ENABLE_MOCK && SecretResourcePoolType.CloudSecurityResourceService.toString().equals(type)) {
            return new SanSecPoolClient(restf);
        }
        return create();
    }

    @Override
    public String getType() {
        return SanSecConstant.SECURITY_MACHINE_TYPE;
    }

    @Override
    public ErrorCode testConnection(AddSecurityMachineMessage message) {
        AddSanSecSecurityMachineMessage infoMsg = (AddSanSecSecurityMachineMessage) message;

        try (SecurityMachineClient agent = create()) {
            ErrorableValue<Boolean> res = agent.connect(infoMsg.getManagementIp(), infoMsg.getPort(), infoMsg.getPassword());
            return res.isSuccess() ? null : res.error;
        } catch (Exception ignored) {
            return operr(ignored.getMessage(), "SanSecClientFactory testConnection failed");
        }
    }
}
