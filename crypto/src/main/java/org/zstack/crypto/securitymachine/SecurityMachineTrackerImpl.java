package org.zstack.crypto.securitymachine;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.thread.AsyncThread;
import org.zstack.core.thread.AsyncTimer;
import org.zstack.header.Component;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.managementnode.ManagementNodeChangeListener;
import org.zstack.header.managementnode.ManagementNodeInventory;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.message.MessageReply;
import org.zstack.header.securitymachine.*;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import javax.persistence.Tuple;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by LiangHanYu on 2021/11/22 20:44
 */
public class SecurityMachineTrackerImpl implements SecurityMachineTracker, ManagementNodeChangeListener, Component, ManagementNodeReadyExtensionPoint {
    private final static CLogger logger = Utils.getLogger(SecurityMachineTrackerImpl.class);

    private Map<String, SecurityMachineTrackerImpl.Tracker> trackers = new HashMap<>();
    private final static Long interval = 6L;

    @Autowired
    private ResourceDestinationMaker destMaker;
    @Autowired
    private CloudBus bus;
    @Autowired
    protected EventFacade evtf;


    @Override
    public void managementNodeReady() {
        reScanSecurityMachine();
    }

    @Override
    public void trackSecurityMachine(String SecurityMachineUuid) {
        SecurityMachineTrackerImpl.Tracker t = trackers.get(SecurityMachineUuid);
        if (t != null) {
            t.cancel();
        }

        t = new SecurityMachineTrackerImpl.Tracker(SecurityMachineUuid);
        trackers.put(SecurityMachineUuid, t);

        if (CoreGlobalProperty.UNIT_TEST_ON) {
            t.start();
        } else {
            t.startRightNow();
        }

        logger.debug(String.format("starting tracking securityMachine[uuid:%s]", SecurityMachineUuid));
    }

    @Override
    public void untrackSecurityMachine(String SecurityMachineUuid) {
        SecurityMachineTrackerImpl.Tracker t = trackers.get(SecurityMachineUuid);
        if (t != null) {
            t.cancel();
        }
        trackers.remove(SecurityMachineUuid);
        logger.debug(String.format("stop tracking securityMachine[uuid:%s]", SecurityMachineUuid));
    }

    @Override
    public void trackSecurityMachine(Collection<String> SecurityMachineUuids) {
        SecurityMachineUuids.forEach(this::trackSecurityMachine);
    }

    private class Tracker extends AsyncTimer {
        private final CLogger logger = Utils.getLogger(SecurityMachineTrackerImpl.class);

        private String uuid;
        private String secretResourcePoolUuid;
        private Integer heartbeatInterval;

        Tracker(String uuid) {
            super(TimeUnit.SECONDS, interval);
            this.uuid = uuid;
            secretResourcePoolUuid = Q.New(SecurityMachineVO.class).select(SecurityMachineVO_.secretResourcePoolUuid)
                    .eq(SecurityMachineVO_.uuid, uuid).findValue();
            if (secretResourcePoolUuid == null) {
                throw new CloudRuntimeException(String.format("security machine[uuid:%s] is deleted, why you submit a tracker for it???", uuid));
            }
            heartbeatInterval = Q.New(SecretResourcePoolVO.class).select(SecretResourcePoolVO_.heartbeatInterval)
                    .eq(SecretResourcePoolVO_.uuid, secretResourcePoolUuid).findValue();
            if (heartbeatInterval != null) {
                super.period = heartbeatInterval.longValue();
            }
            __name__ = String.format("security-machine-tracker-%s", uuid);
        }

        @Override
        protected void execute() {
            track();
        }

        private void track() {
            Tuple t = Q.New(SecurityMachineVO.class).select(SecurityMachineVO_.state, SecurityMachineVO_.status)
                    .eq(SecurityMachineVO_.uuid, uuid).findTuple();

            if (t == null) {
                logger.debug(String.format("security machine[uuid:%s] seems to be deleted, stop tracking it", uuid));
                return;
            }

            SecurityMachineState state = t.get(0, SecurityMachineState.class);

            if (state == SecurityMachineState.Disabled) {
                logger.debug(String.format("security machine[uuid:%s] is in state of %s, not tracking it this time", uuid, state));
                cancel();
                return;
            }

            SecurityMachineHeartbeatDetectionMsg msg = new SecurityMachineHeartbeatDetectionMsg();
            msg.setSecurityMachineUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(msg, SecurityMachineConstant.SERVICE_ID, uuid);
            bus.send(msg, new CloudBusCallBack(null) {
                @Override
                public void run(MessageReply reply) {
                    continueToRunThisTimer();
                }
            });
        }
    }

    private void reScanSecurityMachine() {
        reScanSecurityMachine(false);
    }

    private void reScanSecurityMachine(boolean skipExisting) {
        if (!skipExisting) {
            new HashSet<>(trackers.values()).forEach(SecurityMachineTrackerImpl.Tracker::cancel);
        }

        List<String> securityMachineUuids = Q.New(SecurityMachineVO.class)
                .notEq(SecurityMachineVO_.state, SecurityMachineState.Disabled)
                .select(SecurityMachineVO_.uuid)
                .listValues();
        Set<String> toTrack = securityMachineUuids.stream().filter(securityMachineUuid -> {
            if (skipExisting) {
                return destMaker.isManagedByUs(securityMachineUuid) && !trackers.containsKey(securityMachineUuid);
            } else {
                return destMaker.isManagedByUs(securityMachineUuid);
            }
        }).collect(Collectors.toSet());

        trackSecurityMachine(toTrack);
    }

    @Override
    @AsyncThread
    public void nodeJoin(ManagementNodeInventory inv) {
        reScanSecurityMachine();
    }

    @Override
    public void nodeLeft(ManagementNodeInventory inv) {
        reScanSecurityMachine();
    }

    @Override
    public void iAmDead(ManagementNodeInventory inv) {

    }

    @Override
    public void iJoin(ManagementNodeInventory inv) {

    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }
}
