package org.zstack.crypto.securitymachine.thirdparty.plugin;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.abstraction.crypto.CryptoClientDriver;
import org.zstack.core.db.Q;
import org.zstack.core.plugin.PluginManager;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APICreatePluginSecretResourcePoolMsg;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APIUpdatePluginSecretResourcePoolMsg;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.message.APIMessage;

import java.util.Map;

import static org.zstack.core.Platform.argerr;

public class PluginSecretResourcePoolApiInterceptor implements ApiMessageInterceptor {
    @Autowired
    private PluginManager pluginManager;

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreatePluginSecretResourcePoolMsg) {
            validate((APICreatePluginSecretResourcePoolMsg) msg);
        } else if (msg instanceof APIUpdatePluginSecretResourcePoolMsg) {
            validate((APIUpdatePluginSecretResourcePoolMsg) msg);
        }

        return msg;
    }

    private void validate(APIUpdatePluginSecretResourcePoolMsg msg) {
        String pluginUuid = Q.New(PluginSecretResourcePoolVO.class).select(PluginSecretResourcePoolVO_.pluginDriverUuid).eq(PluginSecretResourcePoolVO_.uuid, msg.getUuid()).findValue();
        CryptoClientDriver client = pluginManager.getPlugin(pluginUuid);
        if (msg.getProperties() == null) {
            return;
        }

        if (!client.connect(msg.getProperties())) {
            throw new ApiMessageInterceptionException(argerr("%s client initialization failed: secretResourcePool property is invalid.", msg.getModel()));
        }
    }

    private void validate(APICreatePluginSecretResourcePoolMsg msg) {
        CryptoClientDriver client = pluginManager.getPlugin(msg.getPluginDriverUuid());
        if (msg.getProperties() == null) {
            return;
        }

        if (!client.connect(msg.getProperties())) {
            throw new ApiMessageInterceptionException(argerr("%s client initialization failed: secretResourcePool property is invalid.", msg.getModel()));
        }
    }
}
