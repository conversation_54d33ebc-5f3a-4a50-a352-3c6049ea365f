package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.message.GsonTransient;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.search.Inventory;
import org.zstack.header.search.Parent;
import org.zstack.header.securitymachine.SecurityMachineInventory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by LiangHanYu on 2021/11/3 16:06
 */
@PythonClassInventory
@Inventory(mappingVOClass = InfoSecSecurityMachineVO.class, collectionValueOfMethod = "valueOf1",
        parent = {@Parent(inventoryClass = SecurityMachineInventory.class, type = InfoSecSecurityMachineConstant.SECURITY_MACHINE_TYPE)})
public class InfoSecSecurityMachineInventory extends SecurityMachineInventory {
    @GsonTransient
    @APINoSee
    private String password;

    private Integer port;

    protected InfoSecSecurityMachineInventory(InfoSecSecurityMachineVO vo) {
        super(vo);
        this.setPassword(vo.getPassword());
        this.setPort(vo.getPort());
    }

    public InfoSecSecurityMachineInventory() {
    }

    public static InfoSecSecurityMachineInventory valueOf(InfoSecSecurityMachineVO vo) {
        return new InfoSecSecurityMachineInventory(vo);
    }

    public static List<InfoSecSecurityMachineInventory> valueOf1(Collection<InfoSecSecurityMachineVO> vos) {
        List<InfoSecSecurityMachineInventory> invs = new ArrayList<InfoSecSecurityMachineInventory>();
        for (InfoSecSecurityMachineVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }
}
