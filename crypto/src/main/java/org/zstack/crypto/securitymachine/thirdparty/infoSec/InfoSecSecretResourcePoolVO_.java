package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import org.zstack.header.securitymachine.SecretResourcePoolVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * Created by LiangHanYu on 2021/10/28 11:38
 */
@StaticMetamodel(InfoSecSecretResourcePoolVO.class)
public class InfoSecSecretResourcePoolVO_ extends SecretResourcePoolVO_ {
    public static volatile SingularAttribute<InfoSecSecretResourcePoolVO, Integer> connectionMode;
    public static volatile SingularAttribute<InfoSecSecretResourcePoolVO, String> activatedToken;
    public static volatile SingularAttribute<InfoSecSecretResourcePoolVO, String> protectToken;
    public static volatile SingularAttribute<InfoSecSecretResourcePoolVO, String> hmacToken;
    public static volatile SingularAttribute<InfoSecSecretResourcePoolVO, String> encryptPublicKey;
    public static volatile SingularAttribute<InfoSecSecretResourcePoolVO, String> encryptSubjectDN;
}
