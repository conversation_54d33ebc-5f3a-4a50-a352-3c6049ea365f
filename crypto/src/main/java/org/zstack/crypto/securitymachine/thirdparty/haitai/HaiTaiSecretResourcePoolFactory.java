package org.zstack.crypto.securitymachine.thirdparty.haitai;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.crypto.securitymachine.secretresourcepool.CreateSecretResourcePoolMessage;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePool;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePoolFactory;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.securitymachine.SecretResourcePoolInventory;
import org.zstack.header.securitymachine.SecretResourcePoolVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import static org.zstack.core.Platform.operr;

/**
 * Created by LiangHanYu on 2022/7/27 16:18
 */
public class HaiTaiSecretResourcePoolFactory implements SecretResourcePoolFactory {
    private static final CLogger logger = Utils.getLogger(HaiTaiSecretResourcePoolFactory.class);
    @Autowired
    private DatabaseFacade dbf;

    @Override
    public String getSecretResourcePoolModel() {
        return HaiTaiSecretResourcePoolConstant.SECRET_RESOURCE_POOL_TYPE;
    }


    @Override
    public SecretResourcePoolVO createSecretResourcePool(SecretResourcePoolVO vo, CreateSecretResourcePoolMessage msg) {
        if (!(msg instanceof CreateHaiTaiSecretResourcePoolMessage)) {
            throw new OperationFailureException(operr("secretResourcePool[uuid:%s] model is not %s", msg.getResourceUuid(), vo.getModel()));
        }

        CreateHaiTaiSecretResourcePoolMessage createHaiTaiSecretResourcePoolMessage = (CreateHaiTaiSecretResourcePoolMessage) msg;
        HaiTaiSecretResourcePoolVO ivo = new HaiTaiSecretResourcePoolVO(vo);
        ivo.setManagementIp(createHaiTaiSecretResourcePoolMessage.getManagementIp());
        ivo.setPort(createHaiTaiSecretResourcePoolMessage.getPort());
        ivo.setRealm(createHaiTaiSecretResourcePoolMessage.getRealm());
        ivo = dbf.persistAndRefresh(ivo);
        return ivo;
    }

    @Override
    public SecretResourcePoolInventory getSecretResourcePoolInventory(String uuid) {
        HaiTaiSecretResourcePoolVO vo = dbf.findByUuid(uuid, HaiTaiSecretResourcePoolVO.class);
        return vo == null ? null : HaiTaiSecretResourcePoolInventory.valueOf(vo);
    }

    @Override
    public SecretResourcePool getSecretResourcePool(SecretResourcePoolVO vo) {
        HaiTaiSecretResourcePoolVO haiTaiSecretResourcePoolVO = dbf.findByUuid(vo.getUuid(), HaiTaiSecretResourcePoolVO.class);
        return new HaiTaiSecretResourcePoolBase(haiTaiSecretResourcePoolVO);
    }
}
