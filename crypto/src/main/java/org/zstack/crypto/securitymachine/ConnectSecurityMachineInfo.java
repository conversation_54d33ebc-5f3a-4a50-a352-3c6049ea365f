package org.zstack.crypto.securitymachine;

/**
 * Created by LiangHanYu on 2021/11/17 20:10
 */
public class ConnectSecurityMachineInfo {
    private boolean isNewAdded;

    public boolean isNewAdded() {
        return isNewAdded;
    }

    public void setNewAdded(boolean isNewAdded) {
        this.isNewAdded = isNewAdded;
    }

    public static ConnectSecurityMachineInfo fromConnectSecurityMachineMsg(ConnectSecurityMachineMsg msg) {
        ConnectSecurityMachineInfo info = new ConnectSecurityMachineInfo();
        info.setNewAdded(msg.isNewAdd());
        return info;
    }

    ConnectSecurityMachineInfo(boolean isNewAdded) {
        this.isNewAdded = isNewAdded;
    }

    ConnectSecurityMachineInfo() {
    }
}
