package org.zstack.crypto.securitymachine.api.securitymachine

import org.zstack.crypto.securitymachine.api.securitymachine.APIDeleteSecurityMachineEvent

doc {
    title "DeleteSecurityMachine"

    category "securityMachine"

    desc """删除密码机"""

    rest {
        request {
			url "DELETE /v1/security-machines/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeleteSecurityMachineMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "4.3.18"
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc "删除模式(Permissive / Enforcing，Permissive)"
					location "body"
					type "String"
					optional true
					since "4.3.18"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "4.3.18"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "4.3.18"
				}
			}
        }

        response {
            clz APIDeleteSecurityMachineEvent.class
        }
    }
}