package org.zstack.crypto.securitymachine.api.secretresourcepool

import org.zstack.crypto.securitymachine.api.secretresourcepool.APICreateSecretResourcePoolEvent

doc {
    title "CreateCSPSecretResourcePool"

    category "secretResourcePool"

    desc """创建CSP资源池"""

    rest {
        request {
			url "POST /v1/secret-resource-pool/csp"

			header (Authorization: 'OAuth the-session-uuid')

            clz APICreateCSPSecretResourcePoolMsg.class

            desc """"""
            
			params {

				column {
					name "managementIp"
					enclosedIn "params"
					desc "服务ip"
					location "body"
					type "String"
					optional false
					since "5.0.0"
				}
				column {
					name "port"
					enclosedIn "params"
					desc "服务端口"
					location "body"
					type "Integer"
					optional false
					since "5.0.0"
				}
				column {
					name "appId"
					enclosedIn "params"
					desc "应用ID用于获取服务token"
					location "body"
					type "String"
					optional false
					since "5.0.0"
				}
				column {
					name "appKey"
					enclosedIn "params"
					desc "应用key用于获取服务token"
					location "body"
					type "String"
					optional false
					since "5.0.0"
				}
				column {
					name "keyId"
					enclosedIn "params"
					desc "keyId用于对称加解密"
					location "body"
					type "String"
					optional false
					since "5.0.0"
				}
				column {
					name "userId"
					enclosedIn "params"
					desc "用户id用于验签"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "name"
					enclosedIn "params"
					desc "资源名称"
					location "body"
					type "String"
					optional false
					since "5.0.0"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "model"
					enclosedIn "params"
					desc "服务厂商"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "type"
					enclosedIn "params"
					desc "服务类型"
					location "body"
					type "String"
					optional false
					since "5.0.0"
					values ("CloudSecurityMachine","OrdinarySecurityMachine","CloudSecurityResourceService")
				}
				column {
					name "heartbeatInterval"
					enclosedIn "params"
					desc "心跳检测时间间隔"
					location "body"
					type "Integer"
					optional false
					since "5.0.0"
				}
				column {
					name "zoneUuid"
					enclosedIn "params"
					desc "区域UUID"
					location "body"
					type "String"
					optional false
					since "5.0.0"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc "资源UUID"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "5.0.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.0.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.0.0"
				}
				column {
					name "protocol"
					enclosedIn "params"
					desc "接口协议"
					location "body"
					type "String"
					optional true
					since "5.0.0"
					values ("https","http")
				}
				column {
					name "ability"
					enclosedIn "params"
					desc "用途"
					location "body"
					type "String"
					optional true
					since "5.3.20"
					values ("All","DataCrypto","Auth")
				}
			}
        }

        response {
            clz APICreateSecretResourcePoolEvent.class
        }
    }
}