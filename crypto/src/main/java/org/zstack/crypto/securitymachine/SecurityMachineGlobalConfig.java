package org.zstack.crypto.securitymachine;

import org.zstack.core.config.GlobalConfig;
import org.zstack.core.config.GlobalConfigDef;
import org.zstack.core.config.GlobalConfigDefinition;
import org.zstack.core.config.GlobalConfigValidation;
import org.zstack.header.securitymachine.SecurityMachineConstant;

/**
 * Created by LiangHanYu on 2021/11/2 11:16
 */
@GlobalConfigDefinition
public class SecurityMachineGlobalConfig {
    public static final String CATEGORY = SecurityMachineConstant.CATEGORY;
    public static final String NONE = "None";

    @GlobalConfigDef(defaultValue = NONE, description = "resource pool uuid used for identity authentication")
    public static GlobalConfig RESOURCE_POOL_UUID_FOR_AUTH_LOGIN =
            new GlobalConfig(CATEGORY, "crypto.authLogin.resourcePoolUuid");
    @GlobalConfigDef(defaultValue = NONE, description = "resource pool uuid used for data protection")
    public static GlobalConfig RESOURCE_POOL_UUID_FOR_DATA_PROTECT =
            new GlobalConfig(CATEGORY, "crypto.dataProtect.resourcePoolUuid");
    @GlobalConfigValidation(numberGreaterThan = 0)
    public static GlobalConfig HEART_BEAT_PARALLELISM_DEGREE = new GlobalConfig(CATEGORY, "heartbeat.parallelismDegree");

    @GlobalConfigDef(defaultValue = "None", description = "sm4 encryption mode for security machine")
    public static GlobalConfig SECURITY_MACHINE_SM4_ENCRYPTION_MODE = new GlobalConfig(CATEGORY, "security.machine.sm4.encryption.mode");
    @GlobalConfigDef(defaultValue = "None", description = "sm4 padding mode for security machine")
    public static GlobalConfig SECURITY_MACHINE_SM4_PADDING_MODE = new GlobalConfig(CATEGORY, "security.machine.sm4.padding.mode");
}
