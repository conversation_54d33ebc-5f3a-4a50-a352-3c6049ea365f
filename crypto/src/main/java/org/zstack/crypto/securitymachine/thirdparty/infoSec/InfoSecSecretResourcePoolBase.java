package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import com.aliyuncs.utils.StringUtils;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.crypto.securitymachine.ChangeSecurityMachineStatusMsg;
import org.zstack.crypto.securitymachine.SecurityMachineClient;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APIUpdateInfoSecSecretResourcePoolMsg;
import org.zstack.crypto.securitymachine.api.secretresourcepool.APIUpdateSecretResourcePoolMsg;
import org.zstack.crypto.securitymachine.api.securitymachine.APISetSecurityMachineKeyMsg;
import org.zstack.crypto.securitymachine.secretresourcepool.ChangeSecretResourcePoolStateMsg;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePool;
import org.zstack.crypto.securitymachine.secretresourcepool.SecretResourcePoolBase;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.ErrorableValue;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.securitymachine.*;

import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.zstack.core.Platform.operr;
import static org.zstack.header.securitymachine.SecurityMachineKeyType.*;

/**
 * Created by LiangHanYu on 2021/11/4 18:22
 */
public class InfoSecSecretResourcePoolBase extends SecretResourcePoolBase implements SecretResourcePool {
    protected InfoSecSecretResourcePoolBase(SecretResourcePoolVO self) {
        super(self);
    }

    @Override
    protected SecretResourcePoolVO updateSecretResourcePool(APIUpdateSecretResourcePoolMsg msg) {
        if (!(msg instanceof APIUpdateInfoSecSecretResourcePoolMsg)) {
            return super.updateSecretResourcePool(msg);
        }

        InfoSecSecretResourcePoolVO vo = (InfoSecSecretResourcePoolVO) super.updateSecretResourcePool(msg);
        vo = vo == null ? getSelf() : vo;

        APIUpdateInfoSecSecretResourcePoolMsg umsg = (APIUpdateInfoSecSecretResourcePoolMsg) msg;
        if (umsg.getConnectionMode() != null) {
            vo.setConnectionMode(umsg.getConnectionMode());
        }
        if (umsg.getActivatedToken() != null) {
            vo.setActivatedToken(umsg.getActivatedToken());
        }
        if (umsg.getProtectToken() != null) {
            vo.setProtectToken(umsg.getProtectToken());
        }
        if (umsg.getHmacToken() != null) {
            vo.setHmacToken(umsg.getHmacToken());
        }

        return vo;
    }

    private InfoSecSecretResourcePoolVO getSelf() {
        return (InfoSecSecretResourcePoolVO) self;
    }

    @Override
    protected SecretResourcePoolInventory getSelfInventory() {
        return InfoSecSecretResourcePoolInventory.valueOf(getSelf());
    }

    @Override
    protected void handleApiMessage(APIMessage msg) {
        super.handleApiMessage(msg);
    }

    @Override
    protected void handleLocalMessage(Message msg) {
        super.handleLocalMessage(msg);
    }

    @Override
    protected void heartbeatDetectHook(Completion completion) {
        completion.success();
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    @Override
    public String findByKey(SecurityMachineKeyType key) {
        return getDbToken(self.getUuid(), key.toString());
    }

    private String getDbToken(String uuid, String tokenType) {
        String dbToken = null;
        switch (SecurityMachineKeyType.valueOf(tokenType)) {
            case Active:
                dbToken = Q.New(InfoSecSecretResourcePoolVO.class).select(InfoSecSecretResourcePoolVO_.activatedToken)
                        .eq(SecretResourcePoolVO_.uuid, uuid).findValue();
                break;
            case Protect:
                dbToken = Q.New(InfoSecSecretResourcePoolVO.class).select(InfoSecSecretResourcePoolVO_.protectToken)
                        .eq(SecretResourcePoolVO_.uuid, uuid).findValue();
                break;
            case Hmac:
                dbToken = Q.New(InfoSecSecretResourcePoolVO.class).select(InfoSecSecretResourcePoolVO_.hmacToken)
                        .eq(SecretResourcePoolVO_.uuid, uuid).findValue();
                break;
            case EncryptPublicKey:
                dbToken = Q.New(InfoSecSecretResourcePoolVO.class).select(InfoSecSecretResourcePoolVO_.encryptPublicKey)
                        .eq(SecretResourcePoolVO_.uuid, uuid).findValue();
                break;
            case EncryptSubjectDN:
                dbToken = Q.New(InfoSecSecretResourcePoolVO.class).select(InfoSecSecretResourcePoolVO_.encryptSubjectDN)
                        .eq(SecretResourcePoolVO_.uuid, uuid).findValue();
                break;
        }
        return dbToken;
    }

    @Override
    protected void setTokenForSecretResourcePool(APISetSecurityMachineKeyMsg msg, ReturnValueCompletion<List<SecurityMachineInventory>> completion) {
        final FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("activate-secret-resource-pool-%s", msg.getUuid()));

        chain.then(new ShareFlow() {
            String generatedActivateToken = null;
            String generatedDataProtectToken = null;
            String generatedHmacToken = null;
            List<SecurityMachineInventory> unsyncedMachineList = new ArrayList<>();
            List<InfoSecSecurityMachineVO> checkMachineList = new ArrayList<>();

            private boolean noNeedToRegenerateToken() {
                String activatedToken = Q.New(InfoSecSecretResourcePoolVO.class)
                        .select(InfoSecSecretResourcePoolVO_.activatedToken)
                        .eq(InfoSecSecretResourcePoolVO_.uuid, msg.getUuid()).findValue();
                return msg.getTokenName().equals(activatedToken);
            }

            private boolean notActivatePoolOperation() {
                return !SecurityMachineKeyType.Active.toString().equals(msg.getType());
            }

            private boolean isDryRunTokenCheck() {
                return msg.isDryRun();
            }

            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = "generate-token";

                    @Override
                    public boolean skip(Map data) {
                        return notActivatePoolOperation() || noNeedToRegenerateToken();
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        InfoSecSecurityMachineVO vo = Q.New(InfoSecSecurityMachineVO.class)
                                .eq(InfoSecSecurityMachineVO_.secretResourcePoolUuid, msg.getUuid())
                                .eq(InfoSecSecurityMachineVO_.state, SecurityMachineState.Enabled)
                                .limit(1).find();
                        try (SecurityMachineClient generateClient = securityMachineMgr.getSecurityMachineClient(self.getModel(), self.getType().toString())) {
                            ErrorableValue<Boolean> response = generateClient.connect(vo.getManagementIp(), vo.getPort(), vo.getPassword());
                            if (!response.isSuccess()) {
                                trigger.fail(operr("the connection to the security machine %s failed during the process of generating the test key because %s", vo.getUuid(), response.error));
                                return;
                            }
                            generateAllTokenAndSetToken(generateClient, vo, trigger);
                        } catch (Exception ignored) {
                            trigger.fail(operr(ignored.getMessage(), "generate-token failed"));
                        }
                    }

                    private void generateAllTokenAndSetToken(SecurityMachineClient client, InfoSecSecurityMachineVO vo, FlowTrigger trigger) {
                        ErrorableValue<String> activateTokenRes = client.generateSm4Token(msg.getTokenName());
                        if (!activateTokenRes.isSuccess()) {
                            trigger.fail(operr("failed to generate activated token for the security machine %s because %s", vo.getUuid(), activateTokenRes.error));
                            return;
                        }

                        ErrorableValue<String> dataProtectTokenRes = ErrorableValue.of(null);
                        String dataProtectTokenName = SecretResourcePool.buildDataProtectToken(msg.getUuid());
                        if (StringUtils.isEmpty(getDbToken(msg.getUuid(), SecurityMachineKeyType.Protect.toString()))) {
                            dataProtectTokenRes = client.generateDataProtectToken(dataProtectTokenName);
                        }
                        if (!dataProtectTokenRes.isSuccess()) {
                            trigger.fail(operr("failed to generate dataProtect token %s for the security machine %s because %s", dataProtectTokenName, vo.getUuid(), dataProtectTokenRes.error));
                            return;
                        }

                        ErrorableValue<String> hmacTokenTokenRes = ErrorableValue.of(null);
                        String hmacTokenName = SecretResourcePool.buildHmacToken(msg.getUuid());
                        if (StringUtils.isEmpty(getDbToken(msg.getUuid(), SecurityMachineKeyType.Hmac.toString()))) {
                            hmacTokenTokenRes = client.generateHmacToken(hmacTokenName);
                        }
                        if (!hmacTokenTokenRes.isSuccess()) {
                            trigger.fail(operr("failed to generate hmac token %s for the security machine %s because %s", hmacTokenName, vo.getUuid(), hmacTokenTokenRes.error));
                            return;
                        }

                        generatedActivateToken = activateTokenRes.result;
                        generatedDataProtectToken = dataProtectTokenRes.result;
                        generatedHmacToken = hmacTokenTokenRes.result;
                        trigger.next();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "save-all-generated-token-to-db";

                    @Override
                    public boolean skip(Map data) {
                        return notActivatePoolOperation() || noNeedToRegenerateToken();
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        updateAllTokenInDB(msg.getUuid(), generatedActivateToken, generatedDataProtectToken, generatedHmacToken);
                        trigger.next();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "save-non-generated-token-to-db";

                    @Override
                    public boolean skip(Map data) {
                        return !isNonGeneratedKeyType(SecurityMachineKeyType.valueOf(msg.getType()));
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        SecurityMachineKeyType type = SecurityMachineKeyType.valueOf(msg.getType());

                        if (type.equals(EncryptPublicKey)) {
                            ErrorableValue<String> subjectDN = extractSubjectDNFromPublicKey(msg.getTokenName());
                            if (!subjectDN.isSuccess()) {
                                trigger.fail(operr(subjectDN.error, "failed to parse encrypt public key"));
                                return;
                            }

                            SQL.New(InfoSecSecretResourcePoolVO.class)
                                    .set(InfoSecSecretResourcePoolVO_.encryptPublicKey, msg.getTokenName())
                                    .set(InfoSecSecretResourcePoolVO_.encryptSubjectDN, subjectDN.result)
                                    .update();
                        } else if (type.equals(EncryptSubjectDN)) {
                            SQL.New(InfoSecSecretResourcePoolVO.class)
                                    .set(InfoSecSecretResourcePoolVO_.encryptSubjectDN, msg.getTokenName())
                                    .update();
                        }

                        trigger.next();
                    }

                    boolean isNonGeneratedKeyType(SecurityMachineKeyType type) {
                        return type == EncryptPublicKey || type == EncryptSubjectDN;
                    }

                    ErrorableValue<String> extractSubjectDNFromPublicKey(String publicKey) {
                        InfoSecSecurityMachineVO vo = Q.New(InfoSecSecurityMachineVO.class)
                                .eq(InfoSecSecurityMachineVO_.secretResourcePoolUuid, msg.getUuid())
                                .eq(InfoSecSecurityMachineVO_.state, SecurityMachineState.Enabled)
                                .limit(1).find();

                        try (SecurityMachineClient client = securityMachineMgr.getSecurityMachineClient(self.getModel(), self.getType().toString())) {
                            ErrorableValue<Boolean> response = client.connect(vo.getManagementIp(), vo.getPort(), vo.getPassword());
                            if (!response.isSuccess()) {
                                return ErrorableValue.ofErrorCode(response.error);
                            }

                            ErrorableValue<X509Certificate> certificate = client.genericCertificate(publicKey.getBytes(UTF_8));
                            if (!certificate.isSuccess()) {
                                return ErrorableValue.ofErrorCode(certificate.error);
                            }

                            return ErrorableValue.of(certificate.result.getSubjectDN().toString());
                        } catch (Exception e) {
                            return ErrorableValue.ofErrorCode(operr(String.format("failed to extract subject DN From public key: %s", e.getMessage())));
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "check-all-security-machine-token-exist";

                    private void checkTokenAndRecordFailMachines(List<InfoSecSecurityMachineVO> voList, String checkToken, FlowTrigger trigger) {
                        voList.forEach(vo -> {
                            try (SecurityMachineClient tempClient = securityMachineMgr.getSecurityMachineClient(self.getModel(), self.getType().toString())) {
                                ErrorableValue<Boolean> connectResponse = tempClient.connect(vo.getManagementIp(), vo.getPort(), vo.getPassword());
                                if (!connectResponse.isSuccess()) {
                                    logger.debug(String.format("failed to connect client %s[%s:%d], because %s", vo.getUuid(), vo.getManagementIp(), vo.getPort(), connectResponse.error));
                                    unsyncedMachineList.add(SecurityMachineInventory.valueOf(vo));
                                    return;
                                }
                                //if the detection is successful, it means that the generated token has been synchronized to other secure machines
                                ErrorableValue<Boolean> existRes = tempClient.isSecretKeyExist(checkToken);
                                if (!existRes.isSuccess() || !existRes.result) {
                                    logger.debug(String.format("failed to detect the existence of the token[%s] on the security machine %s[%s:%d], because %s", checkToken, vo.getUuid(), vo.getManagementIp(), vo.getPort(), existRes.error));
                                    unsyncedMachineList.add(SecurityMachineInventory.valueOf(vo));
                                }
                            } catch (Exception ignored) {
                                trigger.fail(operr(ignored.getMessage(), "check-all-security-machine-token-exist failed"));
                            }
                        });

                        trigger.next();
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        checkMachineList = Q.New(InfoSecSecurityMachineVO.class)
                                .eq(InfoSecSecurityMachineVO_.secretResourcePoolUuid, msg.getUuid())
                                .eq(InfoSecSecurityMachineVO_.state, SecurityMachineState.Enabled).list();

                        checkTokenAndRecordFailMachines(checkMachineList, getDbToken(msg.getUuid(), msg.getType()), trigger);
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "change-the-status-of-all-security-machines";

                    @Override
                    public boolean skip(Map data) {
                        return notActivatePoolOperation() || isDryRunTokenCheck() || !unsyncedMachineList.isEmpty();
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        new While<>(checkMachineList).each((vo, whileComplection) -> {
                            ChangeSecurityMachineStatusMsg cmsg = new ChangeSecurityMachineStatusMsg();
                            cmsg.setUuid(vo.getUuid());
                            cmsg.setStatusEvent(SecurityMachineStatusEvent.sync.toString());
                            bus.makeTargetServiceIdByResourceUuid(cmsg, SecurityMachineConstant.SERVICE_ID, cmsg.getSecurityMachineUuid());
                            bus.send(cmsg, new CloudBusCallBack(whileComplection) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (!reply.isSuccess() && reply.getError() != null) {
                                        logger.debug(String.format("failed to security machine status from %s to %s, because %s", vo.getStatus(), SecurityMachineStatusEvent.sync.toString(), reply.getError()));
                                        whileComplection.addError(reply.getError());
                                    }

                                    whileComplection.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (!errorCodeList.getCauses().isEmpty()) {
                                    trigger.fail(errorCodeList.getCauses().get(0));
                                    return;
                                }
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("change-resource-pool-%s-state", msg.getUuid());

                    @Override
                    public boolean skip(Map data) {
                        return notActivatePoolOperation() || isDryRunTokenCheck() || !unsyncedMachineList.isEmpty();
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        ChangeSecretResourcePoolStateMsg cmsg = new ChangeSecretResourcePoolStateMsg();
                        cmsg.setUuid(msg.getUuid());
                        cmsg.setStateEvent(SecretResourcePoolStateEvent.activate.toString());
                        bus.makeTargetServiceIdByResourceUuid(cmsg, SecretResourcePoolConstant.SERVICE_ID, cmsg.getSecretResourcePoolUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                trigger.next();
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success(unsyncedMachineList);
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private void updateAllTokenInDB(String uuid, String activateToken, String dataProtectToken, String hmacToken) {
        InfoSecSecretResourcePoolVO vo = Q.New(InfoSecSecretResourcePoolVO.class)
                .eq(InfoSecSecretResourcePoolVO_.uuid, uuid)
                .find();
        if (activateToken != null) {
            vo.setActivatedToken(activateToken);
        }
        if (dataProtectToken != null) {
            vo.setProtectToken(dataProtectToken);
        }
        if (hmacToken != null) {
            vo.setHmacToken(hmacToken);
        }
        dbf.update(vo);
    }
}
