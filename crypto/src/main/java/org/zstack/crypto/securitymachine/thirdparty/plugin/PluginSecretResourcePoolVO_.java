package org.zstack.crypto.securitymachine.thirdparty.plugin;

import org.zstack.header.securitymachine.SecretResourcePoolVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

/**
 * Created by LiangHanYu on 2022/7/27 16:17
 */
@StaticMetamodel(PluginSecretResourcePoolVO.class)
public class PluginSecretResourcePoolVO_ extends SecretResourcePoolVO_ {
    public static volatile SingularAttribute<PluginSecretResourcePoolVO, String> properties;
    public static volatile SingularAttribute<PluginSecretResourcePoolVO, String> pluginDriverUuid;
}
