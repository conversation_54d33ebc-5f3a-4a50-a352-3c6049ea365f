package org.zstack.crypto.securitymachine.thirdparty.infoSec;

import cn.com.infosec.netsign.agent.GenericCertificate;
import cn.com.infosec.netsign.agent.NetSignAgent;
import cn.com.infosec.netsign.agent.NetSignAgentUtil;
import cn.com.infosec.netsign.agent.PBCAgent2G;
import cn.com.infosec.netsign.newagent.NetSignAgentBasic;
import cn.com.infosec.netsign.newagent.cypher.key.NSKeyFactory;
import cn.com.infosec.netsign.newagent.cypher.key.NSSecretKey;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.zstack.core.db.Q;
import org.zstack.crypto.datatype.CCSCertificateStruct;
import org.zstack.crypto.datatype.MSEnvelope;
import org.zstack.crypto.securitymachine.AttachVerifyPair;
import org.zstack.crypto.securitymachine.SecurityMachineClient;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorableValue;
import org.zstack.header.securitymachine.*;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.Utils;

import java.io.InputStream;
import java.security.cert.X509Certificate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Base64;
import java.util.List;
import java.util.Locale;

import static org.zstack.core.Platform.*;
import static org.zstack.crypto.securitymachine.SecurityMachineErrors.*;

/**
 * Created by LiangHanYu on 2021/10/28 14:07
 */
public class InfoSecClient extends SecurityMachineClient {
    private static final CLogger logger = Utils.getLogger(InfoSecClient.class);

    private PBCAgent2G agent = new PBCAgent2G();
    private NetSignAgentBasic agentBasic = null;
    private static final int SECRET_KEY_NOT_FOUND = -100341;
    private static final int API_PASSWORD_ERROR = -100006 ;
    private static final int DECRYPT_ERROR = -100122;

    private int connectionMode = 2;
    private int connectionTimeOut = 5000;
    private boolean autoCheck = true;
    private long interval = 30000;
    final static String digestAlg = "SM3";
    final static String attachedSignature = "SM3";
    final static String testTokenAlgType = "SM4";
    final static String dataProtectTokenAlgType = "SM4";
    final static String hmacTokenAlgType = "HmacSM3";

    private String testToken = null;
    private String hmacToken = null;
    private String dataProtectToken = null;
    protected String mod = "ECB";

    final static boolean hmacTokenIsCover = true;
    final static int connectionLimit = 3;

    public PBCAgent2G getAgent() {
        return agent;
    }

    public void setAgent(PBCAgent2G agent) {
        this.agent = agent;
    }

    public NetSignAgentBasic getAgentBasic() {
        return agentBasic;
    }

    public void setAgentBasic(NetSignAgentBasic agentBasic) {
        this.agentBasic = agentBasic;
    }

    @Override
    public void setSm4EncryptionMode(String encryptionMode) {
        mod = encryptionMode;
    }

    @Override
    public void setSm4PaddingMode(String paddingMode) {
        padding = paddingMode;
    }

    private void securityMachineConfig() {
        PBCAgent2G.setConnectionMode(connectionMode);
        PBCAgent2G.setIsAutoTestServices(autoCheck);
        PBCAgent2G.setServiceTestInterval(interval);
        agent.setTimeout(connectionTimeOut);
    }

    public void setClientToken(String poolUuid) {
        InfoSecSecretResourcePoolVO poolVO = Q.New(InfoSecSecretResourcePoolVO.class).eq(InfoSecSecretResourcePoolVO_.uuid, poolUuid).find();
        testToken = poolVO.getActivatedToken();
        dataProtectToken = poolVO.getProtectToken();
        hmacToken = poolVO.getHmacToken();
    }

    @Override
    public void close() {
        if (agent != null) {
            agent.closeSignServer();
        }
    }

    @Override
    public ErrorableValue<Boolean> connect(String uuid) {
        //the sdk will filter out machines that cannot provide services, and take one to establish a connection to provide services.
        // so we only need to pass in the security machine

        setClientToken(uuid);

        List<InfoSecSecurityMachineVO> vos = Q.New(InfoSecSecurityMachineVO.class)
                .eq(InfoSecSecurityMachineVO_.secretResourcePoolUuid, uuid)
                .eq(InfoSecSecurityMachineVO_.state, SecurityMachineState.Enabled)
                .eq(InfoSecSecurityMachineVO_.status, SecurityMachineStatus.Synced)
                .limit(connectionLimit)
                .list();
        if (vos.isEmpty()) {
            String errorText = "there is no available security machine in the secretResourcePool[%s]";
            logger.warn(String.format(errorText, uuid));
            return ErrorableValue.ofErrorCode(operr(errorText, uuid));
        }
        securityMachineConfig();

        StringBuilder ips = new StringBuilder();
        StringBuilder ports = new StringBuilder();
        StringBuilder passwords = new StringBuilder();
        vos.forEach(vo -> {
            ips.append(vo.getManagementIp()).append(",");
            ports.append(vo.getPort()).append(",");
            passwords.append(vo.getPassword()).append(",");
        });
        boolean[] results = agent.openSignServer(ips.toString(), ports.toString(), passwords.toString());

        if (logger.isTraceEnabled()) {
            logger.trace(String.format("agent connect ips is %s, ports is %s, passwords is %s", ips, ports, passwords));
        }

        boolean isAllFalse = true;
        for (boolean result : results) {
            if (result) {
                isAllFalse = false;
                break;
            }
        }
        //if all security machines fail to establish a connection, an error will be thrown
        if (isAllFalse) {
            String errorText = "no available security machine found";
            logger.warn(errorText);
            return ErrorableValue.ofErrorCode(err(NO_AVAILABLE_MACHINES, errorText));
        }
        agentBasic = new NetSignAgentBasic(agent);
        return ErrorableValue.of(true);
    }

    @Override
    public ErrorableValue<Boolean> connect(String ip, int port, String password) {
        securityMachineConfig();
        boolean result = agent.openSignServer(ip, port, password);

        if (logger.isTraceEnabled()) {
            logger.trace(String.format("agent connect ip is %s, port is %s, password is %s", ip, port, password));
        }

        if (!result || !agent.heartbeat()) {
            String errorText = "failed to connect SecurityMachine[%s] , err：%s";
            logger.warn(String.format(errorText, ip, agent.getErrorMsg()));
            return ErrorableValue.ofErrorCode(operr(errorText, ip, agent.getErrorMsg()));
        }

        /*
         * if return code is -10006 means password is incorrect
         *
         * use messageDigest() verify password because openSignServer() only estabilish 
         * connection but do not check if password is correct
         */
        agent.messageDigest(digestAlg.getBytes(), digestAlg);
        if (agent.getReturnCode() == API_PASSWORD_ERROR) {
            String errorText = "incorrect password connection failed, code: %s, detail: %s, digestAlg: %s";
            logger.warn(String.format(errorText, agent.getReturnCode(), agent.getErrorMsg(), digestAlg));
            return ErrorableValue.ofErrorCode(operr(errorText, agent.getReturnCode(), agent.getErrorMsg(), digestAlg));
        }
        // regardless of whether the agent can establish a connection, the agentBasic initialization is successful. So only detect agent
        agentBasic = new NetSignAgentBasic(agent);
        return ErrorableValue.of(true);
    }

    @Override
    public String getType() {
        return InfoSecSecurityMachineConstant.SECURITY_MACHINE_TYPE;
    }

    @Override
    public ErrorableValue<Boolean> isSecretKeyExist(String keyLabel) {
        if (keyLabel == null || keyLabel.isEmpty()) {
            String errorText = "secretKey label is empty";
            logger.warn(errorText);
            return ErrorableValue.ofErrorCode(operr(errorText));
        }

        ErrorableValue<NSSecretKey> nsKey = exportSecretKey(keyLabel); // export SM4 key
        if (!nsKey.isSuccess()) {
            logger.warn(String.format("exportSecretKey failed, because %s", nsKey.error));
            return ErrorableValue.ofErrorCode(nsKey.error);
        }

        return ErrorableValue.of(nsKey.result != null);
    }

    public int getConnectionMode() {
        return connectionMode;
    }

    public void setConnectionMode(int connectionMode) {
        this.connectionMode = connectionMode;
        PBCAgent2G.setConnectionMode(connectionMode);
    }

    public int getConnectionTimeOut() {
        return connectionTimeOut;
    }

    public void setConnectionTimeOut(int connectionTimeOut) {
        this.connectionTimeOut = connectionTimeOut;
        agent.setTimeout(connectionTimeOut);
    }

    public boolean isAutoCheck() {
        return autoCheck;
    }

    public void setAutoCheck(boolean autoCheck) {
        this.autoCheck = autoCheck;
        PBCAgent2G.setIsAutoTestServices(autoCheck);
    }

    public long getInterval() {
        return interval;
    }

    public void setInterval(long interval) {
        this.interval = interval;
        PBCAgent2G.setServiceTestInterval(interval);
    }

    @Override
    public ErrorableValue<String> attachedSignature(String input, String certId) {
        if (input == null || certId == null) {
            String errorText = "attachedSignature input[%s] or certId[%s] is null";
            logger.warn(String.format(errorText, input, certId));
            return ErrorableValue.ofErrorCode(operr(errorText, input, certId));
        }

        byte[] bytes = input.getBytes();
        String attachedResult = agent.attachedSignature(bytes, attachedSignature, certId, false);
        if (agent.getReturnCode() < 0) {
            String errorText = "attached Signature fail, code: %s, detail: %s";
            logger.warn(String.format(errorText, agent.getReturnCode(), agent.getErrorMsg()));
            return ErrorableValue.ofErrorCode(operr(errorText, agent.getReturnCode(), agent.getErrorMsg()));
        }
        return ErrorableValue.of(attachedResult);
    }

    @Override
    public ErrorableValue<AttachVerifyPair> attachedVerify(String input) {
        if (input == null) {
            logger.warn(attachedVerifyNullError);
            return ErrorableValue.ofErrorCode(operr(attachedVerifyNullError));
        }

        byte[][] netSignResult = agent.attachedVerify(input);
        int code = agent.getReturnCode();
        if (code < 0) {
            String errorText = "attached Verify fail, agent return code: %s, detail: %s";
            logger.warn(String.format(errorText, code, agent.getErrorMsg()));
            return ErrorableValue.ofErrorCode(operr(errorText, code, agent.getErrorMsg()));
        }
        if (netSignResult == null || netSignResult.length != 2) {
            String errorText = "attached Verify fail, invalid net sign result";
            logger.warn(errorText);
            return ErrorableValue.ofErrorCode(operr(errorText));
        }
        return AttachVerifyPair.create(netSignResult[0], netSignResult[1]);
    }

    @Override
    public ErrorableValue<String> digest(String input) {
        if (input == null) {
            logger.warn(digestNullError);
            return ErrorableValue.ofErrorCode(operr(digestNullError));
        }

        byte[] bytes = input.getBytes();
        String result = agent.messageDigest(bytes, digestAlg);//摘要算法:SHA1/SHA256/SM3
        if (agent.getReturnCode() < 0) {
            String errorText = "digest encrypt failed, code: %s, detail: %s, digestAlg: %s";
            logger.warn(String.format(errorText, agent.getReturnCode(), agent.getErrorMsg(), digestAlg));
            return ErrorableValue.ofErrorCode(operr(errorText, agent.getReturnCode(), agent.getErrorMsg(), digestAlg));
        }
        return ErrorableValue.of(result);
    }

    @Override
    public ErrorableValue<String> digestLocal(String input) {
        if (input == null) {
            logger.warn(digestLocalNullError);
            return ErrorableValue.ofErrorCode(operr(digestLocalNullError));
        }

        byte[] bytes = input.getBytes();
        byte[] result = NetSignAgentUtil.digest(bytes, digestAlg);
        return ErrorableValue.of(cn.com.infosec.netsign.base.util.Utils.toHexString(result));
    }

    @Override
    public ErrorableValue<X509Certificate> genericCertificate(byte[] cert) {
        if (cert == null) {
            logger.warn(certNullError);
            return ErrorableValue.ofErrorCode(operr(certNullError));
        }

        try {
            X509Certificate certification = NetSignAgent.generateCertificate(cert);
            return ErrorableValue.of(certification);
        } catch (Exception e) {
            logger.warn(String.format("generate certificate failed, detail: %s", e.getMessage()));
            return ErrorableValue.ofErrorCode(operr("generate certificate failed"));
        }
    }

    @Override
    public ErrorableValue<String> sm4Encrypt(String plain) {
        if (plain == null) {
            logger.warn(sm4EncryptNullError);
            return ErrorableValue.ofErrorCode(operr(sm4EncryptNullError));
        }

        byte[] result = agent.symmEncrypt(plain.getBytes(), dataProtectToken, String.format("/%s/%s", mod, padding));
        if (agent.getReturnCode() < 0) {
            String errorText = "sm4 encrypt failed, code: %s, detail: %s";
            logger.warn(String.format(errorText, agent.getReturnCode(), agent.getErrorMsg()));
            return ErrorableValue.ofErrorCode(operr(errorText, agent.getReturnCode(), agent.getErrorMsg()));
        }
        if (logger.isTraceEnabled()) {
            logger.trace(String.format("converted value on sm4Encrypt: %s -> %s", plain, cn.com.infosec.netsign.base.util.Utils.toHexString(result)));
        }
        return ErrorableValue.of(cn.com.infosec.netsign.base.util.Utils.toHexString(result));
    }

    @Override
    public ErrorableValue<String> sm4Decrypt(String plain) {
        if (plain == null) {
            logger.warn(sm4DecryptNullError);
            return ErrorableValue.ofErrorCode(operr(sm4DecryptNullError));
        }

        byte[] result = agent.symmDecrypt(cn.com.infosec.netsign.base.util.Utils.hex2byte(plain), dataProtectToken, String.format("/%s/%s", mod, padding));
        if (agent.getReturnCode() == DECRYPT_ERROR) {
            logger.trace(String.format("decrypt value error, return original plain: %s, %s", plain, cn.com.infosec.netsign.base.util.Utils.toHexString(result)));
            return ErrorableValue.of(new String(cn.com.infosec.netsign.base.util.Utils.hex2byte(plain)));
        }
        if (agent.getReturnCode() < 0) {
            String errorText = "sm4 decrypt failed, code: %s, detail: %s";
            logger.warn(String.format(errorText, agent.getReturnCode(), agent.getErrorMsg()));
            return ErrorableValue.ofErrorCode(operr(errorText, agent.getReturnCode(), agent.getErrorMsg()));
        }
        if (logger.isTraceEnabled()) {
            logger.trace(String.format("converted value on sm4Decrypt: %s -> %s", plain, cn.com.infosec.netsign.base.util.Utils.toHexString(result)));
        }
        return ErrorableValue.of(new String(result));
    }

    @Override
    public ErrorableValue<String> hmac(String plain) {
        if (plain == null) {
            logger.warn(hmacNullError);
            return ErrorableValue.ofErrorCode(operr(hmacNullError));
        }

        byte[] result = agentBasic.INSMHmac(plain.getBytes(), getInnerSecretKey(hmacToken));
        if (agentBasic.INSMGetReturnCode() < 0) {
            String errorText = "hmac encrypt failed, code: %s, detail: %s";
            logger.warn(String.format(errorText, agentBasic.INSMGetReturnCode(), agentBasic.INSMGetErrMsg()));
            return ErrorableValue.ofErrorCode(operr(errorText, agentBasic.INSMGetReturnCode(), agentBasic.INSMGetErrMsg()));
        }
        if (logger.isTraceEnabled()) {
            logger.trace(String.format("converted value on hmac: %s -> %s", plain, cn.com.infosec.netsign.base.util.Utils.toHexString(result)));
        }
        return ErrorableValue.of(cn.com.infosec.netsign.base.util.Utils.toHexString(result));
    }

    @Override
    public ErrorableValue<byte[]> largeFileHmac(InputStream stream) {
        byte[] result = agentBasic.INSMLargeFileHmac(stream, getInnerSecretKey(hmacToken));
        if (agentBasic.INSMGetReturnCode() < 0) {
            ErrorableValue.ofErrorCode(operr("large file hmac encrypt failed, code: %s, detail: %s", agentBasic.INSMGetReturnCode(), agentBasic.INSMGetErrMsg()));
        }
        return ErrorableValue.of(result);
    }


    @Override
    public ErrorableValue<String> generateToken(String tokenName, String algType) {
        boolean result = agent.generateWorkKey(tokenName, algType, true);
        if (!result) {
            String errorText = "generate token failed, code: %s, detail: %s, tokenType: %s";
            return ErrorableValue.ofErrorCode(operr(errorText, agent.getReturnCode(), agent.getErrorMsg(), algType));
        }
        return ErrorableValue.of(tokenName);
    }

    @Override
    public ErrorableValue<String> generateSm4Token(String tokenName) {
        return generateToken(tokenName, testTokenAlgType);
    }

    @Override
    public ErrorableValue<String> generateDataProtectToken(String tokenName) {
        ErrorableValue<NSSecretKey> nsKey = exportSecretKey(tokenName);//check if the SM4 key exists, create it if it does not exist
        if (nsKey.result != null && dataProtectTokenAlgType.equals(nsKey.result.getKeyAlg())) {
            logger.info("It is detected that the dataProtect key already exists, no need to generate");
            dataProtectToken = tokenName;
            return ErrorableValue.of(tokenName);
        }
        logger.info(String.format("generate data protection key %s", tokenName));
        dataProtectToken = tokenName;
        return generateToken(tokenName, dataProtectTokenAlgType);
    }

    @Override
    public ErrorableValue<String> generateHmacToken(String tokenName) {
        ErrorableValue<NSSecretKey> hmacKey = exportSecretKey(tokenName); // check if the hmac key exists, create it if it does not exist
        if (hmacKey.result != null && hmacTokenAlgType.equals(hmacKey.result.getKeyAlg())) {
            logger.info(String.format("It is detected that the hmac key %s already exists, no need to generate", tokenName));
            hmacToken = tokenName;
            return ErrorableValue.of(tokenName);
        }

        //dataProtectToken is generated before the hmacToken, so dataProtectToken must exist, otherwise an error occurs
        ErrorableValue<NSSecretKey> nsKey = exportSecretKey(dataProtectToken);
        if (nsKey.result == null) {
            return ErrorableValue.ofErrorCode((nsKey.error != null) ?
                    operr(nsKey.error, "failed to export secret key") :
                    operr("failed to find secret key"));
        }

        NSSecretKey extKey;
        try {
            byte[] keyData = cn.com.infosec.netsign.base.util.Utils.hex2byte(cn.com.infosec.netsign.base.util.Utils.toHexString(nsKey.result.getKeyData()));
            extKey = getExtSecretKey(hmacTokenAlgType, keyData); // the SM4 type key is converted to the hmac type key
        } catch (RuntimeException e) {
            return ErrorableValue.ofErrorCode(operr("failed to parse secret key, error: %s", e.getMessage()));
        }

        ErrorCode errorCode = importSecretKey(tokenName, extKey, hmacTokenIsCover); // upload HMAC key
        if (errorCode != null) {
            logger.warn(String.format("failed to upload the hmac key, error: %s", errorCode.getRootCauseDetails()));
            return ErrorableValue.ofErrorCode(errorCode);
        }
        logger.info("Successfully generated hmac key");
        hmacToken = tokenName;
        return ErrorableValue.of(tokenName);
    }

    @Override
    public ErrorableValue<String> generateToken(String keyType) {
        if (SecurityMachineKeyType.Active.toString().equals(keyType)) {
            return generateSm4Token(testToken);
        } else if (SecurityMachineKeyType.Protect.toString().equals(keyType)) {
            return generateDataProtectToken(dataProtectToken);
        } else if (SecurityMachineKeyType.Hmac.toString().equals(keyType)) {
            return generateHmacToken(hmacToken);
        }
        return null;
    }


    @Override
    public ErrorableValue<String> backupToken() {
        //the current client's SDK does not currently support only backup but no token generation
        return null;
    }

    @Override
    public ErrorableValue<MSEnvelope> decryptMSEnvelope(String cipherText, String encryptSubjectDN) {
        if (cipherText == null) {
            return ErrorableValue.ofErrorCode(operr("cipherText can not be null"));
        }
        if (encryptSubjectDN == null) {
            return ErrorableValue.ofErrorCode(operr("encryptSubjectDN can not be null"));
        }

        Object[] objects = agent.decryptMSEnvelope(cipherText, encryptSubjectDN);
        if (objects == null) {
            return ErrorableValue.ofErrorCode(operr("failed to parse MS Envelope"));
        }

        MSEnvelope result = new MSEnvelope();
        result.plainBytes = Base64.getDecoder().decode(objects[0].toString());
        if (objects[1] instanceof GenericCertificate) {
            result.encryptCertificate = parseToCCSCertificate((GenericCertificate) objects[1]);
        }
        if (objects[2] instanceof GenericCertificate) {
            result.signCertificate = parseToCCSCertificate((GenericCertificate) objects[2]);
        }

        return ErrorableValue.of(result);
    }

    /**
     * @return
     * if secret key not found, return {@link ErrorableValue#result} is null
     */
    private ErrorableValue<NSSecretKey> exportSecretKey(String keyLabel) {
        NSSecretKey nsKey;
        try {
            nsKey = agentBasic.INSMExportSecretKey("", keyLabel, "binary", null, null);
        } catch (RuntimeException e) {
            logger.warn(String.format("export secretKey fail, detail: %s", e.getMessage()));
            return ErrorableValue.ofErrorCode(operr("failed to export secret key"));
        }
        if (agentBasic.INSMGetReturnCode() == SECRET_KEY_NOT_FOUND) {
            return ErrorableValue.of(null); // not exist, return result = null
        }
        if (agentBasic.INSMGetReturnCode() < 0 || nsKey == null) {
            String errorText = "export secret key[%s] fail, code: %s, detail: %s";
            logger.warn(String.format(errorText, keyLabel, agentBasic.INSMGetReturnCode(), agentBasic.INSMGetErrMsg()));
            return ErrorableValue.ofErrorCode(operr(errorText, keyLabel, agentBasic.INSMGetReturnCode(), agentBasic.INSMGetErrMsg()));
        }
        return ErrorableValue.of(nsKey);
    }

    private ErrorCode importSecretKey(String keyLabel, NSSecretKey key, boolean isCover) {
        boolean result = agentBasic.INSMImportSecretKey("default", keyLabel, key, isCover);
        if (!result || agentBasic.INSMGetReturnCode() < 0) {
            logger.warn(String.format("import secretKey fail, code: %s, detail: %s", agentBasic.INSMGetReturnCode(), agentBasic.INSMGetErrMsg()));
            return operr("import secret key fail");
        }
        return null;
    }

    private NSSecretKey getExtSecretKey(String keyAlg, byte[] keyData) {
        return NSKeyFactory.getExtSecretKey(keyAlg, "binary", keyData, null, null);
    }

    private NSSecretKey getInnerSecretKey(String keyLabel) {
        return NSKeyFactory.getInnerSecretKey("default", keyLabel);
    }

    private static CCSCertificateStruct parseToCCSCertificate(GenericCertificate raw) {
        CCSCertificateStruct struct = new CCSCertificateStruct();
        struct.setAlgorithm("unknown");
        struct.setIssuerDN(raw.getIssuer_subject());
        struct.setSubjectDN(raw.getSubject());
        struct.setSerNumber(raw.getSer_number());
        struct.setEffectiveTime(parseTime(raw.getStart_time()));
        struct.setExpirationTime(parseTime(raw.getEnd_time()));
        return struct;
    }

    private static long parseTime(String timeText) {
        if (StringUtils.isEmpty(timeText)) {
            return 0;
        }
        if (NumberUtils.isDigits(timeText)) {
            return NumberUtils.toLong(timeText);
        }
        DateTimeFormatter[] formatters = new DateTimeFormatter[] {
                DateTimeFormatter.ofPattern("EE MMM dd HH:mm:ss zzz yyyy", Locale.US), // Wed Nov 02 10:31:22 HKT 2022
                DateTimeFormatter.ISO_OFFSET_DATE_TIME,
                DateTimeFormatter.ISO_INSTANT,
        };
        for (DateTimeFormatter formatter : formatters) {
            try {
                ZonedDateTime time = ZonedDateTime.parse(timeText, formatter);
                return time.toInstant().toEpochMilli();
            } catch (DateTimeParseException ignored) {}
        }
        logger.warn("failed to parse time text: " + timeText);
        return 0;
    }
}
