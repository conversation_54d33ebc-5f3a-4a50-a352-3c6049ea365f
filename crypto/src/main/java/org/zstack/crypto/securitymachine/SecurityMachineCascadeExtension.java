package org.zstack.crypto.securitymachine;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusListCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SimpleQuery;
import org.zstack.header.core.Completion;
import org.zstack.header.message.MessageReply;
import org.zstack.header.securitymachine.*;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by LiangHanYu on 2021/11/17 14:37
 */
public class SecurityMachineCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(SecurityMachineCascadeExtension.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    private static final String NAME = SecurityMachineVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }

    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }


    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        dbf.eoCleanup(SecurityMachineVO.class);
        completion.success();
    }

    private void handleDeletion(CascadeAction action, Completion completion) {
        final List<SecurityMachineInventory> sinvs = securityMachineFromAction(action);
        if (sinvs == null) {
            completion.success();
            return;
        }

        List<SecurityMachineDeletionMsg> msgs = new ArrayList<SecurityMachineDeletionMsg>();
        for (SecurityMachineInventory sinv : sinvs) {
            SecurityMachineDeletionMsg msg = new SecurityMachineDeletionMsg();
            msg.setForceDelete(action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE));
            msg.setSecurityMachineUuid(sinv.getUuid());
            bus.makeTargetServiceIdByResourceUuid(msg, SecurityMachineConstant.SERVICE_ID, sinv.getUuid());
            msgs.add(msg);
        }

        bus.send(msgs, new CloudBusListCallBack(completion) {
            @Override
            public void run(List<MessageReply> replies) {
                if (!action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                    for (MessageReply r : replies) {
                        if (!r.isSuccess()) {
                            completion.fail(r.getError());
                            return;
                        }
                    }
                }

                List<String> uuids = new ArrayList<String>();
                for (MessageReply r : replies) {
                    SecurityMachineInventory inv = sinvs.get(replies.indexOf(r));
                    uuids.add(inv.getUuid());
                    logger.debug(String.format("deleted SecurityMachine[uuid:%s, name:%s]", inv.getUuid(), inv.getName()));
                }

                dbf.removeByPrimaryKeys(uuids, SecurityMachineVO.class);
                completion.success();
            }
        });
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(SecretResourcePoolVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }


    private List<SecurityMachineInventory> securityMachineFromAction(CascadeAction action) {
        List<SecurityMachineInventory> ret = null;
        if (SecretResourcePoolVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<SecretResourcePoolInventory> secretResourcePoolInventories = action.getParentIssuerContext();
            List<String> suuids = CollectionUtils.transformToList(secretResourcePoolInventories, new Function<String, SecretResourcePoolInventory>() {
                @Override
                public String call(SecretResourcePoolInventory arg) {
                    return arg.getUuid();
                }
            });

            SimpleQuery<SecurityMachineVO> q = dbf.createQuery(SecurityMachineVO.class);
            q.add(SecurityMachineVO_.secretResourcePoolUuid, SimpleQuery.Op.IN, suuids);
            List<SecurityMachineVO> svos = q.list();
            if (!svos.isEmpty()) {
                ret = SecurityMachineInventory.valueOf(svos);
            }
        } else if (NAME.equals(action.getParentIssuer())) {
            ret = action.getParentIssuerContext();
        }

        return ret;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<SecurityMachineInventory> ctx = securityMachineFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}
