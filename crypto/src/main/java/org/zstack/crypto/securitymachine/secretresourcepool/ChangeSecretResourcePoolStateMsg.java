package org.zstack.crypto.securitymachine.secretresourcepool;

import org.zstack.header.message.NeedReplyMessage;

/**
 * Created by LiangHanYu on 2021/11/25 20:11
 */
public class ChangeSecretResourcePoolStateMsg extends NeedReplyMessage implements SecretResourcePoolMessage {
    private String uuid;

    private String stateEvent;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getStateEvent() {
        return stateEvent;
    }

    public void setStateEvent(String stateEvent) {
        this.stateEvent = stateEvent;
    }

    @Override
    public String getSecretResourcePoolUuid() {
        return uuid;
    }
}
