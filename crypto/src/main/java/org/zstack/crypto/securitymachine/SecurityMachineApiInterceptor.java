package org.zstack.crypto.securitymachine;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.Q;
import org.zstack.core.encrypt.EncryptType;
import org.zstack.crypto.securitymachine.api.securitymachine.APIAddSecurityMachineMsg;
import org.zstack.crypto.securitymachine.api.securitymachine.APIChangeSecurityMachineStateMsg;
import org.zstack.crypto.securitymachine.api.securitymachine.APIDeleteSecurityMachineMsg;
import org.zstack.crypto.securitymachine.api.securitymachine.APISecurityMachineDetectSyncMsg;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.APIMessage;
import org.zstack.header.securitymachine.*;
import org.zstack.utils.network.NetworkUtils;

import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.err;

/**
 *  * Created by LiangHanYu on 2021/11/19 16:55
 *   */
public class SecurityMachineApiInterceptor implements ApiMessageInterceptor {
    @Autowired
    protected SecurityMachineManager securityMachineMgr;
    @Autowired
    private CloudBus bus;

    private void setServiceId(APIMessage msg) {
        if (msg instanceof SecurityMachineMessage) {
            SecurityMachineMessage smsg = (SecurityMachineMessage) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, SecurityMachineConstant.SERVICE_ID, smsg.getSecurityMachineUuid());
        }
    }


    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIDeleteSecurityMachineMsg) {
            validate((APIDeleteSecurityMachineMsg) msg);
        } else if (msg instanceof APISecurityMachineEncryptMsg) {
            validate((APISecurityMachineEncryptMsg) msg);
        } else if (msg instanceof APIAddSecurityMachineMsg) {
            validate((APIAddSecurityMachineMsg) msg);
        } else if (msg instanceof APIChangeSecurityMachineStateMsg) {
            validate((APIChangeSecurityMachineStateMsg) msg);
        } else if (msg instanceof APISecurityMachineDetectSyncMsg) {
            validate((APISecurityMachineDetectSyncMsg) msg);
        }

        setServiceId(msg);
        return msg;
    }

    private void validate(APISecurityMachineDetectSyncMsg msg) {
        if (!Q.New(SecurityMachineVO.class).eq(SecretResourcePoolVO_.uuid, msg.getSecurityMachineUuid()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("the security machine [%s] does not exist", msg.getSecurityMachineUuid()));
        }
    }

    private void validate(APIChangeSecurityMachineStateMsg msg) {
        if (SecurityMachineHelper.skipChangeStateValidate(msg.getSecurityMachineUuid(), msg.getStateEvent())) {
            return;
        }

        ErrorCode error = SecurityMachineHelper.changeStateCanBeOperated(msg.getSecurityMachineUuid(), msg.getStateEvent());
        if (error != null) {
            throw new ApiMessageInterceptionException(error);
        }

        error = SecurityMachineHelper.checkEnableCryptoCanChangeState(msg.getSecurityMachineUuid(), msg.getStateEvent());
        if (error != null) {
            throw new ApiMessageInterceptionException(error);
        }
    }

    private void validate(APIAddSecurityMachineMsg msg) {
        if (Q.New(SecurityMachineVO.class).eq(SecurityMachineVO_.managementIp, msg.getManagementIp()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("there has been a security machine having managementIp[%s]", msg.getManagementIp()));
        }

        if (!NetworkUtils.isIpv4Address(msg.getManagementIp())) {
            throw new ApiMessageInterceptionException(argerr("managementIp[%s] is not in IPV4 format", msg.getManagementIp()));
        }

        SecurityMachineClientFactory factory = securityMachineMgr.getSecurityMachineClientFactory(msg.getModel());
        ErrorCode error = factory.testConnection(msg);
        if (error != null) {
            throw new ApiMessageInterceptionException(argerr("failed to connect to the security machine %s[%s], because %s", msg.getName(), msg.getManagementIp(), error));
        }
    }

    private void validate(APIDeleteSecurityMachineMsg msg) {
        if (!SecurityMachineHelper.isCryptoFunctionEnable()) {
            return;
        }

        ErrorCode error = SecurityMachineHelper.checkCryptoFunctionSetupTheResourcePool();
        if (error != null) {
            throw new ApiMessageInterceptionException(error);
        }

        String poolForAuth = SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_AUTH_LOGIN.value(String.class);
        if (!SecurityMachineHelper.securityMachineInTheResourcePool(msg.getSecurityMachineUuid(), poolForAuth)) {
            return;
        }

        if (!SecurityMachineHelper.existOtherSecurityMachine(poolForAuth, msg.getSecurityMachineUuid())) {
            throw new ApiMessageInterceptionException(argerr("after the crypto function is enabled, at least one security machine should be reserved in the corresponding resource pool", msg.getSecurityMachineUuid()));
        }
    }

    private void validate(APISecurityMachineEncryptMsg msg) {
        String poolForProtect = SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.value(String.class);
        if (poolForProtect.equals(SecurityMachineGlobalConfig.RESOURCE_POOL_UUID_FOR_DATA_PROTECT.getDefaultValue())) {
            throw new ApiMessageInterceptionException(err(SecurityMachineErrors.CRYPTO_DISABLE_ERROR, "please enable data protection function and try again"));
        }

        if (!EnumUtils.isValidEnum(EncryptType.class, msg.getAlgType())) {
            throw new ApiMessageInterceptionException(argerr("invalid algType %s, supported types: %s.", msg.getAlgType(), StringUtils.join(EncryptType.values(), ',')));
        }

        if (!Q.New(SecretResourcePoolVO.class).eq(SecretResourcePoolVO_.uuid, poolForProtect).isExists()) {
            throw new ApiMessageInterceptionException(argerr("the resource pool[%s] specified by data protection does not exist", poolForProtect));
        }
    }
}
