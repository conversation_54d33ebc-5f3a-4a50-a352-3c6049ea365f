package org.zstack.crypto.securitymachine.secretresourcepool;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusListCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SimpleQuery;
import org.zstack.header.securitymachine.SecretResourcePoolConstant;
import org.zstack.header.securitymachine.SecretResourcePoolInventory;
import org.zstack.header.securitymachine.SecretResourcePoolVO;
import org.zstack.header.securitymachine.SecretResourcePoolVO_;
import org.zstack.header.core.Completion;
import org.zstack.header.message.MessageReply;
import org.zstack.header.zone.ZoneInventory;
import org.zstack.header.zone.ZoneVO;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by LiangHanYu on 2021/11/8 17:16
 */
public class SecretResourcePoolCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(SecretResourcePoolCascadeExtension.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    private static final String NAME = SecretResourcePoolVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }

    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        dbf.eoCleanup(SecretResourcePoolVO.class);
        completion.success();
    }

    private void handleDeletion(CascadeAction action, Completion completion) {
        final List<SecretResourcePoolInventory> sinvs = secretResourcePoolFromAction(action);
        if (sinvs == null) {
            completion.success();
            return;
        }

        List<SecretResourcePoolDeletionMsg> msgs = new ArrayList<SecretResourcePoolDeletionMsg>();
        for (SecretResourcePoolInventory sinv : sinvs) {
            SecretResourcePoolDeletionMsg msg = new SecretResourcePoolDeletionMsg();
            msg.setForceDelete(action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE));
            msg.setSecretResourcePoolUuid(sinv.getUuid());
            bus.makeTargetServiceIdByResourceUuid(msg, SecretResourcePoolConstant.SERVICE_ID, sinv.getUuid());
            msgs.add(msg);
        }

        bus.send(msgs, new CloudBusListCallBack(completion) {
            @Override
            public void run(List<MessageReply> replies) {
                if (!action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                    for (MessageReply r : replies) {
                        if (!r.isSuccess()) {
                            completion.fail(r.getError());
                            return;
                        }
                    }
                }

                List<String> uuids = new ArrayList<String>();
                for (MessageReply r : replies) {
                    SecretResourcePoolInventory inv = sinvs.get(replies.indexOf(r));
                    uuids.add(inv.getUuid());
                    logger.debug(String.format("deleted secretResourcePool[uuid:%s, name:%s]", inv.getUuid(), inv.getName()));
                }

                dbf.removeByPrimaryKeys(uuids, SecretResourcePoolVO.class);
                completion.success();
            }
        });
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(ZoneVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }


    private List<SecretResourcePoolInventory> secretResourcePoolFromAction(CascadeAction action) {
        List<SecretResourcePoolInventory> ret = null;
        if (ZoneVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<ZoneInventory> zones = action.getParentIssuerContext();
            List<String> zuuids = CollectionUtils.transformToList(zones, new Function<String, ZoneInventory>() {
                @Override
                public String call(ZoneInventory arg) {
                    return arg.getUuid();
                }
            });

            SimpleQuery<SecretResourcePoolVO> q = dbf.createQuery(SecretResourcePoolVO.class);
            q.add(SecretResourcePoolVO_.zoneUuid, SimpleQuery.Op.IN, zuuids);
            List<SecretResourcePoolVO> cvos = q.list();
            if (!cvos.isEmpty()) {
                ret = SecretResourcePoolInventory.valueOf(cvos);
            }
        } else if (NAME.equals(action.getParentIssuer())) {
            ret = action.getParentIssuerContext();
        }

        return ret;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<SecretResourcePoolInventory> ctx = secretResourcePoolFromAction(action);
            if (ctx != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(ctx);
            }
        }

        return null;
    }
}
