package org.zstack.crypto.securitymachine.thirdparty.sansec;

import org.zstack.header.securitymachine.SecretResourcePoolVO;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.PrimaryKeyJoinColumn;
import javax.persistence.Table;

/**
 * Created by LiangHanYu on 2021/10/28 11:25
 */
@Entity
@Table
@PrimaryKeyJoinColumn(name = "uuid", referencedColumnName = "uuid")
public class SanSecSecretResourcePoolVO extends SecretResourcePoolVO {
    @Column
    private Integer keyIndex;
    @Column
    private String managementIp;
    @Column
    private Integer port;
    @Column
    private String username;
    @Column
    private String password;
    @Column
    private String sm3Key;
    @Column
    private String sm4Key;

    public SanSecSecretResourcePoolVO() {
    }

    public SanSecSecretResourcePoolVO(SecretResourcePoolVO vo) {
        super(vo);
    }

    public Integer getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(Integer keyIndex) {
        this.keyIndex = keyIndex;
    }

    public String getManagementIp() {
        return managementIp;
    }

    public void setManagementIp(String managementIp) {
        this.managementIp = managementIp;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSm3Key() {
        return sm3Key;
    }

    public void setSm3Key(String sm3Key) {
        this.sm3Key = sm3Key;
    }

    public String getSm4Key() {
        return sm4Key;
    }

    public void setSm4Key(String sm4Key) {
        this.sm4Key = sm4Key;
    }
}