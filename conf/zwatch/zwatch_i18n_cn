ZStack/BackupStorage/BackupStorageDisconnected: 镜像服务器失联
ZStack/BackupStorage/BackupStorageConnected: 镜像服务器已连接
ZStack/SNS/SendSmsFailed: 短信发送失败
ZStack/VCenter/VCenterHostWrongDateTime: vCenter物理机时间异常
ZStack/VCenter/VCenterResourceEvent: vCenter资源事件
ZStack/Scheduler/VolumeSnapshotScheduler: 云盘快照定时任务
ZStack/Scheduler/VolumeBackupScheduler: 云盘备份定时任务
ZStack/Scheduler/VMStartScheduler: 云主机启动定时任务
ZStack/Scheduler/VMStopScheduler: 云主机停止定时任务
ZStack/Scheduler/VMRebootScheduler: 云主机重启定时任务
ZStack/Scheduler/DatabaseBackupScheduler: 数据库备份定时任务
ZStack/Scheduler/JobGroupFailure: 定时任务组结果失败
ZStack/Host/HostStatusChanged: 物理机状态发生变化
ZStack/Host/HostHardwareChanged: 物理机硬件发生变化
ZStack/Host/HostUnknownVMDetected: 物理机上发现了未受系统管控的云主机
ZStack/Host/HostVMOperateErrorDetected: 对物理机上云主机操作失败
ZStack/Host/HostDisconnected: 物理机失联
ZStack/Host/HostCheckAlive: 物理机连通性检测
ZStack/Host/VMHAHostSelfFencerTriggered: 物理机上高可用云主机自我仲裁已触发
ZStack/Host/FaultMountPointOnHost: 物理机上挂载路径错误
ZStack/Host/HostConnected: 物理机已连接
ZStack/Host/HostPhysicalNicStatusUp: 物理机网络链路上线
ZStack/Host/HostPhysicalNicStatusDown: 物理机网络链路离线
ZStack/Cluster/ClusterQemuVersionMismatch: 集群内物理机Qemu版本未更新
ZStack/XDragonHost/HostStatusChanged: 神龙物理机状态发生变化
ZStack/XDragonHost/HostUnknownVMDetected: 神龙物理机上发现了未受系统管控的云主机
ZStack/XDragonHost/HostVMOperateErrorDetected: 对神龙物理机上云主机操作失败
ZStack/XDragonHost/HostDisconnected: 神龙物理机失联
ZStack/XDragonHost/HostCheckAlive: 神龙物理机连通性检测
ZStack/XDragonHost/VMHAHostSelfFencerTriggered: 神龙物理机上高可用云主机自我仲裁已触发
ZStack/XDragonHost/FaultMountPointOnHost: 神龙物理机上挂载路径错误
ZStack/XDragonHost/HostConnected: 神龙物理机已连接
ZStack/XDragonHost/HostPhysicalNicStatusUp: 神龙物理机网络链路上线
ZStack/XDragonHost/HostPhysicalNicStatusDown: 神龙物理机网络链路离线
ZStack/KVMHost/HostStatusChanged: KVM物理机状态发生变化
ZStack/KVMHost/HostUnknownVMDetected: KVM物理机上发现了未受系统管控的云主机
ZStack/KVMHost/HostVMOperateErrorDetected: 对KVM物理机上云主机操作失败
ZStack/KVMHost/HostDisconnected: KVM物理机失联
ZStack/KVMHost/HostCheckAlive: KVM物理机连通性检测
ZStack/KVMHost/VMHAHostSelfFencerTriggered: KVM物理机上高可用云主机自我仲裁已触发
ZStack/KVMHost/FaultMountPointOnHost: KVM物理机上挂载路径错误
ZStack/KVMHost/HostConnected: KVM物理机已连接
ZStack/KVMHost/HostPhysicalNicStatusUp: KVM物理机网络链路上线
ZStack/KVMHost/HostPhysicalNicStatusDown: KVM物理机网络链路离线
ZStack/ESXHost/HostStatusChanged: ESX物理机状态发生变化
ZStack/ESXHost/HostUnknownVMDetected: ESX物理机上发现了未受系统管控的云主机
ZStack/ESXHost/HostVMOperateErrorDetected: 对ESX物理机上云主机操作失败
ZStack/ESXHost/HostDisconnected: ESX物理机失联
ZStack/ESXHost/HostCheckAlive: ESX物理机连通性检测
ZStack/ESXHost/VMHAHostSelfFencerTriggered: ESX物理机上高可用云主机自我仲裁已触发
ZStack/ESXHost/FaultMountPointOnHost: ESX物理机上挂载路径错误
ZStack/ESXHost/HostConnected: ESX物理机已连接
ZStack/ESXHost/HostPhysicalNicStatusUp: ESX物理机网络链路上线
ZStack/ESXHost/HostPhysicalNicStatusDown: ESX物理机网络链路离线
ZStack/BareMetal2Gateway/HostStatusChanged: 弹性裸金属网关状态发生变化
ZStack/BareMetal2Gateway/HostUnknownVMDetected: 弹性裸金属网关上发现了未受系统管控的云主机
ZStack/BareMetal2Gateway/HostVMOperateErrorDetected: 对弹性裸金属网关上云主机操作失败
ZStack/BareMetal2Gateway/HostDisconnected: 弹性裸金属网关失联
ZStack/BareMetal2Gateway/HostCheckAlive: 弹性裸金属网关连通性检测
ZStack/BareMetal2Gateway/VMHAHostSelfFencerTriggered: 弹性裸金属网关上高可用云主机自我仲裁已触发
ZStack/BareMetal2Gateway/FaultMountPointOnHost: 弹性裸金属网关上挂载路径错误
ZStack/BareMetal2Gateway/HostConnected: 弹性裸金属网关已连接
ZStack/BareMetal2Gateway/HostPhysicalNicStatusUp: 弹性裸金属网关网络链路上线
ZStack/BareMetal2Gateway/HostPhysicalNicStatusDown: 弹性裸金属网关网络链路离线
ZStack/PrimaryStorage/PrimaryStorageDisconnected: 主存储失联
ZStack/PrimaryStorage/PrimaryStorageConnected: 主存储已连接
ZStack/PrimaryStorage/PrimaryStorageHostDisconnected: 主存储到物理机连接状态检查失败
ZStack/Identity/SessionForceLogout: Session强制退出
ZStack/MN/ManagementNodeLeft: 管理节点失联
ZStack/MN/ManagementNodeJoin: 管理节点已连接
ZStack/MN/ManagementNodeTemporalRegression: 管理节点时间回溯
ZStack/VM/VMHAProcess: 云主机高可用进程
ZStack/VM/VMStateChanged: 云主机状态发生变化
ZStack/VM/VMHAStarted: 云主机在物理机HA启动
ZStack/VM/VMStateChangedOnHost: 云主机在物理机上的状态发生变化
ZStack/VM/VMStateInShutdown: 云主机处于in shutdown状态
ZStack/VM/VMInternalIpChanged: 云主机内部IP发生变化
ZStack/VM/VMInternalIpDuplicate: 云主机内部IP已被占用
ZStack/VM/VMInternalIpRangeConflict: 云主机内部IP与云平台三层网络IpRange冲突
ZStack/VRouter/VRouterDisconnected: 路由器失联
ZStack/VRouter/VRouterConnected: 路由器已连接
ZStack/VRouter/VRouterUnhealthy: 路由器状态异常
ZStack/VRouter/MasterVpcRouterChanged: 路由器高可用组主备发生切换
ZStack/VRouter/VRouterHealthy: 路由器状态正常
ZStack/VRouter/MasterVpcRouterChanged: 路由器高可用组主备发生切换
ZStack/VRouter/VRouterAbnormalFilesExists: VPC路由器磁盘空间被异常文件占用
ZStack/VRouter/VRouterPaused: 路由器异常停机
ZStack/VRouter/ZWatchAgentFeaturePvpanic: 路由器崩溃检查模块运行情况
ZStack/VRouter/PVPanicEnableInDomainXML: 路由器崩溃对应的物理机支持情况
ZStack/BaremetalVM/OperatingSystemCPUSystemUtilization: 内部监控CPU系统进程使用率
ZStack/BaremetalVM/OperatingSystemCPUUserUtilization: 内部监控CPU用户进程使用率
ZStack/BaremetalVM/OperatingSystemCPUWaitUtilization: 内部监控CPU等待IO完成使用率
ZStack/BaremetalVM/OperatingSystemCPUIdleUtilization: 内部监控CPU空闲率
ZStack/BaremetalVM/OperatingSystemCPUUsedUtilization: 内部监控CPU已使用率
ZStack/BaremetalVM/OperatingSystemCPUAverageSystemUtilization: 内部监控CPU系统进程平均使用率
ZStack/BaremetalVM/OperatingSystemCPUAverageUserUtilization: 内部监控CPU用户进程平均使用率
ZStack/BaremetalVM/OperatingSystemCPUAverageWaitUtilization: 内部监控CPU等待IO完成平均使用率
ZStack/BaremetalVM/OperatingSystemCPUAverageIdleUtilization: 内部监控CPU平均空闲率
ZStack/BaremetalVM/OperatingSystemCPUAverageUsedUtilization: 内部监控CPU平均已使用率
ZStack/BaremetalVM/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/BaremetalVM/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/BaremetalVM/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/BaremetalVM/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/BaremetalVM/DiskFreeCapacityInBytes: 磁盘剩余容量
ZStack/BaremetalVM/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/BaremetalVM/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/BaremetalVM/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/BaremetalVM/DiskTotalCapacityInBytes: 磁盘总容量
ZStack/BaremetalVM/DiskReadBytesPerSecond: 磁盘每秒读字节数
ZStack/BaremetalVM/DiskReadRequestPerSecond: 磁盘每秒读请求数
ZStack/BaremetalVM/DiskWriteBytesPerSecond: 磁盘每秒写字节数
ZStack/BaremetalVM/DiskWriteRequestPerSecond: 磁盘每秒写请求数
ZStack/BaremetalVM/OperatingSystemNetworkInBytes: 裸金属云主机网卡接收字节数
ZStack/BaremetalVM/OperatingSystemNetworkAllInBytes: 裸金属云主机全部网卡接收字节数
ZStack/BaremetalVM/OperatingSystemNetworkInPackets: 裸金属云主机网卡接收报数
ZStack/BaremetalVM/OperatingSystemNetworkAllInPackets: 裸金属云主机全部网卡接收报数
ZStack/BaremetalVM/OperatingSystemNetworkInErrors: 裸金属云主机网卡接收错误报字节数
ZStack/BaremetalVM/OperatingSystemNetworkAllInErrors: 裸金属云主机全部网卡接收错误报字节数
ZStack/BaremetalVM/OperatingSystemNetworkOutBytes: 裸金属云主机网卡发送字节数
ZStack/BaremetalVM/OperatingSystemNetworkAllOutBytes: 裸金属云主机全部网卡发送字节数
ZStack/BaremetalVM/OperatingSystemNetworkOutPackets: 裸金属云主机网卡发送报数
ZStack/BaremetalVM/OperatingSystemNetworkAllOutPackets: 裸金属云主机全部网卡发送报数
ZStack/BaremetalVM/OperatingSystemNetworkOutErrors: 裸金属云主机网卡发送错误报字节数
ZStack/BaremetalVM/OperatingSystemNetworkAllOutErrors: 裸金属云主机全部网卡发送错误报字节数
ZStack/BaremetalVM/OperatingSystemMemoryTotalBytes: 裸金属云主机内存总字节数
ZStack/BaremetalVM/OperatingSystemMemoryFreeBytes: 裸金属云主机内存空余节数
ZStack/BaremetalVM/OperatingSystemMemoryUsedBytes: 裸金属云主机内存已使用节数
ZStack/BaremetalVM/OperatingSystemMemoryAvailableBytes: 裸金属云主机内存可用节数
ZStack/BaremetalVM/OperatingSystemMemoryFreePercent: 裸金属云主机内存空闲率
ZStack/BaremetalVM/OperatingSystemMemoryUsedPercent: 裸金属云主机内存使用率
ZStack/BackupStorage/TotalAvailableCapacityInBytes: 全部镜像存储可用容量
ZStack/BackupStorage/TotalAvailableCapacityInPercent: 全部镜像存储可用容量百分比
ZStack/BackupStorage/AvailableCapacityInBytes: 镜像服务器可用容量
ZStack/BackupStorage/AvailableCapacityInPercent: 镜像服务器可用容量百分比
ZStack/BackupStorage/TotalUsedCapacityInBytes: 全部镜像存储已用容量
ZStack/BackupStorage/TotalUsedCapacityInPercent: 全部镜像存储已用容量百分比
ZStack/BackupStorage/UsedCapacityInBytes: 镜像服务器已用容量
ZStack/BackupStorage/UsedCapacityInPercent: 镜像服务器已用容量百分比
ZStack/BackupStorage/TotalLockedCapacityInBytes: 镜像存储禁用容量
ZStack/BackupStorage/TotalLockedCapacityInPercent: 镜像存储禁用容量百分比
ZStack/BackupStorage/NetworkInBytes: 网卡入速度
ZStack/BackupStorage/NetworkAllInBytes: 全部网卡入速度
ZStack/BackupStorage/NetworkInPackets: 网卡入包数
ZStack/BackupStorage/NetworkAllInPackets: 全部网卡入包数
ZStack/BackupStorage/NetworkInErrors: 网卡入错误数
ZStack/BackupStorage/NetworkInDropped: 网卡入丢包数
ZStack/BackupStorage/NetworkAllInErrors: 全部网卡入错误数
ZStack/BackupStorage/NetworkOutBytes: 网卡出速度
ZStack/BackupStorage/NetworkAllOutBytes: 全部网卡出速度
ZStack/BackupStorage/NetworkOutPackets: 网卡出包数
ZStack/BackupStorage/NetworkAllOutPackets: 全部网卡出包数
ZStack/BackupStorage/NetworkOutErrors: 网卡出错误数
ZStack/BackupStorage/NetworkOutDropped: 网卡出丢包数
ZStack/BackupStorage/NetworkAllOutErrors: 全部网卡出错误数
ZStack/BackupStorage/CPUIdleUtilization: CPU空闲率
ZStack/BackupStorage/CPUSystemUtilization: CPU系统进程使用率
ZStack/BackupStorage/CPUUserUtilization: CPU用户进程使用率
ZStack/BackupStorage/CPUWaitUtilization: CPU等待IO完成使用率
ZStack/BackupStorage/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/BackupStorage/CPUUsedUtilization: CPU使用率
ZStack/BackupStorage/CPUAllUsedUtilization: 全部CPU使用率
ZStack/BackupStorage/CPUAverageUsedUtilization: 平均CPU使用率
ZStack/BackupStorage/CPUAverageUserUtilization: 平均CPU用户进程使用率
ZStack/BackupStorage/CPUAverageSystemUtilization: 平均CPU系统进程使用率
ZStack/BackupStorage/CPUAverageIdleUtilization: 平均CPU空闲率
ZStack/BackupStorage/CPUAverageWaitUtilization: 平均CPU等待IO完成使用率
ZStack/BackupStorage/DiskReadOps: 磁盘读IOPS
ZStack/BackupStorage/DiskAllReadOps: 全部磁盘读IOPS
ZStack/BackupStorage/DiskWriteOps: 磁盘写IOPS
ZStack/BackupStorage/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/BackupStorage/DiskReadBytes: 磁盘读速度
ZStack/BackupStorage/DiskAllReadBytes: 全部磁盘读速度
ZStack/BackupStorage/DiskWriteBytes: 磁盘写速度
ZStack/BackupStorage/DiskAllWriteBytes: 全部磁盘写速度
ZStack/BackupStorage/MemoryFreeBytes: 内存未使用容量
ZStack/BackupStorage/MemoryFreeInPercent: 内存未使用百分比
ZStack/BackupStorage/MemoryUsedBytes: 内存使用容量
ZStack/BackupStorage/MemoryUsedInPercent: 内存使用百分比
ZStack/VCenter/VmCPUUsage: 云主机CPU使用率（百分比）
ZStack/VCenter/VmCPUUsageMHZ: 云主机CPU使用率（兆赫）
ZStack/VCenter/VmCPUIdle: 云主机CPU空闲状态时间
ZStack/VCenter/VmCPUUsed: 云主机CPU使用量
ZStack/VCenter/VmMemoryUsage: 云主机内存使用率（百分比）
ZStack/VCenter/VmMemoryGranted: 为云主机映射的物理内存量
ZStack/VCenter/VmMemoryActive: 云主机主动读取或写入的物理内存量
ZStack/VCenter/VmMemoryVmMemCtl: 从云主机上回收的物理内存量
ZStack/VCenter/VmMemoryConsumed: 备份云主机内存的物理内存量
ZStack/VCenter/VmMemoryEntitlement: 云主机所需的物理内存量
ZStack/VCenter/VmDiskUsage: 云主机磁盘使用率（百分比）
ZStack/VCenter/VmDiskRead: 云主机磁盘读速率
ZStack/VCenter/VmDiskWrite: 云主机磁盘写速率
ZStack/VCenter/VmDiskMaxTotalLatency: 云主机磁盘最大时延
ZStack/VCenter/VmVirtualDiskNumberReadAveraged: 每秒向虚拟磁盘发出的读取命令的平均数目
ZStack/VCenter/VmVirtualDiskNumberWriteAveraged: 每秒向虚拟磁盘发出的写入命令的平均数目
ZStack/VCenter/VmVirtualDiskRead: 云主机虚拟磁盘读速率
ZStack/VCenter/VmVirtualDiskWrite: 云主机虚拟磁盘写速率
ZStack/VCenter/VmVirtualDiskTotalReadLatency: 读虚拟磁盘耗费的平均时间
ZStack/VCenter/VmVirtualDiskTotalWriteLatency: 写虚拟磁盘耗费的平均时间
ZStack/VCenter/VmNetworkUsage: 云主机网络总负载
ZStack/VCenter/VmNetworkPacketRx: 云主机接收数据包数
ZStack/VCenter/VmNetworkPacketTx: 云主机发送数据包数
ZStack/VCenter/VmNetworkReceived: 云主机接收数据包平均速率
ZStack/VCenter/VmNetworkTransmitted: 云主机发送数据包平均速率
ZStack/VCenter/VmNetworkByteRx: 每秒接收的平均数据量
ZStack/VCenter/VmNetworkByteTx: 每秒发送的平均数据量
ZStack/VCenter/HostCPUUsage: 物理机CPU使用率（百分比）
ZStack/VCenter/HostCPUUsageMHZ: 物理机CPU使用率（兆赫）
ZStack/VCenter/HostCPUIdle: 物理机CPU空闲状态时间
ZStack/VCenter/HostCPUUsed: 物理机CPU使用量
ZStack/VCenter/HostMemoryUsage: 物理机内存使用率（百分比）
ZStack/VCenter/HostMemoryGranted: 为物理机分配的内存量
ZStack/VCenter/HostMemoryActive: 物理机主动读取或写入的物理内存量
ZStack/VCenter/HostMemoryHostMemCtl: 从云主机上回收的物理内存量
ZStack/VCenter/HostMemoryConsumed: 备份云主机内存的物理内存量
ZStack/VCenter/HostMemoryEntitlement: 云主机所需的物理内存量
ZStack/VCenter/HostDiskUsage: 物理机磁盘使用率（百分比）
ZStack/VCenter/HostDiskRead: 物理机磁盘读速率
ZStack/VCenter/HostDiskWrite: 物理机磁盘写速率
ZStack/VCenter/HostDiskMaxTotalLatency: 物理机磁盘最大时延
ZStack/VCenter/HostVirtualDiskNumberReadAveraged: 物理机每秒向虚拟磁盘发出的读取命令的平均数目
ZStack/VCenter/HostVirtualDiskNumberWriteAveraged: 物理机每秒向虚拟磁盘发出的写入命令的平均数目
ZStack/VCenter/HostVirtualDiskRead: 物理机虚拟磁盘读速率
ZStack/VCenter/HostVirtualDiskWrite: 物理机虚拟磁盘写速率
ZStack/VCenter/HostVirtualDiskTotalReadLatency: 读取虚拟磁盘耗费的平均时间
ZStack/VCenter/HostVirtualDiskTotalWriteLatency: 写入虚拟磁盘耗费的平均时间
ZStack/VCenter/HostNetworkUsage: 物理机网络总负载
ZStack/VCenter/HostNetworkPacketRx: 物理机接收数据包数
ZStack/VCenter/HostNetworkPacketTx: 物理机发送数据包数
ZStack/VCenter/HostNetworkReceived: 物理机接收数据包平均速率
ZStack/VCenter/HostNetworkTransmitted: 物理机发送数据包平均速率
ZStack/VCenter/HostNetworkByteRx: 每秒接收的平均数据量
ZStack/VCenter/HostNetworkByteTx: 每秒发送的平均数据量
ZStack/System/ManagementServerDirFreeCapacityInBytes: 管理节点数据目录磁盘空闲容量
ZStack/System/ManagementServerDirFreeCapacityInPercent: 管理节点数据目录磁盘空闲率
ZStack/System/ManagementServerDirUsedCapacityInBytes: 管理节点数据目录磁盘已用容量
ZStack/System/ManagementServerDirUsedCapacityInPercent: 管理节点数据目录磁盘占用率
ZStack/System/ManagementInfluxDBUsedCapacityInBytes: 管理节点influxdb目录磁盘占用量
ZStack/System/ManagementPrometheusUsedCapacityInBytes: 管理节点prometheus目录磁盘占用量
ZStack/System/ManagementZStackLogCapacityInBytes: 管理节点ZStack日志目录磁盘占用量
ZStack/System/ManagementZStackDBBackupCapacityInBytes: 管理节点ZStack备份目录磁盘占用量
ZStack/System/ManagementMysqlCapacityInBytes: 管理节点Mysql目录磁盘占用量
ZStack/Host/CPUIdleUtilization: CPU空闲率
ZStack/Host/CPUSystemUtilization: CPU系统进程使用率
ZStack/Host/CPUUserUtilization: CPU用户进程使用率
ZStack/Host/CPUWaitUtilization: CPU等待IO完成使用率
ZStack/Host/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/Host/CPUUsedUtilization: CPU使用率
ZStack/Host/CPUAllUsedUtilization: 全部CPU使用率
ZStack/Host/CPUAverageUsedUtilization: 平均CPU使用率
ZStack/Host/CPUAverageUserUtilization: 平均CPU用户进程使用率
ZStack/Host/CPUAverageSystemUtilization: 平均CPU系统进程使用率
ZStack/Host/CPUAverageIdleUtilization: 平均CPU空闲率
ZStack/Host/CPUAverageWaitUtilization: 平均CPU等待IO完成使用率
ZStack/Host/MemoryFreeBytes: 内存未使用容量
ZStack/Host/MemoryFreeInPercent: 内存未使用百分比
ZStack/Host/MemoryUsedBytes: 内存使用容量
ZStack/Host/MemoryUsedInPercent: 内存使用百分比
ZStack/Host/DiskReadOps: 磁盘读IOPS
ZStack/Host/DiskAllReadOps: 全部磁盘读IOPS
ZStack/Host/DiskWriteOps: 磁盘写IOPS
ZStack/Host/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/Host/DiskReadBytes: 磁盘读速度
ZStack/Host/DiskAllReadBytes: 全部磁盘读速度
ZStack/Host/DiskWriteBytes: 磁盘写速度
ZStack/Host/DiskAllWriteBytes: 全部磁盘写速度
ZStack/Host/NetworkInBytes: 网卡入速度
ZStack/Host/NetworkAllInBytes: 全部网卡入速度
ZStack/Host/NetworkInPackets: 网卡入包数
ZStack/Host/NetworkAllInPackets: 全部网卡入包数
ZStack/Host/NetworkInErrors: 网卡入错误数
ZStack/Host/NetworkInDropped: 网卡入丢包数
ZStack/Host/NetworkAllInErrors: 全部网卡入错误数
ZStack/Host/NetworkOutBytes: 网卡出速度
ZStack/Host/NetworkAllOutBytes: 全部网卡出速度
ZStack/Host/NetworkOutPackets: 网卡出包数
ZStack/Host/NetworkAllOutPackets: 全部网卡出包数
ZStack/Host/NetworkOutErrors: 网卡出错误数
ZStack/Host/NetworkOutDropped: 网卡出丢包数
ZStack/Host/NetworkAllOutErrors: 全部网卡出错误数
ZStack/Host/NetworkAllInBytesByServiceType: 网络服务全部网卡入速度
ZStack/Host/NetworkAllInPacketsByServiceType: 网络服务全部网卡入包数
ZStack/Host/NetworkAllInErrorsByServiceType: 网络服务全部网卡入错误数
ZStack/Host/NetworkAllOutBytesByServiceType: 网络服务全部网卡出速度
ZStack/Host/NetworkAllOutPacketsByServiceType: 网络服务全部网卡出包数
ZStack/Host/NetworkAllOutErrorsByServiceType: 网络服务全部网卡出错误数
ZStack/Host/NetworkConntrackCount: Conntrack连接数
ZStack/Host/NetworkConntrackInPercent: Conntrack已使用百分比
ZStack/Host/DiskTotalCapacityInBytes: 全部磁盘容量
ZStack/Host/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/Host/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/Host/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/Host/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/Host/DiskCapacityInBytes: 磁盘容量
ZStack/Host/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/Host/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/Host/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/Host/DiskZStackUsedCapacityInBytes: 云平台系统文件占用磁盘容量
ZStack/Host/DiskZStackUsedCapacityInPercent: 云平台系统文件占用磁盘容量百分比
ZStack/Host/DiskTransUsedCapacityInBytes: 客户业务数据占用磁盘容量
ZStack/Host/DiskTransUsedCapacityInPercent: 客户业务数据占用磁盘容量百分比
ZStack/Host/DiskRootUsedCapacityInBytes: 根盘已使用容量
ZStack/Host/DiskRootUsedCapacityInPercent: 根盘已使用容量百分比
ZStack/Host/DiskLatency: 磁盘IO时延
ZStack/Host/DiskLatencyWwid: 所有物理机对同一磁盘标识的IO平均时延
ZStack/Host/DiskWriteOpsWwid: 所有物理机对同一磁盘标识的写IOPS总量
ZStack/Host/DiskReadOpsWwid: 所有物理机对同一磁盘标识的读IOPS总量
ZStack/Host/DiskWriteBytesWwid: 所有物理机对同一磁盘标识的写带宽总量
ZStack/Host/DiskReadBytesWwid: 所有物理机对同一磁盘标识的读带宽总量
ZStack/Host/VolumeGroupCapacityInbytes: 卷组总容量
ZStack/Host/VolumeGroupFreeCapacityInbytes: 卷组可用容量
ZStack/Host/VolumeGroupUsedCapacityInPercent: 卷组已使用容量百分比
ZStack/Host/RaidState: RAID状态(0为正常，5为降级)
ZStack/Host/PhysicalDiskState: 物理磁盘状态
ZStack/Host/PhysicalDiskTemperature: 物理磁盘温度
ZStack/Host/PowerSupply: 供电单元状态（0为正常，其他为异常）
ZStack/Host/PowerSupplyCurrentOutputPower: 电源当前输出功率（单位为瓦特，w）
ZStack/Host/IpmiStatus: IPMI模块状态（0为正常，其他为异常）
ZStack/Host/PhysicalNetworkInterface: 物理网卡状态（0为正常，其他为异常，Speed单位为Mb/s，表示千兆/万兆网卡）
ZStack/Host/DiskXfsFragInPercent: XFS文件系统碎片化程度百分比
ZStack/Host/HostTotal: 物理机数量
ZStack/Host/ConnectedHostCount: 已连接物理机数量
ZStack/Host/ConnectedHostInPercent: 已连接物理机百分比
ZStack/Host/DisconnectedHostCount: 未连接物理机数量
ZStack/Host/DisconnectedHostInPercent: 未连接物理机百分比
ZStack/XDragonHost/CPUIdleUtilization: CPU空闲率
ZStack/XDragonHost/CPUSystemUtilization: CPU系统进程使用率
ZStack/XDragonHost/CPUUserUtilization: CPU用户进程使用率
ZStack/XDragonHost/CPUWaitUtilization: CPU等待IO完成使用率
ZStack/XDragonHost/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/XDragonHost/CPUUsedUtilization: CPU使用率
ZStack/XDragonHost/CPUAllUsedUtilization: 全部CPU使用率
ZStack/XDragonHost/CPUAverageUsedUtilization: 平均CPU使用率
ZStack/XDragonHost/CPUAverageUserUtilization: 平均CPU用户进程使用率
ZStack/XDragonHost/CPUAverageSystemUtilization: 平均CPU系统进程使用率
ZStack/XDragonHost/CPUAverageIdleUtilization: 平均CPU空闲率
ZStack/XDragonHost/CPUAverageWaitUtilization: 平均CPU等待IO完成使用率
ZStack/XDragonHost/MemoryFreeBytes: 内存未使用容量
ZStack/XDragonHost/MemoryFreeInPercent: 内存未使用百分比
ZStack/XDragonHost/MemoryUsedBytes: 内存使用容量
ZStack/XDragonHost/MemoryUsedInPercent: 内存使用百分比
ZStack/XDragonHost/DiskReadOps: 磁盘读IOPS
ZStack/XDragonHost/DiskAllReadOps: 全部磁盘读IOPS
ZStack/XDragonHost/DiskWriteOps: 磁盘写IOPS
ZStack/XDragonHost/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/XDragonHost/DiskReadBytes: 磁盘读速度
ZStack/XDragonHost/DiskAllReadBytes: 全部磁盘读速度
ZStack/XDragonHost/DiskWriteBytes: 磁盘写速度
ZStack/XDragonHost/DiskAllWriteBytes: 全部磁盘写速度
ZStack/XDragonHost/NetworkInBytes: 网卡入速度
ZStack/XDragonHost/NetworkAllInBytes: 全部网卡入速度
ZStack/XDragonHost/NetworkInPackets: 网卡入包数
ZStack/XDragonHost/NetworkAllInPackets: 全部网卡入包数
ZStack/XDragonHost/NetworkInErrors: 网卡入错误数
ZStack/XDragonHost/NetworkInDropped: 网卡入丢包数
ZStack/XDragonHost/NetworkAllInErrors: 全部网卡入错误数
ZStack/XDragonHost/NetworkOutBytes: 网卡出速度
ZStack/XDragonHost/NetworkAllOutBytes: 全部网卡出速度
ZStack/XDragonHost/NetworkOutPackets: 网卡出包数
ZStack/XDragonHost/NetworkAllOutPackets: 全部网卡出包数
ZStack/XDragonHost/NetworkOutErrors: 网卡出错误数
ZStack/XDragonHost/NetworkOutDropped: 网卡出丢包数
ZStack/XDragonHost/NetworkAllOutErrors: 全部网卡出错误数
ZStack/XDragonHost/NetworkAllInBytesByServiceType: 网络服务全部网卡入速度
ZStack/XDragonHost/NetworkAllInPacketsByServiceType: 网络服务全部网卡入包数
ZStack/XDragonHost/NetworkAllInErrorsByServiceType: 网络服务全部网卡入错误数
ZStack/XDragonHost/NetworkAllOutBytesByServiceType: 网络服务全部网卡出速度
ZStack/XDragonHost/NetworkAllOutPacketsByServiceType: 网络服务全部网卡出包数
ZStack/XDragonHost/NetworkAllOutErrorsByServiceType: 网络服务全部网卡出错误数
ZStack/XDragonHost/NetworkConntrackCount: Conntrack连接数
ZStack/XDragonHost/NetworkConntrackInPercent: Conntrack已使用百分比
ZStack/XDragonHost/DiskTotalCapacityInBytes: 全部磁盘容量
ZStack/XDragonHost/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/XDragonHost/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/XDragonHost/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/XDragonHost/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/XDragonHost/DiskCapacityInBytes: 磁盘容量
ZStack/XDragonHost/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/XDragonHost/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/XDragonHost/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/XDragonHost/DiskZStackUsedCapacityInBytes: 云平台系统文件占用磁盘容量
ZStack/XDragonHost/DiskZStackUsedCapacityInPercent: 云平台系统文件占用磁盘容量百分比
ZStack/XDragonHost/DiskTransUsedCapacityInBytes: 客户业务数据占用磁盘容量
ZStack/XDragonHost/DiskTransUsedCapacityInPercent: 客户业务数据占用磁盘容量百分比
ZStack/XDragonHost/DiskRootUsedCapacityInBytes: 根盘已使用容量
ZStack/XDragonHost/DiskRootUsedCapacityInPercent: 根盘已使用容量百分比
ZStack/XDragonHost/DiskLatency: 磁盘IO时延
ZStack/XDragonHost/DiskLatencyWwid: 所有神龙物理机对同一磁盘标识的IO平均时延
ZStack/XDragonHost/DiskWriteOpsWwid: 所有神龙物理机对同一磁盘标识的写IOPS总量
ZStack/XDragonHost/DiskReadOpsWwid: 所有神龙物理机对同一磁盘标识的读IOPS总量
ZStack/XDragonHost/DiskWriteBytesWwid: 所有神龙物理机对同一磁盘标识的写带宽总量
ZStack/XDragonHost/DiskReadBytesWwid: 所有神龙物理机对同一磁盘标识的读带宽总量
ZStack/XDragonHost/VolumeGroupCapacityInbytes: 卷组总容量
ZStack/XDragonHost/VolumeGroupFreeCapacityInbytes: 卷组可用容量
ZStack/XDragonHost/VolumeGroupUsedCapacityInPercent: 卷组已使用容量百分比
ZStack/XDragonHost/RaidState: RAID状态(0为正常，5为降级)
ZStack/XDragonHost/PhysicalDiskState: 物理磁盘状态
ZStack/XDragonHost/PhysicalDiskTemperature: 物理磁盘温度
ZStack/XDragonHost/PowerSupply: 供电单元状态（0为正常，其他为异常）
ZStack/XDragonHost/PowerSupplyCurrentOutputPower: 电源当前输出功率（单位为瓦特，w）
ZStack/XDragonHost/IpmiStatus: IPMI模块状态（0为正常，其他为异常）
ZStack/XDragonHost/PhysicalNetworkInterface: 物理网卡状态（0为正常，其他为异常，Speed单位为Mb/s，表示千兆/万兆网卡）
ZStack/XDragonHost/DiskXfsFragInPercent: XFS文件系统碎片化程度百分比
ZStack/XDragonHost/HostTotal: 神龙物理机数量
ZStack/XDragonHost/ConnectedHostCount: 已连接神龙物理机数量
ZStack/XDragonHost/ConnectedHostInPercent: 已连接神龙物理机百分比
ZStack/XDragonHost/DisconnectedHostCount: 未连接神龙物理机数量
ZStack/XDragonHost/DisconnectedHostInPercent: 未连接神龙物理机百分比
ZStack/KVMHost/CPUIdleUtilization: CPU空闲率
ZStack/KVMHost/CPUSystemUtilization: CPU系统进程使用率
ZStack/KVMHost/CPUUserUtilization: CPU用户进程使用率
ZStack/KVMHost/CPUWaitUtilization: CPU等待IO完成使用率
ZStack/KVMHost/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/KVMHost/CPUUsedUtilization: CPU使用率
ZStack/KVMHost/CPUAllUsedUtilization: 全部CPU使用率
ZStack/KVMHost/CPUAverageUsedUtilization: 平均CPU使用率
ZStack/KVMHost/CPUAverageUserUtilization: 平均CPU用户进程使用率
ZStack/KVMHost/CPUAverageSystemUtilization: 平均CPU系统进程使用率
ZStack/KVMHost/CPUAverageIdleUtilization: 平均CPU空闲率
ZStack/KVMHost/CPUAverageWaitUtilization: 平均CPU等待IO完成使用率
ZStack/KVMHost/MemoryFreeBytes: 内存未使用容量
ZStack/KVMHost/MemoryFreeInPercent: 内存未使用百分比
ZStack/KVMHost/MemoryUsedBytes: 内存使用容量
ZStack/KVMHost/MemoryUsedInPercent: 内存使用百分比
ZStack/KVMHost/DiskReadOps: 磁盘读IOPS
ZStack/KVMHost/DiskAllReadOps: 全部磁盘读IOPS
ZStack/KVMHost/DiskWriteOps: 磁盘写IOPS
ZStack/KVMHost/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/KVMHost/DiskReadBytes: 磁盘读速度
ZStack/KVMHost/DiskAllReadBytes: 全部磁盘读速度
ZStack/KVMHost/DiskWriteBytes: 磁盘写速度
ZStack/KVMHost/DiskAllWriteBytes: 全部磁盘写速度
ZStack/KVMHost/NetworkInBytes: 网卡入速度
ZStack/KVMHost/NetworkAllInBytes: 全部网卡入速度
ZStack/KVMHost/NetworkInPackets: 网卡入包数
ZStack/KVMHost/NetworkAllInPackets: 全部网卡入包数
ZStack/KVMHost/NetworkInErrors: 网卡入错误数
ZStack/KVMHost/NetworkInDropped: 网卡入丢包数
ZStack/KVMHost/NetworkAllInErrors: 全部网卡入错误数
ZStack/KVMHost/NetworkOutBytes: 网卡出速度
ZStack/KVMHost/NetworkAllOutBytes: 全部网卡出速度
ZStack/KVMHost/NetworkOutPackets: 网卡出包数
ZStack/KVMHost/NetworkAllOutPackets: 全部网卡出包数
ZStack/KVMHost/NetworkOutErrors: 网卡出错误数
ZStack/KVMHost/NetworkOutDropped: 网卡出丢包数
ZStack/KVMHost/NetworkAllOutErrors: 全部网卡出错误数
ZStack/KVMHost/NetworkAllInBytesByServiceType: 网络服务全部网卡入速度
ZStack/KVMHost/NetworkAllInPacketsByServiceType: 网络服务全部网卡入包数
ZStack/KVMHost/NetworkAllInErrorsByServiceType: 网络服务全部网卡入错误数
ZStack/KVMHost/NetworkAllOutBytesByServiceType: 网络服务全部网卡出速度
ZStack/KVMHost/NetworkAllOutPacketsByServiceType: 网络服务全部网卡出包数
ZStack/KVMHost/NetworkAllOutErrorsByServiceType: 网络服务全部网卡出错误数
ZStack/KVMHost/NetworkConntrackCount: Conntrack连接数
ZStack/KVMHost/NetworkConntrackInPercent: Conntrack已使用百分比
ZStack/KVMHost/DiskTotalCapacityInBytes: 全部磁盘容量
ZStack/KVMHost/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/KVMHost/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/KVMHost/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/KVMHost/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/KVMHost/DiskCapacityInBytes: 磁盘容量
ZStack/KVMHost/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/KVMHost/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/KVMHost/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/KVMHost/DiskZStackUsedCapacityInBytes: 云平台系统文件占用磁盘容量
ZStack/KVMHost/DiskZStackUsedCapacityInPercent: 云平台系统文件占用磁盘容量百分比
ZStack/KVMHost/DiskTransUsedCapacityInBytes: 客户业务数据占用磁盘容量
ZStack/KVMHost/DiskTransUsedCapacityInPercent: 客户业务数据占用磁盘容量百分比
ZStack/KVMHost/DiskRootUsedCapacityInBytes: 根盘已使用容量
ZStack/KVMHost/DiskRootUsedCapacityInPercent: 根盘已使用容量百分比
ZStack/KVMHost/DiskLatency: 磁盘IO时延
ZStack/KVMHost/DiskLatencyWwid: 所有KVM物理机对同一磁盘标识的IO平均时延
ZStack/KVMHost/DiskWriteOpsWwid: 所有KVM物理机对同一磁盘标识的写IOPS总量
ZStack/KVMHost/DiskReadOpsWwid: 所有KVM物理机对同一磁盘标识的读IOPS总量
ZStack/KVMHost/DiskWriteBytesWwid: 所有KVM物理机对同一磁盘标识的写带宽总量
ZStack/KVMHost/DiskReadBytesWwid: 所有KVM物理机对同一磁盘标识的读带宽总量
ZStack/KVMHost/VolumeGroupCapacityInbytes: 卷组总容量
ZStack/KVMHost/VolumeGroupFreeCapacityInbytes: 卷组可用容量
ZStack/KVMHost/VolumeGroupUsedCapacityInPercent: 卷组已使用容量百分比
ZStack/KVMHost/RaidState: RAID状态(0为正常，5为降级)
ZStack/KVMHost/PhysicalDiskState: 物理磁盘状态
ZStack/KVMHost/PhysicalDiskTemperature: 物理磁盘温度
ZStack/KVMHost/PowerSupply: 供电单元状态（0为正常，其他为异常）
ZStack/KVMHost/PowerSupplyCurrentOutputPower: 电源当前输出功率（单位为瓦特，w）
ZStack/KVMHost/IpmiStatus: IPMI模块状态（0为正常，其他为异常）
ZStack/KVMHost/PhysicalNetworkInterface: 物理网卡状态（0为正常，其他为异常，Speed单位为Mb/s，表示千兆/万兆网卡）
ZStack/KVMHost/DiskXfsFragInPercent: XFS文件系统碎片化程度百分比
ZStack/KVMHost/HostTotal: KVM物理机数量
ZStack/KVMHost/ConnectedHostCount: 已连接KVM物理机数量
ZStack/KVMHost/ConnectedHostInPercent: 已连接KVM物理机百分比
ZStack/KVMHost/DisconnectedHostCount: 未连接KVM物理机数量
ZStack/KVMHost/DisconnectedHostInPercent: 未连接KVM物理机百分比
ZStack/ESXHost/CPUIdleUtilization: CPU空闲率
ZStack/ESXHost/CPUSystemUtilization: CPU系统进程使用率
ZStack/ESXHost/CPUUserUtilization: CPU用户进程使用率
ZStack/ESXHost/CPUWaitUtilization: CPU等待IO完成使用率
ZStack/ESXHost/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/ESXHost/CPUUsedUtilization: CPU使用率
ZStack/ESXHost/CPUAllUsedUtilization: 全部CPU使用率
ZStack/ESXHost/CPUAverageUsedUtilization: 平均CPU使用率
ZStack/ESXHost/CPUAverageUserUtilization: 平均CPU用户进程使用率
ZStack/ESXHost/CPUAverageSystemUtilization: 平均CPU系统进程使用率
ZStack/ESXHost/CPUAverageIdleUtilization: 平均CPU空闲率
ZStack/ESXHost/CPUAverageWaitUtilization: 平均CPU等待IO完成使用率
ZStack/ESXHost/MemoryFreeBytes: 内存未使用容量
ZStack/ESXHost/MemoryFreeInPercent: 内存未使用百分比
ZStack/ESXHost/MemoryUsedBytes: 内存使用容量
ZStack/ESXHost/MemoryUsedInPercent: 内存使用百分比
ZStack/ESXHost/DiskReadOps: 磁盘读IOPS
ZStack/ESXHost/DiskAllReadOps: 全部磁盘读IOPS
ZStack/ESXHost/DiskWriteOps: 磁盘写IOPS
ZStack/ESXHost/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/ESXHost/DiskReadBytes: 磁盘读速度
ZStack/ESXHost/DiskAllReadBytes: 全部磁盘读速度
ZStack/ESXHost/DiskWriteBytes: 磁盘写速度
ZStack/ESXHost/DiskAllWriteBytes: 全部磁盘写速度
ZStack/ESXHost/NetworkInBytes: 网卡入速度
ZStack/ESXHost/NetworkAllInBytes: 全部网卡入速度
ZStack/ESXHost/NetworkInPackets: 网卡入包数
ZStack/ESXHost/NetworkAllInPackets: 全部网卡入包数
ZStack/ESXHost/NetworkInErrors: 网卡入错误数
ZStack/ESXHost/NetworkInDropped: 网卡入丢包数
ZStack/ESXHost/NetworkAllInErrors: 全部网卡入错误数
ZStack/ESXHost/NetworkOutBytes: 网卡出速度
ZStack/ESXHost/NetworkAllOutBytes: 全部网卡出速度
ZStack/ESXHost/NetworkOutPackets: 网卡出包数
ZStack/ESXHost/NetworkAllOutPackets: 全部网卡出包数
ZStack/ESXHost/NetworkOutErrors: 网卡出错误数
ZStack/ESXHost/NetworkOutDropped: 网卡出丢包数
ZStack/ESXHost/NetworkAllOutErrors: 全部网卡出错误数
ZStack/ESXHost/NetworkAllInBytesByServiceType: 网络服务全部网卡入速度
ZStack/ESXHost/NetworkAllInPacketsByServiceType: 网络服务全部网卡入包数
ZStack/ESXHost/NetworkAllInErrorsByServiceType: 网络服务全部网卡入错误数
ZStack/ESXHost/NetworkAllOutBytesByServiceType: 网络服务全部网卡出速度
ZStack/ESXHost/NetworkAllOutPacketsByServiceType: 网络服务全部网卡出包数
ZStack/ESXHost/NetworkAllOutErrorsByServiceType: 网络服务全部网卡出错误数
ZStack/ESXHost/NetworkConntrackCount: Conntrack连接数
ZStack/ESXHost/NetworkConntrackInPercent: Conntrack已使用百分比
ZStack/ESXHost/DiskTotalCapacityInBytes: 全部磁盘容量
ZStack/ESXHost/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/ESXHost/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/ESXHost/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/ESXHost/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/ESXHost/DiskCapacityInBytes: 磁盘容量
ZStack/ESXHost/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/ESXHost/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/ESXHost/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/ESXHost/DiskZStackUsedCapacityInBytes: 云平台系统文件占用磁盘容量
ZStack/ESXHost/DiskZStackUsedCapacityInPercent: 云平台系统文件占用磁盘容量百分比
ZStack/ESXHost/DiskTransUsedCapacityInBytes: 客户业务数据占用磁盘容量
ZStack/ESXHost/DiskTransUsedCapacityInPercent: 客户业务数据占用磁盘容量百分比
ZStack/ESXHost/DiskRootUsedCapacityInBytes: 根盘已使用容量
ZStack/ESXHost/DiskRootUsedCapacityInPercent: 根盘已使用容量百分比
ZStack/ESXHost/DiskLatency: 磁盘IO时延
ZStack/ESXHost/DiskLatencyWwid: 所有ESX物理机对同一磁盘标识的IO平均时延
ZStack/ESXHost/DiskWriteOpsWwid: 所有ESX物理机对同一磁盘标识的写IOPS总量
ZStack/ESXHost/DiskReadOpsWwid: 所有ESX物理机对同一磁盘标识的读IOPS总量
ZStack/ESXHost/DiskWriteBytesWwid: 所有ESX物理机对同一磁盘标识的写带宽总量
ZStack/ESXHost/DiskReadBytesWwid: 所有ESX物理机对同一磁盘标识的读带宽总量
ZStack/ESXHost/VolumeGroupCapacityInbytes: 卷组总容量
ZStack/ESXHost/VolumeGroupFreeCapacityInbytes: 卷组可用容量
ZStack/ESXHost/VolumeGroupUsedCapacityInPercent: 卷组已使用容量百分比
ZStack/ESXHost/RaidState: RAID状态(0为正常，5为降级)
ZStack/ESXHost/PhysicalDiskState: 物理磁盘状态
ZStack/ESXHost/PhysicalDiskTemperature: 物理磁盘温度
ZStack/ESXHost/PowerSupply: 供电单元状态（0为正常，其他为异常）
ZStack/ESXHost/PowerSupplyCurrentOutputPower: 电源当前输出功率（单位为瓦特，w）
ZStack/ESXHost/IpmiStatus: IPMI模块状态（0为正常，其他为异常）
ZStack/ESXHost/PhysicalNetworkInterface: 物理网卡状态（0为正常，其他为异常，Speed单位为Mb/s，表示千兆/万兆网卡）
ZStack/ESXHost/DiskXfsFragInPercent: XFS文件系统碎片化程度百分比
ZStack/ESXHost/HostTotal: ESX物理机数量
ZStack/ESXHost/ConnectedHostCount: 已连接ESX物理机数量
ZStack/ESXHost/ConnectedHostInPercent: 已连接ESX物理机百分比
ZStack/ESXHost/DisconnectedHostCount: 未连接ESX物理机数量
ZStack/ESXHost/DisconnectedHostInPercent: 未连接ESX物理机百分比
ZStack/BareMetal2Gateway/CPUIdleUtilization: CPU空闲率
ZStack/BareMetal2Gateway/CPUSystemUtilization: CPU系统进程使用率
ZStack/BareMetal2Gateway/CPUUserUtilization: CPU用户进程使用率
ZStack/BareMetal2Gateway/CPUWaitUtilization: CPU等待IO完成使用率
ZStack/BareMetal2Gateway/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/BareMetal2Gateway/CPUUsedUtilization: CPU使用率
ZStack/BareMetal2Gateway/CPUAllUsedUtilization: 全部CPU使用率
ZStack/BareMetal2Gateway/CPUAverageUsedUtilization: 平均CPU使用率
ZStack/BareMetal2Gateway/CPUAverageUserUtilization: 平均CPU用户进程使用率
ZStack/BareMetal2Gateway/CPUAverageSystemUtilization: 平均CPU系统进程使用率
ZStack/BareMetal2Gateway/CPUAverageIdleUtilization: 平均CPU空闲率
ZStack/BareMetal2Gateway/CPUAverageWaitUtilization: 平均CPU等待IO完成使用率
ZStack/BareMetal2Gateway/MemoryFreeBytes: 内存未使用容量
ZStack/BareMetal2Gateway/MemoryFreeInPercent: 内存未使用百分比
ZStack/BareMetal2Gateway/MemoryUsedBytes: 内存使用容量
ZStack/BareMetal2Gateway/MemoryUsedInPercent: 内存使用百分比
ZStack/BareMetal2Gateway/DiskReadOps: 磁盘读IOPS
ZStack/BareMetal2Gateway/DiskAllReadOps: 全部磁盘读IOPS
ZStack/BareMetal2Gateway/DiskWriteOps: 磁盘写IOPS
ZStack/BareMetal2Gateway/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/BareMetal2Gateway/DiskReadBytes: 磁盘读速度
ZStack/BareMetal2Gateway/DiskAllReadBytes: 全部磁盘读速度
ZStack/BareMetal2Gateway/DiskWriteBytes: 磁盘写速度
ZStack/BareMetal2Gateway/DiskAllWriteBytes: 全部磁盘写速度
ZStack/BareMetal2Gateway/NetworkInBytes: 网卡入速度
ZStack/BareMetal2Gateway/NetworkAllInBytes: 全部网卡入速度
ZStack/BareMetal2Gateway/NetworkInPackets: 网卡入包数
ZStack/BareMetal2Gateway/NetworkAllInPackets: 全部网卡入包数
ZStack/BareMetal2Gateway/NetworkInErrors: 网卡入错误数
ZStack/BareMetal2Gateway/NetworkInDropped: 网卡入丢包数
ZStack/BareMetal2Gateway/NetworkAllInErrors: 全部网卡入错误数
ZStack/BareMetal2Gateway/NetworkOutBytes: 网卡出速度
ZStack/BareMetal2Gateway/NetworkAllOutBytes: 全部网卡出速度
ZStack/BareMetal2Gateway/NetworkOutPackets: 网卡出包数
ZStack/BareMetal2Gateway/NetworkAllOutPackets: 全部网卡出包数
ZStack/BareMetal2Gateway/NetworkOutErrors: 网卡出错误数
ZStack/BareMetal2Gateway/NetworkOutDropped: 网卡出丢包数
ZStack/BareMetal2Gateway/NetworkAllOutErrors: 全部网卡出错误数
ZStack/BareMetal2Gateway/NetworkAllInBytesByServiceType: 网络服务全部网卡入速度
ZStack/BareMetal2Gateway/NetworkAllInPacketsByServiceType: 网络服务全部网卡入包数
ZStack/BareMetal2Gateway/NetworkAllInErrorsByServiceType: 网络服务全部网卡入错误数
ZStack/BareMetal2Gateway/NetworkAllOutBytesByServiceType: 网络服务全部网卡出速度
ZStack/BareMetal2Gateway/NetworkAllOutPacketsByServiceType: 网络服务全部网卡出包数
ZStack/BareMetal2Gateway/NetworkAllOutErrorsByServiceType: 网络服务全部网卡出错误数
ZStack/BareMetal2Gateway/NetworkConntrackCount: Conntrack连接数
ZStack/BareMetal2Gateway/NetworkConntrackInPercent: Conntrack已使用百分比
ZStack/BareMetal2Gateway/DiskTotalCapacityInBytes: 全部磁盘容量
ZStack/BareMetal2Gateway/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/BareMetal2Gateway/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/BareMetal2Gateway/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/BareMetal2Gateway/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/BareMetal2Gateway/DiskCapacityInBytes: 磁盘容量
ZStack/BareMetal2Gateway/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/BareMetal2Gateway/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/BareMetal2Gateway/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/BareMetal2Gateway/DiskZStackUsedCapacityInBytes: 云平台系统文件占用磁盘容量
ZStack/BareMetal2Gateway/DiskZStackUsedCapacityInPercent: 云平台系统文件占用磁盘容量百分比
ZStack/BareMetal2Gateway/DiskTransUsedCapacityInBytes: 客户业务数据占用磁盘容量
ZStack/BareMetal2Gateway/DiskTransUsedCapacityInPercent: 客户业务数据占用磁盘容量百分比
ZStack/BareMetal2Gateway/DiskRootUsedCapacityInBytes: 根盘已使用容量
ZStack/BareMetal2Gateway/DiskRootUsedCapacityInPercent: 根盘已使用容量百分比
ZStack/BareMetal2Gateway/DiskLatency: 磁盘IO时延
ZStack/BareMetal2Gateway/DiskLatencyWwid: 所有弹性裸金属网关对同一磁盘标识的IO平均时延
ZStack/BareMetal2Gateway/DiskWriteOpsWwid: 所有弹性裸金属网关对同一磁盘标识的写IOPS总量
ZStack/BareMetal2Gateway/DiskReadOpsWwid: 所有弹性裸金属网关对同一磁盘标识的读IOPS总量
ZStack/BareMetal2Gateway/DiskWriteBytesWwid: 所有弹性裸金属网关对同一磁盘标识的写带宽总量
ZStack/BareMetal2Gateway/DiskReadBytesWwid: 所有弹性裸金属网关对同一磁盘标识的读带宽总量
ZStack/BareMetal2Gateway/VolumeGroupCapacityInbytes: 卷组总容量
ZStack/BareMetal2Gateway/VolumeGroupFreeCapacityInbytes: 卷组可用容量
ZStack/BareMetal2Gateway/VolumeGroupUsedCapacityInPercent: 卷组已使用容量百分比
ZStack/BareMetal2Gateway/RaidState: RAID状态(0为正常，5为降级)
ZStack/BareMetal2Gateway/PhysicalDiskState: 物理磁盘状态
ZStack/BareMetal2Gateway/PhysicalDiskTemperature: 物理磁盘温度
ZStack/BareMetal2Gateway/PowerSupply: 供电单元状态（0为正常，其他为异常）
ZStack/BareMetal2Gateway/PowerSupplyCurrentOutputPower: 电源当前输出功率（单位为瓦特，w）
ZStack/BareMetal2Gateway/IpmiStatus: IPMI模块状态（0为正常，其他为异常）
ZStack/BareMetal2Gateway/PhysicalNetworkInterface: 物理网卡状态（0为正常，其他为异常，Speed单位为Mb/s，表示千兆/万兆网卡）
ZStack/BareMetal2Gateway/DiskXfsFragInPercent: XFS文件系统碎片化程度百分比
ZStack/BareMetal2Gateway/HostTotal: 弹性裸金属网关数量
ZStack/BareMetal2Gateway/ConnectedHostCount: 已连接弹性裸金属网关数量
ZStack/BareMetal2Gateway/ConnectedHostInPercent: 已连接弹性裸金属网关百分比
ZStack/BareMetal2Gateway/DisconnectedHostCount: 未连接弹性裸金属网关数量
ZStack/BareMetal2Gateway/DisconnectedHostInPercent: 未连接弹性裸金属网关百分比
ZStack/KVMHost/KVMHostTotal: KVM物理机数量
ZStack/KVMHost/KVMConnectedHostCount: 已连KVM接物理机数量
ZStack/KVMHost/KVMConnectedHostInPercent: 已连接KVM物理机百分比
ZStack/KVMHost/KVMDisconnectedHostCount: 未连接KVM物理机数量
ZStack/KVMHost/KVMDisconnectedHostInPercent: 未连接KVM物理机百分比
ZStack/KVMHost/PhysicalVolumeStatus: 物理卷活跃状态
ZStack/KVMHost/PhysicalVolumeState: 物理卷健康状态
ZStack/ESXHost/ESXHostTotal: ESX物理机数量
ZStack/ESXHost/ESXConnectedHostCount: 已连接ESX物理机数量
ZStack/ESXHost/ESXConnectedHostInPercent: 已连接ESX物理机百分比
ZStack/ESXHost/ESXDisconnectedHostCount: 未连接ESX物理机数量
ZStack/ESXHost/ESXDisconnectedHostInPercent: 未连接ESX物理机百分比
ZStack/ESXHost/PhysicalVolumeStatus: 物理卷活跃状态
ZStack/ESXHost/PhysicalVolumeState: 物理卷健康状态
ZStack/XDragonHost/XDragonHostTotal: 神龙物理机数量
ZStack/XDragonHost/XDragonConnectedHostCount: 已连接神龙物理机数量
ZStack/XDragonHost/XDragonConnectedHostInPercent: 已连接神龙物理机百分比
ZStack/XDragonHost/XDragonDisconnectedHostCount: 未连接神龙物理机数量
ZStack/XDragonHost/XDragonDisconnectedHostInPercent: 未连接神龙物理机百分比
ZStack/BareMetal2Gateway/Baremetal2GateWayTotal: 弹性裸金属网关节点数量
ZStack/BareMetal2Gateway/Baremetal2ConnectedGateWayCount: 已连接弹性裸金属网关节点数量
ZStack/BareMetal2Gateway/Baremetal2ConnectedGateWayInPercent: 已连接弹性裸金属网关节点百分比
ZStack/BareMetal2Gateway/Baremetal2DisconnectedGateWayCount: 未连接弹性裸金属网关节点数量
ZStack/BareMetal2Gateway/Baremetal2DisconnectedGateWayInPercent: 未连接弹性裸金属网关节点百分比
ZStack/Host/CPUCapacityTotal: 全部CPU数量
ZStack/Host/CPUUsedCapacityCount: 已使用CPU数量
ZStack/Host/CPULockedCapacityCount: 已禁用CPU数量
ZStack/Host/CPUUsedCapacityInPercent: 已使用CPU百分比
ZStack/Host/CPULockedCapacityInPercent: 已禁用CPU百分比
ZStack/Host/CPUAvailableCapacityCount: 可用CPU数量
ZStack/Host/CPUAvailableCapacityInPercent: 可用CPU百分比
ZStack/Host/CPUUsedCapacityPerHostCount: 物理机已使用CPU数量
ZStack/Host/CPUUsedCapacityPerHostInPercent: 物理机已使用CPU百分比
ZStack/Host/CPUAvailableCapacityPerHostCount: 物理机可用CPU数量
ZStack/Host/CPUAvailableCapacityPerHostInPercent: 物理机可用CPU百分比
ZStack/Host/MemoryCapacityTotal: 内存容量
ZStack/Host/MemoryUsedCapacityInBytes: 已使用内存容量
ZStack/Host/MemoryUsedCapacityInPercent: 已使用内存百分比
ZStack/Host/MemoryLockedCapacityInBytes: 已禁用内存容量
ZStack/Host/MemoryLockedCapacityInPercent: 已禁用内存容量百分比
ZStack/Host/MemoryAvailableCapacityInBytes: 剩余内存容量
ZStack/Host/MemoryAvailableCapacityInPercent: 剩余内存容量百分比
ZStack/Host/MemoryUsedCapacityPerHostInBytes: 物理机已使用内存容量
ZStack/Host/MemoryUsedCapacityPerHostInPercent: 物理机已使用内存容量百分比
ZStack/Host/MemoryAvailableCapacityPerHostInBytes: 物理机可用内存容量
ZStack/Host/MemoryAvailableCapacityPerHostInPercent: 物理机可用内存容量百分比
ZStack/Host/V2VAvailableCapacityInBytes: v2v迁移主机容量
ZStack/Host/V2VAvailableCapacityInPercent: v2v迁移主机容量百分比
ZStack/Host/V2VUsedCapacityInBytes: v2v迁移主机己用容量
ZStack/Host/V2VUsedCapacityInPercent: v2v迁移主机己用容量百分比
ZStack/Host/FanSpeedRpm: 物理机风扇转速
ZStack/Host/FanSpeedState: 物理机风扇状态（0为正常，其他为异常）
ZStack/Host/CpuTemperature: 物理机CPU温度
ZStack/Host/CpuStatus: 物理机CPU在位状态（0为在位且正常使用，其他为异常）
ZStack/Host/SSDLifeLeft: 物理机SSD盘剩余寿命
ZStack/Host/SSDTemperature: 物理机SSD盘温度
ZStack/Host/PhysicalMemoryStatus: 物理机内存条状态
ZStack/Host/BlockDeviceUsedCapacityInBytes: 物理机块设备已使用容量
ZStack/Host/BlockDeviceUsedCapacityInPercent: 物理机块设备已使用容量百分比
ZStack/Host/HostPhysicalMemoryEccErrorTriggered: 物理机内存ECC错误已触发
ZStack/Host/HostPhysicalCpuStatusAbnormal: 物理机CPU状态异常
ZStack/Host/HostPhysicalMemoryStatusAbnormal: 物理机内存条状态异常
ZStack/Host/HostPhysicalFanStatusAbnormal: 物理机风扇状态异常
ZStack/Host/HostPhysicalPowerSupplyStatusAbnormal: 物理机电源插槽状态异常
ZStack/Host/HostPhysicalDiskStatusAbnormal: 物理机磁盘状态异常
ZStack/Host/HostPhysicalDiskInsertTriggered: 物理机磁盘插入
ZStack/Host/HostPhysicalDiskRemoveTriggered: 物理机磁盘拔出
ZStack/Host/PhysicalVolumeStatus: 物理卷活跃状态
ZStack/Host/PhysicalVolumeState: 物理卷健康状态
ZStack/XDragonHost/CPUCapacityTotal: 全部CPU数量
ZStack/XDragonHost/CPUUsedCapacityCount: 已使用CPU数量
ZStack/XDragonHost/CPULockedCapacityCount: 已禁用CPU数量
ZStack/XDragonHost/CPUUsedCapacityInPercent: 已使用CPU百分比
ZStack/XDragonHost/CPULockedCapacityInPercent: 已禁用CPU百分比
ZStack/XDragonHost/CPUAvailableCapacityCount: 可用CPU数量
ZStack/XDragonHost/CPUAvailableCapacityInPercent: 可用CPU百分比
ZStack/XDragonHost/CPUUsedCapacityPerHostCount: 神龙物理机已使用CPU数量
ZStack/XDragonHost/CPUUsedCapacityPerHostInPercent: 神龙物理机已使用CPU百分比
ZStack/XDragonHost/CPUAvailableCapacityPerHostCount: 神龙物理机可用CPU数量
ZStack/XDragonHost/CPUAvailableCapacityPerHostInPercent: 神龙物理机可用CPU百分比
ZStack/XDragonHost/MemoryCapacityTotal: 内存容量
ZStack/XDragonHost/MemoryUsedCapacityInBytes: 已使用内存容量
ZStack/XDragonHost/MemoryUsedCapacityInPercent: 已使用内存百分比
ZStack/XDragonHost/MemoryLockedCapacityInBytes: 已禁用内存容量
ZStack/XDragonHost/MemoryLockedCapacityInPercent: 已禁用内存容量百分比
ZStack/XDragonHost/MemoryAvailableCapacityInBytes: 剩余内存容量
ZStack/XDragonHost/MemoryAvailableCapacityInPercent: 剩余内存容量百分比
ZStack/XDragonHost/MemoryUsedCapacityPerHostInBytes: 物理机已使用内存容量
ZStack/XDragonHost/MemoryUsedCapacityPerHostInPercent: 物理机已使用内存容量百分比
ZStack/XDragonHost/MemoryAvailableCapacityPerHostInBytes: 物理机可用内存容量
ZStack/XDragonHost/MemoryAvailableCapacityPerHostInPercent: 物理机可用内存容量百分比
ZStack/XDragonHost/V2VAvailableCapacityInBytes: v2v迁移主机容量
ZStack/XDragonHost/V2VAvailableCapacityInPercent: v2v迁移主机容量百分比
ZStack/XDragonHost/V2VUsedCapacityInBytes: v2v迁移主机己用容量
ZStack/XDragonHost/V2VUsedCapacityInPercent: v2v迁移主机己用容量百分比
ZStack/XDragonHost/FanSpeedRpm: 神龙物理机风扇转速
ZStack/XDragonHost/FanSpeedState: 神龙物理机风扇状态（0为正常，其他为异常）
ZStack/XDragonHost/CpuTemperature: 神龙物理机CPU温度
ZStack/XDragonHost/CpuStatus: 神龙物理机CPU在位状态（0为在位且正常使用，其他为异常）
ZStack/XDragonHost/SSDLifeLeft: 神龙物理机SSD剩余寿命
ZStack/XDragonHost/SSDTemperature: 神龙物理机SSD盘温度
ZStack/XDragonHost/PhysicalMemoryStatus: 神龙物理机内存条状态
ZStack/XDragonHost/HostPhysicalMemoryEccErrorTriggered: 神龙物理机内存ECC错误已触发
ZStack/XDragonHost/HostPhysicalCpuStatusAbnormal: 神龙物理机CPU状态异常
ZStack/XDragonHost/HostPhysicalMemoryStatusAbnormal: 神龙物理机内存条状态异常
ZStack/XDragonHost/HostPhysicalFanStatusAbnormal: 神龙物理机风扇状态异常
ZStack/XDragonHost/HostPhysicalPowerSupplyStatusAbnormal: 神龙物理机电源插槽状态异常
ZStack/XDragonHost/HostPhysicalDiskStatusAbnormal: 神龙物理机磁盘状态异常
ZStack/XDragonHost/HostPhysicalDiskInsertTriggered: 神龙物理机磁盘插入
ZStack/XDragonHost/HostPhysicalDiskRemoveTriggered: 神龙物理机磁盘拔出
ZStack/XDragonHost/BlockDeviceUsedCapacityInBytes: 神龙物理机块设备已使用容量
ZStack/XDragonHost/BlockDeviceUsedCapacityInPercent: 神龙物理机块设备已使用容量百分比
ZStack/XDragonHost/PhysicalVolumeStatus: 物理卷活跃状态
ZStack/XDragonHost/PhysicalVolumeState: 物理卷健康状态
ZStack/KVMHost/CPUCapacityTotal: 全部CPU数量
ZStack/KVMHost/CPUUsedCapacityCount: 已使用CPU数量
ZStack/KVMHost/CPULockedCapacityCount: 已禁用CPU数量
ZStack/KVMHost/CPUUsedCapacityInPercent: 已使用CPU百分比
ZStack/KVMHost/CPULockedCapacityInPercent: 已禁用CPU百分比
ZStack/KVMHost/CPUAvailableCapacityCount: 可用CPU数量
ZStack/KVMHost/CPUAvailableCapacityInPercent: 可用CPU百分比
ZStack/KVMHost/CPUUsedCapacityPerHostCount: KVM物理机已使用CPU数量
ZStack/KVMHost/CPUUsedCapacityPerHostInPercent: KVM物理机已使用CPU百分比
ZStack/KVMHost/CPUAvailableCapacityPerHostCount: KVM物理机可用CPU数量
ZStack/KVMHost/CPUAvailableCapacityPerHostInPercent: KVM物理机可用CPU百分比
ZStack/KVMHost/MemoryCapacityTotal: 内存容量
ZStack/KVMHost/MemoryUsedCapacityInBytes: 已使用内存容量
ZStack/KVMHost/MemoryUsedCapacityInPercent: 已使用内存百分比
ZStack/KVMHost/MemoryLockedCapacityInBytes: 已禁用内存容量
ZStack/KVMHost/MemoryLockedCapacityInPercent: 已禁用内存容量百分比
ZStack/KVMHost/MemoryAvailableCapacityInBytes: 剩余内存容量
ZStack/KVMHost/MemoryAvailableCapacityInPercent: 剩余内存容量百分比
ZStack/KVMHost/MemoryUsedCapacityPerHostInBytes: KVM物理机已使用内存容量
ZStack/KVMHost/MemoryUsedCapacityPerHostInPercent: KVM物理机已使用内存容量百分比
ZStack/KVMHost/MemoryAvailableCapacityPerHostInBytes: KVM物理机可用内存容量
ZStack/KVMHost/MemoryAvailableCapacityPerHostInPercent: KVM物理机可用内存容量百分比
ZStack/KVMHost/V2VAvailableCapacityInBytes: v2v迁移主机容量
ZStack/KVMHost/V2VAvailableCapacityInPercent: v2v迁移主机容量百分比
ZStack/KVMHost/V2VUsedCapacityInBytes: v2v迁移主机己用容量
ZStack/KVMHost/V2VUsedCapacityInPercent: v2v迁移主机己用容量百分比
ZStack/KVMHost/FanSpeedRpm: KVM物理机风扇转速
ZStack/KVMHost/FanSpeedState: KVM物理机风扇状态（0为正常，其他为异常）
ZStack/KVMHost/CpuTemperature: KVM物理机CPU温度
ZStack/KVMHost/CpuStatus: KVM物理机CPU在位状态（0为在位且正常使用，其他为异常）
ZStack/KVMHost/SSDLifeLeft: KVM物理机SSD剩余寿命
ZStack/KVMHost/SSDTemperature: KVM物理机SSD盘温度
ZStack/KVMHost/PhysicalMemoryStatus: KVM物理机内存条状态
ZStack/KVMHost/HostPhysicalMemoryEccErrorTriggered: KVM物理机内存ECC错误已触发
ZStack/KVMHost/HostPhysicalCpuStatusAbnormal: KVM物理机CPU状态异常
ZStack/KVMHost/HostPhysicalMemoryStatusAbnormal: KVM物理机内存条状态异常
ZStack/KVMHost/HostPhysicalFanStatusAbnormal: KVM物理机风扇状态异常
ZStack/KVMHost/HostPhysicalPowerSupplyStatusAbnormal: KVM物理机电源插槽状态异常
ZStack/KVMHost/HostPhysicalDiskStatusAbnormal: KVM物理机磁盘状态异常
ZStack/KVMHost/HostPhysicalDiskInsertTriggered: KVM物理机磁盘插入
ZStack/KVMHost/HostPhysicalDiskRemoveTriggered: KVM物理机磁盘拔出
ZStack/KVMHost/BlockDeviceUsedCapacityInBytes: KVM物理机块设备已使用容量
ZStack/KVMHost/BlockDeviceUsedCapacityInPercent: KVM物理机块设备已使用容量百分比
ZStack/ESXHost/CPUCapacityTotal: 全部CPU数量
ZStack/ESXHost/CPUUsedCapacityCount: 已使用CPU数量
ZStack/ESXHost/CPULockedCapacityCount: 已禁用CPU数量
ZStack/ESXHost/CPUUsedCapacityInPercent: 已使用CPU百分比
ZStack/ESXHost/CPULockedCapacityInPercent: 已禁用CPU百分比
ZStack/ESXHost/CPUAvailableCapacityCount: 可用CPU数量
ZStack/ESXHost/CPUAvailableCapacityInPercent: 可用CPU百分比
ZStack/ESXHost/CPUUsedCapacityPerHostCount: ESX物理机已使用CPU数量
ZStack/ESXHost/CPUUsedCapacityPerHostInPercent: ESX物理机已使用CPU百分比
ZStack/ESXHost/CPUAvailableCapacityPerHostCount: ESX物理机可用CPU数量
ZStack/ESXHost/CPUAvailableCapacityPerHostInPercent: ESX物理机可用CPU百分比
ZStack/ESXHost/MemoryCapacityTotal: 内存容量
ZStack/ESXHost/MemoryUsedCapacityInBytes: 已使用内存容量
ZStack/ESXHost/MemoryUsedCapacityInPercent: 已使用内存百分比
ZStack/ESXHost/MemoryLockedCapacityInBytes: 已禁用内存容量
ZStack/ESXHost/MemoryLockedCapacityInPercent: 已禁用内存容量百分比
ZStack/ESXHost/MemoryAvailableCapacityInBytes: 剩余内存容量
ZStack/ESXHost/MemoryAvailableCapacityInPercent: 剩余内存容量百分比
ZStack/ESXHost/MemoryUsedCapacityPerHostInBytes: ESX物理机已使用内存容量
ZStack/ESXHost/MemoryUsedCapacityPerHostInPercent: ESX物理机已使用内存容量百分比
ZStack/ESXHost/MemoryAvailableCapacityPerHostInBytes: ESX物理机可用内存容量
ZStack/ESXHost/MemoryAvailableCapacityPerHostInPercent: ESX物理机可用内存容量百分比
ZStack/ESXHost/V2VAvailableCapacityInBytes: v2v迁移主机容量
ZStack/ESXHost/V2VAvailableCapacityInPercent: v2v迁移主机容量百分比
ZStack/ESXHost/V2VUsedCapacityInBytes: v2v迁移主机己用容量
ZStack/ESXHost/V2VUsedCapacityInPercent: v2v迁移主机己用容量百分比
ZStack/ESXHost/FanSpeedRpm: ESX物理机风扇转速
ZStack/ESXHost/FanSpeedState: ESX物理机风扇状态（0为正常，其他为异常）
ZStack/ESXHost/CpuTemperature: ESX物理机CPU温度
ZStack/ESXHost/CpuStatus: ESX物理机CPU在位状态（0为在位且正常使用，其他为异常）
ZStack/ESXHost/SSDLifeLeft: ESX物理机SSD剩余寿命
ZStack/ESXHost/SSDTemperature: ESX物理机SSD盘温度
ZStack/ESXHost/PhysicalMemoryStatus: ESX物理机内存条状态
ZStack/ESXHost/HostPhysicalMemoryEccErrorTriggered: ESX物理机内存ECC错误已触发
ZStack/ESXHost/HostPhysicalCpuStatusAbnormal: ESX物理机CPU状态异常
ZStack/ESXHost/HostPhysicalMemoryStatusAbnormal: ESX物理机内存条状态异常
ZStack/ESXHost/HostPhysicalFanStatusAbnormal: ESX物理机风扇状态异常
ZStack/ESXHost/HostPhysicalPowerSupplyStatusAbnormal: ESX物理机电源插槽状态异常
ZStack/ESXHost/HostPhysicalDiskStatusAbnormal: ESX物理机磁盘状态异常
ZStack/ESXHost/HostPhysicalDiskInsertTriggered: ESX物理机磁盘插入
ZStack/ESXHost/HostPhysicalDiskRemoveTriggered: ESX物理机磁盘拔出
ZStack/ESXHost/BlockDeviceUsedCapacityInBytes: ESX物理机块设备已使用容量
ZStack/ESXHost/BlockDeviceUsedCapacityInPercent: ESX物理机块设备已使用容量百分比
ZStack/BareMetal2Gateway/CPUCapacityTotal: 全部CPU数量
ZStack/BareMetal2Gateway/CPUUsedCapacityCount: 已使用CPU数量
ZStack/BareMetal2Gateway/CPULockedCapacityCount: 已禁用CPU数量
ZStack/BareMetal2Gateway/CPUUsedCapacityInPercent: 已使用CPU百分比
ZStack/BareMetal2Gateway/CPULockedCapacityInPercent: 已禁用CPU百分比
ZStack/BareMetal2Gateway/CPUAvailableCapacityCount: 可用CPU数量
ZStack/BareMetal2Gateway/CPUAvailableCapacityInPercent: 可用CPU百分比
ZStack/BareMetal2Gateway/CPUUsedCapacityPerHostCount: 弹性裸金属网关已使用CPU数量
ZStack/BareMetal2Gateway/CPUUsedCapacityPerHostInPercent: 弹性裸金属网关已使用CPU百分比
ZStack/BareMetal2Gateway/CPUAvailableCapacityPerHostCount: 弹性裸金属网关可用CPU数量
ZStack/BareMetal2Gateway/CPUAvailableCapacityPerHostInPercent: 弹性裸金属网关可用CPU百分比
ZStack/BareMetal2Gateway/MemoryCapacityTotal: 内存容量
ZStack/BareMetal2Gateway/MemoryUsedCapacityInBytes: 已使用内存容量
ZStack/BareMetal2Gateway/MemoryUsedCapacityInPercent: 已使用内存百分比
ZStack/BareMetal2Gateway/MemoryLockedCapacityInBytes: 已禁用内存容量
ZStack/BareMetal2Gateway/MemoryLockedCapacityInPercent: 已禁用内存容量百分比
ZStack/BareMetal2Gateway/MemoryAvailableCapacityInBytes: 剩余内存容量
ZStack/BareMetal2Gateway/MemoryAvailableCapacityInPercent: 剩余内存容量百分比
ZStack/BareMetal2Gateway/MemoryUsedCapacityPerHostInBytes: 弹性裸金属网关已使用内存容量
ZStack/BareMetal2Gateway/MemoryUsedCapacityPerHostInPercent: 弹性裸金属网关已使用内存容量百分比
ZStack/BareMetal2Gateway/MemoryAvailableCapacityPerHostInBytes: 弹性裸金属网关可用内存容量
ZStack/BareMetal2Gateway/MemoryAvailableCapacityPerHostInPercent: 弹性裸金属网关可用内存容量百分比
ZStack/BareMetal2Gateway/V2VAvailableCapacityInBytes: v2v迁移主机容量
ZStack/BareMetal2Gateway/V2VAvailableCapacityInPercent: v2v迁移主机容量百分比
ZStack/BareMetal2Gateway/V2VUsedCapacityInBytes: v2v迁移主机己用容量
ZStack/BareMetal2Gateway/V2VUsedCapacityInPercent: v2v迁移主机己用容量百分比
ZStack/BareMetal2Gateway/FanSpeedRpm: 弹性裸金属网关风扇转速
ZStack/BareMetal2Gateway/FanSpeedState: 弹性裸金属网关风扇状态（0为正常，其他为异常）
ZStack/BareMetal2Gateway/CpuTemperature: 弹性裸金属网关CPU温度
ZStack/BareMetal2Gateway/CpuStatus: 弹性裸金属网关CPU在位状态（0为在位且正常使用，其他为异常）
ZStack/BareMetal2Gateway/SSDLifeLeft: 弹性裸金属网关SSD剩余寿命
ZStack/BareMetal2Gateway/SSDTemperature: 弹性裸金属网关SSD盘温度
ZStack/BareMetal2Gateway/PhysicalMemoryStatus: 弹性裸金属网关内存条状态
ZStack/BareMetal2Gateway/HostPhysicalMemoryEccErrorTriggered: 弹性裸金属网关内存ECC错误已触发
ZStack/BareMetal2Gateway/HostPhysicalCpuStatusAbnormal: 弹性裸金属网关CPU状态异常
ZStack/BareMetal2Gateway/HostPhysicalMemoryStatusAbnormal: 弹性裸金属网关内存条状态异常
ZStack/BareMetal2Gateway/HostPhysicalFanStatusAbnormal: 弹性裸金属网关风扇状态异常
ZStack/BareMetal2Gateway/HostPhysicalPowerSupplyStatusAbnormal: 弹性裸金属网关电源插槽状态异常
ZStack/BareMetal2Gateway/HostPhysicalDiskStatusAbnormal: 弹性裸金属网关磁盘状态异常
ZStack/BareMetal2Gateway/HostPhysicalDiskInsertTriggered: 弹性裸金属网关磁盘插入
ZStack/BareMetal2Gateway/HostPhysicalDiskRemoveTriggered: 弹性裸金属网关磁盘拔出
ZStack/BareMetal2Gateway/BlockDeviceUsedCapacityInBytes: 弹性裸金属网关块设备已使用容量
ZStack/BareMetal2Gateway/BlockDeviceUsedCapacityInPercent: 弹性裸金属网关块设备已使用容量百分比
ZStack/VIP/VIPInBoundTrafficInBytes: 虚拟IP下行网络流量
ZStack/VIP/VIPInBoundTrafficInPackages: 虚拟IP下行网络包数
ZStack/VIP/VIPOutBoundTrafficInBytes: 虚拟IP上行网络流量
ZStack/VIP/VIPOutBoundTrafficInPackages: 虚拟IP上行网络包数
ZStack/VIP/VIPTotalInBytesIn5Min: 5分钟虚拟IP下行字节量
ZStack/VIP/VIPTotalInPacketsIn5Min: 5分钟虚拟IP下行字节量
ZStack/VIP/VIPTotalOutBytesIn5Min: 5分钟虚拟IP上行字节量
ZStack/VIP/VIPTotalOutPacketsIn5Min: 5分钟虚拟IP上行字节量
ZStack/VIP/VIPTotalInBytesIn1Min: 1分钟虚拟IP下行包量
ZStack/VIP/VIPTotalInPacketsIn1Min: 1分钟虚拟IP下行包量
ZStack/VIP/VIPTotalOutBytesIn1Min: 1分钟虚拟IP上行包量
ZStack/VIP/VIPTotalOutPacketsIn1Min: 1分钟虚拟IP上行包量
ZStack/IPSec/IPSecConnectionBytes: IPSec连接网络流量
ZStack/IPSec/IPSecConnectionPackets: IPSec连接网络包数量
ZStack/PrimaryStorage/TotalCapacityInBytes: 主存储全部容量
ZStack/PrimaryStorage/TotalAvailableCapacityInBytes: 主存储全部可用容量
ZStack/PrimaryStorage/TotalAvailableCapacityInPercent: 主存储全部可用容量百分比
ZStack/PrimaryStorage/TotalUsedCapacityInBytes: 主存储全部已用容量
ZStack/PrimaryStorage/TotalUsedCapacityInPercent: 主存储全部已用容量百分比
ZStack/PrimaryStorage/TotalLockedCapacityInBytes: 主存储全部已禁用容量
ZStack/PrimaryStorage/TotalLockedCapacityInPercent: 主存储全部已禁用容量百分比
ZStack/PrimaryStorage/AvailableCapacityInBytes: 主存储可用容量
ZStack/PrimaryStorage/AvailableCapacityInPercent: 主存储可用容量百分比
ZStack/PrimaryStorage/UsedCapacityInBytes: 主存储已用容量
ZStack/PrimaryStorage/UsedCapacityInPercent: 已用容量百分比
ZStack/PrimaryStorage/AvailablePhysicalCapacityInBytes: 主存储可用物理容量
ZStack/PrimaryStorage/AvailablePhysicalCapacityInPercent: 主存储可用物理容量百分比
ZStack/PrimaryStorage/UsedPhysicalCapacityInBytes: 主存储已用物理容量
ZStack/PrimaryStorage/TotalPhysicalCapacityInBytes: 主存储总容量 
ZStack/PrimaryStorage/UsedPhysicalCapacityInPercent: 主存储已用物理容量百分比
ZStack/PrimaryStorage/RootVolumeCount: 根云盘数量
ZStack/PrimaryStorage/DataVolumeCount: 数据云盘数量
ZStack/PrimaryStorage/SnapshotCount: 快照数量
ZStack/PrimaryStorage/SharedBlockStateAbnormal: SharedBlock主存储共享块状态异常
ZStack/MN/TimeNeededToSyncDB: 双管理节点数据库不同步
ZStack/MN/DbFencerIpReachable: 仲裁IP不可达
ZStack/MN/MNProgressLasts: 管理节点运行时长
ZStack/MN/MNProgressSocketNum: 管理节点打开的socket数
ZStack/MN/MNProgressGCStatus: 管理节点的Java GC情况
ZStack/MN/MNProgressExpends: 管理节点的开销
ZStack/MN/MNProgressMsgNum: 管理节点处理消息数
ZStack/MN/MNThreadPoolStatus: 管理节点线程池情况
ZStack/MN/MNQueueStatus: 管理节点队列情况
ZStack/MN/MysqlLasts: mysql进程运行时长
ZStack/MN/MysqlProgressExpends: mysql进程开销
ZStack/MN/MysqlSlowQuery: mysql慢SQL数
ZStack/MN/MysqlProcessLists: mysqlprocesslist情况
ZStack/MN/MysqlTableSize: mysql表大小
ZStack/MN/MysqlQuerys: mysql读请求数
ZStack/MN/MysqlTransactions: mysql写请求数
ZStack/MN/MysqlOpenttables: mysql打开表及锁情况
ZStack/MN/MysqlQueryCache: mysql读cache命中
ZStack/MN/ErrorCodes: 管理节点错误数
ZStack/Prometheus/PrometheusLasts: prometheus进程运行时长
ZStack/Prometheus/PrometheusSocketNum: prometheus进程打开socket数
ZStack/Prometheus/PrometheusExpends: prometheus进程开销
ZStack/Prometheus/PrometheusDiskSpace: prometheus进程磁盘空间
ZStack/Prometheus/PrometheusQueries: prometheus请求数
ZStack/Prometheus/PrometheusDeltaQueries: prometheus单位时间请求数
ZStack/Prometheus/InfluxdbLasts: influxdb进程运行时长
ZStack/Prometheus/InfluxdbSocketNum: influxdb进程打开socket数
ZStack/Prometheus/InfluxdbExpends: influxdb进程开销
ZStack/Prometheus/InfluxdbDiskSpace: influxdb进程磁盘空间
ZStack/Prometheus/InfluxdbQueries: influxdb请求数
ZStack/Prometheus/InfluxdbDeltaQueries: influxdb单位时间请求数
ZStack/Image/TotalImageCount: 镜像总数
ZStack/Image/ReadyImageCount: 可用镜像总数
ZStack/Image/ReadyImageInPercent: 可用镜像百分比
ZStack/Image/RootVolumeTemplateCount: 根云盘镜像数量
ZStack/Image/RootVolumeTemplateInPercent: 根云盘镜像百分比
ZStack/Image/DataVolumeTemplateCount: 数据云盘镜像数量
ZStack/Image/DataVolumeTemplateInPercent: 数据云盘镜像百分比
ZStack/Image/ISOCount: ISO镜像数量
ZStack/Image/ISOInPercent: ISO镜像百分比
ZStack/REST/SyncAPIs: rest同步API
ZStack/REST/ASyncAPIs: rest异步API
ZStack/REST/IncreaseSyncAPIs: rest同步API增加量
ZStack/REST/IncreaseASyncAPIs: rest异步API增加量
ZStack/REST/SyncRests: rest同步调用量
ZStack/REST/ASyncRests: rest异步调用量
ZStack/REST/IncreaseSyncRests: rest同步调用量增量数
ZStack/REST/IncreaseASyncRests: rest异步调用量增加数
ZStack/VM/CPUUsedUtilization: CPU使用率
ZStack/VM/CPUAverageUsedUtilization: CPU平均使用率
ZStack/VM/CPUIdleUtilization: CPU空闲率
ZStack/VM/CPUAllUsedUtilization: 全部CPU使用率
ZStack/VM/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/VM/OperatingSystemCPUSystemUtilization: 内部监控CPU系统进程使用率
ZStack/VM/OperatingSystemCPUUserUtilization: 内部监控CPU用户进程使用率
ZStack/VM/OperatingSystemCPUWaitUtilization: 内部监控CPU等待IO完成使用率
ZStack/VM/OperatingSystemCPUIdleUtilization: 内部监控CPU空闲率
ZStack/VM/OperatingSystemCPUUsedUtilization: 内部监控CPU使用率
ZStack/VM/OperatingSystemCPUAverageSystemUtilization: 内部监控CPU系统进程平均使用率
ZStack/VM/OperatingSystemCPUAverageUserUtilization: 内部监控CPU用户进程平均使用率
ZStack/VM/OperatingSystemCPUAverageWaitUtilization: 内部监控CPU等待IO完成平均使用率
ZStack/VM/OperatingSystemCPUAverageIdleUtilization: 内部监控CPU平均空闲率
ZStack/VM/OperatingSystemCPUAverageUsedUtilization: 内部监控CPU平均使用率
ZStack/VM/DiskReadOps: 磁盘读IOPS
ZStack/VM/DiskAllReadOps: 全部磁盘读IOPS
ZStack/VM/DiskWriteOps: 磁盘写IOPS
ZStack/VM/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/VM/DiskReadBytes: 磁盘读速度
ZStack/VM/DiskAllReadBytes: 全部磁盘读速度
ZStack/VM/DiskWriteBytes: 磁盘写速度
ZStack/VM/DiskAllWriteBytes: 全部磁盘写速度
ZStack/VM/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/VM/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/VM/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/VM/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/VM/DiskFreeCapacityInBytes: 磁盘剩余容量
ZStack/VM/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/VM/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/VM/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/VM/NetworkInBytes: 网卡入速度
ZStack/VM/NetworkAllInBytes: 全部网卡入速度
ZStack/VM/NetworkInPackets: 网卡入包数
ZStack/VM/NetworkAllInPackets: 全部网卡入包数
ZStack/VM/NetworkInErrors: 网卡入错误数
ZStack/VM/NetworkInDropped: 网卡入丢包数
ZStack/VM/NetworkAllInErrors: 全部网卡入错误数
ZStack/VM/NetworkInDroppedBytes: 网卡入丢包数
ZStack/VM/NetworkAllInDroppedBytes: 全部网卡入丢包数
ZStack/VM/NetworkOutBytes: 网卡出速度
ZStack/VM/NetworkAllOutBytes: 全部网卡出速度
ZStack/VM/NetworkOutPackets: 网卡出包数
ZStack/VM/NetworkAllOutPackets: 全部网卡出包数
ZStack/VM/NetworkOutErrors: 网卡出错误数
ZStack/VM/NetworkOutDropped: 网卡出丢包数
ZStack/VM/NetworkAllOutErrors: 全部网卡出错误数
ZStack/VM/NetworkOutDroppedBytes: 网卡出丢包数
ZStack/VM/NetworkAllOutDroppedBytes: 全部网卡出丢包数
ZStack/VM/TotalNetworkInBytesIn5Min: 5分钟网卡入字节量
ZStack/VM/TotalNetworkInPacketsIn5Min: 5分钟网卡入包量
ZStack/VM/TotalNetworkOutBytesIn5Min: 5分钟网卡出字节量
ZStack/VM/TotalNetworkOutPacketsIn5Min: 5分钟网卡出包量
ZStack/VM/TotalNetworkInBytesIn1Min: 1分钟网卡入字节量
ZStack/VM/TotalNetworkInPacketsIn1Min: 1分钟网卡入包量
ZStack/VM/TotalNetworkOutBytesIn1Min: 1分钟网卡出字节量
ZStack/VM/TotalNetworkOutPacketsIn1Min: 1分钟网卡出包量
ZStack/VM/MemoryFreeBytes: 内存空闲容量
ZStack/VM/MemoryFreeInPercent: 内存空闲百分比
ZStack/VM/MemoryUsedBytes: 内存已用容量
ZStack/VM/MemoryUsedInPercent: 内存已用百分比
ZStack/VM/OperatingSystemMemoryTotalBytes: 内部监控全部内存总容量
ZStack/VM/OperatingSystemMemoryFreeBytes: 内部监控内存空闲容量
ZStack/VM/OperatingSystemMemoryUsedBytes: 内部监控内存已用容量
ZStack/VM/OperatingSystemMemoryAvailableBytes: 内部监控内存可用容量
ZStack/VM/OperatingSystemMemoryFreePercent: 内部监控内存空闲容量百分比
ZStack/VM/OperatingSystemMemoryUsedPercent: 内部监控内存已用容量百分比
ZStack/VM/ZWatchAgentVersion: zwatch客户端版本
ZStack/VM/AgentLastResponseTimestampDelta: zwatch客户端上次响应时间戳变化量
ZStack/VM/VRouterCPUSystemUtilization: VPC路由器CPU系统占用率
ZStack/VM/VRouterCPUUserUtilization: VPC路由器CPU用户占用率
ZStack/VM/VRouterCPUWaitUtilization: VPC路由器CPU IO等待占用率
ZStack/VM/VRouterCPUIdleUtilization: VPC路由器CPU空闲率
ZStack/VM/VRouterCPUUsedUtilization: VPC路由器CPU使用率
ZStack/VM/VRouterCPUAverageSystemUtilization: VPC路由器CPU平均系统占用率
ZStack/VM/VRouterCPUAverageUserUtilization: VPC路由器CPU平均用户占用率
ZStack/VM/VRouterCPUAverageWaitUtilization: VPC路由器CPU平均IO等待占用率
ZStack/VM/VRouterCPUAverageIdleUtilization: VPC路由器CPU平均空闲率
ZStack/VM/VRouterCPUAverageUsedUtilization: VPC路由器CPU平均使用率
ZStack/VM/VRouterDiskAllFreeCapacityInBytes: VPC路由器全部磁盘可用字节数
ZStack/VM/VRouterDiskAllFreeCapacityInPercent: VPC路由器全部磁盘百分比
ZStack/VM/VRouterDiskAllUsedCapacityInBytes: VPC路由器全部磁盘使用字节数
ZStack/VM/VRouterDiskAllUsedCapacityInPercent: VPC路由器全部磁盘使用百分比
ZStack/VM/VRouterDiskFreeCapacityInBytes: VPC路由器磁盘可用字节数
ZStack/VM/VRouterDiskFreeCapacityInPercent: VPC路由器磁盘百分比
ZStack/VM/VRouterDiskUsedCapacityInBytes: VPC路由器磁盘使用字节数
ZStack/VM/VRouterDiskUsedCapacityInPercent: VPC路由器磁盘使用百分比
ZStack/VM/VRouterMemoryTotalBytes: VPC路由器内存总字节数
ZStack/VM/VRouterMemoryFreeBytes: VPC路由器内存空余节数
ZStack/VM/VRouterMemoryUsedBytes: VPC路由器内存已使用节数
ZStack/VM/VRouterMemoryAvailableBytes: VPC路由器内存可用节数
ZStack/VM/VRouterMemoryFreePercent: VPC路由器内存空闲百分比
ZStack/VM/VRouterMemoryUsedPercent:  VPC路由器内存使用百分比
ZStack/VM/CPUOccupiedByVm: vm占用物理机的cpu开销
ZStack/VM/MemoryOccupiedByVm: vm占用物理机的内存开销
ZStack/VM/TotalVMCount: 云主机数量
ZStack/VM/RunningVMCount: 运行云主机数量
ZStack/VM/RunningVMInPercent: 运行云主机百分比
ZStack/VM/StoppedVMCount: 停止云主机数量
ZStack/VM/StoppedVMInPercent: 停止云主机百分比
ZStack/VM/OtherStateVMCount: 其他状态云主机数量
ZStack/VM/OtherStateVMInPercent: 其他状态云主机百分比
ZStack/VM/VmAbnormalLifeCycleDetected: 发现云主机异常生命周期变化
ZStack/VM/VmCrash: 云主机崩溃
ZStack/VM/ZWatchAgentFeaturePvpanic: 云主机崩溃检查模块运行情况
ZStack/VM/PVPanicEnableInDomainXML: 云主机崩溃对应的物理机支持情况
ZStack/L3Network/TotalAvailableIPCount: 全部可用IP数
ZStack/L3Network/TotalAvailableIPInPercent: 全部可用IP百分比
ZStack/L3Network/TotalUsedIPCount: 全部已用IP数
ZStack/L3Network/TotalUsedIPInPercent: 全部已用IP百分比
ZStack/L3Network/TotalLockedIPCount: 全部已禁用IP数
ZStack/L3Network/TotalLockedIPInPercent: 全部已禁用IP百分比
ZStack/L3Network/AvailableIPCount: 可用IP数
ZStack/L3Network/AvailableIPInPercent: 可用IP百分比
ZStack/L3Network/UsedIPCount: 已用IP数
ZStack/L3Network/UsedIPInPercent: 已用IP百分比
ZStack/Volume/TotalVolumeCount: 云盘总数
ZStack/Volume/RootVolumeCount: 根云盘总数
ZStack/Volume/RootVolumeInPercent: 根云盘百分比
ZStack/Volume/DataVolumeCount: 数据云盘总数
ZStack/Volume/DataVolumeInPercent: 数据云盘百分比
ZStack/Volume/ReadyDataVolumeCount: 可用数据云盘总数
ZStack/Volume/ReadyDataVolumeInPercent: 可用数据云盘百分比
ZStack/Volume/TotalVolumeSnapshotCount: 云盘快照总数
ZStack/Volume/RootVolumeSnapshotCount: 根云盘快照总数
ZStack/Volume/RootVolumeSnapshotInPercent: 根云盘快照百分比
ZStack/Volume/DataVolumeSnapshotCount: 数据云盘快照总数
ZStack/Volume/DataVolumeSnapshotInPercent: 数据云盘快照百分比
ZStack/Volume/VolumeActualSizeInPercent: 云盘使用容量百分比
ZStack/Volume/VolumeXfsFragCount: 云盘碎片程度（Ext总数）
ZStack/License/LicenseEnabledDays: 许可证有效期(天)
ZStack/LoadBalancer/LoadBalancerBackendStatus: 负载均衡监听器健康状态（单个网卡，1表示正常，0表示异常)
ZStack/LoadBalancer/LoadBalancerBackendSessionNumber: 负载均衡监听器活跃连接数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendTrafficInBytes: 负载均衡监听器流入流量（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendTrafficOutBytes: 负载均衡监听器流出流量（单个网卡）
ZStack/LoadBalancer/LoadBalancerStatus: 负载均衡监听器健康状态（单个服务器）
ZStack/LoadBalancer/LoadBalancerSessionNumber: 负载均衡监听器活跃连接数（单个服务器）
ZStack/LoadBalancer/LoadBalancerTrafficInBytes: 负载均衡监听器流入流量（单个服务器）
ZStack/LoadBalancer/LoadBalancerTrafficOutBytes: 负载均衡监听器流出流量（单个服务器）
ZStack/LoadBalancer/LoadBalancerSessionUsage: 负载均衡监听器连接使用率（单个服务器，百分比）
ZStack/LoadBalancer/LoadBalancerRefusedSessionNumber: 负载均衡监听器拒绝连接数（单个服务器）
ZStack/LoadBalancer/LoadBalancerConcurrentSessionNumber: 负载均衡监听器并发连接数（单个服务器）
ZStack/LoadBalancer/LoadBalancerNewSessionNumber: 负载均衡监听器新建连接数（单个服务器）
ZStack/LoadBalancer/LoadBalancerTotalSessionNumber: 负载均衡监听器全部连接数（单个服务器）
ZStack/LoadBalancer/LoadBalancerBackendRefusedSessionNumber: 负载均衡监听器拒绝连接数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendConcurrentSessionNumber: 负载均衡监听器并发连接数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendNewSessionNumber: 负载均衡监听器新建连接数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendTotalSessionNumber: 负载均衡监听器全部连接数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendHttp1xxResponses: 负载均衡监听器收到1xx响应的HTTP请求总数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendHttp2xxResponses: 负载均衡监听器收到2xx响应的HTTP请求总数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendHttp3xxResponses: 负载均衡监听器收到3xx响应的HTTP请求总数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendHttp4xxResponses: 负载均衡监听器收到4xx响应的HTTP请求总数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendHttp5xxResponses: 负载均衡监听器收到5xx响应的HTTP请求总数（单个网卡）
ZStack/LoadBalancer/LoadBalancerBackendHttpOtherResponses: 负载均衡监听器收到其他响应的HTTP请求总数（单个网卡）
ZStack/LoadBalancer/LoadBalancerHttp1xxResponses: 负载均衡监听器收到1xx响应的HTTP请求总数（单个服务器）
ZStack/LoadBalancer/LoadBalancerHttp2xxResponses: 负载均衡监听器收到2xx响应的HTTP请求总数（单个服务器）
ZStack/LoadBalancer/LoadBalancerHttp3xxResponses: 负载均衡监听器收到3xx响应的HTTP请求总数（单个服务器）
ZStack/LoadBalancer/LoadBalancerHttp4xxResponses: 负载均衡监听器收到4xx响应的HTTP请求总数（单个服务器）
ZStack/LoadBalancer/LoadBalancerHttp5xxResponses: 负载均衡监听器收到5xx响应的HTTP请求总数（单个服务器）
ZStack/LoadBalancer/LoadBalancerHttpOtherResponses: 负载均衡监听器收到其他响应的HTTP请求总数（单个服务器）
ZStack/SDN/YunshanNSPInforEvents: yunshan网络异常事件
ZStack/MN/MysqlDbError: 数据库错误数
ZStack/Thirdparty/ThirdpartyAlert: 第三方平台报警
ZStack/IAM2Project/VmQuotaUsedInPercent: 项目虚拟机quota百分比
ZStack/IAM2Project/RunningVMInPercent: 项目运行主机百分比
ZStack/IAM2Project/CPUQuotaUsedInPercent: 项目CPUquota百分比
ZStack/IAM2Project/MemoryQuotaUsedInPercent: 项目内存quota百分比
ZStack/IAM2Project/GPUQuotaUsedInPercent: 项目GPU quota百分比
ZStack/IAM2Project/AffinityGroupQuotaUsedInPercent: 项目亲和组quota百分比
ZStack/IAM2Project/VolumeSnapshotQuotaUsedInPercent: 项目云盘快照quota百分比
ZStack/IAM2Project/VolumeDataQuotaUsedInPercent: 项目数据云盘quota百分比
ZStack/IAM2Project/VolumeCapacityQuotaUsedInPercent: 项目使用存储容量quota百分比
ZStack/IAM2Project/ImageQuotaUsedInPercent: 项目镜像数量quota百分比
ZStack/IAM2Project/ImageCapacityQuotaUsedInPercent: 项目镜像容量quota百分比
ZStack/IAM2Project/BackupStorageCapacityQuotaUsedInPercent: 项目使用备份容量quota百分比
ZStack/IAM2Project/BackupStorageQuotaUsedInPercent: 项目备份数量quota百分比
ZStack/IAM2Project/VxlanNetworkQuotaUsedInPercent: 项目vxlan 数量quota百分比
ZStack/IAM2Project/L3NetworkQuotaUsedInPercent: 项目三层网络数量quota百分比
ZStack/IAM2Project/SecurityGroupQuotaUsedInPercent: 项目安全组数量quota百分比
ZStack/IAM2Project/VipQuotaUsedInPercent: 项目虚拟ip数量quota百分比
ZStack/IAM2Project/EipQuotaUsedInPercent: 项目弹性IP数量quota百分比
ZStack/IAM2Project/PortForwardQuotaUsedInPercent: 项目端口转发数量quota百分比
ZStack/IAM2Project/LoadBalancerQuotaUsedInPercent: 项目负载均衡数量quota百分比
ZStack/IAM2Project/LoadBalancerListenerQuotaUsedInPercent: 项目监听器数量quota百分比
ZStack/IAM2Project/IAM2ProjecRetire: 项目回收策略
ZStack/VM/VMFailedRecoverFaultTolerance: FT辅助云主机启动失败
ZStack/HA/MigrateVMFailedWithHostMaintain: 物理机进入维护模式触发云主机迁移失败
ZStack/VRouter/CPUUsedUtilization: CPU使用率
ZStack/VRouter/CPUAverageUsedUtilization: CPU平均使用率
ZStack/VRouter/CPUIdleUtilization: CPU空闲率
ZStack/VRouter/CPUAllUsedUtilization: 全部CPU使用率
ZStack/VRouter/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/VRouter/OperatingSystemCPUSystemUtilization: 内部监控CPU系统进程使用率
ZStack/VRouter/OperatingSystemCPUUserUtilization: 内部监控CPU用户进程使用率
ZStack/VRouter/OperatingSystemCPUWaitUtilization: 内部监控CPU等待IO完成使用率
ZStack/VRouter/OperatingSystemCPUIdleUtilization: 内部监控CPU空闲率
ZStack/VRouter/OperatingSystemCPUUsedUtilization: 内部监控CPU使用率
ZStack/VRouter/OperatingSystemCPUAverageSystemUtilization: 内部监控CPU系统进程平均使用率
ZStack/VRouter/OperatingSystemCPUAverageUserUtilization: 内部监控CPU用户进程平均使用率
ZStack/VRouter/OperatingSystemCPUAverageWaitUtilization: 内部监控CPU等待IO完成平均使用率
ZStack/VRouter/OperatingSystemCPUAverageIdleUtilization: 内部监控CPU平均空闲率
ZStack/VRouter/OperatingSystemCPUAverageUsedUtilization: 内部监控CPU平均使用率
ZStack/VRouter/DiskReadOps: 磁盘读IOPS
ZStack/VRouter/DiskAllReadOps: 全部磁盘读IOPS
ZStack/VRouter/DiskWriteOps: 磁盘写IOPS
ZStack/VRouter/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/VRouter/DiskReadBytes: 磁盘读速度
ZStack/VRouter/DiskAllReadBytes: 全部磁盘读速度
ZStack/VRouter/DiskWriteBytes: 磁盘写速度
ZStack/VRouter/DiskAllWriteBytes: 全部磁盘写速度
ZStack/VRouter/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/VRouter/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/VRouter/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/VRouter/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/VRouter/DiskFreeCapacityInBytes: 磁盘剩余容量
ZStack/VRouter/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/VRouter/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/VRouter/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/VRouter/NetworkInBytes: 网卡入速度
ZStack/VRouter/NetworkAllInBytes: 全部网卡入速度
ZStack/VRouter/NetworkInPackets: 网卡入包数
ZStack/VRouter/NetworkAllInPackets: 全部网卡入包数
ZStack/VRouter/NetworkInErrors: 网卡入错误数
ZStack/VRouter/NetworkInDropped: 网卡入丢包数
ZStack/VRouter/NetworkAllInErrors: 全部网卡入错误数
ZStack/VRouter/NetworkInDroppedBytes: 网卡入丢包数
ZStack/VRouter/NetworkAllInDroppedBytes: 全部网卡入丢包数
ZStack/VRouter/NetworkOutBytes: 网卡出速度
ZStack/VRouter/NetworkAllOutBytes: 全部网卡出速度
ZStack/VRouter/NetworkOutPackets: 网卡出包数
ZStack/VRouter/NetworkAllOutPackets: 全部网卡出包数
ZStack/VRouter/NetworkOutErrors: 网卡出错误数
ZStack/VRouter/NetworkOutDropped: 网卡出丢包数
ZStack/VRouter/NetworkAllOutErrors: 全部网卡出错误数
ZStack/VRouter/NetworkOutDroppedBytes: 网卡出丢包数
ZStack/VRouter/NetworkAllOutDroppedBytes: 全部网卡出丢包数
ZStack/VRouter/TotalNetworkInBytesIn5Min: 5分钟网卡入字节量
ZStack/VRouter/TotalNetworkInPacketsIn5Min: 5分钟网卡入包量
ZStack/VRouter/TotalNetworkOutBytesIn5Min: 5分钟网卡出字节量
ZStack/VRouter/TotalNetworkOutPacketsIn5Min: 5分钟网卡出包量
ZStack/VRouter/TotalNetworkInBytesIn1Min: 1分钟网卡入字节量
ZStack/VRouter/TotalNetworkInPacketsIn1Min: 1分钟网卡入包量
ZStack/VRouter/TotalNetworkOutBytesIn1Min: 1分钟网卡出字节量
ZStack/VRouter/TotalNetworkOutPacketsIn1Min: 1分钟网卡出包量
ZStack/VRouter/MemoryFreeBytes: 内存空闲容量
ZStack/VRouter/MemoryFreeInPercent: 内存空闲百分比
ZStack/VRouter/MemoryUsedBytes: 内存已用容量
ZStack/VRouter/MemoryUsedInPercent: 内存已用百分比
ZStack/VRouter/OperatingSystemMemoryTotalBytes: 内部监控全部内存总容量
ZStack/VRouter/OperatingSystemMemoryFreeBytes: 内部监控内存空闲容量
ZStack/VRouter/OperatingSystemMemoryUsedBytes: 内部监控内存已用容量
ZStack/VRouter/OperatingSystemMemoryAvailableBytes: 内部监控内存可用容量
ZStack/VRouter/OperatingSystemMemoryFreePercent: 内部监控内存空闲容量百分比
ZStack/VRouter/OperatingSystemMemoryUsedPercent: 内部监控内存已用容量百分比
ZStack/VRouter/ZWatchAgentVersion: zwatch客户端版本
ZStack/VRouter/AgentLastResponseTimestampDelta: zwatch客户端上次响应时间戳变化量
ZStack/VRouter/CPUOccupiedByVm: vm占用物理机的cpu开销
ZStack/VRouter/MemoryOccupiedByVm: vm占用物理机的内存开销
ZStack/VRouter/VRouterCPUSystemUtilization: VPC路由器CPU系统占用率
ZStack/VRouter/VRouterCPUUserUtilization: VPC路由器CPU用户占用率
ZStack/VRouter/VRouterCPUWaitUtilization: VPC路由器CPU IO等待占用率
ZStack/VRouter/VRouterCPUIdleUtilization: VPC路由器CPU空闲率
ZStack/VRouter/VRouterCPUUsedUtilization: VPC路由器CPU使用率
ZStack/VRouter/VRouterCPUAverageSystemUtilization: VPC路由器CPU平均系统占用率
ZStack/VRouter/VRouterCPUAverageUserUtilization: VPC路由器CPU平均用户占用率
ZStack/VRouter/VRouterCPUAverageWaitUtilization: VPC路由器CPU平均IO等待占用率
ZStack/VRouter/VRouterCPUAverageIdleUtilization: VPC路由器CPU平均空闲率
ZStack/VRouter/VRouterCPUAverageUsedUtilization: VPC路由器CPU平均使用率
ZStack/VRouter/VRouterDiskAllFreeCapacityInBytes: VPC路由器全部磁盘可用字节数
ZStack/VRouter/VRouterDiskAllFreeCapacityInPercent: VPC路由器全部磁盘百分比
ZStack/VRouter/VRouterDiskAllUsedCapacityInBytes: VPC路由器全部磁盘使用字节数
ZStack/VRouter/VRouterDiskAllUsedCapacityInPercent: VPC路由器全部磁盘使用百分比
ZStack/VRouter/VRouterDiskFreeCapacityInBytes: VPC路由器磁盘可用字节数
ZStack/VRouter/VRouterDiskFreeCapacityInPercent: VPC路由器磁盘百分比
ZStack/VRouter/VRouterDiskUsedCapacityInBytes: VPC路由器磁盘使用字节数
ZStack/VRouter/VRouterDiskUsedCapacityInPercent: VPC路由器磁盘使用百分比
ZStack/VRouter/VRouterMemoryTotalBytes: VPC路由器内存总字节数
ZStack/VRouter/VRouterMemoryFreeBytes: VPC路由器内存空余节数
ZStack/VRouter/VRouterMemoryUsedBytes: VPC路由器内存已使用节数
ZStack/VRouter/VRouterMemoryAvailableBytes: VPC路由器内存可用节数
ZStack/VRouter/VRouterMemoryFreePercent: VPC路由器内存空闲百分比
ZStack/VRouter/VRouterMemoryUsedPercent:  VPC路由器内存使用百分比
ZStack/Baremetal2VM/OperatingSystemCPUSystemUtilization: 内部监控CPU系统进程使用率
ZStack/Baremetal2VM/OperatingSystemCPUUserUtilization: 内部监控CPU用户进程使用率
ZStack/Baremetal2VM/OperatingSystemCPUWaitUtilization: 内部监控CPU等待IO完成使用率
ZStack/Baremetal2VM/OperatingSystemCPUIdleUtilization: 内部监控CPU空闲率
ZStack/Baremetal2VM/OperatingSystemCPUUsedUtilization: 内部监控CPU已使用率
ZStack/Baremetal2VM/OperatingSystemCPUAverageSystemUtilization: 内部监控CPU系统进程平均使用率
ZStack/Baremetal2VM/OperatingSystemCPUAverageUserUtilization: 弹性裸金属云主机内部监控CPU用户进程平均使用率
ZStack/Baremetal2VM/OperatingSystemCPUAverageWaitUtilization: 弹性裸金属云主机内部监控CPU等待IO完成平均使用率
ZStack/Baremetal2VM/OperatingSystemCPUAverageIdleUtilization: 弹性裸金属云主机内部监控CPU平均空闲率
ZStack/Baremetal2VM/OperatingSystemCPUAverageUsedUtilization: 弹性裸金属云主机内部监控CPU平均已使用率
ZStack/Baremetal2VM/DiskAllFreeCapacityInBytes: 弹性裸金属云主机全部磁盘剩余容量
ZStack/Baremetal2VM/DiskAllFreeCapacityInPercent: 弹性裸金属云主机全部磁盘剩余容量百分比
ZStack/Baremetal2VM/DiskAllUsedCapacityInBytes: 弹性裸金属云主机全部磁盘已使用容量
ZStack/Baremetal2VM/DiskAllUsedCapacityInPercent: 弹性裸金属云主机全部磁盘已使用容量百分比
ZStack/Baremetal2VM/DiskFreeCapacityInBytes: 弹性裸金属云主机磁盘剩余容量
ZStack/Baremetal2VM/DiskFreeCapacityInPercent: 弹性裸金属云主机磁盘剩余容量百分比
ZStack/Baremetal2VM/DiskUsedCapacityInBytes: 弹性裸金属云主机磁盘已使用容量
ZStack/Baremetal2VM/DiskUsedCapacityInPercent: 弹性裸金属云主机磁盘已使用容量百分比
ZStack/Baremetal2VM/DiskTotalCapacityInBytes: 弹性裸金属云主机磁盘总容量
ZStack/Baremetal2VM/DiskReadBytesPerSecond: 弹性裸金属云主机磁盘每秒读字节数
ZStack/Baremetal2VM/DiskReadRequestPerSecond: 弹性裸金属云主机磁盘每秒读请求数
ZStack/Baremetal2VM/DiskWriteBytesPerSecond: 弹性裸金属云主机磁盘每秒写字节数
ZStack/Baremetal2VM/DiskWriteRequestPerSecond: 弹性裸金属云主机磁盘每秒写请求数
ZStack/Baremetal2VM/OperatingSystemNetworkInBytes: 弹性裸金属云主机网卡接收字节数
ZStack/Baremetal2VM/OperatingSystemNetworkAllInBytes: 弹性裸金属云主机全部网卡接收字节数
ZStack/Baremetal2VM/OperatingSystemNetworkInPackets: 弹性裸金属云主机网卡接收报数
ZStack/Baremetal2VM/OperatingSystemNetworkAllInPackets: 弹性裸金属云主机全部网卡接收报数
ZStack/Baremetal2VM/OperatingSystemNetworkInErrors: 弹性裸金属云主机网卡接收错误报字节数
ZStack/Baremetal2VM/OperatingSystemNetworkAllInErrors: 弹性裸金属云主机全部网卡接收错误报字节数
ZStack/Baremetal2VM/OperatingSystemNetworkOutBytes: 弹性裸金属云主机网卡发送字节数
ZStack/Baremetal2VM/OperatingSystemNetworkAllOutBytes: 弹性裸金属云主机全部网卡发送字节数
ZStack/Baremetal2VM/OperatingSystemNetworkOutPackets: 弹性裸金属云主机网卡发送报数
ZStack/Baremetal2VM/OperatingSystemNetworkAllOutPackets: 弹性裸金属云主机全部网卡发送报数
ZStack/Baremetal2VM/OperatingSystemNetworkOutErrors: 弹性裸金属云主机网卡发送错误报字节数
ZStack/Baremetal2VM/OperatingSystemNetworkAllOutErrors: 弹性裸金属云主机全部网卡发送错误报字节数
ZStack/Baremetal2VM/OperatingSystemMemoryTotalBytes: 弹性裸金属云主机内存总字节数
ZStack/Baremetal2VM/OperatingSystemMemoryFreeBytes: 弹性裸金属云主机内存空余节数
ZStack/Baremetal2VM/OperatingSystemMemoryUsedBytes: 弹性裸金属云主机内存已使用节数
ZStack/Baremetal2VM/OperatingSystemMemoryAvailableBytes: 弹性裸金属云主机内存可用节数
ZStack/Baremetal2VM/OperatingSystemMemoryFreePercent: 弹性裸金属云主机内存空闲率
ZStack/Baremetal2VM/OperatingSystemMemoryUsedPercent: 弹性裸金属云主机内存使用率
ZStack/Baremetal2VM/GpuPowerDraw: 弹性裸金属GPU功率
ZStack/Baremetal2VM/GpuTemperature: 弹性裸金属GPU温度
ZStack/Baremetal2VM/GpuFanSpeed: 弹性裸金属GPU风扇速度
ZStack/Baremetal2VM/GpuUtilization: 弹性裸金属GPU使用率
ZStack/Baremetal2VM/GpuStatus: 弹性裸金属GPU状态
ZStack/Baremetal2VM/GpuMemoryUtilization: 弹性裸金属GPU显存使用率
ZStack/Baremetal2VM/GpuPciRxThroughputInBytes: 弹性裸金属GPU PCIE RX吞吐量
ZStack/Baremetal2VM/GpuPciTxThroughputInBytes: 弹性裸金属GPU PCIE TX吞吐量
ZStack/VM/VMFailedRecoverFaultTolerance: FT辅助云主机启动失败
ZStack/VM/VMRequestRecoverFaultTolerance: FT辅助云主机开始恢复
ZStack/CdpTask/CdpTaskUsedCapacityInPercent: CDP任务已用分配容量使用率
ZStack/CdpTask/CdpTaskLatency: CDP任务RPO偏移量
ZStack/CdpTask/CdpTaskFailed: CDP任务失败
ZStack/CdpTask/CdpTaskStatusAbnormallyChanged: CDP任务状态异常切换
ZStack/SlbVmInstance/SlbVmInstanceDisconnected: 负载均衡实例失联
ZStack/SlbVmInstance/SlbVmInstanceConnected: 负载均衡实例已连接
ZStack/SlbVmInstance/CPUUsedUtilization: CPU使用率
ZStack/SlbVmInstance/CPUAverageUsedUtilization: CPU平均使用率
ZStack/SlbVmInstance/CPUIdleUtilization: CPU空闲率
ZStack/SlbVmInstance/CPUAllUsedUtilization: 全部CPU使用率
ZStack/SlbVmInstance/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/SlbVmInstance/OperatingSystemCPUSystemUtilization: 内部监控CPU系统进程使用率
ZStack/SlbVmInstance/OperatingSystemCPUUserUtilization: 内部监控CPU用户进程使用率
ZStack/SlbVmInstance/OperatingSystemCPUWaitUtilization: 内部监控CPU等待IO完成使用率
ZStack/SlbVmInstance/OperatingSystemCPUIdleUtilization: 内部监控CPU空闲率
ZStack/SlbVmInstance/OperatingSystemCPUUsedUtilization: 内部监控CPU使用率
ZStack/SlbVmInstance/OperatingSystemCPUAverageSystemUtilization: 内部监控CPU系统进程平均使用率
ZStack/SlbVmInstance/OperatingSystemCPUAverageUserUtilization: 内部监控CPU用户进程平均使用率
ZStack/SlbVmInstance/OperatingSystemCPUAverageWaitUtilization: 内部监控CPU等待IO完成平均使用率
ZStack/SlbVmInstance/OperatingSystemCPUAverageIdleUtilization: 内部监控CPU平均空闲率
ZStack/SlbVmInstance/OperatingSystemCPUAverageUsedUtilization: 内部监控CPU平均使用率
ZStack/SlbVmInstance/DiskReadOps: 磁盘读IOPS
ZStack/SlbVmInstance/DiskAllReadOps: 全部磁盘读IOPS
ZStack/SlbVmInstance/DiskWriteOps: 磁盘写IOPS
ZStack/SlbVmInstance/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/SlbVmInstance/DiskReadBytes: 磁盘读速度
ZStack/SlbVmInstance/DiskAllReadBytes: 全部磁盘读速度
ZStack/SlbVmInstance/DiskWriteBytes: 磁盘写速度
ZStack/SlbVmInstance/DiskAllWriteBytes: 全部磁盘写速度
ZStack/SlbVmInstance/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/SlbVmInstance/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/SlbVmInstance/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/SlbVmInstance/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/SlbVmInstance/DiskFreeCapacityInBytes: 磁盘剩余容量
ZStack/SlbVmInstance/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/SlbVmInstance/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/SlbVmInstance/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/SlbVmInstance/NetworkInBytes: 网卡入速度
ZStack/SlbVmInstance/NetworkAllInBytes: 全部网卡入速度
ZStack/SlbVmInstance/NetworkInPackets: 网卡入包数
ZStack/SlbVmInstance/NetworkAllInPackets: 全部网卡入包数
ZStack/SlbVmInstance/NetworkInErrors: 网卡入错误数
ZStack/SlbVmInstance/NetworkInDropped: 网卡入丢包数
ZStack/SlbVmInstance/NetworkAllInErrors: 全部网卡入错误数
ZStack/SlbVmInstance/NetworkInDroppedBytes: 全部网卡入错误数
ZStack/SlbVmInstance/NetworkAllInDroppedBytes: 网卡入丢包数
ZStack/SlbVmInstance/NetworkOutBytes: 网卡出速度
ZStack/SlbVmInstance/NetworkAllOutBytes: 全部网卡出速度
ZStack/SlbVmInstance/NetworkOutPackets: 网卡出包数
ZStack/SlbVmInstance/NetworkAllOutPackets: 全部网卡出包数
ZStack/SlbVmInstance/NetworkOutErrors: 网卡出错误数
ZStack/SlbVmInstance/NetworkOutDropped: 网卡出丢包数
ZStack/SlbVmInstance/NetworkAllOutErrors: 全部网卡出错误数
ZStack/SlbVmInstance/NetworkOutDroppedBytes: 网卡出丢包数
ZStack/SlbVmInstance/NetworkAllOutDroppedBytes: 全部网卡出丢包数
ZStack/SlbVmInstance/TotalNetworkInBytesIn5Min: 5分钟网卡入字节量
ZStack/SlbVmInstance/TotalNetworkInPacketsIn5Min: 5分钟网卡入包量
ZStack/SlbVmInstance/TotalNetworkOutBytesIn5Min: 5分钟网卡出字节量
ZStack/SlbVmInstance/TotalNetworkOutPacketsIn5Min: 5分钟网卡出包量
ZStack/SlbVmInstance/TotalNetworkInBytesIn1Min: 1分钟网卡入字节量
ZStack/SlbVmInstance/TotalNetworkInPacketsIn1Min: 1分钟网卡入包量
ZStack/SlbVmInstance/TotalNetworkOutBytesIn1Min: 1分钟网卡出字节量
ZStack/SlbVmInstance/TotalNetworkOutPacketsIn1Min: 1分钟网卡出包量
ZStack/SlbVmInstance/MemoryFreeBytes: 内存空闲容量
ZStack/SlbVmInstance/MemoryFreeInPercent: 内存空闲百分比
ZStack/SlbVmInstance/MemoryUsedBytes: 内存已用容量
ZStack/SlbVmInstance/MemoryUsedInPercent: 内存已用百分比
ZStack/SlbVmInstance/OperatingSystemMemoryTotalBytes: 内部监控全部内存总容量
ZStack/SlbVmInstance/OperatingSystemMemoryFreeBytes: 内部监控内存空闲容量
ZStack/SlbVmInstance/OperatingSystemMemoryUsedBytes: 内部监控内存已用容量
ZStack/SlbVmInstance/OperatingSystemMemoryAvailableBytes: 内部监控内存可用容量
ZStack/SlbVmInstance/OperatingSystemMemoryFreePercent: 内部监控内存空闲容量百分比
ZStack/SlbVmInstance/OperatingSystemMemoryUsedPercent: 内部监控内存已用容量百分比
ZStack/SlbVmInstance/ZWatchAgentVersion: zwatch客户端版本
ZStack/SlbVmInstance/AgentLastResponseTimestampDelta: zwatch客户端上次响应时间戳变化量
ZStack/SlbVmInstance/CPUOccupiedByVm: 负载均衡实例占用物理机的cpu开销
ZStack/SlbVmInstance/MemoryOccupiedByVm: 负载均衡实例占用物理机的内存开销
ZStack/SlbVmInstance/VRouterCPUSystemUtilization: 负载均衡实例CPU系统占用率
ZStack/SlbVmInstance/VRouterCPUUserUtilization: 负载均衡实例CPU用户占用率
ZStack/SlbVmInstance/VRouterCPUWaitUtilization: 负载均衡实例CPU IO等待占用率
ZStack/SlbVmInstance/VRouterCPUIdleUtilization: 负载均衡实例CPU空闲率
ZStack/SlbVmInstance/VRouterCPUUsedUtilization: 负载均衡实例CPU使用率
ZStack/SlbVmInstance/VRouterCPUAverageSystemUtilization: 负载均衡实例CPU平均系统占用率
ZStack/SlbVmInstance/VRouterCPUAverageUserUtilization: 负载均衡实例CPU平均用户占用率
ZStack/SlbVmInstance/VRouterCPUAverageWaitUtilization: 负载均衡实例CPU平均IO等待占用率
ZStack/SlbVmInstance/VRouterCPUAverageIdleUtilization: 负载均衡实例CPU平均空闲率
ZStack/SlbVmInstance/VRouterCPUAverageUsedUtilization: 负载均衡实例CPU平均使用率
ZStack/SlbVmInstance/VRouterDiskAllFreeCapacityInBytes: 负载均衡实例全部磁盘可用字节数
ZStack/SlbVmInstance/VRouterDiskAllFreeCapacityInPercent: 负载均衡实例全部磁盘百分比
ZStack/SlbVmInstance/VRouterDiskAllUsedCapacityInBytes: 负载均衡实例全部磁盘使用字节数
ZStack/SlbVmInstance/VRouterDiskAllUsedCapacityInPercent: 负载均衡实例全部磁盘使用百分比
ZStack/SlbVmInstance/VRouterDiskFreeCapacityInBytes: 负载均衡实例磁盘可用字节数
ZStack/SlbVmInstance/VRouterDiskFreeCapacityInPercent: 负载均衡实例磁盘百分比
ZStack/SlbVmInstance/VRouterDiskUsedCapacityInBytes: 负载均衡实例磁盘使用字节数
ZStack/SlbVmInstance/VRouterDiskUsedCapacityInPercent: 负载均衡实例磁盘使用百分比
ZStack/SlbVmInstance/VRouterMemoryTotalBytes: 负载均衡实例内存总字节数
ZStack/SlbVmInstance/VRouterMemoryFreeBytes: 负载均衡实例内存空余节数
ZStack/SlbVmInstance/VRouterMemoryUsedBytes: 负载均衡实例内存已使用节数
ZStack/SlbVmInstance/VRouterMemoryAvailableBytes: 负载均衡实例内存可用节数
ZStack/SlbVmInstance/VRouterMemoryFreePercent: 负载均衡实例内存空闲百分比
ZStack/SlbVmInstance/VRouterMemoryUsedPercent:  负载均衡实例内存使用百分比
ZStack/SlbVmInstance/CPUOccupiedByVm: 负载均衡实例占用物理机的cpu开销
ZStack/SlbVmInstance/MemoryOccupiedByVm: 负载均衡实例占用物理机的内存开销
ZStack/SlbVmInstance/SlbVmInstanceAbnormalFilesExists: 负载均衡实例磁盘空间被异常文件占用
ZStack/SlbVmInstance/SlbVmInstanceHaStatusChanged: 负载均衡实例主备切换
ZStack/SlbVmInstance/SlbVmInstanceConfigFailed: 负载均衡实例配置同步失败
ZStack/SlbVmInstance/ZWatchAgentFeaturePvpanic: 负载均衡实例崩溃检查模块运行情况
ZStack/SlbVmInstance/PVPanicEnableInDomainXML: 负载均衡实例崩溃对应的物理机支持情况
ZStack/PrimaryStorage/PoolAvailableCapacityInPercent: Ceph存储池可用容量百分比
ZStack/PrimaryStorage/PoolUsedCapacityInPercent: Ceph存储池已用容量百分比
ZStack/SecurityMachine/SecurityMachineStateChange: 密码机状态变化
ZStack/SecretResourcePool/SecretResourcePoolStatusChange: 密码资源状态变化
ZStack/PrimaryStorage/PoolVirtualAvailableCapacityInPercent: Ceph存储池虚拟可用容量百分比
ZStack/PrimaryStorage/TimeDurationRequiredForPrimaryStorageForecastUsageExceedingThresholdUsage: 主存储预测使用容量超过阈值所需时间
ZStack/PrimaryStorage/TimeDurationRequiredForCephPoolForecastUsageExceedingThresholdUsage: Ceph存储池预测使用容量超过阈值所需时间
ZStack/PrimaryStorage/TimeDurationRequiredForLocalStorageHostForecastUsageExceedingThresholdUsage: 本地存储物理机预测使用容量超过阈值所需时间
ZStack/Host/SharedPagesMemoryInBytes: 物理机可共享内存页字节数
ZStack/Host/SharingPagesMemoryInBytes: 物理机共享中的内存页字节数
ZStack/Host/ReclaimedMemoryInBytes: 物理机回收内存字节数
ZStack/BareMetal2Gateway/SharedPagesMemoryInBytes: 弹性裸金属网关可共享内存页字节数
ZStack/BareMetal2Gateway/SharingPagesMemoryInBytes: 弹性裸金属共享中的内存页字节数
ZStack/BareMetal2Gateway/ReclaimedMemoryInBytes: 弹性裸金属网关物理机回收内存字节数
ZStack/KVMHost/SharedPagesMemoryInBytes: KVM物理机可共享内存页字节数
ZStack/KVMHost/SharingPagesMemoryInBytes: KVM物理机共享中的内存页字节数
ZStack/KVMHost/ReclaimedMemoryInBytes: KVM物理机回收内存字节数
ZStack/VM/ReclaimedMemoryInBytes: 虚拟机回收内存字节数
ZStack/VM/VGpuUtilization: 虚拟机使用VGPU的利用率
ZStack/VM/VGpuMemoryUtilization: 虚拟机使用VGPU的显存使用率
ZStack/VM/GpuPowerDraw: 虚拟机中GPU使用功率
ZStack/VM/GpuTemperature: 虚拟机中GPU温度
ZStack/VM/GpuFanSpeed: 虚拟机中GPU风扇速度
ZStack/VM/GpuUtilization: 虚拟机中GPU使用率
ZStack/VM/GpuDdrCapacity: 虚拟机中GPU DDR容量
ZStack/VM/GpuDdrUsageRate: 虚拟机中GPU DDR使用率
ZStack/VM/GpuHbmCapacity: 虚拟机中GPU HBM容量
ZStack/VM/GpuHbmUsageRate: 虚拟机中GPU HBM使用率
ZStack/VM/GpuStatus: 虚拟机中GPU状态
ZStack/VM/GpuMemoryUtilization: 虚拟机中GPU内存使用率
ZStack/VM/GpuPciRxThroughputInBytes: 虚拟机中GPU PCIE RX吞吐量
ZStack/VM/GpuPciTxThroughputInBytes: 虚拟机中GPU PCIE TX吞吐量
ZStack/VRouter/VGpuMemoryUtilization: VROUTER虚拟机使用VGPU的显存使用率
ZStack/VRouter/VGpuUtilization: VROUTER虚拟机使用VGPU的利用率
ZStack/VRouter/GpuPowerDraw: 虚拟机中GPU使用功率
ZStack/VRouter/GpuTemperature: 虚拟机中GPU温度
ZStack/VRouter/GpuFanSpeed: 虚拟机中GPU风扇速度
ZStack/VRouter/GpuUtilization: 虚拟机中GPU使用率
ZStack/VRouter/GpuDdrCapacity: 虚拟机中GPU DDR容量
ZStack/VRouter/GpuDdrUsageRate: 虚拟机中GPU DDR使用率
ZStack/VRouter/GpuHbmCapacity: 虚拟机中GPU HBM容量
ZStack/VRouter/GpuHbmUsageRate: 虚拟机中GPU HBM使用率
ZStack/VRouter/GpuStatus: 虚拟机中GPU状态
ZStack/VRouter/GpuMemoryUtilization: 虚拟机中GPU内存使用率
ZStack/VRouter/GpuPciRxThroughputInBytes: 虚拟机中GPU PCIE RX吞吐量
ZStack/VRouter/GpuPciTxThroughputInBytes: 虚拟机中GPU PCIE TX吞吐量
ZStack/SlbVmInstance/VGpuMemoryUtilization: SLB虚拟机使用VGPU的显存使用率
ZStack/SlbVmInstance/VGpuUtilization: SLB虚拟机使用VGPU的利用率
ZStack/SlbVmInstance/VGpuMemoryUtilization: VROUTER虚拟机使用VGPU的显存使用率
ZStack/SlbVmInstance/VGpuUtilization: VROUTER虚拟机使用VGPU的利用率
ZStack/SlbVmInstance/GpuPowerDraw: 虚拟机中GPU使用功率
ZStack/SlbVmInstance/GpuTemperature: 虚拟机中GPU温度
ZStack/SlbVmInstance/GpuFanSpeed: 虚拟机中GPU风扇速度
ZStack/SlbVmInstance/GpuUtilization: 虚拟机中GPU使用率
ZStack/SlbVmInstance/GpuDdrCapacity: 虚拟机中GPU DDR容量
ZStack/SlbVmInstance/GpuDdrUsageRate: 虚拟机中GPU DDR使用率
ZStack/SlbVmInstance/GpuHbmCapacity: 虚拟机中GPU HBM容量
ZStack/SlbVmInstance/GpuHbmUsageRate: 虚拟机中GPU HBM使用率
ZStack/SlbVmInstance/GpuStatus: 虚拟机中GPU状态
ZStack/SlbVmInstance/GpuMemoryUtilization: 虚拟机中GPU内存使用率
ZStack/SlbVmInstance/GpuPciRxThroughputInBytes: 虚拟机中GPU PCIE RX吞吐量
ZStack/SlbVmInstance/GpuPciTxThroughputInBytes: 虚拟机中GPU PCIE TX吞吐量
ZStack/ESXHost/SharedPagesMemoryInBytes: ESX物理机可共享内存页字节数
ZStack/ESXHost/SharingPagesMemoryInBytes: ESX物理机共享中的内存页字节数
ZStack/ESXHost/ReclaimedMemoryInBytes: ESX物理机回收内存字节数
ZStack/VRouter/ReclaimedMemoryInBytes: VPC路由器回收内存字节数
ZStack/XDragonHost/SharedPagesMemoryInBytes: XDragon物理机可共享内存页字节数
ZStack/XDragonHost/SharingPagesMemoryInBytes: XDragon物理机共享中的内存页字节数
ZStack/XDragonHost/ReclaimedMemoryInBytes: XDragon物理机回收内存字节数
ZStack/SlbVmInstance/ReclaimedMemoryInBytes: 负载均衡实例回收内存字节数
ZStack/VM/MemoryMinorPageFaults: 虚拟机内存次缺页错误
ZStack/SlbVmInstance/MemoryMinorPageFaults: 负载均衡实例内存次缺页错误
ZStack/VRouter/MemoryMinorPageFaults: VPC路由器内存次缺页错误
ZStack/Host/GpuPowerDraw: GPU功耗
ZStack/Host/GpuTemperature: GPU温度
ZStack/Host/GpuFanSpeed: 当前GPU风扇速度百分比
ZStack/Host/GpuStatus: GPU状态
ZStack/Host/GpuUtilization: GPU使用率
ZStack/Host/GpuDdrCapacity: GPU DDR容量
ZStack/Host/GpuDdrUsageRate: GPU DDR使用率
ZStack/Host/GpuHbmCapacity: GPU HBM容量
ZStack/Host/GpuHbmUsageRate: GPU HBM使用率
ZStack/Host/GpuMemoryUtilization: GPU显存使用率
ZStack/Host/GpuPciRxThroughputInBytes: PCIE RX吞吐量
ZStack/Host/GpuPciTxThroughputInBytes: PCIE TX吞吐量
ZStack/Host/VGpuUtilization: VGPU使用率
ZStack/Host/VGpuMemoryUtilization: VGPU显存使用率
ZStack/Host/HostPhysicalGpuStatusAbnormal: 物理机GPU异常状态
ZStack/Host/HostPhysicalVGpuStatusAbnormal: 物理机vGPU异常状态
ZStack/Host/HostPhysicalGpuRemoveTriggered: 物理机GPU拔出
ZStack/Host/HostPhysicalRaidStateAbnormal: 物理机RAID卡异常
ZStack/Host/HostHbaPortStateAbnormal: 物理机HBA卡异常
ZStack/XDragonHost/GpuPowerDraw: GPU功耗
ZStack/XDragonHost/GpuTemperature: GPU温度
ZStack/XDragonHost/GpuFanSpeed: 当前GPU风扇速度百分比
ZStack/XDragonHost/GpuStatus: GPU状态
ZStack/XDragonHost/GpuUtilization: GPU使用率
ZStack/XDragonHost/GpuDdrCapacity: GPU DDR容量
ZStack/XDragonHost/GpuDdrUsageRate: GPU DDR使用率
ZStack/XDragonHost/GpuHbmCapacity: GPU HBM容量
ZStack/XDragonHost/GpuHbmUsageRate: GPU HBM使用率
ZStack/XDragonHost/GpuMemoryUtilization: GPU显存使用率
ZStack/XDragonHost/GpuPciRxThroughputInBytes: PCIE RX吞吐量
ZStack/XDragonHost/GpuPciTxThroughputInBytes: PCIE TX吞吐量
ZStack/XDragonHost/VGpuUtilization: VGPU使用率
ZStack/XDragonHost/VGpuMemoryUtilization: VGPU显存使用率
ZStack/XDragonHost/HostPhysicalGpuStatusAbnormal: 物理机GPU异常状态
ZStack/XDragonHost/HostPhysicalVGpuStatusAbnormal: 物理机vGPU异常状态
ZStack/XDragonHost/HostPhysicalGpuRemoveTriggered: 物理机GPU拔出
ZStack/XDragonHost/HostPhysicalRaidStateAbnormal: 物理机RAID卡异常
ZStack/XDragonHost/HostHbaPortStateAbnormal: 物理机HBA卡异常
ZStack/KVMHost/GpuPowerDraw: GPU功耗
ZStack/KVMHost/GpuTemperature: GPU温度
ZStack/KVMHost/GpuFanSpeed: 当前GPU风扇速度百分比
ZStack/KVMHost/GpuStatus: GPU状态
ZStack/KVMHost/GpuUtilization: GPU使用率
ZStack/KVMHost/GpuDdrCapacity: GPU DDR容量
ZStack/KVMHost/GpuDdrUsageRate: GPU DDR使用率
ZStack/KVMHost/GpuHbmCapacity: GPU HBM容量
ZStack/KVMHost/GpuHbmUsageRate: GPU HBM使用率
ZStack/KVMHost/GpuMemoryUtilization: GPU显存使用率
ZStack/KVMHost/GpuPciRxThroughputInBytes: PCIE RX吞吐量
ZStack/KVMHost/GpuPciTxThroughputInBytes: PCIE TX吞吐量
ZStack/KVMHost/VGpuUtilization: VGPU使用率
ZStack/KVMHost/VGpuMemoryUtilization: VGPU显存使用率
ZStack/KVMHost/HostPhysicalGpuStatusAbnormal: 物理机GPU异常状态
ZStack/KVMHost/HostPhysicalVGpuStatusAbnormal: 物理机vGPU异常状态
ZStack/KVMHost/HostPhysicalGpuRemoveTriggered: 物理机GPU拔出
ZStack/KVMHost/HostPhysicalRaidStateAbnormal: 物理机RAID卡异常
ZStack/KVMHost/HostHbaPortStateAbnormal: 物理机HBA卡异常
ZStack/ESXHost/GpuPowerDraw: GPU功耗
ZStack/ESXHost/GpuTemperature: GPU温度
ZStack/ESXHost/GpuFanSpeed: 当前GPU风扇速度百分比
ZStack/ESXHost/GpuStatus: GPU状态
ZStack/ESXHost/GpuUtilization: GPU使用率
ZStack/ESXHost/GpuDdrCapacity: GPU DDR容量
ZStack/ESXHost/GpuDdrUsageRate: GPU DDR使用率
ZStack/ESXHost/GpuHbmCapacity: GPU HBM容量
ZStack/ESXHost/GpuHbmUsageRate: GPU HBM使用率
ZStack/ESXHost/GpuMemoryUtilization: GPU显存使用率
ZStack/ESXHost/GpuPciRxThroughputInBytes: PCIE RX吞吐量
ZStack/ESXHost/GpuPciTxThroughputInBytes: PCIE TX吞吐量
ZStack/ESXHost/VGpuUtilization: VGPU使用率
ZStack/ESXHost/VGpuMemoryUtilization: VGPU显存使用率
ZStack/ESXHost/HostPhysicalGpuStatusAbnormal: 物理机GPU异常状态
ZStack/ESXHost/HostPhysicalVGpuStatusAbnormal: 物理机vGPU异常状态
ZStack/ESXHost/HostPhysicalGpuRemoveTriggered: 物理机GPU拔出
ZStack/ESXHost/HostPhysicalRaidStateAbnormal: 物理机RAID卡异常
ZStack/ESXHost/HostHbaPortStateAbnormal: 物理机HBA卡异常
ZStack/BareMetal2Gateway/GpuPowerDraw: GPU功耗
ZStack/BareMetal2Gateway/GpuTemperature: GPU温度
ZStack/BareMetal2Gateway/GpuFanSpeed: 当前GPU风扇速度百分比
ZStack/BareMetal2Gateway/GpuStatus: GPU状态
ZStack/BareMetal2Gateway/GpuUtilization: GPU使用率
ZStack/BareMetal2Gateway/GpuDdrCapacity: GPU DDR容量
ZStack/BareMetal2Gateway/GpuDdrUsageRate: GPU DDR使用率
ZStack/BareMetal2Gateway/GpuHbmCapacity: GPU HBM容量
ZStack/BareMetal2Gateway/GpuHbmUsageRate: GPU HBM使用率
ZStack/BareMetal2Gateway/GpuMemoryUtilization: GPU显存使用率
ZStack/BareMetal2Gateway/GpuPciRxThroughputInBytes: PCIE RX吞吐量
ZStack/BareMetal2Gateway/GpuPciTxThroughputInBytes: PCIE TX吞吐量
ZStack/BareMetal2Gateway/VGpuUtilization: VGPU使用率
ZStack/BareMetal2Gateway/VGpuMemoryUtilization: VGPU显存使用率
ZStack/BareMetal2Gateway/HostPhysicalGpuStatusAbnormal: 物理机GPU异常状态
ZStack/BareMetal2Gateway/HostPhysicalVGpuStatusAbnormal: 物理机vGPU异常状态
ZStack/BareMetal2Gateway/HostPhysicalGpuRemoveTriggered: 物理机GPU拔出
ZStack/BareMetal2Gateway/HostPhysicalRaidStateAbnormal: 物理机RAID卡异常
ZStack/BareMetal2Gateway/HostHbaPortStateAbnormal: 物理机HBA卡异常
ZStack/BareMetal2Gateway/PhysicalVolumeStatus: 物理卷活跃状态
ZStack/BareMetal2Gateway/PhysicalVolumeState: 物理卷健康状态
ZStack/Host/IPMIPhysicalMemoryStatus: 物理内存状态
ZStack/Host/IPMIPowerSupply: 电源状态
ZStack/Host/IPMIFanSpeedState: 风扇状态
ZStack/Host/IPMIFanSpeedRpm: 每分钟风扇速度
ZStack/Host/IPMITemperatureState: 传感器状态
ZStack/Host/IPMITemperatureCelsius: 传感器温度
ZStack/Host/FcHbaLinkFailureTotal: FC HBA 链接失败次数
ZStack/Host/FcHbaLossOfSignalTotal: FC HBA 信号丢失次数
ZStack/XDragonHost/IPMIPhysicalMemoryStatus: 物理内存状态
ZStack/XDragonHost/IPMIPowerSupply: 电源状态
ZStack/XDragonHost/IPMIFanSpeedState: 风扇状态
ZStack/XDragonHost/IPMIFanSpeedRpm: 每分钟风扇速度
ZStack/XDragonHost/IPMITemperatureState: 传感器状态
ZStack/XDragonHost/IPMITemperatureCelsius: 传感器温度
ZStack/XDragonHost/FcHbaLinkFailureTotal: FC HBA 链接失败次数
ZStack/XDragonHost/FcHbaLossOfSignalTotal: FC HBA 信号丢失次数
ZStack/KVMHost/IPMIPhysicalMemoryStatus: 物理内存状态
ZStack/KVMHost/IPMIPowerSupply: 电源状态
ZStack/KVMHost/IPMIFanSpeedState: 风扇状态
ZStack/KVMHost/IPMIFanSpeedRpm: 每分钟风扇速度
ZStack/KVMHost/IPMITemperatureState: 传感器状态
ZStack/KVMHost/IPMITemperatureCelsius: 传感器温度
ZStack/KVMHost/FcHbaLinkFailureTotal: FC HBA 链接失败次数
ZStack/KVMHost/FcHbaLossOfSignalTotal: FC HBA 信号丢失次数
ZStack/ESXHost/IPMIPhysicalMemoryStatus: 物理内存状态
ZStack/ESXHost/IPMIPowerSupply: 电源状态
ZStack/ESXHost/IPMIFanSpeedState: 风扇状态
ZStack/ESXHost/IPMIFanSpeedRpm: 每分钟风扇速度
ZStack/ESXHost/IPMITemperatureState: 传感器状态
ZStack/ESXHost/IPMITemperatureCelsius: 传感器温度
ZStack/ESXHost/FcHbaLinkFailureTotal: FC HBA 链接失败次数
ZStack/ESXHost/FcHbaLossOfSignalTotal: FC HBA 信号丢失次数
ZStack/BareMetal2Gateway/IPMIPhysicalMemoryStatus: 物理内存状态
ZStack/BareMetal2Gateway/IPMIPowerSupply: 电源状态
ZStack/BareMetal2Gateway/IPMIFanSpeedState: 风扇状态
ZStack/BareMetal2Gateway/IPMIFanSpeedRpm: 每分钟风扇速度
ZStack/BareMetal2Gateway/IPMITemperatureState: 传感器状态
ZStack/BareMetal2Gateway/IPMITemperatureCelsius: 传感器温度
ZStack/BareMetal2Gateway/FcHbaLinkFailureTotal: FC HBA 链接失败次数
ZStack/BareMetal2Gateway/FcHbaLossOfSignalTotal: FC HBA 信号丢失次数
ZStack/Host/CPUSeconds: CPU 时间片
ZStack/Host/CPUUsedSeconds: CPU 使用时间片
ZStack/Host/CPUIdleSeconds: CPU 空闲时间片
ZStack/Host/MemoryTotalBytes: 物理机总内存
ZStack/Host/HostProcessPhysicalMemoryUsageAbnormal: 物理机进程物理内存使用量异常
ZStack/BareMetal2Gateway/CPUSeconds: CPU 时间片
ZStack/BareMetal2Gateway/CPUUsedSeconds: CPU 使用时间片 
ZStack/BareMetal2Gateway/CPUIdleSeconds: CPU 空闲时间片
ZStack/BareMetal2Gateway/MemoryTotalBytes: 物理机总内存
ZStack/BareMetal2Gateway/HostProcessPhysicalMemoryUsageAbnormal: 物理机进程物理内存使用量异常
ZStack/KVMHost/CPUSeconds: CPU 时间片
ZStack/KVMHost/CPUUsedSeconds: CPU 使用时间片
ZStack/KVMHost/CPUIdleSeconds: CPU 空闲时间片
ZStack/KVMHost/MemoryTotalBytes: 物理机总内存
ZStack/KVMHost/HostProcessPhysicalMemoryUsageAbnormal: 物理机进程物理内存使用量异常
ZStack/ESXHost/CPUSeconds: CPU 时间片
ZStack/ESXHost/CPUUsedSeconds: CPU 使用时间片
ZStack/ESXHost/CPUIdleSeconds: CPU 空闲时间片
ZStack/ESXHost/MemoryTotalBytes: 物理机总内存
ZStack/ESXHost/HostProcessPhysicalMemoryUsageAbnormal: 物理机进程物理内存使用量异常
ZStack/XDragonHost/CPUSeconds: CPU 时间片
ZStack/XDragonHost/CPUUsedSeconds: CPU 使用时间片
ZStack/XDragonHost/CPUIdleSeconds: CPU 空闲时间片
ZStack/XDragonHost/MemoryTotalBytes: 物理机总内存
ZStack/NativeHost/GpuStatus: 云原生节点GPU状态
ZStack/NativeHost/GpuUtilization: 云原生节点GPU使用率
ZStack/NativeHost/GpuMemoryUtilization: 云原生节点GPU显存使用率
ZStack/NativeHost/GpuPowerDraw: 云原生节点GPU使用功率
ZStack/NativeHost/GpuTemperature: 云原生节点GPU温度
ZStack/NativeHost/GpuFanSpeed: 云原生节点GPU风扇速度
ZStack/NativeHost/GpuPciRxThroughputInBytes: 云原生节点GPU PCIE RX吞吐量
ZStack/NativeHost/GpuPciTxThroughputInBytes: 云原生节点GPU PCIE TX吞吐量
ZStack/NativeHost/VGpuUtilization: 云原生节点VGPU使用率
ZStack/NativeHost/VGpuMemoryUtilization: 云原生节点VGPU显存使用率
ZStack/XDragonHost/HostProcessPhysicalMemoryUsageAbnormal: 物理机进程物理内存使用量异常
ZStack/OvnVmInstance/CPUUsedUtilization: CPU使用率
ZStack/OvnVmInstance/CPUAverageUsedUtilization: CPU平均使用率
ZStack/OvnVmInstance/CPUIdleUtilization: CPU空闲率
ZStack/OvnVmInstance/CPUAllUsedUtilization: 全部CPU使用率
ZStack/OvnVmInstance/CPUAllIdleUtilization: 全部CPU空闲率
ZStack/OvnVmInstance/OperatingSystemCPUSystemUtilization: 内部监控CPU系统进程使用率
ZStack/OvnVmInstance/OperatingSystemCPUUserUtilization: 内部监控CPU用户进程使用率
ZStack/OvnVmInstance/OperatingSystemCPUWaitUtilization: 内部监控CPU等待IO完成使用率
ZStack/OvnVmInstance/OperatingSystemCPUIdleUtilization: 内部监控CPU空闲率
ZStack/OvnVmInstance/OperatingSystemCPUUsedUtilization: 内部监控CPU使用率
ZStack/OvnVmInstance/OperatingSystemCPUAverageSystemUtilization: 内部监控CPU系统进程平均使用率
ZStack/OvnVmInstance/OperatingSystemCPUAverageUserUtilization: 内部监控CPU用户进程平均使用率
ZStack/OvnVmInstance/OperatingSystemCPUAverageWaitUtilization: 内部监控CPU等待IO完成平均使用率
ZStack/OvnVmInstance/OperatingSystemCPUAverageIdleUtilization: 内部监控CPU平均空闲率
ZStack/OvnVmInstance/OperatingSystemCPUAverageUsedUtilization: 内部监控CPU平均使用率
ZStack/OvnVmInstance/DiskReadOps: 磁盘读IOPS
ZStack/OvnVmInstance/DiskAllReadOps: 全部磁盘读IOPS
ZStack/OvnVmInstance/DiskWriteOps: 磁盘写IOPS
ZStack/OvnVmInstance/DiskAllWriteOps: 全部磁盘写IOPS
ZStack/OvnVmInstance/DiskReadBytes: 磁盘读速度
ZStack/OvnVmInstance/DiskAllReadBytes: 全部磁盘读速度
ZStack/OvnVmInstance/DiskWriteBytes: 磁盘写速度
ZStack/OvnVmInstance/DiskAllWriteBytes: 全部磁盘写速度
ZStack/OvnVmInstance/DiskAllFreeCapacityInBytes: 全部磁盘剩余容量
ZStack/OvnVmInstance/DiskAllFreeCapacityInPercent: 全部磁盘剩余容量百分比
ZStack/OvnVmInstance/DiskAllUsedCapacityInBytes: 全部磁盘已使用容量
ZStack/OvnVmInstance/DiskAllUsedCapacityInPercent: 全部磁盘已使用容量百分比
ZStack/OvnVmInstance/DiskFreeCapacityInBytes: 磁盘剩余容量
ZStack/OvnVmInstance/DiskFreeCapacityInPercent: 磁盘剩余容量百分比
ZStack/OvnVmInstance/DiskUsedCapacityInBytes: 磁盘已使用容量
ZStack/OvnVmInstance/DiskUsedCapacityInPercent: 磁盘已使用容量百分比
ZStack/OvnVmInstance/NetworkInBytes: 网卡入速度
ZStack/OvnVmInstance/NetworkAllInBytes: 全部网卡入速度
ZStack/OvnVmInstance/NetworkInPackets: 网卡入包数
ZStack/OvnVmInstance/NetworkAllInPackets: 全部网卡入包数
ZStack/OvnVmInstance/NetworkInErrors: 网卡入错误数
ZStack/OvnVmInstance/NetworkAllInErrors: 全部网卡入错误数
ZStack/OvnVmInstance/NetworkInDroppedBytes: 全部网卡入错误数
ZStack/OvnVmInstance/NetworkAllInDroppedBytes: 网卡入丢包数
ZStack/OvnVmInstance/NetworkOutBytes: 网卡出速度
ZStack/OvnVmInstance/NetworkAllOutBytes: 全部网卡出速度
ZStack/OvnVmInstance/NetworkOutPackets: 网卡出包数
ZStack/OvnVmInstance/NetworkAllOutPackets: 全部网卡出包数
ZStack/OvnVmInstance/NetworkOutErrors: 网卡出错误数
ZStack/OvnVmInstance/NetworkAllOutErrors: 全部网卡出错误数
ZStack/OvnVmInstance/NetworkOutDroppedBytes: 网卡出丢包数
ZStack/OvnVmInstance/NetworkAllOutDroppedBytes: 全部网卡出丢包数
ZStack/OvnVmInstance/TotalNetworkInBytesIn5Min: 5分钟网卡入字节量
ZStack/OvnVmInstance/TotalNetworkInPacketsIn5Min: 5分钟网卡入包量
ZStack/OvnVmInstance/TotalNetworkOutBytesIn5Min: 5分钟网卡出字节量
ZStack/OvnVmInstance/TotalNetworkOutPacketsIn5Min: 5分钟网卡出包量
ZStack/OvnVmInstance/TotalNetworkInBytesIn1Min: 1分钟网卡入字节量
ZStack/OvnVmInstance/TotalNetworkInPacketsIn1Min: 1分钟网卡入包量
ZStack/OvnVmInstance/TotalNetworkOutBytesIn1Min: 1分钟网卡出字节量
ZStack/OvnVmInstance/TotalNetworkOutPacketsIn1Min: 1分钟网卡出包量
ZStack/OvnVmInstance/MemoryFreeBytes: 内存空闲容量
ZStack/OvnVmInstance/MemoryFreeInPercent: 内存空闲百分比
ZStack/OvnVmInstance/MemoryUsedBytes: 内存已用容量
ZStack/OvnVmInstance/MemoryUsedInPercent: 内存已用百分比
ZStack/OvnVmInstance/OperatingSystemMemoryTotalBytes: 内部监控全部内存总容量
ZStack/OvnVmInstance/OperatingSystemMemoryFreeBytes: 内部监控内存空闲容量
ZStack/OvnVmInstance/OperatingSystemMemoryUsedBytes: 内部监控内存已用容量
ZStack/OvnVmInstance/OperatingSystemMemoryAvailableBytes: 内部监控内存可用容量
ZStack/OvnVmInstance/OperatingSystemMemoryFreePercent: 内部监控内存空闲容量百分比
ZStack/OvnVmInstance/OperatingSystemMemoryUsedPercent: 内部监控内存已用容量百分比
ZStack/OvnVmInstance/ZWatchAgentVersion: zwatch客户端版本
ZStack/OvnVmInstance/AgentLastResponseTimestampDelta: zwatch客户端上次响应时间戳变化量
ZStack/OvnVmInstance/CPUOccupiedByVm: 负载均衡实例占用物理机的cpu开销
ZStack/OvnVmInstance/MemoryOccupiedByVm: 负载均衡实例占用物理机的内存开销
ZStack/OvnVmInstance/ZWatchAgentFeaturePvpanic: 负载均衡实例崩溃检查模块运行情况
ZStack/OvnVmInstance/PVPanicEnableInDomainXML: 负载均衡实例崩溃对应的物理机支持情况
ZStack/OvnVmInstance/MemoryMinorPageFaults: 虚拟机内存次缺页错误
ZStack/OvnVmInstance/VRouterCPUSystemUtilization: VPC路由器CPU系统占用率
ZStack/OvnVmInstance/VRouterCPUUserUtilization: VPC路由器CPU用户占用率
ZStack/OvnVmInstance/VRouterCPUWaitUtilization: VPC路由器CPU IO等待占用率
ZStack/OvnVmInstance/VRouterCPUIdleUtilization: VPC路由器CPU空闲率
ZStack/OvnVmInstance/VRouterCPUUsedUtilization: VPC路由器CPU使用率
ZStack/OvnVmInstance/VRouterCPUAverageSystemUtilization: VPC路由器CPU平均系统占用率
ZStack/OvnVmInstance/VRouterCPUAverageUserUtilization: VPC路由器CPU平均用户占用率
ZStack/OvnVmInstance/VRouterCPUAverageWaitUtilization: VPC路由器CPU平均IO等待占用率
ZStack/OvnVmInstance/VRouterCPUAverageIdleUtilization: VPC路由器CPU平均空闲率
ZStack/OvnVmInstance/VRouterCPUAverageUsedUtilization: VPC路由器CPU平均使用率
ZStack/OvnVmInstance/VRouterDiskAllFreeCapacityInBytes: VPC路由器全部磁盘可用字节数
ZStack/OvnVmInstance/VRouterDiskAllFreeCapacityInPercent: VPC路由器全部磁盘百分比
ZStack/OvnVmInstance/VRouterDiskAllUsedCapacityInBytes: VPC路由器全部磁盘使用字节数
ZStack/OvnVmInstance/VRouterDiskAllUsedCapacityInPercent: VPC路由器全部磁盘使用百分比
ZStack/OvnVmInstance/VRouterDiskFreeCapacityInBytes: VPC路由器磁盘可用字节数
ZStack/OvnVmInstance/VRouterDiskFreeCapacityInPercent: VPC路由器磁盘百分比
ZStack/OvnVmInstance/VRouterDiskUsedCapacityInBytes: VPC路由器磁盘使用字节数
ZStack/OvnVmInstance/VRouterDiskUsedCapacityInPercent: VPC路由器磁盘使用百分比
ZStack/OvnVmInstance/VRouterMemoryTotalBytes: VPC路由器内存总字节数
ZStack/OvnVmInstance/VRouterMemoryFreeBytes: VPC路由器内存空余节数
ZStack/OvnVmInstance/VRouterMemoryUsedBytes: VPC路由器内存已使用节数
ZStack/OvnVmInstance/VRouterMemoryAvailableBytes: VPC路由器内存可用节数
ZStack/OvnVmInstance/VRouterMemoryFreePercent: VPC路由器内存空闲百分比
ZStack/OvnVmInstance/VRouterMemoryUsedPercent:  VPC路由器内存使用百分比
ZStack/OvnVmInstance/ReclaimedMemoryInBytes: 虚拟机回收内存字节数
ZStack/OvnVmInstance/VGpuUtilization: 虚拟机使用VGPU的利用率
ZStack/OvnVmInstance/VGpuMemoryUtilization: 虚拟机使用VGPU的显存使用率
ZStack/OvnVmInstance/GpuPowerDraw: 虚拟机中GPU使用功率
ZStack/OvnVmInstance/GpuTemperature: 虚拟机中GPU温度
ZStack/OvnVmInstance/GpuFanSpeed: 虚拟机中GPU风扇速度
ZStack/OvnVmInstance/GpuUtilization: 虚拟机中GPU使用率
ZStack/OvnVmInstance/GpuDdrCapacity: 虚拟机中GPU DDR容量
ZStack/OvnVmInstance/GpuDdrUsageRate: 虚拟机中GPU DDR使用率
ZStack/OvnVmInstance/GpuHbmCapacity: 虚拟机中GPU HBM容量
ZStack/OvnVmInstance/GpuHbmUsageRate: 虚拟机中GPU HBM使用率
ZStack/OvnVmInstance/GpuStatus: 虚拟机中GPU状态
ZStack/OvnVmInstance/GpuMemoryUtilization: 虚拟机中GPU内存使用率
ZStack/OvnVmInstance/GpuPciRxThroughputInBytes: 虚拟机中GPU PCIE RX吞吐量
ZStack/OvnVmInstance/GpuPciTxThroughputInBytes: 虚拟机中GPU PCIE TX吞吐量
ZStack/ModelCenter/ServiceStatus: 模型平台服务状态
ZStack/Zdfs/MetadataServerUp: Zdfs元数据服务是否运行
ZStack/Zdfs/MetadataServerInstanceInfo: Zdfs元数据服务实例信息
ZStack/Zdfs/MetadataServerSlaveCount: Zdfs元数据服务从节点数量
ZStack/Zdfs/MetadataServerSentinelCount: Zdfs元数据服务哨兵数量
ZStack/Zdfs/MetadataServerMemoryUsedCapacityInBytes: Zdfs元数据服务实例已用内存
ZStack/Zdfs/MetadataServerMaxMemoryInBytes: Zdfs元数据服务实例最大可用内存
ZStack/Zdfs/MetadataServerConnectClients: Zdfs元数据服务实例已连接客户端数
ZStack/Zdfs/MetadataServerMaxClients: Zdfs元数据服务实例最大可连接客户端数
ZStack/Zdfs/MetadataServerMasterSyncInProgress: Zdfs元数据服务实例是否正在同步主节点数据
ZStack/Zdfs/MetadataServerReplLagBytes: Zdfs元数据服务实例待同步数据量