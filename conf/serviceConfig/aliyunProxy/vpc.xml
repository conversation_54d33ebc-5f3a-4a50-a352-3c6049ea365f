<?xml version="1.0" encoding="UTF-8"?>
<service xmlns="http://zstack.org/schema/zstack">
    <id>aliyun.proxy.vpc</id>
    <interceptor>AliyunProxyVpcApiInterceptor</interceptor>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APICreateAliyunProxyVpcMsg</name>
    </message>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APICreateAliyunProxyVSwitchMsg</name>
    </message>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APIUpdateAliyunProxyVpcMsg</name>
    </message>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APIUpdateAliyunProxyVSwitchMsg</name>
    </message>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APIQueryAliyunProxyVpcMsg</name>
        <serviceId>query</serviceId>
    </message>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APIQueryAliyunProxyVSwitchMsg</name>
        <serviceId>query</serviceId>
    </message>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APIDeleteAliyunProxyVpcMsg</name>
    </message>
    <message>
        <name>org.zstack.aliyunproxy.vpc.APIDeleteAliyunProxyVSwitchMsg</name>
    </message>
</service>