{"org.zstack.appliancevm.ApplianceVmKvmCommands$PrepareBootstrapInfoCmd": {"PATH": "3.10.38", "bootStrapInfoTimeout": "3.10.38", "info": "3.10.38", "kvmHostAddons": "3.10.38", "socketPath": "3.10.38"}, "org.zstack.appliancevm.ApplianceVmKvmCommands$PrepareBootstrapInfoRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.cbd.kvm.KvmCbdCommands$AgentCmd": {"kvmHostAddons": "5.2.0", "uuid": "5.2.0"}, "org.zstack.cbd.kvm.KvmCbdCommands$KvmCancelSelfFencerCmd": {"kvmHostAddons": "5.3.28", "uuid": "5.3.28"}, "org.zstack.cbd.kvm.KvmCbdCommands$KvmSetupSelfFencerCmd": {"coveringPaths": "5.2.0", "fencers": "5.2.0", "heartbeatRequiredSpace": "5.2.0", "heartbeatUrl": "5.2.0", "hostId": "5.2.0", "hostUuid": "5.2.0", "interval": "5.2.0", "kvmHostAddons": "5.2.0", "maxAttempts": "5.2.0", "storageCheckerTimeout": "5.2.0", "strategy": "5.2.0", "uuid": "5.2.0"}, "org.zstack.cbd.kvm.KvmCbdCommands$KvmUpdateClientConfCmd": {"kvmHostAddons": "5.2.0", "mdsInfos": "5.2.0", "uuid": "5.2.0"}, "org.zstack.compute.host.HostXfsFragReader$GetXfsDataCmd": {"kvmHostAddons": "3.10.38", "volumePathMap": "3.10.38"}, "org.zstack.compute.host.HostXfsFragReader$GetXfsDataRsp": {"error": "3.10.38", "fsType": "3.10.38", "hostFrag": "3.10.38", "success": "3.10.38", "volumeFragMap": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$AddBridgeFdbEntryCmd": {"kvmHostAddons": "3.10.38", "macs": "3.10.38", "physicalInterface": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$AddBridgeFdbEntryRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ApplyMemoryBalloonCmd": {"adjustPercent": "4.7.22", "direction": "4.7.22", "kvmHostAddons": "4.7.22", "vmReservedMemory": "4.7.22", "vmUuids": "4.7.22"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ApplyMemoryBalloonResponse": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.compute.host.MevocoKVMAgentCommands$AttachNicToBondingCmd": {"bondName": "4.7.11", "kvmHostAddons": "4.7.11", "slaves": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$AttachNicToBondingRsp": {"error": "4.7.11", "success": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$BlockStreamVolumeCmd": {"kvmHostAddons": "3.10.38", "taskContext": "4.4.58", "threadContext": "4.4.58", "threadContextStack": "4.4.58", "vmUuid": "3.10.38", "volume": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$BridgeRouterPortCmd": {"enable": "3.10.38", "kvmHostAddons": "3.10.38", "nicNames": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$BridgeRouterPortResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ChangeEmulatorPinnningCmd": {"emulatorPinning": "4.3.12", "kvmHostAddons": "4.3.12", "uuid": "4.3.12"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ChangeEmulatorPinnningResponse": {"error": "4.3.12", "success": "4.3.12"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ChangeHostPasswordCmd": {"kvmHostAddons": "3.10.38", "password": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ChangeVfNicHaStateCmd": {"haState": "5.2.0", "kvmHostAddons": "5.2.0", "nic": "5.2.0", "vmUuid": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ChangeVfNicHaStateRsp": {"error": "5.2.0", "success": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ChangeVmPasswordCmd": {"accountPerference": "3.10.38", "kvmHostAddons": "3.10.38", "timeout": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ChangeVmPasswordResponse": {"accountPerference": "3.10.38", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$CheckInterfaceVlanCmd": {"interfaceName": "4.7.11", "kvmHostAddons": "4.7.11", "vlanId": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$CheckInterfaceVlanRsp": {"error": "4.7.11", "success": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$CheckMountDomainCmd": {"kvmHostAddons": "3.10.38", "timeout": "3.10.38", "url": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$CheckMountDomainResponse": {"active": "3.10.38", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$CheckVmVolumesCmd": {"kvmHostAddons": "3.10.38", "uuid": "3.10.38", "volumes": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$CreateBondingCmd": {"bondName": "4.7.0", "kvmHostAddons": "4.7.0", "mode": "4.7.0", "slaves": "4.7.0", "type": "4.7.0", "xmitHashPolicy": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$CreateBondingRsp": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DelScsiControllerCmd": {"ioThreadId": "4.6.31", "kvmHostAddons": "4.6.31", "vmUuid": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DelScsiControllerRsp": {"error": "4.6.31", "ioThreadId": "4.6.31", "success": "4.6.31", "vmUuid": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DelVmIoThreadPinCmd": {"ioThreadId": "4.6.31", "kvmHostAddons": "4.6.31", "vmUuid": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DelVmIoThreadPinRsp": {"error": "4.6.31", "ioThreadId": "4.6.31", "success": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DeleteBondingCmd": {"bondName": "4.7.0", "kvmHostAddons": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DeleteBondingRsp": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DetachNicFromBondingCmd": {"bondName": "4.7.11", "kvmHostAddons": "4.7.11", "slaves": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DetachNicFromBondingRsp": {"error": "4.7.11", "success": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DisableHugePageCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DisableHugePageResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DisableZeroCopyCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$DisableZeroCopyResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$EnableHugePageCmd": {"kvmHostAddons": "3.10.38", "pageSize": "3.10.38", "reserveSize": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$EnableHugePageResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$EnableZeroCopyCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$EnableZeroCopyResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetGuestToolsStateCmd": {"kvmHostAddons": "4.6.11", "vmInstanceUuids": "4.6.21"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetHostNetworkBondingCmd": {"kvmHostAddons": "3.10.38", "managementServerIp": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetHostNetworkBondingResponse": {"bondings": "3.10.38", "error": "3.10.38", "nics": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetHostPhysicalMemoryFactsCmd": {"kvmHostAddons": "4.3.10"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetHostPhysicalMemoryFactsResponse": {"error": "4.3.10", "physicalMemoryFacts": "4.3.10", "success": "4.3.10"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetInterfaceNameCmd": {"ipAddresses": "4.7.11", "kvmHostAddons": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetInterfaceNameRsp": {"error": "4.7.11", "interfaceNames": "4.7.11", "success": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetInterfaceVlanCmd": {"interfaceNames": "4.7.11", "kvmHostAddons": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetInterfaceVlanRsp": {"error": "4.7.11", "success": "4.7.11", "vlanIds": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetVmIoThreadPinCmd": {"kvmHostAddons": "4.6.31", "vmUuid": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GetVmIoThreadPinRsp": {"error": "4.6.31", "ioThreadInfo": "4.6.31", "success": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$GuestToolsStateResp": {"error": "4.6.11", "states": "4.6.11", "success": "4.6.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$IdentifyHostCmd": {"interval": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$LocateHostNetworkInterfaceCmd": {"interval": "4.3.10", "kvmHostAddons": "4.3.10", "networkInterface": "4.3.10"}, "org.zstack.compute.host.MevocoKVMAgentCommands$NicAgentResponse": {"error": "3.10.38", "inbound": "3.10.38", "outbound": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$NicQosCmd": {"inboundBandwidth": "3.10.38", "internalName": "3.10.38", "kvmHostAddons": "3.10.38", "outboundBandwidth": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$PreconfigureOvsDpdkCmd": {"kvmHostAddons": "4.7.22", "pageSize": "4.7.22", "reserveSize": "4.7.22", "socketMem": "4.7.22"}, "org.zstack.compute.host.MevocoKVMAgentCommands$PreconfigureOvsDpdkCmdRsp": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ResizeVolumeCmd": {"deviceType": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "size": "3.10.38", "vmUuid": "3.10.38", "volume": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$ResizeVolumeResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetHostPhysicalNicMonitorCmd": {"enable": "4.4.0", "error": "4.4.0", "nics": "4.4.0", "success": "4.4.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetHostPhysicalNicMonitorRsp": {"error": "4.4.0", "success": "4.4.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetIpOnHostNetworkInterfaceCmd": {"gateway": "4.7.0", "interfaceName": "4.7.0", "ipAddress": "4.7.0", "kvmHostAddons": "4.7.0", "netmask": "4.7.0", "oldGateway": "4.7.0", "oldIpAddress": "4.7.0", "oldNetmask": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetIpOnHostNetworkInterfaceRsp": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetScsiControllerCmd": {"ioThreadId": "4.6.31", "kvmHostAddons": "4.6.31", "vmUuid": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetScsiControllerRsp": {"controllerIndex": "4.6.31", "error": "4.6.31", "ioThreadId": "4.6.31", "success": "4.6.31", "vmUuid": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetServiceTypeOnHostNetworkInterfaceCmd": {"interfaceName": "4.7.11", "kvmHostAddons": "4.7.11", "serviceType": "4.7.11", "vlanId": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetServiceTypeOnHostNetworkInterfaceRsp": {"error": "4.7.11", "success": "4.7.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetSyncVmClockTaskCmd": {"intervalMap": "4.4.24", "kvmHostAddons": "4.4.24"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetSyncVmClockTaskRsp": {"error": "4.4.24", "success": "4.4.24"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetVmHostNameCmd": {"defaultIP": "4.7.0", "hostName": "4.7.0", "kvmHostAddons": "4.7.0", "vmUuid": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetVmHostNameResponse": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetVmIoThreadPinCmd": {"ioThreadId": "4.6.31", "kvmHostAddons": "4.6.31", "pin": "4.6.31", "vmUuid": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SetVmIoThreadPinRsp": {"error": "4.6.31", "ioThreadId": "4.6.31", "success": "4.6.31"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncGpuInfoByGuesttoolsCommand": {"kvmHostAddons": "5.2.0", "vendors": "5.2.0", "vmUuid": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncGpuInfoByGuesttoolsResponse": {"error": "5.2.0", "gpuInfos": "5.2.0", "success": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncVdpaCmd": {"kvmHostAddons": "4.7.22", "ovsBridgeInfo": "4.7.22"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncVdpaRsp": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncVmClockCmd": {"kvmHostAddons": "4.4.24", "vmUuid": "4.4.24"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncVmClockRsp": {"error": "4.4.24", "success": "4.4.24"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncVmPortsCommand": {"defaultIP": "5.2.0", "hostname": "5.2.0", "kvmHostAddons": "4.6.11", "portsConfig": "4.6.11", "vmUuid": "4.6.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$SyncVmPortsResponse": {"error": "4.6.11", "success": "4.6.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$TakeSnapshotsCmd": {"kvmHostAddons": "3.10.38", "snapshotJobs": "3.10.38", "taskContext": "4.4.58", "threadContext": "4.4.58", "threadContextStack": "4.4.58", "timeout": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$TakeSnapshotsResponse": {"error": "3.10.38", "snapshots": "3.10.38", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$UpdateBondingCmd": {"bondName": "4.7.0", "kvmHostAddons": "4.7.0", "mode": "4.7.0", "oldSlaves": "4.7.0", "slaves": "4.7.0", "xmitHashPolicy": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$UpdateBondingRsp": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$UpdateOvsCpuPinningCmd": {"kvmHostAddons": "4.6.0", "ovsCpuPinning": "4.6.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$UpdateOvsCpuPinningResponse": {"error": "4.6.0", "success": "4.6.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$VmConfigSyncResponse": {"error": "4.6.11", "success": "4.6.11"}, "org.zstack.compute.host.MevocoKVMAgentCommands$VmScriptExecCmd": {"dstPath": "5.2.0", "kvmHostAddons": "5.2.0", "logPath": "5.2.0", "scriptTimeout": "5.2.0", "scriptType": "5.2.0", "vmUuid": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$VmScriptExecRsp": {"error": "5.2.0", "exitCode": "5.2.0", "stderr": "5.2.0", "stdout": "5.2.0", "success": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$VmUploadCmd": {"dstPath": "5.2.0", "fileContent": "5.2.0", "fileType": "5.2.0", "kvmHostAddons": "5.2.0", "param": "5.2.0", "scriptType": "5.2.0", "vmUuid": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$VmUploadRsp": {"error": "5.2.0", "success": "5.2.0"}, "org.zstack.compute.host.MevocoKVMAgentCommands$VolumeAgentResponse": {"bandWidth": "3.10.38", "bandWidthRead": "3.10.38", "bandWidthWrite": "3.10.38", "error": "3.10.38", "iopsRead": "4.4.0", "iopsTotal": "4.4.0", "iopsWrite": "4.4.0", "success": "3.10.38"}, "org.zstack.compute.host.MevocoKVMAgentCommands$VolumeQosCmd": {"kvmHostAddons": "3.10.38", "mode": "3.10.38", "readBandwidth": "3.10.38", "readIOPS": "4.4.0", "totalBandwidth": "3.10.38", "totalIOPS": "4.4.0", "vmUuid": "3.10.38", "volume": "3.10.38", "writeBandwidth": "3.10.38", "writeIOPS": "4.4.0"}, "org.zstack.compute.vm.virtio.VirtIODriverCommands$DetachVirtIODriverFromVmCmd": {"driverFormat": "4.4.64", "kvmHostAddons": "4.4.64", "vmInstanceUuid": "4.4.64"}, "org.zstack.compute.vm.virtio.VirtIODriverCommands$DetachVirtIODriverFromVmRsp": {"error": "4.4.64", "success": "4.4.64"}, "org.zstack.externalbackup.zbox.kvm.ZBoxBackupKvmBackend$InitZBoxBackupCmd": {"backupUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPath": "4.0.0", "name": "3.10.38", "zboxUuid": "3.10.38"}, "org.zstack.externalbackup.zbox.kvm.ZBoxBackupKvmBackend$InitZBoxBackupRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.externalbackup.zbox.kvm.ZBoxBackupKvmBackend$TakeVolumesBackupCmd": {"dstDeviceIdPath": "3.10.38", "kvmHostAddons": "3.10.38", "remoteInfo": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "vmUuid": "3.10.38", "volumes": "3.10.38"}, "org.zstack.externalbackup.zbox.kvm.ZBoxBackupKvmBackend$TaskVolumesBackupRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.faulttolerance.FaultToleranceManagerImpl$CheckColoVmStateCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "vmInstanceUuid": "3.10.38"}, "org.zstack.faulttolerance.FaultToleranceManagerImpl$CheckColoVmStateRsp": {"currentMode": "3.10.38", "error": "3.10.38", "state": "3.10.38", "success": "3.10.38"}, "org.zstack.faulttolerance.FaultToleranceManagerImpl$DeployColoQemuCmd": {"kvmHostAddons": "3.10.38", "qemuUrl": "3.10.38", "taskContext": "4.3.10", "threadContext": "4.3.10", "threadContextStack": "4.3.10"}, "org.zstack.faulttolerance.FaultToleranceManagerImpl$FailColoPrimaryVmCmd": {"kvmHostAddons": "3.10.38", "targetHostIp": "3.10.38", "targetHostPassword": "3.10.38", "targetHostPort": "3.10.38", "vmInstanceUuid": "3.10.38"}, "org.zstack.faulttolerance.FaultToleranceManagerImpl$RollbackQuorumConfigCmd": {"kvmHostAddons": "3.10.38", "nicNumber": "3.10.38", "vmInstanceUuid": "3.10.38", "volumes": "3.10.38"}, "org.zstack.faulttolerance.FaultToleranceManagerImpl$SetupSelfFencerCmd": {"hostManagementIp": "4.3.10", "hostUuid": "4.3.10", "kvmHostAddons": "4.3.10", "peerHostManagementNetworkIp": "4.3.10", "peerHostStorageNetworkIp": "4.3.10"}, "org.zstack.faulttolerance.FaultToleranceManagerImpl$WaitColoVmReadyCmd": {"coloCheckTimeout": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "vmInstanceUuid": "3.10.38"}, "org.zstack.flowMeter.FlowMeterBackend$GetFlowCounterRsp": {"counters": "3.10.38", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.flowMeter.FlowMeterBackend$SetFlowMeterCmd": {"flowMeterInfor": "3.10.38"}, "org.zstack.flowMeter.FlowMeterBackend$SetFlowMeterRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$AttachGuestToolsIsoToVmCmd": {"kvmHostAddons": "3.10.38", "needTempDisk": "3.10.38", "platform": "4.6.11", "vmInstanceUuid": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$AttachGuestToolsIsoToVmRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$DetachGuestToolsIsoFromVmCmd": {"kvmHostAddons": "3.10.38", "platform": "4.6.11", "vmInstanceUuid": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$DetachGuestToolsIsoFromVmRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$DownloadGuestToolsIsoToHostCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$DownloadGuestToolsIsoToHostRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$GetVmGuestToolsInfoCmd": {"kvmHostAddons": "3.10.38", "platform": "4.2.0", "vmInstanceUuid": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$GetVmGuestToolsInfoRsp": {"error": "3.10.38", "features": "4.2.0", "status": "3.10.38", "success": "3.10.38", "version": "3.10.38"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$GetVmMetricsRoutingStatusCmd": {"items": "4.4.52", "kvmHostAddons": "4.4.52", "vmInstanceUuid": "4.4.52"}, "org.zstack.guesttools.kvm.GuestToolsKvmCommands$GetVmMetricsRoutingStatusRsp": {"error": "4.4.52", "success": "4.4.52", "values": "4.4.52"}, "org.zstack.ha.HaManagerImpl$AddVmFencerRuleToHostCmd": {"allowRules": "4.7.0", "blockRules": "4.7.0", "hostUuid": "4.7.0", "kvmHostAddons": "4.7.0"}, "org.zstack.ha.HaManagerImpl$AddVmFencerRuleToHostRsp": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.ha.HaManagerImpl$RemoveVmFencerRuleFromHostCmd": {"allowRules": "4.7.0", "blockRules": "4.7.0", "hostUuid": "4.7.0", "kvmHostAddons": "4.7.0"}, "org.zstack.ha.HaManagerImpl$RemoveVmFencerRuleFromHostRsp": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.ha.SelfFencerKvmBackend$GetFencerStateFromHostCmd": {"kvmHostAddons": "5.3.20"}, "org.zstack.ha.SelfFencerKvmBackend$GetFencerStateFromHostRsp": {"error": "5.3.20", "psUuids": "5.3.20", "success": "5.3.20"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$CreateIPsecConnectionCmd": {"autoRestartVpn": "3.10.38", "infos": "3.10.38"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$CreateIPsecConnectionRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$DeleteIPsecConnectionCmd": {"infos": "3.10.38"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$DeleteIPsecConnectionRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$GetIPsecLogCmd": {"lines": "4.5.0"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$GetIPsecLogRsp": {"error": "4.5.0", "ipsecLog": "4.5.0", "success": "4.5.0"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$SyncIPsecConnectionCmd": {"autoRestartVpn": "3.10.38", "infos": "3.10.38", "needStatus": "4.5.0"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$SyncIPsecConnectionRsp": {"downIpsecConns": "4.5.0", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$UpdateIPsecConnectionCmd": {"infos": "3.10.38"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$UpdateIPsecConnectionRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.ipsec.vyos.VyosIPsecBackend$UpdateIPsecVersionCmd": {"autoRestartVpn": "4.4.24", "infos": "4.4.24", "targetVersion": "4.4.24"}, "org.zstack.iscsi.kvm.KvmIscsiCommands$AgentCmd": {"kvmHostAddons": "5.2.0", "uuid": "5.2.0"}, "org.zstack.iscsi.kvm.KvmIscsiCommands$KvmCancelSelfFencerCmd": {"hostId": "5.2.0", "hostUuid": "5.2.0", "installPath": "5.2.0", "kvmHostAddons": "5.2.0", "uuid": "5.2.0"}, "org.zstack.iscsi.kvm.KvmIscsiCommands$KvmSetupSelfFencerCmd": {"coveringPaths": "5.2.0", "fencers": "5.2.0", "heartbeatRequiredSpace": "5.2.0", "heartbeatUrl": "5.2.0", "hostId": "5.2.0", "hostUuid": "5.2.0", "interval": "5.2.0", "kvmHostAddons": "5.2.0", "maxAttempts": "5.2.0", "storageCheckerTimeout": "5.2.0", "strategy": "5.2.0", "uuid": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$AddInterfaceToBridgeCmd": {"bridgeName": "3.10.38", "kvmHostAddons": "3.10.38", "physicalInterfaceName": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$AddInterfaceToBridgeResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ApplySecurityGroupRuleCmd": {"ip6RuleTOs": "4.7.22", "kvmHostAddons": "3.10.38", "ruleTOs": "3.10.38", "vmNicTOs": "4.7.22"}, "org.zstack.kvm.KVMAgentCommands$ApplySecurityGroupRuleResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$AttachDataVolumeCmd": {"addons": "3.10.38", "kvmHostAddons": "3.10.38", "vmInstanceUuid": "3.10.38", "volume": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$AttachDataVolumeResponse": {"error": "3.10.38", "success": "3.10.38", "virtualDeviceInfoList": "4.4.24"}, "org.zstack.kvm.KVMAgentCommands$AttachIsoCmd": {"iso": "3.10.38", "kvmHostAddons": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$AttachIsoRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$AttachNicCommand": {"accountUuid": "4.4.52", "addons": "3.10.38", "kvmHostAddons": "3.10.38", "nic": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$AttachNicResponse": {"error": "3.10.38", "success": "3.10.38", "virtualDeviceInfoList": "4.4.24"}, "org.zstack.kvm.KVMAgentCommands$AttachVolumeCmd": {"device": "4.5.0", "kvmHostAddons": "4.5.0", "mountPath": "4.5.0", "volumeInstallPath": "4.5.0", "volumePrimaryStorageUuid": "4.5.0"}, "org.zstack.kvm.KVMAgentCommands$AttachVolumeRsp": {"device": "4.5.0", "error": "4.5.0", "success": "4.5.0"}, "org.zstack.kvm.KVMAgentCommands$BlockCommitVolumeCmd": {"base": "4.8.0", "kvmHostAddons": "4.8.0", "taskContext": "4.8.0", "threadContext": "4.8.0", "threadContextStack": "4.8.0", "top": "4.8.0", "vmUuid": "4.8.0", "volume": "4.8.0", "volumeUuid": "4.8.0"}, "org.zstack.kvm.KVMAgentCommands$BlockCommitVolumeResponse": {"error": "4.8.0", "newVolumeInstallPath": "4.8.0", "size": "4.8.0", "success": "4.8.0"}, "org.zstack.kvm.KVMAgentCommands$CancelCmd": {"cancellationApiId": "3.10.38", "interval": "4.6.21", "kvmHostAddons": "3.10.38", "times": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$CancelRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ChangeCpuMemoryCmd": {"cpuNum": "3.10.38", "kvmHostAddons": "3.10.38", "memorySize": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ChangeCpuMemoryResponse": {"cpuNum": "3.10.38", "error": "3.10.38", "memorySize": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ChangeVmNicStateCommand": {"kvmHostAddons": "4.5.11", "nic": "4.5.11", "state": "4.5.11", "vmUuid": "4.5.11"}, "org.zstack.kvm.KVMAgentCommands$ChangeVmNicStateRsp": {"error": "4.5.11", "success": "4.5.11"}, "org.zstack.kvm.KVMAgentCommands$CheckBridgeCmd": {"bridgeName": "3.10.38", "kvmHostAddons": "3.10.38", "physicalInterfaceName": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckBridgeResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckDefaultSecurityGroupCmd": {"disableIp6Tables": "5.3.0", "kvmHostAddons": "3.10.38", "skipIpv6": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckDefaultSecurityGroupResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckFileOnHostCmd": {"kvmHostAddons": "4.3.12", "md5Return": "4.3.12", "paths": "4.3.12"}, "org.zstack.kvm.KVMAgentCommands$CheckFileOnHostResponse": {"error": "4.3.12", "existPaths": "4.3.12", "success": "4.3.12"}, "org.zstack.kvm.KVMAgentCommands$CheckPhysicalNetworkInterfaceCmd": {"interfaceNames": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckPhysicalNetworkInterfaceResponse": {"error": "3.10.38", "failedInterfaceNames": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckSnapshotCmd": {"currentInstallPath": "3.10.38", "excludeInstallPaths": "3.10.38", "kvmHostAddons": "3.10.38", "vmUuid": "3.10.38", "volumeChainToCheck": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckSnapshotResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckVlanBridgeCmd": {"bridgeName": "3.10.38", "kvmHostAddons": "3.10.38", "physicalInterfaceName": "3.10.38", "vlan": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckVlanBridgeResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckVmStateCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "vmUuids": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CheckVmStateRsp": {"error": "3.10.38", "states": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CleanVmFirmwareFlashCmd": {"kvmHostAddons": "4.4.52", "vmUuid": "4.4.52"}, "org.zstack.kvm.KVMAgentCommands$CleanupUnusedRulesOnHostCmd": {"disableIp6Tables": "5.3.0", "kvmHostAddons": "3.10.38", "skipIpv6": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CleanupUnusedRulesOnHostResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ConfigPrimaryVmCmd": {"configs": "3.10.38", "hostIp": "3.10.38", "kvmHostAddons": "3.10.38", "vmInstanceUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ConfigSecondaryVmCmd": {"kvmHostAddons": "3.10.38", "mirrorPort": "3.10.38", "nbdServerPort": "3.10.38", "primaryVmHostIp": "3.10.38", "secondaryInPort": "3.10.38", "vmInstanceUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ConnectCmd": {"hostUuid": "3.10.38", "ignoreMsrs": "3.10.38", "iptablesRules": "3.10.38", "isInstallHostShutdownHook": null, "kvmHostAddons": "3.10.38", "pageTableExtensionDisabled": "3.10.38", "sendCommandUrl": "3.10.38", "tcpServerPort": "3.10.38", "version": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ConnectResponse": {"error": "3.10.38", "libvirtVersion": "3.10.38", "qemuVersion": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CreateBridgeCmd": {"bridgeName": "3.10.38", "disableIptables": "3.10.38", "igmpVersion": null, "isolated": "5.2.0", "kvmHostAddons": "3.10.38", "l2NetworkUuid": "3.10.38", "mldVersion": null, "mtu": "3.10.38", "physicalInterfaceName": "3.10.38", "pvlan": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$CreateBridgeResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CreateVlanBridgeCmd": {"bridgeName": "3.10.38", "disableIptables": "3.10.38", "igmpVersion": null, "isolated": "5.2.0", "kvmHostAddons": "3.10.38", "l2NetworkUuid": "3.10.38", "mldVersion": null, "mtu": "3.10.38", "physicalInterfaceName": "3.10.38", "pvlan": "5.2.0", "vlan": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$CreateVlanBridgeResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DeleteBridgeCmd": {"bridgeName": "4.1.2", "kvmHostAddons": "4.1.2", "l2NetworkUuid": "4.1.2", "physicalInterfaceName": "4.1.2"}, "org.zstack.kvm.KVMAgentCommands$DeleteBridgeResponse": {"error": "4.1.2", "success": "4.1.2"}, "org.zstack.kvm.KVMAgentCommands$DeleteVHostUserClientCmd": {"kvmHostAddons": "4.6.0", "nicInternalName": "4.6.0", "vmUuid": "4.6.0"}, "org.zstack.kvm.KVMAgentCommands$DeleteVHostUserClientRsp": {"error": "4.6.0", "success": "4.6.0"}, "org.zstack.kvm.KVMAgentCommands$DeleteVdpaCmd": {"kvmHostAddons": "4.1.2", "nicInternalName": "4.3.0", "vmUuid": "4.1.2"}, "org.zstack.kvm.KVMAgentCommands$DeleteVdpaRsp": {"error": "4.3.0", "success": "4.3.0"}, "org.zstack.kvm.KVMAgentCommands$DeleteVlanBridgeCmd": {"bridgeName": "4.1.2", "kvmHostAddons": "4.1.2", "l2NetworkUuid": "4.1.2", "physicalInterfaceName": "4.1.2", "vlan": "4.1.2"}, "org.zstack.kvm.KVMAgentCommands$DeleteVlanBridgeResponse": {"error": "4.1.2", "success": "4.1.2"}, "org.zstack.kvm.KVMAgentCommands$DeleteVmConsoleFirewallCmd": {"hostManagementIp": "3.10.38", "kvmHostAddons": "3.10.38", "vmInternalId": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DestroyVmCmd": {"kvmHostAddons": "3.10.38", "uuid": "3.10.38", "vmNics": "4.4.52"}, "org.zstack.kvm.KVMAgentCommands$DestroyVmResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DetachDataVolumeCmd": {"kvmHostAddons": "3.10.38", "vmInstanceUuid": "3.10.38", "volume": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DetachDataVolumeResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DetachIsoCmd": {"deviceId": "3.10.38", "isoUuid": "3.10.38", "kvmHostAddons": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DetachIsoRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DetachNicCommand": {"kvmHostAddons": "3.10.38", "nic": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DetachNicRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$DetachVolumeCmd": {"device": "4.5.0", "kvmHostAddons": "4.5.0", "mountPath": "4.5.0", "volumeInstallPath": "4.5.0"}, "org.zstack.kvm.KVMAgentCommands$DetachVolumeRsp": {"error": "4.5.0", "success": "4.5.0"}, "org.zstack.kvm.KVMAgentCommands$GenerateVHostUserClientCmd": {"kvmHostAddons": "4.6.0", "nics": "4.6.0", "vmUuid": "4.6.0"}, "org.zstack.kvm.KVMAgentCommands$GenerateVHostUserClientResponse": {"error": "4.6.0", "success": "4.6.0"}, "org.zstack.kvm.KVMAgentCommands$GenerateVdpaCmd": {"kvmHostAddons": "4.1.2", "nics": "4.1.2", "vmUuid": "4.1.2"}, "org.zstack.kvm.KVMAgentCommands$GenerateVdpaResponse": {"error": "4.1.2", "success": "4.1.2", "vdpaPaths": "4.1.2"}, "org.zstack.kvm.KVMAgentCommands$GetDevCapacityCmd": {"dirPath": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$GetDevCapacityResponse": {"availableSize": "3.10.38", "dirSize": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalSize": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$GetHostNUMATopologyCmd": {"HostUuid": "4.3.12", "kvmHostAddons": "4.3.12"}, "org.zstack.kvm.KVMAgentCommands$GetHostNUMATopologyResponse": {"error": "4.3.12", "success": "4.3.12", "topology": "4.3.12"}, "org.zstack.kvm.KVMAgentCommands$GetVirtualizerInfoCmd": {"kvmHostAddons": "4.6.21", "vmUuids": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$GetVirtualizerInfoRsp": {"error": "4.6.21", "hostInfo": "4.6.21", "success": "4.6.21", "vmInfoList": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$GetVmDeviceAddressCmd": {"deviceTOs": "3.10.38", "kvmHostAddons": "3.10.38", "uuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$GetVmDeviceAddressRsp": {"addresses": "3.10.38", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$GetVmFirstBootDeviceCmd": {"kvmHostAddons": "3.10.38", "uuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$GetVmFirstBootDeviceResponse": {"error": "3.10.38", "firstBootDevice": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$GetVncPortCmd": {"kvmHostAddons": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$GetVncPortResponse": {"error": "3.10.38", "port": "3.10.38", "protocol": "3.10.38", "spicePort": "3.10.38", "spiceTlsPort": "3.10.38", "success": "3.10.38", "vncPort": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$HardenVmConsoleCmd": {"hostManagementIp": "3.10.38", "kvmHostAddons": "3.10.38", "vmInternalId": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$HardwareMonitorCmd": {"kvmHostAddons": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$HostCapacityCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$HostCapacityResponse": {"cpuNum": "3.10.38", "cpuSockets": "3.10.38", "cpuSpeed": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalMemory": "3.10.38", "usedCpu": "3.10.38", "usedMemory": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$HostFactCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$HostFactResponse": {"biosReleaseDate": "4.7.0", "biosVendor": "4.7.0", "biosVersion": "4.7.0", "bmcVersion": "4.7.0", "cpuArchitecture": "3.10.38", "cpuCache": "4.4.24", "cpuGHz": "3.10.38", "cpuModelName": "3.10.38", "cpuProcessorNum": "4.4.24", "deployMode": "5.0.0", "eptFlag": "3.10.38", "error": "3.10.38", "hostCpuModelName": "3.10.38", "hvmCpuFlag": "3.10.38", "ipAddresses": "3.10.38", "ipmiAddress": "4.4.34", "iscsiInitiatorName": "5.2.0", "libvirtCapabilities": "4.4.0", "libvirtPackageVersion": "5.0.0", "libvirtVersion": "3.10.38", "memorySlotsMaximum": "4.7.0", "osDistribution": "3.10.38", "osRelease": "3.10.38", "osVersion": "3.10.38", "powerSupplyManufacturer": "4.4.24", "powerSupplyMaxPowerCapacity": "4.4.24", "powerSupplyModelName": "4.4.24", "qemuImgVersion": "3.10.38", "success": "3.10.38", "systemManufacturer": "4.7.0", "systemProductName": "3.10.38", "systemSerialNumber": "3.10.38", "systemUUID": "4.7.0", "uptime": "4.7.0", "virtualizerInfo": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$IncreaseCpuCmd": {"cpuNum": "3.10.38", "kvmHostAddons": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$IncreaseCpuResponse": {"cpuNum": "3.10.38", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$IncreaseMemoryCmd": {"kvmHostAddons": "3.10.38", "memorySize": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$IncreaseMemoryResponse": {"error": "3.10.38", "memorySize": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$InstallOvsPackageCmd": {"kvmHostAddons": "5.3.20", "ovnControllerIp": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$InstallOvsPackageRsp": {"error": "5.3.20", "success": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$LoginIscsiTargetCmd": {"chapPassword": "3.10.38", "chapUsername": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38", "target": "3.10.38", "url": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$LoginIscsiTargetRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$LogoutIscsiTargetCmd": {"hostname": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38", "target": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$LogoutIscsiTargetRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$MergeSnapshotCmd": {"destPath": "3.10.38", "fullRebase": "3.10.38", "kvmHostAddons": "3.10.38", "srcPath": "3.10.38", "taskContext": "4.4.52", "threadContext": "4.4.52", "threadContextStack": "4.4.52", "vmUuid": "3.10.38", "volume": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$MergeSnapshotRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$MigrateVmCmd": {"autoConverge": "3.10.38", "bandwidth": "4.7.11", "destHostIp": "3.10.38", "destHostManagementIp": "4.4.46", "disks": "4.4.34", "downTime": "4.6.21", "kvmHostAddons": "3.10.38", "migrateFromDestination": "3.10.38", "nics": "4.4.52", "reload": "4.6.0", "srcHostIp": "3.10.38", "storageMigrationPolicy": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "useNuma": "3.10.38", "vdpaPaths": "4.1.2", "vmUuid": "3.10.38", "xbzrle": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$MigrateVmResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$OvsAddPortCmd": {"kvmHostAddons": "5.3.20", "nicMap": "5.3.20", "reInstall": "5.3.20", "sync": "5.3.20", "vSwitchType": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$OvsAddPortRsp": {"error": "5.3.20", "success": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$OvsDelPortCmd": {"kvmHostAddons": "5.3.20", "nicMap": "5.3.20", "vswitchType": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$OvsDelPortRsp": {"error": "5.3.20", "success": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$PauseVmCmd": {"kvmHostAddons": "3.10.38", "timeout": "3.10.38", "uuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$PauseVmResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$PingCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "kvmagentPhysicalMemoryUsageAlarmThreshold": "4.8.0", "kvmagentPhysicalMemoryUsageHardLimit": "4.8.0"}, "org.zstack.kvm.KVMAgentCommands$PingResponse": {"error": "3.10.38", "hostUuid": "3.10.38", "sendCommandUrl": "3.10.38", "success": "3.10.38", "version": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$PrimaryStorageCommand": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$RebootHostCmd": {"kvmHostAddons": "4.7.0"}, "org.zstack.kvm.KVMAgentCommands$RebootHostResponse": {"error": "4.7.0", "success": "4.7.0"}, "org.zstack.kvm.KVMAgentCommands$RebootVmCmd": {"bootDev": "3.10.38", "kvmHostAddons": "3.10.38", "timeout": "3.10.38", "uuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$RebootVmResponse": {"error": "3.10.38", "success": "3.10.38", "virtualizerInfo": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$RefreshAllRulesOnHostCmd": {"ip6RuleTOs": "4.7.22", "kvmHostAddons": "3.10.38", "ruleTOs": "3.10.38", "vmNicTOs": "4.7.22"}, "org.zstack.kvm.KVMAgentCommands$RefreshAllRulesOnHostResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$RegisterPrimaryVmHeartbeatCmd": {"coloPrimary": "3.10.38", "heartbeatPort": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "redirectNum": "3.10.38", "targetHostIp": "3.10.38", "vmInstanceUuid": "3.10.38", "volumes": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ResumeVmCmd": {"kvmHostAddons": "3.10.38", "timeout": "3.10.38", "uuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ResumeVmResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ScanVmPortCmd": {"brname": "3.10.38", "ip": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ScanVmPortResponse": {"error": "3.10.38", "portStatus": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ShutdownHostCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$ShutdownHostResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$StartColoSyncCmd": {"blockReplicationPort": "3.10.38", "checkpointDelay": "3.10.38", "fullSync": "3.10.38", "kvmHostAddons": "3.10.38", "nbdServerPort": "3.10.38", "nics": "4.3.10", "secondaryVmHostIp": "3.10.38", "vmInstanceUuid": "3.10.38", "volumes": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$StartOvsServiceCmd": {"bondingMode": "5.3.20", "brExName": "5.3.20", "hostIp": "5.3.20", "hugePageNumber": "5.3.20", "kvmHostAddons": "5.3.20", "lacpMode": "5.3.20", "nicNameDriverMap": "5.3.20", "nicNamePciAddressMap": "5.3.20", "ovnEncapIP": "5.3.20", "ovnEncapNetmask": "5.3.20", "ovnEncapType": "5.3.20", "ovnRemoteConnection": "5.3.20", "restoreNicPciAddressList": "5.4.0", "vSwitchType": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$StartOvsServiceRsp": {"error": "5.3.20", "success": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$StartVmCmd": {"MemAccess": "4.1.2", "VDIMonitorNumber": "3.10.38", "accountUuid": "4.4.52", "acpi": "4.1.2", "additionalQmp": "3.10.38", "addons": "3.10.38", "bootDev": "3.10.38", "bootMenuSplashTimeout": "4.4.52", "bootMode": "3.10.38", "cacheVolumes": "3.10.38", "cdRoms": "3.10.38", "chassisAssetTag": "3.10.38", "clock": "3.10.38", "clockTrack": "3.10.38", "coloPrimary": "3.10.38", "coloSecondary": "3.10.38", "consoleLogToFile": "3.10.38", "consoleMode": "3.10.38", "consolePassword": "3.10.38", "cpuHypervisorFeature": "4.6.11", "cpuNum": "3.10.38", "cpuOnSocket": "3.10.38", "cpuSpeed": "3.10.38", "createPaused": "3.10.38", "dataVolumes": "3.10.38", "emulateHyperV": "3.10.38", "enableSecurityElement": "4.5.11", "fromForeignHypervisor": "3.10.38", "hostManagementIp": "3.10.38", "hypervClock": "4.4.52", "imageArchitecture": "4.0.0", "imagePlatform": "3.10.38", "instanceOfferingOnlineChange": "3.10.38", "isApplianceVm": "3.10.38", "kvmHiddenState": "3.10.38", "kvmHostAddons": "3.10.38", "machineType": "3.10.38", "maxMemory": "3.10.38", "maxVcpuNum": "4.4.52", "memBalloon": "4.4.24", "memory": "3.10.38", "memorySnapshotPath": "3.10.38", "nestedVirtualization": "3.10.38", "nics": "3.10.38", "noSharePages": "5.2.0", "oemStrings": "4.4.52", "pciePortNums": "3.10.38", "predefinedPciBridgeNum": "3.10.38", "priorityConfigStruct": "3.10.38", "qemu64BitPciMmioSetup": "5.2.0", "qxlMemory": "3.10.38", "reservedMemory": "4.7.22", "rootVolume": "3.10.38", "secureBoot": "4.4.24", "socketNum": "3.10.38", "soundType": "3.10.38", "spiceChannels": "3.10.38", "spiceStreamingMode": "3.10.38", "suspendToDisk": "4.4.52", "suspendToRam": "4.4.52", "systemSerialNumber": "3.10.38", "threadsPerCore": "4.4.52", "timeout": "3.10.38", "usbRedirect": "3.10.38", "useBootMenu": "3.10.38", "useColoBinary": "3.10.38", "useHugePage": "3.10.38", "useNuma": "3.10.38", "vendorId": "4.2.0", "videoType": "3.10.38", "vmCpuModel": "3.10.38", "vmCpuVendorId": "5.2.0", "vmInstanceUuid": "3.10.38", "vmInternalId": "3.10.38", "vmName": "3.10.38", "vmPortOff": "3.10.38", "x2apic": "4.4.58"}, "org.zstack.kvm.KVMAgentCommands$StartVmResponse": {"error": "3.10.38", "memBalloonInfo": "4.4.24", "nicInfos": "4.1.0", "success": "3.10.38", "virtualDeviceInfoList": "4.4.24", "virtualizerInfo": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$StopVmCmd": {"kvmHostAddons": "3.10.38", "timeout": "3.10.38", "type": "3.10.38", "uuid": "3.10.38", "vmNics": "4.4.52"}, "org.zstack.kvm.KVMAgentCommands$StopVmResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$SyncIpsetCmd": {"interfaceMap": "5.2.0", "kvmHostAddons": "5.2.0", "l2MacMap": "5.2.0", "nicList": "5.2.0", "vlanMap": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$SyncIpsetRsp": {"error": "5.2.0", "success": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$SyncVmDeviceInfoCmd": {"kvmHostAddons": "4.4.26", "vmInstanceUuid": "4.4.26"}, "org.zstack.kvm.KVMAgentCommands$SyncVmDeviceInfoResponse": {"error": "4.4.26", "memBalloonInfo": "4.4.26", "nicInfos": "4.4.26", "success": "4.4.26", "virtualDeviceInfoList": "4.4.26", "virtualizerInfo": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$TakeSnapshotCmd": {"fullSnapshot": "3.10.38", "installPath": "3.10.38", "isBaremetal2InstanceOnlineSnapshot": "4.0.0", "kvmHostAddons": "3.10.38", "newVolumeInstallPath": "3.10.38", "newVolumeUuid": "3.10.38", "online": "4.4.46", "primaryStorageUuid": "5.2.0", "taskContext": "4.4.52", "threadContext": "4.4.52", "threadContextStack": "4.4.52", "timeout": "4.3.12", "vmUuid": "3.10.38", "volume": "3.10.38", "volumeInstallPath": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$TakeSnapshotResponse": {"error": "3.10.38", "newVolumeInstallPath": "3.10.38", "size": "3.10.38", "snapshotInstallPath": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$TakeVmConsoleScreenshotCmd": {"kvmHostAddons": "4.8.0", "vmUuid": "4.8.0"}, "org.zstack.kvm.KVMAgentCommands$TakeVmConsoleScreenshotRsp": {"error": "4.8.0", "imageData": "4.8.0", "success": "4.8.0"}, "org.zstack.kvm.KVMAgentCommands$UnInstallOvsPackageCmd": {"kvmHostAddons": "5.3.20", "ovnControllerIp": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$UnInstallOvsPackageRsp": {"error": "5.3.20", "success": "5.3.20"}, "org.zstack.kvm.KVMAgentCommands$UpdateDependencyCmd": {"enableExpRepo": "4.1.3", "excludePackages": "4.1.6", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "updatePackages": "4.1.6", "zstackRepo": "4.2.25"}, "org.zstack.kvm.KVMAgentCommands$UpdateDependencyRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateGroupMemberCmd": {"kvmHostAddons": "3.10.38", "updateGroupTOs": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateGroupMemberResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateHostConfigurationCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "sendCommandUrl": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateHostConfigurationResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateHostOSCmd": {"enableExpRepo": "3.10.38", "excludePackages": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "releaseVersion": "3.10.38", "updatePackages": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateHostOSRsp": {"error": "3.10.38", "libvirtVersion": "4.4.64", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateL2NetworkCmd": {"bridgeName": "5.2.0", "kvmHostAddons": "5.2.0", "l2NetworkUuid": "5.2.0", "newVlan": "5.2.0", "oldVlan": "5.2.0", "peers": "5.2.0", "physicalInterfaceName": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$UpdateL2NetworkResponse": {"error": "5.2.0", "success": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$UpdateNicCmd": {"accountUuid": "4.4.52", "addons": "3.10.38", "kvmHostAddons": "3.10.38", "nics": "3.10.38", "vmInstanceUuid": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateNicRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateSpiceChannelConfigCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateSpiceChannelConfigResponse": {"error": "3.10.38", "restartLibvirt": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateVmPriorityCmd": {"kvmHostAddons": "3.10.38", "priorityConfigStructs": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$UpdateVmPriorityRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$VmCompareCpuFunctionCmd": {"cpuXml": "4.4.52", "kvmHostAddons": "4.4.52"}, "org.zstack.kvm.KVMAgentCommands$VmCompareCpuFunctionResponse": {"error": "4.4.52", "success": "4.4.52"}, "org.zstack.kvm.KVMAgentCommands$VmDevicesInfoResponse": {"error": "4.4.26", "memBalloonInfo": "4.4.26", "nicInfos": "4.4.26", "success": "4.4.26", "virtualDeviceInfoList": "4.4.26", "virtualizerInfo": "4.6.21"}, "org.zstack.kvm.KVMAgentCommands$VmFstrimCmd": {"kvmHostAddons": "4.7.22", "vmUuid": "4.7.22"}, "org.zstack.kvm.KVMAgentCommands$VmFstrimRsp": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.kvm.KVMAgentCommands$VmGetCpuXmlCmd": {"kvmHostAddons": "4.4.52"}, "org.zstack.kvm.KVMAgentCommands$VmGetCpuXmlResponse": {"cpuModelName": "4.4.52", "cpuXml": "4.4.52", "error": "4.4.52", "success": "4.4.52"}, "org.zstack.kvm.KVMAgentCommands$VmSyncCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$VmSyncResponse": {"error": "3.10.38", "states": "3.10.38", "success": "3.10.38", "vmInShutdowns": "3.10.38"}, "org.zstack.kvm.KVMAgentCommands$VolumeSyncCmd": {"kvmHostAddons": "5.2.0", "storagePaths": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$VolumeSyncRsp": {"error": "5.2.0", "inactiveVolumePaths": "5.2.0", "success": "5.2.0"}, "org.zstack.kvm.KVMAgentCommands$vdiCmd": {"VDIMonitorNumber": "3.10.38", "consoleMode": "3.10.38", "consolePassword": "3.10.38", "kvmHostAddons": "3.10.38", "qxlMemory": "3.10.38", "soundType": "3.10.38", "spiceChannels": "3.10.38", "spiceStreamingMode": "3.10.38", "videoType": "3.10.38"}, "org.zstack.macvlan.KvmAgentL2NetworkMacVlanCommands$CheckVlanBridgeCmd": {"bridgeName": "4.7.22", "kvmHostAddons": "4.7.22", "physicalInterfaceName": "4.7.22", "vlan": "4.7.22"}, "org.zstack.macvlan.KvmAgentL2NetworkMacVlanCommands$CheckVlanBridgeResponse": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.macvlan.KvmAgentL2NetworkMacVlanCommands$CreateVlanBridgeCmd": {"bridgeName": "4.7.22", "disableIptables": "4.7.22", "igmpVersion": null, "isolated": "5.2.0", "kvmHostAddons": "4.7.22", "l2NetworkUuid": "4.7.22", "mldVersion": null, "mtu": "4.7.22", "physicalInterfaceName": "4.7.22", "pvlan": "5.2.0", "vlan": "4.7.22"}, "org.zstack.macvlan.KvmAgentL2NetworkMacVlanCommands$CreateVlanBridgeResponse": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.macvlan.KvmAgentL2NetworkMacVlanCommands$DeleteVlanBridgeCmd": {"bridgeName": "4.7.22", "kvmHostAddons": "4.7.22", "l2NetworkUuid": "4.7.22", "physicalInterfaceName": "4.7.22", "vlan": "4.7.22"}, "org.zstack.macvlan.KvmAgentL2NetworkMacVlanCommands$DeleteVlanBridgeResponse": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.mttyDevice.KvmMttyDeviceBackend.MttyDeviceKvmBackend$GenerateSeVfioMdevDevicesCommand": {"kvmHostAddons": "4.5.11", "mdevUuids": "4.5.11", "mttyDeviceUuid": "4.5.11", "reSplite": "4.5.11"}, "org.zstack.mttyDevice.KvmMttyDeviceBackend.MttyDeviceKvmBackend$GenerateSeVfioMdevDevicesRsp": {"error": "4.5.11", "mdevUuids": "4.5.11", "success": "4.5.11"}, "org.zstack.mttyDevice.KvmMttyDeviceBackend.MttyDeviceKvmBackend$GetMttyDevicesCmd": {"kvmHostAddons": "4.5.11"}, "org.zstack.mttyDevice.KvmMttyDeviceBackend.MttyDeviceKvmBackend$GetMttyDevicesRsp": {"error": "4.5.11", "mttyDeviceInfo": "4.5.11", "success": "4.5.11"}, "org.zstack.mttyDevice.KvmMttyDeviceBackend.MttyDeviceKvmBackend$UngenerateSeVfioMdevDevicesCommand": {"kvmHostAddons": "4.5.11", "mttyDeviceUuid": "4.5.11"}, "org.zstack.mttyDevice.KvmMttyDeviceBackend.MttyDeviceKvmBackend$UngenerateSeVfioMdevDevicesRsp": {"error": "4.5.11", "success": "4.5.11"}, "org.zstack.multicast.router.backend.MulticastRouterVyosCommands$VyosPimdDisableRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.multicast.router.backend.MulticastRouterVyosCommands$VyosPimdEnableCmd": {"rps": "3.10.38"}, "org.zstack.multicast.router.backend.MulticastRouterVyosCommands$VyosPimdEnableRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.multicast.router.backend.MulticastRouterVyosCommands$VyosPimdRouteRsp": {"error": "3.10.38", "routes": "3.10.38", "success": "3.10.38"}, "org.zstack.network.plugin.FlatGratuitousARPBackend$ApplyGratuitousARPCmd": {"infos": "4.2.25", "interval": "4.2.25", "kvmHostAddons": "4.2.25", "rebuild": "4.2.25", "state": "4.2.25"}, "org.zstack.network.plugin.FlatGratuitousARPBackend$ApplyGratuitousARPRsp": {"error": "4.2.25", "success": "4.2.25"}, "org.zstack.network.plugin.FlatGratuitousARPBackend$CleanupGratuitousARPCmd": {"infos": "4.2.25", "kvmHostAddons": "4.2.25"}, "org.zstack.network.plugin.FlatGratuitousARPBackend$CleanupGratuitousARPRsp": {"error": "4.2.25", "success": "4.2.25"}, "org.zstack.network.plugin.FlatGratuitousARPBackend$ReleaseGratuitousARPCmd": {"infos": "4.2.25", "kvmHostAddons": "4.2.25"}, "org.zstack.network.plugin.FlatGratuitousARPBackend$ReleaseGratuitousARPRsp": {"error": "4.2.25", "success": "4.2.25"}, "org.zstack.network.service.flat.FlatDhcpBackend$ApplyDhcpCmd": {"dhcp": "3.10.38", "kvmHostAddons": "3.10.38", "l3NetworkUuid": "3.10.38", "rebuild": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$ApplyDhcpRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$ArpingCmd": {"bridgeName": "5.2.0", "kvmHostAddons": "5.2.0", "namespaceName": "5.2.0", "targetIps": "5.2.0", "vlanId": "5.2.0"}, "org.zstack.network.service.flat.FlatDhcpBackend$ArpingRsp": {"error": "5.2.0", "result": "5.2.0", "success": "5.2.0"}, "org.zstack.network.service.flat.FlatDhcpBackend$BatchApplyDhcpCmd": {"dhcpInfos": "4.0.0", "kvmHostAddons": "4.0.0"}, "org.zstack.network.service.flat.FlatDhcpBackend$BatchPrepareDhcpCmd": {"dhcpInfos": "4.0.0", "kvmHostAddons": "4.0.0"}, "org.zstack.network.service.flat.FlatDhcpBackend$ConnectCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$ConnectRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$DeleteNamespaceCmd": {"bridgeName": "3.10.38", "kvmHostAddons": "3.10.38", "namespaceName": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$DeleteNamespaceRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$FlushDhcpNamespaceCmd": {"bridgeName": "5.2.0", "kvmHostAddons": "5.2.0", "namespaceName": "5.2.0"}, "org.zstack.network.service.flat.FlatDhcpBackend$FlushDhcpNamespaceRsp": {"error": "5.2.0", "success": "5.2.0"}, "org.zstack.network.service.flat.FlatDhcpBackend$PrepareDhcpCmd": {"addressMode": "3.10.38", "bridgeName": "3.10.38", "dhcp6ServerIp": "3.10.38", "dhcpNetmask": "3.10.38", "dhcpServerIp": "3.10.38", "ipVersion": "3.10.38", "kvmHostAddons": "3.10.38", "namespaceName": "3.10.38", "prefixLen": "3.10.38", "vlanId": "5.2.0"}, "org.zstack.network.service.flat.FlatDhcpBackend$PrepareDhcpRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$ReleaseDhcpCmd": {"dhcp": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$ReleaseDhcpRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$ResetDefaultGatewayCmd": {"bridgeNameOfGatewayToAdd": "3.10.38", "bridgeNameOfGatewayToRemove": "3.10.38", "gatewayToAdd": "3.10.38", "gatewayToRemove": "3.10.38", "kvmHostAddons": "3.10.38", "macOfGatewayToAdd": "3.10.38", "macOfGatewayToRemove": "3.10.38", "namespaceNameOfGatewayToAdd": "3.10.38", "namespaceNameOfGatewayToRemove": "3.10.38"}, "org.zstack.network.service.flat.FlatDhcpBackend$ResetDefaultGatewayRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatUserdataBackend$ApplyUserdataCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "userdata": "3.10.38"}, "org.zstack.network.service.flat.FlatUserdataBackend$ApplyUserdataRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatUserdataBackend$BatchApplyUserdataCmd": {"kvmHostAddons": "3.10.38", "rebuild": "3.10.38", "userdata": "3.10.38"}, "org.zstack.network.service.flat.FlatUserdataBackend$CleanupUserdataCmd": {"bridgeName": "3.10.38", "kvmHostAddons": "3.10.38", "l3NetworkUuid": "3.10.38", "namespaceName": "3.10.38"}, "org.zstack.network.service.flat.FlatUserdataBackend$CleanupUserdataRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.flat.FlatUserdataBackend$ReleaseUserdataCmd": {"bridgeName": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "namespaceName": "3.10.38", "vmIp": "3.10.38"}, "org.zstack.network.service.flat.FlatUserdataBackend$ReleaseUserdataRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.vipQos.vyos.VyosVipQosBackend$DeleteVipAllQosCmd": {"vipQosSettings": "3.10.38"}, "org.zstack.network.service.vipQos.vyos.VyosVipQosBackend$DeleteVipAllQosRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.vipQos.vyos.VyosVipQosBackend$DeleteVipQosCmd": {"vipQosSettings": "3.10.38"}, "org.zstack.network.service.vipQos.vyos.VyosVipQosBackend$DeleteVipQosRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.vipQos.vyos.VyosVipQosBackend$SetVipQosCmd": {"vipQosSettings": "3.10.38"}, "org.zstack.network.service.vipQos.vyos.VyosVipQosBackend$SetVipQosRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$AddDhcpEntryCmd": {"dhcpEntries": "3.10.38", "rebuild": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$AddDhcpEntryRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ChangeDefaultNicCmd": {"newNic": "3.10.38", "snats": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ChangeDefaultNicRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ConfigPromtailCmd": {"enable": "5.3.28", "logTarget": "5.3.28"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ConfigureNicCmd": {"nics": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ConfigureNicFirewallDefaultActionCmd": {"nics": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ConfigureNicFirewallDefaultActionRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ConfigureNicRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ConfigureNtpCmd": {"timeServers": "4.0.0"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$ConfigureNtpRsp": {"error": "4.0.0", "success": "4.0.0"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$CreateEipCmd": {"eip": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$CreateEipRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$CreatePortForwardingRuleCmd": {"rules": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$CreatePortForwardingRuleRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$CreateVipCmd": {"nicIps": "3.10.38", "resetQosRules": "5.3.0", "syncVip": "3.10.38", "vips": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$CreateVipRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$GetTypeCommand": {"uuid": "4.7.0"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$GetTypeRsp": {"error": "4.7.0", "isVyos": "4.7.0", "success": "4.7.0"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$InitCommand": {"logLevel": "3.10.38", "mgtCidr": "3.10.38", "parms": "3.10.38", "restartDnsmasqAfterNumberOfSIGUSER1": "3.10.38", "timeServers": "4.0.0", "uuid": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$InitRsp": {"error": "3.10.38", "ipsecCurrentVersion": "4.4.24", "ipsecLatestVersion": "4.4.24", "kernelVersion": "3.10.38", "success": "3.10.38", "vyosVersion": "3.10.38", "zvrVersion": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$PingCmd": {"uuid": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$PingRsp": {"error": "3.10.38", "haStatus": "3.10.38", "healthDetail": "3.10.38", "healthy": "3.10.38", "serviceHealthList": "4.5.0", "success": "3.10.38", "uuid": "3.10.38", "version": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RefreshDHCPServerCmd": {"dhcpServers": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RefreshDHCPServerRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveDhcpEntryCmd": {"dhcpEntries": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveDhcpEntryRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveDnsCmd": {"dns": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveDnsRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveEipCmd": {"eip": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveEipRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveForwardDnsCmd": {"bridgeName": "3.10.38", "mac": "3.10.38", "nameSpace": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveForwardDnsRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveNicCmd": {"nics": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveNicRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveSNATCmd": {"natInfo": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveSNATRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveVipCmd": {"vips": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RemoveVipRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RevokePortForwardingRuleCmd": {"rules": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$RevokePortForwardingRuleRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SetDnsCmd": {"dns": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SetDnsRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SetForwardDnsCmd": {"bridgeName": "3.10.38", "dns": "3.10.38", "mac": "3.10.38", "nameSpace": "3.10.38", "wrongDns": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SetForwardDnsRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SetSNATCmd": {"snat": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SetSNATRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SyncEipCmd": {"eips": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SyncEipRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SyncPortForwardingRuleCmd": {"rules": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SyncPortForwardingRuleRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SyncSNATCmd": {"enable": "3.10.38", "snats": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterCommands$SyncSNATRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterKvmBackendCommands$CreateVritualRouterBootstrapIsoCmd": {"isoInfo": "3.10.38", "isoPath": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterKvmBackendCommands$CreateVritualRouterBootstrapIsoRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterKvmBackendCommands$DeleteVirtualRouterBootstrapIsoCmd": {"isoPath": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.network.service.virtualrouter.VirtualRouterKvmBackendCommands$DeleteVirtualRouterBootstrapIsoRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$CertificateCmd": {"certificate": "3.10.38", "uuid": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$CertificateRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$CertificatesCmd": {"certs": "5.2.0"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$DeleteLbCmd": {"lbs": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$DeleteLbRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$RefreshLbCmd": {"enableHaproxyLog": "3.10.38", "lbs": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$RefreshLbLogLevelCmd": {"level": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$RefreshLbLogLevelRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerBackend$RefreshLbRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.network.service.virtualrouter.vyos.VyosKeepalivedCommands$RestartKeepalivedRsp": {"error": "5.2.0", "success": "5.2.0"}, "org.zstack.network.service.virtualrouter.vyos.VyosKeepalivedCommands$SyncVpcRouterHaRsp": {"error": "5.2.0", "haStatus": "5.2.0", "success": "5.2.0"}, "org.zstack.network.service.virtualrouter.vyos.VyosKeepalivedCommands$VyosHaEnableCmd": {"callbackUrl": "5.2.0", "heartbeatNic": "5.2.0", "keepalive": "5.2.0", "localIp": "5.2.0", "localIpV6": "5.2.0", "monitors": "5.2.0", "peerIp": "5.2.0", "peerIpV6": "5.2.0", "vips": "5.2.0"}, "org.zstack.network.service.virtualrouter.vyos.VyosKeepalivedCommands$VyosHaEnableRsp": {"error": "5.2.0", "success": "5.2.0"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$AttachPciDeviceToHostCommand": {"kvmHostAddons": "3.10.38", "pciDeviceAddress": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$AttachPciDeviceToHostRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$CreatePciDeviceRomFileCommand": {"kvmHostAddons": "3.10.38", "romContent": "3.10.38", "romMd5sum": "3.10.38", "specUuid": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$CreatePciDeviceRomFileRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$DetachPciDeviceFromHostCommand": {"kvmHostAddons": "3.10.38", "pciDeviceAddress": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$DetachPciDeviceFromHostRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$GenerateSriovPciDevicesCommand": {"interfaceName": "5.4.0", "kvmHostAddons": "3.10.38", "pciDeviceAddress": "3.10.38", "pciDeviceType": "3.10.38", "reSplite": "3.10.38", "virtPartNum": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$GenerateSriovPciDevicesRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$GenerateVfioMdevDevicesCommand": {"kvmHostAddons": "3.10.38", "mdevSpecTypeId": "3.10.38", "mdevUuids": "3.10.38", "pciDeviceAddress": "3.10.38", "vendor": "5.2.0"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$GenerateVfioMdevDevicesRsp": {"error": "3.10.38", "mdevUuids": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$GetPciDevicesCmd": {"enableIommu": "3.10.38", "filterString": "3.10.38", "kvmHostAddons": "3.10.38", "skipGrubConfig": "4.0.0"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$GetPciDevicesRsp": {"error": "3.10.38", "hostIommuStatus": "3.10.38", "pciDevicesInfo": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$HotPlugPciDeviceCommand": {"kvmHostAddons": "3.10.38", "pciDeviceAddress": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$HotPlugPciDeviceRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$HotUnplugPciDeviceCommand": {"kvmHostAddons": "3.10.38", "pciDeviceAddress": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$HotUnplugPciDeviceRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$ReloadGpuDriverOnHostCommand": {"kvmHostAddons": "5.3.28", "pciDeviceAddress": "5.3.28", "vendor": "5.3.28"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$ReloadGpuDriverOnHostRsp": {"error": "5.3.28", "success": "5.3.28"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$UngenerateSriovPciDevicesCommand": {"kvmHostAddons": "3.10.38", "pciDeviceAddress": "3.10.38", "pciDeviceType": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$UngenerateSriovPciDevicesRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$UngenerateVfioMdevDevicesCommand": {"kvmHostAddons": "3.10.38", "mdevSpecTypeId": "3.10.38", "pciDeviceAddress": "3.10.38", "vendor": "5.2.0"}, "org.zstack.pciDevice.KvmPciDeviceBackend.PciDeviceKvmBackend$UngenerateVfioMdevDevicesRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceBase$DeleteVfioMdevDeviceCommand": {"MdevDeviceUuid": "4.5.11", "kvmHostAddons": "4.5.11"}, "org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceBase$DeleteVfioMdevDeviceRsp": {"error": "4.5.11", "success": "4.5.11"}, "org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceBase$HotPlugMdevDeviceCommand": {"MdevDeviceUuid": "4.5.11", "kvmHostAddons": "4.5.11", "vmUuid": "4.5.11"}, "org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceBase$HotPlugMdevDeviceRsp": {"error": "4.5.11", "success": "4.5.11"}, "org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceBase$HotUnplugMdevDeviceCommand": {"MdevDeviceUuid": "4.5.11", "kvmHostAddons": "4.5.11", "vmUuid": "4.5.11"}, "org.zstack.pciDevice.virtual.vfio_mdev.MdevDeviceBase$HotUnplugMdevDeviceRsp": {"error": "4.5.11", "success": "4.5.11"}, "org.zstack.policyRoute.PolicyRouteCommands$ApplyPolicyRouteCmd": {"error": "3.10.38", "markConntrack": "4.1.2", "refs": "3.10.38", "routes": "3.10.38", "ruleSets": "3.10.38", "rules": "3.10.38", "success": "3.10.38", "tableNumbers": "3.10.38"}, "org.zstack.policyRoute.PolicyRouteCommands$ApplyPolicyRouteRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.portMirror.PortMirrorKvmBackend$PortMirrorApplyCmd": {"isLocal": "3.10.38", "kvmHostAddons": "3.10.38", "mirror": "3.10.38", "tunnel": "3.10.38"}, "org.zstack.portMirror.PortMirrorKvmBackend$PortMirrorApplyRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.portMirror.PortMirrorKvmBackend$PortMirrorReleaseCmd": {"isLocal": "3.10.38", "kvmHostAddons": "3.10.38", "mirror": "3.10.38", "tunnel": "3.10.38"}, "org.zstack.portMirror.PortMirrorKvmBackend$PortMirrorReleaseRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.routeProtocol.RouteProtocolOspfBackend$GetOspfNeighborRsp": {"error": "3.10.38", "neighbors": "3.10.38", "success": "3.10.38"}, "org.zstack.routeProtocol.RouteProtocolOspfBackend$SetOspfCmd": {"areaInfos": "3.10.38", "networkInfos": "3.10.38", "routerId": "3.10.38"}, "org.zstack.routeProtocol.RouteProtocolOspfBackend$SetOspfRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.sshkeypair.SshKeyPairBase$AttachSshKeyPairToVmInstanceCommand": {"kvmHostAddons": "4.7.22", "publicKey": "4.7.22", "sshKeyPairUuid": "4.7.22", "vmInstanceUuid": "4.7.22"}, "org.zstack.sshkeypair.SshKeyPairBase$AttachSshKeyPairToVmInstanceRsp": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.sshkeypair.SshKeyPairBase$DetachSshKeyPairFromVmInstanceCommand": {"kvmHostAddons": "4.7.22", "publicKey": "4.7.22", "sshKeyPairUuid": "4.7.22", "vmInstanceUuid": "4.7.22"}, "org.zstack.sshkeypair.SshKeyPairBase$DetachSshKeyPairFromVmInstanceRsp": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$CancelBackupJobCmd": {"force": "4.8.10", "kvmHostAddons": "4.7.11", "taskContext": "4.7.11", "threadContext": "4.7.11", "threadContextStack": "4.7.11", "vmUuid": "4.7.11", "volume": "4.7.11"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$CancelBackupJobResponse": {"error": "4.7.11", "success": "4.7.11"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$CancelBackupJobsCmd": {"force": "4.8.10", "kvmHostAddons": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$CancelBackupJobsResponse": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$TakeBackupCmd": {"backupPath": "4.4.30", "bitmap": "3.10.38", "bsPath": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "lastBackup": "3.10.38", "maxIncremental": "3.10.38", "mode": "3.10.38", "networkWriteBandwidth": "3.10.38", "outOfBand": "5.2.0", "password": "3.10.38", "pointInTime": "4.4.43", "sshPort": "3.10.38", "storageInfo": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uploadConcurrency": "4.4.46", "uploadDir": "3.10.38", "username": "3.10.38", "vmUuid": "3.10.38", "volume": "3.10.38", "volumeWriteBandwidth": "3.10.38"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$TakeBackupResponse": {"backupFile": "3.10.38", "bitmap": "3.10.38", "error": "3.10.38", "parentInstallPath": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$TakeBackupsCmd": {"backupInfos": "3.10.38", "backupPaths": "4.4.30", "bsPath": "3.10.38", "deviceIds": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "maxIncremental": "3.10.38", "mode": "3.10.38", "networkWriteBandwidth": "3.10.38", "outOfBand": "5.2.0", "password": "3.10.38", "pointInTime": "4.4.43", "sshPort": "3.10.38", "storageInfo": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uploadConcurrency": "4.4.46", "uploadDir": "3.10.38", "username": "3.10.38", "vmUuid": "3.10.38", "volumeWriteBandwidth": "3.10.38", "volumes": "3.10.38"}, "org.zstack.storage.backup.VolumeBackupKvmCommands$TakeBackupsResponse": {"backupInfos": "3.10.38", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.ceph.primary.CephPrimaryStorageBase$CreateKvmSecretCmd": {"kvmHostAddons": "3.10.38", "userKey": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$AttachScsiLunToVmCmd": {"kvmHostAddons": "3.10.38", "multipath": "3.10.38", "vmInstanceUuid": "3.10.38", "volume": "3.10.38", "wwid": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$AttachScsiLunToVmRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$DetachScsiLunFromHostCmd": {"kvmHostAddons": "3.10.38", "wwid": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$DetachScsiLunFromVmCmd": {"kvmHostAddons": "3.10.38", "vmInstanceUuid": "3.10.38", "volume": "3.10.38", "wwid": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$DetachScsiLunFromVmRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$FcScanCmd": {"identifiers": "3.10.38", "kvmHostAddons": "3.10.38", "rescan": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$FcScanRsp": {"error": "3.10.38", "fiberChannelLunStructs": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$GetMultipathTopologyCmd": {"kvmHostAddons": "3.10.38", "wwids": "5.3.0"}, "org.zstack.storage.device.StorageDeviceKvmCommands$GetMultipathTopologyRsp": {"devices": "5.3.0", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$HbaScanCmd": {"kvmHostAddons": null}, "org.zstack.storage.device.StorageDeviceKvmCommands$HbaScanRsp": {"error": null, "hbaDeviceStructs": null, "success": null}, "org.zstack.storage.device.StorageDeviceKvmCommands$IscsiLoginCmd": {"iscsiChapUserName": "3.10.38", "iscsiChapUserPassword": "3.10.38", "iscsiServerIp": "3.10.38", "iscsiServerPort": "3.10.38", "iscsiTargets": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$IscsiLoginRsp": {"error": "3.10.38", "iscsiTargetStructList": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$IscsiLogoutCmd": {"iscsiChapUserName": "3.10.38", "iscsiChapUserPassword": "3.10.38", "iscsiServerIp": "3.10.38", "iscsiServerPort": "3.10.38", "iscsiTargets": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$IscsiLogoutRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$MultipathEnableCmd": {"blacklist": "4.4.52", "kvmHostAddons": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$MultipathEnableRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$NvmeScanCmd": {"identifiers": "4.6.21", "kvmHostAddons": "4.6.21", "rescan": "4.6.21"}, "org.zstack.storage.device.StorageDeviceKvmCommands$NvmeScanRsp": {"error": "4.6.21", "nvmeLunStructs": "4.6.21", "success": "4.6.21"}, "org.zstack.storage.device.StorageDeviceKvmCommands$NvmeServerConnectCmd": {"ip": "4.7.22", "kvmHostAddons": "4.7.22", "port": "4.7.22", "transport": "4.7.22"}, "org.zstack.storage.device.StorageDeviceKvmCommands$NvmeServerConnectRsp": {"error": "4.7.22", "nvmeLunStructs": "4.7.22", "success": "4.7.22"}, "org.zstack.storage.device.StorageDeviceKvmCommands$NvmeServerDisconnectCmd": {"ip": "4.7.22", "kvmHostAddons": "4.7.22", "port": "4.7.22", "transport": "4.7.22"}, "org.zstack.storage.device.StorageDeviceKvmCommands$NvmeServerDisconnectRsp": {"error": "4.7.22", "success": "4.7.22"}, "org.zstack.storage.device.StorageDeviceKvmCommands$RaidPhysicalDriveLocateCmd": {"busNumber": "3.10.38", "deviceNumber": "3.10.38", "enclosureDeviceID": "3.10.38", "kvmHostAddons": "3.10.38", "locate": "3.10.38", "raidControllerNumber": "3.10.38", "slotNumber": "3.10.38", "wwn": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$RaidPhysicalDriveSmartCmd": {"busNumber": "3.10.38", "deviceNumber": "3.10.38", "kvmHostAddons": "3.10.38", "slotNumber": "3.10.38", "wwn": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$RaidPhysicalDriveSmartRsp": {"error": "3.10.38", "smartDataStructs": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$RaidPhysicalDriveSmartTestCmd": {"busNumber": "3.10.38", "deviceNumber": "3.10.38", "kvmHostAddons": "3.10.38", "slotNumber": "3.10.38", "wwn": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$RaidPhysicalDriveSmartTestRsp": {"error": "3.10.38", "result": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$RaidScanCmd": {"kvmHostAddons": "3.10.38"}, "org.zstack.storage.device.StorageDeviceKvmCommands$RaidScanRsp": {"error": "3.10.38", "raidPhysicalDriveStructs": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.primary.block.ImageStoreBackupStorageBlockKvmDownloader$DownloadBitsFromBackupStorageResponse": {"error": "4.5.11", "success": "4.5.11", "this$0": "4.5.11"}, "org.zstack.storage.primary.cbd.CbdHostHeartbeatChecker$CheckHostHeartbeatCmd": {"heartbeatUrl": "5.3.20", "hostId": "5.3.20", "hostUuid": "5.3.20", "interval": "5.3.20", "kvmHostAddons": "5.3.20", "primaryStorageUuid": "5.3.20", "storageCheckerTimeout": "5.3.20", "times": "5.3.20"}, "org.zstack.storage.primary.cbd.CbdHostHeartbeatChecker$CheckHostHeartbeatRsp": {"error": "5.3.20", "result": "5.3.20", "success": "5.3.20", "vmUuids": "5.3.20"}, "org.zstack.storage.primary.ceph.CephHostHeartbeatChecker$CheckHostHeartbeatCmd": {"interval": "4.2.0", "kvmHostAddons": "4.2.0", "manufacturer": "4.2.0", "poolNames": "4.2.0", "primaryStorageUuid": "4.2.0", "storageCheckerTimeout": "4.2.0", "targetHostUuid": "4.2.0", "times": "4.2.0"}, "org.zstack.storage.primary.ceph.CephHostHeartbeatChecker$CheckHostHeartbeatRsp": {"error": "4.2.0", "result": "4.2.0", "success": "4.2.0", "vmUuids": "4.7.0"}, "org.zstack.storage.primary.filesystem.AbstractFileSystemHostHeartbeatChecker$CheckFileSystemVmStateCmd": {"interval": "4.7.0", "kvmHostAddons": "4.7.0", "mountPath": "4.7.0", "primaryStorageUuid": "4.7.0", "storageCheckerTimeout": "4.7.0", "targetHostUuid": "4.7.0", "times": "4.7.0"}, "org.zstack.storage.primary.filesystem.AbstractFileSystemHostHeartbeatChecker$CheckFileSystemVmStateRsp": {"error": "4.7.0", "result": "4.7.0", "success": "4.7.0", "vmUuids": "4.7.0"}, "org.zstack.storage.primary.imagestore.local.LocalStorageImageStoreKvmBackend$CleanImageMetaCmd": {"kvmHostAddons": "4.8.0", "primaryStorageInstallPath": "4.8.0", "primaryStorageUuid": "4.8.0", "storagePath": "4.8.0", "uuid": "4.8.0"}, "org.zstack.storage.primary.imagestore.local.LocalStorageImageStoreKvmBackend$CommitVolumeAsImageCmd": {"description": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.local.LocalStorageImageStoreKvmBackend$ResizeVolumeCmd": {"force": "4.4.24", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "size": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.local.LocalStorageKvmImageStoreBackupStorageMediatorImpl$ImageStoreCmd": {"backupStorageInstallPath": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "privateKey": "3.10.38", "servicePort": "3.10.38", "storagePath": "3.10.38", "trustedHosts": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.local.LocalStorageKvmImageStoreBackupStorageMediatorImpl$ImageStoreDownloadBitsCmd": {"backupStorageInstallPath": "3.10.38", "concurrency": "4.4.46", "hostname": "3.10.38", "isData": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "privateKey": "3.10.38", "servicePort": "3.10.38", "storagePath": "3.10.38", "trustedHosts": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.local.LocalStorageKvmImageStoreBackupStorageMediatorImpl$ImageStoreUploadBitsCmd": {"backupStorageInstallPath": "3.10.38", "concurrency": "4.4.46", "description": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "privateKey": "3.10.38", "servicePort": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "trustedHosts": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryStorageImageStoreBackend$CleanImageMetaCmd": {"kvmHostAddons": "4.8.0", "primaryStorageInstallPath": "4.8.0", "primaryStorageUuid": "4.8.0", "uuid": "4.8.0"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryStorageImageStoreBackend$ResizeVolumeCmd": {"force": "4.4.24", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "size": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryStorageImageStoreBackend$ResizeVolumeRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "size": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryToImageStoreBackupKVMBackend$DownloadFromImageStoreCmd": {"backupStorageInstallPath": "3.10.38", "concurrency": "4.4.46", "hostname": "3.10.38", "isData": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryToImageStoreBackupKVMBackend$DownloadFromImageStoreRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryToImageStoreBackupKVMBackend$ImageStoreCmd": {"backupStorageInstallPath": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryToImageStoreBackupKVMBackend$ImageStoreUploadBitsCmd": {"backupStorageInstallPath": "3.10.38", "concurrency": "4.4.46", "description": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.imagestore.nfs.NfsPrimaryToImageStoreBackupKVMBackend$ImageStoreUploadBitsRsp": {"availableCapacity": "3.10.38", "backupStorageInstallPath": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.imagestore.smp.ImageStoreBackupStorageKvmDownloader$DownloadFromImageStoreCmd": {"backupStorageInstallPath": "3.10.38", "concurrency": "4.4.46", "hostname": "3.10.38", "isData": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "username": "3.10.38"}, "org.zstack.storage.primary.imagestore.smp.ImageStoreBackupStorageKvmUploader$UploadToImageStoreCmd": {"concurrency": "4.4.46", "description": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38"}, "org.zstack.storage.primary.imagestore.smp.SMPImageStoreKvmBackend$CleanImageMetaCmd": {"kvmHostAddons": "4.8.0", "mountPoint": "4.8.0", "primaryStorageInstallPath": "4.8.0", "primaryStorageUuid": "4.8.0"}, "org.zstack.storage.primary.imagestore.smp.SMPImageStoreKvmBackend$CommitVolumeAsImageCmd": {"description": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.storage.primary.iscsi.IscsiHostHeartbeatChecker$CheckHostHeartbeatCmd": {"heartbeatUrl": "5.2.0", "hostId": "5.2.0", "hostUuid": "5.2.0", "interval": "5.2.0", "kvmHostAddons": "5.2.0", "primaryStorageUuid": "5.2.0", "storageCheckerTimeout": "5.2.0", "times": "5.2.0"}, "org.zstack.storage.primary.iscsi.IscsiHostHeartbeatChecker$CheckHostHeartbeatRsp": {"error": "5.2.0", "result": "5.2.0", "success": "5.2.0", "vmUuids": "5.2.0"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$AgentCommand": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CancelDownloadBitsFromKVMHostCmd": {"kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CheckBitsCmd": {"kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CheckInitializedFileCmd": {"filePath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CheckMd5sumCmd": {"kvmHostAddons": "3.10.38", "md5s": "3.10.38", "primaryStorageUuid": "3.10.38", "sendCommandUrl": "3.10.38", "stage": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CreateEmptyVolumeCmd": {"accountUuid": "3.10.38", "backingFile": "3.10.38", "installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "size": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CreateFolderCmd": {"installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CreateInitializedFileCmd": {"filePath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CreateTemplateFromVolumeCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38", "volumePath": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CreateVolumeFromCacheCmd": {"installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "templatePathInCache": "3.10.38", "uuid": "3.10.38", "virtualSize": "4.4.24", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$CreateVolumeWithBackingCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "templatePathInCache": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$DeleteBitsCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$DownloadBitsFromKVMHostCmd": {"backupStorageInstallPath": "3.10.38", "bandWidth": "3.10.38", "hostname": "3.10.38", "identificationCode": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "storagePath": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$EstimateTemplateSizeCmd": {"kvmHostAddons": "4.7.0", "primaryStorageUuid": "4.7.0", "storagePath": "4.7.0", "uuid": "4.7.0", "volumePath": "4.7.0"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetBackingChainCmd": {"installPath": "4.7.0", "kvmHostAddons": "4.7.0", "primaryStorageUuid": "4.7.0", "storagePath": "4.7.0", "uuid": "4.7.0", "volumeUuid": "4.7.0"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetBackingFileCmd": {"kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetBatchVolumeSizeCmd": {"kvmHostAddons": "4.5.11", "primaryStorageUuid": "4.5.11", "storagePath": "4.5.11", "uuid": "4.5.11", "volumeUuidInstallPaths": "4.5.11"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetDownloadBitsFromKVMHostProgressCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumePaths": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetMd5Cmd": {"kvmHostAddons": "3.10.38", "md5s": "3.10.38", "primaryStorageUuid": "3.10.38", "sendCommandUrl": "3.10.38", "stage": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetPhysicalCapacityCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetQCOW2ReferenceCmd": {"kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "searchingDir": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetQcow2HashValueCmd": {"hostUuid": "4.3.18", "installPath": "4.3.18", "kvmHostAddons": "4.3.18", "primaryStorageUuid": "4.3.18", "storagePath": "4.3.18", "uuid": "4.3.18"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetVolumeBaseImagePathCmd": {"imageCacheDir": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumeInstallDir": "3.10.38", "volumeInstallPath": "4.8.10", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$GetVolumeSizeCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$InitCmd": {"hostUuid": "3.10.38", "initFilePath": "3.10.38", "kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$LinkVolumeNewDirCmd": {"dstDir": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "srcDir": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$ListPathCmd": {"hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$MergeSnapshotCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPath": "3.10.38", "storagePath": "3.10.38", "taskContext": "4.4.0", "threadContext": "4.4.0", "threadContextStack": "4.4.0", "uuid": "3.10.38", "volumeUuid": "3.10.38", "workspaceInstallPath": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$OfflineMergeSnapshotCmd": {"destPath": "3.10.38", "fullRebase": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "srcPath": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$Qcow2Cmd": {"kvmHostAddons": "3.10.38", "preallocation": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$RebaseAndMergeSnapshotsCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPaths": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38", "workspaceInstallPath": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$ReinitImageCmd": {"imagePath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38", "volumePath": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$RevertVolumeFromSnapshotCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPath": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmBackend$UnlinkBitsCmd": {"installPath": "4.4.46", "kvmHostAddons": "4.4.46", "onlyLinkedFile": "4.4.46", "primaryStorageUuid": "4.4.46", "storagePath": "4.4.46", "uuid": "4.4.46"}, "org.zstack.storage.primary.local.LocalStorageKvmMigrateVmFlow$CopyBitsFromRemoteCmd": {"dstIp": "3.10.38", "dstPassword": "3.10.38", "dstPort": "3.10.38", "dstUsername": "3.10.38", "kvmHostAddons": "3.10.38", "paths": "3.10.38", "primaryStorageUuid": "3.10.38", "sendCommandUrl": "3.10.38", "stage": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmMigrateVmFlow$RebaseSnapshotBackingFilesCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshots": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmMigrateVmFlow$VerifySnapshotChainCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshots": "3.10.38", "storagePath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmSftpBackupStorageMediatorImpl$SftpDownloadBitsCmd": {"backupStorageInstallPath": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.local.LocalStorageKvmSftpBackupStorageMediatorImpl$SftpUploadBitsCmd": {"backupStorageInstallPath": "3.10.38", "hostname": "3.10.38", "imageName": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "storagePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.ImageStoreBackupStorageKvmDownloader$DownloadFromImageStoreCmd": {"addons": "3.10.38", "backupStorageInstallPath": "3.10.38", "concurrency": "4.4.46", "hostUuid": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "size": "3.10.38", "username": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.ImageStoreBackupStorageKvmUploader$UploadToImageStoreCmd": {"addons": "3.10.38", "concurrency": "4.4.46", "description": "3.10.38", "hostUuid": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageImageStoreKvmBackend$CommitVolumeAsImageCmd": {"addons": "3.10.38", "description": "3.10.38", "hostUuid": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$ActiveVolumeCmd": {"addons": "3.10.38", "checkPeer": "3.10.38", "force": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "primaryStorageUuid": "3.10.38", "role": "3.10.38", "single": "4.3.10", "vgUuid": "3.10.38", "vmNics": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$AgentCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$CheckBitsCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "path": "3.10.38", "peerIps": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$CheckDisksCmd": {"addons": "3.10.38", "diskIdentifiers": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "rescan": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$ConnectCmd": {"addons": "3.10.38", "diskIdentifiers": "3.10.38", "fencerAddress": "3.10.38", "forceWipe": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "magementAddress": "3.10.38", "peerManagementAddress": "3.10.38", "peerSshPassword": "3.10.38", "peerSshUsername": "3.10.38", "primaryStorageUuid": "3.10.38", "storageNetworkCidr": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$ConvertImageToVolumeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "skipReplication": "4.3.10", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$CreateEmptyCacheVolumeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "size": "3.10.38", "vgUuid": "3.10.38", "volumeFormat": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$CreateEmptyVolumeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "size": "3.10.38", "skipReplication": "4.3.10", "vgUuid": "3.10.38", "volumeFormat": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$CreateSecondaryVolumeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "size": "3.10.38", "skipReplication": "4.3.10", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$CreateTemplateFromVolumeCmd": {"addons": "3.10.38", "compareQcow2": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "sharedVolume": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "vgUuid": "3.10.38", "volumePath": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$CreateVolumeFromCacheCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "skipReplication": "4.3.10", "templatePathInCache": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$DeleteBitsCmd": {"addons": "3.10.38", "folder": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$DeleteTag": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "tag": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$DiscardResourceCmd": {"addons": "4.3.10", "hostUuid": "4.3.10", "kvmHostAddons": "4.3.10", "primaryStorageUuid": "4.3.10", "resourceUuid": "4.3.10", "vgUuid": "4.3.10"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$DisonnectCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "stopServices": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$DownloadBitsFromFileSystemCmd": {"addons": "3.10.38", "dstInstallPath": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "skipIfExisting": "3.10.38", "skipReplication": "4.3.10", "srcInstallPath": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$FlushCacheCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$ForceConnectCmd": {"addons": "4.3.10", "hostUuid": "4.3.10", "kvmHostAddons": "4.3.10", "primaryStorageUuid": "4.3.10", "resourceUuid": "4.3.10", "vgUuid": "4.3.10"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$GetQCOW2ReferenceCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$GetVolumeSizeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$KvmCancelSelfFencerCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$KvmSetupSelfFencerCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "interval": "3.10.38", "kvmHostAddons": "3.10.38", "maxAttempts": "3.10.38", "primaryStorageUuid": "3.10.38", "storageCheckerTimeout": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$ProvisionResourceCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "skipReplication": "4.3.10", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$ReportReplicationStatusCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$ResizeVolumeCmd": {"addons": "3.10.38", "drbd": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "isFileSystem": "4.3.10", "kvmHostAddons": "3.10.38", "live": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "size": "3.10.38", "skipReplication": "4.3.10", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$RevertVolumeFromSnapshotCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "init": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "local_address": "3.10.38", "local_host_name": "3.10.38", "local_host_port": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "remote_address": "3.10.38", "remote_host_name": "3.10.38", "remote_host_port": "3.10.38", "resourceUuid": "3.10.38", "single": "4.3.10", "skipReplication": "4.3.10", "snapshotInstallPath": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$SyncBackingChainCmd": {"addons": "3.10.38", "backingFileInfos": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "remoteHostname": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.ministorage.MiniStorageKvmCommands$UploadBitsToFileSystemCmd": {"addons": "3.10.38", "dstInstallPath": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "skipIfExisting": "3.10.38", "srcInstallPath": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CancelDownloadBitsFromKVMHostCmd": {"kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CheckIsBitsExistingCmd": {"hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CheckIsBitsExistingRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "existing": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateEmptyVolumeCmd": {"accountUuid": "3.10.38", "backingFile": "4.8.10", "hypervisorType": "3.10.38", "installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "size": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38", "withoutVolume": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateEmptyVolumeResponse": {"actualSize": "4.5.0", "availableCapacity": "3.10.38", "error": "3.10.38", "size": "4.7.22", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateFolderCmd": {"installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateRootVolumeFromTemplateCmd": {"accountUuid": "3.10.38", "hypervisorType": "3.10.38", "installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "templatePathInCache": "3.10.38", "timeout": "3.10.38", "uuid": "3.10.38", "virtualSize": "4.4.24", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateRootVolumeFromTemplateResponse": {"actualSize": "4.5.0", "availableCapacity": "3.10.38", "error": "3.10.38", "size": "4.7.22", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateTemplateFromVolumeCmd": {"incremental": "4.7.0", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "rootVolumePath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateTemplateFromVolumeRsp": {"actualSize": "4.1.0", "availableCapacity": "3.10.38", "error": "3.10.38", "size": "4.1.0", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateVolumeCmd": {"accountUuid": "3.10.38", "hypervisorType": "3.10.38", "installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateVolumeWithBackingCmd": {"accountUuid": "3.10.38", "hypervisorType": "3.10.38", "installUrl": "3.10.38", "kvmHostAddons": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "templatePathInCache": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$CreateVolumeWithBackingRsp": {"actualSize": "3.10.38", "availableCapacity": "3.10.38", "error": "3.10.38", "size": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$DeleteCmd": {"folder": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$DeleteResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "inUse": "4.4.52", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$DownloadBitsFromKVMHostCmd": {"backupStorageInstallPath": "3.10.38", "bandWidth": "3.10.38", "hostname": "3.10.38", "identificationCode": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "username": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$DownloadBitsFromKVMHostRsp": {"error": "3.10.38", "format": "3.10.38", "success": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$DownloadBitsFromSftpBackupStorageCmd": {"backupStorageInstallPath": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "username": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$DownloadBitsFromSftpBackupStorageResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$EstimateTemplateSizeCmd": {"kvmHostAddons": "4.7.0", "primaryStorageUuid": "4.7.0", "uuid": "4.7.0", "volumePath": "4.7.0"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$EstimateTemplateSizeRsp": {"actualSize": "4.7.0", "availableCapacity": "4.7.0", "error": "4.7.0", "size": "4.7.0", "success": "4.7.0", "totalCapacity": "4.7.0"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetBackingChainCmd": {"installPath": "4.7.0", "kvmHostAddons": "4.7.0", "primaryStorageUuid": "4.7.0", "uuid": "4.7.0", "volumeUuid": "4.7.0"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetBackingChainRsp": {"availableCapacity": "4.7.0", "backingChain": "4.7.0", "error": "4.7.0", "success": "4.7.0", "totalCapacity": "4.7.0", "totalSize": "4.7.0"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetBatchVolumeActualSizeCmd": {"kvmHostAddons": "4.5.11", "primaryStorageUuid": "4.5.11", "uuid": "4.5.11", "volumeUuidInstallPaths": "4.5.11"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetBatchVolumeActualSizeRsp": {"actualSizes": "4.5.11", "availableCapacity": "4.5.11", "error": "4.5.11", "success": "4.5.11", "totalCapacity": "4.5.11"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetCapacityCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetCapacityResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetDownloadBitsFromKVMHostProgressCmd": {"kvmHostAddons": "3.10.38", "volumePaths": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetDownloadBitsFromKVMHostProgressRsp": {"error": "3.10.38", "success": "3.10.38", "totalSize": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetQcow2HashValueCmd": {"hostUuid": "4.3.18", "installPath": "4.3.18", "kvmHostAddons": "4.3.18", "primaryStorageUuid": "4.3.18", "uuid": "4.3.18"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetQcow2HashValueRsp": {"availableCapacity": "4.3.18", "error": "4.3.18", "hashValue": "4.3.18", "success": "4.3.18", "totalCapacity": "4.3.18"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetVolumeActualSizeCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetVolumeActualSizeRsp": {"actualSize": "3.10.38", "availableCapacity": "3.10.38", "error": "3.10.38", "size": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetVolumeBaseImagePathCmd": {"imageCacheDir": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38", "volumeInstallDir": "3.10.38", "volumeInstallPath": "4.8.10", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$GetVolumeBaseImagePathRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "otherPaths": "4.8.10", "path": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$LinkVolumeNewDirCmd": {"dstDir": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "srcDir": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$LinkVolumeNewDirRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$ListDirectionCmd": {"kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$ListDirectionResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "paths": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$MergeSnapshotCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPath": "3.10.38", "taskContext": "4.4.0", "threadContext": "4.4.0", "threadContextStack": "4.4.0", "uuid": "3.10.38", "volumeUuid": "3.10.38", "workspaceInstallPath": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$MergeSnapshotResponse": {"actualSize": "3.10.38", "availableCapacity": "3.10.38", "error": "3.10.38", "size": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$MountAgentResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$MountCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "options": "3.10.38", "primaryStorageUuid": "3.10.38", "url": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$MoveBitsCmd": {"destPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "srcPath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$MoveBitsRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$NfsPrimaryStorageAgentCommand": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$NfsPrimaryStorageAgentResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$NfsRebaseVolumeBackingFileCmd": {"dstImageCacheTemplateFolderPath": "3.10.38", "dstPsMountPath": "3.10.38", "dstVolumeFolderPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "srcPsMountPath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$NfsRebaseVolumeBackingFileRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$NfsToNfsMigrateBitsCmd": {"dstFolderPath": "3.10.38", "filtPaths": "3.10.38", "independentPath": "4.7.0", "isMounted": "3.10.38", "kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "options": "3.10.38", "primaryStorageUuid": "3.10.38", "srcFolderPath": "3.10.38", "srcPrimaryStorageUuid": "4.7.22", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "url": "3.10.38", "uuid": "3.10.38", "volumeInstallPath": "4.7.22"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$NfsToNfsMigrateBitsRsp": {"availableCapacity": "3.10.38", "dstFilesActualSize": "4.7.22", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$OfflineMergeSnapshotCmd": {"destPath": "3.10.38", "fullRebase": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "srcPath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$OfflineMergeSnapshotRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$PingCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "primaryStorageUuid": "3.10.38", "url": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$ReInitImageCmd": {"imagePath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38", "volumePath": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$ReInitImageRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "newVolumeInstallPath": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$RebaseAndMergeSnapshotsCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPaths": "3.10.38", "uuid": "3.10.38", "volumeUuid": "3.10.38", "workspaceInstallPath": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$RebaseAndMergeSnapshotsResponse": {"actualSize": "3.10.38", "availableCapacity": "3.10.38", "error": "3.10.38", "size": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$RemountCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "options": "3.10.38", "primaryStorageUuid": "3.10.38", "url": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$RevertVolumeFromSnapshotCmd": {"kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPath": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$RevertVolumeFromSnapshotResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "newVolumeInstallPath": "3.10.38", "size": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UnlinkBitsCmd": {"installPath": "4.4.46", "kvmHostAddons": "4.4.46", "onlyLinkedFile": "4.4.46", "primaryStorageUuid": "4.4.46", "uuid": "4.4.46"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UnlinkBitsRsp": {"availableCapacity": "4.4.46", "error": "4.4.46", "success": "4.4.46", "totalCapacity": "4.4.46"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UnmountCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "primaryStorageUuid": "3.10.38", "url": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UnmountResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UpdateMountPointCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38", "newMountPoint": "3.10.38", "oldMountPoint": "3.10.38", "options": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UpdateMountPointRsp": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UploadToSftpCmd": {"backupStorageHostName": "3.10.38", "backupStorageInstallPath": "3.10.38", "backupStorageSshKey": "3.10.38", "backupStorageSshPort": "3.10.38", "backupStorageUserName": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.nfs.NfsPrimaryStorageKVMBackendCommands$UploadToSftpResponse": {"availableCapacity": "3.10.38", "error": "3.10.38", "success": "3.10.38", "totalCapacity": "3.10.38"}, "org.zstack.storage.primary.shareblock.ShareBlockHostHeartbeatChecker$ShareBlockCheckHostHeartbeatCmd": {"hostId": "5.2.0", "hostUuid": "4.7.0", "interval": "4.7.0", "kvmHostAddons": "4.7.0", "psUuid": "4.7.0", "storageCheckerTimeout": "4.7.0", "times": "4.7.0"}, "org.zstack.storage.primary.shareblock.ShareBlockHostHeartbeatChecker$ShareBlockCheckHostHeartbeatRsp": {"error": "4.7.0", "result": "4.7.0", "success": "4.7.0", "vmUuids": "4.7.0"}, "org.zstack.storage.primary.sharedblock.HaSanlockHostChecker$ScanCmd": {"hostIds": "3.10.38", "hostUuid": "4.7.0", "interval": "3.10.38", "kvmHostAddons": "3.10.38", "psUuid": "4.7.0", "times": "3.10.38"}, "org.zstack.storage.primary.sharedblock.HaSanlockHostChecker$ScanRsp": {"error": "3.10.38", "result": "3.10.38", "success": "3.10.38", "vmUuids": "4.7.0"}, "org.zstack.storage.primary.sharedblock.ImageStoreBackupStorageKvmDownloader$DownloadFromImageStoreCmd": {"addons": "3.10.38", "backupStorageInstallPath": "3.10.38", "concurrency": "4.4.46", "hostUuid": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "lockType": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "username": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.ImageStoreBackupStorageKvmUploader$UploadToImageStoreCmd": {"addons": "3.10.38", "concurrency": "4.4.46", "description": "3.10.38", "hostUuid": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockImageStoreKvmBackend$CleanLvMetaCmd": {"addons": "4.8.0", "hostUuid": "4.8.0", "kvmHostAddons": "4.8.0", "primaryStorageInstallPath": "4.8.0", "primaryStorageUuid": "4.8.0", "provisioning": "4.8.0", "vgUuid": "4.8.0"}, "org.zstack.storage.primary.sharedblock.SharedBlockImageStoreKvmBackend$CommitVolumeAsImageCmd": {"addons": "3.10.38", "description": "3.10.38", "hostUuid": "3.10.38", "hostname": "3.10.38", "imageUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ActiveVolumeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "killProcess": "3.10.38", "kvmHostAddons": "3.10.38", "lockType": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "recursive": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$AddDiskCmd": {"addons": "3.10.38", "allSharedBlockUuids": "3.10.38", "diskUuid": "3.10.38", "forceWipe": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "onlyGenerateFilter": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$AgentCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CancelDownloadBitsFromKVMHostCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "lockType": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CheckBitsCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CheckDisksCmd": {"addons": "3.10.38", "failIfNoPath": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "rescan": "3.10.38", "rescan_scsi": "3.10.38", "sharedBlockUuids": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CheckLockCmd": {"addons": "4.4.52", "hostUuid": "4.4.52", "kvmHostAddons": "4.4.52", "primaryStorageUuid": "4.4.52", "provisioning": "4.4.52", "vgUuid": "4.4.52", "vgUuids": "4.4.52"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ConfigFilterCmd": {"addons": "3.10.38", "allSharedBlockUuids": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ConnectCmd": {"addons": "3.10.38", "allSharedBlockUuids": "3.10.38", "enableLvmetad": "3.10.38", "forceWipe": "3.10.38", "hostId": "3.10.38", "hostUuid": "3.10.38", "ioTimeout": "3.10.38", "isFirst": "3.10.38", "kvmHostAddons": "3.10.38", "maxActualSizeFactor": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "sharedBlockUuids": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ConvertImageToVolumeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ConvertVolumeFormatCmd": {"addons": "3.10.38", "dstFormat": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "srcFormat": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ConvertVolumeProvisioningCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "provisioningStrategy": "3.10.38", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CreateDataVolumeWithBackingCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "taskContext": "4.4.64", "templatePathInCache": "3.10.38", "threadContext": "4.4.64", "threadContextStack": "4.4.64", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CreateEmptyVolumeCmd": {"addons": "3.10.38", "backingFile": "4.8.10", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "size": "3.10.38", "taskContext": "4.4.64", "threadContext": "4.4.64", "threadContextStack": "4.4.64", "vgUuid": "3.10.38", "volumeFormat": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CreateImageCacheFromVolumeCmd": {"addons": "4.1.3", "compareQcow2": "4.1.3", "hostUuid": "4.1.3", "incremental": "4.7.0", "installPath": "4.1.3", "kvmHostAddons": "4.1.3", "primaryStorageUuid": "4.1.3", "provisioning": "4.1.3", "taskContext": "4.1.3", "threadContext": "4.1.3", "threadContextStack": "4.1.3", "vgUuid": "4.1.3", "volumePath": "4.1.3"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CreateTemplateFromVolumeCmd": {"addons": "3.10.38", "compareQcow2": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "sharedVolume": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "vgUuid": "3.10.38", "volumePath": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$CreateVolumeFromCacheCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "taskContext": "4.4.64", "templatePathInCache": "3.10.38", "threadContext": "4.4.64", "threadContextStack": "4.4.64", "vgUuid": "3.10.38", "virtualSize": "4.4.24", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$DeleteBitsCmd": {"addons": "3.10.38", "folder": "3.10.38", "hostUuid": "3.10.38", "issueDiscards": "5.2.0", "kvmHostAddons": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$DeleteTag": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "tag": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$DisonnectCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "stopServices": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$DownloadBitsFromKVMHostCmd": {"addons": "3.10.38", "backupStorageInstallPath": "3.10.38", "bandWidth": "3.10.38", "hostUuid": "3.10.38", "hostname": "3.10.38", "identificationCode": "3.10.38", "kvmHostAddons": "3.10.38", "lockType": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "username": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$EstimateTemplateSizeCmd": {"addons": "4.7.0", "hostUuid": "4.7.0", "kvmHostAddons": "4.7.0", "primaryStorageUuid": "4.7.0", "provisioning": "4.7.0", "vgUuid": "4.7.0", "volumePath": "4.7.0"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ExtendMergeTargetCmd": {"addons": "4.4.10", "destPath": "4.4.10", "fullRebase": "4.4.10", "hostUuid": "4.4.10", "kvmHostAddons": "4.4.10", "primaryStorageUuid": "4.4.10", "provisioning": "4.4.10", "srcPath": "4.4.10", "vgUuid": "4.4.10", "volumeUuid": "4.4.10"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ExtendMigrateTargetCmd": {"addons": "4.7.22", "destPath": "4.7.22", "hostUuid": "4.7.22", "kvmHostAddons": "4.7.22", "primaryStorageUuid": "4.7.22", "provisioning": "4.7.22", "requiredSize": "4.7.22", "vgUuid": "4.7.22", "volumeUuid": "4.7.22"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$GetBackingChainCmd": {"addons": "3.10.38", "containSelf": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$GetBatchVolumeSizeCmd": {"addons": "4.5.11", "hostUuid": "4.5.11", "kvmHostAddons": "4.5.11", "primaryStorageUuid": "4.5.11", "provisioning": "4.5.11", "vgUuid": "4.5.11", "volumeUuidInstallPaths": "4.5.11"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$GetBlockDevicesCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$GetDownloadBitsFromKVMHostProgressCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38", "volumePaths": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$GetQcow2HashValueCmd": {"addons": "4.3.18", "hostUuid": "4.3.18", "installPath": "4.3.18", "kvmHostAddons": "4.3.18", "primaryStorageUuid": "4.3.18", "provisioning": "4.3.18", "vgUuid": "4.3.18"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$GetVolumeSizeCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$KvmCancelSelfFencerCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$KvmSetupSelfFencerCmd": {"addons": "3.10.38", "checkIo": "3.10.38", "fail_if_no_path": "3.10.38", "fencers": "4.7.0", "hostUuid": "3.10.38", "interval": "3.10.38", "kvmHostAddons": "3.10.38", "maxAttempts": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "storageCheckerTimeout": "3.10.38", "strategy": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$MergeSnapshotCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "snapshotInstallPath": "3.10.38", "taskContext": "4.4.0", "threadContext": "4.4.0", "threadContextStack": "4.4.0", "vgUuid": "3.10.38", "volumeUuid": "3.10.38", "workspaceInstallPath": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$MigrateDataCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "migrateVolumeStructs": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "vgUuid": "3.10.38", "volumePath": "4.7.22"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$OfflineMergeSnapshotCmd": {"addons": "3.10.38", "destPath": "3.10.38", "fullRebase": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "sharedVolume": "3.10.38", "srcPath": "3.10.38", "taskContext": "4.7.0", "threadContext": "4.7.0", "threadContextStack": "4.7.0", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$Qcow2Cmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "qcow2Options": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ResizeVolumeCmd": {"addons": "3.10.38", "force": "4.4.24", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "live": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "size": "3.10.38", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$RevertVolumeFromSnapshotCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "snapshotInstallPath": "3.10.38", "vgUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$SftpDownloadBitsCmd": {"addons": "3.10.38", "backupStorageInstallPath": "3.10.38", "hostUuid": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "lockType": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "username": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$SftpUploadBitsCmd": {"addons": "3.10.38", "backupStorageInstallPath": "3.10.38", "hostUuid": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.sharedblock.SharedBlockKvmCommands$ShrinkSnapshotCmd": {"addons": "3.10.38", "hostUuid": "3.10.38", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "primaryStorageUuid": "3.10.38", "provisioning": "3.10.38", "vgUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$AgentCmd": {"kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$CancelDownloadBitsFromKVMHostCmd": {"kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$CheckBitsCmd": {"kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$ConnectCmd": {"existUuids": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "uuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$CreateEmptyVolumeCmd": {"backingFile": "4.8.10", "installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "name": "3.10.38", "primaryStorageUuid": "3.10.38", "size": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$CreateFolderCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$CreateTemplateFromVolumeCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "volumePath": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$CreateVolumeFromCacheCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "templatePathInCache": "3.10.38", "virtualSize": "4.4.24", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$CreateVolumeWithBackingCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "templatePathInCache": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$DeleteBitsCmd": {"folder": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$DownloadBitsFromKVMHostCmd": {"backupStorageInstallPath": "3.10.38", "bandWidth": "3.10.38", "hostname": "3.10.38", "identificationCode": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "username": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$EstimateTemplateSizeCmd": {"kvmHostAddons": "4.7.0", "mountPoint": "4.7.0", "primaryStorageUuid": "4.7.0", "volumePath": "4.7.0"}, "org.zstack.storage.primary.smp.KvmBackend$GetBackingChainCmd": {"installPath": "4.7.0", "kvmHostAddons": "4.7.0", "mountPoint": "4.7.0", "primaryStorageUuid": "4.7.0", "volumeUuid": "4.7.0"}, "org.zstack.storage.primary.smp.KvmBackend$GetDownloadBitsFromKVMHostProgressCmd": {"kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "volumePaths": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$GetQcow2HashValueCmd": {"installPath": "4.3.18", "kvmHostAddons": "4.3.18", "mountPoint": "4.3.18", "primaryStorageUuid": "4.3.18"}, "org.zstack.storage.primary.smp.KvmBackend$GetSubPathCmd": {"kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "path": "3.10.38", "primaryStorageUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$GetVolumeSizeCmd": {"installPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$LinkVolumeNewDirCmd": {"dstDir": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "srcDir": "3.10.38", "volumeUuid": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$MergeSnapshotCmd": {"kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPath": "3.10.38", "taskContext": "4.4.0", "threadContext": "4.4.0", "threadContextStack": "4.4.0", "volumeUuid": "3.10.38", "workspaceInstallPath": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$OfflineMergeSnapshotCmd": {"destPath": "3.10.38", "fullRebase": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "srcPath": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$ReInitImageCmd": {"imageInstallPath": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "volumeInstallPath": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$ResizeVolumeCmd": {"force": "4.7.11", "installPath": "4.7.11", "kvmHostAddons": "4.7.11", "mountPoint": "4.7.11", "primaryStorageUuid": "4.7.11", "size": "4.7.11"}, "org.zstack.storage.primary.smp.KvmBackend$RevertVolumeFromSnapshotCmd": {"kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageUuid": "3.10.38", "snapshotInstallPath": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$SftpDownloadBitsCmd": {"backupStorageInstallPath": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "username": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$SftpUploadBitsCmd": {"backupStorageInstallPath": "3.10.38", "hostname": "3.10.38", "kvmHostAddons": "3.10.38", "mountPoint": "3.10.38", "primaryStorageInstallPath": "3.10.38", "primaryStorageUuid": "3.10.38", "sshKey": "3.10.38", "sshPort": "3.10.38", "taskContext": "3.10.38", "threadContext": "3.10.38", "threadContextStack": "3.10.38", "username": "3.10.38"}, "org.zstack.storage.primary.smp.KvmBackend$UnlinkBitsCmd": {"installPath": "4.4.46", "kvmHostAddons": "4.4.46", "mountPoint": "4.4.46", "onlyLinkedFile": "4.4.46", "primaryStorageUuid": "4.4.46"}, "org.zstack.sugonSdnController.network.TfMigrateVmBackend$SugonNicNotifyCmd": {"accountUuid": "4.4.52", "kvmHostAddons": "4.4.52", "nics": "4.4.52", "sugonSdnAction": "4.4.52", "vmInstanceUuid": "4.4.52"}, "org.zstack.sugonSdnController.network.TfMigrateVmBackend$SugonNicNotifyCmdRsp": {"error": "4.8.0", "success": "4.8.0"}, "org.zstack.sugonSdnController.userdata.TfUserdataBackend$ApplyUserdataCmd": {"hostUuid": "4.8.0", "kvmHostAddons": "4.8.0", "userdata": "4.8.0"}, "org.zstack.sugonSdnController.userdata.TfUserdataBackend$ApplyUserdataRsp": {"error": "4.8.0", "success": "4.8.0"}, "org.zstack.sugonSdnController.userdata.TfUserdataBackend$BatchApplyUserdataCmd": {"kvmHostAddons": "4.8.0", "rebuild": "4.8.0", "userdata": "4.8.0"}, "org.zstack.sugonSdnController.userdata.TfUserdataBackend$ReleaseUserdataCmd": {"hostUuid": "4.8.0", "kvmHostAddons": "4.8.0", "vmIp": "4.8.0", "vmUuid": "4.8.0"}, "org.zstack.sugonSdnController.userdata.TfUserdataBackend$ReleaseUserdataRsp": {"error": "4.8.0", "success": "4.8.0"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$CheckUsbRedirectPortCmd": {"kvmHostAddons": "3.10.38", "portList": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$CheckUsbRedirectPortRsp": {"error": "3.10.38", "success": "3.10.38", "uuids": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$GetUsbDevicesCmd": {"blackList": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$GetUsbDevicesRsp": {"error": "3.10.38", "success": "3.10.38", "usbDevicesInfo": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$KvmAttachUsbDeviceCmd": {"attachType": "3.10.38", "busNum": "3.10.38", "devNum": "3.10.38", "hostUuid": "3.10.38", "idProduct": "3.10.38", "idVendor": "3.10.38", "ip": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38", "usbVersion": "3.10.38", "vmBusNum": "5.2.0", "vmUuid": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$KvmAttachUsbDeviceRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$KvmDetachUsbDeviceCmd": {"attachType": "3.10.38", "busNum": "3.10.38", "devNum": "3.10.38", "hostUuid": "3.10.38", "idProduct": "3.10.38", "idVendor": "3.10.38", "ip": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38", "vmUuid": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$KvmDetachUsbDeviceRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$ReloadRedirectUsbCmd": {"attachType": "3.10.38", "busNum": "3.10.38", "devNum": "3.10.38", "hostUuid": "3.10.38", "idProduct": "3.10.38", "idVendor": "3.10.38", "ip": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38", "usbVersion": "3.10.38", "vmBusNum": "5.2.0", "vmUuid": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$ReloadRedirectUsbRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$StartUsbServerCmd": {"busNum": "3.10.38", "devNum": "3.10.38", "idProduct": "3.10.38", "idVendor": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$StartUsbServerRsp": {"error": "3.10.38", "port": "3.10.38", "success": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$StopUsbServerCmd": {"busNum": "3.10.38", "devNum": "3.10.38", "kvmHostAddons": "3.10.38", "port": "3.10.38"}, "org.zstack.usbDevice.KvmUsbDeviceBackend.UsbDeviceKvmBackend$StopUsbServerRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpc.VpcManagerImpl$GetConnectionsRsp": {"error": "3.10.38", "rawConnections": "3.10.38", "success": "3.10.38"}, "org.zstack.vpc.VpcManagerImpl$GetStatusRsp": {"error": "3.10.38", "rawStatus": "3.10.38", "success": "3.10.38"}, "org.zstack.vpc.VpcManagerImpl$SetDistributedRoutingCmd": {"enabled": "3.10.38"}, "org.zstack.vpc.VpcManagerImpl$SetDistributedRoutingRsp": {"enabled": "3.10.38", "error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpc.VpcManagerImpl$SetNetworkServiceRsp": {"error": "3.10.38", "serviceStatus": "3.10.38", "success": "3.10.38"}, "org.zstack.vpc.VpcManagerImpl$SetNetworkServiceSnatCmd": {"enabled": "3.10.38", "snats": "3.10.38"}, "org.zstack.vpc.VpcRouterCommands$VpcRouterSetDnsCmd": {"dns": "3.10.38", "nicMac": "3.10.38"}, "org.zstack.vpc.VpcRouterCommands$VpcRouterSetDnsRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$ApplyRuleSetChangesRsp": {"error": "4.0.0", "success": "4.0.0"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$ApplyUserFirewallRuleCmd": {"error": "3.10.38", "refs": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$ApplyUserFirewallRuleRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$AttachRuleSetCmd": {"ref": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$AttachRuleSetRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$ChangeFirewallRuleStateCmd": {"forward": "4.0.0", "mac": "4.0.0", "rule": "3.10.38", "state": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$ChangeFirewallRuleStateRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$CreateFirewallRuleCmd": {"ref": "4.0.0"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$CreateFirewallRuleRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$CreateFirewallRuleSetCmd": {"ruleSet": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$CreateFirewallRuleSetRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DeleteFirewallCmd": {"nicInfos": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DeleteFirewallRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DeleteFirewallRuleCmd": {"ref": "4.0.0"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DeleteFirewallRuleRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DeleteFirewallRuleSetCmd": {"ruleSetName": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DeleteFirewallRuleSetRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DetachRuleSetCmd": {"ref": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$DetachRuleSetRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$GetFirewallConfigCmd": {"nicInfos": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$GetFirewallConfigRsp": {"error": "3.10.38", "refs": "3.10.38", "success": "3.10.38"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$UpdateRuleSetCmd": {"actionType": "3.10.38", "forward": "4.0.0", "mac": "4.0.0"}, "org.zstack.vpcfirewall.entity.VpcFirewallCommands$UpdateRuleSetRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.vrouterRoute.vyos.VyosRouteBackend$GetRoutesRsp": {"error": "3.10.38", "rawRoutes": "3.10.38", "success": "3.10.38"}, "org.zstack.vrouterRoute.vyos.VyosRouteBackend$SyncRoutesCmd": {"routes": "3.10.38"}, "org.zstack.vrouterRoute.vyos.VyosRouteBackend$SyncRoutesRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.xdragon.XDragonHostConnectExtensionPoint$InitMocCmd": {"bridgeName": "3.10.38", "kvmHostAddons": "3.10.38", "masterVethName": "3.10.38", "mode": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$DeleteBitsCmd": {"installPath": "3.10.38", "isDir": "3.10.38", "kvmHostAddons": "3.10.38", "mountPath": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$DeleteBitsRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$EjectZBoxCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$EjectZBoxRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$InitZBoxCmd": {"kvmHostAddons": "3.10.38", "name": "3.10.38", "skipFormat": "3.10.38", "usbDevice": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$InitZBoxRsp": {"error": "3.10.38", "info": "3.10.38", "success": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$RefreshZBoxCmd": {"ignoreZBoxes": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$RefreshZBoxRsp": {"error": "3.10.38", "infos": "3.10.38", "success": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$SyncZBoxCmd": {"kvmHostAddons": "3.10.38", "mountPath": "3.10.38"}, "org.zstack.zbox.host.ZBoxKvmBackend$SyncZBoxRsp": {"error": "3.10.38", "info": "3.10.38", "success": "3.10.38"}, "org.zstack.zwatch.prometheus.KvmHostScrape$CollectdExporterCmd": {"binaryPath": "3.10.38", "interval": "3.10.38", "kvmHostAddons": "3.10.38", "startupArguments": "3.10.38", "version": "3.10.38"}, "org.zstack.zwatch.prometheus.KvmHostScrape$StartCollectdExporterCmd": {"cmds": "3.10.38", "kvmHostAddons": "3.10.38"}, "org.zstack.zwatch.prometheus.KvmHostScrape$StartCollectdExporterRsp": {"error": "3.10.38", "success": "3.10.38"}, "org.zstack.zwatch.prometheus.KvmHostScrape$ZSExporterCmd": {"binaryPath": "5.3.0", "configYaml": "5.3.0", "interval": "5.3.0", "kvmHostAddons": "5.3.0", "startupArguments": "5.3.0", "version": "5.3.0"}}