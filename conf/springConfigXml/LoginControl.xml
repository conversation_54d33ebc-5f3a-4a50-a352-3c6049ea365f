<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:zstack="http://zstack.org/schema/zstack"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
         http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
         http://www.springframework.org/schema/tx 
     	 http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
     	 http://zstack.org/schema/zstack 
         http://zstack.org/schema/zstack/plugin.xsd"
       default-init-method="init" default-destroy-method="destroy">

    <bean id="LoginControlManager" class="org.zstack.loginControl.LoginControlManagerImpl">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.header.Component"/>
            <zstack:extension interface="org.zstack.header.Service"/>
            <zstack:extension interface="org.zstack.header.apimediator.GlobalApiMessageInterceptor"/>
            <zstack:extension interface="org.zstack.identity.PasswordUpdateExtensionPoint"/>
            <zstack:extension interface="org.zstack.header.identity.login.LoginAuthExtensionPoint" order="9999"/>
        </zstack:plugin>
    </bean>

    <bean id="LoginControlApiInterceptor" class="org.zstack.loginControl.LoginControlApiInterceptor">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.header.apimediator.ApiMessageInterceptor"/>
        </zstack:plugin>
    </bean>
</beans>
