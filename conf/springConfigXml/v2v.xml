<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:zstack="http://zstack.org/schema/zstack"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
         http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
         http://www.springframework.org/schema/tx
         http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
         http://zstack.org/schema/zstack
         http://zstack.org/schema/zstack/plugin.xsd"
       default-init-method="init" default-destroy-method="destroy">

    <bean id="V2VManagerImpl" class="org.zstack.v2v.V2VManagerImpl">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.header.Component"/>
            <zstack:extension interface="org.zstack.header.Service"/>
            <zstack:extension interface="org.zstack.v2v.VmInfoCacher" />
            <zstack:extension interface="org.zstack.header.host.HostAfterConnectedExtensionPoint" order="-2"/>
            <zstack:extension interface="org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint" />
            <zstack:extension interface="org.zstack.kvm.KVMHostAttachVolumeExtensionPoint" />
            <zstack:extension interface="org.zstack.kvm.KVMHostDetachVolumeExtensionPoint" />
        </zstack:plugin>

        <property name="v2vConvertWorkFlowElements">
            <list>
                <value>org.zstack.v2v.ConvertBitsFromForeignHypervisorFlow</value>
                <value>org.zstack.v2v.CheckV2VAllocateHostFlow</value>
                <value>org.zstack.v2v.CheckV2VAllocatePrimaryStorageFlow</value>
                <value>org.zstack.v2v.CheckV2VAllocateVolumeFlow</value>
                <value>org.zstack.v2v.CheckVmAllocateNicFlow</value>
                <value>org.zstack.v2v.CheckVmAllocateNicIpFlow</value>
                <value>org.zstack.v2v.CheckVmInstantiateResourcesPreFlow</value>
                <value>org.zstack.v2v.CheckDownloadBitsFromConversionHostToPrimaryStorageFlow</value>
                <value>org.zstack.v2v.V2VSystemTagsOperationFlow</value>
                <value>org.zstack.v2v.PostV2VConversionExtensionFlow</value>
                <value>org.zstack.v2v.MarkVmReadyFlow</value>
                <value>org.zstack.v2v.CleanUpV2VConversionHostFlow</value>
                <value>org.zstack.v2v.CheckVmCreateOnHypervisorFlow</value>
                <value>org.zstack.compute.vm.VmInstantiateResourcePostFlow</value>
            </list>
        </property>
    </bean>

    <bean id="V2VApiInterceptor" class="org.zstack.v2v.V2VApiInterceptor">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.header.apimediator.ApiMessageInterceptor" />
            <zstack:extension interface="org.zstack.header.apimediator.GlobalApiMessageInterceptor"/>
        </zstack:plugin>
    </bean>

    <bean id="V2VSharedBlockBackend" class="org.zstack.v2v.sharedblock.V2VSharedBlockBackend">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.v2v.V2VPrimaryStorageBackend" />
        </zstack:plugin>
    </bean>

    <bean id="V2VConversionHostCascadeExtension" class="org.zstack.v2v.V2VConversionHostCascadeExtension">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.core.cascade.CascadeExtensionPoint" />
        </zstack:plugin>
    </bean>

    <bean id="VMwareV2VFactory" class="org.zstack.v2v.vmware.VMwareV2VFactory">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.v2v.V2VFactory" />
            <zstack:extension interface="org.zstack.header.apimediator.GlobalApiMessageInterceptor"/>
            <zstack:extension interface="org.zstack.header.host.HostAfterConnectedExtensionPoint"/>
        </zstack:plugin>
    </bean>

    <bean id="KVMV2VFactory" class="org.zstack.v2v.kvm.KVMV2VFactory">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.v2v.V2VFactory" />
            <zstack:extension interface="org.zstack.header.apimediator.GlobalApiMessageInterceptor"/>
            <zstack:extension interface="org.zstack.header.host.HostAfterConnectedExtensionPoint"/>
        </zstack:plugin>
    </bean>

    <bean id="VMwareV2VBase" class="org.zstack.v2v.vmware.VMwareV2VBase">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.header.Component"/>
            <zstack:extension interface="org.zstack.header.vm.VmInstanceStartExtensionPoint"/>
        </zstack:plugin>
    </bean>
</beans>
