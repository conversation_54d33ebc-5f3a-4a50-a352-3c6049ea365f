package org.zstack.vmware;

import com.vmware.vim25.mo.HostSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.thread.PeriodicTask;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.host.HostStatus;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

public class ESXHostMonitor implements ManagementNodeReadyExtensionPoint {
    private static final CLogger logger = Utils.getLogger(ESXHostMonitor.class);

    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    protected EventFacade evtf;
    @Autowired
    private ResourceDestinationMaker destinationMaker;

    private long getTimeIntervalInMilliSec(Date a, Date b){
        return Math.abs(a.getTime() - b.getTime());
    }

    private long getESXHostCheckInterval() {
        return VCenterGlobalConfig.ESX_HOST_CHECK_INTERVAL.value(Long.class);
    }

    @Override
    public void managementNodeReady() {
        thdf.submitPeriodicTask(new ESXHostResourceCheckTask());
    }

    private class ESXHostResourceCheckTask implements PeriodicTask {
        @Override
        public TimeUnit getTimeUnit() {
            return TimeUnit.SECONDS;
        }

        @Override
        public long getInterval() {
            return getESXHostCheckInterval();
        }

        @Override
        public String getName() {
            return "ESX-host-resource-check";
        }

        @Override
        public void run() {
            checkEsxHostDateTime();
        }
    }

    private void checkEsxHostDateTime() {
        List<VCenterVO> vos = Q.New(VCenterVO.class).list();

        for (VCenterVO vo : vos) {
            if (!destinationMaker.isManagedByUs(vo.getUuid())) {
                continue;
            }

            try (VCenterServiceInstance si = VMwareHelper.getVCenterServiceInstance(vo)){
                List<ESXHostVO> hostVos = Q.New(ESXHostVO.class).eq(ESXHostVO_.vCenterUuid, vo.getUuid()).list();
                for (ESXHostVO host : hostVos) {
                    if (!host.getStatus().equals(HostStatus.Connected)) {
                        continue;
                    }

                    try {
                        //unify the datetime
                        HostSystem esxHost = ESXHost.getHost(si, host);
                        TimeZone mnTimeZone = Calendar.getInstance().getTimeZone();
                        Calendar hostCal = esxHost.getHostDateTimeSystem().queryDateTime();
                        hostCal.setTimeZone(mnTimeZone);
                        Date hostTime = hostCal.getTime();

                        Date mnTime = Calendar.getInstance().getTime();
                        // fire if time difference more then one day
                        final long hostMnTimeInterval = getTimeIntervalInMilliSec(hostTime, mnTime);
                        if (hostMnTimeInterval > TimeUnit.DAYS.toMillis(1)) {
                            VCenterCanonicalEvents.VCenterHostWrongDateTimeData data = new VCenterCanonicalEvents.VCenterHostWrongDateTimeData();
                            data.setHostUuid(host.getUuid());
                            data.setTimeDifferenceInHour(String.valueOf(TimeUnit.MILLISECONDS.toHours(hostMnTimeInterval)));
                            evtf.fire(VCenterCanonicalEvents.VC_HOST_WRONG_DATETIME_PATH, data);
                        }
                    } catch (Exception e) {
                        logger.debug(String.format("checkEsxHostDateTime faild because %s", e.getMessage()));
                    }
                }
            }
        }
    }
}
