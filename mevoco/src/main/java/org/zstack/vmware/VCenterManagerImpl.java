package org.zstack.vmware;

import com.vmware.vim25.ArrayOfCheckResult;
import com.vmware.vim25.CheckResult;
import com.vmware.vim25.DVPortSetting;
import com.vmware.vim25.GuestNicInfo;
import com.vmware.vim25.HostHardwareSummary;
import com.vmware.vim25.HostVirtualNic;
import com.vmware.vim25.HostVirtualSwitch;
import com.vmware.vim25.InvalidLogin;
import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.ResourceAllocationInfo;
import com.vmware.vim25.ResourceConfigSpec;
import com.vmware.vim25.VMwareDVSPortSetting;
import com.vmware.vim25.VirtualDevice;
import com.vmware.vim25.VirtualDeviceBackingInfo;
import com.vmware.vim25.VirtualDeviceFileBackingInfo;
import com.vmware.vim25.VirtualDisk;
import com.vmware.vim25.VirtualEthernetCard;
import com.vmware.vim25.VirtualEthernetCardDistributedVirtualPortBackingInfo;
import com.vmware.vim25.VirtualEthernetCardNetworkBackingInfo;
import com.vmware.vim25.VirtualHardware;
import com.vmware.vim25.VirtualMachinePowerState;
import com.vmware.vim25.VirtualNicManagerNetConfig;
import com.vmware.vim25.VmwareDistributedVirtualSwitchPvlanSpec;
import com.vmware.vim25.VmwareDistributedVirtualSwitchTrunkVlanSpec;
import com.vmware.vim25.VmwareDistributedVirtualSwitchVlanIdSpec;
import com.vmware.vim25.VmwareDistributedVirtualSwitchVlanSpec;
import com.vmware.vim25.mo.ClusterComputeResource;
import com.vmware.vim25.mo.Datacenter;
import com.vmware.vim25.mo.Datastore;
import com.vmware.vim25.mo.DistributedVirtualPortgroup;
import com.vmware.vim25.mo.DistributedVirtualSwitch;
import com.vmware.vim25.mo.HostSystem;
import com.vmware.vim25.mo.InventoryNavigator;
import com.vmware.vim25.mo.ManagedEntity;
import com.vmware.vim25.mo.Network;
import com.vmware.vim25.mo.ResourcePool;
import com.vmware.vim25.mo.ServerConnection;
import com.vmware.vim25.mo.ServiceInstance;
import com.vmware.vim25.mo.Task;
import com.vmware.vim25.mo.VirtualMachine;
import com.vmware.vim25.mo.VirtualMachineProvisioningChecker;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.xml.sax.SAXParseException;
import org.zstack.appliancevm.ApplianceVmVO;
import org.zstack.appliancevm.ApplianceVmVO_;
import org.zstack.compute.host.HostSystemTags;
import org.zstack.compute.vm.GetInterdependentL3NetworksExtensionPoint;
import org.zstack.compute.vm.VmCapabilitiesExtensionPoint;
import org.zstack.compute.vm.VmNicManager;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.config.GlobalConfigException;
import org.zstack.core.config.GlobalConfigValidatorExtensionPoint;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.db.SQLBatchWithReturn;
import org.zstack.core.db.UpdateQuery;
import org.zstack.core.thread.AsyncThread;
import org.zstack.core.thread.CancelablePeriodicTask;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.AbstractService;
import org.zstack.header.Component;
import org.zstack.header.allocator.HostCapacityVO;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.cluster.ClusterConstant;
import org.zstack.header.cluster.ClusterDeletionMsg;
import org.zstack.header.cluster.ClusterState;
import org.zstack.header.core.Completion;
import org.zstack.header.core.FutureCompletion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.NopeWhileDoneCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.core.workflow.FlowDoneHandler;
import org.zstack.header.core.workflow.FlowErrorHandler;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.ConnectHostMsg;
import org.zstack.header.host.HostCanonicalEvents;
import org.zstack.header.host.HostConstant;
import org.zstack.header.host.HostDeletionMsg;
import org.zstack.header.host.HostInventory;
import org.zstack.header.host.HostState;
import org.zstack.header.host.HostStatus;
import org.zstack.header.host.HostVO;
import org.zstack.header.image.ImageArchitecture;
import org.zstack.header.image.ImageBackupStorageRefVO;
import org.zstack.header.image.ImageBackupStorageRefVO_;
import org.zstack.header.image.ImageConstant;
import org.zstack.header.image.ImageDeletionMsg;
import org.zstack.header.image.ImageDeletionPolicyManager;
import org.zstack.header.image.ImageEO;
import org.zstack.header.image.ImageStatus;
import org.zstack.header.image.ImageVO;
import org.zstack.header.managementnode.ManagementNodeChangeListener;
import org.zstack.header.managementnode.ManagementNodeInventory;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.L2NetworkClusterRefVO;
import org.zstack.header.network.l2.L2NetworkClusterRefVO_;
import org.zstack.header.network.l2.L2NetworkConstant;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l2.L2NetworkVO_;
import org.zstack.header.network.l2.L2VlanNetworkVO;
import org.zstack.header.network.l3.AllocateIpMsg;
import org.zstack.header.network.l3.AllocateIpReply;
import org.zstack.header.network.l3.L3NetworkCategory;
import org.zstack.header.network.l3.L3NetworkConstant;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkState;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO_;
import org.zstack.header.network.l3.NormalIpRangeVO;
import org.zstack.header.network.l3.UsedIpInventory;
import org.zstack.header.network.service.NetworkServiceType;
import org.zstack.header.storage.backup.BackupStorageConstant;
import org.zstack.header.storage.backup.BackupStorageState;
import org.zstack.header.storage.backup.BackupStorageStatus;
import org.zstack.header.storage.backup.BackupStorageZoneRefVO;
import org.zstack.header.storage.backup.BackupStorageZoneRefVO_;
import org.zstack.header.storage.backup.ConnectBackupStorageMsg;
import org.zstack.header.storage.primary.PrimaryStorageCapacityVO;
import org.zstack.header.storage.primary.PrimaryStorageClusterRefVO;
import org.zstack.header.storage.primary.PrimaryStorageClusterRefVO_;
import org.zstack.header.storage.primary.PrimaryStorageConstant;
import org.zstack.header.storage.primary.PrimaryStorageDeletionMsg;
import org.zstack.header.storage.primary.PrimaryStorageState;
import org.zstack.header.storage.primary.PrimaryStorageStatus;
import org.zstack.header.storage.primary.ReconnectPrimaryStorageMsg;
import org.zstack.header.vm.APIMigrateVmMsg;
import org.zstack.header.vm.DetachNicExtensionPoint;
import org.zstack.header.vm.DetachVolumeFromVmOnHypervisorMsg;
import org.zstack.header.vm.VmCanonicalEvents;
import org.zstack.header.vm.VmCapabilities;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.header.vm.VmInstanceDeletionMsg;
import org.zstack.header.vm.VmInstanceDeletionPolicyManager;
import org.zstack.header.vm.VmInstanceInventory;
import org.zstack.header.vm.VmInstanceSequenceNumberVO;
import org.zstack.header.vm.VmInstanceSpec;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmInstanceVO_;
import org.zstack.header.vm.VmJustBeforeDeleteFromDbExtensionPoint;
import org.zstack.header.vm.VmNicInventory;
import org.zstack.header.vm.VmNicVO;
import org.zstack.header.vm.VmNicVO_;
import org.zstack.header.vm.VmReleaseResourceExtensionPoint;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.volume.VolumeAO;
import org.zstack.header.volume.VolumeCanonicalEvents;
import org.zstack.header.volume.VolumeConstant;
import org.zstack.header.volume.VolumeDeletionMsg;
import org.zstack.header.volume.VolumeDeletionPolicyManager;
import org.zstack.header.volume.VolumeInventory;
import org.zstack.header.volume.VolumeState;
import org.zstack.header.volume.VolumeStatus;
import org.zstack.header.volume.VolumeType;
import org.zstack.header.volume.VolumeVO;
import org.zstack.header.volume.VolumeVO_;
import org.zstack.license.AskLicenseCapacityMsg;
import org.zstack.license.LicenseConstant;
import org.zstack.network.service.vip.VipConstant;
import org.zstack.network.service.vip.VipDeletionMsg;
import org.zstack.network.service.vip.VipNetworkServicesRefVO;
import org.zstack.network.service.vip.VipNetworkServicesRefVO_;
import org.zstack.network.service.vip.VipVO;
import org.zstack.network.service.virtualrouter.vip.VipConfigProxy;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6Constants;
import org.zstack.utils.network.NetworkUtils;

import javax.net.ssl.SSLHandshakeException;
import javax.persistence.Tuple;
import java.net.ConnectException;
import java.net.URL;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.err;
import static org.zstack.core.Platform.operr;
import static org.zstack.header.configuration.ConfigurationConstant.USER_VM_INSTANCE_OFFERING_TYPE;
import static org.zstack.vmware.VCenterConstant.SERVICE_ID;
import static org.zstack.vmware.VCenterConstant.VCENTER_BACKUP_STORAGE_TYPE;
import static org.zstack.vmware.VCenterConstant.VCENTER_CLUSTER_TYPE;
import static org.zstack.vmware.VCenterConstant.VCENTER_PRIMARY_STORAGE_TYPE;

/**
 * Created by david on 9/8/16.
 */
public class
VCenterManagerImpl extends AbstractService
        implements VCenterManager, Component, ManagementNodeReadyExtensionPoint, ManagementNodeChangeListener,
        VmReleaseResourceExtensionPoint, GlobalApiMessageInterceptor, VmCapabilitiesExtensionPoint,
        GetInterdependentL3NetworksExtensionPoint, VmJustBeforeDeleteFromDbExtensionPoint, DetachNicExtensionPoint {
    private static final CLogger logger = Utils.getLogger(VCenterManagerImpl.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;
    @Autowired
    private CascadeFacade casf;
    @Autowired
    private ResourceDestinationMaker destMaker;
    @Autowired
    private VncPortAllocator portAllocator;
    @Autowired
    protected EventFacade evtf;
    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    private VipConfigProxy vipProxy;
    @Autowired
    private VmNicManager nicManager;
    @Autowired
    private VMwareVmNicManager vMwareVmNicManager;
    @Autowired
    private VCenterServiceInstanceManager vCenterServerInstanceManager;

    private final Map<String, VMwareResourceMonitor> instancePool = new ConcurrentHashMap<>();

    private String getVCenterSyncSignature(final VCenterMessage msg) {
        return VCenterUtils.getVCenterSyncSignature(msg.getVCenterUuid());
    }

    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage(msg);
        } else if (msg instanceof VCenterMessage) {
            handleLocalMessage(msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handleApiMessage(Message msg) {
        if (msg instanceof APIAddVCenterMsg) {
            handle((APIAddVCenterMsg) msg);
        } else if (msg instanceof APISyncVCenterMsg) {
            handle((APISyncVCenterMsg) msg);
        } else if (msg instanceof APIUpdateVCenterMsg) {
            handle((APIUpdateVCenterMsg) msg);
        } else if (msg instanceof APIDeleteVCenterMsg) {
            handle((APIDeleteVCenterMsg) msg);
        } else if (msg instanceof APIGetVCenterDVSwitchesMsg) {
            handle((APIGetVCenterDVSwitchesMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof AllocateVncPortMsg) {
            handle((AllocateVncPortMsg) msg);
        } else if (msg instanceof VCenterDeletionMsg) {
            handle((VCenterDeletionMsg) msg);
        } else if (msg instanceof SyncVCenterMsg) {
            handle((SyncVCenterMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private static HostCapacityVO getHostCapacity(HostSystem host) {
        HostCapacityVO vo = new HostCapacityVO();

        final HostHardwareSummary hwSummary = host.getSummary().getHardware();
        final long memorySize = hwSummary.getMemorySize();
        vo.setTotalMemory(memorySize);
        vo.setTotalPhysicalMemory(memorySize);

        // Physical memory usage on the host in MB
        long memUsageMB = 0;
        try {
            memUsageMB = host.getSummary().getQuickStats().getOverallMemoryUsage() * 1024L * 1024L;
        } catch (Exception ex) {
            logger.warn("[vc] failed to get host memory usage for " + host.getName(), ex);
        }

        vo.setAvailableMemory(memorySize - memUsageMB);
        vo.setAvailablePhysicalMemory(memorySize - memUsageMB);

        vo.setCpuSockets(hwSummary.numCpuPkgs);
        vo.setCpuNum(hwSummary.numCpuThreads);

        return vo;
    }

    public List<VolumeVO> getAllVolumes(Map<String, VCenterPrimaryStorageVO> psvos,
                                        VirtualMachine vm,
                                        VmInstanceVO ivo) {
        final VirtualDevice[] devices = vm.getConfig().getHardware().getDevice();
        final List<VolumeVO> dataVolumes = new ArrayList<>();
        boolean rootVolume = false;

        final List<VolumeVO> existDataVolumes = Q.New(VolumeVO.class)
                .eq(VolumeVO_.type, VolumeType.Data)
                .eq(VolumeVO_.vmInstanceUuid, ivo.getUuid())
                .list();

        for (VirtualDevice device : devices) {
            if (!(device instanceof VirtualDisk)) {
                continue;
            }

            VirtualDeviceBackingInfo backingInfo = device.getBacking();
            if (!(backingInfo instanceof VirtualDeviceFileBackingInfo)) {
                logger.info("[vc] ignored vdisk backing device: " + backingInfo.getClass().getSimpleName());
                continue;
            }

            VirtualDeviceFileBackingInfo fileBackingInfo = (VirtualDeviceFileBackingInfo) backingInfo;
            String installPath = fileBackingInfo.getFileName();
            if (!installPath.endsWith("." + VMwareHelper.vmwareVDiskFormat)) {
                logger.info("[vc] ignored vdisk file: " + installPath);
                continue;
            }

            VCenterPrimaryStorageVO psvo;
            try {
                String dsMorVal = fileBackingInfo.getDatastore().getVal();
                psvo = psvos.get(dsMorVal);
            } catch (Exception e) {
                logger.info(String.format("[vc] get datastroe for %s failed: %s", installPath, e.getMessage()));
                continue;
            }

            VolumeVO volvo;
            if (device.getDeviceInfo().getLabel().equals(VMwareHelper.vmwareRootVolumeLabel)) {
                rootVolume = true;
                volvo = getVolumeVO(vm, ivo);
                VirtualDisk vdisk = (VirtualDisk) device;
                volvo.setSize(vdisk.getCapacityInBytes() != null ?
                        vdisk.getCapacityInBytes() : vdisk.getCapacityInKB() * 1024);
                volvo.setActualSize(vdisk.getCapacityInBytes() != null ?
                        vdisk.getCapacityInBytes() : vdisk.getCapacityInKB() * 1024);
                ivo.setRootVolumeUuid(volvo.getUuid());
            } else {
                volvo = getDataVolume((VirtualDisk) device, installPath, ivo, existDataVolumes, vm);
            }
            volvo.setVmInstanceUuid(ivo.getUuid());
            volvo.setFormat(VolumeConstant.VOLUME_FORMAT_VMTX);
            volvo.setPrimaryStorageUuid(psvo.getUuid());
            
            dataVolumes.add(volvo);
        }

        if (!rootVolume && !dataVolumes.isEmpty()) {
            dataVolumes.get(0).setType(VolumeType.Root);
            dataVolumes.get(0).setName("ROOT-for-" + vm.getName());
            dataVolumes.get(0).setDescription("Volume for vm:" + ivo.getUuid());
            ivo.setRootVolumeUuid(dataVolumes.get(0).getUuid());
        }

        return dataVolumes;
    }

    private VolumeVO getDataVolume(VirtualDisk vdisk,
                                   String installPath,
                                   VmInstanceVO ivo,
                                   List<VolumeVO> existDataVolumes,
                                   VirtualMachine vm) {
        VolumeVO vol = new VolumeVO();
        if (!existDataVolumes.isEmpty()) {
            for (VolumeVO vo : existDataVolumes) {
                if (vo.getInstallPath().equals(installPath)
                        || checkVolumeMapping(vo.getUuid(), installPath, vm.getServerConnection().getServiceInstance())) {
                    vol = vo;
                    break;
                }
            }
        }
        if (vol.getUuid() == null) {
            vol.setUuid(Platform.getUuid());
            vol.setName("DATA-for-" + vm.getName());
            vol.setDescription("Data volume for vm:" + ivo.getUuid());
        }
        if (ivo.getState().equals(VmInstanceState.Destroyed)) {
            vol.setStatus(VolumeStatus.Deleted);
        } else {
            vol.setStatus(VolumeStatus.Ready);
        }
        vol.setType(VolumeType.Data);
        vol.setState(VolumeState.Enabled);
        vol.setInstallPath(installPath);
        vol.setSize(vdisk.getCapacityInBytes() != null ?
                vdisk.getCapacityInBytes() : vdisk.getCapacityInKB() * 1024);
        vol.setActualSize(vdisk.getCapacityInBytes() != null ?
                vdisk.getCapacityInBytes() : vdisk.getCapacityInKB() * 1024);
        return vol;
    }

    private Boolean checkVolumeMapping(String volUuid, String installPath, ServiceInstance si) {
        if (VolumeSystemTag.VCENTER_VOLUME.hasTag(volUuid)) {
            String token = VolumeSystemTag.VCENTER_VOLUME.getTokenByResourceUuid(volUuid, VolumeSystemTag.VCENTER_VOLUME_TOKEN);
            String vCenterVolUuid = VMwareHelper.getVCenterVolUuidByInstallPath(si, volUuid, installPath);
            return vCenterVolUuid != null && vCenterVolUuid.equals(token);
        }
        return false;
    }

    private VolumeVO getVolumeVO(VirtualMachine vm, VmInstanceVO ivo) {
        VolumeVO vol = new VolumeVO();
        String installPath = VMwareHelper.getVmPathName(vm);
        List<VolumeVO> vos = Q.New(VolumeVO.class)
                .eq(VolumeVO_.type, VolumeType.Root)
                .eq(VolumeVO_.installPath, installPath)
                .eq(VolumeVO_.vmInstanceUuid, ivo.getUuid())
                .limit(2)
                .list();
        if (vos.size() == 1) {
            vol = vos.get(0);
        }
        if (vol.getUuid() == null) {
            vol.setUuid(Platform.getUuid());
            vol.setName("ROOT-for-" + vm.getName());
            vol.setDescription("Volume for vm:" + ivo.getUuid());
        }
        if (ivo.getState().equals(VmInstanceState.Destroyed)) {
            vol.setStatus(VolumeStatus.Deleted);
        } else {
            vol.setStatus(VolumeStatus.Ready);
        }
        vol.setType(VolumeType.Root);
        vol.setState(VolumeState.Enabled);
        vol.setInstallPath(installPath);

        final VMwareHelper.StorageUsage usage = VMwareHelper.getVmStorageUsage(vm);
        vol.setSize(usage.getSize());
        vol.setActualSize(usage.getActualSize());
        return vol;
    }

    private VmInstanceVO getVmInstanceVO(VirtualMachine vm, List<VmInstanceVO> imported) {
        final VirtualHardware hw = vm.getConfig().getHardware();
        final VmInstanceVO vo = new VmInstanceVO();
        final String zsVmUuid = VMwareHelper.getZstackVmUuid(vm);
        if (zsVmUuid != null
                && imported.stream().noneMatch((vmvo) -> vmvo.getUuid().equals(zsVmUuid))
                && !VMwareHelper.resourceExists(zsVmUuid, ImageVO.class.getSimpleName())) {
            vo.setUuid(zsVmUuid);
            vo.setState(VMwareHelper.getVmZStackSideState(vo.getUuid()));
        } else {
            vo.setUuid(Platform.getUuid());
            VMwareHelper.setZstackVmUuid(vm, vo.getUuid());
        }

        vo.setName(vm.getName());
        vo.setCpuNum(hw.getNumCPU());
        vo.setMemorySize(hw.getMemoryMB() * 1024L * 1024L);
        vo.setCpuSpeed(0);
        vo.setPlatform(VMwareHelper.getVmImagePlatform(vm).toString());
        vo.setGuestOsType(vo.getPlatform());
        vo.setArchitecture(ImageArchitecture.x86_64.toString());

        final VirtualMachinePowerState guestState = vm.getRuntime().getPowerState();
        if (!VmInstanceState.Destroyed.equals(vo.getState())) {
            vo.setState(VMwareHelper.fromPowerState(guestState));
        }
        vo.setType(USER_VM_INSTANCE_OFFERING_TYPE);
        vo.setHypervisorType(ESXConstant.VMWARE_HYPERVISOR_TYPE);
        vo.setInternalId(dbf.generateSequenceNumber(VmInstanceSequenceNumberVO.class));

        return vo;
    }

    private static boolean isUpLink(DistributedVirtualPortgroup dvpg) {
        DistributedVirtualSwitch dvs = VMwareHelper.getManagedEntity(
                dvpg.getServerConnection(),
                dvpg.getConfig().getDistributedVirtualSwitch());

        for (ManagedObjectReference lnk : dvs.getConfig().getUplinkPortgroup()) {
            if (lnk.get_value().equals(dvpg.getKey())) {
                return true;
            }
        }

        return false;
    }

    private static VmwareDistributedVirtualSwitchVlanSpec getDvsVlanSpec(DistributedVirtualPortgroup dvpg) {
        DVPortSetting ps = dvpg.getConfig().getDefaultPortConfig();
        VMwareDVSPortSetting vmps = (VMwareDVSPortSetting) ps;
        return vmps.getVlan();
    }

    private static boolean isTrunkVLan(DistributedVirtualPortgroup dvpg) {
        try {
            VmwareDistributedVirtualSwitchVlanSpec vLanSpec = getDvsVlanSpec(dvpg);
            return (vLanSpec instanceof VmwareDistributedVirtualSwitchTrunkVlanSpec);
        } catch (Throwable ex) {
            String message = "failed to get vlan spec for PortGroup: " + dvpg.getName();
            throw new CloudRuntimeException(message, ex);
        }
    }

    // A vLanId that is greater than 0 is a valid vLanId.
    private static int getVlanId(DistributedVirtualPortgroup dvpg) {
        try {
            VmwareDistributedVirtualSwitchVlanSpec vLanSpec = getDvsVlanSpec(dvpg);

            if (vLanSpec instanceof VmwareDistributedVirtualSwitchVlanIdSpec) {
                VmwareDistributedVirtualSwitchVlanIdSpec spec = (VmwareDistributedVirtualSwitchVlanIdSpec) vLanSpec;
                return spec.getVlanId();
            }

            if (vLanSpec instanceof VmwareDistributedVirtualSwitchPvlanSpec) {
                VmwareDistributedVirtualSwitchPvlanSpec spec = (VmwareDistributedVirtualSwitchPvlanSpec) vLanSpec;
                return spec.getPvlanId();
            }
        } catch (Throwable ex) {
            String message = "failed to get vlan ID for PortGroup: " + dvpg.getName();
            throw new CloudRuntimeException(message, ex);
        }

        throw new CloudRuntimeException(String.format("unexpected type of dvpg: %s", dvpg.getName()));
    }

    private static boolean isDVPG(Network nt) {
        if (!(nt instanceof DistributedVirtualPortgroup)) {
            return false;
        }

        DistributedVirtualPortgroup dvpg = (DistributedVirtualPortgroup) nt;
        if (dvpg.getConfig() == null) {
            logger.warn("DVPG has no configure: " + dvpg.getName());
            return false;
        }

        if (isUpLink(dvpg)) {
            return false;
        }

        return !isTrunkVLan(dvpg);
    }

    private static boolean networksHaveDVPG(Network[] networks) {
        if (networks == null) {
            return false;
        }

        for (Network nt : networks) {
            if (isDVPG(nt)) {
                return true;
            }
        }

        return false;
    }

    private static Map<String, DistributedVirtualPortgroup> getClusterDVPGs(ClusterComputeResource cluster) {
        Map<String, DistributedVirtualPortgroup> clusterDVPGs = new HashMap<>();
        for (Network nt : cluster.getNetworks()) {
            if (isDVPG(nt)) {
                DistributedVirtualPortgroup dvpg = (DistributedVirtualPortgroup) nt;
                clusterDVPGs.put(dvpg.getKey(), dvpg);
            }
        }

        return clusterDVPGs;
    }

    private String[] getL2Clusters(final String l2key,
                                   final Map<String, Map<String, DistributedVirtualPortgroup>> l2networks) {
        Set<String> clusters = new HashSet<>();
        for (Map.Entry<String, Map<String, DistributedVirtualPortgroup>> entry : l2networks.entrySet()) {
            for (Map.Entry<String, DistributedVirtualPortgroup> pg : entry.getValue().entrySet()) {
                if (l2key.equals(pg.getKey())) {
                    clusters.add(entry.getKey());
                }
            }
        }

        return clusters.toArray(new String[0]);
    }

    private L2NetworkVO getExistingL2Record(List<L2NetworkVO> l2vos, String vcUuid) {
        for (L2NetworkVO vo : l2vos) {
            if (VCenterTagHelper.getVCenterUuidFromL2Network(vo.getUuid()).equals(vcUuid)) {
                return vo;
            }
        }

        return null;
    }

    // For each common portgroup, we generate and persist the following records:
    //  1. L2NetworkVO/L2VlanNetworkVO
    //  2. L2NetworkClusterRefVO
    //  3. L3NetworkVO
    //  4. NormalIpRangeVO
    private void savePortGroupNetwork(final String acntUuid,
                                      final Map<String, Map<String, String>> l3dict,
                                      final String clusterUuid,
                                      final PortGroupInfo pgInfo,
                                      VCenterVO vcvo) {
        List<L2NetworkVO> l2vos = SQL.New("select l2vo from L2NetworkVO l2vo, L2NetworkClusterRefVO  refvo " +
                " where l2vo.physicalInterface = :interface " +
                " and l2vo.uuid = refvo.l2NetworkUuid " +
                " and refvo.clusterUuid = :uuid", L2NetworkVO.class)
                .param("interface", pgInfo.getName())
                .param("uuid", clusterUuid)
                .list();
        L2NetworkVO vo = getExistingL2Record(l2vos, vcvo.getUuid());
        if (vo == null) {
            vo = new L2NetworkVO();
            vo.setUuid(Platform.getUuid());
            vo.setZoneUuid(vcvo.getZoneUuid());
            vo.setName(pgInfo.getName());
            vo.setPhysicalInterface(pgInfo.getName());
            vo.setDescription("imported from vCenter: " + vcvo.getUuid());
            vo.setAccountUuid(acntUuid);
            final int vlanId = pgInfo.getVlanId();
            if (vlanId > 0) {
                vo.setType(L2NetworkConstant.L2_VLAN_NETWORK_TYPE);
                L2VlanNetworkVO vlanvo = new L2VlanNetworkVO(vo);
                vlanvo.setVlan(vlanId);
                dbf.persist(vlanvo);
            } else {
                vo.setType(L2NetworkConstant.L2_NO_VLAN_NETWORK_TYPE);
                dbf.persist(vo);
            }

            VCenterTagHelper.tagL2NetworkWithVCenterUuid(vo.getUuid(), vcvo.getUuid());
            VCenterTagHelper.tagL2VSwitch(vo.getUuid(), pgInfo.getSwitchName());
        }

        // find clusters that use this network
        if (!Q.New(L2NetworkClusterRefVO.class)
                .eq(L2NetworkClusterRefVO_.clusterUuid, clusterUuid)
                .eq(L2NetworkClusterRefVO_.l2NetworkUuid, vo.getUuid())
                .isExists()) {
            L2NetworkClusterRefVO crvo = new L2NetworkClusterRefVO();
            crvo.setClusterUuid(clusterUuid);
            crvo.setL2NetworkUuid(vo.getUuid());
            dbf.persist(crvo);
        }

        // create corresponding L3 network
        final String l3name = "L3-" + vo.getName();
        final String l3uuid = Q.New(L3NetworkVO.class)
                .eq(L3NetworkVO_.l2NetworkUuid, vo.getUuid())
                .select(L3NetworkVO_.uuid)
                .findValue();
        if (l3uuid != null) {
            buildL3Dict(l3dict, new String[]{clusterUuid}, vo.getName(), l3uuid);
            return;
        }

        final L3NetworkVO l3vo = new L3NetworkVO();
        l3vo.setUuid(Platform.getUuid());
        l3vo.setL2NetworkUuid(vo.getUuid());
        l3vo.setName(l3name);
        l3vo.setDescription("imported from vCenter: " + vcvo.getDomainName());
        l3vo.setSystem(false);
        l3vo.setCategory(L3NetworkCategory.Private);
        l3vo.setZoneUuid(vcvo.getZoneUuid());
        l3vo.setState(L3NetworkState.Enabled);
        l3vo.setType(L3NetworkConstant.L3_BASIC_NETWORK_TYPE);
        l3vo.setAccountUuid(acntUuid);
        l3vo.setIpVersion(IPv6Constants.IPv4);
        dbf.persist(l3vo);

        buildL3Dict(l3dict, new String[]{clusterUuid}, vo.getName(), l3vo.getUuid());

        // create NormalIpRangeVO for each L3 network
        final NormalIpRangeVO ipvo = new NormalIpRangeVO();
        ipvo.setUuid(Platform.getUuid());
        ipvo.setL3NetworkUuid(l3vo.getUuid());

        // When a host fails to dynamically acquire an address, it can optionally
        // assign itself a link-local IPv4 address in accordance with RFC 3927.
        ipvo.setName("dummy range for " + l3vo.getName());
        ipvo.setStartIp("***********");
        ipvo.setEndIp("***************");
        ipvo.setNetmask("***********");
        ipvo.setGateway("***********");
        ipvo.setAccountUuid(acntUuid);
        ipvo.setIpVersion(IPv6Constants.IPv4);
        dbf.persist(ipvo);
    }

    private void saveVSwitchNetworks(final String acntUuid,
                                     final Map<String, Map<String, String>> l3dict,
                                     List<VSwitchNetwork> vSwitchNetworks, VCenterVO vcvo) {
        logger.info("[vc] persistent vSwitch networks.");
        for (VSwitchNetwork vsn : vSwitchNetworks) {

            for (String hostUuid : vsn.getHostUuids()) {
                VCenterTagHelper.tagHostVSwitches(hostUuid, vsn.getVSwitchNames());
            }

            for (Map.Entry<String, List<PortGroupInfo>> entry : vsn.getPortGroups().entrySet()) {
                String vSwitchName = entry.getKey();
                List<PortGroupInfo> portgroups = entry.getValue();
                logger.info(String.format("[vc] vSwitch %s for cluster %s has %d networks",
                        vSwitchName, vsn.getClusterUuid(), portgroups.size()));
            }

            List<PortGroupInfo> pgInfoList = vsn.getPortGroups().values().stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            for (PortGroupInfo pgInfo : pgInfoList) {
                savePortGroupNetwork(acntUuid, l3dict, vsn.getClusterUuid(), pgInfo, vcvo);
            }
        }
    }

    private void buildL3Dict(final Map<String, Map<String, String>> l3dict,
                             String[] clusters,
                             String pGName,
                             String l3Uuid) {
        for (String clusterUuid : clusters) {
            if (l3dict.containsKey(clusterUuid)) {
                l3dict.get(clusterUuid).put(pGName, l3Uuid);
            } else {
                HashMap<String, String> map = new HashMap<>();
                map.put(pGName, l3Uuid);
                l3dict.put(clusterUuid, map);
            }
        }
    }

    private Set<String> saveNetworks(
            final String acntUuid,
            final Map<String, Map<String, String>> l3dict,
            final Map<String, DistributedVirtualPortgroup> dvPGs,
            final Map<String, Map<String, DistributedVirtualPortgroup>> l2networks,
            final VCenterVO vcvo) {
        Set<String> dvpgKeys = new HashSet<>();
        for (Map.Entry<String, DistributedVirtualPortgroup> entry : dvPGs.entrySet()) {
            String key = entry.getKey();
            DistributedVirtualPortgroup dvpg = entry.getValue();
            dvpgKeys.add(key);
            logger.trace(String.format("save network for port group %s", key));
            List<L2NetworkVO> l2vos = Q.New(L2NetworkVO.class).eq(L2NetworkVO_.physicalInterface, key).list();
            L2NetworkVO vo = getExistingL2Record(l2vos, vcvo.getUuid());
            if (vo == null) {
                vo = new L2NetworkVO();
                vo.setUuid(Platform.getUuid());
                vo.setZoneUuid(vcvo.getZoneUuid());
                vo.setName(dvpg.getName());
                vo.setPhysicalInterface(key);
                vo.setDescription("imported from vCenter: " + vcvo.getUuid());
                vo.setAccountUuid(acntUuid);
                final int vlanId = getVlanId(dvpg);
                if (vlanId > 0) {
                    vo.setType(L2NetworkConstant.L2_VLAN_NETWORK_TYPE);
                    L2VlanNetworkVO vlanvo = new L2VlanNetworkVO(vo);
                    vlanvo.setVlan(vlanId);
                    dbf.persist(vlanvo);
                } else {
                    vo.setType(L2NetworkConstant.L2_NO_VLAN_NETWORK_TYPE);
                    dbf.persist(vo);
                }

                VCenterTagHelper.tagL2NetworkWithVCenterUuid(vo.getUuid(), vcvo.getUuid());
                logger.trace(String.format("port group %s is new network", key));
            }

            logger.trace(String.format("port group %s is existing network", key));
            // find clusters that use this network
            final String[] clusters = getL2Clusters(key, l2networks);
            for (String clusterUuid : clusters) {
                if (!Q.New(L2NetworkClusterRefVO.class)
                        .eq(L2NetworkClusterRefVO_.clusterUuid, clusterUuid)
                        .eq(L2NetworkClusterRefVO_.l2NetworkUuid, vo.getUuid())
                        .isExists()) {
                    L2NetworkClusterRefVO crvo = new L2NetworkClusterRefVO();
                    crvo.setClusterUuid(clusterUuid);
                    crvo.setL2NetworkUuid(vo.getUuid());
                    dbf.persist(crvo);
                }
            }

            // create corresponding L3 network
            // VCenter l2 and l3 should one-one maping
            // if l2 has corresponding l3, skip creating
            final String l3name = "L3-" + vo.getName();
            final String l3uuid = Q.New(L3NetworkVO.class)
                    .eq(L3NetworkVO_.l2NetworkUuid, vo.getUuid())
                    .select(L3NetworkVO_.uuid)
                    .findValue();
            if (l3uuid != null) {
                buildL3Dict(l3dict, clusters, vo.getName(), l3uuid);
                continue;
            }

            logger.trace(String.format("create l3 for port group %s", key));
            final L3NetworkVO l3vo = new L3NetworkVO();
            l3vo.setUuid(Platform.getUuid());
            l3vo.setL2NetworkUuid(vo.getUuid());
            l3vo.setName(l3name);
            l3vo.setDescription("imported from vCenter: " + vcvo.getDomainName());
            l3vo.setSystem(false);
            l3vo.setCategory(L3NetworkCategory.Private);
            l3vo.setZoneUuid(vcvo.getZoneUuid());
            l3vo.setState(L3NetworkState.Enabled);
            l3vo.setType(L3NetworkConstant.L3_BASIC_NETWORK_TYPE);
            l3vo.setAccountUuid(acntUuid);
            l3vo.setIpVersion(IPv6Constants.IPv4);
            dbf.persist(l3vo);

            buildL3Dict(l3dict, clusters, vo.getName(), l3vo.getUuid());

            // create NormalIpRangeVO for each L3 network
            final NormalIpRangeVO ipvo = new NormalIpRangeVO();
            ipvo.setUuid(Platform.getUuid());
            ipvo.setL3NetworkUuid(l3vo.getUuid());

            // When a host fails to dynamically acquire an address, it can optionally
            // assign itself a link-local IPv4 address in accordance with RFC 3927.
            ipvo.setName("dummy range for " + l3vo.getName());
            ipvo.setStartIp("***********");
            ipvo.setEndIp("***************");
            ipvo.setNetmask("***********");
            ipvo.setGateway("***********");
            ipvo.setAccountUuid(acntUuid);
            ipvo.setIpVersion(IPv6Constants.IPv4);
            dbf.persist(ipvo);
        }
        return dvpgKeys;
    }

    private String getHostSystemManagementIp(final HostSystem host, String hostUuid, final Map<String, List<String>> hostExtraIps) {
        String ip;
        List<ESXINicInfo> nicInfos = new ArrayList<>();
        try {
            HostVirtualNic[] vnic = host.getConfig().getNetwork().getConsoleVnic();
            VirtualNicManagerNetConfig netConfig = host.getHostVirtualNicManager().queryNetConfig("management");
            if (vnic == null) {
                // All CandidateVnic
                vnic = netConfig.getCandidateVnic();
            }
            for (HostVirtualNic nic : vnic) {
                nicInfos.add(ESXINicInfo.valueOf(nic));
            }
            // vNics marked as the management
            String[] managements = netConfig.getSelectedVnic();
            nicInfos.forEach(it -> {
                it.setManagement(Arrays.asList(managements).contains(it.getKey()));
                it.setReachable(NetworkUtils.isHostReachable(it.getIp(), 80, 2000));
            });
            logger.debug(String.format("all vnics info on host[%s]:%s", host.getName(), nicInfos));
            if (nicInfos.size() <= 1) {
                return nicInfos.get(0).getIp();
            }
            List<String> ips = nicInfos.stream().map(ESXINicInfo::getIp).collect(Collectors.toList());
            List<ESXINicInfo> fitIps = nicInfos.stream().filter(ESXINicInfo::isManagement).filter(ESXINicInfo::isReachable).collect(Collectors.toList());
            if (!fitIps.isEmpty()) {
                ip = fitIps.get(0).getIp();
            } else {
                ip = nicInfos.get(0).getIp();
            }
            ips.remove(ip);
            if (ips.size() > 0) {
                hostExtraIps.put(hostUuid, ips);
            }
            return ip;
        } catch (Throwable ex) {
            String message = "failed to get host IP for: " + host.getName();
            throw new CloudRuntimeException(message, ex);
        }
    }

    private void prepareStorageVOs(Map<String, Datastore> datastores,
                                   final VCenterVO vcvo,
                                   Map<String, VCenterPrimaryStorageVO> psvos,
                                   List<PrimaryStorageCapacityVO> psCapVOs,
                                   Map<String, VCenterBackupStorageVO> bsvos,
                                   List<BackupStorageZoneRefVO> bsZoneRefs) {
        for (Map.Entry<String, Datastore> entry : datastores.entrySet()) {
            final Datastore ds = entry.getValue();
            final String morval = entry.getKey();

            final VCenterBackupStorageVO bsvo = new VCenterBackupStorageVO();
            final String bsUuid = Q.New(VCenterBackupStorageVO.class)
                    .eq(VCenterBackupStorageVO_.vCenterUuid, vcvo.getUuid())
                    .eq(VCenterBackupStorageVO_.datastore, morval)
                    .select(VCenterBackupStorageVO_.uuid)
                    .findValue();
            if (bsUuid != null) {
                bsvo.setUuid(bsUuid);
            } else {
                bsvo.setUuid(Platform.getUuid());
            }
            bsvo.setvCenterUuid(vcvo.getUuid());
            bsvo.setState(BackupStorageState.Enabled);
            bsvo.setStatus(BackupStorageStatus.Connected);
            bsvo.setName(ds.getName());
            bsvo.setUrl(ds.getSummary().getUrl());
            bsvo.setDatastore(morval);
            bsvo.setType(VCENTER_BACKUP_STORAGE_TYPE);
            bsvo.setAvailableCapacity(ds.getSummary().getFreeSpace());
            bsvo.setTotalCapacity(ds.getSummary().getCapacity());
            bsvos.put(morval, bsvo);

            BackupStorageZoneRefVO rvo = new BackupStorageZoneRefVO();
            rvo.setBackupStorageUuid(bsvo.getUuid());
            rvo.setZoneUuid(vcvo.getZoneUuid());
            bsZoneRefs.add(rvo);

            final VCenterPrimaryStorageVO psvo = new VCenterPrimaryStorageVO();
            final String psUuid = Q.New(VCenterPrimaryStorageVO.class)
                    .eq(VCenterPrimaryStorageVO_.vCenterUuid, vcvo.getUuid())
                    .eq(VCenterPrimaryStorageVO_.datastore, morval)
                    .select(VCenterPrimaryStorageVO_.uuid)
                    .findValue();
            if (psUuid != null) {
                psvo.setUuid(psUuid);
            } else {
                psvo.setUuid(Platform.getUuid());
            }
            psvo.setvCenterUuid(vcvo.getUuid());
            psvo.setState(PrimaryStorageState.Enabled);
            psvo.setStatus(PrimaryStorageStatus.Connected);
            psvo.setName(ds.getName());
            psvo.setUrl(ds.getSummary().getUrl());
            psvo.setDatastore(morval);
            psvo.setType(VCENTER_PRIMARY_STORAGE_TYPE);
            psvo.setMountPath(VCENTER_PRIMARY_STORAGE_TYPE);
            psvo.setZoneUuid(vcvo.getZoneUuid());
            psvos.put(morval, psvo);

            final PrimaryStorageCapacityVO capvo = new PrimaryStorageCapacityVO();
            capvo.setUuid(psvo.getUuid());
            capvo.setAvailablePhysicalCapacity(ds.getSummary().getFreeSpace());
            capvo.setAvailableCapacity(ds.getSummary().getFreeSpace());
            capvo.setTotalPhysicalCapacity(ds.getSummary().getCapacity());
            capvo.setTotalCapacity(ds.getSummary().getCapacity());
            psCapVOs.add(capvo);
        }
    }

    private VmNicVO buildNicVO(VirtualEthernetCard virtualEthernetCard, String vmUuid, String networkName, int idx, String macAddr, VirtualMachine vm) {
        return new SQLBatchWithReturn<VmNicVO>() {
            @Override
            protected VmNicVO scripts() {
                VmNicVO nicvo = q(VmNicVO.class)
                        .eq(VmNicVO_.mac, macAddr)
                        .eq(VmNicVO_.vmInstanceUuid, vmUuid)
                        .find();
                if (nicvo != null) {
                    nicvo.setHypervisorType(ESXConstant.VMWARE_HYPERVISOR_TYPE);
                    return nicvo;
                }

                nicvo = new VmNicVO();
                nicvo.setUuid(Platform.getUuid());
                nicvo.setVmInstanceUuid(vmUuid);
                nicvo.setMac(macAddr);
                nicvo.setDeviceId(idx);
                nicvo.setInternalName(String.format("%s.%d", networkName, idx));
                nicvo.setHypervisorType(ESXConstant.VMWARE_HYPERVISOR_TYPE);
                nicvo.setIpVersion(IPv6Constants.IPv4);
                if (virtualEthernetCard == null) {
                    nicvo.setDriverType(VMwareHelper.getVmImagePlatform(vm).isParaVirtualization() ?
                            nicManager.getDefaultPVNicDriver() : nicManager.getDefaultNicDriver());
                } else {
                    VirtualEthernetCardFactory factory = new VirtualEthernetCardFactory();
                    nicvo.setDriverType(factory.getDriverTypeByVirtualEthernetCardType(virtualEthernetCard));
                }
                nicvo.setType(VmInstanceConstant.VIRTUAL_NIC_TYPE);
                return nicvo;
            }
        }.execute();
    }

    private static String getGuestPrimaryIpV4(VirtualMachine vm) {
        final String ip = vm.getGuest().getIpAddress();

        if (ip != null && !ip.contains(":")) {
            return ip;
        }

        return null;
    }

    static private String getDvpgName(VirtualMachine vm, String pgKey) {
        try {
            ManagedEntity nw = VMwareHelper.getDVPG(vm.getServerConnection(), pgKey);
            return nw.getName();
        } catch (Throwable th) {
            logger.warn(String.format("[vc] virtual machine %s has an invalid portgroup: %s", vm.getName(), pgKey));
            return String.format("%s-%s", vm.getName(), pgKey);
        }
    }

    private static Map<String, String> getVmNicMacIpMap(VirtualMachine vm) {
        Map<String, String> res = new HashMap<>();
        GuestNicInfo[] nicInfoList = vm.getGuest().getNet();
        if (nicInfoList != null) {
            Arrays.stream(nicInfoList).forEach(info -> {
                String[] ips = info.ipAddress;
                if (ips != null) {
                    ips = Arrays.stream(ips)
                            .filter(ip -> !ip.contains(":") && !ip.startsWith("169.254"))
                            .toArray(String[]::new);
                    if (ips.length > 0) {
                        res.putIfAbsent(info.macAddress, ips[0]);
                    }
                }
            });
        }
        return res;
    }

    /**
     * Get the NicVOs (their L3 networks are empty) for a virtual machine.
     *
     * @param vmUuid the VM instance UUID
     * @param vm     the VM managed entity
     * @return An Object contains a map from L2 network name to a NicVO and primary nic uuid
     */
    private VmNicsResult getNicVOs(String vmUuid, VirtualMachine vm) {
        VmNicsResult res = new VmNicsResult();
        Map<String, VmNicVO> vos = new LinkedHashMap<>();
        res.setNics(vos);
        String primaryIpV4 = getGuestPrimaryIpV4(vm);
        Map<String, String> macIpMap = getVmNicMacIpMap(vm);
        int idx = 0;
        for (VirtualDevice dev : vm.getConfig().getHardware().getDevice()) {
            if (!(dev instanceof VirtualEthernetCard)) {
                continue;
            }

            VirtualEthernetCard vmxNet = (VirtualEthernetCard) dev;
            String mac = vmxNet.getMacAddress();
            if (StringUtils.isEmpty(mac)) {
                logger.warn(String.format("[vc] vm[%s]'s nic [id: %s] has no mac address", vm.getName(), vmxNet.getExternalId()));
                continue;
            }

            String ip = macIpMap.get(mac);
            String l2Name = null;

            if (dev.getBacking() instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo) {
                VirtualEthernetCardDistributedVirtualPortBackingInfo back =
                        (VirtualEthernetCardDistributedVirtualPortBackingInfo) dev.getBacking();
                l2Name = getDvpgName(vm, back.getPort().getPortgroupKey());
            } else if (dev.getBacking() instanceof VirtualEthernetCardNetworkBackingInfo) {
                VirtualEthernetCardNetworkBackingInfo back =
                        (VirtualEthernetCardNetworkBackingInfo) dev.getBacking();
                l2Name = back.getDeviceName();
            }

            if (l2Name == null) {
                logger.trace(String.format("[vc] vm[%s]'s nic [id: %s] has no device name", vm.getName(), vmxNet.getExternalId()));
                continue;
            }

            VmNicVO nicvo = buildNicVO(vmxNet, vmUuid, l2Name, idx++, mac, vm);
            if (StringUtils.isNotEmpty(ip)) {
                nicvo.setIp(ip);
                if (StringUtils.equals(ip, primaryIpV4)) {
                    res.setPrimaryNicUuid(nicvo.getUuid());
                }
            }
            vos.put(l2Name, nicvo);
        }

        return res;
    }

    private NicVmFilteredResult fixVmNicInfo(final Map<String, Map<String, String>> l3dict,
                                             final List<VmInstanceVO> vms,
                                             final Map<String, VmNicsResult> vmNics) {
        Map<String, VmNicVO> macTbl = new HashMap<>();
        Map<String, VmInstanceVO> vmvos = new HashMap<>();
        Map<String, VmNicVO> nicRes = new HashMap<>();

        for (VmInstanceVO vmvo : vms) {
            vmvos.put(vmvo.getUuid(), vmvo);
        }

        for (Map.Entry<String, VmNicsResult> entry : vmNics.entrySet()) {
            final String vmUuid = entry.getKey();
            int cnt = 0;
            final VmNicsResult nics = entry.getValue();
            final String primaryNicUuid = nics.getPrimaryNicUuid();
            for (Map.Entry<String, VmNicVO> vmnic : nics.getNics().entrySet()) {
                final String l2name = vmnic.getKey();
                final VmNicVO nicvo = vmnic.getValue();
                final Map<String, String> pgL3Dict = l3dict.getOrDefault(vmvos.get(vmUuid).getClusterUuid(), null);
                final String l3uuid = pgL3Dict != null ? pgL3Dict.get(l2name) : null;
                if (l3uuid == null) {
                    logger.warn(String.format("[vc] l2network[%s] has no corresponding L3", l2name));
                    continue;
                }

                nicvo.setL3NetworkUuid(l3uuid);
                macTbl.put(nicvo.getMac(), nicvo);
                nicRes.put(nicvo.getUuid(), nicvo);

                if (cnt == 0 || (primaryNicUuid != null && primaryNicUuid.equals(nicvo.getUuid()))) {
                    vmvos.computeIfPresent(vmUuid, (key, vmvo) -> {
                        vmvo.setDefaultL3NetworkUuid(nicvo.getL3NetworkUuid());
                        return vmvo;
                    });
                }
                cnt += 1;
            }
        }

        NicVmFilteredResult res = new NicVmFilteredResult();
        res.setVms(new ArrayList<>(vmvos.values()));
        res.setVmNics(nicRes.values().stream()
                .filter(nic -> nic.getVmInstanceUuid() == null || vmvos.containsKey(nic.getVmInstanceUuid()))
                .collect(Collectors.toList()));
        return res;
    }

    private String getCollisionMsg(String vmUuid, String macAddr,
                                   Collection<VmNicVO> nics, List<VmInstanceVO> vms) {
        String msg = String.format("MAC[%s] collision with VM[uuid: %s]", macAddr, vmUuid);

        Optional<VmNicVO> nicvo = nics.stream().filter(n -> n.getMac().equals(macAddr)).findFirst();
        if (!nicvo.isPresent()) {
            return msg;
        }

        Optional<VmInstanceVO> vmvo = vms.stream().filter(v -> v.getUuid().equals(nicvo.get().getVmInstanceUuid())).findFirst();
        if (!vmvo.isPresent()) {
            return msg;
        }

        return String.format("VM[name: %s, mac: %s] collides with VM[uuid: %s]",
                vmvo.get().getName(), macAddr, vmUuid);
    }

    private VCenterResourcePoolVO getVCenterResourcePool(ResourcePool rp, String vCenterClusterUuid, String parentUuid) {
        VCenterResourcePoolVO vo = Q.New(VCenterResourcePoolVO.class).eq(VCenterResourcePoolVO_.vCenterClusterUuid, vCenterClusterUuid)
                .eq(VCenterResourcePoolVO_.morVal, rp.getMOR().val).find();
        if (vo == null) {
            vo = new VCenterResourcePoolVO();
        }

        ResourceConfigSpec spec = rp.getConfig();
        ResourceAllocationInfo cpu = spec.getCpuAllocation();
        ResourceAllocationInfo memory = spec.getMemoryAllocation();

        String uuid = VMwareHelper.getZstackUuidFromManagedEntity(rp, VMwareHelper.customFieldResourcePoolUuid);
        if (uuid == null) {
            uuid = Platform.getUuid();
            VMwareHelper.setZstackUuidToManagedEntity(rp, VMwareHelper.customFieldResourcePoolUuid, uuid);
        }

        vo.setUuid(uuid);
        vo.setMorVal(rp.getMOR().val);
        vo.setName(rp.getName());
        vo.setvCenterClusterUuid(vCenterClusterUuid);
        vo.setParentUuid(parentUuid);
        vo.setCPULevel(cpu.shares.level.toString());
        vo.setCPUShares(cpu.shares.shares);
        vo.setCPULimit(VMwareHelper.vCenterResourcePoolCpuUnitToZStack(cpu.limit));
        vo.setCPUReservation(VMwareHelper.vCenterResourcePoolCpuUnitToZStack(cpu.reservation));
        if (cpu.overheadLimit != null) {
            vo.setCPUOverheadLimit(VMwareHelper.vCenterResourcePoolCpuUnitToZStack(cpu.overheadLimit));
        }
        vo.setMemoryLevel(memory.shares.level.toString());
        vo.setMemoryShares(memory.shares.shares);
        vo.setMemoryLimit(VMwareHelper.vCenterResourcePoolMemoryUnitToZStack(memory.limit));
        vo.setMemoryReservation(VMwareHelper.vCenterResourcePoolMemoryUnitToZStack(memory.reservation));
        if (memory.overheadLimit != null) {
            vo.setMemoryOverheadLimit(VMwareHelper.vCenterResourcePoolMemoryUnitToZStack(memory.overheadLimit));
        }

        return vo;
    }

    private VCenterResourcePoolUsageVO getVCenterResourcePoolUsage(String parentUuid, VCenterResourcePoolVO child) {
        VCenterResourcePoolUsageVO vo = Q.New(VCenterResourcePoolUsageVO.class).eq(VCenterResourcePoolUsageVO_.vCenterResourcePoolUuid, parentUuid)
                .eq(VCenterResourcePoolUsageVO_.resourceUuid, child.getUuid()).find();
        if (vo == null) {
            vo = new VCenterResourcePoolUsageVO();
            vo.setUuid(Platform.getUuid());
        }

        vo.setvCenterResourcePoolUuid(parentUuid);
        vo.setResourceUuid(child.getUuid());
        vo.setResourceType(VCenterResourcePoolVO.class.getSimpleName());
        vo.setResourceName(child.getName());
        return vo;
    }

    private List<VCenterResourcePoolVO> scanResourcePool(ResourcePool rp, String vCenterClusterUuid,
                                                         String parentUuid, List<VCenterResourcePoolUsageVO> usages) {
        List<VCenterResourcePoolVO> res = new ArrayList<>();

        VCenterResourcePoolVO vo = getVCenterResourcePool(rp, vCenterClusterUuid, parentUuid);
        if (parentUuid != null) {
            VCenterResourcePoolUsageVO usage = getVCenterResourcePoolUsage(parentUuid, vo);
            usages.add(usage);
        }

        res.add(vo);
        try {
            ResourcePool[] rps = rp.getResourcePools();
            for (ResourcePool item : rps) {
                res.addAll(scanResourcePool(item, vCenterClusterUuid, vo.getUuid(), usages));
            }
        } catch (Exception ignored) {

        }

        return res;
    }

    private VCenterResourcePoolUsageVO getVCenterResourcePoolUsage(VCenterResourcePoolVO rp, VmInstanceVO ivo) {
        VCenterResourcePoolUsageVO vo = Q.New(VCenterResourcePoolUsageVO.class).eq(VCenterResourcePoolUsageVO_.vCenterResourcePoolUuid, rp.getUuid())
                .eq(VCenterResourcePoolUsageVO_.resourceUuid, ivo.getUuid()).find();
        if (vo == null) {
            vo = new VCenterResourcePoolUsageVO();
            vo.setUuid(Platform.getUuid());
        }

        vo.setvCenterResourcePoolUuid(rp.getUuid());
        vo.setResourceUuid(ivo.getUuid());
        vo.setResourceType(VmInstanceVO.class.getSimpleName());
        vo.setResourceName(ivo.getName());
        return vo;
    }

    private VCenterResourcePoolUsageVO getVCenterResourcePoolUsage(final List<VCenterResourcePoolVO> resourcePools, VirtualMachine vm, VmInstanceVO ivo) {
        ResourcePool vmVp;
        try {
            vmVp = vm.getResourcePool();
        } catch (Exception e) {
            return null;
        }

        VCenterResourcePoolVO vo = null;
        for (VCenterResourcePoolVO rp : resourcePools) {
            if (rp.getMorVal().equals(vmVp.getMOR().val)) {
                vo = rp;
                break;
            }
        }

        if (vo == null) {
            return null;
        }

        return getVCenterResourcePoolUsage(vo, ivo);
    }

    // Import Cluster Compute Resources, ESX hosts and VM templates.
    private ResourceScanResult scanVCenterResource(ServiceInstance si, final VCenterVO vcvo, List<String> vcvms)
            throws RemoteException {


        final List<Datacenter> dataCenters = VMwareHelper.getVmwareHelper().getDataCenters(si);

        // From each cluster, we found its hosts, in which we found its VMs.
        final List<VCenterDatacenterVO> dcs = new ArrayList<>();
        final List<VCenterClusterVO> cvos = new ArrayList<>();
        final List<ESXHostVO> hvos = new ArrayList<>();
        final List<HostCapacityVO> hcvos = new ArrayList<>();
        final List<VmInstanceVO> vms = new ArrayList<>();
        final List<VolumeVO> volvos = new ArrayList<>();
        final List<ImageVO> templates = new ArrayList<>();
        final List<VSwitchNetwork> vSwitchNetworks = new ArrayList<>();
        final List<VCenterResourcePoolVO> resourcePools = new ArrayList<>();
        final List<VCenterResourcePoolUsageVO> resourcePoolUsages = new ArrayList<>();

        final List<ImageBackupStorageRefVO> imageRefs = new ArrayList<>();
        final List<PrimaryStorageClusterRefVO> psClusterRefs = new ArrayList<>();
        final Map<String, List<String>> hostExtraIps = new HashMap<>();

        //A map from host uuid to its currently datetime
        final Map<String, Date> hostTime = new HashMap<>();

        // A map from VM instance uuid to its NICs
        final Map<String, VmNicsResult> vmNics = new HashMap<>();

        // Existing dvPGs listed from VCenter (with dvPG's morVal as its key).
        // We will convert them into L2NetworkVO
        final Map<String, DistributedVirtualPortgroup> dvPGs = new HashMap<>();

        // A map from cluster UUID to its associated dvPGs.
        // So that we know the relationship between cluster and its L2 networks.
        final Map<String, Map<String, DistributedVirtualPortgroup>> l2networks = new HashMap<>();

        // The storage usages seen by this vCenter
        final Map<String, Datastore> stores = VMwareHelper.getVmwareHelper().listDataStores(si);
        // Data-store MorVal -> VCenterPrimaryStorageVO
        final Map<String, VCenterPrimaryStorageVO> psvos = new HashMap<>();
        final List<PrimaryStorageCapacityVO> psCapVOs = new ArrayList<>();
        final Map<String, VCenterBackupStorageVO> bsvos = new HashMap<>();
        final List<BackupStorageZoneRefVO> bsZoneRefs = new ArrayList<>();
        prepareStorageVOs(stores, vcvo, psvos, psCapVOs, bsvos, bsZoneRefs);

        for (Datacenter dc : dataCenters) {
            final VCenterDatacenterVO datacenterVO = new VCenterDatacenterVO();
            final String dataCenterUuid = Q.New(VCenterDatacenterVO.class)
                    .select(VCenterDatacenterVO_.uuid)
                    .eq(VCenterDatacenterVO_.vCenterUuid, vcvo.getUuid())
                    .eq(VCenterDatacenterVO_.morval, dc.getMOR().getVal())
                    .findValue();
            datacenterVO.setName(dc.getName());
            datacenterVO.setMorval(dc.getMOR().getVal());
            datacenterVO.setvCenterUuid(vcvo.getUuid());
            if (dataCenterUuid != null) {
                datacenterVO.setUuid(dataCenterUuid);
            } else {
                datacenterVO.setUuid(Platform.getUuid());
            }
            dcs.add(datacenterVO);

            List<ClusterComputeResource> clusterComputeResources = Arrays.stream(dc.getHostFolder().getChildEntity())
                    .filter(v -> v instanceof ClusterComputeResource)
                    .map(v -> (ClusterComputeResource) v)
                    .collect(Collectors.toList());

            for (ClusterComputeResource res : clusterComputeResources) {
                final VCenterClusterVO cvo = new VCenterClusterVO();
                cvo.setMorval(res.getMOR().getVal());
                cvo.setvCenterUuid(vcvo.getUuid());
                cvo.setDataCenterUuid(datacenterVO.getUuid());
                cvo.setZoneUuid(vcvo.getZoneUuid());
                final String clusterUuid = Q.New(VCenterClusterVO.class)
                        .eq(VCenterClusterVO_.vCenterUuid, vcvo.getUuid())
                        .eq(VCenterClusterVO_.morval, cvo.getMorval())
                        .select(VCenterClusterVO_.uuid)
                        .findValue();
                if (clusterUuid != null) {
                    cvo.setUuid(clusterUuid);
                } else {
                    cvo.setUuid(Platform.getUuid());
                }

                cvo.setName(res.getName());
                cvo.setDescription("from VCenter " + vcvo.getUuid());
                cvo.setHypervisorType(ESXConstant.VMWARE_HYPERVISOR_TYPE);
                cvo.setType(VCENTER_CLUSTER_TYPE);
                cvo.setState(ClusterState.Enabled);

                ResourcePool rootRp = res.getResourcePool();
                ResourcePool[] vcRps = rootRp.getResourcePools();
                for (ResourcePool rp : vcRps) {
                    List<VCenterResourcePoolVO> rps = scanResourcePool(rp, cvo.getUuid(), null, resourcePoolUsages);
                    resourcePools.addAll(rps);
                }
                logger.debug(String.format("[vc] scanResourcePool for cluster[%s]", res.getName()));

                // create an primary storage cluster reference VO
                psClusterRefs.addAll(buildPsClusterRef(res.getDatastores(), cvo.getUuid(), psvos));

                // list VirtualSwitchPortGroups for each cluster
                Map<String, DistributedVirtualPortgroup> clusterDVPGs = getClusterDVPGs(res);
                l2networks.put(cvo.getUuid(), clusterDVPGs);

                for (Map.Entry<String, DistributedVirtualPortgroup> entry : clusterDVPGs.entrySet()) {
                    dvPGs.put(entry.getKey(), entry.getValue());
                }

                // host.parent => ComputeResource (ClusterComputeResource)
                // host.parent.parent => Folder
                // host.parent.parent.parent => DataCenter
                final Map<String, Collection<VSwitchInfo>> hostVSwitchDict = new HashMap<>();
                boolean hasUnqualifiedHost = false;

                for (HostSystem host : res.getHosts()) {
                    final boolean hostHasDVPG = networksHaveDVPG(host.getNetworks());
                    final List<HostVirtualSwitch> vswitches = VMwareHelper.getVmwareHelper().getHostVirtualSwitches(host);
                    logger.debug(String.format("[vc] get virtual switches for host[%s]", host.getName()));
                    if (!hostHasDVPG && vswitches.isEmpty()) {
                        hasUnqualifiedHost = true;
                        logger.warn("[vc] no qualified VM networks found for host: " + host.getName());
                        continue;
                    }

                    ESXHostVO hvo = new ESXHostVO();
                    hvo.setvCenterUuid(cvo.getvCenterUuid());
                    hvo.setMorval(host.getMOR().getVal());
                    hvo.setEsxiVersion(VMwareHelper.getESXIVersion(host));
                    final String hostUuid = Q.New(ESXHostVO.class)
                            .eq(ESXHostVO_.vCenterUuid, vcvo.getUuid())
                            .eq(ESXHostVO_.morval, hvo.getMorval())
                            .select(ESXHostVO_.uuid)
                            .findValue();
                    if (hostUuid != null) {
                        hvo.setUuid(hostUuid);
                    } else {
                        hvo.setUuid(Platform.getUuid());
                    }

                    hvo.setName(host.getName());
                    hvo.setDescription(String.format("from VCenter (%s)", vcvo.getUuid()));

                    hvo.setClusterUuid(cvo.getUuid());
                    hvo.setHypervisorType(ESXConstant.VMWARE_HYPERVISOR_TYPE);
                    hvo.setZoneUuid(vcvo.getZoneUuid());
                    hvo.setArchitecture(ImageArchitecture.x86_64.toString());

                    hvo.setState(HostState.Enabled);
                    if (VMwareHelper.getVmwareHelper().isHostConnected(host)) {
                        hvo.setStatus(HostStatus.Connected);
                    } else {
                        hvo.setStatus(HostStatus.Disconnected);
                    }

                    try {
                        hvo.setManagementIp(getHostSystemManagementIp(host, hvo.getUuid(), hostExtraIps));
                    } catch (Exception e) {
                        hvo.setManagementIp("");
                    }

                    HostCapacityVO hcvo = getHostCapacity(host);
                    hcvo.setUuid(hvo.getUuid());
                    hcvos.add(hcvo);

                    if (hvo.getStatus().equals(HostStatus.Connected)) {
                        //unify the datetime
                        TimeZone mnTimeZone = Calendar.getInstance().getTimeZone();
                        Calendar hostCal = host.getHostDateTimeSystem().queryDateTime();
                        hostCal.setTimeZone(mnTimeZone);
                        hostTime.put(hvo.getUuid(), hostCal.getTime());
                    }


                    if (!vswitches.isEmpty()) {
                        hostVSwitchDict.put(
                                hvo.getUuid(),
                                VMwareHelper.getVmwareHelper().listHostVMNetworkVSwitches(
                                        si.getServerConnection(),
                                        hvo.getMorval()
                                ).values());
                    }

                    for (VirtualMachine vm : host.getVms()) {
                        if (vm.getConfig() == null) {
                            logger.warn("VM has no configure: " + vm.getName());
                            continue;
                        }
                        logger.debug(String.format("[vc] sync configs for vm[%s]", vm.getName()));

                        if (vm.getConfig().isTemplate()) {
                            logger.trace(String.format("[vc] vm[%s] is template", vm.getName()));
                            ImageVO imageVO = VMwareHelper.getImageVO(vm);
                            templates.add(imageVO);

                            ImageBackupStorageRefVO imgref = new ImageBackupStorageRefVO();
                            VCenterBackupStorageVO bsvo = bsvos.get(VMwareHelper.getVmDatastoreMorVal(vm));
                            imgref.setBackupStorageUuid(bsvo.getUuid());
                            imgref.setImageUuid(imageVO.getUuid());
                            imgref.setInstallPath(imageVO.getUrl());
                            imgref.setStatus(ImageStatus.Ready);
                            imageRefs.add(imgref);
                        } else {
                            // do not import vCenter Vms and vCls Vms
                            if (vcvms.contains(vm.getMOR().getVal()) || VMwareHelper.isClusterAgentVm(vm)) {
                                logger.trace(String.format("[vc] skip vCenter vm or vCls Vm, vm[%s]", vm.getName()));
                                continue;
                            }

                            final VmInstanceVO ivo = getVmInstanceVO(vm, vms);
                            ivo.setZoneUuid(vcvo.getZoneUuid());
                            ivo.setClusterUuid(cvo.getUuid());
                            ivo.setHostUuid(hvo.getUuid());
                            ivo.setLastHostUuid(hvo.getUuid());
                            List<VolumeVO> dataVolumes = getAllVolumes(psvos, vm, ivo);
                            if (dataVolumes.isEmpty()) {
                                /* skip the vm without disks */
                                logger.trace(String.format("[vc] skip vm[%s] without any volumes", vm.getName()));
                                continue;
                            }
                            // TODO setImageUuid()
                            vms.add(ivo);

                            VCenterResourcePoolUsageVO usageVO = getVCenterResourcePoolUsage(resourcePools, vm, ivo);
                            if (usageVO != null) {
                                resourcePoolUsages.add(usageVO);
                            }

                            // Create VmNicVO with from all possible nic devices
                            final VmNicsResult nicvos = getNicVOs(ivo.getUuid(), vm);
                            vmNics.put(ivo.getUuid(), nicvos);

                            dataVolumes.forEach(v -> {
                                if (v.getType() == VolumeType.Root) {
                                    ivo.setRootVolumeUuid(v.getUuid());
                                }
                            });

                            volvos.addAll(dataVolumes);
                        }
                    }

                    hvos.add(hvo);
                }

                cvos.add(cvo);
                if (!hasUnqualifiedHost) {
                    VSwitchNetwork nw = buildVSwitchNetwork(cvo, hostVSwitchDict);
                    if (nw != null) {
                        vSwitchNetworks.add(nw);
                    }
                }
            }
        }


        // TODO find other hosts that are not in any cluster

        ResourceScanResult res = new ResourceScanResult();
        res.setDateCenters(dcs);
        res.setClusters(cvos);
        res.setHosts(hvos);
        res.setHostCapacities(hcvos);
        res.setVmInstances(vms);
        res.setVolumes(volvos);
        res.setImages(templates);
        res.setImageRefs(imageRefs);
        res.setPsClusterRefs(psClusterRefs);
        res.setVmNics(vmNics);
        res.setDvPGs(dvPGs);
        res.setL2Networks(l2networks);
        res.setVSwitchNetworks(vSwitchNetworks);
        res.setBackupStorages(bsvos.values());
        res.setPrimaryStorages(psvos.values());
        res.setBackupStorageZoneRefs(bsZoneRefs);
        res.setPrimaryStorageCapacities(psCapVOs);
        res.setHostTime(hostTime);
        res.setRps(resourcePools);
        res.setRpUsages(resourcePoolUsages);
        res.setHostExtraIps(hostExtraIps);
        res.setVersion(si.getAboutInfo().getVersion());
        return res;
    }

    private VSwitchNetwork buildVSwitchNetwork(VCenterClusterVO cvo,
                                               final Map<String, Collection<VSwitchInfo>> hostVSwitchDict) {
        if (hostVSwitchDict.values().stream().anyMatch(Collection::isEmpty)) {
            logger.info("[vc] some host has no vSwitch in cluster: " + cvo.getName());
            return null;
        }

        // A set initially contains vSwitches from all hosts, for example:
        // vSwitches = [a, b, c, d]
        Set<String> vSWitches = new HashSet<>();
        for (Collection<VSwitchInfo> vss : hostVSwitchDict.values()) {
            for (VSwitchInfo info : vss) {
                vSWitches.add(info.getName());
            }
        }

        for (Map.Entry<String, Collection<VSwitchInfo>> entry : hostVSwitchDict.entrySet()) {
            // The vSwitches for current host, might be something like:
            //  HostA = [ b, c ]
            //  HostB = [ a, b ]
            //  HostC = [ b, d ]
            Set<String> hostVSwitches = entry.getValue().stream()
                    .map(VSwitchInfo::getName)
                    .collect(Collectors.toSet());

            // find intersection of 'vSWitches' & 'hostVSwitches'
            //   [a, b, c, d ] `intersec` [ b, c ] => [ b, c ]
            //        [ b, c ] `intersec` [ a, b ] => [ b ]
            //           [ b ] `intersec` [ b, d ] => [ b ]
            vSWitches = vSWitches.stream()
                    .filter(it -> hostVSwitches.stream().anyMatch(it::equalsIgnoreCase))
                    .collect(Collectors.toSet());
        }

        if (vSWitches.isEmpty()) {
            logger.info("[vc] no vSwitches with the same name found in cluster: " + cvo.getName());
            return null;
        }

        logger.info(String.format("[vc] found the following common vSwitches: %s, for cluster %s",
                StringUtils.join(vSWitches, ","), cvo.getName()));

        // Using the same method as above, we can find the common port-groups.
        // But now, we use HashMap (so to record its vlanId).
        // vSwitch name -> portgroup name -> [ portgroup info ]
        Map<String, Map<String, List<PortGroupInfo>>> portgroups = new HashMap<>();

        // port-group with same Network Label but different vlanId
        // vSwtich name -> [ pg name ]
        Map<String, Set<String>> portgroupBlockList = new HashMap<>();

        for (Collection<VSwitchInfo> vss : hostVSwitchDict.values()) {
            for (VSwitchInfo info : vss) {
                final String vSwitchName = info.getName();
                if (!vSWitches.contains(vSwitchName)) {
                    continue;
                }

                Map<String, List<PortGroupInfo>> pgInfoDict;
                if (portgroups.keySet().stream().anyMatch(vSwitchName::equalsIgnoreCase)) {
                    pgInfoDict = getValueByKeyIgnoreCase(portgroups, vSwitchName, new HashMap<>());
                } else {
                    pgInfoDict = new HashMap<>();
                    portgroups.put(vSwitchName, pgInfoDict);
                }

                Set<String> blockList;
                if (portgroupBlockList.keySet().stream().anyMatch(vSwitchName::equalsIgnoreCase)) {
                    blockList = getValueByKeyIgnoreCase(portgroupBlockList, vSwitchName, new HashSet<>());
                } else {
                    blockList = new HashSet<>();
                    portgroupBlockList.put(vSwitchName, blockList);
                }

                for (PortGroupInfo pgInfo : info.getPortGroups()) {
                    String pgName = pgInfo.getName();
                    if (blockList.contains(pgName)) {
                        continue;
                    }

                    if (pgInfoDict.keySet().stream().anyMatch(pgName::equalsIgnoreCase)) {
                        List<PortGroupInfo> saved = getValueByKeyIgnoreCase(pgInfoDict, pgName, new ArrayList<>());
                        if (saved.stream().allMatch(x -> x.getVlanId() == pgInfo.getVlanId())) {
                            // the size of 'saved' will be counted later
                            saved.add(pgInfo);
                        } else {
                            logger.info(String.format("[vc] portgroup[%s] has different vlanId across vSwitch[%s]", pgName, vSwitchName));
                            blockList.add(pgName);
                            pgInfoDict.remove(pgName);
                        }
                    } else {
                        List<PortGroupInfo> item = new ArrayList<>();
                        item.add(pgInfo);
                        pgInfoDict.put(pgName, item);
                    }
                }
            }
        }

        // search portgroups for the qualified
        VSwitchNetwork vsn = new VSwitchNetwork();
        vsn.setClusterUuid(cvo.getUuid());
        vsn.setHostUuids(new ArrayList<>(hostVSwitchDict.keySet()));

        Map<String, List<PortGroupInfo>> finalPortGroups = new HashMap<>();
        for (Map.Entry<String, Map<String, List<PortGroupInfo>>> entry : portgroups.entrySet()) {
            String vSwitchName = entry.getKey();
            List<PortGroupInfo> pgList;

            if (finalPortGroups.containsKey(vSwitchName)) {
                pgList = finalPortGroups.get(vSwitchName);
            } else {
                pgList = new ArrayList<>();
                finalPortGroups.put(vSwitchName, pgList);
            }

            for (List<PortGroupInfo> collected : entry.getValue().values()) {
                if (collected.size() != vsn.getHostUuids().size()) {
                    continue;
                }

                pgList.add(collected.get(0));
            }

            finalPortGroups.put(vSwitchName, pgList);
        }

        vsn.setPortGroups(finalPortGroups);
        return vsn;
    }

    private static <T> T getValueByKeyIgnoreCase(Map<String, T> map, String key, T defaultValue) {
        List<String> res = map.keySet().stream().filter(key::equalsIgnoreCase).collect(Collectors.toList());
        return res.isEmpty() ? defaultValue : map.get(res.get(0));
    }

    private List<PrimaryStorageClusterRefVO> buildPsClusterRef(Datastore[] dss,
                                                               String clusterUuid,
                                                               final Map<String, VCenterPrimaryStorageVO> psvos) {
        List<PrimaryStorageClusterRefVO> res = new ArrayList<>();
        if (dss == null) {
            return res;
        }

        for (Datastore ds : dss) {
            final PrimaryStorageClusterRefVO pscfvo = new PrimaryStorageClusterRefVO();
            final String key = ds.getMOR().getVal();
            pscfvo.setClusterUuid(clusterUuid);
            pscfvo.setPrimaryStorageUuid(psvos.get(key).getUuid());
            res.add(pscfvo);
        }

        return res;
    }

    private void doConnectHosts(List<ESXHostVO> hvos) {
        List<ConnectHostMsg> msgs = hvos.stream().map(hvo -> {
            ConnectHostMsg msg = new ConnectHostMsg(hvo.getUuid());
            msg.setNewAdd(true);
            msg.setStartPingTaskOnFailure(false);
            bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hvo.getUuid());
            return msg;
        }).collect(Collectors.toList());

        bus.send(msgs);
    }

    private FlowChain getDeletionFlowChain(final VCenterVO vcvo, APIDeleteMessage.DeletionMode mode, String issuer, Object ctx) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-vCenter-%s", vcvo.getUuid()));
        if (mode == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }

        return chain;
    }

    private void syncVCenterVO(VCenterVO vo, ResourceScanResult res) {
        boolean isVCenterChanged = false;

        if (vo.getVersion() == null || !vo.getVersion().equals(res.getVersion())) {
            vo.setVersion(res.getVersion());
            isVCenterChanged = true;
        }

        if (isVCenterChanged) {
            dbf.update(vo);
        }
    }

    private void syncDataCenter(VCenterVO vcvo, ResourceScanResult res) {
        final List<VCenterDatacenterVO> existingDataCenters = Q.New(VCenterDatacenterVO.class)
                .eq(VCenterDatacenterVO_.vCenterUuid, vcvo.getUuid())
                .list();

        // persist new dataCenter
        for (VCenterDatacenterVO cvo : res.getDateCenters()) {
            // for v2v
            Optional<VCenterDatacenterVO> optional = existingDataCenters.stream()
                    .filter(v -> v.getMorval().equalsIgnoreCase(cvo.getMorval())
                            && !v.getName().equals(cvo.getName()))
                    .findAny();
            if (optional.isPresent()) {
                VCenterDatacenterVO vo = optional.get();
                vo.setName(cvo.getName());
                dbf.update(vo);
                continue;
            }

            if (existingDataCenters.stream().noneMatch(c -> c.getMorval().equalsIgnoreCase(cvo.getMorval()))) {
                logger.info(String.format("[vc] got new dataCenter [%s:%s]", cvo.getName(), cvo.getUuid()));
                dbf.persist(cvo);
            }
        }

        //remove stale dataCenter
        List<String> staleDataCenters = new ArrayList<>();
        for (VCenterDatacenterVO cvo : existingDataCenters) {
            if (res.getDateCenters().stream().noneMatch(c -> c.getMorval().equalsIgnoreCase(cvo.getMorval()))) {
                staleDataCenters.add(cvo.getUuid());
            }
        }

        dbf.removeByPrimaryKeys(staleDataCenters, VCenterDatacenterVO.class);
    }

    // The database records in scan result have uuids that mis-match those
    // in ZStack db.  We need to fix that before persisting any changes.
    private void syncWithLatestResources(final VCenterVO vcvo, String acntUuid, ResourceScanResult res, VCenterServiceInstance si) {
        // In case we have left dirty records in local database... :(

        syncVCenterVO(vcvo, res);

        logger.info("[vc] sync vCenter DataCenters, number = " + res.getDateCenters().size());
        syncDataCenter(vcvo, res);

        logger.info("[vc] sync vCenter BS data-stores, number = " + res.getBackupStorages().size());
        syncBackupStorage(vcvo, res, si);

        logger.info("[vc] sync vCenter Cluster Resources, number = " + res.getClusters().size());
        ClusterSyncResult csres = syncClusters(vcvo, res);

        logger.info(String.format("cluster sync result: new %d, stale: %d",
                csres.getNewClusters().size(),
                csres.getStaleClustes().size()));

        logger.info("[vc] sync vCenter PS data-stores, number = " + res.getBackupStorages().size());
        syncPrimaryStorage(vcvo, csres, res);

        logger.info("[vc] sync virtual distributed port-groups, number = " + res.getDvPGs().size());
        // Map: <ClusterUuid, <PGName, l3Uuid>>
        final Map<String, Map<String, String>> l3dict = new HashMap<>();
        Set<String> dvpgKeys = saveNetworks(acntUuid, l3dict, res.getDvPGs(), res.getL2Networks(), vcvo);

        logger.info("[vc] sync ESX hosts, number = " + res.getHosts().size());
        List<String> hostsToBeDeleted = syncEsxHosts(vcvo, res);
        createHostSystemTags(res);

        logger.info("[vc] sync vSwitch, number = " + res.getVSwitchNetworks().size());
        saveVSwitchNetworks(acntUuid, l3dict, res.getVSwitchNetworks(), vcvo);
        NicVmFilteredResult nicVmInfo = fixVmNicInfo(l3dict, res.getVmInstances(), res.getVmNics());
        List<VmNicVO> nics = nicVmInfo.getVmNics();
        List<VmInstanceVO> vms = nicVmInfo.getVms();

        logger.info("[vc] sync volumes, number = " + res.getVolumes().size());
        Map<String, String> vmNewVolumeDict = syncVolumes(acntUuid, res, si, vms);


        logger.info("[vc] sync VM templates, number = " + res.getImages().size());
        syncVmTemplates(acntUuid, res);

        logger.info("[vc] sync virtual machines, number = " + vms.size());
        Set<String> volumesToDelete = syncVirtualMachines(acntUuid, vmNewVolumeDict, vms);

        logger.info("[vc] sync VM NICs, number = " + nics.size());
        syncVmNics(nics, acntUuid, vcvo, res);

        logger.info("[vc] sync vCenter Resource pool, number = " + res.getRps().size());
        syncResourcePools(vcvo, acntUuid, res);

        removeStaleNetworks(res.getVSwitchNetworks(), dvpgKeys, csres, vcvo.getUuid());
        removeStaleVMs(vcvo.getUuid(), hostsToBeDeleted, res.getVmInstances());
        deleteOldVolumes(volumesToDelete, vcvo.getUuid(), res.getVolumes(), si);
        removeStaleVMNics(vcvo.getUuid(), res.getVmNics());
    }

    private void createTagForNewVolume(Collection<String> collection, ServiceInstance si) {
        if (!collection.isEmpty()) {
            List<String> uuids = new ArrayList<>(collection);
            List<VolumeVO> volumes = Q.New(VolumeVO.class).eq(VolumeVO_.type, VolumeType.Data).in(VolumeVO_.uuid, uuids).listValues();
            createVolumeMappingTag(volumes, si);
        }
    }

    private void syncVmNics(List<VmNicVO> nics, String acntUuid, VCenterVO vcvo, ResourceScanResult res) {
        nics.forEach(n -> n.setAccountUuid(acntUuid));

        Map<Boolean, List<VmNicVO>> nicGroups = nics.stream().collect(Collectors.partitioningBy(v -> !Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, v.getVmInstanceUuid())
                .eq(VmNicVO_.mac, v.getMac())
                .isExists()));

        List<VmNicVO> newVmNics = nicGroups.get(true);
        List<VmNicVO> existNics = nicGroups.get(false);

        dbf.updateCollection(existNics);
        dbf.persistCollection(newVmNics);
        for (VmNicVO vo : nics) {
            UpdateNicIpIfNeeded(vo, vcvo.getUuid());
        }

        cleanUpVmNicDirtyData();
    }
    
    private void cleanUpVmNicDirtyData() {
        List<String> needRemoveVmNicUuids = SQL.New("select nic.uuid from VmNicVO nic, VmInstanceVO vm " +
                "where nic.vmInstanceUuid = vm.uuid and vm.hypervisorType = :hypervisorType " +
                "and nic.l3NetworkUuid not in (select uuid from L3NetworkVO)", String.class)
                .param("hypervisorType", ESXConstant.VMWARE_HYPERVISOR_TYPE)
                .list();

        if (!CollectionUtils.isEmpty(needRemoveVmNicUuids)) {
            dbf.removeByPrimaryKeys(needRemoveVmNicUuids, VmNicVO.class);
        }
    }

    private ClusterSyncResult syncClusters(VCenterVO vcvo, ResourceScanResult res) {
        final List<VCenterClusterVO> existingClusters = Q.New(VCenterClusterVO.class)
                .eq(VCenterClusterVO_.vCenterUuid, vcvo.getUuid())
                .list();

        // persist new clusters
        Set<String> newClusters = new HashSet<>();
        for (VCenterClusterVO cvo : res.getClusters()) {
            // for v2v
            Optional<VCenterClusterVO> optional = existingClusters.stream()
                    .filter(v -> v.getMorval().equalsIgnoreCase(cvo.getMorval())
                            && (!v.getName().equals(cvo.getName())
                            || !cvo.getDataCenterUuid().equals(v.getDataCenterUuid())))
                    .findAny();
            if (optional.isPresent()) {
                VCenterClusterVO vo = optional.get();
                vo.setName(cvo.getName());
                vo.setDataCenterUuid(cvo.getDataCenterUuid());
                dbf.update(vo);
                continue;
            }

            if (existingClusters.stream().noneMatch(c -> c.getMorval().equalsIgnoreCase(cvo.getMorval()))) {
                logger.info(String.format("[vc] got new cluster [%s:%s]", cvo.getName(), cvo.getUuid()));
                dbf.persist(cvo);
                newClusters.add(cvo.getUuid());
            }
        }

        // for each hosts, check whether its cluster changed
        updateHostCluster(res.getHosts(), vcvo.getUuid());

        // remove stale cluster records
        List<ClusterDeletionMsg> cdmsgs = new ArrayList<>();
        for (VCenterClusterVO cvo : existingClusters) {
            if (res.getClusters().stream().noneMatch(c -> c.getMorval().equalsIgnoreCase(cvo.getMorval()))) {
                ClusterDeletionMsg msg = new ClusterDeletionMsg();
                msg.setForceDelete(true);
                msg.setClusterUuid(cvo.getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, ClusterConstant.SERVICE_ID, cvo.getUuid());
                cdmsgs.add(msg);
            }
        }

        if (!cdmsgs.isEmpty()) {
            bus.call(cdmsgs);
            dbf.removeByPrimaryKeys(
                    cdmsgs.stream().map(ClusterDeletionMsg::getClusterUuid).collect(Collectors.toList()),
                    VCenterClusterVO.class);
        }

        ClusterSyncResult cres = new ClusterSyncResult();
        cres.setNewClusters(newClusters);
        cres.setStaleClustes(cdmsgs.stream().map(ClusterDeletionMsg::getClusterUuid).collect(Collectors.toSet()));
        return cres;
    }

    private void syncResourcePools(VCenterVO vcvo, String acntUuid, ResourceScanResult res) {
        final List<String> vCenterClusterUuids = Q.New(VCenterClusterVO.class).select(VCenterClusterVO_.uuid)
                .eq(VCenterClusterVO_.vCenterUuid, vcvo.getUuid()).listValues();
        final List<VCenterResourcePoolVO> existingResourcePools = Q.New(VCenterResourcePoolVO.class)
                .in(VCenterResourcePoolVO_.vCenterClusterUuid, vCenterClusterUuids).list();
        final List<VCenterResourcePoolUsageVO> existingResourcePoolUsages;
        if (!existingResourcePools.isEmpty()) {
            List<String> existingResourcePoolUuids = existingResourcePools.stream().map(ResourceVO::getUuid).collect(Collectors.toList());
            existingResourcePoolUsages = Q.New(VCenterResourcePoolUsageVO.class)
                    .in(VCenterResourcePoolUsageVO_.vCenterResourcePoolUuid, existingResourcePoolUuids).list();
        } else {
            existingResourcePoolUsages = new ArrayList<>();
        }

        // update resource pool records
        Set<VCenterResourcePoolVO> changedPools = new HashSet<>();
        for (VCenterResourcePoolVO vo : existingResourcePools) {
            for (VCenterResourcePoolVO vo1 : res.getRps()) {
                if (vo1.getUuid().equals(vo.getUuid()) && !vo1.equals(vo)) {
                    changedPools.add(vo.copyValue(vo1));
                    break;
                }
            }
        }

        if (!changedPools.isEmpty()) {
            dbf.updateCollection(changedPools);
        }

        // persist new resource pool usage
        Set<VCenterResourcePoolUsageVO> newResourcePoolUsages = new HashSet<>();
        for (VCenterResourcePoolUsageVO vo : res.getRpUsages()) {
            if (existingResourcePoolUsages.stream().noneMatch(c -> (c.getvCenterResourcePoolUuid().equals(vo.getvCenterResourcePoolUuid())
                    && c.getResourceUuid().equals(vo.getResourceUuid())))) {
                logger.info(String.format("[vc] got new Resource Pool Usage [pool uuid:%s], resource [type:%s, uuid: %s]",
                        vo.getvCenterResourcePoolUuid(), vo.getResourceType(), vo.getResourceUuid()));
                newResourcePoolUsages.add(vo);
            }
        }

        // remove stale resource pool usage records
        Set<VCenterResourcePoolUsageVO> staleResourcePoolUsages = new HashSet<>();
        for (VCenterResourcePoolUsageVO vo : existingResourcePoolUsages) {
            if (res.getRpUsages().stream().noneMatch(c -> (c.getvCenterResourcePoolUuid().equals(vo.getvCenterResourcePoolUuid())
                    && c.getResourceUuid().equals(vo.getResourceUuid())))) {
                logger.info(String.format("[vc] remove stale Resource Pool Usage [pool uuid:%s], resource [type:%s, uuid: %s]",
                        vo.getvCenterResourcePoolUuid(), vo.getResourceType(), vo.getResourceUuid()));
                staleResourcePoolUsages.add(vo);
            }
        }
        dbf.removeCollection(staleResourcePoolUsages, VCenterResourcePoolUsageVO.class);

        // persist new resource pool
        for (VCenterResourcePoolVO rp : res.getRps()) {
            if (existingResourcePools.stream().noneMatch(c -> c.getMorVal().equalsIgnoreCase(rp.getMorVal()))) {
                logger.info(String.format("[vc] get new Resource Pool [name:%s, uuid: %s]",
                        rp.getName(), rp.getUuid()));
                rp.setAccountUuid(acntUuid);
                dbf.persist(rp);
            }
        }

        // remove stale resource pool records
        Set<String> staleResourcePools = new HashSet<>();
        for (VCenterResourcePoolVO rp : existingResourcePools) {
            if (res.getRps().stream().noneMatch(c -> c.getMorVal().equalsIgnoreCase(rp.getMorVal()))) {
                logger.info(String.format("[vc] remove stale Resource Pool [%s:%s]", rp.getName(), rp.getUuid()));
                staleResourcePools.add(rp.getUuid());
            }
        }
        dbf.removeByPrimaryKeys(staleResourcePools, VCenterResourcePoolVO.class);

        dbf.persistCollection(newResourcePoolUsages);
    }

    private void syncBackupStorage(VCenterVO vcvo, ResourceScanResult res, VCenterServiceInstance si) {
        final List<VCenterBackupStorageVO> existingBSs = Q.New(VCenterBackupStorageVO.class)
                .eq(VCenterBackupStorageVO_.vCenterUuid, vcvo.getUuid())
                .list();

        if (existingBSs.size() == 1 && existingBSs.get(0).getDatastore() == null) {
            throw new OperationFailureException(operr("can't sync before datastores are separated"));
        }

        // persist new backup storage
        for (VCenterBackupStorageVO bsvo : res.getBackupStorages()) {
            if (existingBSs.stream().noneMatch(v -> v.getDatastore().equalsIgnoreCase(bsvo.getDatastore()))) {
                logger.info(String.format("[vc] got new bs [%s:%s]", bsvo.getName(), bsvo.getUuid()));
                dbf.persist(bsvo);

                for (BackupStorageZoneRefVO rvo : res.getBackupStorageZoneRefs()) {
                    if (rvo.getBackupStorageUuid().equalsIgnoreCase(bsvo.getUuid())) {
                        dbf.persist(rvo);
                    }
                }
            }
        }

        // image deletion should before remote backupstorage
        removeStaleImages(vcvo.getUuid(), res.getImages(), si);

        // remove old backup storage
        for (VCenterBackupStorageVO bsvo : existingBSs) {
            if (res.getBackupStorages().stream().noneMatch(v -> v.getDatastore().equalsIgnoreCase(bsvo.getDatastore()))) {
                List<BackupStorageZoneRefVO> rvos = Q.New(BackupStorageZoneRefVO.class)
                        .eq(BackupStorageZoneRefVO_.backupStorageUuid, bsvo.getUuid())
                        .list();
                dbf.removeCollection(rvos, BackupStorageZoneRefVO.class);
                dbf.remove(bsvo);
            }
        }
    }

    private void syncPrimaryStorage(VCenterVO vcvo, ClusterSyncResult csres, ResourceScanResult res) {
        final List<VCenterPrimaryStorageVO> existingPSs = Q.New(VCenterPrimaryStorageVO.class)
                .eq(VCenterPrimaryStorageVO_.vCenterUuid, vcvo.getUuid())
                .list();

        List<String> psUuids = existingPSs.stream().map(VCenterPrimaryStorageVO::getUuid).collect(Collectors.toList());

        List<PrimaryStorageClusterRefVO> existingRefs;
        if (psUuids.size() == 0) {
            existingRefs = new ArrayList<>();
        } else {
            existingRefs = Q.New(PrimaryStorageClusterRefVO.class)
                    .in(PrimaryStorageClusterRefVO_.primaryStorageUuid, psUuids)
                    .list();
        }

        // persist new primary storage
        for (VCenterPrimaryStorageVO psvo : res.getPrimaryStorages()) {
            if (existingPSs.stream().noneMatch(v -> v.getDatastore().equalsIgnoreCase(psvo.getDatastore()))) {
                logger.info(String.format("[vc] got new ps [%s:%s]", psvo.getName(), psvo.getUuid()));
                dbf.persist(psvo);

                for (PrimaryStorageClusterRefVO rvo : res.getPsClusterRefs()) {
                    if (rvo.getPrimaryStorageUuid().equalsIgnoreCase(psvo.getUuid())) {
                        dbf.persist(rvo);
                    }
                }

                for (PrimaryStorageCapacityVO cvo : res.getPrimaryStorageCapacities()) {
                    if (cvo.getUuid().equalsIgnoreCase(psvo.getUuid())) {
                        dbf.persist(cvo);
                    }
                }

                continue;
            }

            //refresh cluster and primaryStorage ref

            List<PrimaryStorageClusterRefVO> refs = existingRefs.stream()
                    .filter(v -> v.getPrimaryStorageUuid().equals(psvo.getUuid()))
                    .collect(Collectors.toList());

            //persist new refs
            for (PrimaryStorageClusterRefVO newRef : res.getPsClusterRefs()) {
                if (!newRef.getPrimaryStorageUuid().equals(psvo.getUuid())) {
                    continue;
                }

                if (refs.stream().noneMatch(v -> v.getPrimaryStorageUuid().equals(newRef.getPrimaryStorageUuid())
                        && v.getClusterUuid().equals(newRef.getClusterUuid()))) {
                    dbf.persist(newRef);
                }
            }

            //remove old refs
            for (PrimaryStorageClusterRefVO oldRef : refs) {
                if (res.getPsClusterRefs().stream().noneMatch(v -> v.getPrimaryStorageUuid().equals(oldRef.getPrimaryStorageUuid())
                        && v.getClusterUuid().equals(oldRef.getClusterUuid()))) {
                    dbf.removeByPrimaryKey(oldRef.getId(), PrimaryStorageClusterRefVO.class);
                }
            }

        }

        List<PrimaryStorageDeletionMsg> psdmsgs = new ArrayList<>();
        for (VCenterPrimaryStorageVO psvo : existingPSs) {
            if (res.getPrimaryStorages().stream().noneMatch(v -> v.getDatastore().equalsIgnoreCase(psvo.getDatastore()))) {
                PrimaryStorageDeletionMsg msg = new PrimaryStorageDeletionMsg();
                msg.setPrimaryStorageUuid(psvo.getUuid());
                msg.setForceDelete(true);
                bus.makeTargetServiceIdByResourceUuid(msg, PrimaryStorageConstant.SERVICE_ID, psvo.getUuid());
                psdmsgs.add(msg);
            }
        }

        if (!psdmsgs.isEmpty()) {
            bus.call(psdmsgs);
            dbf.removeByPrimaryKeys(
                    psdmsgs.stream().map(PrimaryStorageDeletionMsg::getPrimaryStorageUuid).collect(Collectors.toList()),
                    VCenterPrimaryStorageVO.class);
        }

        // remove old primary storage
        for (VCenterPrimaryStorageVO psvo : existingPSs) {
            if (res.getPrimaryStorages().stream().noneMatch(v -> v.getDatastore().equalsIgnoreCase(psvo.getDatastore()))) {
                List<PrimaryStorageClusterRefVO> rvos = Q.New(PrimaryStorageClusterRefVO.class)
                        .eq(PrimaryStorageClusterRefVO_.primaryStorageUuid, psvo.getUuid())
                        .list();
                dbf.removeCollection(rvos, PrimaryStorageClusterRefVO.class);
                dbf.remove(psvo);
            }
        }

        updatePrimaryStorageClusterRef(res, csres);
    }

    private void updatePrimaryStorageClusterRef(ResourceScanResult res, ClusterSyncResult csres) {
        // in case we have new clusters attached to existing primary storage
        if (!csres.getNewClusters().isEmpty()) {
            res.getPsClusterRefs().stream()
                    .filter(r -> csres.getNewClusters().contains(r.getClusterUuid()))
                    .forEach(ref -> {
                        if (!Q.New(PrimaryStorageClusterRefVO.class)
                                .eq(PrimaryStorageClusterRefVO_.primaryStorageUuid, ref.getPrimaryStorageUuid())
                                .eq(PrimaryStorageClusterRefVO_.clusterUuid, ref.getClusterUuid())
                                .isExists()) {
                            dbf.persist(ref);
                        }
                    });
        }

        if (!csres.getStaleClustes().isEmpty()) {
            SQL.New(PrimaryStorageClusterRefVO.class)
                    .in(PrimaryStorageClusterRefVO_.clusterUuid, csres.getStaleClustes())
                    .delete();
        }
    }

    private List<String> syncEsxHosts(VCenterVO vcvo, ResourceScanResult res) {
        final List<ESXHostVO> existingHosts = Q.New(ESXHostVO.class)
                .eq(ESXHostVO_.vCenterUuid, vcvo.getUuid())
                .list();

        // persist new hosts
        for (ESXHostVO hvo : res.getHosts()) {
            ESXHostVO existingHost = existingHosts.stream().filter(h -> h.getMorval().equalsIgnoreCase(hvo.getMorval())).findFirst().orElse(null);
            if (existingHost == null) {
                logger.info(String.format("[vc] got new host [%s:%s]", hvo.getName(), hvo.getUuid()));
                dbf.persist(hvo);

                for (HostCapacityVO cvo : res.getHostCapacities()) {
                    if (cvo.getUuid().equalsIgnoreCase(hvo.getUuid())) {
                        dbf.persist(cvo);
                    }
                }

                HostCanonicalEvents.HostStatusChangedData data = new HostCanonicalEvents.HostStatusChangedData();
                data.setHostUuid(hvo.getUuid());
                data.setNewStatus(hvo.getStatus().toString());
                data.setOldStatus(null);
                HostInventory hostInventory = new HostInventory();
                hostInventory.setUuid(hvo.getUuid());
                data.setInventory(HostInventory.valueOf(hvo));
                evtf.fire(HostCanonicalEvents.HOST_STATUS_CHANGED_PATH, data);
            } else if (hvo.getStatus() != existingHost.getStatus() || hvo.getState() != existingHost.getState()) {
                HostCanonicalEvents.HostStatusChangedData data = new HostCanonicalEvents.HostStatusChangedData();
                data.setHostUuid(hvo.getUuid());
                data.setNewStatus(hvo.getStatus().toString());
                data.setOldStatus(existingHost.getStatus().toString());
                HostInventory hostInventory = new HostInventory();
                hostInventory.setUuid(hvo.getUuid());
                data.setInventory(HostInventory.valueOf(hvo));
                evtf.fire(HostCanonicalEvents.HOST_STATUS_CHANGED_PATH, data);

                existingHost.setStatus(hvo.getStatus());
                existingHost.setState(hvo.getState());
                existingHost.setManagementIp(hvo.getManagementIp());
                existingHost.setEsxiVersion(hvo.getEsxiVersion());
                existingHost.setvCenterUuid(hvo.getvCenterUuid());
                existingHost.setName(hvo.getName());
                existingHost.setDescription(hvo.getDescription());
                existingHost.setClusterUuid(hvo.getClusterUuid());
                existingHost.setHypervisorType(hvo.getHypervisorType());
                existingHost.setZoneUuid(hvo.getZoneUuid());
                existingHost.setArchitecture(hvo.getArchitecture());

                dbf.update(existingHost);
            }
        }

        // remove stale host records
        List<HostDeletionMsg> hdmsgs = new ArrayList<>();
        for (ESXHostVO hvo : existingHosts) {
            if (res.getHosts().stream().noneMatch(c -> c.getMorval().equalsIgnoreCase(hvo.getMorval()))) {
                HostDeletionMsg msg = new HostDeletionMsg();
                msg.setForceDelete(true);
                msg.setHostUuid(hvo.getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hvo.getUuid());
                hdmsgs.add(msg);

                HostCanonicalEvents.HostStatusChangedData data = new HostCanonicalEvents.HostStatusChangedData();
                data.setHostUuid(hvo.getUuid());
                data.setNewStatus(HostStatus.Disconnected.toString());
                data.setOldStatus(null);
                HostInventory hostInventory = new HostInventory();
                hostInventory.setUuid(hvo.getUuid());
                data.setInventory(HostInventory.valueOf(hvo));
                evtf.fire(HostCanonicalEvents.HOST_STATUS_CHANGED_PATH, data);
            }
        }

        // check host datetime
        checkVcenterHostTime(res.getHostTime());

        final List<String> hostsToBeDeleted = hdmsgs.stream().map(HostDeletionMsg::getHostUuid).collect(Collectors.toList());
        if (!hdmsgs.isEmpty()) {
            bus.call(hdmsgs);
            dbf.removeByPrimaryKeys(hostsToBeDeleted, ESXHostVO.class);
            for (String uuid : hostsToBeDeleted) {
                dbf.eoCleanup(HostVO.class, uuid);
            }
        }

        return hostsToBeDeleted;
    }

    private Map<String, String> syncVolumes(String acntUuid, ResourceScanResult res, ServiceInstance si, List<VmInstanceVO> vms) {
        Map<String, String> vmNewVolumeDict = new HashMap<>();
        List<String> newDataVolumeUuids = new ArrayList<>();
        Set<String> vmUuids = vms.stream().map(vm -> vm.getUuid()).collect(Collectors.toSet());

        for (VolumeVO vo : res.getVolumes()) {
            if (!vmUuids.contains(vo.getVmInstanceUuid())) {
                continue;
            }

            final String psUuid = SQL.New("select vol.primaryStorageUuid from VmInstanceVO vm, VolumeVO vol " +
                            "where vm.uuid = :vmUuid " +
                            "and vm.rootVolumeUuid = vol.uuid", String.class)
                    .param("vmUuid", vo.getVmInstanceUuid())
                    .find();
            if (psUuid != null && psUuid.equals(vo.getPrimaryStorageUuid())) {
                if (vo.getType() == VolumeType.Root) {
                    final String oldRootVolumeUuid = Q.New(VmInstanceVO.class)
                            .eq(VmInstanceVO_.uuid, vo.getVmInstanceUuid())
                            .eq(VmInstanceVO_.hypervisorType, ESXConstant.VMWARE_HYPERVISOR_TYPE)
                            .select(VmInstanceVO_.rootVolumeUuid)
                            .findValue();
                    if (oldRootVolumeUuid != null && oldRootVolumeUuid.equals(vo.getUuid())) {
                        dbf.updateAndRefresh(vo);
                        continue;
                    }
                }
            }

            // dataVolume may have different PS other than root, no need to regard it as a new Volume
            if (Q.New(VolumeVO.class)
                    .eq(VolumeVO_.type, VolumeType.Data)
                    .eq(VolumeVO_.uuid, vo.getUuid())
                    .eq(VolumeVO_.vmInstanceUuid, vo.getVmInstanceUuid())
                    .eq(VolumeVO_.primaryStorageUuid, vo.getPrimaryStorageUuid())
                    .isExists()) {
                dbf.updateAndRefresh(vo);
                continue;
            }

            VolumeVO local = dbf.findByUuid(vo.getUuid(), VolumeVO.class);
            if (local != null) {
                dbf.updateAndRefresh(vo);
            } else {
                logger.info(String.format("[vc] got new volume [%s:%s]", vo.getName(), vo.getUuid()));
                vo.setAccountUuid(acntUuid);
                dbf.persist(vo);
                // fix ZSTAC-10633 to take DataVolume as root after sync
                if (vo.getType().equals(VolumeType.Root)) {
                    vmNewVolumeDict.put(vo.getVmInstanceUuid(), vo.getUuid());
                } else {
                    newDataVolumeUuids.add(vo.getUuid());
                }
            }

            VolumeCanonicalEvents.VolumeStatusChangedData d = new VolumeCanonicalEvents.VolumeStatusChangedData();
            d.setInventory(VolumeInventory.valueOf(vo));
            d.setDate(new Date());
            d.setNewStatus(vo.getStatus().toString());
            d.setOldStatus(null);
            d.setVolumeUuid(vo.getUuid());
            evtf.fire(VolumeCanonicalEvents.VOLUME_STATUS_CHANGED_PATH, d);
        }

        createTagForNewVolume(newDataVolumeUuids, si);
        return vmNewVolumeDict;
    }

    private void syncVmTemplates(String acntUuid, ResourceScanResult res) {
        for (ImageVO vo : res.getImages()) {
            if (dbf.isExist(vo.getUuid(), ImageVO.class)) {
                continue;
            }

            // imageDeletionMsg only clean vo record, delete stale eo record before persist
            if (dbf.isExist(vo.getUuid(), ImageEO.class)) {
                dbf.eoCleanup(ImageVO.class, vo.getUuid());
            }

            logger.info(String.format("[vc] got new image [%s:%s]", vo.getName(), vo.getUuid()));
            vo.setAccountUuid(acntUuid);
            dbf.persist(vo);

            for (ImageBackupStorageRefVO rvo : res.getImageRefs()) {
                if (rvo.getImageUuid().equalsIgnoreCase(vo.getUuid())) {
                    dbf.persist(rvo);
                }
            }
        }
    }

    private Set<String> syncVirtualMachines(String acntUuid, Map<String, String> vmNewVolumeDict, List<VmInstanceVO> vms) {
        Set<String> volumesToDelete = new HashSet<>();

        for (VmInstanceVO vo : vms) {

            VmInstanceVO local = dbf.findByUuid(vo.getUuid(), VmInstanceVO.class);
            if (local != null) {
                boolean isVmChangeDone = false;
                if (!local.getName().equals(vo.getName())) {
                    local.setName(vo.getName());
                    isVmChangeDone = true;
                }

                if (local.getCpuNum() != vo.getCpuNum()) {
                    local.setCpuNum(vo.getCpuNum());
                    isVmChangeDone = true;
                }

                if (local.getMemorySize() != vo.getMemorySize()) {
                    local.setMemorySize(vo.getMemorySize());
                    isVmChangeDone = true;
                }

                if (!local.getClusterUuid().equals(vo.getClusterUuid())) {
                    local.setClusterUuid(vo.getClusterUuid());
                    isVmChangeDone = true;
                }

                if (!local.getState().equals(vo.getState())) {
                    local.setState(vo.getState());
                    isVmChangeDone = true;
                }

                if (local.getHostUuid() == null || !local.getHostUuid().equals(vo.getHostUuid())) {
                    local.setLastHostUuid(local.getHostUuid());
                    local.setHostUuid(vo.getHostUuid());
                    isVmChangeDone = true;
                }

                if (local.getDefaultL3NetworkUuid() == null || !local.getDefaultL3NetworkUuid().equals(vo.getDefaultL3NetworkUuid())) {
                    local.setDefaultL3NetworkUuid(vo.getDefaultL3NetworkUuid());
                    isVmChangeDone = true;
                }

                if (isVmChangeDone) {
                    local = dbf.updateAndRefresh(local);
                }

                String oldRootVolumeUuid = local.getRootVolumeUuid();
                if (oldRootVolumeUuid != null) {
                    if (vmNewVolumeDict.containsKey(vo.getUuid())) {
                        String newRootVolumeUuid = vmNewVolumeDict.get(vo.getUuid());
                        changeVmRootVolume(vo, newRootVolumeUuid);
                        volumesToDelete.add(oldRootVolumeUuid);
                    }
                    continue;
                }

                local.setRootVolumeUuid(vo.getRootVolumeUuid());
                local.setImageUuid(vo.getImageUuid());
                dbf.update(local);
            } else {
                logger.info(String.format("[vc] got new vm [%s:%s]", vo.getName(), vo.getUuid()));
                vo.setAccountUuid(acntUuid);
                dbf.persist(vo);
            }

            VmCanonicalEvents.VmStateChangedData data = new VmCanonicalEvents.VmStateChangedData();
            data.setVmUuid(vo.getUuid());
            data.setDate(new Date());
            data.setOldState(null);
            data.setNewState(vo.getState().toString());
            data.setInventory(VmInstanceInventory.valueOf(vo));
            evtf.fire(VmCanonicalEvents.VM_FULL_STATE_CHANGED_PATH, data);
        }

        return volumesToDelete;
    }

    private void UpdateNicIpIfNeeded(VmNicVO nic, String vcUuid) {
        //Skip if already allocate
        if (nic.getIp() != null) {
            return;
        }

        String l2Uuid = SQL.New("select l2.uuid from L3NetworkVO l3, L2NetworkVO l2  " +
                " where l3.l2NetworkUuid = l2.uuid " +
                " and l3.uuid = :l3Uuid")
                .param("l3Uuid", nic.getL3NetworkUuid())
                .find();

        //Skip if not zstack created network
        String vcUuidFromTag = L2NetworkSystemTag.NW_FOR_VCENTER.getTokenByResourceUuid(l2Uuid,
                L2NetworkSystemTag.NW_FOR_VCENTER_TOKEN);

        if (vcUuidFromTag == null) {
            return;
        }

        if (!vcUuid.equals(vcUuidFromTag)) {
            return;
        }
        allocateNicIp(nic);
    }

    private void allocateNicIp(VmNicVO nicvo) {
        AllocateIpMsg msg = new AllocateIpMsg();
        msg.setL3NetworkUuid(nicvo.getL3NetworkUuid());
        bus.makeTargetServiceIdByResourceUuid(msg, L3NetworkConstant.SERVICE_ID, nicvo.getL3NetworkUuid());
        MessageReply reply = bus.call(msg);
        if (!reply.isSuccess()) {
            return;
        }

        AllocateIpReply areply = (AllocateIpReply) reply;
        UsedIpInventory ip = areply.getIpInventory();
        nicvo.setIp(ip.getIp());
        nicvo.setGateway(ip.getGateway());
        nicvo.setNetmask(ip.getNetmask());
        nicvo.setUsedIpUuid(ip.getUuid());
        dbf.updateAndRefresh(nicvo);
    }

    private long getTimeIntervalInMilliSec(Date a, Date b) {
        return Math.abs(a.getTime() - b.getTime());
    }

    private void removeStaleNetworks(List<VSwitchNetwork> vSwitchNetworks, Set<String> dvpgKeys, ClusterSyncResult csres, String vcUuid) {
        if (!csres.getStaleClustes().isEmpty()) {
            SQL.New(L2NetworkClusterRefVO.class)
                    .in(L2NetworkClusterRefVO_.clusterUuid, csres.getStaleClustes())
                    .delete();
        }

        List<String> allL2Uuids = Q.New(L2NetworkVO.class).select(L2NetworkVO_.uuid).listValues();

        Set<String> vcL2s = allL2Uuids.stream().filter(l2uuid -> {
            String vc = VCenterTagHelper.getVCenterUuidFromL2Network(l2uuid);
            return vc != null && vc.equals(vcUuid);
        }).collect(Collectors.toSet());

        Set<String> vSwitchL2s = removePortgroupsByInterface(vcL2s, vSwitchNetworks);

        // the left should be all dvpgs
        vcL2s.removeAll(vSwitchL2s);
        removeDvpgsByInterface(vcL2s, dvpgKeys);
    }

    private boolean findPortGroup(String l2uuid, String vSwitchName, String localPgName, List<VSwitchNetwork> vSwitchNetworks) {
        List<String> clusterUuids = Q.New(L2NetworkClusterRefVO.class)
                .eq(L2NetworkClusterRefVO_.l2NetworkUuid, l2uuid)
                .select(L2NetworkClusterRefVO_.clusterUuid)
                .listValues();
        if (clusterUuids.isEmpty()) {
            logger.warn(String.format("[vc] l2network[%s] has no attached clusters", l2uuid));
            return false;
        }

        for (VSwitchNetwork vsn : vSwitchNetworks) {
            if (!clusterUuids.contains(vsn.getClusterUuid())) {
                continue;
            }

            List<PortGroupInfo> portGroupInfos = vsn.getPortGroups().getOrDefault(vSwitchName, null);
            if (portGroupInfos == null) {
                continue;
            }

            if (portGroupInfos.stream().anyMatch(info -> info.getName().equals(localPgName))) {
                return true;
            }
        }

        return false;
    }

    private Set<String> removePortgroupsByInterface(Set<String> vcL2s, List<VSwitchNetwork> vSwitchNetworks) {
        Set<String> vSwitchL2s = new HashSet<>();

        for (String l2uuid : vcL2s) {
            String vsName = VCenterTagHelper.getL2VSwitch(l2uuid);
            if (vsName == null) {
                continue;
            }

            vSwitchL2s.add(l2uuid);

            String local = Q.New(L2NetworkVO.class)
                    .eq(L2NetworkVO_.uuid, l2uuid)
                    .select(L2NetworkVO_.physicalInterface)
                    .findValue();
            if (!findPortGroup(l2uuid, vsName, local, vSwitchNetworks)) {
                logger.info(String.format("[vc] remove stale L2 record[%s:%s]", local, l2uuid));
                removeStaleL2(l2uuid);
            }
        }

        return vSwitchL2s;
    }

    private void removeDvpgsByInterface(Set<String> l2Uuids, Set<String> remotes) {
        for (String l2uuid : l2Uuids) {
            String local = Q.New(L2NetworkVO.class)
                    .eq(L2NetworkVO_.uuid, l2uuid)
                    .select(L2NetworkVO_.physicalInterface)
                    .findValue();
            if (!remotes.contains(local)) {
                logger.info(String.format("[vc] remove stale L2 record[%s:%s]", local, l2uuid));
                removeStaleL2(l2uuid);
            }
        }
    }

    private void removeStaleL2(String l2uuid) {
        List<String> l3Uuids = Q.New(L3NetworkVO.class)
                .eq(L3NetworkVO_.l2NetworkUuid, l2uuid)
                .select(L3NetworkVO_.uuid)
                .listValues();
        if (!CollectionUtils.isEmpty(l3Uuids)) {
            UpdateQuery.New(VmNicVO.class).in(VmNicVO_.l3NetworkUuid, l3Uuids).delete();
        }

        UpdateQuery.New(L3NetworkVO.class)
                .eq(L3NetworkVO_.l2NetworkUuid, l2uuid)
                .delete();
        dbf.removeByPrimaryKey(l2uuid, L2NetworkVO.class);
    }

    private void changeVmRootVolume(VmInstanceVO vo, String newRootVolumeUuid) {
        logger.info(String.format("[vc] got vm[%s:%s] has new root volume[%s]",
                vo.getName(), vo.getUuid(), newRootVolumeUuid));
        VmInstanceVO newVO = dbf.reload(vo);   // its host might have been changed
        if (newVO != null) {                   // in case the VM was deleted in ZStack
            newVO.setHostUuid(vo.getHostUuid());
            newVO.setState(vo.getState());
            newVO.setRootVolumeUuid(newRootVolumeUuid);
            dbf.updateAndRefresh(newVO);
        }
    }

    private void deleteOldVolumes(Set<String> volumeUuids, String vcUuid, List<VolumeVO> remoteVolumes, VCenterServiceInstance si) {
        final Set<String> volumesToDelete = new HashSet<>(volumeUuids);
        final List<String> allVolumeUuids = SQL.New("select uuid from VolumeVO where "
                + "primaryStorageUuid in "
                + "(select uuid from VCenterPrimaryStorageVO where vCenterUuid = :vcUuid)", String.class)
                .param("vcUuid", vcUuid)
                .list();

        List<String> requiredVolumes = new ArrayList<>();
        List<String> requiredPaths = remoteVolumes.stream().map(VolumeAO::getInstallPath).collect(Collectors.toList());
        requiredPaths.add(VMwareVolumeInstallPath.NotInstantiated.toString());
        if (!remoteVolumes.isEmpty()) {
            requiredVolumes = SQL.New("select uuid from VolumeVO where "
                    + "installPath in :allPaths "
                    + "and primaryStorageUuid in "
                    + "(select uuid from VCenterPrimaryStorageVO where vCenterUuid = :vcUuid)", String.class)
                    .param("vcUuid", vcUuid)
                    .param("allPaths", requiredPaths)
                    .param("vcUuid", vcUuid)
                    .list();
        }

        allVolumeUuids.removeAll(requiredVolumes);
        List<String> nonExistedDataVolume = allVolumeUuids.stream()
                .filter(uuid -> !isDataVolumeAndFileExist(uuid, si))
                .collect(Collectors.toList());
        allVolumeUuids.removeAll(nonExistedDataVolume);


        if (!allVolumeUuids.isEmpty()) {
            List<VolumeVO> volumesToDetachCandidates = dbf.listByPrimaryKeys(allVolumeUuids, VolumeVO.class);
            List<VolumeVO> volumesToDetach = volumesToDetachCandidates.stream()
                    .filter(vo -> vo.getType().equals(VolumeType.Data))
                    .filter(vo -> vo.getVmInstanceUuid() != null)
                    .collect(Collectors.toList());

            for (VolumeVO vo : volumesToDetach) {
                vo.setVmInstanceUuid(null);
                vo.setDeviceId(null);
                dbf.updateAndRefresh(vo);
            }
        }

        volumesToDelete.addAll(nonExistedDataVolume);
        if (volumesToDelete.isEmpty()) {
            return;
        }

        logger.info("[vc] cleaning up stale volumes: " + volumesToDelete.size());
        List<VolumeDeletionMsg> dmsgs = new ArrayList<>();
        volumesToDelete.forEach(uuid -> {
            VolumeDeletionMsg msg = new VolumeDeletionMsg();
            msg.setDeletionPolicy(VolumeDeletionPolicyManager.VolumeDeletionPolicy.DBOnly.toString());
            msg.setVolumeUuid(uuid);
            msg.setForceDelete(true);
            bus.makeTargetServiceIdByResourceUuid(msg, VolumeConstant.SERVICE_ID, uuid);
            dmsgs.add(msg);
        });

        bus.call(dmsgs);
        dbf.removeByPrimaryKeys(volumesToDelete, VolumeVO.class);
        for (String uuid : volumesToDelete) {
            dbf.eoCleanup(VolumeVO.class, uuid);
        }
    }

    private boolean isDataVolumeTemplateAndFileExist(String imageUuid, VCenterServiceInstance si) {
        ImageVO vo = dbf.findByUuid(imageUuid, ImageVO.class);
        if (!vo.getMediaType().equals(ImageConstant.ImageMediaType.DataVolumeTemplate)) {
            return false;
        }

        String dsMorVal = SQL.New("select bs.datastore from VCenterBackupStorageVO bs, ImageBackupStorageRefVO rvo" +
                " where bs.uuid = rvo.backupStorageUuid " +
                " and rvo.imageUuid = :uuid", String.class)
                .param("uuid", imageUuid)
                .find();

        String installPath = Q.New(ImageBackupStorageRefVO.class)
                .eq(ImageBackupStorageRefVO_.imageUuid, imageUuid)
                .select(ImageBackupStorageRefVO_.installPath)
                .findValue();
        Datastore ds = VMwareHelper.getDatastore(si.getServerConnection(), dsMorVal);
        try {
            VMwareHelper.getVirtualDiskStorageUsage(ds, installPath);
        } catch (Throwable ignored) {
            return false;
        }

        return true;
    }

    private boolean isDataVolumeAndFileExist(String volUuid, VCenterServiceInstance si) {
        VolumeVO vo = dbf.findByUuid(volUuid, VolumeVO.class);
        if (!vo.getType().equals(VolumeType.Data)) {
            return false;
        }

        String dsMorVal = Q.New(VCenterPrimaryStorageVO.class)
                .eq(VCenterPrimaryStorageVO_.uuid, vo.getPrimaryStorageUuid())
                .select(VCenterPrimaryStorageVO_.datastore)
                .findValue();

        Datastore ds = VMwareHelper.getDatastore(si.getServerConnection(), dsMorVal);
        try {
            VMwareHelper.getVirtualDiskStorageUsage(ds, vo.getInstallPath());
        } catch (Throwable ignored) {
            return false;
        }

        return true;
    }

    private void removeStaleVMs(String vcUuid, List<String> hostsToBeDeleted, List<VmInstanceVO> remoteVMs) {
        final List<String> existingVmUuids = SQL.New("select vm.uuid from VmInstanceVO vm, ESXHostVO hvo " +
                "where vm.lastHostUuid = hvo.uuid " +
                "and hvo.vCenterUuid = :vcUuid", String.class)
                .param("vcUuid", vcUuid)
                .list();
        Set<String> vmRecordsToBeDeleted = new HashSet<>();
        if (!hostsToBeDeleted.isEmpty()) {
            vmRecordsToBeDeleted.addAll(Q.New(VmInstanceVO.class)
                    .in(VmInstanceVO_.hostUuid, hostsToBeDeleted)
                    .select(VmInstanceVO_.uuid)
                    .listValues());

            vmRecordsToBeDeleted.addAll(Q.New(VmInstanceVO.class)
                    .isNull(VmInstanceVO_.hostUuid)
                    .in(VmInstanceVO_.lastHostUuid, hostsToBeDeleted)
                    .select(VmInstanceVO_.uuid)
                    .listValues());
        }

        for (String vmUuid : existingVmUuids) {
            if (remoteVMs.stream().noneMatch(v -> v.getUuid().equalsIgnoreCase(vmUuid))) {
                vmRecordsToBeDeleted.add(vmUuid);
            }
        }

        //remove orphan vms
        List<String> orphanVmUuids = SQL.New("select vm.uuid from VmInstanceVO vm where vm.hypervisorType=:htype and (" +
                "(vm.lastHostUuid is null and vm.hostUuid is null)" +
                " or vm.clusterUuid not in (select uuid from ClusterVO where hypervisorType=:htype))", String.class)
                .param("htype", ESXConstant.VMWARE_HYPERVISOR_TYPE)
                .list();

        if (CollectionUtils.isNotEmpty(orphanVmUuids)) {
            logger.info(String.format("[vc] find %s orphan vms", orphanVmUuids.size()));
            vmRecordsToBeDeleted.addAll(orphanVmUuids);
        }

        if (vmRecordsToBeDeleted.isEmpty()) {
            return;
        }

        List<String> snatVipsToBeDeleted = new ArrayList<>();
        List<String> vrUuids = Q.New(ApplianceVmVO.class).in(ApplianceVmVO_.uuid, vmRecordsToBeDeleted).select(ApplianceVmVO_.uuid).listValues();
        if (vrUuids != null && !vrUuids.isEmpty()) {
            for (String uuid : vrUuids) {
                List<String> vipUuids = vipProxy.getServiceUuidsByRouterUuid(uuid, VipVO.class.getSimpleName());
                if (vipUuids == null || vipUuids.isEmpty()) {
                    continue;
                }

                vipUuids = Q.New(VipNetworkServicesRefVO.class).eq(VipNetworkServicesRefVO_.serviceType, NetworkServiceType.SNAT.toString())
                        .in(VipNetworkServicesRefVO_.vipUuid, vipUuids).select(VipNetworkServicesRefVO_.vipUuid).listValues();
                if (vipUuids == null || vipUuids.isEmpty()) {
                    continue;
                }
                snatVipsToBeDeleted.addAll(vipUuids);
            }
        }

        logger.info("[vc] remove obsolete VM records, number = " + vmRecordsToBeDeleted.size());

        List<VmInstanceDeletionMsg> vmdmsgs = new ArrayList<>();

        for (String vmUuid : vmRecordsToBeDeleted) {
            VmInstanceDeletionMsg msg = new VmInstanceDeletionMsg();
            msg.setForceDelete(true);
            msg.setDeletionPolicy(VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.DBOnly.toString());
            msg.setVmInstanceUuid(vmUuid);
            bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, vmUuid);
            vmdmsgs.add(msg);
        }

        bus.call(vmdmsgs);

        if (snatVipsToBeDeleted.isEmpty()) {
            return;
        }

        removeStaleVips(snatVipsToBeDeleted);
    }

    private void removeStaleVips(List<String> vipUuids) {
        List<VipDeletionMsg> msgs = new ArrayList<>();
        vipUuids.forEach(uuid -> {
            VipDeletionMsg msg = new VipDeletionMsg();
            msg.setVipUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(msg, VipConstant.SERVICE_ID, msg.getVipUuid());
            msgs.add(msg);
        });
        bus.call(msgs);
    }

    private void removeStaleImages(String vcUuid, List<ImageVO> remoteImages, VCenterServiceInstance si) {
        final List<String> exsitingImages = SQL.New("select img.uuid from ImageVO img, VCenterBackupStorageVO bs, ImageBackupStorageRefVO rvo " +
                "where bs.vCenterUuid = :vcUuid " +
                "and rvo.backupStorageUuid = bs.uuid " +
                "and rvo.imageUuid = img.uuid", String.class)
                .param("vcUuid", vcUuid)
                .list();

        //filter out existed data volume template
        List<String> filteredImages = exsitingImages.stream()
                .filter(uuid -> !isDataVolumeTemplateAndFileExist(uuid, si))
                .collect(Collectors.toList());

        List<ImageDeletionMsg> imgdmsgs = new ArrayList<>();
        for (String imgUuid : filteredImages) {
            if (remoteImages.stream().noneMatch(v -> v.getUuid().equalsIgnoreCase(imgUuid))) {
                ImageDeletionMsg msg = new ImageDeletionMsg();
                msg.setForceDelete(true);
                msg.setDeletionPolicy(ImageDeletionPolicyManager.ImageDeletionPolicy.DeleteReference.toString());
                msg.setImageUuid(imgUuid);
                bus.makeTargetServiceIdByResourceUuid(msg, ImageConstant.SERVICE_ID, imgUuid);
                imgdmsgs.add(msg);
            }
        }

        if (!imgdmsgs.isEmpty()) {
            bus.call(imgdmsgs);
        }
    }

    private void removeStaleVMNics(String vcUuid, Map<String, VmNicsResult> vmNics) {
        final List<Tuple> existingVmNics = SQL.New("select nic.mac, nic.uuid from VmInstanceVO vm, ESXHostVO hvo, VmNicVO nic " +
                "where vm.lastHostUuid = hvo.uuid " +
                "and vm.uuid = nic.vmInstanceUuid " +
                "and hvo.vCenterUuid = :vcUuid", Tuple.class)
                .param("vcUuid", vcUuid)
                .list();
        final Map<String, String> macDict = new HashMap<>();
        existingVmNics.forEach(t -> macDict.put(t.get(0, String.class), t.get(1, String.class)));
        vmNics.values()
                .forEach(nics -> nics.getNics().values().forEach(nic -> macDict.remove(nic.getMac())));
        if (macDict.isEmpty()) {
            return;
        }

        dbf.removeByPrimaryKeys(macDict.values(), VmNicVO.class);
    }

    private void createHostSystemTags(ResourceScanResult res) {
        List<ESXHostVO> hosts = res.getHosts();
        Map<String, List<String>> hostExtraIps = res.getHostExtraIps();
        for (ESXHostVO hvo : hosts) {
            if (hostExtraIps.containsKey(hvo.getUuid())) {
                String tag = StringUtils.join(hostExtraIps.get(hvo.getUuid()), ",");
                if (StringUtils.isEmpty(tag)) {
                    continue;
                }

                SystemTagCreator creator = HostSystemTags.EXTRA_IPS.newSystemTagCreator(hvo.getUuid());
                Optional.ofNullable(HostSystemTags.EXTRA_IPS_TOKEN).ifPresent(it -> creator.setTagByTokens(Collections.singletonMap(HostSystemTags.EXTRA_IPS_TOKEN, tag)));
                creator.inherent = true;
                creator.recreate = true;
                creator.create();
            } else {
                HostSystemTags.EXTRA_IPS.delete(hvo.getUuid());
            }
        }
    }

    private void persistResources(final VCenterVO vcvo, final String acntUuid, final ResourceScanResult res) {
        logger.info("[vc] persistent vCenter DataCenters, number = " + res.getDateCenters().size());
        dbf.persistCollection(res.getDateCenters());

        logger.info("[vc] persistent vCenter data-stores, number = " + res.getBackupStorages().size());
        dbf.persistCollection(res.getBackupStorages());
        dbf.persistCollection(res.getBackupStorageZoneRefs());
        dbf.persistCollection(res.getPrimaryStorages());
        dbf.persistCollection(res.getPrimaryStorageCapacities());

        logger.info("[vc] persistent vCenter Cluster Resources, number = " + res.getClusters().size());
        dbf.persistCollection(res.getClusters());
        dbf.persistCollection(res.getPsClusterRefs());

        logger.info("[vc] persistent virtual distributed port-groups, number = " + res.getDvPGs().size());

        // A map from L2 network name to L3 network UUID.
        final Map<String, Map<String, String>> l3dict = new HashMap<>();
        saveNetworks(acntUuid, l3dict, res.getDvPGs(), res.getL2Networks(), vcvo);

        logger.info("[vc] persistent ESX hosts, number = " + res.getHosts().size());
        dbf.persistCollection(res.getHosts());
        createHostSystemTags(res);
        for (ESXHostVO hvo : res.getHosts()) {
            HostCanonicalEvents.HostStatusChangedData data = new HostCanonicalEvents.HostStatusChangedData();
            data.setHostUuid(hvo.getUuid());
            data.setNewStatus(hvo.getStatus().toString());
            data.setOldStatus(null);
            HostInventory hostInventory = new HostInventory();
            hostInventory.setUuid(hvo.getUuid());
            data.setInventory(HostInventory.valueOf(hvo));
            evtf.fire(HostCanonicalEvents.HOST_STATUS_CHANGED_PATH, data);
        }
        dbf.persistCollection(res.getHostCapacities());

        // Must after persisting hosts
        saveVSwitchNetworks(acntUuid, l3dict, res.getVSwitchNetworks(), vcvo);
        NicVmFilteredResult nicVmInfo = fixVmNicInfo(l3dict, res.getVmInstances(), res.getVmNics());
        List<VmNicVO> nics = nicVmInfo.getVmNics();
        List<VmInstanceVO> vms = nicVmInfo.getVms();
        Set<String> vmUuids = vms.stream().map(vm -> vm.getUuid()).collect(Collectors.toSet());

        List<VolumeVO> volumes = res.getVolumes().stream()
                .filter(vol -> vmUuids.contains(vol.getVmInstanceUuid())).collect(Collectors.toList());
        logger.info("[vc] persistent volumes, number = " + volumes.size());
        volumes.forEach(v -> v.setAccountUuid(acntUuid));
        dbf.persistCollection(volumes);
        for (VolumeVO vo : volumes) {
            VolumeCanonicalEvents.VolumeStatusChangedData d = new VolumeCanonicalEvents.VolumeStatusChangedData();
            d.setInventory(VolumeInventory.valueOf(vo));
            d.setDate(new Date());
            d.setNewStatus(vo.getStatus().toString());
            d.setOldStatus(null);
            d.setVolumeUuid(vo.getUuid());
            evtf.fire(VolumeCanonicalEvents.VOLUME_STATUS_CHANGED_PATH, d);
        }

        logger.info("[vc] persistent virtual machines, number = " + vms.size());
        vms.forEach(v -> v.setAccountUuid(acntUuid));
        dbf.persistCollection(vms);
        for (VmInstanceVO vo : vms) {
            VmCanonicalEvents.VmStateChangedData data = new VmCanonicalEvents.VmStateChangedData();
            data.setVmUuid(vo.getUuid());
            data.setDate(new Date());
            data.setOldState(null);
            data.setNewState(vo.getState().toString());
            data.setInventory(VmInstanceInventory.valueOf(vo));
            evtf.fire(VmCanonicalEvents.VM_FULL_STATE_CHANGED_PATH, data);
        }

        logger.info("[vc] persistent VM NICs, number = " + nics.size());
        nics.forEach(n -> n.setAccountUuid(acntUuid));
        dbf.persistCollection(nics);

        logger.info("[vc] persistent VM templates, number = " + res.getImages().size());
        res.getImages().forEach(i -> i.setAccountUuid(acntUuid));
        dbf.persistCollection(res.getImages());
        dbf.persistCollection(res.getImageRefs());

        logger.info("[vc] persistent vCenter Resource pool, number = " + res.getRps().size());
        res.getRps().forEach(i -> i.setAccountUuid(acntUuid));
        dbf.persistCollection(res.getRps());
        dbf.persistCollection(res.getRpUsages());
    }

    public void createVolumeMappingTag(List<VolumeVO> volList, ServiceInstance si) {
        for (VolumeVO vo : volList) {
            if (vo.getType().equals(VolumeType.Root)) {
                continue;
            }
            String vCenterVolUuid = VMwareHelper.getVCenterVolUuidByInstallPath(si, vo.getUuid(), vo.getInstallPath());
            if (vCenterVolUuid == null) {
                continue;
            }
            VCenterTagHelper.tagVCenterVolumeUuid(vo.getUuid(), vCenterVolUuid);
        }
    }

    private void doReconnectStorage(final String vcUuid) {
        final List<String> psUuids = Q.New(VCenterPrimaryStorageVO.class)
                .eq(VCenterPrimaryStorageVO_.vCenterUuid, vcUuid)
                .select(VCenterPrimaryStorageVO_.uuid)
                .listValues();

        new While<>(psUuids).step((psUuid, completion) -> {
            ReconnectPrimaryStorageMsg msg = new ReconnectPrimaryStorageMsg();
            msg.setPrimaryStorageUuid(psUuid);
            msg.setServiceId(bus.makeLocalServiceId(PrimaryStorageConstant.SERVICE_ID));
            bus.send(msg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("connect primaryStorage [%s] failed, %s", psUuid, reply.getError()));
                    }
                    completion.done();
                }
            });
        }, 3).run(new NopeWhileDoneCompletion());

        final List<String> bsUuids = Q.New(VCenterBackupStorageVO.class)
                .eq(VCenterBackupStorageVO_.vCenterUuid, vcUuid)
                .select(VCenterBackupStorageVO_.uuid)
                .listValues();

        new While<>(bsUuids).step((bsUuid, completion) -> {
            ConnectBackupStorageMsg cmsg = new ConnectBackupStorageMsg();
            cmsg.setBackupStorageUuid(bsUuid);
            cmsg.setNewAdd(false);
            cmsg.setServiceId(bus.makeLocalServiceId(BackupStorageConstant.SERVICE_ID));
            bus.send(cmsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("connect backupStorage [%s] failed, %s", bsUuid, reply.getError()));
                    }
                    completion.done();
                }
            });
        }, 2).run(new NopeWhileDoneCompletion());
    }

    private void updateHostCluster(List<ESXHostVO> hosts, String vcUuid) {
        Map<String, String> remoteHostClusterDict = new HashMap<>();
        hosts.forEach(h -> remoteHostClusterDict.put(h.getUuid(), h.getClusterUuid()));

        List<ESXHostVO> localHosts = Q.New(ESXHostVO.class)
                .eq(ESXHostVO_.vCenterUuid, vcUuid)
                .list();
        for (ESXHostVO hvo : localHosts) {
            String hostUuid = hvo.getUuid();
            String clusterUuid = remoteHostClusterDict.get(hostUuid);
            if (clusterUuid == null) {
                logger.info(String.format("[vc] the cluster for host[%s:%s] is not found remotely", hvo.getName(), hostUuid));
                continue;
            }

            if (hvo.getClusterUuid() == null || !hvo.getClusterUuid().equals(clusterUuid)) {
                logger.info(String.format("[vc] set host[%s] cluster to [%s]", hvo.getName(), clusterUuid));
                hvo.setClusterUuid(clusterUuid);
                dbf.update(hvo);
            }
        }
    }

    private void syncVCenter(final String vcUuid, final String acntUuid, Completion completion) {
        final VCenterVO vcvo = dbf.findByUuid(vcUuid, VCenterVO.class);
        
        try (VCenterServiceInstance si = VMwareHelper.getVCenterServiceInstance(vcvo)) {
            if (VCenterGlobalConfig.VCENTER_TASK_CHECK.value(Boolean.class)) {
                ErrorCode errorCode = VMwareHelper.allowSync(si);
                if (errorCode != null) {
                    completion.fail(operr(errorCode, "There are tasks running on the VCenter[uuid:%s], please try again later.", vcvo.getUuid()));
                    return;
                }
            }

            vcvo.setStatus(VCenterStatus.Synchronizing);
            dbf.update(vcvo);

            stopMonitorForVC(vcUuid);

            final List<String> vcvms = VMwareHelper.listVCenterVMs(si);

            ResourceScanResult latestResource = scanVCenterResource(si, vcvo, vcvms);
            logger.info("[vc] preparing for syncing resource changes for vCenter: " + vcvo.getName());

            syncWithLatestResources(vcvo, acntUuid, latestResource, si);
            completion.success();
        } catch (RemoteException rex) {
            completion.fail(operr("%s", rex.getMessage()));
        } finally {
            checkStatus(vcvo);

            try {
                doConnectHosts(Q.New(ESXHostVO.class).eq(ESXHostVO_.vCenterUuid, vcUuid).list());
                doReconnectStorage(vcvo.getUuid());
            } catch (Exception ignored) {
            }
            launchMonitor(vcvo);
            VCenterMetricsReader.startVCenterMetricsTask(vcvo.getUuid());
        }
    }

    private void handle(final APISyncVCenterMsg msg) {
        SyncVCenterMsg smsg = new SyncVCenterMsg();
        smsg.setAccountUuid(msg.getSession().getAccountUuid());
        smsg.setUuid(msg.getVCenterUuid());
        bus.makeTargetServiceIdByResourceUuid(smsg, SERVICE_ID, msg.getVCenterUuid());
        bus.send(smsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                APISyncVCenterEvent evt = new APISyncVCenterEvent(msg.getId());
                if (!reply.isSuccess()) {
                    evt.setError(reply.getError());
                }
                bus.publish(evt);
            }
        });
    }

    private void handle(final APIUpdateVCenterMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getVCenterSyncSignature(msg);
            }

            @Override
            public void run(SyncTaskChain chain) {
                updateVCenter(msg, new ReturnValueCompletion<VCenterInventory>(chain, msg) {
                    final APIUpdateVCenterEvent evt = new APIUpdateVCenterEvent(msg.getId());

                    @Override
                    public void success(VCenterInventory inv) {
                        evt.setInventory(inv);
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("update-vcenter-%s", msg.getUuid());
            }
        });
    }

    private void updateVCenter(final APIUpdateVCenterMsg msg, ReturnValueCompletion<VCenterInventory> completion) {
        VCenterVO vcvo = dbf.findByUuid(msg.getVCenterUuid(), VCenterVO.class);

        if (vcvo == null) {
            completion.fail(operr("VCenter[uuid:%s] not found: ", msg.getVCenterUuid()));
            return;
        }

        if (msg.getName() != null) {
            vcvo.setName(msg.getName());
        }

        if (msg.getDescription() != null) {
            vcvo.setDescription(msg.getDescription());
        }

        if (msg.getUsername() != null) {
            vcvo.setUserName(msg.getUsername());
        }

        if (msg.getPassword() != null) {
            vcvo.setPassword(msg.getPassword());
        }

        if (msg.getDomainName() != null) {
            vcvo.setDomainName(msg.getDomainName());
        }

        if (msg.getPort() != null) {
            vcvo.setPort(msg.getPort());
        }

        if (msg.getState() != null) {
            vcvo.setState(VCenterState.valueOf(msg.getState()));
        }

        vcvo = dbf.updateAndRefresh(vcvo);
        if (msg.getUsername() != null ||
                msg.getPassword() != null ||
                msg.getPort() != null ||
                msg.getDomainName() != null) {
            stopMonitorForVC(msg.getVCenterUuid());
            launchMonitor(vcvo);
            VCenterMetricsReader.stopVCenterMetricsTask(vcvo.getUuid());
            VCenterMetricsReader.startVCenterMetricsTask(vcvo.getUuid());
        }

        completion.success(VCenterInventory.valueOf(vcvo));
    }

    // AddVCenter/DeleteVCenter is not running inside a queue.
    // Because DeleteVCenter needs an UUID from AddVCenter.
    // Thus they can not run at the same time.
    private void deleteVCenter(final APIDeleteVCenterMsg msg, Completion completion) {
        final VCenterVO vcvo = dbf.findByUuid(msg.getUuid(), VCenterVO.class);
        final String issuer = VCenterVO.class.getSimpleName();
        final List<VCenterInventory> ctx = VCenterInventory.valueOf(Collections.singletonList(vcvo));

        // No null check against vcvo, since its uuid is verified with 'APIParam'.
        FlowChain chain = getDeletionFlowChain(vcvo, msg.getDeletionMode(), issuer, ctx);
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, completion);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(err(SysErrors.DELETE_RESOURCE_ERROR, errCode, errCode.getDetails()));
            }
        });

        chain.start();
    }

    private void sendVCenterDeletionEvent(String vcenterUuid) {
        VCenterCanonicalEvents.VCenterDeletionData data = new VCenterCanonicalEvents.VCenterDeletionData();
        data.setVcenterUuid(vcenterUuid);
        evtf.fire(VCenterCanonicalEvents.VC_DELETION_PATH, data);
    }

    private void handle(final APIDeleteVCenterMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getVCenterSyncSignature(msg);
            }

            @Override
            public void run(SyncTaskChain chain) {
                final APIDeleteVCenterEvent evt = new APIDeleteVCenterEvent(msg.getId());
                deleteVCenter(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        sendVCenterDeletionEvent(msg.getVCenterUuid());
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        sendVCenterDeletionEvent(msg.getVCenterUuid());
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("update-vcenter-%s", msg.getUuid());
            }
        });
    }

    private void handle(final APIGetVCenterDVSwitchesMsg msg) {
        final VCenterVO vcvo = dbf.findByUuid(msg.getVCenterUuid(), VCenterVO.class);
        List<DVSwitchInfo> infos = VMwareHelper.listDVSwitch(vcvo.getUuid());

        APIGetVCenterDVSwitchesReply reply = new APIGetVCenterDVSwitchesReply();
        reply.setVcUuid(msg.getVCenterUuid());
        reply.setInventories(infos);
        bus.reply(msg, reply);
    }

    private ErrorCode fromThrowable(Throwable ex, APIAddVCenterMsg msg) {
        if (ex instanceof NullPointerException) {
            return operr("Login failed, please check your login parameters.");
        }

        if (ex instanceof ConnectException) {
            return operr("connect %s failed: %s", msg.getDomainName(), ex.getMessage());
        }

        if (ex instanceof RemoteException) {
            final RemoteException rex = (RemoteException) ex;
            if (rex.getMessage().contains(InvalidLogin.class.getName())) {
                return operr("Login to vCenter [%s] failed with user [%s],"
                                + "please check your network connection and credential.",
                        msg.getDomainName(),
                        msg.getUsername());
            }

            if (rex.getMessage().contains(SAXParseException.class.getName())) {
                return operr("Parse response failed from vCenter [%s],"
                                + "please check the port number[%d].",
                        msg.getDomainName(),
                        msg.getPort() == null ? 443 : msg.getPort());
            }

            if (rex.getMessage().contains(SSLHandshakeException.class.getName())) {
                if (rex.getMessage().contains("TLS10 is not accepted by client preferences")) {
                    return operr("SSL handshake failed with vCenter [%s],"
                                    + "because insecure TLS 1.0 is used."
                                    + " Manually enabled TLS 1.0 in jdk configuration if needed.",
                            msg.getDomainName());
                }

                return operr("SSL handshake failed with vCenter [%s],"
                                + "please check the port number[%d].",
                        msg.getDomainName(),
                        msg.getPort() == null ? 443 : msg.getPort());
            }
        }

        return operr(ex.getMessage());
    }

    private void checkVcenterHostTime(Map<String, Date> hostsDate) {
        // check host datetime
        hostsDate.forEach((uuid, hostTime) -> {
                    Date mnTime = Calendar.getInstance().getTime();
                    // fire if time difference more then one day
                    final long hostMnTimeInterval = getTimeIntervalInMilliSec(hostTime, mnTime);
                    if (hostMnTimeInterval > TimeUnit.DAYS.toMillis(1)) {
                        VCenterCanonicalEvents.VCenterHostWrongDateTimeData data = new VCenterCanonicalEvents.VCenterHostWrongDateTimeData();
                        data.setHostUuid(uuid);
                        data.setTimeDifferenceInHour(String.valueOf(TimeUnit.MILLISECONDS.toHours(hostMnTimeInterval)));
                        evtf.fire(VCenterCanonicalEvents.VC_HOST_WRONG_DATETIME_PATH, data);
                    }
                }
        );
    }

    private void handle(final APIAddVCenterMsg msg) {
        final String vCenterUrl = VMwareHelper.buildVCenterUrl(msg.isHttps(), msg.getDomainName(), msg.getPort());
        final APIAddVCenterEvent evt = new APIAddVCenterEvent(msg.getId());
        VCenterVO vcvo = new VCenterVO();
        boolean needRb = false;

        logger.debug(String.format("[vc] adding vCenter (%s): %s", msg.getName(), vCenterUrl));

        try (VCenterServiceInstance si = vCenterServerInstanceManager.getVCenterServiceInstance(new URL(vCenterUrl),
                msg.getUsername(),
                msg.getPassword(),
                true /* ignore cert */)) {
            final List<String> vcvms = VMwareHelper.listVCenterVMs(si);

            /*
            Long expirationHours = VMwareHelper.getExpirationHours(si);
            if (expirationHours != null && expirationHours <= 1) {
                String info = String.format("vCenter [%s] will expire in %d hours",
                        vCenterUrl, expirationHours);
                throw new CloudRuntimeException(info);
            }
            */

            // Create the vCenter record
            if (msg.getResourceUuid() != null) {
                vcvo.setUuid(msg.getResourceUuid());
            } else {
                vcvo.setUuid(Platform.getUuid());
            }
            vcvo.setName(msg.getName());
            vcvo.setDescription(msg.getDescription());

            vcvo.setZoneUuid(msg.getZoneUuid());
            vcvo.setDomainName(msg.getDomainName());
            vcvo.setHttps(msg.isHttps() ? 1 : 0);
            vcvo.setUserName(msg.getUsername());
            vcvo.setPassword(msg.getPassword());
            vcvo.setPort(msg.getPort());
            vcvo.setStatus(VCenterStatus.Connecting);
            vcvo.setState(VCenterState.Disabled);
            vcvo.setAccountUuid(msg.getSession().getAccountUuid());
            vcvo.setVersion(si.getAboutInfo().getVersion());

            vcvo = dbf.persistAndRefresh(vcvo);
            needRb = true;

            ResourceScanResult res = scanVCenterResource(si, vcvo, vcvms);

            if (res.getClusters().size() == 0) {
                throw new OperationFailureException(operr("No clustered compute resource found"));
            }

            if (res.getDvPGs().isEmpty() && res.getVSwitchNetworks().isEmpty()) {
                throw new OperationFailureException(operr("No dvSwitch or qualified vSwitch found"));
            }

            FutureCompletion completion = new FutureCompletion(null);
            checkLicense(res, completion);
            completion.await(TimeUnit.SECONDS.toMillis(600));
            if (!completion.isSuccess()) {
                throw new OperationFailureException(completion.getErrorCode());
            }
            persistResources(vcvo, msg.getSession().getAccountUuid(), res);
            createVolumeMappingTag(res.getVolumes(), si);

            vcvo.setState(VCenterState.Enabled);
            vcvo.setStatus(VCenterStatus.Connected);
            vcvo = dbf.updateAndRefresh(vcvo);

            // check host datetime
            checkVcenterHostTime(res.getHostTime());
            doConnectHosts(res.getHosts());

            evt.setInventory(VCenterInventory.valueOf(vcvo));
            bus.publish(evt);
        } catch (Throwable ex) {
            logger.warn("[vc] failed in adding vCenter " + vCenterUrl, ex);

            if (needRb) {
                cleanVCenterResource(vcvo);
            }
            evt.setError(fromThrowable(ex, msg));
            bus.publish(evt);
            return;
        }

        // OK - we added a vCenter successfully.
        logger.info("[vc] added vCenter: " + msg.getDomainName());
        monitorVmStatus(vcvo.getUuid(), true);
        VCenterMetricsReader.startVCenterMetricsTask(vcvo.getUuid());
    }

    private void checkLicense(final ResourceScanResult res, Completion completion) {
        AskLicenseCapacityMsg msg = new AskLicenseCapacityMsg();
        Integer hostNum = res.getHosts().size();
        Integer cpuNum = res.getHostCapacities().stream()
                .map(HostCapacityVO::getCpuSockets)
                .reduce(0, Integer::sum);
        Integer vmNum = res.getVmInstances().size();
        msg.setCpuNum(cpuNum);
        msg.setHostNum(hostNum);
        msg.setVmNum(vmNum);
        bus.makeLocalServiceId(msg, LicenseConstant.SERVICE_ID);
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    completion.success();
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    private void cleanVCenterResource(VCenterVO vcvo) {
        final String issuer = VCenterVO.class.getSimpleName();
        final List<VCenterInventory> ctx = VCenterInventory.valueOf(Collections.singletonList(vcvo));

        FlowChain chain = getDeletionFlowChain(vcvo, APIDeleteMessage.DeletionMode.Enforcing, issuer, ctx);
        chain.done(new FlowDoneHandler(null) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
            }
        }).error(new FlowErrorHandler(null) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
            }
        });

        chain.start();
    }

    private void monitorVmStatus(String vcUuid, boolean newVCenter) {
        VMwareResourceMonitor mon = new VMwareResourceMonitor(newVCenter, vcUuid);
        enableVncForHosts(vcUuid);
        instancePool.put(vcUuid, mon);
        mon.start();
    }

    private void tryUpgradeStorage(VCenterVO vcvo) {
        try (VCenterServiceInstance si = VMwareHelper.getVCenterServiceInstance(vcvo)) {
            upgradeStorages(vcvo, si);
        } catch (Throwable ex) {
            logger.warn(String.format("Failed to list datastores from vCenter %s: %s", vcvo.getDomainName(), ex.getMessage()));
        }
    }

    private void launchMonitor(VCenterVO vcvo) {
        if (instancePool.get(vcvo.getUuid()) != null) {
            return;
        }

        try {
            monitorVmStatus(vcvo.getUuid(), false);
        } catch (Throwable ex) {
            logger.warn(String.format("Monitor aborted for vCenter %s: %s", vcvo.getDomainName(), ex.getMessage()));
        }
    }

    private void upgradeStorages(VCenterVO vcvo, ServiceInstance si) {
        // generate BS & PS with datastores
        try {
            logger.info("[vc] checking datastores for " + vcvo.getDomainName());

            if (!Q.New(VCenterBackupStorageVO.class)
                    .eq(VCenterBackupStorageVO_.vCenterUuid, vcvo.getUuid())
                    .isNull(VCenterBackupStorageVO_.datastore)
                    .isExists()) {
                logger.info("[vc] skipped datastore upgrade for " + vcvo.getDomainName());
                return;
            }

            generateDataStores(vcvo, si);
        } catch (Throwable ex) {
            logger.warn(String.format("[vc] failed to upgrade to datastore for vCenter[%s]", vcvo.getDomainName()), ex);
        }
    }

    // Check again to see whether we have volumes left-overs.
    // When query VirtualMachine entities from vCenter, its configure might
    // be temporary set as null if its settings are in an intermediate state.
    private List<String> findLeftOverVolumes(Map<String, List<String>> psVolumeDicts, String vcUuid) {
        Set<String> volumeUuids = psVolumeDicts.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        List<String> volumeUuidsInDB = SQL.New("select vol.uuid from VolumeVO vol, VCenterPrimaryStorageVO ps "
                + "where ps.uuid = vol.primaryStorageUuid "
                + "and ps.vCenterUuid = :vcUuid", String.class)
                .param("vcUuid", vcUuid)
                .list();

        List<String> res = new ArrayList<>();

        for (String vol : volumeUuidsInDB) {
            if (!volumeUuids.contains(vol)) {
                logger.info(String.format("[vc] found a left-over volume: %s", vol));
                res.add(vol);
            }
        }

        logger.info(String.format("[vc] found %d left-over volumes", res.size()));
        return res;
    }

    private void fixupLeftOverVolumes(Map<String, VCenterPrimaryStorageVO> psvos,
                                      Map<String, List<String>> psVolumeDicts,
                                      List<String> leftOvers,
                                      String vcUuid,
                                      ServiceInstance si) throws RemoteException {
        if (leftOvers.isEmpty()) {
            return;
        }

        logger.info(String.format("[vc] fixup leftover volumes, count = %d, vcUuid = %s", leftOvers.size(), vcUuid));
        Map<String, String> vmVolDict = new HashMap<>();
        Q.New(VolumeVO.class)
                .select(VolumeVO_.uuid, VolumeVO_.vmInstanceUuid)
                .in(VolumeVO_.uuid, leftOvers)
                .listTuple()
                .forEach(t -> vmVolDict.put(t.get(1, String.class), t.get(0, String.class)));

        final ManagedEntity[] mes = new InventoryNavigator(si.getRootFolder()).searchManagedEntities("VirtualMachine");
        for (ManagedEntity me : mes) {
            VirtualMachine vm = (VirtualMachine) me;
            String zsVmUuid = VMwareHelper.getZstackVmUuid(vm);
            if (zsVmUuid == null || !vmVolDict.containsKey(zsVmUuid)) {
                continue;
            }

            String dsMorVal = VMwareHelper.getVmDatastoreMorVal(vm);
            VCenterPrimaryStorageVO psvo = psvos.getOrDefault(dsMorVal, null);
            if (psvo == null) {
                throw new OperationFailureException(operr("Datastore %s not found for vCenter %s", dsMorVal, vcUuid));
            }

            List<String> vols = psVolumeDicts.get(psvo.getUuid());
            vols.add(vmVolDict.get(zsVmUuid));
        }
    }

    /**
     * Generates the primary & backup storage records for given datastore
     * <p>
     * 1. Generate new PrimaryStorageVO & BackupStorageVO with datastore MorVal
     * 2. Update existing volumes & images that belongs to this datastore
     * 3. Remove the old PrimaryStorageVO & BackupStorageVO.
     * <p>
     * Do above database operations within SQLBatch.
     *
     * @param vcvo The vCenterVO
     * @param si   The service instance
     */
    private void generateDataStores(final VCenterVO vcvo, ServiceInstance si) throws RemoteException {
        final Map<String, Datastore> stores = VMwareHelper.getVmwareHelper().listDataStores(si);
        final Map<String, VCenterPrimaryStorageVO> psvos = new HashMap<>();
        final List<PrimaryStorageCapacityVO> psCapVOs = new ArrayList<>();
        final Map<String, VCenterBackupStorageVO> bsvos = new HashMap<>();
        final List<BackupStorageZoneRefVO> bsZoneRefs = new ArrayList<>();
        prepareStorageVOs(stores, vcvo, psvos, psCapVOs, bsvos, bsZoneRefs);

        Map<String, List<String>> bsImageDicts = new HashMap<>();
        Map<String, List<String>> psVolumeDicts = new HashMap<>();

        final List<String> vcvms = VMwareHelper.listVCenterVMs(si);

        for (Map.Entry<String, Datastore> entry : stores.entrySet()) {
            final Datastore ds = entry.getValue();
            final String morval = entry.getKey();
            final List<String> imageUuids = new ArrayList<>();
            final List<String> volumeUuids = new ArrayList<>();

            final VirtualMachine[] vms = ds.getVms();
            if (vms == null) {
                continue;
            }

            for (VirtualMachine vm : vms) {
                if (vcvms.contains(vm.getMOR().getVal())) {
                    continue;
                }

                final String zsVmUuid = VMwareHelper.getZstackVmUuid(vm);
                if (zsVmUuid == null) {
                    continue;
                }

                // We only handle existing volume/image records in ZStack.
                if (vm.getConfig().isTemplate()) {
                    tryAddImage(zsVmUuid, imageUuids);
                } else {
                    tryAddVolume(zsVmUuid, volumeUuids);
                }
            }

            final VCenterBackupStorageVO bsvo = bsvos.get(morval);
            final VCenterPrimaryStorageVO psvo = psvos.get(morval);
            bsImageDicts.put(bsvo.getUuid(), imageUuids);
            psVolumeDicts.put(psvo.getUuid(), volumeUuids);
        }

        final ManagedEntity[] mes = new InventoryNavigator(si.getRootFolder()).searchManagedEntities("ClusterComputeResource");
        final List<ClusterComputeResource> clusterComputeResources = VMwareHelper.fromManagedEntity(mes);
        final List<PrimaryStorageClusterRefVO> psClusterRefs = new ArrayList<>();
        for (ClusterComputeResource res : clusterComputeResources) {
            // we need to generate primary storage cluster reference
            String clusterUuid = Q.New(VCenterClusterVO.class)
                    .eq(VCenterClusterVO_.vCenterUuid, vcvo.getUuid())
                    .eq(VCenterClusterVO_.morval, res.getMOR().getVal())
                    .select(VCenterClusterVO_.uuid)
                    .findValue();
            // This a new cluster.  We let it be handled by syncVCenter.
            if (clusterUuid == null) {
                continue;
            }
            psClusterRefs.addAll(buildPsClusterRef(res.getDatastores(), clusterUuid, psvos));
        }

        // To guarantee that all volumes are proceeded
        List<String> leftOverVolumes = findLeftOverVolumes(psVolumeDicts, vcvo.getUuid());
        fixupLeftOverVolumes(psvos, psVolumeDicts, leftOverVolumes, vcvo.getUuid(), si);

        new SQLBatch() {
            @Override
            protected void scripts() {
                bsvos.values().forEach(bsvo -> {
                    dbf.getEntityManager().persist(bsvo);

                    BackupStorageZoneRefVO rvo = new BackupStorageZoneRefVO();
                    rvo.setBackupStorageUuid(bsvo.getUuid());
                    rvo.setZoneUuid(vcvo.getZoneUuid());
                    dbf.getEntityManager().persist(rvo);

                    List<String> imageUuids = bsImageDicts.get(bsvo.getUuid());
                    if (!imageUuids.isEmpty()) {
                        logger.info(String.format("[vc] update %d image records to bsUuid: %s",
                                imageUuids.size(), bsvo.getUuid()));

                        sql(ImageBackupStorageRefVO.class)
                                .in(ImageBackupStorageRefVO_.imageUuid, imageUuids)
                                .set(ImageBackupStorageRefVO_.backupStorageUuid, bsvo.getUuid())
                                .update();
                    }
                });

                psvos.values().forEach(vo -> dbf.getEntityManager().persist(vo));
                psCapVOs.forEach(vo -> dbf.getEntityManager().persist(vo));
                psClusterRefs.forEach(vo -> dbf.getEntityManager().persist(vo));

                for (Map.Entry<String, List<String>> entry : psVolumeDicts.entrySet()) {
                    String psUuid = entry.getKey();
                    List<String> volumeUuids = entry.getValue();
                    if (volumeUuids.isEmpty()) {
                        continue;
                    }

                    logger.info(String.format("[vc] update %d volume records to psUuid: %s",
                            volumeUuids.size(), psUuid));
                    sql(VolumeVO.class)
                            .in(VolumeVO_.uuid, volumeUuids)
                            .set(VolumeVO_.primaryStorageUuid, psUuid)
                            .update();
                }

                // BackupStorageZoneRefVO has cascade to bsUuid
                sql(VCenterBackupStorageVO.class)
                        .eq(VCenterBackupStorageVO_.vCenterUuid, vcvo.getUuid())
                        .isNull(VCenterBackupStorageVO_.datastore)
                        .delete();

                // PrimaryStorageClusterRefVO has cascade to psUuid
                sql(VCenterPrimaryStorageVO.class)
                        .eq(VCenterPrimaryStorageVO_.vCenterUuid, vcvo.getUuid())
                        .isNull(VCenterPrimaryStorageVO_.datastore)
                        .delete();

                flush();
            }
        }.execute();

        dbf.eoCleanup(VCenterBackupStorageVO.class);
        dbf.eoCleanup(VCenterPrimaryStorageVO.class);
    }

    private void tryAddImage(String imageUuid, List<String> imageUuids) {
        if (dbf.isExist(imageUuid, ImageVO.class)) {
            imageUuids.add(imageUuid);
        }
    }

    private void tryAddVolume(String vmUuid, List<String> volumeUuids) {
        String volumeUuid = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, vmUuid)
                .eq(VolumeVO_.type, VolumeType.Root)
                .select(VolumeVO_.uuid)
                .findValue();
        if (volumeUuid != null) {
            volumeUuids.add(volumeUuid);
        }
    }

    @Override
    public boolean start() {
        VCenterGlobalConfig.VCENTER_SYNC_INTERVAL.installValidateExtension(new GlobalConfigValidatorExtensionPoint() {
            @Override
            public void validateGlobalConfig(String category, String name, String oldValue, String newValue) throws GlobalConfigException {
                long value = Long.parseLong(newValue);
                if (value < 0) {
                    throw new GlobalConfigException(String.format("the value[%s] is lesser than 0", newValue));
                } else if (value > 0 && value < 3600) {
                    throw new GlobalConfigException(String.format("Sync vcenter data interval must be not less than 3600s" + " but got %s", value));
                }
            }
        });

        return true;
    }

    @Override
    public boolean stop() {
        for (Map.Entry<String, VMwareResourceMonitor> entry : instancePool.entrySet()) {
            entry.getValue().stop();
        }
        VCenterMetricsReader.stopAllVCenterMetricsTask();
        return true;
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(SERVICE_ID);
    }

    @Override
    public void managementNodeReady() {
        logger.debug(String.format("Management node[uuid:%s] joins, start monitoring vCenter", Platform.getManagementServerId()));
        loadVCenter();
    }

    @Override
    public void nodeJoin(ManagementNodeInventory inv) {
    }

    @Override
    public void nodeLeft(ManagementNodeInventory inv) {
        logger.debug(String.format("Management node[uuid:%s] left, node[uuid:%s] starts to monitor vCenter", inv.getUuid(), Platform.getManagementServerId()));
        loadVCenter();
    }

    @Override
    public void iAmDead(ManagementNodeInventory inv) {
    }

    @Override
    public void iJoin(ManagementNodeInventory inv) {
    }

    @AsyncThread
    private void loadVCenter() {
        List<VCenterVO> vcvos = dbf.listAll(VCenterVO.class);
        for (VCenterVO vcvo : vcvos) {
            if (!destMaker.isManagedByUs(vcvo.getUuid())) {
                logger.debug(String.format("vCenter [name:%s] is not managed by us", vcvo.getDomainName()));
                continue;
            }

            checkStatus(vcvo);
            tryUpgradeStorage(vcvo);
            launchMonitor(vcvo);
            VCenterMetricsReader.startVCenterMetricsTask(vcvo.getUuid());
        }
    }

    private void checkStatus(VCenterVO vcvo) {
        try (VCenterServiceInstance si = VMwareHelper.getVCenterServiceInstance(vcvo)) {
            logger.info(String.format("get vCenter[%s] version: %s", vcvo.getUuid(), si.getAboutInfo().getVersion()));

            if (!vcvo.getStatus().equals(VCenterStatus.Connected)) {
                logger.info(String.format("change vCenter status from %s to %s", vcvo.getStatus(), VCenterStatus.Connected.toString()));
                vcvo.setStatus(VCenterStatus.Connected);
                dbf.updateAndRefresh(vcvo);
            }
        } catch (Throwable ex) {
            logger.warn(String.format("check vCenter[%s] version failed, set Disconnected status", vcvo.getUuid()));
            vcvo.setStatus(VCenterStatus.Disconnected);
            dbf.updateAndRefresh(vcvo);
        }
    }

    @Override
    public void releaseVmResource(VmInstanceSpec spec, Completion completion) {
        VmInstanceInventory vmInv = spec.getVmInventory();
        if (spec.getCurrentVmOperation() != VmInstanceConstant.VmOperation.Destroy) {
            completion.success();
            return;
        }

        if (!ESXConstant.VMWARE_HYPERVISOR_TYPE.equals(vmInv.getHypervisorType())) {
            completion.success();
            return;
        }
        if (vmInv.getAllVolumes().size() <= 1) {
            completion.success();
            return;
        }

        List<VolumeInventory> dataInvs = vmInv.getAllVolumes().stream()
                .filter(vol -> vol.getType().equals(VolumeType.Data.toString()))
                .collect(Collectors.toList());

        List<DetachVolumeFromVmOnHypervisorMsg> dmsgs = new ArrayList<>();
        String hostUuid = vmInv.getHostUuid() == null ? vmInv.getLastHostUuid() : vmInv.getHostUuid();
        dataInvs.forEach(dataInv -> {
            DetachVolumeFromVmOnHypervisorMsg dmsg = new DetachVolumeFromVmOnHypervisorMsg();
            dmsg.setVmInventory(vmInv);
            dmsg.setInventory(dataInv);
            dmsg.setHostUuid(hostUuid);
            bus.makeTargetServiceIdByResourceUuid(dmsg, HostConstant.SERVICE_ID, hostUuid);
            dmsgs.add(dmsg);
        });

        if (dmsgs.isEmpty()) {
            completion.success();
            return;
        }

        List<MessageReply> replys = bus.call(dmsgs);
        for (MessageReply reply : replys) {
            if (!reply.isSuccess()) {
                completion.fail(reply.getError());
                return;
            }
        }
        completion.success();
    }

    private void retryEnableVncOnHost(String hostUuid) {
        thdf.submitCancelablePeriodicTask(new CancelablePeriodicTask() {
            @Override
            public boolean run() {
                ESXHostVO hostVO = Q.New(ESXHostVO.class).eq(ESXHostVO_.uuid, hostUuid).find();
                if (hostVO == null) {
                    return true;
                }
                VCenterVO vcVo = Q.New(VCenterVO.class).eq(VCenterVO_.uuid, hostVO.getvCenterUuid()).find();
                if (vcVo == null) {
                    return true;
                }

                boolean success = true;
                try (VCenterServiceInstance si = VMwareHelper.getVCenterServiceInstance(vcVo)) {
                    ServerConnection conn = si.getServerConnection();

                    HostSystem host = VMwareHelper.getVmwareHelper().getHost(conn, hostVO.getMorval());
                    if (host == null) {
                        logger.warn(String.format("[vc] ESX host[uuid: %s] not found on vCenter[uuid: %s]", hostVO.getUuid(), vcVo.getUuid()));
                        return false;
                    }

                    if (!VMwareHelper.getVmwareHelper().isHostConnected(host)) {
                        logger.warn(String.format("[vc] ESX host[uuid: %s] is not connected", hostVO.getUuid()));
                        return false;
                    }

                    VMwareHelper.enableVncOnHost(host);
                } catch (Throwable ex) {
                    logger.warn("[vc] failed to enable VNC for ESX host: " + hostVO.getUuid(), ex);
                    success = false;
                }

                return success;
            }

            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.SECONDS;
            }

            @Override
            public long getInterval() {
                return 60;
            }

            @Override
            public String getName() {
                return "enable-vnc-port-of-ESX-" + hostUuid;
            }
        });
    }

    @AsyncThread
    private void enableVncForHosts(String vcUuid) {
        VCenterVO vcvo = dbf.findByUuid(vcUuid, VCenterVO.class);

        List<Tuple> hosts = Q.New(ESXHostVO.class)
                .eq(ESXHostVO_.vCenterUuid, vcUuid)
                .select(ESXHostVO_.uuid, ESXHostVO_.morval)
                .listTuple();
        try (VCenterServiceInstance si = VMwareHelper.getVCenterServiceInstance(vcvo)) {
            for (Tuple t : hosts) {
                final String huuid = t.get(0, String.class);
                final String morval = t.get(1, String.class);

                try {
                    HostSystem host = VMwareHelper.getVmwareHelper().getHost(si.getServerConnection(), morval);
                    if (host == null || !VMwareHelper.getVmwareHelper().isHostConnected(host)) {
                        logger.warn(String.format("[vc] ESX host[uuid: %s] is not connected", huuid));
                        continue;
                    }

                    VMwareHelper.enableVncOnHost(host);
                } catch (Throwable ex) {
                    logger.warn("[vc] failed to enable VNC for ESX host: " + huuid, ex);
                    retryEnableVncOnHost(huuid);
                }
            }
        }
    }

    private void handle(AllocateVncPortMsg msg) {
        final AllocateVncPortReply reply = new AllocateVncPortReply();
        final String esxHost = msg.getHostUuid();

        if (esxHost == null) {
            reply.setError(operr("Missing host UUID in message"));
            bus.reply(msg, reply);
            return;
        }

        portAllocator.allocatePort(esxHost, msg.getPreferred(), new ReturnValueCompletion<Integer>(msg) {
            @Override
            public void success(Integer returnValue) {
                reply.setPort(returnValue);
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void stopMonitorForVC(final String vcUuid) {
        VMwareResourceMonitor mon = instancePool.getOrDefault(vcUuid, null);
        if (mon != null) {
            mon.stop();
            instancePool.remove(vcUuid);
        }
    }

    private void handle(VCenterDeletionMsg msg) {
        // Stopping vCenter monitor
        stopMonitorForVC(msg.getVCenterUuid());
        VCenterMetricsReader.stopVCenterMetricsTask(msg.getVCenterUuid());

        VCenterDeletionReply reply = new VCenterDeletionReply();
        bus.reply(msg, reply);
    }

    private void handle(final SyncVCenterMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getVCenterSyncSignature(msg);
            }

            @Override
            public void run(SyncTaskChain chain) {
                syncVCenter(msg.getVCenterUuid(), msg.getAccountUuid(), new Completion(chain, msg) {
                    final MessageReply reply = new MessageReply();

                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("sync-vcenter-%s", msg.getAccountUuid());
            }
        });
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        return Collections.singletonList(APIMigrateVmMsg.class);
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.END;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIMigrateVmMsg) {
            validate((APIMigrateVmMsg) msg);
        }

        return msg;
    }

    private void validate(final APIMigrateVmMsg msg) {
        String hypervisorType = Q.New(VmInstanceVO.class).select(VmInstanceVO_.hypervisorType)
                .eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid())
                .findValue();
        if (!ESXConstant.VMWARE_HYPERVISOR_TYPE.equals(hypervisorType)) {
            return;
        }

        if (msg.getHostUuid() == null) {
            throw new ApiMessageInterceptionException(argerr("Missing destination host uuid."));
        }

        ESXHostVO hvo = dbf.findByUuid(msg.getHostUuid(), ESXHostVO.class);
        if (hvo == null) {
            throw new ApiMessageInterceptionException(argerr("Destination host is not ESX host."));
        }

        VCenterVO vcvo = dbf.findByUuid(hvo.getvCenterUuid(), VCenterVO.class);
        final String vmUuid = msg.getVmUuid();

        try (VCenterServiceInstance si = VMwareHelper.getVCenterServiceInstance(vcvo)) {
            VirtualMachine vm = VMwareHelper.findVmWithZsUuid(
                    si,
                    vmUuid,
                    false
            );

            if (vm == null) {
                String info = String.format("VM [%s] not found in vCenter", vmUuid);
                throw new ApiMessageInterceptionException(
                        operr(info));
            }

            HostSystem host = VMwareHelper.getVmwareHelper().getHost(si.getServerConnection(), hvo.getMorval());
            VirtualMachineProvisioningChecker checker = new VirtualMachineProvisioningChecker(si.getServerConnection(), si.getServiceContent().getVmProvisioningChecker());
            try {
                Task task = checker.queryVMotionCompatibilityEx_Task(new VirtualMachine[]{vm}, new HostSystem[]{host});
                String status = task.waitForTask();
                if (status.equals(Task.SUCCESS)) {
                    ArrayOfCheckResult result = (ArrayOfCheckResult) task.getTaskInfo().getResult();
                    CheckResult[] cr = result.getCheckResult();
                    if (cr[0] != null) {
                        if (cr[0].getError() == null) {
                            return;
                        }
                        throw new ApiMessageInterceptionException(operr(cr[0].getError()[0].localizedMessage));
                    }
                }
                throw new ApiMessageInterceptionException(
                        operr("HOST CPU/software NOT compatible"));
            } catch (RemoteException | InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ApiMessageInterceptionException(
                        operr("Checking compatibility with vm %s failed on host %s", vm.getConfig().getName(), hvo.getManagementIp()));
            }
        }
    }

    @Override
    public void checkVmCapability(VmInstanceInventory inv, VmCapabilities capabilities) {
        //TODO use vCenter api check migration capability
    }

    @Override
    public List<L3NetworkInventory> afterFilterByImage(List<L3NetworkInventory> l3s, List<String> bsUuids, String imageUuid) {
        ImageVO image = dbf.findByUuid(imageUuid, ImageVO.class);

        if (!image.getFormat().equals(VMwareHelper.vmwareTemplateFormat)) {
            return l3s;
        }

        List<String> l3Uuids = new SQLBatchWithReturn<List<String>>() {
            @Override
            protected List<String> scripts() {
                return sql("select l3.uuid from L3NetworkVO l3, VCenterBackupStorageVO bs, " +
                        "VCenterClusterVO cluster, L2NetworkClusterRefVO l2Ref where bs.uuid in (:bsUuids) " +
                        "and bs.vCenterUuid = cluster.vCenterUuid " +
                        "and l3.l2NetworkUuid = l2Ref.l2NetworkUuid and l2Ref.clusterUuid = cluster.uuid")
                        .param("bsUuids", bsUuids)
                        .list();
            }
        }.execute();

        return l3s.stream().filter(l3 -> l3Uuids.contains(l3.getUuid())).collect(Collectors.toList());
    }

    @Override
    public void vmJustBeforeDeleteFromDb(VmInstanceInventory inv) {
        if (destMaker.isManagedByUs(inv.getUuid()) && ESXConstant.VMWARE_HYPERVISOR_TYPE.equals(inv.getHypervisorType())) {
            SQL.New(VCenterResourcePoolUsageVO.class).eq(VCenterResourcePoolUsageVO_.resourceType, VmInstanceVO.class.getSimpleName())
                    .eq(VCenterResourcePoolUsageVO_.resourceUuid, inv.getUuid()).delete();
        }

    }

    @Override
    public ErrorCode validateDetachNicByDriverTypeAndClusterType(L3NetworkInventory l3, VmInstanceInventory vm) {
        for (VmNicInventory nic : vm.getVmNics()) {
            if (nic.getL3NetworkUuid().equals(l3.getUuid())) {
                final VCenterClusterVO vCenterClusterVO = SQL.New("select vcc from VCenterClusterVO vcc, L3NetworkVO l3, L2NetworkClusterRefVO ref "
                        + "where l3.uuid = :l3Uuid "
                        + "and l3.l2NetworkUuid = ref.l2NetworkUuid "
                        + "and ref.clusterUuid = vcc.uuid", VCenterClusterVO.class)
                        .param("l3Uuid", l3.getUuid())
                        .find();
                if (vCenterClusterVO == null) {
                    return null;
                }

                if (nic.getDriverType().equals(nicManager.getDefaultPVNicDriver()) ||
                        nic.getDriverType().equals(vMwareVmNicManager.getDefaultPVNicDriver())) {
                    return operr("Can't detach nic because the nic not supported to hot plugin in vcenter");
                }
            }
        }
        return null;
    }
}
