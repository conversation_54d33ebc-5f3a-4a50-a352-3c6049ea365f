package org.zstack.vmware.cascade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.compute.vm.VmInstanceExtensionPointEmitter;
import org.zstack.core.cascade.*;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusListCallBack;
import org.zstack.core.db.*;
import org.zstack.core.errorcode.ErrorFacade;
import org.zstack.header.cluster.ClusterInventory;
import org.zstack.header.cluster.ClusterVO;
import org.zstack.header.configuration.DiskOfferingInventory;
import org.zstack.header.configuration.DiskOfferingVO;
import org.zstack.header.core.Completion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.identity.AccountResourceRefVO;
import org.zstack.header.identity.AccountResourceRefVO_;
import org.zstack.header.identity.AccountVO;
import org.zstack.header.image.*;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.*;
import org.zstack.header.storage.backup.BackupStorageInventory;
import org.zstack.header.storage.primary.PrimaryStorageAO;
import org.zstack.header.storage.primary.PrimaryStorageAO_;
import org.zstack.header.storage.primary.PrimaryStorageEO;
import org.zstack.header.storage.primary.PrimaryStorageInventory;
import org.zstack.header.vm.*;
import org.zstack.header.volume.*;
import org.zstack.header.zone.ZoneInventory;
import org.zstack.header.zone.ZoneVO;
import org.zstack.network.l2.L2NetworkExtensionPointEmitter;
import org.zstack.sns.SNSTopicInventory;
import org.zstack.storage.volume.VolumeGlobalConfig;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;
import org.zstack.vmware.*;

import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.getUuid;
import static org.zstack.core.Platform.inerr;
import static org.zstack.core.Platform.operr;

/**
 * Created by david on 11/7/16.
 */
public class VCenterCascadeAddOnExtensionPoint implements CascadeAddOnExtensionPoint {
    private static final CLogger logger = Utils.getLogger(VCenterCascadeAddOnExtensionPoint.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private VmInstanceDeletionPolicyManager deletionPolicyManager;
    @Autowired
    private L2NetworkExtensionPointEmitter extpEmitter;
    @Autowired
    private CloudBus bus;
    @Autowired
    private ErrorFacade errf;
    @Autowired
    private VmInstanceExtensionPointEmitter extEmitter;

    @Override
    public CascadeExtensionPoint cascadeAddOn(String resourceName) {
        if (VmInstanceVO.class.getSimpleName().equals(resourceName)) {
            return new CascadeExtensionPoint() {
                private static final int OP_NOPE = 0;
                private static final int OP_STOP = 1;
                private static final int OP_DELETION = 2;

                @Override
                public void syncCascade(CascadeAction action) throws CascadeException {
                }

                private int toDeletionOpCode(CascadeAction action) {
                    if (!CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
                        return OP_NOPE;
                    }

                    if (ESXHostVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        if (VCenterVO.class.getSimpleName().equals(action.getRootIssuer())
                                || VCenterClusterVO.class.getSimpleName().equals(action.getRootIssuer())) {
                            return OP_DELETION;
                        } else {
                            return OP_STOP;
                        }
                    }

                    return OP_NOPE;
                }

                @Override
                public void asyncCascade(CascadeAction action, Completion completion) {
                    if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
                        handleDeletionCheck(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                        handleDeletion(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
                        handleDeletionCleanup(action, completion);
                    } else {
                        completion.success();
                    }
                }

                private void handleDeletionCleanup(CascadeAction action, Completion completion) {
                    try {
                        dbf.eoCleanup(VmInstanceVO.class);
                        completion.success();
                    } catch (Exception e) {
                        logger.debug(String.format("exception :%s %s \n %s",e.getMessage(), e.toString(), DebugUtils.getStackTrace(e)));
                        completion.fail(operr(e.getMessage()));
                    }
                }

                private void handleDeletion(final CascadeAction action, final Completion completion) {
                    int op = toDeletionOpCode(action);
                    if (op == OP_NOPE) {
                        completion.success();
                        return;
                    }

                    final List<VmDeletionStruct> vminvs = vmsFromAction(action);
                    if (vminvs == null || vminvs.isEmpty()) {
                        completion.success();
                        return;
                    }

                    if (op == OP_STOP) {
                        List<StopVmInstanceMsg> msgs = new ArrayList<>();
                        List<String> vmStateCanStop = Arrays.asList(
                                VmInstanceState.Unknown.toString(),
                                VmInstanceState.Stopped.toString(),
                                VmInstanceState.Running.toString());
                        for (VmDeletionStruct inv : vminvs) {
                            if (!vmStateCanStop.stream().anyMatch(
                                    str -> str.trim().equals(inv.getInventory().getState()))) {
                                continue;
                            }
                            StopVmInstanceMsg msg = new StopVmInstanceMsg();
                            msg.setVmInstanceUuid(inv.getInventory().getUuid());
                            msg.setGcOnFailure(true);
                            bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, inv.getInventory().getUuid());
                            msgs.add(msg);
                        }

                        if (msgs.isEmpty()) {
                            completion.success();
                            return;
                        }

                        bus.send(msgs, 20, new CloudBusListCallBack(completion) {
                            @Override
                            public void run(List<MessageReply> replies) {
                                if (!action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                                    for (MessageReply r : replies) {
                                        if (!r.isSuccess()) {
                                            completion.fail(r.getError());
                                            return;
                                        }
                                    }
                                }

                                completion.success();
                            }
                        });
                    } else if (op == OP_DELETION) {
                        List<VmInstanceDeletionMsg> msgs = new ArrayList<>();
                        for (VmDeletionStruct inv : vminvs) {
                            VmInstanceDeletionMsg msg = new VmInstanceDeletionMsg();
                            msg.setForceDelete(action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE));
                            msg.setVmInstanceUuid(inv.getInventory().getUuid());
                            msg.setDeletionPolicy(VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.DBOnly.toString());
                            bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, inv.getInventory().getUuid());
                            msgs.add(msg);
                        }

                        bus.send(msgs, 20, new CloudBusListCallBack(completion) {
                            @Override
                            public void run(List<MessageReply> replies) {
                                if (!action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                                    for (MessageReply r : replies) {
                                        if (!r.isSuccess()) {
                                            logger.warn(r.getError().toString());
                                        }
                                    }
                                }

                                completion.success();
                            }
                        });
                    }
                }

                private void handleDeletionCheck(CascadeAction action, Completion completion) {
                    int op = toDeletionOpCode(action);
                    if (op == OP_NOPE || op == OP_STOP) {
                        completion.success();
                        return;
                    }

                    List<VmDeletionStruct> vminvs = vmsFromAction(action);
                    if (vminvs == null) {
                        completion.success();
                        return;
                    }

                    for (VmDeletionStruct inv : vminvs) {
                        ErrorCode err = extEmitter.preDestroyVm(inv.getInventory());
                        if (err != null) {
                            completion.fail(err);
                            return;
                        }
                    }

                    completion.success();
                }

                @Override
                public List<String> getEdgeNames() {
                    return Collections.singletonList(ESXHostVO.class.getSimpleName());
                }

                @Override
                public String getCascadeResourceName() {
                    return VmInstanceVO.class.getSimpleName();
                }

                @Override
                public CascadeAction createActionForChildResource(CascadeAction action) {
                    if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
                        int op = toDeletionOpCode(action);
                        if (op == OP_NOPE || op == OP_STOP) {
                            return null;
                        }

                        List<VmDeletionStruct> vms = vmsFromAction(action);
                        if (vms == null) {
                            return null;
                        }

                        return action.copy()
                                .setParentIssuer(VmInstanceVO.class.getSimpleName())
                                .setParentIssuerContext(vms);
                    }

                    return null;
                }

                private List<VmDeletionStruct> vmsFromAction(CascadeAction action) {
                    if (ESXHostVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        List<String> hostUuids = CollectionUtils.transformToList((List<ESXHostInventory>) action.getParentIssuerContext(), new Function<String, ESXHostInventory>() {
                            @Override
                            public String call(ESXHostInventory arg) {
                                return arg.getUuid();
                            }
                        });

                        List<VmInstanceVO> vms = new Callable<List<VmInstanceVO>>() {
                            @Override
                            @Transactional(readOnly = true)
                            public List<VmInstanceVO> call() {
                                final String sql = "select vm from VmInstanceVO vm"
                                        + " where vm.hostUuid in (:hostUuids)"
                                        + " or vm.lastHostUuid in (:hostUuids)";
                                TypedQuery<VmInstanceVO> q = dbf.getEntityManager().createQuery(
                                        sql, VmInstanceVO.class);
                                q.setParameter("hostUuids", hostUuids);
                                return q.getResultList();
                            }
                        }.call();

                        if (VCenterVO.class.getSimpleName().equals(action.getRootIssuer())) {
                            List<VCenterInventory> vcs = action.getRootIssuerContext();
                            List<String> vCenterUuids = vcs.stream().map(VCenterInventory::getUuid).collect(Collectors.toList());
                            List<String> clusterUuids = Q.New(VCenterClusterVO.class)
                                    .select(VCenterClusterVO_.uuid)
                                    .in(VCenterClusterVO_.vCenterUuid, vCenterUuids)
                                    .listValues();
                            if (!clusterUuids.isEmpty()) {
                                List<VmInstanceVO> vmInstanceVOS = Q.New(VmInstanceVO.class)
                                        .in(VmInstanceVO_.clusterUuid, clusterUuids)
                                        .eq(VmInstanceVO_.type, VmInstanceConstant.USER_VM_TYPE)
                                        .list();
                                vms.addAll(vmInstanceVOS);
                            }
                        } else if (VCenterClusterVO.class.getSimpleName().equals(action.getRootIssuer())) {
                            List<VCenterClusterInventory> clusters = action.getRootIssuerContext();
                            List<String> clusterUuids = clusters.stream().map(VCenterClusterInventory::getUuid).collect(Collectors.toList());

                            List<VmInstanceVO> vmInstanceVOS = Q.New(VmInstanceVO.class)
                                    .in(VmInstanceVO_.clusterUuid, clusterUuids)
                                    .eq(VmInstanceVO_.type, VmInstanceConstant.USER_VM_TYPE)
                                    .list();
                            vms.addAll(vmInstanceVOS);
                        } else if (ZoneVO.class.getSimpleName().equals(action.getRootIssuer())) {
                            List<ZoneInventory> zones = action.getRootIssuerContext();
                            List<String> zoneUuids = zones.stream().map(ZoneInventory::getUuid).collect(Collectors.toList());

                            List<VmInstanceVO> vmInstanceVOS = Q.New(VmInstanceVO.class)
                                    .in(VmInstanceVO_.zoneUuid, zoneUuids)
                                    .eq(VmInstanceVO_.type, VmInstanceConstant.USER_VM_TYPE)
                                    .list();
                            vms.addAll(vmInstanceVOS);
                        }

                        if (!vms.isEmpty()) {
                            Map<String, VmInstanceVO> vmvos = new HashMap<>();
                            for (VmInstanceVO vmInstanceVO : vms) {
                                vmvos.put(vmInstanceVO.getUuid(), vmInstanceVO);
                            }
                            return toVmDeletionStruct(vmvos.values());
                        }

                    } else if (VmInstanceVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        return action.getParentIssuerContext();
                    }

                    return null;
                }

                private List<VmDeletionStruct> toVmDeletionStruct(Collection<VmInstanceVO> vos) {
                    List<VmDeletionStruct> structs = new ArrayList<>();
                    for (VmInstanceVO vo : vos) {
                        VmDeletionStruct s = new VmDeletionStruct();
                        s.setInventory(VmInstanceInventory.valueOf(vo));
                        s.setDeletionPolicy(deletionPolicyManager.getDeletionPolicy(vo.getUuid()));
                        structs.add(s);
                    }
                    return structs;
                }
            };
        }

        if (L2NetworkVO.class.getSimpleName().equals(resourceName)) {
            return new CascadeExtensionPoint() {
                @Override
                public void syncCascade(CascadeAction action) throws CascadeException {
                }

                @Override
                public void asyncCascade(CascadeAction action, Completion completion) {
                    if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
                        handleDeletionCheck(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                        handleDeletion(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
                        handleDeletionCleanup(action, completion);
                    } else {
                        completion.success();
                    }
                }

                private void handleDeletionCleanup(CascadeAction action, Completion completion) {
                    try {
                        dbf.eoCleanup(L2NetworkVO.class);
                        completion.success();
                    } catch (Exception e) {logger.debug(String.format("exception :%s %s \n %s",e.getMessage(), e.toString(), DebugUtils.getStackTrace(e)));
                        completion.fail(operr(e.getMessage()));
                    }
                }

                private void handleDeletion(final CascadeAction action, final Completion completion) {
                    final List<L2NetworkInventory> l2invs = l2networksFromAction(action);
                    if (l2invs == null) {
                        completion.success();
                        return;
                    }

                    List<L2NetworkDeletionMsg> msgs = new ArrayList<L2NetworkDeletionMsg>();
                    for (L2NetworkInventory l2inv : l2invs) {
                        L2NetworkDeletionMsg msg = new L2NetworkDeletionMsg();
                        msg.setForceDelete(action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE));
                        msg.setL2NetworkUuid(l2inv.getUuid());
                        bus.makeTargetServiceIdByResourceUuid(msg, L2NetworkConstant.SERVICE_ID, l2inv.getUuid());
                        msgs.add(msg);
                    }

                    bus.send(msgs, new CloudBusListCallBack(completion) {
                        @Override
                        public void run(List<MessageReply> replies) {
                            if (!action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                                for (MessageReply r : replies) {
                                    if (!r.isSuccess()) {
                                        completion.fail(r.getError());
                                        return;
                                    }
                                }
                            }

                            List<String> uuids = new ArrayList<String>();
                            for (MessageReply r : replies) {
                                L2NetworkInventory inv = l2invs.get(replies.indexOf(r));
                                uuids.add(inv.getUuid());
                                logger.debug(String.format("delete backup storage[uuid:%s, name:%s]", inv.getUuid(), inv.getName()));
                            }

                            dbf.removeByPrimaryKeys(uuids, L2NetworkVO.class);
                            completion.success();
                        }
                    });
                }

                private void handleDeletionCheck(CascadeAction action, Completion completion) {
                    List<L2NetworkInventory> l2invs = l2networksFromAction(action);
                    if (l2invs == null) {
                        completion.success();
                        return;
                    }

                    try {
                        for (L2NetworkInventory prinv : l2invs) {
                            extpEmitter.preDelete(prinv);
                        }

                        completion.success();
                    } catch (L2NetworkException e) {
                        completion.fail(inerr(e.getMessage()));
                    }
                }

                @Override
                public List<String> getEdgeNames() {
                    return Collections.singletonList(VCenterVO.class.getSimpleName());
                }

                @Override
                public String getCascadeResourceName() {
                    return L2NetworkVO.class.getSimpleName();
                }

                @Override
                public CascadeAction createActionForChildResource(CascadeAction action) {
                    if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
                        List<L2NetworkInventory> ctx = l2networksFromAction(action);
                        if (ctx != null) {
                            return action.copy()
                                    .setParentIssuer(L2NetworkVO.class.getSimpleName())
                                    .setParentIssuerContext(ctx);
                        }
                    }

                    return null;
                }

                private List<L2NetworkInventory> l2networksFromAction(CascadeAction action) {
                    if (VCenterVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        List<VCenterInventory> vcs = (List<VCenterInventory>) action.getParentIssuerContext();
                        List<String> zoneUuids = CollectionUtils.transformToList(vcs, new Function<String, VCenterInventory>() {
                            @Override
                            public String call(VCenterInventory arg) {
                                return arg.getZoneUuid();
                            }
                        });

                        Set<String> vcUuids = new HashSet<>();
                        for (VCenterInventory vc: vcs) {
                            vcUuids.add(vc.getUuid());
                        }

                        SimpleQuery<L2NetworkVO> q = dbf.createQuery(L2NetworkVO.class);
                        q.add(L2NetworkVO_.zoneUuid, SimpleQuery.Op.IN, zoneUuids);
                        List<L2NetworkVO> l2vos = q.list();
                        List<L2NetworkVO> targetL2VOs = new ArrayList<>();
                        for (L2NetworkVO vo : l2vos) {
                            String vcUuid = VCenterTagHelper.getVCenterUuidFromL2Network(vo.getUuid());
                            if (vcUuid != null && vcUuids.contains(vcUuid)) {
                                targetL2VOs.add(vo);
                            }
                        }

                        if (!targetL2VOs.isEmpty()) {
                            return L2NetworkInventory.valueOf(targetL2VOs);
                        }

                    } else if (L2NetworkVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        return action.getParentIssuerContext();
                    }

                    return null;
                }
            };
        }

        if (VolumeVO.class.getSimpleName().equals(resourceName)) {
            return new CascadeExtensionPoint() {
                private static final int OP_DELETE_VOLUME = 0;
                private static final int OP_UPDATE_DISK_OFFERING_COLUMN = 1;

                @Override
                public void syncCascade(CascadeAction action) throws CascadeException {
                }

                @Override
                public void asyncCascade(CascadeAction action, Completion completion) {
                    if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
                        handleDeletionCheck(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                        handleDeletion(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
                        handleDeletionCleanup(action, completion);
                    } else {
                        completion.success();
                    }
                }

                private int actionToOpCode(CascadeAction action) {
                    if (action.getParentIssuer().equals(VolumeVO.class.getSimpleName()) || action.getParentIssuer().equals(VCenterPrimaryStorageVO.class.getSimpleName())) {
                        return OP_DELETE_VOLUME;
                    }

                    if (action.getParentIssuer().equals(DiskOfferingVO.class.getSimpleName())) {
                        return OP_UPDATE_DISK_OFFERING_COLUMN;
                    }

                    if (action.getParentIssuer().equals(AccountVO.class.getSimpleName())) {
                        return OP_DELETE_VOLUME;
                    }

                    throw new CloudRuntimeException(String.format("unknown edge [%s]", action.getParentIssuer()));
                }

                private String actionToDeletionPolicy(CascadeAction action, String volUuid) {
                    if (action.getParentIssuer().equals(VCenterPrimaryStorageVO.class.getSimpleName())) {
                        return VolumeDeletionPolicyManager.VolumeDeletionPolicy.DBOnly.toString();
                    } else {
                        return deletionPolicyManager.getDeletionPolicy(volUuid).toString();
                    }
                }

                private void handleDeletionCleanup(CascadeAction action, Completion completion) {
                    try {
                        int op = actionToOpCode(action);
                        if (op != OP_DELETE_VOLUME) {
                            completion.success();
                            return;
                        }

                        List<String> volumes = volumesCleanupFromAction(action);
                        if (volumes == null || volumes.isEmpty()) {
                            completion.success();
                            return;
                        }

                        for (String volume : volumes) {
                            dbf.eoCleanup(VolumeVO.class, volume);
                        }

                        completion.success();
                    } catch (Exception e) {
                        logger.debug(String.format("exception :%s %s \n %s",e.getMessage(), e.toString(), DebugUtils.getStackTrace(e)));
                        completion.fail(operr(e.getMessage()));
                    }
                }

                private List<VolumeDeletionStruct> toVolumeDeletionStruct(CascadeAction action, List<VolumeVO> vos) {
                    List<VolumeDeletionStruct> structs = new ArrayList<>();
                    for (VolumeVO vo : vos) {
                        VolumeDeletionStruct s = new VolumeDeletionStruct();
                        s.setInventory(VolumeInventory.valueOf(vo));
                        s.setDeletionPolicy(actionToDeletionPolicy(action, vo.getUuid()));
                        structs.add(s);
                    }
                    return structs;
                }

                private void handleDeletion(final CascadeAction action, final Completion completion) {
                    int op = actionToOpCode(action);

                    if (op == OP_DELETE_VOLUME) {
                        deleteVolume(action, completion);
                    } else if (op == OP_UPDATE_DISK_OFFERING_COLUMN) {
                        if (VolumeGlobalConfig.UPDATE_DISK_OFFERING_TO_NULL_WHEN_DELETING.value(Boolean.class)) {
                            updateDiskOfferingColumn(action, completion);
                        } else {
                            completion.success();
                        }
                    }
                }

                @Transactional
                private void updateDiskOfferingColumn(CascadeAction action, Completion completion) {
                    List<DiskOfferingInventory> diskOfferingInventories = action.getParentIssuerContext();
                    List<String> diskOfferingUuids = CollectionUtils.transformToList(diskOfferingInventories, new Function<String, DiskOfferingInventory>() {
                        @Override
                        public String call(DiskOfferingInventory arg) {
                            return arg.getUuid();
                        }
                    });

                    String sql = "update VolumeVO vol set vol.diskOfferingUuid = null where vol.diskOfferingUuid in (:uuids)";
                    Query q = dbf.getEntityManager().createQuery(sql);
                    q.setParameter("uuids", diskOfferingUuids);
                    q.executeUpdate();
                    completion.success();
                }

                private void deleteVolume(final CascadeAction action, final Completion completion) {
                    final List<VolumeDeletionStruct> volumes = volumesFromAction(action);
                    if (volumes == null || volumes.isEmpty()) {
                        completion.success();
                        return;
                    }

                    List<VolumeDeletionMsg> msgs = new ArrayList<VolumeDeletionMsg>();
                    for (VolumeDeletionStruct vol : volumes) {
                        VolumeDeletionMsg msg = new VolumeDeletionMsg();
                        msg.setDetachBeforeDeleting(vol.isDetachBeforeDeleting());
                        msg.setForceDelete(action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE));
                        msg.setVolumeUuid(vol.getInventory().getUuid());
                        msg.setDeletionPolicy(vol.getDeletionPolicy());
                        bus.makeTargetServiceIdByResourceUuid(msg, VolumeConstant.SERVICE_ID, vol.getInventory().getUuid());
                        msgs.add(msg);
                    }

                    bus.send(msgs, 20, new CloudBusListCallBack(completion) {
                        @Override
                        public void run(List<MessageReply> replies) {
                            if (!action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                                for (MessageReply r : replies) {
                                    if (!r.isSuccess()) {
                                        completion.fail(r.getError());
                                        return;
                                    }
                                }
                            }

                            if (action.getParentIssuer().equals(VCenterPrimaryStorageVO.class.getSimpleName())) {
                                // when deleting the primary storage, the foreign key of VolumeVO to PrimaryStorageVO
                                // will cause VolumeVO to be deleted but left AccountResourceRefVO of the volume left

                                List<String> volUuids = volumes.stream().map(s -> s.getInventory().getUuid()).collect(Collectors.toList());
                                UpdateQuery q = UpdateQuery.New(AccountResourceRefVO.class);
                                q.condAnd(AccountResourceRefVO_.resourceUuid, SimpleQuery.Op.IN, volUuids);
                                q.condAnd(AccountResourceRefVO_.resourceType, SimpleQuery.Op.EQ, VolumeVO.class.getSimpleName());
                                q.delete();
                            }

                            completion.success();
                        }
                    });
                }

                private void handleDeletionCheck(CascadeAction action, Completion completion) {
                    completion.success();
                }

                @Override
                public List<String> getEdgeNames() {
                    return Collections.singletonList(VCenterPrimaryStorageVO.class.getSimpleName());
                }

                @Override
                public String getCascadeResourceName() {
                    return VolumeVO.class.getSimpleName();
                }

                private List<VolumeDeletionStruct> volumesFromAction(CascadeAction action) {
                    if (VCenterPrimaryStorageVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        List<String> psUuids = CollectionUtils.transformToList((List<PrimaryStorageInventory>) action.getParentIssuerContext(), new Function<String, PrimaryStorageInventory>() {
                            @Override
                            public String call(PrimaryStorageInventory arg) {
                                return arg.getUuid();
                            }
                        });

                        SimpleQuery<VolumeVO> q = dbf.createQuery(VolumeVO.class);
                        q.add(VolumeVO_.primaryStorageUuid, SimpleQuery.Op.IN, psUuids);
                        List<VolumeVO> vos = q.list();
                        if (!vos.isEmpty()) {
                            return toVolumeDeletionStruct(action, vos);
                        }
                    } else if (VolumeVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        return action.getParentIssuerContext();
                    }

                    return null;
                }

                private List<String> volumesCleanupFromAction(CascadeAction action) {
                    if (VolumeVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        List<VolumeDeletionStruct> list = action.getParentIssuerContext();
                        if (list == null) {
                            return null;
                        }

                        return CollectionUtils.transformToList(list, new Function<String, VolumeDeletionStruct>() {
                            @Override
                            public String call(VolumeDeletionStruct arg) {
                                return arg.getInventory().getUuid();
                            }
                        });
                    } else if (VCenterPrimaryStorageVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        List<PrimaryStorageInventory> pinvs = action.getParentIssuerContext();
                        List<String> psUuids = new ArrayList<>();
                        if (pinvs != null) {
                            psUuids = CollectionUtils.transformToList(pinvs, new Function<String, PrimaryStorageInventory>() {
                                @Override
                                public String call(PrimaryStorageInventory arg) {
                                    return arg.getUuid();
                                }
                            });
                        }

                        if (psUuids.isEmpty() && VCenterVO.class.getSimpleName().equals(action.getRootIssuer())) {
                            psUuids = SQL.New("select uuid from PrimaryStorageEO " +
                                    "where type='VCenter' " +
                                    "and deleted is not null")
                                    .list();
                        }

                        if (psUuids.isEmpty()) {
                            return Collections.emptyList();
                        }

                        return Q.New(VolumeEO.class)
                                .in(VolumeAO_.primaryStorageUuid, psUuids)
                                .select(VolumeAO_.uuid)
                                .listValues();
                    }

                    return Collections.emptyList();
                }

                @Override
                public CascadeAction createActionForChildResource(CascadeAction action) {
                    if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
                        List<VolumeDeletionStruct> ctx = volumesFromAction(action);
                        if (ctx != null) {
                            return action.copy()
                                    .setParentIssuer(VolumeVO.class.getSimpleName())
                                    .setParentIssuerContext(ctx);
                        }
                    }

                    return null;
                }
            };
        }

        if (ImageVO.class.getSimpleName().equals(resourceName)) {
            return new CascadeExtensionPoint() {
                @Override
                public void syncCascade(CascadeAction action) throws CascadeException {
                }

                @Override
                public void asyncCascade(CascadeAction action, Completion completion) {
                    if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
                        handleDeletionCheck(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                        handleDeletion(action, completion);
                    } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
                        handleDeletionCleanup(action, completion);
                    } else {
                        completion.success();
                    }
                }

                private void handleDeletionCleanup(CascadeAction action, Completion completion) {
                    cleanupImageEO();
                    completion.success();
                }

                private void cleanupImageEO() {
                    String sql = "select i.uuid from ImageEO i where i.deleted is not null and i.uuid not in (select vm.imageUuid from VmInstanceVO vm where vm.imageUuid is not null)";
                    dbf.hardDeleteCollectionSelectedBySQL(sql, ImageVO.class);
                }

                private void handleDeletion(final CascadeAction action, final Completion completion) {
                    final List<ImageDeletionStruct> structs = imagesFromAction(action);
                    if (structs == null) {
                        completion.success();
                        return;
                    }

                    List<ImageDeletionMsg> msgs = CollectionUtils.transformToList(structs, new Function<ImageDeletionMsg, ImageDeletionStruct>() {
                        @Override
                        public ImageDeletionMsg call(ImageDeletionStruct arg) {
                            ImageDeletionMsg msg = new ImageDeletionMsg();
                            msg.setImageUuid(arg.getImage().getUuid());
                            if (!arg.getDeleteAll()) {
                                msg.setBackupStorageUuids(arg.getBackupStorageUuids());
                            }
                            ImageDeletionPolicyManager.ImageDeletionPolicy deletionPolicy = deletionPolicyFromAction(action);
                            msg.setDeletionPolicy(deletionPolicy == null ? null : deletionPolicy.toString());
                            bus.makeTargetServiceIdByResourceUuid(msg, ImageConstant.SERVICE_ID, arg.getImage().getUuid());
                            msg.setForceDelete(action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE));
                            return msg;
                        }
                    });

                    bus.send(msgs, new CloudBusListCallBack(completion) {
                        @Override
                        public void run(List<MessageReply> replies) {
                            if (!action.isActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
                                for (MessageReply r : replies) {
                                    if (!r.isSuccess()) {
                                        completion.fail(r.getError());
                                        return;
                                    }
                                }
                            }

                            completion.success();
                        }
                    });
                }

                private ImageDeletionPolicyManager.ImageDeletionPolicy deletionPolicyFromAction(CascadeAction action) {
                    if (VCenterBackupStorageVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        return ImageDeletionPolicyManager.ImageDeletionPolicy.DeleteReference;
                    } else {
                        return null;
                    }
                }

                private void handleDeletionCheck(CascadeAction action, Completion completion) {
                    completion.success();
                }

                @Override
                public List<String> getEdgeNames() {
                    return Collections.singletonList(VCenterBackupStorageVO.class.getSimpleName());
                }

                @Override
                public String getCascadeResourceName() {
                    return ImageVO.class.getSimpleName();
                }

                private List<ImageDeletionStruct> toImageDeletionStruct(List<ImageVO> imgvos) {
                    return CollectionUtils.transformToList(imgvos, new Function<ImageDeletionStruct, ImageVO>() {
                        @Override
                        public ImageDeletionStruct call(ImageVO arg) {
                            ImageDeletionStruct s = new ImageDeletionStruct();
                            s.setImage(ImageInventory.valueOf(arg));
                            return s;
                        }
                    });
                }

                private List<ImageDeletionStruct> imagesFromAction(CascadeAction action) {
                    if (VCenterBackupStorageVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        List<String> bsUuids = CollectionUtils.transformToList((List<BackupStorageInventory>) action.getParentIssuerContext(), new Function<String, BackupStorageInventory>() {
                            @Override
                            public String call(BackupStorageInventory arg) {
                                return arg.getUuid();
                            }
                        });

                        List<ImageVO> imgvos = new Callable<List<ImageVO>>() {
                            @Override
                            @Transactional(readOnly = true)
                            public List<ImageVO> call() {
                                final String sql = "select img from ImageVO img, ImageBackupStorageRefVO ref"
                                        + " where ref.backupStorageUuid in (:bsUuids)"
                                        + " and img.uuid = ref.imageUuid"
                                        + " group by img.uuid";
                                TypedQuery<ImageVO> q = dbf.getEntityManager().createQuery(sql, ImageVO.class);
                                q.setParameter("bsUuids", bsUuids);
                                return q.getResultList();
                            }
                        }.call();

                        if (!imgvos.isEmpty()) {
                            return toImageDeletionStruct(imgvos);
                        }

                    } else if (ImageVO.class.getSimpleName().equals(action.getParentIssuer())) {
                        return action.getParentIssuerContext();
                    }

                    return null;
                }

                @Override
                public CascadeAction createActionForChildResource(CascadeAction action) {
                    if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
                        List<ImageDeletionStruct> ctx = imagesFromAction(action);
                        if (ctx != null) {
                            return action.copy()
                                    .setParentIssuer(ImageVO.class.getSimpleName())
                                    .setParentIssuerContext(ctx);
                        }
                    }

                    return null;
                }
            };
        }

        return null;
    }
}
