package org.zstack.vmware;

import com.vmware.vim25.mo.*;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.core.Platform;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.host.HostErrors;
import org.zstack.header.storage.primary.*;
import org.zstack.header.storage.snapshot.ShrinkVolumeSnapshotOnPrimaryStorageMsg;
import org.zstack.header.volume.*;
import org.zstack.storage.primary.EstimateVolumeTemplateSizeOnPrimaryStorageMsg;
import org.zstack.storage.primary.PrimaryStorageBase;
import org.zstack.storage.primary.PrimaryStorageCapacityUpdater;
import org.zstack.storage.primary.PrimaryStoragePathMaker;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import java.rmi.RemoteException;

import static org.zstack.core.Platform.operr;
/**
 * Created by david on 10/12/16.
 */
public class VCenterPrimaryStorageBase extends PrimaryStorageBase {
    private static final CLogger logger = Utils.getLogger(VCenterPrimaryStorageBase.class);

    public VCenterPrimaryStorageBase(PrimaryStorageVO self) {
        super(self);
    }

    private static String getDataVolumeInstallPath(String dsName, String volumeUuid) {
        String tmpPath = String.format("[%s] dataVolumes/%s",
                dsName, PrimaryStoragePathMaker.makeDataVolumeInstallPath(volumeUuid));
        int index = tmpPath.lastIndexOf('.');
        return String.format("%s.%s", tmpPath.substring(0, index), VMwareHelper.vmwareVDiskFormat);
    }

    @Override
    protected void handle(InstantiateVolumeOnPrimaryStorageMsg msg) {
        if (msg instanceof InstantiateRootVolumeFromTemplateOnPrimaryStorageMsg) {
            createVolumeFromTemplate((InstantiateRootVolumeFromTemplateOnPrimaryStorageMsg) msg);
        } else {
            createEmptyVolume(msg);
        }
    }

   // For ESX, we don't have a step to instantiate volume - thus fake it.
    private void createVolumeFromTemplate(InstantiateRootVolumeFromTemplateOnPrimaryStorageMsg msg) {
        InstantiateVolumeOnPrimaryStorageReply reply = new InstantiateVolumeOnPrimaryStorageReply();
        VolumeInventory inv = new VolumeInventory();
        inv.setUuid(Platform.getUuid());
        inv.setFormat(VMwareHelper.vmwareTemplateFormat);
        inv.setInstallPath("vcenter://dummy");
        reply.setVolume(inv);
        bus.reply(msg, reply);
    }

    // For ESX, we don't have a step to create volume - thus fake it.
    private void createEmptyVolume(InstantiateVolumeOnPrimaryStorageMsg msg) {
        InstantiateVolumeOnPrimaryStorageReply reply = new InstantiateVolumeOnPrimaryStorageReply();
        VolumeInventory inv = new VolumeInventory();
        inv.setUuid(Platform.getUuid());
        inv.setFormat(VMwareHelper.vmwareTemplateFormat);
        inv.setInstallPath(VMwareVolumeInstallPath.NotInstantiated.toString());
        reply.setVolume(inv);
        bus.reply(msg, reply);
    }

    @Transactional(readOnly = true)
    private VCenterVO getVCenterVO() {
        String sql = "select vc from VCenterVO vc, VCenterPrimaryStorageVO psvo "
                + "where vc.uuid = psvo.vCenterUuid "
                + "and psvo.uuid = :psuuid";
        TypedQuery<VCenterVO> q = dbf.getEntityManager().createQuery(sql, VCenterVO.class);
        q.setParameter("psuuid", self.getUuid());
        return q.getSingleResult();
    }

    private String getSelfDatastoreMorVal() {
        return Q.New(VCenterPrimaryStorageVO.class)
                .eq(VCenterPrimaryStorageVO_.uuid, self.getUuid())
                .select(VCenterPrimaryStorageVO_.datastore)
                .findValue();
    }

    private VCenterServiceInstance getServiceInstance() {
        VCenterVO vcvo = getVCenterVO();
        VMwareHelper.checkVCenterOperatingStatus(vcvo);
        return VMwareHelper.getVCenterServiceInstance(vcvo);
    }

    private VCenterServiceInstance getSingletonVCenterInstance() {
        VCenterVO vcvo = getVCenterVO();
        VMwareHelper.checkVCenterOperatingStatus(vcvo);
        return VMwareHelper.getSingletonVCenterServiceInstance(vcvo);
    }

    private String getHostUuid(final String vCenterUuid, final String morval) {
        return Q.New(ESXHostVO.class)
                .eq(ESXHostVO_.morval, morval)
                .eq(ESXHostVO_.vCenterUuid, vCenterUuid)
                .select(ESXHostVO_.uuid)
                .findValue();
    }

    private void doDestroyRootVolume(VCenterServiceInstance si, DeleteVolumeOnPrimaryStorageMsg msg) {
        DeleteVolumeOnPrimaryStorageReply reply = new DeleteVolumeOnPrimaryStorageReply();
        String vmUuid = msg.getVolume().getVmInstanceUuid();
        VirtualMachine vm = VMwareHelper.findVmWithZsUuid(si, vmUuid, false);
        if (vm == null) {
            logger.info(String.format("[vc] vm[uuid:%s] has been deleted in vCenter", vmUuid));
            bus.reply(msg, reply);
            return;
        }

        String hostUuid = getHostUuid(getVCenterVO().getUuid(), vm.getRuntime().getHost().getVal());

        VMwareHelper.destroyVmFromHost(vm, vmUuid, new Completion(msg) {
            @Override
            public void success() {
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                if (errorCode.isError(HostErrors.OPERATION_FAILURE_GC_ELIGIBLE)) {
                    // When the code reaches here, the VO's in database for current VM might have
                    // been deleted.
                    VmwarePrimaryStorageDeleteVolumeGC gc = new VmwarePrimaryStorageDeleteVolumeGC();
                    gc.NAME = String.format("gc-delete-volume-%s-on-vmware-primary-storage-%s", msg.getVolume().getUuid(),
                            self.getUuid());
                    gc.volume = msg.getVolume();
                    gc.hostUuid = hostUuid;
                    gc.submit();
                } else {
                    reply.setError(errorCode);
                }

                bus.reply(msg, reply);
            }
        });
    }

    private void doDestroyDataVolume(VCenterServiceInstance si, DeleteVolumeOnPrimaryStorageMsg msg) {
        DeleteVolumeOnPrimaryStorageReply reply = new DeleteVolumeOnPrimaryStorageReply();
        String dsMorval = getSelfDatastoreMorVal();
        VMwareHelper.deleteDataVolume(si, dsMorval, msg.getVolume().getInstallPath(), new Completion(msg) {
            @Override
            public void success() {
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    @Override
    protected void handle(DeleteVolumeOnPrimaryStorageMsg msg) {
        try (VCenterServiceInstance si = getServiceInstance()) {
            if (msg.getVolume().getType().equals(VolumeType.Root.toString())) {
                doDestroyRootVolume(si, msg);
                return;
            }

            doDestroyDataVolume(si, msg);
        }
    }

    @Override
    protected void handle(DeleteBitsOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(CreateImageCacheFromVolumeOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(CreateImageCacheFromVolumeSnapshotOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(CreateTemplateFromVolumeOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(MergeVolumeSnapshotOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(FlattenVolumeOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(DeleteSnapshotOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(RevertVolumeFromSnapshotOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(DownloadDataVolumeToPrimaryStorageMsg msg) {
        final DownloadDataVolumeToPrimaryStorageReply reply = new DownloadDataVolumeToPrimaryStorageReply();
        try (VCenterServiceInstance si = getServiceInstance()) {
            String dsMorVal = getSelfDatastoreMorVal();
            Datastore ds = VMwareHelper.getDatastore(si.getServerConnection(), dsMorVal);
            String installPath = getDataVolumeInstallPath(ds.getName(), msg.getVolumeUuid());

            VirtualDiskManager dmgr = si.getVirtualDiskManager();
            if (dmgr == null) {
                reply.setError(operr("No virtual disk manager"));
                bus.reply(msg, reply);
                return;
            }

            FileManager fmgr = si.getFileManager();
            if (fmgr == null) {
                reply.setError(operr("No file manager"));
                bus.reply(msg, reply);
                return;
            }

            String srcInstallPath = msg.getBackupStorageRef().getInstallPath();
            String dstpath = installPath.substring(0, installPath.lastIndexOf('/'));
            Datacenter dc = VMwareHelper.getDaterCenterOfDataStore(ds);
            if (dc == null) {
                reply.setError(operr("No file Datacenter"));
                bus.reply(msg, reply);
                return;
            }

            try {
                // pre-create the parent directly to avoid 'XXX was not found' error
                fmgr.makeDirectory(dstpath, dc, true);
            } catch (Exception ex) {
                logger.warn("[vc] create directory " + dstpath, ex);
            }

            Task task = dmgr.copyVirtualDisk_Task(srcInstallPath, dc, installPath, dc, null ,false);
            String status = task.waitForTask();
            if (status.equals(Task.SUCCESS)) {
                reply.setInstallPath(installPath);
            } else {
                reply.setError(operr(task.getTaskInfo().getError().getLocalizedMessage()));
            }

            bus.reply(msg, reply);
        } catch (RemoteException | InterruptedException ex) {
            reply.setError(operr(ex.getMessage()));
            bus.reply(msg, reply);
            Thread.currentThread().interrupt();
        }
    }

    @Override
    protected void handle(GetInstallPathForDataVolumeDownloadMsg msg) {
        final GetInstallPathForDataVolumeDownloadReply reply = new GetInstallPathForDataVolumeDownloadReply();
        try (VCenterServiceInstance si = getServiceInstance()) {
            String dsMorVal = getSelfDatastoreMorVal();
            Datastore ds = VMwareHelper.getDatastore(si.getServerConnection(), dsMorVal);
            String installPath = getDataVolumeInstallPath(ds.getName(), msg.getVolumeUuid());
            reply.setInstallPath(installPath);
            bus.reply(msg, reply);
        }
    }

    @Override
    protected void handle(DeleteVolumeBitsOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(DownloadIsoToPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(DeleteIsoFromPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(AskVolumeSnapshotCapabilityMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(ReInitRootVolumeFromTemplateOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(SyncVolumeSizeOnPrimaryStorageMsg msg) {
        bus.reply(msg, syncVolumeSize(msg.getVolumeUuid(), msg.getInstallPath()));
    }

    private SyncVolumeSizeOnPrimaryStorageReply syncVolumeSize(String volumeUuid, String installPath) {
        SyncVolumeSizeOnPrimaryStorageReply reply = new SyncVolumeSizeOnPrimaryStorageReply();
        //Return original capacity if NotInstantiated vcenter volume
        if (VMwareVolumeInstallPath.NotInstantiated.toString().equals(installPath)) {
            Tuple tuple = Q.New(VolumeVO.class)
                    .select(VolumeVO_.size, VolumeVO_.actualSize)
                    .eq(VolumeVO_.uuid, volumeUuid)
                    .findTuple();

            Long volumeSize = tuple.get(0, Long.class);
            Long volumeActualSize = tuple.get(1, Long.class);
            reply.setSize(volumeSize);
            reply.setActualSize(volumeActualSize);
            return reply;
        }

        VolumeType type = Q.New(VolumeVO.class)
                .eq(VolumeVO_.uuid, volumeUuid)
                .select(VolumeVO_.type)
                .findValue();
        try (VCenterServiceInstance si = getServiceInstance()) {
            if (type == VolumeType.Data) {
                String dsMorVal = getSelfDatastoreMorVal();
                Datastore ds = VMwareHelper.getDatastore(si.getServerConnection(), dsMorVal);
                VMwareHelper.StorageUsage usage = VMwareHelper.getVirtualDiskStorageUsage(ds, installPath);
                reply.setActualSize(usage.getActualSize());
                reply.setSize(usage.getSize());
                return reply;
            }

            VirtualMachine vm = VMwareHelper.findVirtualMachineByDatastorePath(si, installPath);
            if (vm == null) {
                reply.setError(operr("failed to get VM from installPath: %s", installPath));
            } else {
                VMwareHelper.StorageUsage usage = VMwareHelper.syncVMRootDiskUsage(vm, installPath);
                if (usage == null) {
                    reply.setError(operr("failed to get VM[%s] root disk usage", vm.getName()));
                } else {
                    reply.setActualSize(usage.getActualSize());
                    reply.setSize(usage.getSize());
                }
            }

            return reply;
        }
    }

    @Override
    protected void handle(EstimateVolumeTemplateSizeOnPrimaryStorageMsg msg) {
        // TODO
        SyncVolumeSizeOnPrimaryStorageReply r = syncVolumeSize(msg.getVolumeUuid(), msg.getInstallPath());
        EstimateVolumeTemplateSizeReply reply = new EstimateVolumeTemplateSizeReply();
        if (!r.isSuccess()) {
            reply.setError(r.getError());
            bus.reply(msg, reply);
            return;
        }

        reply.setActualSize(r.getActualSize());
        reply.setSize(r.getSize());
        bus.reply(msg, reply);
    }

    @Override
    protected void handle(BatchSyncVolumeSizeOnPrimaryStorageMsg msg) {
        BatchSyncVolumeSizeOnPrimaryStorageReply reply = new BatchSyncVolumeSizeOnPrimaryStorageReply();
        bus.reply(msg, reply);
        logger.warn("Not supported at current edition");
    }

    @Override
    protected void connectHook(ConnectParam param, Completion completion) {
        doSyncPhyisicalCapacity(new ReturnValueCompletion<PhysicalCapacityUsage>(completion) {
            @Override
            public void success(PhysicalCapacityUsage returnValue) {
                PrimaryStorageCapacityUpdater updater = new PrimaryStorageCapacityUpdater(self.getUuid());
                updater.run(new PrimaryStorageCapacityUpdaterRunnable() {
                    @Override
                    public PrimaryStorageCapacityVO call(PrimaryStorageCapacityVO cap) {
                        cap.setAvailablePhysicalCapacity(returnValue.availablePhysicalSize);
                        cap.setTotalPhysicalCapacity(returnValue.totalPhysicalSize);
                        return cap;
                    }
                });
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    protected void pingHook(Completion completion) {
        completion.success();
    }

    @Override
    protected void handle(ShrinkVolumeSnapshotOnPrimaryStorageMsg msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    protected void handle(GetVolumeSnapshotEncryptedOnPrimaryStorageMsg msg) {
        GetVolumeSnapshotEncryptedOnPrimaryStorageReply reply = new GetVolumeSnapshotEncryptedOnPrimaryStorageReply();
        bus.reply(msg, reply);
    }

    @Override
    protected void syncPhysicalCapacity(ReturnValueCompletion<PhysicalCapacityUsage> completion) {
        doSyncPhyisicalCapacity(completion);
    }

    private void doSyncPhyisicalCapacity(ReturnValueCompletion<PhysicalCapacityUsage> completion) {
        final String dsMorVal = getSelfDatastoreMorVal();
        try {
            VCenterServiceInstance si = getSingletonVCenterInstance();
            Datastore ds = VMwareHelper.getDatastore(si.getServerConnection(), dsMorVal);
            PhysicalCapacityUsage usage = new PhysicalCapacityUsage();
            usage.availablePhysicalSize = ds.getSummary().getFreeSpace();
            usage.totalPhysicalSize = ds.getSummary().getCapacity();
            completion.success(usage);
        } catch (Throwable ex) {
            completion.fail(operr(ex.getMessage()));
        }
    }

    @Override
    public void handle(AskInstallPathForNewSnapshotMsg msg) {
        AskInstallPathForNewSnapshotReply reply = new AskInstallPathForNewSnapshotReply();
        bus.reply(msg, reply);
    }

    @Override
    protected void handle(GetPrimaryStorageResourceLocationMsg msg) {
        bus.reply(msg, new GetPrimaryStorageResourceLocationReply());
    }

    @Override
    protected void handle(CheckVolumeSnapshotOperationOnPrimaryStorageMsg msg) {
        bus.reply(msg, new CheckVolumeSnapshotOperationOnPrimaryStorageReply());
    }
}
