package org.zstack.license;

import org.apache.bcel.Repository;
import org.apache.bcel.classfile.JavaClass;
import org.apache.bcel.util.ClassLoaderRepository;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.HttpStatusCodeException;
import org.zstack.compute.host.HostSystemTags;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.*;
import org.zstack.core.componentloader.PluginDSL;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SimpleQuery;
import org.zstack.core.defer.Defer;
import org.zstack.core.defer.Deferred;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.PeriodicTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.retry.Retry;
import org.zstack.core.retry.RetryCondition;
import org.zstack.core.thread.*;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.AbstractService;
import org.zstack.header.Component;
import org.zstack.header.Service;
import org.zstack.header.allocator.HostAllocatorFilterExtensionPoint;
import org.zstack.header.allocator.HostAllocatorSpec;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.cluster.APIDeleteClusterMsg;
import org.zstack.header.cluster.ClusterVO;
import org.zstack.header.cluster.ClusterVO_;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.file.FileDesc;
import org.zstack.header.host.*;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.identity.AccountVO;
import org.zstack.header.identity.ReportApiAccountControlExtensionPoint;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.image.ImageVO;
import org.zstack.header.image.ImageVO_;
import org.zstack.header.license.LicenseMessage;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.managementnode.ManagementNodeVO;
import org.zstack.header.managementnode.ManagementNodeVO_;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.rest.RESTConstant;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.vm.*;
import org.zstack.kvm.KVMConstant;
import org.zstack.kvm.KVMHostConnectExtensionPoint;
import org.zstack.kvm.KVMHostConnectedContext;
import org.zstack.license.cube.AdditionSession;
import org.zstack.mevoco.DeployMode;
import org.zstack.mevoco.MevocoGlobalProperty;
import org.zstack.mevoco.MevocoSystemTags;
import org.zstack.sns.*;
import org.zstack.sns.platform.http.SNSHttpEndpointVO;
import org.zstack.sns.platform.http.SNSHttpEndpointVO_;
import org.zstack.pciDevice.gpu.GpuDeviceVO;
import org.zstack.pciDevice.gpu.GpuDeviceVO_;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.*;
import org.zstack.utils.data.Pair;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.path.PathUtil;
import org.zstack.utils.zsha2.ZSha2Helper;
import org.zstack.vmware.APIDeleteVCenterMsg;
import org.zstack.vmware.ESXConstant;
import org.zstack.vmware.ESXHostVO;
import org.zstack.vmware.ESXHostVO_;

import javax.persistence.Tuple;
import javax.persistence.metamodel.SingularAttribute;
import javax.xml.bind.DatatypeConverter;
import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Arrays.asList;
import static org.zstack.core.Platform.*;
import static org.zstack.license.LicenseConstant.*;
import static org.zstack.license.LicenseErrors.LICENSE_CAPACITY_SETTING_MISMATCH;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * Created by frank on 12/30/2015.
 */
public class LicenseManagerImpl extends AbstractService implements LicenseManager, Component,
        HostAllocatorFilterExtensionPoint, KVMHostConnectExtensionPoint,
        ReportApiAccountControlExtensionPoint, GlobalApiMessageInterceptor,
        ManagementNodeReadyExtensionPoint {
    private final static CLogger logger = Utils.getLogger(LicenseManagerImpl.class);
    private final static String SYSFS_SERIAL_PATH = "/sys/devices/virtual/dmi/id/";

    static {
        PluginDSL.PluginDefinition definition = new PluginDSL.PluginDefinition(LicenseManagerImpl.class);
        definition.newExtension().extensionClass(Component.class).order(Integer.MAX_VALUE);
        definition.newExtension().extensionClass(Service.class).order(Integer.MAX_VALUE);
        definition.newExtension().extensionClass(ReportApiAccountControlExtensionPoint.class);
        definition.newExtension().extensionClass(GlobalApiMessageInterceptor.class);
    }

    private static final String GENLICREQ_SUM = LicenseConstant.GENLICREQ_SUM;

    private String productNameHash;
    private String productUuid;

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private ThreadFacade thdf;
    @Autowired
    private CloudBus bus;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private EventFacade evtf;
    @Autowired
    protected ResourceDestinationMaker destMaker;
    @Autowired
    private RESTFacade restf;

    private File genlicreq;
    private File ca;
    private PlatformLicense platformLicense = new PlatformLicense();
    private String licenseApplicationCode;
    private final Map<String, AdditionalLicenseFactory> additionalLicenseFactoryMap = new ConcurrentHashMap<>();
    private final Map<LicenseType, LicenseTypeDependencyChecker> licenseTypeCheckerMap = new ConcurrentHashMap<>();
    private final Map<String, ReportAddonQuotaUsageExtensionPoint> reportAddonQuotaExtensionPointMap = new ConcurrentHashMap<>();
    private Map<String, Long> usedArm64AddOnHostCpuMap = new ConcurrentHashMap<>();
    private static final List<String> nonCloudProdInfoLicenseList = asList(AddOnModuleType.ZStoneRBD.name, AddOnModuleType.ZStoneRGW.name,AddOnModuleType.ZStoneISCSI.name, AddOnModuleType.AllInOneEdge.name, AddOnModuleType.EnterpriseEdge.name);

    private final Map<String, Long> addonsUsageMap = new ConcurrentHashMap<>();

    private static List<FileDesc> fileDescs = new ArrayList<>();

    private static boolean isVMWAddOn(final LicenseInfo info) {
        return info.getLicenseType() == LicenseType.AddOn && info.getProduct().contains(AddOnModuleType.Vmware.name);
    }

    static void doGuard() {
        List<FileDesc> fds = new ArrayList<>(fileDescs);

        fileGuard();

        List<FileDesc> updatedFds = new ArrayList<>(fileDescs);

        // check file do not be changed
        for (FileDesc fd : fds) {
            FileDesc updatedFd = updatedFds.stream().filter(it -> it.getPath().equals(fd.getPath())).findFirst().orElse(null);
            if (updatedFd == null) {
                Platform.exit(String.format("File[%s] is deleted", fd.getPath()));
            }

            assert updatedFd != null;
            if (!fd.getMd5().equals(updatedFd.getMd5())
                    || (fd.getLastModified() != updatedFd.getLastModified())) {
                Platform.exit(String.format("File[%s] is changed", fd.getPath()));
            }
        }

        // check mevoco's modified time not too different from other files
        for (FileDesc fd : updatedFds) {
            if (fd.getPath().contains("mevoco")) {
                for (FileDesc otherFd : updatedFds) {
                    if (!otherFd.getPath().contains("mevoco")) {
                        // over 30 minutes is too different
                        if (Math.abs(fd.getLastModified() - otherFd.getLastModified()) > TimeUnit.MINUTES.toMillis(30)) {
                            Platform.exit(String.format("File[%s] is too different from other files", fd.getPath()));
                        }
                    }
                }
            }
        }
    }

    static List<String> findFilesFromEnv() {
        String cp = System.getProperty("java.class.path");
        String pv = System.getProperty("project.version");
        List<String> files = new ArrayList<>();

        if (cp != null) {
            String[] classpathEntries = cp.split(":");

            // 如果project.version为空，可以选择跳过筛选或使用其他默认值
            if (pv == null) {
                logger.warn("project.version is not set");
                pv = Platform.getComponentLoader().getComponent(DatabaseFacade.class).getDbVersion();
                if (pv == null) {
                    logger.error("Failed to get version from DatabaseFacade");
                    return files;
                }
            }

            String modifiedVersion;
            int lastDotIndex = pv.lastIndexOf('.');
            if (lastDotIndex != -1) {
                modifiedVersion = pv.substring(0, lastDotIndex) + ".0";
            } else {
                modifiedVersion = pv;
            }
            logger.info(String.format("Find jar with version: %s", modifiedVersion));
            for (String entry : classpathEntries) {
                if (entry != null && entry.endsWith(".jar") && entry.contains(modifiedVersion)) {
                    files.add(entry);
                }
            }
        } else {
            logger.warn("java.class.path is not set");
        }

        return files;
    }

    static void fileGuard() {
        fileDescs.clear();

        List<String> files = findFilesFromEnv();
        // collect all files last modified time
        for (String file : files) {
            File f = new File(file);
            if (!f.exists()) {
                continue;
            }

            fileDescs.add(new FileDesc(f.getAbsolutePath(), DigestUtils.md5Hex(f.getAbsolutePath()), f.lastModified()));
        }
    }

    @Override
    public LicenseType getLicenseType() {
        return platformLicense.getLicenseType();
    }

    @Override
    public LicenseInfo getLicenseInfo() {
        return platformLicense.getLicenseInfo();
    }

    private List<LicenseInfo> getAllLicensesRelatedToAppId(String appId, boolean needContent, String type) {
        if (StringUtils.isEmpty(appId)) {
            return Collections.emptyList();
        }
        List<LicenseInfo> licenses = platformLicense.getAllLicensesFromSpecifiedPath(ca, PathUtil.join(LICENSE_FOLDER, appId), needContent, type);
        // Binding to the license
        licenses.forEach(l -> PlatformLicense.bindAppIdToLicense(appId, l.getUuid()));
        licenses.removeIf(it -> it.getUsbKeyId() != null && !filterUKeyLicenseByAppId(it, appId));
        return licenses;
    }

    @Override
    public boolean isLicenseExpired() {
        return PlatformLicense.isLicenseExpired(platformLicense.getLicenseInfo());
    }

    @Override
    public List<LicenseInfo> getLicenseAddOns() {
        return platformLicense.getLicenseAddOns();
    }

    @Override
    public int getHostNumForTrialLicense() {
        return 1;
    }

    @Override
    public boolean isEnterpriseLicense() {
        return getLicenseType() != LicenseType.Community;
    }

    @SuppressWarnings("rawtypes")
    @Override
    public List<Class> reportApiAccountControl() {
        return Collections.singletonList(APIGetLicenseCapabilitiesMsg.class);
    }

    @SuppressWarnings("rawtypes")
    @Override
    public List<Class> getMessageClassToIntercept() {
        // intercept all APIs
        return null;
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.SYSTEM;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        checkHijackHostNum(msg);

        if (msg instanceof APIUpdateLicenseMsg) {
            check((APIUpdateLicenseMsg) msg);
        }else if (msg instanceof APIRegisterLicenseRequestedApplicationMsg) {
            validate((APIRegisterLicenseRequestedApplicationMsg) msg);
        }

        return msg;
    }

    private void validate(APIRegisterLicenseRequestedApplicationMsg msg) {
        if (msg.getLicenseRequestCode() != null && !msg.getLicenseRequestCode().isEmpty()) {
            Map map = JSONObjectUtil.toObject(new String(Base64.getDecoder().decode(msg.getLicenseRequestCode())), Map.class);
            String key = map.get(RSA_PRIVATE_KEY).toString();
            map = JSONObjectUtil.toObject(new String(Base64.getDecoder().decode((String) map.get(LICENSE_REQUEST))), Map.class);
            if (map != null && key != null) {
                String thumbprint = (String) map.get(THUMBPRINT);
                if (thumbprint != null) {
                    return;
                }
            }
        }
        throw new ApiMessageInterceptionException(argerr("the licenseRequestCode is illegal"));
    }

    private void check(APIUpdateLicenseMsg msg) {
        if (msg.getLicense() == null) {
            throw new APIMessage.InvalidApiMessageException("The field[license] of APIUpdateLicenseMsg is mandatory, can not be null");
        }
        try {
            byte[] bytes = Base64.getDecoder().decode(msg.getLicense());

            // A license contains S/MIME signature. An empty message signed by our CA will
            // generate 2595 bytes (that is signature plus S/MIME payload fields).
            if (bytes.length < (CoreGlobalProperty.UNIT_TEST_ON ? 2 : 2048)) {
                throw new ApiMessageInterceptionException(argerr("Unexpected decoded license file length: %d", bytes.length));
            }
        } catch (Exception e) {
            throw new ApiMessageInterceptionException(argerr("Decode fail because %s", e.getMessage()));
        }
    }

    private long getBareMetal2ChassisNum() {
        final String sql = "select count(*) from BareMetal2ChassisVO";
        Long chassisNum = SQL.New(sql, Long.class).find();
        return chassisNum == null ? 0 : chassisNum;
    }

    private long getBareMetalChassisNum() {
        final String sql = "select count(*) from BaremetalChassisVO";
        Long chassisNum = SQL.New(sql, Long.class).find();
        return chassisNum == null ? 0 : chassisNum;
    }

    // native host is a special kind of host, but it does not occupy license host number.
    private long getNativeHostNum() {
        final String sql = "select count(*) from NativeHostVO";
        Long nativeHosts = SQL.New(sql, Long.class).find();
        return nativeHosts == null ? 0 : nativeHosts;
    }

    // baremetal2 gateway is a special kind of host, but it does not occupy license host number.
    private long getBareMetal2GatewayNum() {
        final String sql = "select count(*) from BareMetal2GatewayVO";
        Long gatewayHosts = SQL.New(sql, Long.class).find();
        return gatewayHosts == null ? 0 : gatewayHosts;
    }

    // baremetal2 gateway is a special kind of host, but it does not occupy license cpu number.
    private long getBareMetal2GatewayCpuSocketsNum() {
        final String sql = "select sum(h.cpuSockets) from HostCapacityVO h, BareMetal2GatewayVO g where g.uuid = h.uuid";
        Long gatewaySocks = SQL.New(sql, Long.class).find();
        return gatewaySocks == null ? 0 : gatewaySocks;
    }

    private long getOtherXinchuangHosts() {
        Long hosts = Q.New(HostVO.class)
                .notEq(HostVO_.architecture, CpuArchitecture.aarch64.toString())
                .notEq(HostVO_.architecture, CpuArchitecture.x86_64.toString())
                .notEq(HostVO_.hypervisorType, "Native")
                .count();

        return hosts == null ? 0 : hosts;
    }

    private List<Tuple> getArm64HostsInfo() {
        // do not modify this sql unless u know what it means.
        // `order by` is important, used to ensure that arm host will consume arm64 addon first.
        final String sql = "SELECT hc.uuid, hc.cpuSockets FROM HostVO hv JOIN HostCapacityVO hc ON hv.uuid = hc.uuid"
                + " WHERE (hv.architecture = :arch and hv.hypervisorType = :hypervisor) ORDER BY hv.createDate";
        List<Tuple> arm64Hosts = SQL.New(sql, Tuple.class)
                .param("arch", CpuArchitecture.aarch64.toString())
                .param("hypervisor", KVMConstant.KVM_HYPERVISOR_TYPE)
                .list();
        return arm64Hosts;
    }

    private long getArm64HostNum() {
        Long arm64Hosts = Q.New(HostVO.class)
                .eq(HostVO_.architecture, CpuArchitecture.aarch64.toString())
                .eq(HostVO_.hypervisorType, KVMConstant.KVM_HYPERVISOR_TYPE)
                .count();
        return arm64Hosts == null ? 0 : arm64Hosts;
    }

    private long getUsedGpuDeviceNum() {
        return getAddonUsedCpuNum(AddOnModuleType.MaaSGpu.name);
    }

    private long getArm64CpuSocketsNum() {
        final String sql = "select sum(hc.cpuSockets) from HostCapacityVO hc, HostVO h"
                + " where h.uuid = hc.uuid and h.architecture = :arch and h.hypervisorType = :hypervisor";
        Long arm64Socks = SQL.New(sql, Long.class)
                .param("arch", CpuArchitecture.aarch64.toString())
                .param("hypervisor", KVMConstant.KVM_HYPERVISOR_TYPE).find();
        return arm64Socks == null ? 0 : arm64Socks;
    }

    private long getArm64VmNum() {
        Long arm64Vms = Q.New(VmInstanceVO.class)
                .eq(VmInstanceVO_.architecture, CpuArchitecture.aarch64.toString())
                .eq(VmInstanceVO_.hypervisorType, KVMConstant.KVM_HYPERVISOR_TYPE)
                .count();
        return arm64Vms == null ? 0 : arm64Vms;
    }

    private long getCdpRunningVmNum() {
        return getAddonUsedVmNum(AddOnModuleType.CDP.name);
    }

    private long getBackupQuotaVmNum() {
        return getAddonUsedVmNum(AddOnModuleType.DisasterRecovery.name);
    }

    private Optional<LicenseInfo> getArm64License() {
        return getLicenseAddOns().stream()
                .filter(info -> info.getModules().contains(AddOnModuleType.Arm64.name))
                .filter(info -> !PlatformLicense.isLicenseExpired(info))
                .max(Comparator.comparing(LicenseInfo::getExpireTime));
    }

    private long getLicensedArm64HostsNum() {
        return getArm64License()
                .map(LicenseInfo::getHostNum)
                .orElse(0);
    }

    private long getLicensedArm64VmNum() {
        return getArm64License()
                .map(LicenseInfo::getVmNum)
                .orElse(0);
    }

    private long getLicensedArm64CpuSocketsNum() {
        return getArm64License()
                .map(LicenseInfo::getCpuNum)
                .orElse(0);
    }

    private ErrorCode checkArmCpuNum(long licensedArm64SocksNum, long usedArm64CpuSocketsNum) {
        if (licensedArm64SocksNum < usedArm64CpuSocketsNum) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                    "Licensed %d ARM64 CPU Sockets, but %d in use",
                    licensedArm64SocksNum, usedArm64CpuSocketsNum);
        }

        return null;
    }

    private ErrorCode checkArmHostNum(long licensedArm64HostsNum, List<Tuple> arm64Hosts) {
        long arm64HostsNum = arm64Hosts.size();
        if (licensedArm64HostsNum < arm64HostsNum && !getLicenseInfo().isXinChuangPaidVersion()) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED, "Licensed %d ARM64 Hosts, but %d in use",
                    licensedArm64HostsNum, arm64HostsNum);
        }
        long usedAddOnArm64HostNum = licensedArm64HostsNum >= arm64HostsNum ? arm64HostsNum : licensedArm64HostsNum;
        // the host using the addon should be subtracted when calculating primary license capacity
        usedArm64AddOnHostCpuMap = arm64Hosts.subList(0, (int) usedAddOnArm64HostNum).stream().collect(Collectors.toMap(
                h -> h.get(0, String.class), h -> h.get(1, Integer.class).longValue()));
        return null;
    }

    private ErrorCode checkArmVmNum(long licensedArm64VmsNum, long usedArm64VmsNum) {
        if (licensedArm64VmsNum < usedArm64VmsNum) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                    "Licensed %d ARM64 VMs, but %d in use",
                    licensedArm64VmsNum, usedArm64VmsNum);
        }

        return null;
    }

    private long getVmwareVmNum() {
        return Q.New(VmInstanceVO.class)
                .eq(VmInstanceVO_.hypervisorType, ESXConstant.VMWARE_HYPERVISOR_TYPE)
                .count();
    }

    private long getVmwareHostNum() {
        return Q.New(ESXHostVO.class).count();
    }

    private long getVmwareCpuSocketsNum() {
        final String sql = "select sum(cap.cpuSockets) from HostCapacityVO cap, ESXHostVO esx where cap.uuid = esx.uuid";
        Long vmwCpuSocks = SQL.New(sql, Long.class).find();
        return vmwCpuSocks == null ? 0 : vmwCpuSocks;
    }

    private long getLicensedVmwareVmNum() {
        return getVMWLicenseInfo()
                .map(LicenseInfo::getVmNum)
                .orElse(0);
    }

    private long getLicensedVmwareHostNum() {
        return getVMWLicenseInfo()
                .map(LicenseInfo::getHostNum)
                .orElse(0);
    }

    private long getLicensedVmwareCpuSocketsNum() {
        return getVMWLicenseInfo()
                .map(LicenseInfo::getCpuNum)
                .orElse(0);
    }

    private boolean isCpuOverrun(boolean forKVM, long licensedVmwCpuNum, long currentKvmCpus, long currentEsxCpus) {
        if (forKVM && licensedVmwCpuNum > currentEsxCpus) {
            return getLicenseInfo().getCpuNum() < currentKvmCpus;
        }

        return getLicenseInfo().getCpuNum() + licensedVmwCpuNum < currentKvmCpus + currentEsxCpus;
    }

    private ErrorCode checkCpuNum(final boolean forKVM, final long vmwCpuNum, final long requestCpuNum) {
        Long cpuSockets = SQL.New("select sum(h.cpuSockets) from HostCapacityVO h", Long.class).find();
        if (cpuSockets == null) cpuSockets = 0L;
        cpuSockets -= usedArm64AddOnHostCpuMap.values().stream().mapToLong(Long::longValue).sum();
        cpuSockets -= getBareMetal2GatewayCpuSocketsNum();

        LicenseInfo vmwLicense = getVMWLicenseInfo().orElse(null);

        if (vmwLicense == null || PlatformLicense.isLicenseExpired(vmwLicense)) {
            cpuSockets -= getVmwareCpuSocketsNum();
        }

        if (vmwCpuNum <= 0) {
            if (cpuSockets + requestCpuNum > getLicenseInfo().getCpuNum()) {
                return err(LicenseErrors.LICENSE_NOT_PERMITTED, "hijacked detected. Your license[%s] permits %s CPU sockets," +
                                " but consumed %s, request additional %s. You can either apply a new license or delete additional hosts",
                        getLicenseType(), getLicenseInfo().getCpuNum(), cpuSockets, requestCpuNum);
            }

            return null;
        }

        List<String> esxHostUuids = Q.New(ESXHostVO.class).select(ESXHostVO_.uuid).listValues();
        Long currentEsxCpus = null;
        if (!esxHostUuids.isEmpty()) {
            currentEsxCpus = SQL.New("select sum(h.cpuSockets) from HostCapacityVO h where h.uuid in (:uuids)")
                    .param("uuids", esxHostUuids)
                    .find();
        }

        if (currentEsxCpus == null) {
            currentEsxCpus = 0L;
        }

        long currentKvmCpus = cpuSockets - currentEsxCpus;

        if (forKVM) {
            currentKvmCpus += requestCpuNum;
        } else {
            currentEsxCpus += requestCpuNum;
        }

        if (!isCpuOverrun(forKVM, vmwCpuNum, currentKvmCpus, currentEsxCpus)) {
            return null;
        }

        return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                "Insufficient CPU sockets licensed.You can either delete additional hosts or apply a new license.");
    }

    private boolean isHostOverrun(boolean forKVM, long licensedVmwHostNum, long currentKvmHosts, long currentEsxHosts) {
        if (forKVM && licensedVmwHostNum > currentEsxHosts) {
            return getLicenseInfo().getHostNum() < currentKvmHosts;
        }

        return getLicenseInfo().getHostNum() + licensedVmwHostNum < currentKvmHosts + currentEsxHosts;
    }

    private boolean isVmOverrun(boolean forKVM, long licensedVmwVmNum, long currentKvmVms, long currentEsxVms) {
        if (forKVM && licensedVmwVmNum > currentEsxVms) {
            return getLicenseInfo().getVmNum() < currentKvmVms;
        }

        return getLicenseInfo().getVmNum() + licensedVmwVmNum < currentKvmVms + currentEsxVms;
    }

    private ErrorCode checkHostNum(final boolean forKVM, final long vmwHostNum, final long requestHostNum) {
        long hostNum = dbf.count(HostVO.class);
        hostNum -= usedArm64AddOnHostCpuMap.keySet().size();
        hostNum -= getBareMetal2GatewayNum();
        hostNum -= getNativeHostNum();

        if (vmwHostNum <= 0) {
            if (hostNum + requestHostNum > getHostNumAllowedByLicense()) {
                return err(LicenseErrors.LICENSE_NOT_PERMITTED, "hijacked detected. Your license[%s] permits %s hosts," +
                                " but consumed %s, request additional %s. You can either apply a new license or delete additional hosts",
                        getLicenseType(), getHostNumAllowedByLicense(), hostNum, requestHostNum);
            }

            return null;
        }

        long currentEsxHosts = Q.New(HostVO.class).eq(HostVO_.hypervisorType, ESXConstant.VMWARE_HYPERVISOR_TYPE).count();
        long currentKvmHosts = hostNum - currentEsxHosts;
        if (forKVM) {
            currentKvmHosts += requestHostNum;
        } else {
            currentEsxHosts += requestHostNum;
        }

        if (!isHostOverrun(forKVM, vmwHostNum, currentKvmHosts, currentEsxHosts)) {
            return null;
        }

        return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                "Insufficient host number licensed.You can either delete additional hosts or apply a new license.");
    }

    private Optional<LicenseInfo> getVMWLicenseInfo() {
        return getLicenseAddOns().stream()
                .filter(info -> info.getModules().contains(AddOnModuleType.Vmware.name))
                .filter(info -> !PlatformLicense.isLicenseExpired(info))
                .max(Comparator.comparing(LicenseInfo::getExpireTime));
    }

    private static int getUnboxed(Integer obj) {
        return obj == null ? 0 : obj;
    }

    private ErrorCode checkVmNum(final boolean forKVM, final long vmwVmNum, final long requestVmNum) {
        Long vmNum = SQL.New("select count(vm.uuid) from VmInstanceVO vm where vm.type = :type", Long.class)
                .param("type", VmInstanceConstant.USER_VM_TYPE)
                .find();
        if (vmNum == null) vmNum = 0L;
        vmNum -= getArm64VmNum();

        if (vmwVmNum <= 0) {
            if (vmNum + requestVmNum > getLicenseInfo().getVmNum()) {
                return err(LicenseErrors.LICENSE_NOT_PERMITTED, "hijacked detected. Your license[%s] permits %s VMs," +
                                " but consumed %s, request additional %s. You can either apply a new license or delete additional VMs",
                        getLicenseType(), getLicenseInfo().getVmNum(), vmNum, requestVmNum);
            }

            return null;
        }

        long currentEsxVMs = Q.New(VmInstanceVO.class).eq(VmInstanceVO_.hypervisorType, ESXConstant.VMWARE_HYPERVISOR_TYPE).count();
        long currentKvmVMs = vmNum - currentEsxVMs;
        if (forKVM) {
            currentKvmVMs += requestVmNum;
        } else {
            currentEsxVMs += requestVmNum;
        }

        if (!isVmOverrun(forKVM, vmwVmNum, currentKvmVMs, currentEsxVMs)) {
            return null;
        }

        return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                "Insufficient VM number licensed.You can either delete additional VMs or apply a new license.");
    }

    private ErrorCode checkXinChuangHost() {
        ErrorCode err = checkArmAddOnLicense();
        if (err != null) {
            return err;
        }

        if (getOtherXinchuangHosts() != 0 && !getLicenseInfo().isXinChuangPaidVersion()){
            return err(LicenseErrors.LICENSE_NOT_PERMITTED, "Found Xinchuang host, but the type of license does not match.");
        }

        return null;
    }

    private ErrorCode checkArmAddOnLicense() {
        List arm64HostsInfo = getArm64HostsInfo();
        if (arm64HostsInfo.size() == 0) {
            return null;
        }

        // arm64 addon always licensed 0 arm64 socket and 0 arm64 vm
        long licensedArm64SocksNum = getLicensedArm64CpuSocketsNum();
        if (licensedArm64SocksNum > 0) {
            return checkArmCpuNum(licensedArm64SocksNum, getArm64CpuSocketsNum());
        }

        long licensedArm64VmNum = getLicensedArm64VmNum();
        if (licensedArm64VmNum > 0) {
            return checkArmVmNum(licensedArm64VmNum, getArm64VmNum());
        }

        return checkArmHostNum(getLicensedArm64HostsNum(), arm64HostsInfo);
    }

    @Override
    public ErrorCode checkHostCapacity(boolean forKVM) {
        LicenseInfo licenseInfo = getLicenseInfo();

        if (PlatformLicense.isLicenseExpired(licenseInfo)) {
            // expired license handled by MevocoManagerImpl
            return null;
        }

        ErrorCode err = checkXinChuangHost();
        if (err != null) {
            return err;
        }

        if (licenseInfo.getCpuNum() != null) {
            return checkCpuNum(forKVM, 0);
        } else if (licenseInfo.getHostNum() != null) {
            return checkHostNum(forKVM, 0);
        } else if (licenseInfo.getVmNum() != null) {
            return checkVmNum(forKVM, 0);
        }

        return null;
    }

    private ErrorCode checkCpuNum(boolean forKVM, int requestCpuNum) {
        Optional<LicenseInfo> vmwLicense = getVMWLicenseInfo();
        return checkCpuNum(forKVM, vmwLicense.map(info -> getUnboxed(info.getCpuNum())).orElse(0), requestCpuNum);
    }

    private ErrorCode checkHostNum(boolean forKVM, int requestHostNum) {
        Optional<LicenseInfo> vmwLicense = getVMWLicenseInfo();
        return checkHostNum(forKVM, vmwLicense.map(info -> getUnboxed(info.getHostNum())).orElse(0), requestHostNum);
    }

    private ErrorCode checkVmNum(boolean forKVM, int requestVmNum) {
        Optional<LicenseInfo> vmwLicense = getVMWLicenseInfo();
        return checkVmNum(forKVM, vmwLicense.map(info -> getUnboxed(info.getVmNum())).orElse(0), requestVmNum);
    }

    private boolean isCreateKvmVm(APICreateVmInstanceMsg msg) {
        if (msg.getImageUuid() != null) {
            String type = Q.New(ImageVO.class).eq(ImageVO_.uuid, msg.getImageUuid()).select(ImageVO_.type).findValue();
            return !type.equalsIgnoreCase(ESXConstant.VMWARE_IMAGE_TYPE);
        }

        if (msg.getHostUuid() != null) {
            String type = Q.New(HostVO.class).eq(HostVO_.uuid, msg.getHostUuid()).select(HostVO_.hypervisorType).findValue();
            return !type.equalsIgnoreCase(ESXConstant.VMWARE_HYPERVISOR_TYPE);
        }

        if (msg.getClusterUuid() != null) {
            String type = Q.New(ClusterVO.class).eq(ClusterVO_.uuid, msg.getHostUuid()).select(ClusterVO_.hypervisorType).findValue();
            if (type != null) {
                return !type.equalsIgnoreCase(ESXConstant.VMWARE_HYPERVISOR_TYPE);
            }
        }

        return false;
    }

    private boolean isAddKvmHost(APIAddHostMsg msg) {
        if (msg.getClusterUuid() == null) {
            throw new APIMessage.InvalidApiMessageException("The field[clusterUuid] of APIAddHostMsg is mandatory, can not be null");
        }
        return Q.New(ClusterVO.class)
                .eq(ClusterVO_.uuid, msg.getClusterUuid())
                .eq(ClusterVO_.hypervisorType, KVMConstant.KVM_HYPERVISOR_TYPE)
                .isExists();
    }

    private boolean isForKVM(Message msg) {
        if (msg instanceof APICreateVmInstanceMsg) {
            return isCreateKvmVm((APICreateVmInstanceMsg) msg);
        }

        if (msg instanceof APIAddHostMsg) {
            return isAddKvmHost((APIAddHostMsg) msg);
        }

        return false;
    }

    private void checkHijackHostNum(Message msg) {
        if (msg instanceof LicenseMessage) {
            return;
        }

        final ErrorCode err = checkHostCapacity(isForKVM(msg));
        if (err == null) {
            return;
        }

        if (!(msg instanceof APISyncCallMessage) &&
                !(msg instanceof APIDeleteHostMsg) &&
                !(msg instanceof APIDeleteVCenterMsg) &&
                !(msg instanceof APIDeleteClusterMsg) &&
                !(msg instanceof APIChangeHostStateMsg) &&
                !(msg instanceof APIDestroyVmInstanceMsg) &&
                !(msg instanceof APIExpungeVmInstanceMsg)) {
            throw new ApiMessageInterceptionException(err);
        }
    }


    /**
     * The @class LicenseRequest below is used to interchange information with
     * license server.  Do NOT change the name of its fields.
     */
    public static class LicenseRequest {
        String privateKey;     // the content of private key
        String licenseRequest; // the content of inner license request
    }

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    private int getHostNumAllowedByLicense() {
        LicenseInfo licenseInfo = getLicenseInfo();
        if (PlatformLicense.isLicenseExpired(licenseInfo)) {
            return 0;
        }

        Integer n = licenseInfo.getHostNum();
        return n == null ? 0 : n;
    }


    private String getSha512Sum(File f, boolean useString) throws NoSuchProviderException, NoSuchAlgorithmException, IOException {
        MessageDigest mda = MessageDigest.getInstance("SHA-512", "BC");
        byte[] data;
        if (useString) {
            String text = FileUtils.readFileToString(f);
            data = text.getBytes();
        } else {
            data = FileUtils.readFileToByteArray(f);
        }

        byte[] hex = mda.digest(data);
        return Hex.encodeHexString(hex);
    }

    private synchronized String generateLicenseApplicationCode() throws IOException {
        doGeneratePrivateKey();

        String priKey = FileUtils.readFileToString(new File(LicenseConstant.PRIVATE_KEY_DEFAULT_PATH));
        String reqFile = FileUtils.readFileToString(new File(LicenseConstant.LICENSE_REQUEST_FILE));

        LicenseRequest req = new LicenseRequest();
        req.licenseRequest = reqFile;
        req.privateKey = priKey;

        String r = DatatypeConverter.printBase64Binary(JSONObjectUtil.toJsonString(req).getBytes(StandardCharsets.UTF_8));
        FileUtils.writeStringToFile(new File(LicenseConstant.LICENSE_APPLICATION_CODE), r);
        PlatformLicense.licenseApplicationCode = r;
        return r;
    }

    private void checkTools() throws ClassNotFoundException {
        genlicreq = LicenseConstant.GENLICREQ_PATH;
        ca = PathUtil.findFileOnClassPath(LicenseConstant.CA, true);
        try {
            String sum = getSha512Sum(genlicreq, false);
            if (!GENLICREQ_SUM.equals(sum)) {
                throw new CloudRuntimeException(String.format("corrupted genlicreq tool[%s], %s", genlicreq.getAbsolutePath(), sum));
            }

            sum = getSha512Sum(ca, true);
            if (!LicenseConstant.CASUM.equals(sum)) {
                throw new CloudRuntimeException(String.format("corrupted CA file[%s], %s", ca.getAbsolutePath(), sum));
            }

            final File keyutil = LicenseConstant.UKEYUTIL_PATH;
            if (keyutil != null) {
                sum = getSha512Sum(keyutil, false);
                if (!LicenseConstant.UKEYUTIL_SUM.equals(sum)) {
                    throw new CloudRuntimeException(String.format("corrupted keyutil file[%s], %s", keyutil.getAbsolutePath(), sum));
                }

                ShellUtils.runAndReturn(String.format("chmod +x %s", keyutil.getAbsolutePath()), false);
            }

            licenseApplicationCode = generateLicenseApplicationCode();
            Repository.setRepository(new ClassLoaderRepository(PlatformLicense.class.getClassLoader()));
            JavaClass objectClazz = Repository.lookupClass(PlatformLicense.class.getName());
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] bytes = digest.digest(objectClazz.toString().getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            String toolSum = sb.toString();
            if (!CoreGlobalProperty.UNIT_TEST_ON) {
                if (!LicenseConstant.TOOL1_SUM.equals(toolSum)) {
                    throw new CloudRuntimeException(String.format("corrupted class file, %s", toolSum));
                }
            }

            objectClazz = Repository.lookupClass(LicenseChecker.class.getName());
            digest = MessageDigest.getInstance("MD5");
            bytes = digest.digest(objectClazz.toString().getBytes());
            sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            toolSum = sb.toString();

            if (!CoreGlobalProperty.UNIT_TEST_ON) {
                if (!LicenseConstant.TOOL2_SUM.equals(toolSum)) {
                    throw new CloudRuntimeException(String.format("corrupted class file, %s", toolSum));
                }
            }
        } catch (NoSuchAlgorithmException | IOException | NoSuchProviderException e) {
            throw new CloudRuntimeException(e);
        }
    }

    private void checkPlatformId() {
        if (!destMaker.isManagedByUs(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID)) {
            return;
        }

        if (MevocoSystemTags.PLATFORM_ID.hasTag(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID, AccountVO.class)) {
            return;
        }

        if (StringUtils.isEmpty(licenseApplicationCode)) {
            logger.warn(String.format("failed to get platform id, because %s is not exist", LicenseConstant.LICENSE_APPLICATION_CODE));
            return;
        }

        SystemTagCreator creator = MevocoSystemTags.PLATFORM_ID.newSystemTagCreator(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
        creator.inherent = true;
        creator.recreate = false;
        creator.setTagByTokens(map(e(MevocoSystemTags.PLATFORM_ID_TOKEN, DigestUtils.md5Hex(licenseApplicationCode).substring(0, 10))));
        creator.create();
    }
    
    private synchronized String getSysUuid() {
        if (productUuid != null) {
            return productUuid;
        }

        String uu = getDmiSysUuid();
        if (StringDSL.isUuid(uu)) {
            productUuid = uu;
            return uu;
        }

        productUuid = getRootBlkUuid();
        return productUuid;
    }

    private void loadSysInfo() {
        productUuid = getShellOutput(String.format("/bin/cat %s",
                PathUtil.join(SYSFS_SERIAL_PATH, "product_uuid")));
        String name = getShellOutput(String.format("/bin/cat %s",
                PathUtil.join(SYSFS_SERIAL_PATH, "product_name")));
        if (name != null) {
            productNameHash = DigestUtils.md5Hex(name);
        }
    }

    @Override
    public List<HostVO> filterHostCandidates(List<HostVO> candidates, HostAllocatorSpec spec) {
        if (getLicenseInfo().getVmNum() == null && getLicensedArm64VmNum() == 0) {
            return candidates;
        }

        final ErrorCode err = checkHostCapacity(KVMConstant.KVM_HYPERVISOR_TYPE.equals(spec.getHypervisorType()));
        return err == null ? candidates : Collections.emptyList();
    }

    @Override
    public String filterErrorReason() {
        return Platform.i18n("Licensed VM number overrun");
    }

    protected void checkLicense() throws Exception {
        checkLicense(new LicenseCheckerContext());
    }

    protected synchronized void checkLicense(LicenseCheckerContext context) throws Exception {
        PlatformLicense lic = Platform.New(PlatformLicense::new);
        List<LicenseInfo> oldAddons = getLicenseAddOns();

        context.productNameHash = productNameHash;
        context.syscode = getSysUuid();
        context.cacert = ca;
        lic.buildLicense(context);

        licenseApplicationCode = generateLicenseApplicationCode();
        if (isEnterpriseLicense()) {
            checkLicenseCapacity(lic.getLicenseInfo(), lic.getLicenseAddOns());
        }

        boolean isEnterpriseLicenseOld = isEnterpriseLicense();

        platformLicense = lic;

        fireEventWhenAddonsChange(oldAddons, getLicenseAddOns());
        fireEventWhenLicenseTypeChange(isEnterpriseLicenseOld, isEnterpriseLicense());
    }

    private Set<String> extractModules(List<LicenseInfo> addonList) {
        return addonList.stream()
                .flatMap(licenseInfo -> licenseInfo.getModules().stream())
                .collect(Collectors.toSet());
    }

    private boolean areModulesSame(List<LicenseInfo> oldAddonList, List<LicenseInfo> newAddonList) {
        Set<String> oldModules = extractModules(oldAddonList);
        Set<String> newModules = extractModules(newAddonList);
        return oldModules.equals(newModules);
    }

    private Map<String, Integer> getCpuCountPerModule(List<LicenseInfo> addonList) {
        Stream<Map.Entry<String, Integer>> entryStream = addonList.stream()
                .filter(license -> license != null && license.getCpuNum() != null)
                .flatMap(license -> Optional.ofNullable(license.getModules())
                        .orElse(Collections.emptyList())
                        .stream()
                        .filter(Objects::nonNull)
                        .map(module -> new AbstractMap.SimpleEntry<>(module, license.getCpuNum())));

        return entryStream.collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                Math::max,  // Keep the larger CPU count
                HashMap::new  // Specify the map implementation
        ));
    }

    public static Map<String, OffsetDateTime> getModuleExpireTimes(List<LicenseInfo> addonList) {
        Stream<Map.Entry<String, OffsetDateTime>> entryStream = addonList.stream()
                .flatMap(license -> license.getModules().stream()
                        .map(module -> new AbstractMap.SimpleEntry<>(module, license.getExpireTime())));

        Map<String, OffsetDateTime> expireTimesMap = entryStream.collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (existing, replacement) -> replacement
        ));
        return expireTimesMap;
    }

    private <T> boolean areMapValuesIdentical(Map<String, T> oldMap, Map<String, T> newMap) {
        if (oldMap.size() != newMap.size()) {
            return false;
        }

        for (Map.Entry<String, T> entry : oldMap.entrySet()) {
            String key = entry.getKey();
            T oldValue = entry.getValue();
            T newValue = newMap.get(key);

            if (!Objects.equals(oldValue, newValue)) {
                return false;
            }
        }

        return true;
    }

    private void fireAddonUpdateEvent(List<LicenseInfo> oldAddonList, List<LicenseInfo> newAddonList) {
        LicenseCanonicalEvents.AddonChangeData data = new LicenseCanonicalEvents.AddonChangeData();
        data.setBeforeAddonInfo(JSONObjectUtil.toJsonString(oldAddonList));
        data.setAfterAddonInfo(JSONObjectUtil.toJsonString(newAddonList));
        evtf.fire(LicenseCanonicalEvents.ADDON_UPDATE_CHANGED_PATH, data);
    }

    private void fireEventWhenAddonsChange(List<LicenseInfo> oldAddonList, List<LicenseInfo> newAddonList) {
        if (!areModulesSame(oldAddonList, newAddonList)) {
            fireAddonUpdateEvent(oldAddonList, newAddonList);
            return;
        }

        if (!areMapValuesIdentical(getCpuCountPerModule(oldAddonList), getCpuCountPerModule(newAddonList))){
            fireAddonUpdateEvent(oldAddonList, newAddonList);
            return;
        }

        if (!areMapValuesIdentical(getModuleExpireTimes(oldAddonList), getModuleExpireTimes(newAddonList))){
            fireAddonUpdateEvent(oldAddonList, newAddonList);
            return;
        }
    }

    private void fireEventWhenLicenseTypeChange(boolean isEnterpriseLicenseOld, boolean isEnterpriseLicenseNew) {
        if (isEnterpriseLicenseNew == isEnterpriseLicenseOld) {
            return;
        }

        LicenseCanonicalEvents.LicenseTypeChangeData data = new LicenseCanonicalEvents.LicenseTypeChangeData();
        data.setCurEnterpriseLicense(isEnterpriseLicenseNew);
        evtf.fire(LicenseCanonicalEvents.LICENSE_TYPE_CHANGED_PATH, data);
    }

    @Override
    public List<String> getAvailableModules() {
        List<String> result = new ArrayList<>();

        for (LicenseInfo addOnInfo : getLicenseAddOns()) {
            List<String> modules = addOnInfo.getModules();

            if (PlatformLicense.isLicenseExpired(addOnInfo)) {
                continue;
            }

            result.addAll(modules);
        }

        return result.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public Flow createKvmHostConnectingFlow(KVMHostConnectedContext context) {
        if (!PlatformLicense.isMINI(productNameHash)) {
            return new NoRollbackFlow() {
                @Override
                public void run(FlowTrigger trigger, @SuppressWarnings("rawtypes") Map data) {
                    trigger.next();
                }
            };
        }

        return new NoRollbackFlow() {
            String __name__ = "check-host-vendor";

            @Override
            public void run(FlowTrigger trigger, @SuppressWarnings("rawtypes") Map data) {
                String hostUuid = context.getInventory().getUuid();
                String t = HostSystemTags.SYSTEM_PRODUCT_NAME.getTokenByResourceUuid(
                        hostUuid, HostSystemTags.SYSTEM_PRODUCT_NAME_TOKEN);
                if (t == null || !PlatformLicense.isMINI(DigestUtils.md5Hex(t))) {
                    trigger.fail(operr("unexpected host vendor for MINI"));
                } else {
                    trigger.next();
                }
            }
        };
    }

    @Override
    public ErrorCode checkAddonAvailability(String module) {
        // Trial and TrialExt will NOT implicitly have all the abilities of the module licenses.
        // There will be no Trial license since 4.0. all Trial will convert to TrialExt.
        // c.f. ZSTAC-21327, ZSTAC-37777, ZSTAC-53504
        if (PlatformLicense.isTrialLicense(getLicenseType())
                || PlatformLicense.isMINI(productNameHash)) {
            return null;
        }

        for (LicenseInfo addOnInfo : getLicenseAddOns()) {
            if (!addOnInfo.getModules().contains(module)) {
                continue;
            }

            if (PlatformLicense.isLicenseExpired(addOnInfo)) {
                return err(LicenseErrors.LICENSE_EXPIRED,
                        "Addon license for product:%s has been expired, please renew it", module);
            } else {
                return null;
            }
        }

        return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                "not allowed for current license, please apply addon license for product: %s", module);
    }

    private String getShellOutput(String cmd) {
        ShellUtils.ShellRunner runner = new ShellUtils.ShellRunner();
        runner.setCommand(cmd);
        runner.setVerbose(false);
        runner.setWithSudo(true);
        runner.setSuppressTraceLog(true);
        ShellResult res = runner.run();
        String stdout = res.getStdout();
        if (res.getRetCode() == 0) {
            return stdout.trim();
        }

        return null;
    }

    private void doCheckLicenseCapacity(final LicenseInfo info, final LicenseInfo masteriLicenseInfo) {
        if ((info.getLicenseType() != LicenseType.AddOn) || isVMWAddOn(info)) {
            if (!hasSameLicensePolicy(info, masteriLicenseInfo)) {
                Pair<Integer, String> addonLicBy = licensedBy(info);
                throw new OperationFailureException(err(LICENSE_CAPACITY_SETTING_MISMATCH,
                        "unexpected license policy: %d %s.", addonLicBy.first(), addonLicBy.second()));
            }
        }
    }

    private static Pair<Integer, String> licensedBy(LicenseInfo info) {
        if (info.getHostNum() != null) {
            return new Pair<>(info.getHostNum(), "hosts");
        }

        if (info.getCpuNum() != null) {
            return new Pair<>(info.getCpuNum(), "CPU sockets");
        }

        return new Pair<>(info.getVmNum(), "VMs");
    }

    private static boolean hasSameLicensePolicy(LicenseInfo lhs, LicenseInfo rhs) {
        return (lhs.getHostNum() != null) == (rhs.getHostNum() != null)
                && (lhs.getCpuNum() != null) == (rhs.getCpuNum() != null)
                && (lhs.getVmNum() != null) == (rhs.getVmNum() != null);
    }

    /**
     * check primary license and vmware addon license capacity settings
     *
     * We need to check that both platform license and addon license
     * has the same capacity settings. e.g. both CPU based or host based.
     *
     * @param licenseInfo primary license info
     * @param addOnLicenseInfoList all addon license info platform tend to use
     */
    private void checkLicenseCapacity(LicenseInfo licenseInfo, List<LicenseInfo> addOnLicenseInfoList) {
        Optional<LicenseInfo> vmwLicense = addOnLicenseInfoList.stream()
                .filter(info -> info.getModules().contains(AddOnModuleType.Vmware.name))
                .filter(info -> !PlatformLicense.isLicenseExpired(info))
                .max(Comparator.comparing(LicenseInfo::getExpireTime));
        if (!vmwLicense.isPresent()) {
            return;
        }

        // if any vmware license contained check primary license and vmware license capacity policy
        doCheckLicenseCapacity(vmwLicense.get(), licenseInfo);
    }

    private void doGeneratePrivateKey() {
        ShellUtils.run(String.format("mkdir -p %s", LicenseConstant.LICENSE_FOLDER));
        String username = System.getProperty("user.name");
        ShellUtils.run(String.format("chown -R %s:%s %s", username, username, LicenseConstant.LICENSE_FOLDER));

        int ret = ShellUtils.runAndReturn(String.format("chmod +x %s; sudo %s -privkey %s -reqfile %s %s", genlicreq.getAbsolutePath(),
                genlicreq.getAbsolutePath(), LicenseConstant.PRIVATE_KEY_DEFAULT_PATH,
                LicenseConstant.LICENSE_REQUEST_FILE, ca.getAbsolutePath()), false).getRetCode();
        if (ret != 0) {
            System.exit(ret);
        }

        ShellUtils.run(String.format("chown -R %s:%s %s", username, username, LicenseConstant.LICENSE_FOLDER));
    }

    private String getDmiSysUuid() {
        return getShellOutput("dmidecode -s system-uuid | tail -1", true);
    }

    private String getRootBlkUuid() {
        return getShellOutput("lsblk -n -oMOUNTPOINT,UUID | awk '$1==\"/\"{print $2}'", false);
    }

    private static String getShellOutput(String cmd, boolean sudo) {
        ShellUtils.ShellRunner runner = new ShellUtils.ShellRunner();
        runner.setCommand(cmd);
        runner.setWithSudo(sudo);
        runner.setVerbose(false);
        runner.setSuppressTraceLog(true);

        ShellResult res = runner.run();
        if (res.getRetCode() == 0) {
            return res.getStdout().trim();
        }

        return null;
    }

    private void loadExtensions() {
        for (AdditionalLicenseFactory f : pluginRgty.getExtensionList(AdditionalLicenseFactory.class)) {
            AdditionalLicenseFactory old = additionalLicenseFactoryMap.get(f.getAdditionalLicenseType().toString());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate AdditionalLicenseFactory[%s, %s] for type[%s]",
                        f.getClass().getName(), old.getClass().getName(), old.getAdditionalLicenseType()));
            }
            additionalLicenseFactoryMap.put(f.getAdditionalLicenseType().toString().toLowerCase(), f);
        }

        for (LicenseTypeDependencyChecker checker: pluginRgty.getExtensionList(LicenseTypeDependencyChecker.class)) {
            LicenseTypeDependencyChecker old = licenseTypeCheckerMap.get(checker.getLicenseType());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate LicenseTypeChecker[%s, %s] for type[%s]",
                        checker.getClass().getName(), old.getClass().getName(), old.getLicenseType()));
            }
            licenseTypeCheckerMap.put(checker.getLicenseType(), checker);
        }

        for (ReportAddonQuotaUsageExtensionPoint ext : pluginRgty.getExtensionList(ReportAddonQuotaUsageExtensionPoint.class)) {
            ReportAddonQuotaUsageExtensionPoint old = reportAddonQuotaExtensionPointMap.get(ext.getModuleName());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate ReportAddonQuotaExtensionPoint[%s, %s] for type[%s]",
                        ext.getClass().getName(), old.getClass().getName(), old.getModuleName()));
            }
            reportAddonQuotaExtensionPointMap.put(ext.getModuleName(), ext);
        }
    }

    @Override
    public boolean start() {
        try {
            checkTools();
        } catch (ClassNotFoundException e) {
            throw new CloudRuntimeException(e);
        }
        loadSysInfo();
        loadExtensions();

        try {
            LicenseRecords.saveForCtl();

            if (LicenseGlobalProperty.UPGRADE_LICENSE_RECORDS) {
                LicenseCheckerContext context = new LicenseCheckerContext();
                context.needSplitHaLicenseRecords = true;
                checkLicense(context);
            } else {
                checkLicense();
            }

            generateLicenseInfo();
        } catch (Exception e) {
            throw new CloudRuntimeException(e);
        }

        final ErrorCode err = checkHostCapacity(false);
        if (err != null) {
            throw new ApiMessageInterceptionException(err);
        }

        thdf.submitPeriodicTask(new PeriodicTask() {
            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.HOURS;
            }

            @Override
            public long getInterval() {
                return 1;
            }

            @Override
            public String getName() {
                return "check-license";
            }

            @Override
            public void run() {
                try {
                    checkLicense();
                } catch (Exception e) {
                    logger.warn(e.getMessage(), e);
                }
            }
        }, 1);

        moreTasks();
        if (LicenseGlobalProperty.UPGRADE_LICENSE_RECORDS && PlatformLicense.isMINI(productNameHash)) {
            upgradeLicenseRecordsForMINI();
        }

        evtf.on(LicenseCanonicalEvents.ADDON_UPDATE_CHANGED_PATH, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                LicenseCanonicalEvents.AddonChangeData d = (LicenseCanonicalEvents.AddonChangeData) data;
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.valueOf(RESTConstant.APP_JSON_UTF8));
                headers.setContentLength(JSONObjectUtil.toJsonString(d).length());
                HttpEntity<String> req = new HttpEntity<>(JSONObjectUtil.toJsonString(d), headers);
                List<String> httpEpUuidList = Q.New(SNSApplicationEndpointVO.class)
                        .eq(SNSApplicationEndpointVO_.state, SNSApplicationEndpointState.Hidden)
                        .eq(SNSApplicationEndpointVO_.type, SNSConstants.HTTP_PLATFORM)
                        .select(SNSApplicationEndpointVO_.uuid).listValues();
                if (httpEpUuidList.isEmpty()) {
                    return;
                }
                List<String> httpEpUrlList = Q.New(SNSHttpEndpointVO.class).select(SNSHttpEndpointVO_.url).in(SNSHttpEndpointVO_.uuid, httpEpUuidList).listValues();
                httpEpUrlList.forEach(url -> {
                    ResponseEntity<String> rsp = new Retry<ResponseEntity<String>>() {
                        @Override
                        @RetryCondition(onExceptions = {IOException.class, HttpStatusCodeException.class})
                        protected ResponseEntity<String> call() {
                            return restf.getRESTTemplate().exchange(url, HttpMethod.POST, req, String.class);
                        }
                    }.run();
                });
            }
        });
        return true;
    }

    private void moreTasks() {
        thdf.submitPeriodicTask(new PeriodicTask() {
            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.MINUTES;
            }

            @Override
            public long getInterval() {
                return 30;
            }

            @Override
            public String getName() {
                return "fire-guard";
            }

            @Override
            public void run() {
                if (CoreGlobalProperty.UNIT_TEST_ON) {
                    return;
                }

                try {
                    doGuard();
                } catch (Exception e) {
                    logger.warn(e.getMessage(), e);
                }
            }
        }, 0);
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void managementNodeReady() {
        checkPlatformId();
    }

    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }

    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof ReloadLicenseMsg) {
            handle((ReloadLicenseMsg) msg);
        } else if (msg instanceof LoadUkeyLicenseMsg) {
            handle((LoadUkeyLicenseMsg) msg);
        } else if (msg instanceof UpdateLicenseMsg) {
            handle((UpdateLicenseMsg) msg);
        } else if (msg instanceof UpdateLocalUKeyLicenseMsg) {
            handle((UpdateLocalUKeyLicenseMsg) msg);
        } else if (msg instanceof DeleteLicenseMsg) {
            handle((DeleteLicenseMsg) msg);
        } else if (msg instanceof AskLicenseCapacityMsg) {
            handle((AskLicenseCapacityMsg) msg);
        } else if (msg instanceof GetLicenseMsg) {
            handle((GetLicenseMsg) msg);
        } else if (msg instanceof GetLocalLicenseUKeyStatusMsg) {
            handle((GetLocalLicenseUKeyStatusMsg) msg);
        } else if (msg instanceof PushLicenseAddOnsUsageMsg) {
            handle((PushLicenseAddOnsUsageMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(PushLicenseAddOnsUsageMsg msg) {
        PushLicenseAddOnsUsageReply reply = new PushLicenseAddOnsUsageReply();
        if (msg.getAddOnsUsage() != null) {
            List<AddOnsUsage> addOns = JSONObjectUtil.toCollection(msg.getAddOnsUsage(), ArrayList.class, AddOnsUsage.class);
            for (AddOnsUsage usage : addOns) {
                addonsUsageMap.put(usage.module, usage.usage);
            }
        }
        bus.reply(msg, reply);
    }

    @Override
    public void deleteLicense(String uuid, Completion completion) {
        final ErrorCode errorCode = platformLicense.checkBeforeDeleteLicense(uuid,
                DeleteLicenseMsg.DeleteLicenseOperation.ByUuid);
        if (errorCode != null) {
            completion.fail(operr(errorCode, "failed to delete license", uuid));
            return;
        }

        if (!platformLicense.deleteLicense(uuid)) {
            completion.success();
            return;
        }

        ReloadLicenseMsg rmsg = new ReloadLicenseMsg();
        bus.makeLocalServiceId(rmsg, LicenseConstant.SERVICE_ID);
        bus.send(rmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }
                completion.success();
            }
        });
    }

    @Override
    public ErrorCode checkAddonQuota(String module, AddonQuotaStruct required) {
        List<LicenseInfo> addon = getLicenseAddOns().stream().filter(it -> it.getModules().contains(module)).collect(Collectors.toList());
        if (addon.isEmpty()) {
            return null;
        }
        //addon used quota
        AddonQuotaStruct usedQuotaStruct = new AddonQuotaStruct();
        AddonLicenseUsedResource addonQuotaResource = getAddonLicenseUsedResource(module);
        if (addonQuotaResource != null) {
            usedQuotaStruct = addonQuotaResource.getConsumeQuotaStruct();
        }

        return checkAddonQuota(addon.get(0), usedQuotaStruct, required);
    }

    @Override
    public AddonLicenseUsedResource getAddonLicenseUsedResource(String module) {
        return getAddonQuotaResource(module);
    }

    private long getAddonUsedVmNum(String module) {
        AddonLicenseUsedResource resource = getAddonQuotaResource(module);
        if (resource == null) {
            return 0;
        }
        return resource.getConsumeQuotaStruct().getVmNum();
    }

    private long getAddonUsedCpuNum(String module) {
        AddonLicenseUsedResource resource = getAddonQuotaResource(module);
        if (resource == null) {
            return 0;
        }
        return resource.getConsumeQuotaStruct().getCpuNum();
    }

    private AddonLicenseUsedResource getAddonQuotaResource(String module) {
        ReportAddonQuotaUsageExtensionPoint ext = reportAddonQuotaExtensionPointMap.get(module);
        if (ext == null) {
            return null;
        }
        return ext.getAddonLicenseUsedResource();
    }


    private ErrorCode checkAddonQuota(LicenseInfo addon, AddonQuotaStruct usedQuota, AddonQuotaStruct needQuota) {
        if (addon == null || needQuota == null || usedQuota == null) {
            return null;
        }
        if (SizeUtils.isPositive(addon.getVmNum()) && addon.getVmNum() < usedQuota.getVmNum() + needQuota.getVmNum()) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED, "Insufficient VM number licensed. Your %s license permits the number of VMs: %d.", addon.getModules(), addon.getVmNum());
        }
        if (SizeUtils.isPositive(addon.getHostNum()) && addon.getHostNum() < usedQuota.getHostNum() + needQuota.getHostNum()) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED, "Insufficient Host number licensed. Your %s license permits the number of Hosts: %d.", addon.getModules(), addon.getHostNum());
        }
        if (SizeUtils.isPositive(addon.getCpuNum()) && addon.getCpuNum() < usedQuota.getCpuNum() + needQuota.getCpuNum()) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED, "Insufficient CPU number licensed. Your %s license permits the number of CPUs: %d.", addon.getModules(), addon.getCpuNum());
        }
        if (SizeUtils.isPositive(addon.getCapacity()) && addon.getCapacity() < usedQuota.getCapacity() + needQuota.getCapacity()) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED, "Insufficient Capacity licensed. Your %s license permits Capacity: %sTB .", addon.getModules(), addon.getCapacity());
        }

        return null;
    }

    private void handle(DeleteLicenseMsg msg) {
        DeleteLicenseReply reply = new DeleteLicenseReply();

        if (msg.getUuid() != null) {
            deleteLicense(msg.getUuid(), new Completion(msg) {
                @Override
                public void success() {
                    bus.reply(msg, reply);
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    reply.setError(errorCode);
                    bus.reply(msg, reply);
                }
            });
            return;
        }

        deleteLicenseByModule(msg.getModule(), new Completion(msg) {
            @Override
            public void success() {
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void deleteLicenseByModule(String module, Completion completion) {
        for (LicenseInfo info: getLicenseAddOns()) {
            final ErrorCode errorCode =
                    platformLicense.checkBeforeDeleteLicense(info.getUuid(), DeleteLicenseMsg.DeleteLicenseOperation.ByModule);
            if (errorCode != null) {
                completion.fail(operr(errorCode, "failed to delete license by module"));
            }
        }

        List<LicenseInfo> licenseInfos = new ArrayList<>();
        try {
            licenseInfos.addAll(platformLicense.getAllLicenseInfo(ca));
        } catch (Exception e) {
            logger.debug("fail to get license info");
        }

        boolean anyLicenseDeleted = false;
        for (LicenseInfo info : licenseInfos) {
            if (!info.getLicenseType().equals(LicenseType.AddOn)) {
                continue;
            }

            if (info.getModules().contains(module)) {
                anyLicenseDeleted |= platformLicense.deleteLicense(info.getUuid());
            }
        }

        if (!anyLicenseDeleted) {
            completion.success();
            return;
        }

        ReloadLicenseMsg rmsg = new ReloadLicenseMsg();
        bus.makeLocalServiceId(rmsg, LicenseConstant.SERVICE_ID);
        bus.send(rmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    logger.debug("Failed to reload license");
                }

                completion.success();
            }
        });
    }

    // Do not check other MN if failed.
    private void handle(UpdateLocalUKeyLicenseMsg msg) {
        if (LicenseConstant.UKEYUTIL_PATH == null) {
            bus.replyErrorByMessageType(msg, operr("UKey not supported (arch: %s)", System.getProperty("os.arch")));
            return;
        }

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("update-ukey-license-for-management-node-%s", Platform.getManagementServerId());
            }

            @Override
            public void run(SyncTaskChain chain) {
                UpdateLocalUKeyLicenseReply reply = new UpdateLocalUKeyLicenseReply();
                doUpdateLocalUKeyLicense(msg.getLicense(), new ReturnValueCompletion<LicenseInventory>(chain, msg) {
                    @Override
                    public void success(LicenseInventory inv) {
                        reply.setInventory(inv);
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void handle(UpdateLicenseMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                UpdateLicenseReply reply = new UpdateLicenseReply();
                doUpdateLicense(msg, new ReturnValueCompletion<LicenseInventory>(msg) {
                    @Override
                    public void success(LicenseInventory inv) {
                        reply.setInventory(inv);
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("update-license-for-management-node-%s", Platform.getManagementServerId());
            }
        });
    }

    private void doUpdateLocalUKeyLicense(String license, ReturnValueCompletion<LicenseInventory> completion) {
        try {
            List<LicenseInfo> licenseInfoList = PlatformLicense.updateUkeyLicense(ca, license.getBytes());
            logger.info(String.format("local ukey license updated with %d licenses", licenseInfoList.size()));
            if (licenseInfoList.isEmpty()) {
                completion.fail(operr("No local ukey license updated"));
                return;
            }
        } catch (Exception ex) {
            completion.fail(operr("update local ukey license: %s", ex.getLocalizedMessage()));
            return;
        }

        doReloadLicense(completion);
    }

    // Here we care only key status & key ID.  There is no need to decrypt & verify license body.
    private static PlatformLicense.UKeyLicenseInfo getUsbKeyInfo() {
        return PlatformLicense.getUsbKeyLicense(2, BigInteger.ONE);
    }

    private void doUpdateUKeyLicense(byte[] license, ReturnValueCompletion<LicenseInventory> completion) {
        final String currentMnUuid = Platform.getManagementServerId();
        final Set<String> nodesToNotify = destMaker.getAllNodeInfo().stream()
                .map(ResourceDestinationMaker.NodeInfo::getNodeUuid)
                .collect(Collectors.toSet());
        final Set<String> mns = new HashSet<>(nodesToNotify);

        PlatformLicense.UKeyLicenseInfo info = getUsbKeyInfo();
        if (info == null || info.getUKeyStatus() != UKeyStatus.Ready) {
            logger.warn(String.format("No UKey ready on current node, status: %s", info == null ? UKeyStatus.Missing : info.getUKeyStatus()));
            mns.remove(currentMnUuid);
        }

        if (mns.isEmpty()) {
            completion.fail(operr("No node available to update UKey"));
            return;
        }

        final String licenseStr = new String(license, StandardCharsets.UTF_8).replace("\r\n", "\n");
        final List<LicenseInventory> invs = Collections.synchronizedList(new ArrayList<>());

        new While<>(mns).each((mnUuid, whileComp) -> {
            UpdateLocalUKeyLicenseMsg msg = new UpdateLocalUKeyLicenseMsg();
            msg.setLicense(licenseStr);
            bus.makeServiceIdByManagementNodeId(msg, LicenseConstant.SERVICE_ID, mnUuid);
            bus.send(msg, new CloudBusCallBack(whileComp) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        UpdateLocalUKeyLicenseReply r = reply.castReply();
                        nodesToNotify.remove(mnUuid);
                        invs.add(r.getInventory());
                        whileComp.allDone();
                        return;
                    }

                    whileComp.addError(reply.getError());
                    whileComp.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                // all failed
                if (errorCodeList.getCauses().size() == mns.size()) {
                    completion.fail(errorCodeList.getCauses().get(0));
                    return;
                }

                // No nodes to notify
                if (nodesToNotify.isEmpty()) {
                    completion.success(invs.get(0));
                    return;
                }

                // Notify other nodes to reload license
                new While<>(nodesToNotify).step((mnUuid, whileCompl) -> {
                    ReloadLicenseMsg rmsg = new ReloadLicenseMsg();
                    bus.makeServiceIdByManagementNodeId(rmsg, LicenseConstant.SERVICE_ID, mnUuid);
                    bus.send(rmsg, new CloudBusCallBack(whileCompl) {
                        @Override
                        public void run(MessageReply reply) {
                            whileCompl.done();
                        }
                    });
                }, nodesToNotify.size()).run(new WhileDoneCompletion(completion) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        completion.success(invs.get(0));
                    }
                });
            }
        });
    }

    @Deferred
    private void doUpdateLicense(UpdateLicenseMsg msg, ReturnValueCompletion<LicenseInventory> completion) {
        final byte[] license = Base64.getDecoder().decode(msg.getLicense());
        /* License content can be of three forms:
         * tarball: collections of signed S/MIME
         * text: a single S/MIME
         * text: UKey license
         */
        if (!CoreGlobalProperty.UNIT_TEST_ON && LicenseChecker.verifyLicenseFormat(license).equals(LicenseFormat.UsbKey)) {
            doUpdateUKeyLicense(license, completion);
            return;
        }

        final LicenseInfoBundle bundle = prepareForUpdatingLicense();
        Defer.defer(() -> cleanUpdatingLicenseContext(bundle));

        try {
            checkUploadedLicense(license, bundle);
        } catch (Exception ex) {
            ErrorCode errorCode = operr(ex.getMessage());
            if (ex instanceof OperationFailureException) {
                errorCode = ((OperationFailureException) ex).getErrorCode();
            }
            completion.fail(errorCode);
            logger.error("update license failed", ex);
            return;
        }
        
        // only third party license
        if (bundle.getLicenseInfoList().isEmpty() && !bundle.isHasUnknownLicense() && bundle.isHasThirdPartyLicense()) {
            if(msg.getAdditionSession() == null){
                completion.fail(err(LicenseErrors.UNEXPECTED_UPLOADED_LICENSE, "unexpected license file"));
                return;
            }
            
            List<AdditionSession> adds = JSONObjectUtil.toCollection(msg.getAdditionSession(), ArrayList.class, AdditionSession.class);
            boolean supportThirdPartyLicense = false;
            for (AdditionSession addition : adds) {
                if (addition != null && addition.type != null ) {
                    AdditionalLicenseType type = AdditionalLicenseType.valueOf(addition.type);
                    AdditionalLicenseFactory factory = getAdditionalLicenseFactory(type);
                    if (factory.supportThirdPartyLicense()) {
                        supportThirdPartyLicense = true;
                        break;
                    }
                }
            }
            
            if (supportThirdPartyLicense) {
                msg.getManagementUuids().clear();
            } else {
                completion.fail(err(LicenseErrors.UNEXPECTED_UPLOADED_LICENSE, "unexpected license file"));
                return;
            }
        }

        final boolean firstMN = !msg.getManagementUuids().isEmpty();
        msg.getManagementUuids().remove(Platform.getManagementServerId());
        if (!msg.getManagementUuids().isEmpty() && !bundle.isHasUnknownLicense() && !bundle.isHasThirdPartyLicense()) {
            completion.fail(operr("Multiple MN exists but only supplied licenses for %s", Platform.getManagementServerIp()));
            return;
        }

        submitUpdateLicense(bundle);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.allowEmptyFlow().setName("update-license");

        if (bundle.isHasUnknownLicense() && !msg.getManagementUuids().isEmpty()) {
            final ErrorCodeList errList = new ErrorCodeList();

            chain.then(new NoRollbackFlow() {
                @Override
                public void run(FlowTrigger trigger, @SuppressWarnings("rawtypes") Map data) {
                    new While<>(msg.getManagementUuids()).each((mnUuid, whileCompletion) -> {
                        UpdateLicenseMsg internalMsg = new UpdateLicenseMsg();
                        internalMsg.setManagementUuids(Collections.emptyList());  // avoid infinite loop
                        internalMsg.setLicense(msg.getLicense());
                        internalMsg.setAdditionSession(msg.getAdditionSession());
                        bus.makeServiceIdByManagementNodeId(internalMsg, LicenseConstant.SERVICE_ID, mnUuid);
                        bus.send(internalMsg, new CloudBusCallBack(whileCompletion) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    errList.getCauses().add(operr("MN[uuid:%s]: %s", mnUuid, reply.getError().getDetails()));
                                }
                                whileCompletion.done();
                            }
                        });
                    }).run(new WhileDoneCompletion(trigger) {
                        @Override
                        public void done(ErrorCodeList errorCodeList) {
                            if (errList.getCauses().isEmpty()) {
                                trigger.next();
                            } else {
                                trigger.fail(errList.getCauses().get(0));
                            }
                        }
                    });
                }
            });
        }

        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(@SuppressWarnings("rawtypes") Map data) {
                doReloadLicense(new ReturnValueCompletion<LicenseInventory>(msg) {
                    @Override
                    public void success(LicenseInventory inv) {
                        if (!bundle.isHasUnknownLicense() && !msg.getManagementUuids().isEmpty()) {
                            completion.fail(operr("Multiple MN exists but only supplied licenses for %s", Platform.getManagementServerIp()));
                        } else if (firstMN && ZSha2Helper.isMNHaEnvironment() && msg.getManagementUuids().isEmpty()) {
                            completion.fail(operr("MN HA environment, but only updated license for %s", msg.getManagementUuids()));
                        } else {
                            completion.success(inv);
                        }
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, @SuppressWarnings("rawtypes") Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void doReloadLicense(ReturnValueCompletion<LicenseInventory> completion) {
        ReloadLicenseMsg rmsg = new ReloadLicenseMsg();
        bus.makeLocalServiceId(rmsg, LicenseConstant.SERVICE_ID);
        bus.send(rmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }
                for (AfterLicenseChangeExtensionPoint exp : pluginRgty.getExtensionList(AfterLicenseChangeExtensionPoint.class)) {
                    exp.afterChangeLicense();
                }

                logger.debug(String.format("reload license on management node %s success, remove bak file", getManagementServerIp()));
                completion.success(((ReloadLicenseReply) reply).getInventory());
            }
        });
    }

    private String getRandomLicenseName() {
        return LicenseConstant.LICENSE_PREFIX + "_" + Platform.getUuid();
    }

    private String getLicenseNameByAppId(LicenseInfo info) {
        String appId = PlatformLicense.getRegisteredApplicationAppId(info.getThumbprint());
        return appId == null ? getRandomLicenseName() : PathUtil.join(appId, getRandomLicenseName());
    }

    private LicenseInfoBundle prepareForUpdatingLicense() {
        LicenseInfoBundle bundle = new LicenseInfoBundle();
        bundle.setLicenseDirectory(PathUtil.createTempDirectory());
        return bundle;
    }

    private void cleanUpdatingLicenseContext(LicenseInfoBundle bundle) {
        PathUtil.forceRemoveDirectory(bundle.getLicenseDirectory());
    }

    /**
     * Check Uploaded license, and put license information to LicenseInfoBundle.
     */
    private void checkUploadedLicense(byte[] license, LicenseInfoBundle results) throws Exception {
        String tmpDir = results.getLicenseDirectory();
        String licensePath = PathUtil.join(tmpDir, getRandomLicenseName());
        PathUtil.writeFile(licensePath, license);

        // license issueDate check
        OffsetDateTime lastIssueDate = getLicenseInfo() == null || getLicenseInfo().getIssueTime() == null ?
                OffsetDateTime.MIN : getLicenseInfo().getIssueTime();
        Map<String, OffsetDateTime> lastAddOnsIssueDates = getLicenseAddOns().stream()
                .collect(Collectors.toMap(addon -> addon.getModules().get(0),
                                          addon -> addon.getIssueTime() == null ? OffsetDateTime.MIN : addon.getIssueTime()));

        if (LicenseChecker.verifyLicenseFormat(license).equals(LicenseFormat.Tarball)) {
            ShellUtils.run(String.format("tar zxf %s 2>>/dev/null && rm -f %s && (find %s -type f -exec tar zxf {} \\; || true)",
                    licensePath, licensePath, tmpDir), tmpDir, false);
        }

        List<LicenseInfo> licenseInfoList = new ArrayList<>();
        LicenseInfoBundle licenseInfoBundle = platformLicense.getConvertedLicenseBundleFromSpecifiedPath(ca, tmpDir);
        results.setHasUnknownLicense(licenseInfoBundle.isHasUnknownLicense());
        results.setHasThirdPartyLicense(licenseInfoBundle.isHasThirdPartyLicense());

        for (LicenseInfo newLicenseInfo : licenseInfoBundle.getLicenseInfoList()) {
            logger.debug(String.format("Check license %s", newLicenseInfo));

            try {
                platformLicense.checkThumbPrint(newLicenseInfo, getSysUuid());
            } catch (Exception e) {
                results.setHasUnknownLicense(true);
                boolean badThumbprint = e instanceof OperationFailureException;
                String appId = PlatformLicense.getRegisteredApplicationAppId(newLicenseInfo.getThumbprint());
                if (appId == null) {
                    logger.warn("unexpected license: " + newLicenseInfo + (badThumbprint ? " with incorrect thumbprint" : ""));
                    continue;
                }
                logger.debug(String.format("license %s belongs to registered application[appId:%s]", newLicenseInfo, appId));
            }

            // check issue date
            // TODO: duration tolerance
            if (newLicenseInfo.getLicenseType().equals(LicenseType.AddOn)) {
                if (newLicenseInfo.getIssueTime().isBefore(lastAddOnsIssueDates.getOrDefault(newLicenseInfo.getModules().get(0), OffsetDateTime.MIN))) {
                    throw new OperationFailureException(err(LicenseErrors.LICENSE_INVALID_ISSUE_DATE,
                            "issue date of addon license is earlier than the existing license issue date"));
                }
            } else {
                if (newLicenseInfo.getIssueTime().isBefore(lastIssueDate)) {
                    throw new OperationFailureException(err(LicenseErrors.LICENSE_INVALID_ISSUE_DATE,
                            "issue date of platform license is earlier than the existing license issue date"));
                }
            }

            licenseInfoList.add(newLicenseInfo);
        }

        if (licenseInfoList.isEmpty() && !(!licenseInfoBundle.isHasUnknownLicense() && licenseInfoBundle.isHasThirdPartyLicense())) {
            throw new OperationFailureException(err(LicenseErrors.UNEXPECTED_UPLOADED_LICENSE, "unexpected license file"));
        }

        //check whether the license type that the uploaded licenses depend on are complete

        List<LicenseInfo> licenseInfoListCopy = new ArrayList<>(licenseInfoList);
        licenseInfoListCopy.add(getLicenseInfo());
        licenseInfoList.forEach(info -> {
            LicenseTypeDependencyChecker checker = licenseTypeCheckerMap.get(info.getLicenseType());
            if (checker == null) {
                return;
            }
            if (checker.skip()) {
                return;
            }
            DependsOnLicenseType licenseType = checker.getDependsOnLicenseType();
            if (!checker.checkLicenseOptionalDependency(licenseInfoListCopy, licenseType)) {
                throw new OperationFailureException(err(LicenseErrors.MISSING_DEPENDENT_LICENSE, "One of the following type licenses is required, %s", licenseType.getDependsOnLicenseTypesOptionals()));
            }
            Set<LicenseType> missingLicenseTypes = checker.checkLicenseNecessaryDependency(licenseInfoListCopy, licenseType);
            if (!missingLicenseTypes.isEmpty()) {
                throw new OperationFailureException(err(LicenseErrors.MISSING_DEPENDENT_LICENSE, "Missing dependent license, type[%s]", missingLicenseTypes.toString()));
            }
        });

        // addon module (exclude service) is not allowed in basic version
        LicenseInfo targetLicense = licenseInfoList.stream()
                .filter(info -> !info.getLicenseType().equals(LicenseType.AddOn))
                .findFirst()
                .orElse(getLicenseInfo());
        if (targetLicense.isBasicPaidVersion()
                && licenseInfoList.stream().filter(info -> info.getLicenseType().equals(LicenseType.AddOn))
                        .flatMap(info -> info.getModules().stream())
                        .anyMatch(moduleName -> !AddOnModuleType.isServiceAddOnModule(moduleName) && !PlatformLicense.isBasicSupportAddOnModule(moduleName))) {
            throw new OperationFailureException(err(LicenseErrors.ADD_ON_MODULES_NOT_SUPPORTED,
                "Add-on license only supports Arm64 addon, when license type is Basic"));
        }

        List<LicenseInfo> addOnLicenseInfoList = licenseInfoList
                .stream()
                .filter(licenseInfo -> licenseInfo.getLicenseType().equals(LicenseType.AddOn))
                .collect(Collectors.toList());
        
        // checkLicenseCapacity needs to keep the addOnLicense unique
        List<LicenseInfo> targetAddOnLicenseInfoList = Stream.concat(addOnLicenseInfoList.stream(), platformLicense.getLicenseAddOns().stream())
                .collect(Collectors.toMap(LicenseInfo::getModules, Function.identity(), (a, b) -> a))
                .values()
                .stream()
                .collect(Collectors.toList());

        // merge addon license info together for capacity setting check
        checkLicenseCapacity(targetLicense, targetAddOnLicenseInfoList);

        List<LicenseInfo> skipLicenseAddOns = needSkipLicenseAddOns(new ArrayList<>(licenseInfoList));
        for (LicenseInfo skipLicenseAddOn : skipLicenseAddOns) {
            Iterator<LicenseInfo> iterator = licenseInfoList.iterator();
            while (iterator.hasNext()) {
                LicenseInfo next = iterator.next();

                if (!skipLicenseAddOn.getUuid().equals(next.getUuid())) {
                    continue;
                }

                iterator.remove();
                logger.info(String.format("skip license Add-on[%s]", next.getPath()));
            }
        }

        // a packaged license tarball for multiple nodes.
        results.setLicenseInfoList(licenseInfoList);
    }

    private void submitUpdateLicense(LicenseInfoBundle bundle) {
        final Set<String> existsLicenseUuidSet = new HashSet<>();
        existsLicenseUuidSet.add(getLicenseInfo().getUuid());
        existsLicenseUuidSet.addAll(getLicenseAddOns().stream().map(LicenseInfo::getUuid).collect(Collectors.toSet()));

        final Set<String> licensePaths = new HashSet<>();
        for (LicenseInfo newLicenseInfo : bundle.getLicenseInfoList()) {
            String path = newLicenseInfo.getPath();
            if (licensePaths.contains(path)) {
                continue;
            }

            // check if license with same uuid already exists
            // then, remove duplicated licenses
            if (existsLicenseUuidSet.contains(newLicenseInfo.getUuid())) {
                logger.debug(String.format("License[uuid:%s] already exists just remove the new file and reload licenses", newLicenseInfo.getUuid()));
                PathUtil.forceRemoveFile(path);
                continue;
            }

            if (!PathUtil.exists(path)) {
                throw new OperationFailureException(operr("can not find license[uuid:%s, type:%s] file on path %s",
                        newLicenseInfo.getUuid(), newLicenseInfo.getLicenseType().toString(), path));
            }

            String dstLicensePath = PathUtil.join(LicenseConstant.LICENSE_FOLDER, getLicenseNameByAppId(newLicenseInfo));
            File f = new File(dstLicensePath);
            if (f.exists()) {
                PathUtil.forceRemoveFile(dstLicensePath);
            }
            logger.debug(String.format("License[uuid:%s, type:%s] on path %s will be moved to %s",
            newLicenseInfo.getUuid(), newLicenseInfo.getLicenseType().toString(), path, dstLicensePath));
            licensePaths.add(path);
            PathUtil.moveFile(path, dstLicensePath);
        }

        Optional<LicenseInfo> optional = bundle.getLicenseInfoList().stream()
                .filter(info -> info.getLicenseType() == LicenseType.Paid)
                .findFirst();
        optional.ifPresent(this::deleteLicenseAddOnsWhenLicenseProdChange);

        // save to DB
        saveLicenseRecordsForUploadAction(bundle.getLicenseInfoList());
    }

    private void saveLicenseRecordsForUploadAction(List<LicenseInfo> licenses) {
        List<LicenseInfo> uKeyLicenses = new ArrayList<>(licenses.size());
        List<LicenseInfo> uploadFileLicenses = new ArrayList<>(licenses.size());
        for (LicenseInfo license: licenses) {
            if (PlatformLicense.isUsbKeyLicense(license)) {
                uKeyLicenses.add(license);
            } else {
                uploadFileLicenses.add(license);
            }
        }
        LicenseRecords.saveAll(uKeyLicenses, LicenseUpdateSource.UKey);
        LicenseRecords.saveAll(uploadFileLicenses, LicenseUpdateSource.UploadFile);
    }

    private void deleteLicenseAddOnsWhenLicenseProdChange(LicenseInfo updateBaseLicenseInfo) {
        if (updateBaseLicenseInfo.getLicenseType() != LicenseType.Paid
                || getLicenseType() != LicenseType.Paid) {
            return;
        }

        LicenseProduct updateLicenseProductName = updateBaseLicenseInfo.getProdInfo();
        LicenseProduct originLicenseProductName = getLicenseInfo().getProdInfo();
        if (Objects.equals(updateLicenseProductName, originLicenseProductName)) {
            return;
        }

        List<LicenseInfo> allLicenseInfo = platformLicense.getAllLicenseInfo(ca);
        List<LicenseInfo> originAddOnInfoList = new ArrayList<>();
        for (LicenseInfo licenseInfo : allLicenseInfo) {
            if (licenseInfo.getLicenseType() != LicenseType.AddOn) {
                continue;
            }
            originAddOnInfoList.add(licenseInfo);
        }

        for (LicenseInfo addOnInfo : originAddOnInfoList) {
            logger.info(String.format("delete license[uuid=%s, type=%s, modules=%s]",
                    addOnInfo.getUuid(), LicenseType.AddOn, addOnInfo.getModules()));
            platformLicense.deleteLicense(addOnInfo.getUuid());
        }
    }

    public List<LicenseInfo> needSkipLicenseAddOns(List<LicenseInfo> updateLicenseInfoList) {
        List<LicenseInfo> result = new ArrayList<>();
        LicenseInfo baseLicenseInfo = this.getLicenseInfo();

        Optional<LicenseInfo> optional = updateLicenseInfoList.stream()
                .filter(info -> info.getLicenseType() == LicenseType.Paid)
                .findFirst();
        if (optional.isPresent()) {
            baseLicenseInfo = optional.get();
        }

        if (baseLicenseInfo.getLicenseType() != LicenseType.Paid) {
            return result;
        }

        if (!baseLicenseInfo.isEnterprisePaidVersion()) {
            return result;
        }

        result = updateLicenseInfoList.stream()
                .filter(licenseInfo -> licenseInfo.getLicenseType() == LicenseType.AddOn
                        && licenseInfo.getModules().stream().anyMatch(AddOnModuleType::isUIAddOnModule))
                .collect(Collectors.toList());

        if (baseLicenseInfo.isAIOSPaidVersion()) {
            result = updateLicenseInfoList.stream()
                    .filter(licenseInfo -> licenseInfo.getLicenseType() == LicenseType.AddOn
                            && licenseInfo.getModules().stream().noneMatch(AddOnModuleType::isAIOsUIAddOnModule))
                    .collect(Collectors.toList());
        }

        return result;
    }

    private void handle(LoadUkeyLicenseMsg msg) {
        PlatformLicense.getLicenseInfoFromUKey(ca);
        bus.reply(msg, new LoadUkeyLicenseReply());
    }

    @Deferred
    private void handle(ReloadLicenseMsg msg) {
        ReloadLicenseReply reply = new ReloadLicenseReply();

        try {
            LicenseRecords.saveForCtl();
            checkLicense();
            reply.setInventory(generateLicenseInfo());
            bus.reply(msg, reply);
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
            reply.setError(operr(e.getMessage()));
            bus.reply(msg, reply);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIGetLicenseInfoMsg) {
            handle((APIGetLicenseInfoMsg) msg);
        } else if (msg instanceof APIGetLicenseCapabilitiesMsg) {
            handle((APIGetLicenseCapabilitiesMsg) msg);
        } else if (msg instanceof APIReloadLicenseMsg) {
            handle((APIReloadLicenseMsg) msg);
        } else if (msg instanceof APIUpdateLicenseMsg) {
            handle((APIUpdateLicenseMsg) msg);
        } else if (msg instanceof APIDeleteLicenseMsg) {
            handle((APIDeleteLicenseMsg) msg);
        } else if (msg instanceof APIGetLicenseAddOnsMsg) {
            handle((APIGetLicenseAddOnsMsg) msg);
        } else if (msg instanceof APIGetLicenseUKeyStatusMsg) {
            handle((APIGetLicenseUKeyStatusMsg) msg);
        } else if (msg instanceof APIGetLicenseRecordsMsg) {
            handle((APIGetLicenseRecordsMsg) msg);
        } else if (msg instanceof APIRegisterLicenseRequestedApplicationMsg) {
            handle((APIRegisterLicenseRequestedApplicationMsg) msg);
        } else if (msg instanceof APIPushLicenseAddOnsUsageMsg) {
            handle((APIPushLicenseAddOnsUsageMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APIPushLicenseAddOnsUsageMsg msg) {
        final APIPushLicenseAddOnsUsageEvent event = new APIPushLicenseAddOnsUsageEvent(msg.getId());
        SimpleQuery<ManagementNodeVO> q = dbf.createQuery(ManagementNodeVO.class);
        q.select(ManagementNodeVO_.uuid);
        List<String> uuids = q.listValue();

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("push-license-addon-usage-flow");
        chain.then(new NoRollbackFlow() {
            String __name__ = "push-all-nodes-addons-usage";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<PushLicenseAddOnsUsageMsg> rmsgs = uuids.stream().map(mnUuid -> {
                    PushLicenseAddOnsUsageMsg rmsg = new PushLicenseAddOnsUsageMsg();
                    rmsg.setManagementNodeUuid(mnUuid);
                    rmsg.setAddOnsUsage(msg.getAddOnsUsage());
                    bus.makeServiceIdByManagementNodeId(rmsg, LicenseConstant.SERVICE_ID, mnUuid);
                    return rmsg;
                }).collect(Collectors.toList());

                new While<>(rmsgs).each((rmsg, whileCompletion) -> bus.send(rmsg, new CloudBusCallBack(whileCompletion) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.warn(String.format("failed to push addons usage %s", rmsg.getManagementNodeUuid()));
                            whileCompletion.addError(reply.getError());
                            whileCompletion.allDone();
                            return;
                        }

                        whileCompletion.done();
                    }
                })).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList.getCauses().get(0));
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    private void handle(final APIGetLicenseAddOnsMsg msg) {
        APIGetLicenseAddOnsReply reply = new APIGetLicenseAddOnsReply();
        List<LicenseAddOnInventory> addonInvs = new ArrayList<>();
        for (LicenseInfo addon : getLicenseAddOns()) {
            if (addon.getModules().stream().anyMatch(AddOnModuleType::isUIAddOnModule)) {
                if (!getLicenseInfo().isStandardPaidVersion()) continue;
            }

            if (addon.getModules().stream().anyMatch(AddOnModuleType::isAIOsUIAddOnModule)) {
                if (getLicenseInfo().isAIOSPaidVersion()) continue;
            }

            LicenseAddOnInventory inv = new LicenseAddOnInventory();

            inv.setUuid(addon.getUuid());
            inv.setExpired(PlatformLicense.isLicenseExpired(addon));
            inv.setCpuNum(addon.getCpuNum());
            inv.setHostNum(addon.getHostNum());
            inv.setVmNum(addon.getVmNum());
            inv.setExpiredDate(addon.getExpireTime().toString());
            inv.setIssuedDate(addon.getIssueTime().toString());
            inv.setModules(addon.getModules());
            inv.setLicenseType(LicenseType.AddOn.toString());
            inv.setName(addon.getUser());
            inv.setManagementNodeUuid(Platform.getManagementServerId());

            if (addon.getCpuNum() != null) {
                long usedCpuSocketsNum = 0L;
                if (addon.getModules().contains(AddOnModuleType.Vmware.name)) {
                    usedCpuSocketsNum = getVmwareCpuSocketsNum();
                } else if (addon.getModules().contains(AddOnModuleType.Arm64.name)) {
                    usedCpuSocketsNum = getArm64CpuSocketsNum();
                } else if (addon.getModules().contains(AddOnModuleType.vCpuZaku.name)) {
                    usedCpuSocketsNum = addonsUsageMap.getOrDefault(AddOnModuleType.vCpuZaku.name, 0L);
                } else if (addon.getModules().contains(AddOnModuleType.CPUZaku.name)) {
                    usedCpuSocketsNum = addonsUsageMap.getOrDefault(AddOnModuleType.CPUZaku.name, 0L);
                } else if (addon.getModules().contains(AddOnModuleType.MaaSGpu.name)) {
                    usedCpuSocketsNum = getUsedGpuDeviceNum();
                }

                int availableNum = (int) (addon.getCpuNum() - usedCpuSocketsNum);
                availableNum = Math.max(availableNum, 0);
                inv.setAvailableCpuNum(availableNum);
            }

            if (addon.getHostNum() != null) {
                long usedHostNum = 0L;
                if (addon.getModules().contains(AddOnModuleType.Vmware.name)) {
                    usedHostNum = getVmwareHostNum();
                } else if (addon.getModules().contains(AddOnModuleType.Arm64.name)) {
                    usedHostNum = getArm64HostNum();
                } else if (addon.getModules().contains(AddOnModuleType.ElasticBaremetal.name)) {
                    // special case: use hostNum/availableHostNum as chassisNum/availableChassisNum
                    usedHostNum = getBareMetal2ChassisNum();
                } else if (addon.getModules().contains(AddOnModuleType.Baremetal.name)) {
                    usedHostNum = getBareMetalChassisNum();
                }

                int availableNum = (int) (addon.getHostNum() - usedHostNum);
                availableNum = Math.max(availableNum, 0);
                inv.setAvailableHostNum(availableNum);
            }

            if (addon.getVmNum() != null) {
                long usedVmNum = 0L;
                if (addon.getModules().contains(AddOnModuleType.Vmware.name)) {
                    usedVmNum = getVmwareVmNum();
                } else if (addon.getModules().contains(AddOnModuleType.Arm64.name)) {
                    usedVmNum = getArm64VmNum();
                } else if (addon.getModules().contains(AddOnModuleType.CDP.name)) {
                    usedVmNum = getCdpRunningVmNum();
                } else if (addon.getModules().contains(AddOnModuleType.DisasterRecovery.name)) {
                    usedVmNum = getBackupQuotaVmNum();
                }

                int availableNum = (int) (addon.getVmNum() - usedVmNum);
                inv.setAvailableVmNum(availableNum);
            }

            addonInvs.add(inv);
        }
        reply.setAddons(addonInvs);
        bus.reply(msg, reply);
    }

    private void handle(final APIDeleteLicenseMsg msg) {
        APIDeleteLicenseEvent evt = new APIDeleteLicenseEvent(msg.getId());

        DeleteLicenseMsg dmsg = new DeleteLicenseMsg();
        if (msg.getUuid() != null) {
            dmsg.setUuid(msg.getUuid());
            dmsg.setOperation(DeleteLicenseMsg.DeleteLicenseOperation.ByUuid);
        } else {
            dmsg.setModule(msg.getModule());
            dmsg.setOperation(DeleteLicenseMsg.DeleteLicenseOperation.ByModule);
        }

        bus.makeLocalServiceId(dmsg, LicenseConstant.SERVICE_ID);
        bus.send(dmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply r) {
                if (!r.isSuccess()) {
                    evt.setError(r.getError());
                }

                bus.publish(evt);
            }
        });
    }

    private void handle(final APIUpdateLicenseMsg msg) {
        final APIUpdateLicenseEvent evt = new APIUpdateLicenseEvent(msg.getId());

        final FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName("update-license");
        chain.then(new ShareFlow() {
            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = "update-zstack-license";

                    @Override
                    public void run(FlowTrigger trigger, @SuppressWarnings("rawtypes") Map data) {
                        UpdateLicenseMsg umsg = new UpdateLicenseMsg();
                        umsg.setLicense(msg.getLicense());
                        List<String> mns = Q.New(ManagementNodeVO.class)
                                .select(ManagementNodeVO_.uuid)
                                .listValues();
                        Collections.sort(mns);
                        umsg.setManagementUuids(mns);
                        umsg.setAdditionSession(msg.getAdditionSession());
                        // The first update license message is sent to the management node with the smallest UUID:
                        // queue message to same node avoid deadlock
                        bus.makeServiceIdByManagementNodeId(umsg, LicenseConstant.SERVICE_ID, mns.get(0));
                        bus.send(umsg, new CloudBusCallBack(msg) {
                            @Override
                            public void run(MessageReply r) {
                                if (!r.isSuccess()) {
                                    trigger.fail(r.getError());
                                    return;
                                }

                                LicenseInventory inv = ((UpdateLicenseReply) r).getInventory();
                                evt.setInventory(inv);

                                trigger.next();
                            }
                        });
                    }
                });

                if (msg.getAdditionSession() != null) {
                    flow(new NoRollbackFlow() {
                        @Override
                        public void run(FlowTrigger trigger, @SuppressWarnings("rawtypes") Map data) {
                            List<AdditionSession> adds = JSONObjectUtil.toCollection(msg.getAdditionSession(), ArrayList.class, AdditionSession.class);
                            adds.forEach(addition -> updateAdditionalLicense(msg.getLicense(), addition, msg.getSession()));
                            trigger.next();
                        }
                    });
                }

            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(@SuppressWarnings("rawtypes") Map data) {
                bus.publish(evt);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, @SuppressWarnings("rawtypes") Map data) {
                evt.setError(errCode);
                bus.publish(evt);
            }
        }).start();
    }

    private void handle(final APIReloadLicenseMsg msg) {
        final APIReloadLicenseReply reply = new APIReloadLicenseReply();

        List<String> uuids;
        if (msg.getManagementNodeUuids() != null) {
            uuids = msg.getManagementNodeUuids();
        } else {
            SimpleQuery<ManagementNodeVO> q = dbf.createQuery(ManagementNodeVO.class);
            q.select(ManagementNodeVO_.uuid);
            uuids = q.listValue();
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("reload-license-flow");
        chain.then(new NoRollbackFlow() {
            String __name__ = "load-all-nodes-ukey-licenses";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<LoadUkeyLicenseMsg> rmsgs = uuids.stream().map(mnUuid -> {
                    LoadUkeyLicenseMsg rmsg = new LoadUkeyLicenseMsg();
                    rmsg.setManagementNodeUuid(mnUuid);
                    bus.makeServiceIdByManagementNodeId(rmsg, LicenseConstant.SERVICE_ID, mnUuid);
                    return rmsg;
                }).collect(Collectors.toList());

                new While<>(rmsgs).each((rmsg, whileCompletion) -> bus.send(rmsg, new CloudBusCallBack(whileCompletion) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.warn(String.format("failed to reload license on %s", rmsg.getManagementNodeUuid()));
                            whileCompletion.addError(reply.getError());
                            whileCompletion.allDone();
                            return;
                        }

                        whileCompletion.done();
                    }
                })).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList.getCauses().get(0));
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "reload-all-nodes-licenses";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<ReloadLicenseMsg> rmsgs = uuids.stream().map(mnUuid -> {
                    ReloadLicenseMsg rmsg = new ReloadLicenseMsg();
                    bus.makeServiceIdByManagementNodeId(rmsg, LicenseConstant.SERVICE_ID, mnUuid);
                    return rmsg;
                }).collect(Collectors.toList());

                bus.send(rmsgs, new CloudBusListCallBack(msg) {
                    @Override
                    public void run(List<MessageReply> replies) {
                        for (MessageReply r : replies) {
                            if (r.isSuccess()) {
                                continue;
                            }

                            trigger.fail(r.getError());
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "reload-addition-license";

            @Override
            public boolean skip(Map data) {
                return msg.getAdditionSession() == null;
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<AdditionSession> adds = JSONObjectUtil.toCollection(msg.getAdditionSession(), ArrayList.class, AdditionSession.class);
                adds.forEach(addition -> {
                    AdditionalLicenseType type = AdditionalLicenseType.valueOf(addition.type);
                    AdditionalLicenseFactory factory = getAdditionalLicenseFactory(type);
                    factory.reloadAdditionalLicense(addition, msg.getSession());
                });
                trigger.next();
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                reply.setInventory(generateLicenseInfo());
                bus.reply(msg, reply);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(msg, reply);
            }
        }).start();
    }

    private void handle(APIGetLicenseCapabilitiesMsg msg) {
        APIGetLicenseCapabilitiesReply reply = new APIGetLicenseCapabilitiesReply();
        Map<String, String> c = new HashMap<>();
        for (Map.Entry<String, LicenseCapability> e : LicenseCapability.capabilities.entrySet()) {
            c.put(e.getKey(), String.valueOf(e.getValue().status()));
        }
        reply.setCapabilities(c);
        bus.reply(msg, reply);
    }

    private void handle(APIGetLicenseInfoMsg msg) {
        APIGetLicenseInfoReply reply = new APIGetLicenseInfoReply();
        reply.setInventory(generateLicenseInfo());
        if (msg.getAdditionSession() != null) {
            List<AdditionSession> adds = JSONObjectUtil.toCollection(msg.getAdditionSession(), ArrayList.class, AdditionSession.class);
            adds.forEach(addition -> {
                List<AdditionalLicenseInfo> additionalLicenseInfos = generateAdditionalLicenseInfo(addition);
                if (!CollectionUtils.isEmpty(additionalLicenseInfos)) {
                    reply.getAdditions().addAll(additionalLicenseInfos);
                }
            });
        }
        bus.reply(msg, reply);
    }

    // This API message will list all USB-keys within the system (possibly multi-nodes).
    // c.f. GetLocalLicenseUKeyStatusMsg - which reads only local USB-key information.
    private void handle(APIGetLicenseUKeyStatusMsg msg) {
        final Collection<String> nodes = destMaker.getAllNodeInfo().stream()
                .map(ResourceDestinationMaker.NodeInfo::getNodeUuid)
                .collect(Collectors.toSet());
        final List<UKeyInventory> res = Collections.synchronizedList(new ArrayList<>());

        if (nodes.isEmpty()) { // although impossible ...
            nodes.add(Platform.getManagementServerId());
        }

        new While<>(nodes).step((mnUuid, whileComp) -> {
            GetLocalLicenseUKeyStatusMsg gmsg = new GetLocalLicenseUKeyStatusMsg();
            bus.makeServiceIdByManagementNodeId(gmsg, LicenseConstant.SERVICE_ID, mnUuid);
            bus.send(gmsg, new CloudBusCallBack(whileComp) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        whileComp.done();
                        return;
                    }

                    GetLocalLicenseUKeyStatusReply r = reply.castReply();
                    res.addAll(r.getInventories());
                    whileComp.done();
                }
            });
        }, 3).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList ignored) {
                APIGetLicenseUKeyStatusEvent evt = new APIGetLicenseUKeyStatusEvent(msg.getId());
                evt.setInventories(res);
                bus.publish(evt);
            }
        });
    }

    // Run zskey-util and use its output if any;
    // otherwise, check local cached record if any;
    // report none if still no luck.
    private void handle(GetLocalLicenseUKeyStatusMsg msg) {
        GetLocalLicenseUKeyStatusReply reply = new GetLocalLicenseUKeyStatusReply();
        PlatformLicense.UKeyLicenseInfo info = getUsbKeyInfo();
        if (info == null || info.getUKeyStatus() != UKeyStatus.Ready) {
            final UKeyLicenseVO licenseVO = PlatformLicense.markUKeyMissingAndGetLastRecord(Platform.getManagementServerId(), info);
            if (licenseVO == null) {
                reply.setInventories(Collections.emptyList());
            } else {
                UKeyInventory inv = new UKeyInventory();
                inv.setKeyId(licenseVO.getKeyId());
                inv.setManagementNodeUuid(licenseVO.getManagementNodeUuid());
                inv.setStatus(licenseVO.getStatus());
                reply.setInventories(Collections.singletonList(inv));
            }

            bus.reply(msg, reply);
            return;
        }

        PlatformLicense.updateUKeyStatusRecord(info);

        UKeyInventory inv = new UKeyInventory();
        inv.setStatus(info.getUKeyStatus());
        inv.setManagementNodeUuid(Platform.getManagementServerId());
        inv.setKeyId(info.getKeyid());
        reply.setInventories(Collections.singletonList(inv));
        bus.reply(msg, reply);
    }

    private void handle(APIGetLicenseRecordsMsg msg) {
        APIGetLicenseRecordsReply reply = new APIGetLicenseRecordsReply();
        if (msg.isReplyWithCount() || msg.isCount()) {
            reply.setTotal(Q.New(LicenseHistoryVO.class).eq(LicenseHistoryVO_.mergedTo, 0).count());
        }
        if (msg.isCount()) {
            bus.reply(msg, reply);
            return;
        }

        // STEP 1: query LicenseHistoryVO
        SingularAttribute<LicenseHistoryVO, Long> sortBy =
                "issuedDate".equals(msg.getSortBy()) ? LicenseHistoryVO_.issuedDate :
                "expiredDate".equals(msg.getSortBy()) ? LicenseHistoryVO_.expiredDate :
                LicenseHistoryVO_.uploadDate;
        SimpleQuery.Od sortDirection = "asc".equals(msg.getSortDirection()) ? SimpleQuery.Od.ASC : SimpleQuery.Od.DESC;
        Q query = Q.New(LicenseHistoryVO.class)
                .eq(LicenseHistoryVO_.mergedTo, 0)
                .orderBy(sortBy, sortDirection)
                .start(msg.getStart())
                .limit(msg.getLimit());
        query.notIn(LicenseHistoryVO_.prodInfo, getNonCloudProdInfoLicenseList());
        List<LicenseHistoryVO> vos = query.list();

        // STEP 2: check and set field: expired
        // STEP 2-1: find current record for this management node (LicenseHistoryVO#id matched)
        //           and its merged record (LicenseHistoryVO#mergedTo matched)
        List<LicenseHistoryVO> currentLicenses = findLicenseRecordByCurrentLicenses();
        Set<Long> currentHistoryIds = currentLicenses.stream()
                .flatMap(license -> Arrays.asList(license.getId(), license.getMergedTo()).stream())
                .filter(id -> id > 0)
                .collect(Collectors.toSet());

        // STEP 2-2: compare current license history id with license merged map
        List<LicenseInventory> invs = new ArrayList<>(vos.size());
        for (LicenseHistoryVO historyVO : vos) {
            boolean matched = currentHistoryIds.contains(historyVO.getId());

            LicenseInventory inv = LicenseInventory.valueOf(historyVO);
            inv.setExpired(!matched);
            invs.add(inv);
        }
        reply.setInventories(invs);
        bus.reply(msg, reply);
    }

    private List<LicenseHistoryVO> findLicenseRecordByCurrentLicenses() {
        List<LicenseInfo> currentLicenses = new ArrayList<>();
        currentLicenses.add(platformLicense.getLicenseInfo());
        currentLicenses.addAll(platformLicense.getLicenseAddOns());
        return new ArrayList<>(LicenseRecords.findLicenseRecordByLicenses(currentLicenses));
    }

    private void handle(AskLicenseCapacityMsg msg) {
        AskLicenseCapacityReply reply = new AskLicenseCapacityReply();
        LicenseInfo licenseInfo = getLicenseInfo();

        if (PlatformLicense.isLicenseExpired(licenseInfo)) {
            reply.setError(operr("License expired"));
            bus.reply(msg, reply);
            return;
        }

        ErrorCode err;
        if (licenseInfo.getCpuNum() != null) {
            err = checkCpuNum(msg.isForKVM(), msg.getCpuNum());
        } else if (licenseInfo.getHostNum() != null) {
            err = checkHostNum(msg.isForKVM(), msg.getHostNum());
        } else {
            err = checkVmNum(msg.isForKVM(), msg.getVmNum());
        }

        if (err != null) {
            reply.setError(err);
        }

        bus.reply(msg, reply);
    }

    private void handle(APIRegisterLicenseRequestedApplicationMsg msg) {
        APIRegisterLicenseRequestedApplicationEvent event = new APIRegisterLicenseRequestedApplicationEvent(msg.getId());
        String appId = registerApplication(msg.getLicenseRequestCode());
        event.setAppId(appId);
        bus.publish(event);
    }

    private String registerApplication(String licenseRequestCode) {
        String appId = PlatformLicense.getRegisteredApplicationAppId(PlatformLicense.getThumbPrint(licenseRequestCode));
        if (appId != null) {
            SQL.New(RegisterLicenseApplicationVO.class)
                    .eq(RegisterLicenseApplicationVO_.appId, appId)
                    .set(RegisterLicenseApplicationVO_.licenseRequestCode, licenseRequestCode)
                    .update();
            logger.info(String.format("found registered application ID: %s. updating licenseRequestCode.", appId));
            return appId;
        }
        appId = PlatformLicense.getAppIdByApplicationCode(licenseRequestCode);
        RegisterLicenseApplicationVO vo = new RegisterLicenseApplicationVO();
        vo.setLicenseRequestCode(licenseRequestCode);
        vo.setAppId(appId);
        dbf.persist(vo);
        return vo.getAppId();
    }

    private void handle(GetLicenseMsg msg) {
        GetLicenseReply reply = new GetLicenseReply();
        List<LicenseExpiredTimeInfo> licenseInfos = new ArrayList<>();
        LicenseExpiredTimeInfo licenseExpiredTimeInfo = LicenseExpiredTimeInfo.valueOf(generateLicenseInfo());
        if (licenseExpiredTimeInfo != null) {
            licenseInfos.add(licenseExpiredTimeInfo);
        }
        for (LicenseInfo addon : getLicenseAddOns()) {
            List<LicenseExpiredTimeInfo> licenseExpiredTimeInfos = LicenseExpiredTimeInfo.valueOf(addon);
            if (licenseExpiredTimeInfos != null) {
                licenseInfos.addAll(licenseExpiredTimeInfos);
            }
        }
        reply.setLicenseExpiredTimeInfos(licenseInfos);
        bus.reply(msg, reply);
    }

    public AdditionalLicenseFactory getAdditionalLicenseFactory(AdditionalLicenseType type) {
        AdditionalLicenseFactory factory = additionalLicenseFactoryMap.get(type.toString().toLowerCase());
        if (factory == null) {
            throw new CloudRuntimeException(String.format("No AdditionalLicenseFactory for type: %s found", type));
        }
        return factory;
    }


    private List<AdditionalLicenseInfo> generateAdditionalLicenseInfo(AdditionSession addition) {
        if (addition == null) {
            return null;
        }
        if (addition.type == null) {
            return null;
        }
        AdditionalLicenseType type = AdditionalLicenseType.valueOf(addition.type);
        AdditionalLicenseFactory factory = getAdditionalLicenseFactory(type);
        boolean needContent = factory.isLicenseContentRequired();
        List<LicenseInfo> licenses = getAllLicensesRelatedToAppId(addition.appId, needContent, type.name());
        return factory.getAdditionalLicenseInfos(addition, licenses);
    }

    private boolean filterUKeyLicenseByAppId(LicenseInfo uKeyLicense, String appId) {
        List<String> licenseIds = Q.New(LicenseAppIdRefVO.class)
                .select(LicenseAppIdRefVO_.licenseId)
                .eq(LicenseAppIdRefVO_.appId, appId).listValues();
        return  licenseIds.contains(uKeyLicense.getUuid());
    }

    private void updateAdditionalLicense(String license, AdditionSession addition, SessionInventory session) {
        if (addition == null) {
            return;
        }
        if (addition.type == null) {
            return;
        }
        AdditionalLicenseType type = AdditionalLicenseType.valueOf(addition.type);
        AdditionalLicenseFactory factory = getAdditionalLicenseFactory(type);
        factory.updateAdditionalLicense(license, addition, session);
    }

    private LicenseInventory generateLicenseInfo() {
        final LicenseInventory inv = new LicenseInventory();
        final LicenseInfo licenseInfo = getLicenseInfo();
        final LicenseType licenseType = licenseInfo.getLicenseType();
        inv.setLicenseType(licenseType.toString());

        switch (licenseType) {
        case Community:
            inv.setHostNum(licenseInfo.getHostNum());
            inv.setExpiredDate(null);
            inv.setUser(null);
            inv.setProdInfo(licenseInfo.getProdInfo().toString());
            inv.setIssuedDate(null);
            inv.setAvailableHostNum(getAvailableHostNum());
            break;
        case TrialExt: case Trial: case Paid:
            inv.setUuid(licenseInfo.getUuid());
            inv.setIssuedDate(licenseInfo.getIssueTime().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
            inv.setExpiredDate(licenseInfo.getExpireTime().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
            inv.setUser(licenseInfo.getUser());
            inv.setProdInfo(licenseInfo.getProdInfo().toString());
            inv.setHostNum(licenseInfo.getHostNum());
            inv.setCpuNum(licenseInfo.getCpuNum());
            inv.setVmNum(licenseInfo.getVmNum());
            inv.setAvailableHostNum(getAvailableHostNum());
            inv.setAvailableCpuNum(getAvailableCpuNum());
            inv.setAvailableVmNum(getAvailableVmNum());
            break;
        default:
            throw new CloudRuntimeException("unexpected license type: "+inv.getLicenseType());
        }

        inv.setManagementNodeUuid(Platform.getManagementServerId());
        inv.setLicenseRequest(licenseApplicationCode);
        inv.setPlatformId(MevocoSystemTags.PLATFORM_ID.getTokenByResourceUuid(
                AccountConstant.INITIAL_SYSTEM_ADMIN_UUID,
                MevocoSystemTags.PLATFORM_ID_TOKEN
        ));
        inv.setExpired(PlatformLicense.isLicenseExpired(licenseInfo));

        return inv;
    }

    // get available number of cpu sockets that are not in esx host or aarch64 host or baremetal2 gateway
    // the available number of cpu sockets in esx host or aarch64 host is in their own license addon info
    // the number of baremetal2 gateway cpu sockets is not important
    // special case: if available esx cpu sockets is 0, new exs cpu sockets will consume main license
    private Integer getAvailableCpuNum() {
        if (getLicenseInfo().getCpuNum() == null) {
            return null;
        }

        Long usedCpus = SQL.New("select sum(h.cpuSockets) from HostCapacityVO h", Long.class).find();
        if (usedCpus == null) {
            return getLicenseInfo().getCpuNum();
        }

        long existingVmwCpuSocketsNum = getVmwareCpuSocketsNum();
        long licensedVmwCpuNum = getLicensedVmwareCpuSocketsNum();
        long usedArm64AddOnCpuNum = usedArm64AddOnHostCpuMap.values().stream().mapToLong(Long::longValue).sum();
        long gatewayCpuNum = getBareMetal2GatewayCpuSocketsNum();

        int totalPrimaryLicensedCpuNum = getLicenseInfo().getCpuNum() + (int) usedArm64AddOnCpuNum +  (int) gatewayCpuNum;

        if (licensedVmwCpuNum == 0) {
            // if vmware license not available do not calculate vmware cpu sockets
            usedCpus = usedCpus - existingVmwCpuSocketsNum;
        } else if (licensedVmwCpuNum <= existingVmwCpuSocketsNum) {
            // if licensed vmware cpu sockets is less than existing vmware cpu sockets
            // the new vmware cpu sockets will consume main license
            // only subtract licensed vmware cpu sockets from usedCpus
            usedCpus = usedCpus - licensedVmwCpuNum;
        } else {
            usedCpus = usedCpus - existingVmwCpuSocketsNum;
        }

        return Math.toIntExact(totalPrimaryLicensedCpuNum - usedCpus);
    }

    // get available number of host that are not esx host or aarch64 host or baremetal2 gateway
    // the available number of esx host or aarch64 host is in their own license addon info
    // the number of baremetal2 gateway is not important
    // special case: if available esx host is 0, new exs host will consume main license
    private Integer getAvailableHostNum() {
        if (getLicenseInfo().getHostNum() == null) {
            return null;
        }

        long hostNum = Q.New(HostVO.class).count();
        long existingVmwHostNum = getVmwareHostNum();
        long licensedVmwHostNum = getLicensedVmwareHostNum();
        long usedArm64AddOnHostNum = usedArm64AddOnHostCpuMap.keySet().size();
        long bmGatewayNum = getBareMetal2GatewayNum();
        long nativeHostNum = getNativeHostNum();

        if (licensedVmwHostNum >= existingVmwHostNum) {
            return getLicenseInfo().getHostNum() + (int) (existingVmwHostNum + usedArm64AddOnHostNum + bmGatewayNum + nativeHostNum - hostNum);
        } else {
            return getLicenseInfo().getHostNum() + (int) (licensedVmwHostNum + usedArm64AddOnHostNum + bmGatewayNum + nativeHostNum - hostNum);
        }
    }

    // get available number of user vm that are not vmware vm or aarch64 vm
    // the available number of vmware vm or aarch64 vm is in their own license addon info
    // special case: if available vmware vm is 0, new vmware vm will consume main license
    private Integer getAvailableVmNum() {
        if (getLicenseInfo().getVmNum() == null) {
            return null;
        }

        long userVmNum = Q.New(VmInstanceVO.class).eq(VmInstanceVO_.type, VmInstanceConstant.USER_VM_TYPE).count();
        long existingVmwVmNum = getVmwareVmNum();
        long licensedVmwVmNum = getLicensedVmwareVmNum();
        long arm64VmNum = getArm64VmNum();

        if (licensedVmwVmNum >= existingVmwVmNum) {
            return getLicenseInfo().getVmNum() + (int) (existingVmwVmNum + arm64VmNum - userVmNum);
        } else {
            return getLicenseInfo().getVmNum() + (int) (licensedVmwVmNum + arm64VmNum - userVmNum);
        }
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(LicenseConstant.SERVICE_ID);
    }

    List<LicenseInfo> getLicenseInfoFromSpecifiedPath(String rootPath) {
        return platformLicense.getLicenseInfoFromSpecifiedPath(ca, rootPath, false, null);
    }

    private void upgradeLicenseRecordsForMINI() {
        List<LicenseHistoryVO> records = findLicenseRecordByCurrentLicenses();
        if (!records.isEmpty()) {
            return;
        }
        LicenseRecords.saveAll(Collections.singletonList(getLicenseInfo()), LicenseUpdateSource.InternalMINI);
    }

    private List<String> getNonCloudProdInfoLicenseList() {
        if (DeployMode.cube.toString().equals(MevocoGlobalProperty.DEPLOY_MODE)) {
            // display zstone addon license in cube
            List<String> cubeNonCloudProdInfoLicenseList = new ArrayList<>(nonCloudProdInfoLicenseList);
            cubeNonCloudProdInfoLicenseList.removeAll(Arrays.asList(
                    AddOnModuleType.ZStoneRBD.name,
                    AddOnModuleType.ZStoneRGW.name,
                    AddOnModuleType.ZStoneISCSI.name
            ));

            return cubeNonCloudProdInfoLicenseList;
        }
            return nonCloudProdInfoLicenseList;
    }
}
