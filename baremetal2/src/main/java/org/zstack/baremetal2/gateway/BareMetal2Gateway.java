package org.zstack.baremetal2.gateway;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;
import org.zstack.baremetal2.*;
import org.zstack.baremetal2.chassis.*;
import org.zstack.baremetal2.chassis.ipmi.BareMetal2IpmiChassisHelper;
import org.zstack.baremetal2.chassis.ipmi.BareMetal2IpmiChassisInventory;
import org.zstack.baremetal2.chassis.ipmi.BareMetal2IpmiChassisVO;
import org.zstack.baremetal2.gateway.BareMetal2GatewayCanonicalEvents.BareMetal2GatewayDeletedData;
import org.zstack.baremetal2.gateway.BareMetal2GatewayCommands.VolumeTO;
import org.zstack.baremetal2.gateway.BareMetal2GatewayCommands.*;
import org.zstack.baremetal2.instance.*;
import org.zstack.baremetal2.provisionnetwork.*;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.console.ConsoleConstants;
import org.zstack.header.core.*;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.host.*;
import org.zstack.header.image.ImageVO;
import org.zstack.header.image.ImageVO_;
import org.zstack.header.message.*;
import org.zstack.header.network.l2.*;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO_;
import org.zstack.header.rest.AsyncRESTCallback;
import org.zstack.header.rest.JsonAsyncRESTCallback;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.storage.addon.primary.*;
import org.zstack.header.storage.primary.*;
import org.zstack.header.vm.*;
import org.zstack.header.volume.VolumeInventory;
import org.zstack.header.volume.VolumeVO;
import org.zstack.header.volume.VolumeVO_;
import org.zstack.header.volume.block.*;
import org.zstack.kvm.*;
import org.zstack.mevoco.ShareableVolumeVmInstanceRefVO;
import org.zstack.mevoco.ShareableVolumeVmInstanceRefVO_;
import org.zstack.storage.addon.primary.BlockExternalPrimaryStorageFactory;
import org.zstack.storage.addon.primary.ExternalPrimaryStorageFactory;
import org.zstack.storage.ceph.CephGlobalConfig;
import org.zstack.storage.ceph.CephSystemTags;
import org.zstack.storage.ceph.MonStatus;
import org.zstack.storage.ceph.primary.CephPrimaryStorageMonVO;
import org.zstack.storage.ceph.primary.CephPrimaryStorageVO;
import org.zstack.storage.volume.block.BlockConstant;
import org.zstack.tag.PatternedSystemTag;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.SystemTagUtils;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.VersionComparator;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import javax.persistence.Tuple;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static java.util.Arrays.asList;
import static org.zstack.baremetal2.gateway.BareMetal2GatewayCanonicalEvents.BARE_METAL_2_GATEWAY_DELETED_PATH;
import static org.zstack.core.CoreGlobalProperty.HTTP_CONSOLE_PROXY_PORT;
import static org.zstack.core.Platform.err;
import static org.zstack.core.Platform.operr;
import static org.zstack.core.progress.ProgressReportService.reportProgress;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * Created by GuoYi on 9/28/20.
 */
public class BareMetal2Gateway extends KVMHost {
    private final static CLogger logger = Utils.getLogger(BareMetal2Gateway.class);

    // REST URL PATH TO GATEWAY PLUGIN
    private final String prepareProvisionNetworkPath;
    private final String destroyProvisionNetworkPath;
    private final String prepareVolumeForInstancePath;
    private final String destroyVolumeForInstancePath;
    private final String createProvisionCfgsForInstancePath;
    private final String deleteProvisionCfgsForInstancePath;
    private final String createConsoleProxyForInstancePath;
    private final String getVolumeLunIdPath;
    private final String prepareVolumeConvertConfigPath;
    private final String destroyVolumeConvertConfigPath;
    private final String GetAccessPathInfoPath;
    private final String CheckStaticProvisionIpPath;

    @Autowired
    protected RESTFacade restf;
    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    protected KVMExtensionEmitter extEmitter;
    @Autowired
    protected BareMetal2IpmiChassisHelper chassisHelper;
    @Autowired
    protected BareMetal2GatewayExtensionPointEmitter gatewayExtpEmitter;
    @Autowired
    private ExternalPrimaryStorageFactory extPsFactory;

    @Autowired
    @Qualifier("BareMetal2GatewayFactory")
    public void set(KVMHostFactory factory) {
        super.factory = factory;
    }

    private BareMetal2GatewayVO self;

    public BareMetal2Gateway(BareMetal2GatewayVO self, KVMHostContext context) {
        super(self, context);
        this.self = self;
        String baseUrl = context.getBaseUrl();

        UriComponentsBuilder ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.PREPARE_PROVISION_NETWORK_PATH);
        prepareProvisionNetworkPath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.DESTROY_PROVISION_NETWORK_PATH);
        destroyProvisionNetworkPath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.CREATE_CONFIGURATIONS_FOR_INSTANCE_PATH);
        createProvisionCfgsForInstancePath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.DELETE_CONFIGURATIONS_FOR_INSTANCE_PATH);
        deleteProvisionCfgsForInstancePath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.CREATE_CONSOLE_PROXY_FOR_INSTANCE_PATH);
        createConsoleProxyForInstancePath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.PREPARE_VOLUME_FOR_INSTANCE_PATH);
        prepareVolumeForInstancePath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.DESTROY_VOLUME_FOR_INSTANCE_PATH);
        destroyVolumeForInstancePath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.GET_VOLUME_LUNID_FOR_INSTANCE_PATH);
        getVolumeLunIdPath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.PREPARE_VOLUME_CONVERT_PATH);
        prepareVolumeConvertConfigPath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.DESTROY_VOLUME_CONVERT_PATH);
        destroyVolumeConvertConfigPath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.GET_ACCESS_PATH_INFO_PATH);
        GetAccessPathInfoPath = ub.build().toUriString();

        ub = UriComponentsBuilder.fromHttpUrl(baseUrl);
        ub.path(BareMetal2GatewayConstant.CHECK_STATIC_PROVISION_IP_PATH);
        CheckStaticProvisionIpPath = ub.build().toUriString();
    }

    private String buildBmInstanceAgentUrl(String subPath, String uuid) {
        UriComponentsBuilder ub = UriComponentsBuilder.newInstance();
        ub.scheme(BareMetal2GlobalProperty.BM_AGENT_URL_SCHEME);
        if (CoreGlobalProperty.UNIT_TEST_ON) {
            ub.host("localhost");
        } else {
            ub.host(self.getManagementIp());
        }

        // to reduce nginx port usage, we reuse HTTP_CONSOLE_PROXY_PORT as BM_AGENT_PORT
        ub.port(HTTP_CONSOLE_PROXY_PORT);
        if (!"".equals(BareMetal2GlobalProperty.BM_AGENT_URL_ROOT_PATH)) {
            ub.path(BareMetal2GlobalProperty.BM_AGENT_URL_ROOT_PATH);
        }

        subPath = subPath.replace(".*", uuid);
        ub.path(subPath);
        return ub.build().toUriString();
    }

    @Override
    public BareMetal2GatewayInventory getSelfInventory() {
        return BareMetal2GatewayInventory.valueOf(self);
    }

    @Override
    public void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIAttachBareMetal2GatewayToClusterMsg) {
            handle((APIAttachBareMetal2GatewayToClusterMsg) msg);
        } else if (msg instanceof APIDetachBareMetal2GatewayFromClusterMsg) {
            handle((APIDetachBareMetal2GatewayFromClusterMsg) msg);
        } else if (msg instanceof APIUpdateBareMetal2GatewayMsg) {
            handle((APIUpdateBareMetal2GatewayMsg) msg);
        } else if (msg instanceof APIDeleteBareMetal2GatewayMsg) {
            handle((APIDeleteBareMetal2GatewayMsg) msg);
        } else if (msg instanceof APIChangeBareMetal2GatewayStateMsg) {
            handle((APIChangeBareMetal2GatewayStateMsg) msg);
        } else if (msg instanceof APIReconnectBareMetal2GatewayMsg) {
            handle((APIReconnectBareMetal2GatewayMsg) msg);
        } else if (msg instanceof APIChangeBareMetal2GatewayClusterMsg) {
            handle((APIChangeBareMetal2GatewayClusterMsg) msg);
        } else {
            super.handleApiMessage(msg);
        }
    }

    @Override
    public void handleLocalMessage(Message msg) {
        if (msg instanceof PrepareProvisionNetworkInGatewayMsg) {
            handle((PrepareProvisionNetworkInGatewayMsg) msg);
        } else if (msg instanceof DestroyProvisionNetworkInGatewayMsg) {
            handle((DestroyProvisionNetworkInGatewayMsg) msg);
        } else if (msg instanceof CreateProvisionConfigurationInGatewayMsg) {
            handle((CreateProvisionConfigurationInGatewayMsg) msg);
        } else if (msg instanceof DeleteProvisionConfigurationInGatewayMsg) {
            handle((DeleteProvisionConfigurationInGatewayMsg) msg);
        } else if (msg instanceof CreateConsoleProxyInGatewayMsg) {
            handle((CreateConsoleProxyInGatewayMsg) msg);
        } else if (msg instanceof AttachBareMetal2GatewayToClusterMsg) {
            handle((AttachBareMetal2GatewayToClusterMsg) msg);
        } else if (msg instanceof DetachBareMetal2GatewayFromClusterMsg) {
            handle((DetachBareMetal2GatewayFromClusterMsg) msg);
        } else if (msg instanceof ChangeBareMetal2InstanceDefaultNetworkMsg) {
            handle((ChangeBareMetal2InstanceDefaultNetworkMsg) msg);
        } else if (msg instanceof PingBareMetal2InstanceThroughGatewayMsg) {
            handle((PingBareMetal2InstanceThroughGatewayMsg) msg);
        } else if (msg instanceof ChangeInstancePasswordThroughGatewayMsg) {
            handle((ChangeInstancePasswordThroughGatewayMsg) msg);
        } else if (msg instanceof CheckNetworkPhysicalInterfaceMsg) {
            handle((CheckNetworkPhysicalInterfaceMsg) msg);
        } else if (msg instanceof BatchCheckNetworkPhysicalInterfaceMsg) {
            handle((BatchCheckNetworkPhysicalInterfaceMsg) msg);
        } else if (msg instanceof ConvertVolumeToChassisLocalDiskMsg) {
            handle((ConvertVolumeToChassisLocalDiskMsg) msg);
        } else if (msg instanceof PrepareConvertVolumeConfigMsg) {
            handle((PrepareConvertVolumeConfigMsg) msg);
        } else if (msg instanceof DestroyConvertVolumeConfigMsg) {
            handle((DestroyConvertVolumeConfigMsg) msg);
        } else if (msg instanceof AttachProvisionNicToBondingMsg) {
            handle((AttachProvisionNicToBondingMsg) msg);
        } else if (msg instanceof DetachProvisionNicToBondingMsg) {
            handle((DetachProvisionNicToBondingMsg) msg);
        } else if (msg instanceof InspectBareMetal2ThroughGatewayMsg) {
            handle((InspectBareMetal2ThroughGatewayMsg) msg);
        } else if (msg instanceof CheckStaticProvisionIpInGatewayMsg) {
            handle((CheckStaticProvisionIpInGatewayMsg) msg);
        } else {
            super.handleLocalMessage(msg);
        }
    }

    private void handle(CheckStaticProvisionIpInGatewayMsg msg) {
        checkStatus();

        final CheckStaticProvisionIpInGatewayReply reply = new CheckStaticProvisionIpInGatewayReply();
        CheckStaticProvisionIpCmd cmd = new CheckStaticProvisionIpCmd();
        String gatewayProvisionIp = Q.New(BareMetal2GatewayProvisionNicVO.class)
                .select(BareMetal2GatewayProvisionNicVO_.ip)
                .eq(BareMetal2GatewayProvisionNicVO_.uuid, self.getUuid())
                .findValue();
        cmd.setProvisionIp(msg.getProvisionIp());
        cmd.setGatewayIp(gatewayProvisionIp);
        cmd.setGatewayUuid(msg.getGatewayUuid());
        restf.asyncJsonPost(CheckStaticProvisionIpPath, cmd, new JsonAsyncRESTCallback<CheckStaticProvisionIpRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(operr("provision ip %s is unavailable, it can not connect to the gateway %s, detail:%s",
                        msg.getProvisionIp(), msg.getGatewayUuid(), err));
                bus.reply(msg, reply);
            }

            @Override
            public void success(CheckStaticProvisionIpRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("provision ip %s is unavailable, it can not connect to the gateway %s, detail:%s",
                            msg.getProvisionIp(), msg.getGatewayUuid(), ret.getError()));
                }
                bus.reply(msg, reply);
            }

            @Override
            public Class<CheckStaticProvisionIpRsp> getReturnClass() {
                return CheckStaticProvisionIpRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    private void handle(InspectBareMetal2ThroughGatewayMsg msg) {
        checkStatus();

        final InspectBareMetal2ThroughGatewayReply reply = new InspectBareMetal2ThroughGatewayReply();
        BareMetal2InstanceVO instanceVO = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);

        String clusterUuid = Q.New(BareMetal2GatewayClusterRefVO.class)
                .select(BareMetal2GatewayClusterRefVO_.clusterUuid)
                .eq(BareMetal2GatewayClusterRefVO_.gatewayUuid, msg.getGatewayUuid())
                .findValue();
        String networkUuid = Q.New(BareMetal2ProvisionNetworkClusterRefVO.class)
                .select(BareMetal2ProvisionNetworkClusterRefVO_.networkUuid)
                .eq(BareMetal2ProvisionNetworkClusterRefVO_.clusterUuid, clusterUuid)
                .findValue();

        BareMetal2ProvisionNetworkVO networkVO = dbf.findByUuid(networkUuid, BareMetal2ProvisionNetworkVO.class);
        BareMetal2IpmiChassisVO chassisVO = dbf.findByUuid(instanceVO.getChassisUuid(), BareMetal2IpmiChassisVO.class);

        InspectBareMetal2ByInstanceCmd cmd = new InspectBareMetal2ByInstanceCmd();
        cmd.setProvisionNetwork(networkVO.getDhcpRangeStartIp());
        cmd.setIpmiAddress(chassisVO.getIpmiAddress());
        cmd.setIpmiPort(chassisVO.getIpmiPort());
        cmd.setGatewayUuid(msg.getGatewayUuid());

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.INSPECT_BM_CHASSIS_BY_INSTANCE_PATH, msg.getInstanceUuid());
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<InspectBareMetal2ByInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                if (err.getCode().equals(SysErrors.HTTP_ERROR.toString())) {
                    reply.setError(err(BareMetal2Errors.NGINX_CONNECTION_ERROR, "failed to inspect baremetal2 chassis by instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), msg.getGatewayUuid(), err.getDetails()));
                } else {
                    reply.setError(operr("failed to inspect baremetal2 chassis by instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), msg.getGatewayUuid(), err));
                }
                bus.reply(msg, reply);
            }

            @Override
            public void success(InspectBareMetal2ByInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to inspect baremetal2 chassis by instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), msg.getGatewayUuid(), ret.getError()));
                    bus.reply(msg, reply);
                    return;
                }

                logger.debug(String.format("successfully inspect baremetal2 chassis by instance[uuid:%s] " +
                        "through gateway[uuid:%s]", msg.getInstanceUuid(), msg.getGatewayUuid()));
                BareMetal2ChassisHardwareInfoSyncer syncer = new BareMetal2ChassisHardwareInfoSyncer(chassisVO.getUuid());
                syncer.addHardwareInfo(ret.getHardwareInfo(), new Completion(msg) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                    }
                });
            }

            @Override
            public Class<InspectBareMetal2ByInstanceRsp> getReturnClass() {
                return InspectBareMetal2ByInstanceRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    @Override
    protected void handle(CheckNetworkPhysicalInterfaceMsg msg) {
        logger.info(String.format("Baremetal2 Gateway[%s] skip check %s physical interface", msg.getHostUuid(), msg.getPhysicalInterface()));
        CheckNetworkPhysicalInterfaceReply reply = new CheckNetworkPhysicalInterfaceReply();
        bus.reply(msg, reply);
    }

    protected void destroyVolumeConvertConfig(DestroyConvertVolumeConfigMsg msg, NoErrorCompletion completion) {
        checkStatus();

        final DestroyConvertVolumeConfigReply reply = new DestroyConvertVolumeConfigReply();
        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);

        DestroyConvertVolumeConfigCmd cmd = new DestroyConvertVolumeConfigCmd();
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid(), msg.getStorageGatewayIpAddress()));
        cmd.setVolume(buildVolume(bm.getRootVolumeUuid(), bm.getUuid()));
        cmd.setGatewayUuid(msg.getGatewayUuid());

        if (cmd.getBmInstance().getProvisionMac() == null) {
            reply.setError(operr("no provision nic found for baremetal2 instance[uuid:%s]", msg.getInstanceUuid()));
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        restf.asyncJsonPost(destroyVolumeConvertConfigPath, cmd, new JsonAsyncRESTCallback<DestroyConvertVolumeConfigRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(operr("failed to delete convert volume to chassis local disk configurations in gateway[uuid:%s] " +
                        "for baremetal2 instance[uuid:%s]", self.getUuid(), msg.getInstanceUuid()));
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void success(DestroyConvertVolumeConfigRsp ret) {
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public Class<DestroyConvertVolumeConfigRsp> getReturnClass() {
                return DestroyConvertVolumeConfigRsp.class;
            }
        });
    }

    protected void handle(DetachProvisionNicToBondingMsg msg) {
        DetachProvisionNicToBondingReply reply = new DetachProvisionNicToBondingReply();
        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);
        if (bm.getStatus() != BareMetal2InstanceStatus.Connected) {
            reply.setError(operr("baremetal2 instance[uuid:%s] is not connected, cannot detach provision nic from bonding", bm.getUuid()));
            bus.reply(msg, reply);
            return;
        }

        DetachNicFromInstanceCmd cmd = new DetachNicFromInstanceCmd();
        cmd.setBmInstance(buildBmInstance(bm.getUuid()));
        cmd.setNic(buildProvisionNic(msg.getInstanceUuid()));
        cmd.setGatewayUuid(msg.getGatewayUuid());

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.DETACH_NIC_FROM_BM_INSTANCE_PATH, msg.getInstanceUuid());
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<DetachNicFromInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
            }

            @Override
            public void success(DetachNicFromInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to detach provision nic to bonding on baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), self.getUuid(), ret.getError()));
                } else {
                    logger.info(String.format("successfully detach provision nic to bonding on baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s]", msg.getInstanceUuid(), self.getUuid()));
                }

                BareMetal2BondingNicRefVO ref = Q.New(BareMetal2BondingNicRefVO.class)
                        .eq(BareMetal2BondingNicRefVO_.provisionNicUuid, msg.getInstanceUuid())
                        .find();
                dbf.removeByPrimaryKey(ref.getBondingUuid(), BareMetal2BondingVO.class);
                bus.reply(msg, reply);
            }

            @Override
            public Class<DetachNicFromInstanceRsp> getReturnClass() {
                return DetachNicFromInstanceRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    protected void handle(AttachProvisionNicToBondingMsg msg) {
        AttachProvisionNicToBondingReply reply = new AttachProvisionNicToBondingReply();
        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);
        if (bm.getStatus() != BareMetal2InstanceStatus.Connected) {
            reply.setError(operr("baremetal2 instance[uuid:%s] not connected, cannot attach provision nic to bond", msg.getInstanceUuid()));
            bus.reply(msg, reply);
            return;
        }

        AttachNicToInstanceCmd cmd = new AttachNicToInstanceCmd();
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid()));
        cmd.setNic(buildProvisionNic(msg.getInstanceUuid()));
        cmd.setGatewayUuid(self.getUuid());

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.ATTACH_NIC_TO_BM_INSTANCE_PATH, msg.getInstanceUuid());
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<AttachNicToInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
            }

            @Override
            public void success(AttachNicToInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to attach provision nic to bonding on baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), self.getUuid(), ret.getError()));
                } else {
                    logger.info(String.format("successfully attach provision nic to bonding on baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s]", msg.getInstanceUuid(), self.getUuid()));
                }
                bus.reply(msg, reply);
            }

            @Override
            public Class<AttachNicToInstanceRsp> getReturnClass() {
                return AttachNicToInstanceRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    protected void handle(DestroyConvertVolumeConfigMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("change-dnsmasq-config-on-gateway:%s", msg.getGatewayUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                destroyVolumeConvertConfig(msg, new NoErrorCompletion() {
                    @Override
                    public void done() {
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("destroy-volume-convert-config-on-gateway:%s", msg.getGatewayUuid());
            }

            @Override
            public int getSyncLevel() {
                return BareMetal2GlobalConfig.BAREMETAL2_UPDATE_CONFIG_PARALLELISM_DEGREE.value(Integer.class);
            }
        });

    }

    private void prepareVolumeConvertConfig(PrepareConvertVolumeConfigMsg msg, NoErrorCompletion completion) {
        checkStatus();

        final PrepareConvertVolumeConfigReply reply = new PrepareConvertVolumeConfigReply();
        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);
        BareMetal2IpmiChassisVO chassis = dbf.findByUuid(msg.getChassisUuid(), BareMetal2IpmiChassisVO.class);
        BareMetal2ChassisDiskVO chassisDisk = dbf.findByUuid(msg.getChassisDiskUuid(), BareMetal2ChassisDiskVO.class);
        ChassisInfo chassisInfo = new ChassisInfo();
        chassisInfo.setAddress(chassis.getIpmiAddress());
        chassisInfo.setPort(chassis.getIpmiPort());

        PrepareConvertVolumeConfigCmd cmd = new PrepareConvertVolumeConfigCmd();
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid(), msg.getStorageGatewayIpAddress()));
        cmd.setVolume(buildVolume(bm.getRootVolumeUuid(), bm.getUuid()));
        cmd.setGatewayUuid(self.getUuid());
        cmd.setDestDiskWwn(chassisDisk.getWwn());
        cmd.setPort(HTTP_CONSOLE_PROXY_PORT);
        cmd.setChassisInfo(chassisInfo);

        if (BareMetal2SystemTags.EXTRA_BOOT_PARAMS.hasTag(chassis.getClusterUuid())) {
            cmd.setExtraBootParams(BareMetal2SystemTags.EXTRA_BOOT_PARAMS.getTokenByResourceUuid(chassis.getClusterUuid(), BareMetal2SystemTags.EXTRA_BOOT_PARAMS_TOKEN));
        }

        if (cmd.getBmInstance().getProvisionMac() == null) {
            reply.setError(operr("no provision nic found for baremetal2 instance[uuid:%s]", msg.getInstanceUuid()));
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        VolumeVO volumeVO = dbf.findByUuid(msg.getVolumeUuid(), VolumeVO.class);
        ExternalPrimaryStorageVO storageVO = Q.New(ExternalPrimaryStorageVO.class)
                .eq(ExternalPrimaryStorageVO_.uuid, volumeVO.getPrimaryStorageUuid())
                .find();

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();

        chain.setName(String.format("do-prepare-volume-convert-config-on-gateway:%s", msg.getGatewayUuid()));
        chain.then(new NoRollbackFlow() {
            String __name__ = "do-active-root-volume";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                PrimaryStorageControllerSvc controller = extPsFactory.getControllerSvc(storageVO.getUuid());
                controller.connect(storageVO.getConfig(), storageVO.getUrl(), new ReturnValueCompletion<LinkedHashMap>(completion) {
                    @Override
                    public void success(LinkedHashMap returnValue) {
                        BlockExternalPrimaryStorageFactory storageFactory = extPsFactory.blockExternalPrimaryStorageFactories.get(storageVO.getIdentity());
                        Map<String, String> info = storageFactory.getVolumeIscsiInfo(volumeVO.getInstallPath(), HostInventory.valueOf(self));
                        if (info.get(BlockConstant.ISCSI_TARGET_IQN) == null
                                || info.get(BlockConstant.ISCSI_GATEWAY_IP) == null) {
                            trigger.fail(operr("fail to get target iqn or gateway ip"));
                        } else {
                            VolumeTO volume = cmd.getVolume();
                            volume.setTargetIqn(info.get(BlockConstant.ISCSI_TARGET_IQN));
                            volume.setMonIp(info.get(BlockConstant.ISCSI_GATEWAY_IP));
                            trigger.next();
                        }
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public boolean skip(Map data) {
                return storageVO == null || !cmd.getBmInstance().getGatewayIp()
                        .equals(self.getProvisionNic().getIp());
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "do-prepare-volume-convert-config";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                restf.asyncJsonPost(prepareVolumeConvertConfigPath, cmd, new JsonAsyncRESTCallback<PrepareConvertVolumeConfigRsp>(reply) {
                    @Override
                    public void fail(ErrorCode err) {
                        trigger.fail(err);
                    }

                    @Override
                    public void success(PrepareConvertVolumeConfigRsp ret) {
                        if (!ret.isSuccess()) {
                            trigger.fail(operr("failed to create provision configurations for baremetal2 instance[uuid:%s] " +
                                    "in gateway[uuid:%s], because %s", msg.getInstanceUuid(), self.getUuid(), ret.getError()));
                        } else {
                            logger.info(String.format("successfully created convert volume to chassis local disk configurations for baremetal2 instance[uuid:%s] " +
                                    "in gateway[uuid:%s]", msg.getInstanceUuid(), self.getUuid()));
                            trigger.next();
                        }
                    }

                    @Override
                    public Class<PrepareConvertVolumeConfigRsp> getReturnClass() {
                        return PrepareConvertVolumeConfigRsp.class;
                    }
                });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                bus.reply(msg, reply);
                completion.done();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(msg, reply);
                completion.done();
            }
        }).start();
    }

    protected void handle(PrepareConvertVolumeConfigMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("change-dnsmasq-config-on-gateway:%s", msg.getGatewayUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                prepareVolumeConvertConfig(msg, new NoErrorCompletion() {
                    @Override
                    public void done() {
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("prepare-volume-convert-config-on-gateway:%s", msg.getGatewayUuid());
            }

            @Override
            public int getSyncLevel() {
                return BareMetal2GlobalConfig.BAREMETAL2_UPDATE_CONFIG_PARALLELISM_DEGREE.value(Integer.class);
            }
        });

    }

    protected void handle(ConvertVolumeToChassisLocalDiskMsg msg) {
        reportProgress("40");
        checkStatus();

        final ConvertRootVolumeToChassisLocalDiskReply reply = new ConvertRootVolumeToChassisLocalDiskReply();

        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);
        BareMetal2IpmiChassisVO chassis = dbf.findByUuid(msg.getChassisUuid(), BareMetal2IpmiChassisVO.class);
        BareMetal2ChassisDiskVO chassisDisk = dbf.findByUuid(msg.getChassisDiskUuid(), BareMetal2ChassisDiskVO.class);
        if (chassisDisk.getWwn() == null) {
            reply.setError(operr("chassis:%s disk does not have wwn info, please inspect chassis and try again", chassis.getUuid()));
            bus.reply(msg, reply);
            return;
        }

        boolean needUpdate = false;
        // set zoneUuid for ui display
        if (bm.getZoneUuid() == null) {
            bm.setZoneUuid(chassis.getZoneUuid());
            needUpdate = true;
        }

        // set chassisUuid for chassisOffering bm
        if (bm.getChassisUuid() == null) {
            bm.setChassisUuid(chassis.getUuid());
            needUpdate = true;
        }

        if (needUpdate) {
            dbf.update(bm);
        }

        String networkUuid = Q.New(BareMetal2ProvisionNetworkClusterRefVO.class)
                .eq(BareMetal2ProvisionNetworkClusterRefVO_.clusterUuid, chassis.getClusterUuid())
                .select(BareMetal2ProvisionNetworkClusterRefVO_.networkUuid)
                .findValue();

        List<String> gatewayUuids = BareMetal2Utils.getGatewayUuidsOnProvisionNetwork(networkUuid);
        if (gatewayUuids == null) {
            reply.setError(operr("make sure all baremetal2 gateways on provision network[uuid:%s] are Connected", networkUuid));
            bus.reply(msg, reply);
            return;
        }

        String storageGatewayIpAddress = Q.New(BareMetal2GatewayProvisionNicVO.class)
                .eq(BareMetal2GatewayProvisionNicVO_.uuid, msg.getGatewayUuid())
                .select(BareMetal2GatewayProvisionNicVO_.ip)
                .findValue();


        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("convert-root-volume-data-to-chassis-local-disk");
        chain.then(new Flow() {
            final String __name__ = "create-convert-volume-to-local-disk-config";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                //all gateways need config dnsmasq
                new While<>(gatewayUuids).each((uuid, cmpl) -> {
                    PrepareConvertVolumeConfigMsg pmsg = new PrepareConvertVolumeConfigMsg();
                    pmsg.setChassisUuid(msg.getChassisUuid());
                    pmsg.setInstanceUuid(msg.getInstanceUuid());
                    pmsg.setGatewayUuid(uuid);
                    pmsg.setVolumeUuid(msg.getVolumeUuid());
                    pmsg.setChassisDiskUuid(msg.getChassisDiskUuid());
                    pmsg.setStorageGatewayIpAddress(storageGatewayIpAddress);
                    bus.makeTargetServiceIdByResourceUuid(pmsg, HostConstant.SERVICE_ID, uuid);
                    bus.send(pmsg, new CloudBusCallBack(cmpl) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                cmpl.done();
                            } else {
                                cmpl.addError(reply.getError());
                                cmpl.allDone();
                            }
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList.getCauses().get(0));
                            return;
                        }

                        trigger.next();
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                new While<>(gatewayUuids).each((uuid, cmpl) -> {
                    DestroyConvertVolumeConfigMsg dmsg = new DestroyConvertVolumeConfigMsg();
                    dmsg.setInstanceUuid(msg.getInstanceUuid());
                    dmsg.setGatewayUuid(uuid);
                    dmsg.setStorageGatewayIpAddress(storageGatewayIpAddress);
                    bus.makeTargetServiceIdByResourceUuid(dmsg, HostConstant.SERVICE_ID, uuid);
                    bus.send(dmsg, new CloudBusCallBack(cmpl) {
                        @Override
                        public void run(MessageReply reply) {
                            cmpl.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        trigger.rollback();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "power-on-baremetal2-chassis-of-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!chassis.getType().equals(BareMetal2ChassisConstant.IPMI_CHASSIS_TYPE)) {
                    trigger.next();
                    return;
                }

                if (chassisHelper.powerOnBareMetal2Chassis(BareMetal2ChassisInventory.valueOf(chassis), BareMetal2ChassisConstant.CHASSIS_BOOT_DEV_IPXE)) {
                    trigger.next();
                } else {
                    trigger.fail(operr("failed to power on baremetal2 chassis[uuid:%s] using ipmitool", chassis.getUuid()));
                }
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "check-convert-data-progress";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (CoreGlobalProperty.UNIT_TEST_ON) {
                    trigger.next();
                    return;
                }

                final long current = System.currentTimeMillis();
                final long timeout = BareMetal2GlobalConfig.CONVERT_VOLUME_TO_LOCAL_DISK_TIMEOUT.value(Integer.class) * 60L * 1000L;
                final long expiredTime = current + timeout;
                final long interval = 10L * 1000L;

                final Timer timer = new Timer();
                timer.schedule(new TimerTask() {
                    long count = current;

                    @Override
                    public void run() {
                        BareMetal2InstanceVO instanceVO = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);

                        if (instanceVO.getStatus().equals(BareMetal2InstanceStatus.Converted)) {
                            timer.cancel();
                            trigger.next();
                            return;
                        }

                        if (instanceVO.getStatus().equals(BareMetal2InstanceStatus.ConvertFailed)) {
                            timer.cancel();
                            trigger.fail(operr("convert image data to local disk failed"));
                            return;
                        }

                        if (count >= expiredTime) {
                            BareMetal2ChassisVO bmc = dbf.findByUuid(chassis.getUuid(), BareMetal2ChassisVO.class);
                            bmc.setStatus(BareMetal2ChassisStatus.IPxeBootFailed);
                            dbf.update(bmc);
                            timer.cancel();
                            trigger.fail(operr(
                                    "baremetal2 instance[uuid:%s] convert volume failed on baremetal2 chassis[uuid:%s] , timeout after %s minutes ", instanceVO.getUuid(), chassis.getUuid(), BareMetal2GlobalConfig.CONVERT_VOLUME_TO_LOCAL_DISK_TIMEOUT.value(Integer.class)));
                        }
                        count += interval;
                    }
                }, 0, interval);
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "power-off-baremetal2-chassis-of-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!chassis.getType().equals(BareMetal2ChassisConstant.IPMI_CHASSIS_TYPE)) {
                    trigger.next();
                    return;
                }

                if (chassisHelper.powerOffBareMetal2Chassis(BareMetal2ChassisInventory.valueOf(chassis))) {
                    trigger.next();
                } else {
                    trigger.fail(operr("failed to power on baremetal2 chassis[uuid:%s] using ipmitool", chassis.getUuid()));
                }
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "destroy-convert-volume-to-local-disk-config";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                new While<>(gatewayUuids).each((uuid, cmpl) -> {
                    DestroyConvertVolumeConfigMsg dmsg = new DestroyConvertVolumeConfigMsg();
                    dmsg.setInstanceUuid(msg.getInstanceUuid());
                    dmsg.setGatewayUuid(uuid);
                    dmsg.setStorageGatewayIpAddress(storageGatewayIpAddress);
                    bus.makeTargetServiceIdByResourceUuid(dmsg, HostConstant.SERVICE_ID, uuid);
                    bus.send(dmsg, new CloudBusCallBack(cmpl) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                cmpl.done();
                            } else {
                                cmpl.addError(reply.getError());
                                cmpl.allDone();
                            }
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList.getCauses().get(0));
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "delete-root-volume-on-primary-storage";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                DeleteVolumeBitsOnPrimaryStorageMsg msg = new DeleteVolumeBitsOnPrimaryStorageMsg();
                msg.setPrimaryStorageUuid(bm.getRootVolume().getPrimaryStorageUuid());
                msg.setInstallPath(bm.getRootVolume().getInstallPath());
                msg.setHypervisorType(BareMetal2GatewayConstant.BM2_HYPERVISOR_TYPE);
                msg.setBitsType(VolumeVO.class.getSimpleName());
                msg.setBitsUuid(bm.getRootVolumeUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, PrimaryStorageConstant.SERVICE_ID, bm.getRootVolume().getPrimaryStorageUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        SQL.New(VolumeVO.class)
                                .eq(VolumeVO_.uuid, bm.getRootVolume().getUuid())
                                .set(VolumeVO_.primaryStorageUuid, null)
                                .set(VolumeVO_.installPath, null)
                                .set(VolumeVO_.size, 0L)
                                .set(VolumeVO_.actualSize, 0L)
                                .update();

                        RecalculatePrimaryStorageCapacityMsg rmsg = new RecalculatePrimaryStorageCapacityMsg();
                        rmsg.setPrimaryStorageUuid(bm.getRootVolume().getPrimaryStorageUuid());
                        bus.makeTargetServiceIdByResourceUuid(rmsg, PrimaryStorageConstant.SERVICE_ID, bm.getRootVolume().getPrimaryStorageUuid());
                        bus.send(rmsg);

                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                reportProgress("97");

                SystemTagCreator creator = BareMetal2SystemTags.CHASSIS_SYSTEM_DISK
                        .newSystemTagCreator(msg.getInstanceUuid());
                creator.setTagByTokens(map(e(BareMetal2SystemTags.CHASSIS_SYSTEM_DISK_TOKEN, chassisDisk.getWwn())));
                creator.inherent = false;
                creator.recreate = true;
                creator.create();
                BareMetal2InstanceVO bmVO = dbf.findByUuid(bm.getUuid(), BareMetal2InstanceVO.class);
                bmVO.setStatus(BareMetal2InstanceStatus.Disconnected);
                dbf.update(bmVO);
                bus.reply(msg, reply);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(msg, reply);
            }
        }).start();
    }

    @Override
    protected void handle(BatchCheckNetworkPhysicalInterfaceMsg msg) {
        logger.info(String.format("Baremetal2 Gateway[%s] skip batch check %s physical interface", msg.getHostUuid(), msg.getPhysicalInterfaces()));
        BatchCheckNetworkPhysicalInterfaceReply reply = new BatchCheckNetworkPhysicalInterfaceReply();
        bus.reply(msg, reply);
    }

    private void handle(APIChangeBareMetal2GatewayClusterMsg msg) {
        final APIChangeBareMetal2GatewayClusterEvent evt = new APIChangeBareMetal2GatewayClusterEvent(msg.getId());

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("change-baremetal2-gateway-cluster");
        chain.then(new NoRollbackFlow() {
            final String __name__ = "detach-gateway-from-old-cluster";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String oldCluster = Q.New(BareMetal2GatewayVO.class)
                        .eq(BareMetal2GatewayVO_.uuid, msg.getGatewayUuid())
                        .select(BareMetal2GatewayVO_.clusterUuid)
                        .findValue();

                DetachBareMetal2GatewayFromClusterMsg dmsg = new DetachBareMetal2GatewayFromClusterMsg();
                dmsg.setGatewayUuid(msg.getGatewayUuid());
                dmsg.setClusterUuid(oldCluster);
                bus.makeTargetServiceIdByResourceUuid(dmsg, HostConstant.SERVICE_ID, msg.getGatewayUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "attach-gateway-to-new-cluster";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                AttachBareMetal2GatewayToClusterMsg amsg = new AttachBareMetal2GatewayToClusterMsg();
                amsg.setGatewayUuid(msg.getGatewayUuid());
                amsg.setClusterUuid(msg.getClusterUuid());
                bus.makeTargetServiceIdByResourceUuid(amsg, HostConstant.SERVICE_ID, msg.getGatewayUuid());
                bus.send(amsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                self = dbf.reload(self);
                evt.setInventory(getSelfInventory());
                bus.publish(evt);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                evt.setError(errCode);
                bus.publish(evt);
            }
        }).start();
    }

    private void handle(APIReconnectBareMetal2GatewayMsg msg) {
        final APIReconnectBareMetal2GatewayEvent evt = new APIReconnectBareMetal2GatewayEvent(msg.getId());

        ReconnectHostMsg rmsg = new ReconnectHostMsg();
        rmsg.setHostUuid(self.getUuid());
        bus.makeTargetServiceIdByResourceUuid(rmsg, HostConstant.SERVICE_ID, self.getUuid());
        bus.send(rmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    self = dbf.reload(self);
                    evt.setInventory((getSelfInventory()));
                } else {
                    evt.setError(reply.getError());
                }
                bus.publish(evt);
            }
        });
    }

    private void handle(APIChangeBareMetal2GatewayStateMsg msg) {
        final APIChangeBareMetal2GatewayStateEvent evt = new APIChangeBareMetal2GatewayStateEvent(msg.getId());

        HostStateEvent stateEvent = HostStateEvent.valueOf(msg.getStateEvent());
        try {
            extpEmitter.preChange(self, stateEvent);
        } catch (HostException e) {
            evt.setError(err(SysErrors.CHANGE_RESOURCE_STATE_ERROR, e.getMessage()));
            bus.publish(evt);
            return;
        }

        ChangeHostStateMsg cmsg = new ChangeHostStateMsg();
        cmsg.setStateEvent(stateEvent.toString());
        cmsg.setUuid(msg.getUuid());
        cmsg.setJustChangeState(false);
        bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, cmsg.getUuid());
        bus.send(cmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    self = dbf.reload(self);
                    evt.setInventory(getSelfInventory());
                } else {
                    evt.setError(reply.getError());
                }

                bus.publish(evt);
            }
        });
    }

    protected void handle(APIDeleteBareMetal2GatewayMsg msg) {
        final APIDeleteBareMetal2GatewayEvent event = new APIDeleteBareMetal2GatewayEvent(msg.getId());

        final String issuer = BareMetal2GatewayVO.class.getSimpleName();
        final List<BareMetal2GatewayInventory> ctx = Collections.singletonList(BareMetal2GatewayInventory.valueOf(self));

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-baremetal2-gateway-%s", msg.getUuid()));
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }

        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(event);

                BareMetal2GatewayDeletedData d = new BareMetal2GatewayDeletedData();
                d.setGatewayUuid(msg.getUuid());
                evtf.fire(BARE_METAL_2_GATEWAY_DELETED_PATH, d);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(err(SysErrors.DELETE_RESOURCE_ERROR, errCode, errCode.getDetails()));
                bus.publish(event);
            }
        }).start();
    }

    private void handle(APIUpdateBareMetal2GatewayMsg msg) {
        final APIUpdateBareMetal2GatewayEvent evt = new APIUpdateBareMetal2GatewayEvent(msg.getId());

        boolean updated = false;
        if (msg.getName() != null) {
            self.setName(msg.getName());
            updated = true;
        }

        if (msg.getDescription() != null) {
            self.setDescription(msg.getDescription());
            updated = true;
        }

        if (msg.getManagementIp() != null) {
            self.setManagementIp(msg.getManagementIp());
            updated = true;
        }

        if (msg.getSshPort() != null) {
            self.setPort(msg.getSshPort());
            updated = true;
        }

        if (msg.getUsername() != null) {
            self.setUsername(msg.getUsername());
            updated = true;
        }

        if (msg.getPassword() != null) {
            self.setPassword(msg.getPassword());
            updated = true;
        }

        if (updated) {
            self = dbf.updateAndRefresh(self);
        }

        evt.setInventory(getSelfInventory());
        bus.publish(evt);
    }

    private void handle(APIAttachBareMetal2GatewayToClusterMsg msg) {
        final APIAttachBareMetal2GatewayToClusterEvent evt = new APIAttachBareMetal2GatewayToClusterEvent(msg.getId());
        AttachBareMetal2GatewayToClusterMsg amsg = new AttachBareMetal2GatewayToClusterMsg();
        amsg.setGatewayUuid(msg.getGatewayUuid());
        amsg.setClusterUuid(msg.getClusterUuid());
        bus.makeTargetServiceIdByResourceUuid(amsg, HostConstant.SERVICE_ID, msg.getGatewayUuid());
        bus.send(amsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    self = dbf.reload(self);
                    evt.setInventory(BareMetal2GatewayInventory.valueOf(self));
                } else {
                    evt.setError(reply.getError());
                }

                bus.publish(evt);
            }
        });
    }

    private void handle(AttachBareMetal2GatewayToClusterMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("attach-baremetal2-gateway-to-cluster-%s", msg.getClusterUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                attachGatewayToCluster(msg, new NoErrorCompletion(msg, chain) {
                    @Override
                    public void done() {
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("attach-baremetal2-gateway-%s-to-cluster-%s", msg.getGatewayUuid(), msg.getClusterUuid());
            }
        });
    }

    private void attachGatewayToCluster(AttachBareMetal2GatewayToClusterMsg msg, NoErrorCompletion completion) {
        final AttachBareMetal2GatewayToClusterReply reply = new AttachBareMetal2GatewayToClusterReply();
        try {
            gatewayExtpEmitter.preAttach(self, msg.getClusterUuid());
        } catch (BareMetal2Exception e) {
            reply.setError(e.getError());
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        gatewayExtpEmitter.beforeAttach(self, msg.getClusterUuid());
        attachHook(msg.getClusterUuid(), new Completion(msg, completion) {
            @Override
            public void success() {
                BareMetal2GatewayClusterRefVO ref = new BareMetal2GatewayClusterRefVO();
                ref.setClusterUuid(msg.getClusterUuid());
                ref.setGatewayUuid(msg.getGatewayUuid());
                dbf.persist(ref);

                self = dbf.reload(self);
                self.setClusterUuid(msg.getClusterUuid());
                self = dbf.updateAndRefresh(self);
                gatewayExtpEmitter.afterAttach(self, msg.getClusterUuid());

                logger.debug(String.format("successfully attached baremetal2 gateway[uuid:%s] to cluster[uuid:%s]",
                        msg.getGatewayUuid(), msg.getClusterUuid()));
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                gatewayExtpEmitter.failToAttach(self, msg.getClusterUuid());
                reply.setError(errorCode);
                bus.reply(msg, reply);
                completion.done();
            }
        });
    }

    private void attachHook(String clusterUuid, Completion completion) {
        String networkUuid = Q.New(BareMetal2ProvisionNetworkClusterRefVO.class)
                .eq(BareMetal2ProvisionNetworkClusterRefVO_.clusterUuid, clusterUuid)
                .select(BareMetal2ProvisionNetworkClusterRefVO_.networkUuid)
                .findValue();
        if (StringUtils.isEmpty(networkUuid)) {
            completion.success();
            return;
        }

        BareMetal2ProvisionNetworkVO network = dbf.findByUuid(networkUuid, BareMetal2ProvisionNetworkVO.class);
        DebugUtils.Assert(network != null, String.format("baremetal2 provision network[uuid:%s] does not exist", networkUuid));

        CheckNetworkPhysicalInterfaceMsg cmsg = new CheckNetworkPhysicalInterfaceMsg();
        cmsg.setHostUuid(self.getUuid());
        cmsg.setPhysicalInterface(network.getDhcpInterface());
        bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(null) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    CheckNetworkPhysicalInterfaceReply rly = reply.castReply();
                    completion.success();
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    private void handle(APIDetachBareMetal2GatewayFromClusterMsg msg) {
        final APIDetachBareMetal2GatewayFromClusterEvent evt = new APIDetachBareMetal2GatewayFromClusterEvent(msg.getId());
        DetachBareMetal2GatewayFromClusterMsg dmsg = new DetachBareMetal2GatewayFromClusterMsg();
        dmsg.setGatewayUuid(msg.getGatewayUuid());
        dmsg.setClusterUuid(msg.getClusterUuid());
        bus.makeTargetServiceIdByResourceUuid(dmsg, HostConstant.SERVICE_ID, msg.getGatewayUuid());
        bus.send(dmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    DetachBareMetal2GatewayFromClusterReply rly = reply.castReply();
                    self = dbf.reload(self);
                    evt.setInventory(BareMetal2GatewayInventory.valueOf(self));
                } else {
                    evt.setError(reply.getError());
                }

                bus.publish(evt);
            }
        });
    }

    private void handle(DetachBareMetal2GatewayFromClusterMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("detach-baremetal2-gateway-from-cluster-%s", msg.getClusterUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                detachGatewayFromCluster(msg, new NoErrorCompletion(msg, chain) {
                    @Override
                    public void done() {
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("detach-baremetal2-gateway-%s-from-cluster-%s", msg.getGatewayUuid(), msg.getClusterUuid());
            }
        });
    }

    private void detachGatewayFromCluster(DetachBareMetal2GatewayFromClusterMsg msg, NoErrorCompletion completion) {
        final DetachBareMetal2GatewayFromClusterReply reply = new DetachBareMetal2GatewayFromClusterReply();
        try {
            extpEmitter.preDelete(getSelfInventory());
            gatewayExtpEmitter.preDetach(self, msg.getClusterUuid());
        } catch (BareMetal2Exception | HostException e) {
            reply.setError(ErrorCode.fromString(e.getMessage()));
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        extpEmitter.beforeDelete(getSelfInventory());
        gatewayExtpEmitter.beforeDetach(self, msg.getClusterUuid());
        detachHook(msg.getClusterUuid(), new Completion(msg, completion) {
            @Override
            public void success() {
                SQL.New(BareMetal2GatewayClusterRefVO.class)
                        .eq(BareMetal2GatewayClusterRefVO_.clusterUuid, msg.getClusterUuid())
                        .eq(BareMetal2GatewayClusterRefVO_.gatewayUuid, msg.getGatewayUuid())
                        .delete();

                self = dbf.reload(self);
                extpEmitter.afterDelete(getSelfInventory());
                gatewayExtpEmitter.afterDetach(self, msg.getClusterUuid());

                logger.debug(String.format("successfully detached baremetal2 gateway[uuid:%s] from cluster[uuid:%s]",
                        msg.getGatewayUuid(), msg.getClusterUuid()));
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                gatewayExtpEmitter.failToDetach(self, msg.getClusterUuid());
                reply.setError(errorCode);
                bus.reply(msg, reply);
                completion.done();
            }
        });
    }

    private void detachHook(String clusterUuid, Completion completion) {
        completion.success();
    }

    private void handle(PrepareProvisionNetworkInGatewayMsg msg) {
        inQueue().name(String.format("prepare-provision-network-in-gateway-%s", self.getUuid()))
                .asyncBackup(msg)
                .run(chain -> prepareProvisionNetwork(msg, new NoErrorCompletion(chain) {
                    @Override
                    public void done() {
                        chain.next();
                    }
                }));
    }

    private void prepareProvisionNetwork(PrepareProvisionNetworkInGatewayMsg msg, NoErrorCompletion completion) {
        String networkUuid = msg.getNetworkInventory().getUuid();
        String clusterUuid = Q.New(BareMetal2GatewayVO.class)
                .select(BareMetal2GatewayVO_.clusterUuid)
                .eq(BareMetal2GatewayVO_.uuid, msg.getGatewayUuid())
                .findValue();
        PrepareProvisionNetworkInGatewayReply reply = new PrepareProvisionNetworkInGatewayReply();

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("prepare-baremetal2-provision-network-in-gateway-" + msg.getGatewayUuid());
        chain.then(new Flow() {
            final String __name__ = "allocate-ip-for-gateway-in-provision-network";

            @Override
            public boolean skip(Map data) {
                return Q.New(BareMetal2GatewayProvisionNicVO.class)
                        .eq(BareMetal2GatewayProvisionNicVO_.uuid, msg.getGatewayUuid())
                        .eq(BareMetal2GatewayProvisionNicVO_.networkUuid, networkUuid)
                        .isExists();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                BareMetal2ProvisionNetworkAllocateIpAddressMsg amsg = new BareMetal2ProvisionNetworkAllocateIpAddressMsg();
                amsg.setNetworkUuid(networkUuid);
                amsg.setResourceType(BareMetal2GatewayVO.class.getSimpleName());
                amsg.setResourceUuid(msg.getGatewayUuid());
                bus.makeTargetServiceIdByResourceUuid(amsg, BareMetal2ProvisionNetworkConstant.SERVICE_ID, networkUuid);
                bus.send(amsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                trigger.rollback();
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "call-gateway-agent-to-prepare-provision-network";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                PrepareProvisionNetworkInGatewayCmd cmd = new PrepareProvisionNetworkInGatewayCmd();
                ProvisionNetworkTO network = buildProvisionNetwork(msg.getNetworkInventory());

                String allocatedIp = Q.New(BareMetal2GatewayProvisionNicVO.class)
                        .eq(BareMetal2GatewayProvisionNicVO_.uuid, msg.getGatewayUuid())
                        .eq(BareMetal2GatewayProvisionNicVO_.networkUuid, networkUuid)
                        .select(BareMetal2GatewayProvisionNicVO_.ip)
                        .findValue();
                network.setProvisionNicIp(allocatedIp);

                if (BareMetal2SystemTags.EXTRA_BOOT_PARAMS.hasTag(clusterUuid)) {
                    network.setExtraBootParams(BareMetal2SystemTags.EXTRA_BOOT_PARAMS.getTokenByResourceUuid(clusterUuid, BareMetal2SystemTags.EXTRA_BOOT_PARAMS_TOKEN));
                }

                // Fix ZSTAC-32805
                network.setManagementIp(self.getManagementIp());
                network.setSendCommandUrl(restf.getSendCommandUrl());

                cmd.setProvisionNetwork(network);
                cmd.setGatewayUuid(msg.getGatewayUuid());
                restf.asyncJsonPost(prepareProvisionNetworkPath, cmd, new JsonAsyncRESTCallback<PrepareProvisionNetworkInGatewayRsp>(reply) {
                    @Override
                    public void fail(ErrorCode err) {
                        trigger.fail(err);
                    }

                    @Override
                    public void success(PrepareProvisionNetworkInGatewayRsp ret) {
                        if (!ret.isSuccess()) {
                            trigger.fail(operr("failed to prepare provision network in gateway[uuid:%s], because %s", self.getUuid(), ret.getError()));
                        } else {
                            trigger.next();
                        }
                    }

                    @Override
                    public Class<PrepareProvisionNetworkInGatewayRsp> getReturnClass() {
                        return PrepareProvisionNetworkInGatewayRsp.class;
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                logger.info(String.format("successfully prepared provision network in gateway[uuid:%s]", self.getUuid()));
                bus.reply(msg, reply);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(msg, reply);
            }
        }).Finally(new FlowFinallyHandler(completion) {
            @Override
            public void Finally() {
                completion.done();
            }
        }).start();
    }

    private void handle(DestroyProvisionNetworkInGatewayMsg msg) {
        inQueue().name(String.format("destroy-provision-network-in-gateway-%s", self.getUuid()))
                .asyncBackup(msg)
                .run(chain -> destroyProvisionNetwork(msg, new NoErrorCompletion(chain) {
                    @Override
                    public void done() {
                        SQL.New(BareMetal2GatewayProvisionNicVO.class)
                                .eq(BareMetal2GatewayProvisionNicVO_.uuid, msg.getGatewayUuid())
                                .hardDelete();
                        chain.next();
                    }
                }));
    }

    private void destroyProvisionNetwork(DestroyProvisionNetworkInGatewayMsg msg, NoErrorCompletion completion) {
        DestroyProvisionNetworkInGatewayReply reply = new DestroyProvisionNetworkInGatewayReply();

        BareMetal2ProvisionNetworkVO network = dbf.findByUuid(msg.getNetworkUuid(), BareMetal2ProvisionNetworkVO.class);
        if (network == null) {
            completion.done();
            return;
        }

        DestroyProvisionNetworkInGatewayCmd cmd = new DestroyProvisionNetworkInGatewayCmd();
        cmd.setProvisionNetwork(buildProvisionNetwork(BareMetal2ProvisionNetworkInventory.valueOf(network)));
        cmd.setGatewayUuid(msg.getGatewayUuid());
        restf.asyncJsonPost(destroyProvisionNetworkPath, cmd, new JsonAsyncRESTCallback<DestroyProvisionNetworkInGatewayRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void success(DestroyProvisionNetworkInGatewayRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to destroy provision network in gateway[uuid:%s], because %s", self.getUuid(), ret.getError()));
                } else {
                    logger.info(String.format("successfully destroyed provision network in gateway[uuid:%s]", self.getUuid()));
                }
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public Class<DestroyProvisionNetworkInGatewayRsp> getReturnClass() {
                return DestroyProvisionNetworkInGatewayRsp.class;
            }
        });
    }

    private void handle(CreateProvisionConfigurationInGatewayMsg msg) {
        CreateProvisionConfigurationInGatewayReply reply = new CreateProvisionConfigurationInGatewayReply();

        CreateProvisionConfigurationForInstanceCmd cmd = new CreateProvisionConfigurationForInstanceCmd();
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid(), msg.getStorageGatewayIpAddress()));
        cmd.setVolumes(buildVolumes(msg.getInstanceUuid()));
        cmd.setGatewayUuid(msg.getGatewayUuid());

        BareMetal2InstanceVO instanceVO = dbf.findByUuid(cmd.getBmInstance().getUuid(), BareMetal2InstanceVO.class);
        if (!BareMetal2Utils.isNonReboot(instanceVO.getChassisUuid(), instanceVO.getProvisionType().toString())) {
            if (cmd.getBmInstance().getProvisionMac() == null) {
                reply.setError(operr("no provision nic found for baremetal2 instance[uuid:%s]", msg.getInstanceUuid()));
                return;
            }
        }

        restf.asyncJsonPost(createProvisionCfgsForInstancePath, cmd, new JsonAsyncRESTCallback<CreateProvisionConfigurationForInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
            }

            @Override
            public void success(CreateProvisionConfigurationForInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to create provision configurations for baremetal2 instance[uuid:%s] " +
                            "in gateway[uuid:%s], because %s", msg.getInstanceUuid(), self.getUuid(), ret.getError()));
                } else {
                    logger.info(String.format("successfully created provision configurations for baremetal2 instance[uuid:%s] " +
                            "in gateway[uuid:%s]", msg.getInstanceUuid(), self.getUuid()));
                    if (ret.getBmInstance() != null && StringUtils.isNotEmpty(ret.getBmInstance().getCustomIqn())) {
                        SystemTagCreator creator = BareMetal2SystemTags.CUSTOM_IQN
                                .newSystemTagCreator(msg.getInstanceUuid());
                        creator.setTagByTokens(map(e(BareMetal2SystemTags.CUSTOM_IQN_TOKEN, ret.getBmInstance().getCustomIqn())));
                        creator.inherent = false;
                        creator.recreate = true;
                        creator.create();
                    }
                }
                bus.reply(msg, reply);
            }

            @Override
            public Class<CreateProvisionConfigurationForInstanceRsp> getReturnClass() {
                return CreateProvisionConfigurationForInstanceRsp.class;
            }
        });
    }

    private void handle(DeleteProvisionConfigurationInGatewayMsg msg) {
        DeleteProvisionConfigurationInGatewayReply reply = new DeleteProvisionConfigurationInGatewayReply();

        DeleteProvisionConfigurationForInstanceCmd cmd = new DeleteProvisionConfigurationForInstanceCmd();
        cmd.setVolumes(buildVolumes(msg.getInstanceUuid()));
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid(), msg.getStorageGatewayIpAddress()));
        cmd.setGatewayUuid(msg.getGatewayUuid());

        if (cmd.getBmInstance().getProvisionMac() == null) {
            logger.debug(String.format("no provision nic found for baremetal2 instance[uuid:%s], " +
                    "no need to delete configurations", msg.getInstanceUuid()));
            bus.reply(msg, reply);
            return;
        }

        restf.asyncJsonPost(deleteProvisionCfgsForInstancePath, cmd, new JsonAsyncRESTCallback<DeleteProvisionConfigurationForInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
            }

            @Override
            public void success(DeleteProvisionConfigurationForInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to delete provision configurations for baremetal2 instance[uuid:%s] " +
                            "in gateway[uuid:%s], because %s", msg.getInstanceUuid(), self.getUuid(), ret.getError()));
                } else {
                    logger.info(String.format("successfully deleted provision configurations for baremetal2 instance[uuid:%s] " +
                            "in gateway[uuid:%s]", msg.getInstanceUuid(), self.getUuid()));
                }
                bus.reply(msg, reply);
            }

            @Override
            public Class<DeleteProvisionConfigurationForInstanceRsp> getReturnClass() {
                return DeleteProvisionConfigurationForInstanceRsp.class;
            }
        });
    }

    private void handle(CreateConsoleProxyInGatewayMsg msg) {
        inQueue().name(String.format("create-console-proxy-for-instance-in-gateway-%s", self.getUuid()))
                .asyncBackup(msg)
                .run(chain -> createConsoleProxy(msg, new NoErrorCompletion(chain) {
                    @Override
                    public void done() {
                        chain.next();
                    }
                }));
    }

    private void createConsoleProxy(CreateConsoleProxyInGatewayMsg msg, NoErrorCompletion completion) {
        CreateConsoleProxyInGatewayReply reply = new CreateConsoleProxyInGatewayReply();

        CreateConsoleProxyForInstanceCmd cmd = new CreateConsoleProxyForInstanceCmd();
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid()));
        cmd.setGatewayUuid(msg.getGatewayUuid());
        restf.asyncJsonPost(createConsoleProxyForInstancePath, cmd, new JsonAsyncRESTCallback<CreateConsoleProxyForInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void success(CreateConsoleProxyForInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to create console proxy for baremetal2 instance[uuid:%s] " +
                            "in gateway[uuid:%s], because %s", msg.getInstanceUuid(), self.getUuid(), ret.getError()));
                } else {
                    reply.setScheme(ret.getScheme() == null ? ConsoleConstants.HTTP_SCHEMA : ret.getScheme());
                    reply.setPort(ret.getPort());
                    logger.info(String.format("successfully created console proxy [protocol:%s, port:%d] for " +
                                    "baremetal2 instance[uuid:%s] in gateway[uuid:%s]",
                            reply.getScheme(), reply.getPort(), msg.getInstanceUuid(), self.getUuid()));
                }
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public Class<CreateConsoleProxyForInstanceRsp> getReturnClass() {
                return CreateConsoleProxyForInstanceRsp.class;
            }
        });
    }

    private void handle(ChangeBareMetal2InstanceDefaultNetworkMsg msg) {
        checkStatus();

        final ChangeBareMetal2InstanceDefaultNetworkReply reply = new ChangeBareMetal2InstanceDefaultNetworkReply();

        ChangeInstanceDefaultRouteCmd cmd = new ChangeInstanceDefaultRouteCmd();
        cmd.setGatewayUuid(msg.getGatewayUuid());
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid()));
        if (msg.getOldDefaultL3Uuid() != null) {
            String nicUuid = Q.New(VmNicVO.class)
                    .eq(VmNicVO_.vmInstanceUuid, msg.getInstanceUuid())
                    .eq(VmNicVO_.l3NetworkUuid, msg.getOldDefaultL3Uuid())
                    .select(VmNicVO_.uuid)
                    .findValue();
            cmd.setOldDefault(buildNic(nicUuid));
        }

        if (msg.getNewDefaultL3Uuid() != null) {
            String nicUuid = Q.New(VmNicVO.class)
                    .eq(VmNicVO_.vmInstanceUuid, msg.getInstanceUuid())
                    .eq(VmNicVO_.l3NetworkUuid, msg.getNewDefaultL3Uuid())
                    .select(VmNicVO_.uuid)
                    .findValue();
            cmd.setNewDefault(buildNic(nicUuid));
        }

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.CHANGE_BM_INSTANCE_DEFAULT_ROUTE_PATH, msg.getInstanceUuid());
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<ChangeInstanceDefaultRouteRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
            }

            @Override
            public void success(ChangeInstanceDefaultRouteRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to change default network from l3[uuid:%s] to l3[uuid:%s] " +
                                    "for baremetal2 instance[uuid:%s], because %s",
                            msg.getOldDefaultL3Uuid(), msg.getNewDefaultL3Uuid(), msg.getInstanceUuid(), ret.getError()));
                } else {
                    logger.info(String.format("changed default network from l3[uuid:%s] to l3[uuid:%s] " +
                                    "for baremetal2 instance[uuid:%s]",
                            msg.getOldDefaultL3Uuid(), msg.getNewDefaultL3Uuid(), msg.getInstanceUuid()));
                }
                bus.reply(msg, reply);
            }

            @Override
            public Class<ChangeInstanceDefaultRouteRsp> getReturnClass() {
                return ChangeInstanceDefaultRouteRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    private void handle(PingBareMetal2InstanceThroughGatewayMsg msg) {
        checkStatus();

        final PingBareMetal2InstanceThroughGatewayReply reply = new PingBareMetal2InstanceThroughGatewayReply();

        PingBareMetal2InstanceCmd cmd = new PingBareMetal2InstanceCmd();
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid()));
        cmd.setGatewayUuid(msg.getGatewayUuid());
        cmd.setIqnTargetIpMap(msg.getIqnTargetIpMap());

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.PING_BAREMETAL2_INSTANCE_PATH, msg.getInstanceUuid());
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<PingBareMetal2InstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                if (err.getCode().equals(SysErrors.HTTP_ERROR.toString())) {
                    reply.setError(err(BareMetal2Errors.NGINX_CONNECTION_ERROR, "failed to ping baremetal2 instance[uuid:%s] " +
                    "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), msg.getGatewayUuid(), err.getDetails()));
                } else {
                    reply.setError(operr("failed to ping baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), msg.getGatewayUuid(), err));
                }
                bus.reply(msg, reply);
            }

            @Override
            public void success(PingBareMetal2InstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to ping baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), msg.getGatewayUuid(), ret.getError()));
                } else {
                    logger.debug(String.format("successfully ping baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s]", msg.getInstanceUuid(), msg.getGatewayUuid()));
                    if (ret.getVersion() != null) {
                        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getInstanceUuid(), BareMetal2InstanceVO.class);
                        if (!ret.getVersion().equals(bm.getAgentVersion())) {
                            bm.setAgentVersion(ret.getVersion());
                        }

                        bm.setLatestAgent(BareMetal2AgentVersionComparer.compare(bm.getPlatform(), bm.getAgentVersion()) == 0);
                        dbf.update(bm);
                    }
                }
                bus.reply(msg, reply);
            }

            @Override
            public Class<PingBareMetal2InstanceRsp> getReturnClass() {
                return PingBareMetal2InstanceRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    private void handle(ChangeInstancePasswordThroughGatewayMsg msg) {
        checkStatus();

        final ChangeInstancePasswordThroughGatewayReply reply = new ChangeInstancePasswordThroughGatewayReply();

        ChangeInstanceSystemPasswordCmd cmd = new ChangeInstanceSystemPasswordCmd();
        cmd.setUsername(msg.getUsername());
        cmd.setPassword(msg.getPassword());
        cmd.setBmInstance(buildBmInstance(msg.getInstanceUuid()));
        cmd.setGatewayUuid(msg.getGatewayUuid());

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.CHANGE_BM_INSTANCE_SYSTEM_PASSWORD_PATH, msg.getInstanceUuid());
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<ChangeInstanceSystemPasswordRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
            }

            @Override
            public void success(ChangeInstanceSystemPasswordRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to change the password of baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", msg.getInstanceUuid(), msg.getGatewayUuid(), ret.getError()));
                } else {
                    logger.debug(String.format("successfully changed the password of baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s]", msg.getInstanceUuid(), msg.getGatewayUuid()));
                }
                bus.reply(msg, reply);
            }

            @Override
            public Class<ChangeInstanceSystemPasswordRsp> getReturnClass() {
                return ChangeInstanceSystemPasswordRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    private String getChassisVMBootDev(BareMetal2IpmiChassisInventory chassis) {
        if (chassis.getProvisionType().equals(BareMetal2ProvisionType.Remote.toString())) {
            return BareMetal2ChassisConstant.CHASSIS_BOOT_DEV_IPXE;
        }

        return BareMetal2ChassisConstant.CHASSIS_BOOT_DEV_DISK;
    }

    @Override
    protected void startVm(VmInstanceSpec spec, NeedReplyMessage msg, NoErrorCompletion completion) {
        checkStatus();

        if (!(spec instanceof BareMetal2InstanceSpec)) {
            completion.done();
            return;
        }

        BareMetal2InstanceSpec bmSpec = (BareMetal2InstanceSpec) spec;
        String clusterUuid = bmSpec.getDestChassis().getClusterUuid();
        String gatewayUuid = bmSpec.getDestHost().getUuid();
        String instanceUuid = bmSpec.getVmInventory().getUuid();

        String networkUuid = Q.New(BareMetal2ProvisionNetworkClusterRefVO.class)
                .eq(BareMetal2ProvisionNetworkClusterRefVO_.clusterUuid, clusterUuid)
                .select(BareMetal2ProvisionNetworkClusterRefVO_.networkUuid)
                .findValue();
        DebugUtils.Assert(networkUuid != null, "no provision network attached in baremetal2 cluster " + clusterUuid);

        String mac = Q.New(BareMetal2ChassisNicVO.class)
                .eq(BareMetal2ChassisNicVO_.chassisUuid, bmSpec.getDestChassis().getUuid())
                .eq(BareMetal2ChassisNicVO_.isProvisionNic, true)
                .select(BareMetal2ChassisNicVO_.mac)
                .findValue();

        if (!BareMetal2Utils.isNonReboot(bmSpec.getDestChassis().getUuid(), bmSpec.getProvisionType())) {
            DebugUtils.Assert(mac != null, String.format("cannot find the mac address of provision nic of baremetal2 instance[uuid:%s]", instanceUuid));
        }
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("start-baremetal2-instance-%s", bmSpec.getVmInventory().getUuid()));
        Map data = new HashMap();
        data.put(BareMetal2InstanceConstant.Params.BareMetal2InstanceInventory, bmSpec.getVmInventory());
        data.put(BareMetal2InstanceConstant.Params.ProvisionNetworkUuid, networkUuid);
        chain.setData(data);
        chain.then(new Flow() {
            final String __name__ = "allocate-provision-nic-ip-baremetal2-instance";

            @Override
            public boolean skip(Map data) {
                return Q.New(BareMetal2InstanceProvisionNicVO.class)
                        .eq(BareMetal2InstanceProvisionNicVO_.uuid, instanceUuid)
                        .eq(BareMetal2InstanceProvisionNicVO_.networkUuid, networkUuid)
                        .isExists();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                BareMetal2ProvisionNetworkAllocateIpAddressMsg amsg = new BareMetal2ProvisionNetworkAllocateIpAddressMsg();
                amsg.setNetworkUuid(networkUuid);
                amsg.setResourceType(BareMetal2InstanceVO.class.getSimpleName());
                amsg.setResourceUuid(instanceUuid);
                amsg.setMacAddress(mac);
                String ip = BareMetal2SystemTags.STATIC_PROVISION_IP.getTokenByResourceUuid(bmSpec.getDestChassis().getUuid(),
                        BareMetal2SystemTags.STATIC_PROVISION_IP_TOKEN);
                amsg.setRequiredIp(ip);
                bus.makeTargetServiceIdByResourceUuid(amsg, BareMetal2ProvisionNetworkConstant.SERVICE_ID, networkUuid);
                bus.send(amsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                trigger.rollback();
            }
        }).then(new NoRollbackFlow() {
                final String __name__ = "active-iscsi-root-volume";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    ExternalPrimaryStorageVO vo = Q.New(ExternalPrimaryStorageVO.class)
                            .eq(ExternalPrimaryStorageVO_.uuid, spec.getDestRootVolume().getPrimaryStorageUuid())
                            .find();
                    PrimaryStorageControllerSvc controller = extPsFactory.getControllerSvc(vo.getUuid());
                    String clientIqn = BlockConstant.getInstanceIscsiIqn(instanceUuid);
                    controller.connect(vo.getConfig(), vo.getUrl(), new ReturnValueCompletion<LinkedHashMap>(trigger) {
                        @Override
                        public void success(LinkedHashMap returnValue) {
                            BlockExternalPrimaryStorageFactory storageFactory = extPsFactory.blockExternalPrimaryStorageFactories.get(vo.getIdentity());
                            storageFactory.activeIscsiVolume(clientIqn, BaseVolumeInfo.valueOf(spec.getDestRootVolume()),
                                    spec.getDestRootVolume().isShareable());
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.setError(errorCode);
                        }
                    });

                }

                @Override
                public boolean skip(Map data) {
                    String type = Q.New(PrimaryStorageVO.class).select(PrimaryStorageVO_.type)
                            .eq(PrimaryStorageVO_.uuid, spec.getDestRootVolume().getPrimaryStorageUuid())
                            .findValue();
                    return !PrimaryStorageConstant.EXTERNAL_PRIMARY_STORAGE_TYPE.equals(type);
                }
        }).then(new NoRollbackFlow() {
            final String __name__ = "convert-root-volume-to-local-disk";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!((BareMetal2InstanceSpec) spec).getProvisionType().equals(BareMetal2ProvisionType.Local.toString())) {
                    trigger.next();
                    return;
                }

                if (BareMetal2SystemTags.CHASSIS_SYSTEM_DISK.hasTag(spec.getVmInventory().getUuid())) {
                    trigger.next();
                    return;
                }

                ConvertVolumeToChassisLocalDiskMsg cmsg = new ConvertVolumeToChassisLocalDiskMsg();
                cmsg.setGatewayUuid(gatewayUuid);
                cmsg.setInstanceUuid(instanceUuid);
                cmsg.setVolumeUuid(bmSpec.getDestRootVolume().getUuid());
                cmsg.setChassisUuid(bmSpec.getDestChassis().getUuid());
                cmsg.setChassisDiskUuid(bmSpec.getRequiredChassisDiskUuid());
                bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, gatewayUuid);
                bus.send(cmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }
        }).then(new BareMetal2InstanceCreateProvisionConfigurationsFlow()
        ).then(new NoRollbackFlow() {
            final String __name__ = "power-on-baremetal2-chassis-of-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!bmSpec.getDestChassis().getType().equals(BareMetal2ChassisConstant.IPMI_CHASSIS_TYPE)) {
                    trigger.next();
                    return;
                }

                if (BareMetal2Utils.isNonReboot(bmSpec.getDestChassis().getUuid(), bmSpec.getProvisionType())) {
                    trigger.next();
                    return;
                }

                BareMetal2IpmiChassisInventory chassis = BareMetal2IpmiChassisInventory.valueOf(
                        dbf.findByUuid(bmSpec.getDestChassis().getUuid(), BareMetal2IpmiChassisVO.class));

                if (chassisHelper.powerOnBareMetal2Chassis(chassis, getChassisVMBootDev(chassis))) {
                    trigger.next();
                } else {
                    trigger.fail(operr("failed to power on baremetal2 chassis[uuid:%s] using ipmitool", chassis.getUuid()));
                }
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                extEmitter.startVmOnKvmSuccess(KVMHostInventory.valueOf(self), spec);
                StartVmOnHypervisorReply reply = new StartVmOnHypervisorReply();
                bus.reply(msg, reply);
                completion.done();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                extEmitter.startVmOnKvmFailed(KVMHostInventory.valueOf(self), spec, errCode);
                StartVmOnHypervisorReply reply = new StartVmOnHypervisorReply();
                reply.setError(errCode);
                bus.reply(msg, reply);
                completion.done();
            }
        }).start();
    }

    private void stopBareMetal2InstanceUsingIpmitool(BareMetal2IpmiChassisInventory chassis, FlowTrigger trigger) {
        boolean success = chassisHelper.powerOffBareMetal2Chassis(chassis);
        if (success) {
            logger.debug(String.format("successfully power off baremetal2 chassis[uuid:%s] by bm agent", chassis.getUuid()));
            trigger.next();
        } else {
            trigger.fail(operr("failed to power off baremetal2 chassis[uuid:%s] using ipmitool", chassis.getUuid()));
        }
    }

    @Override
    protected void stopVm(StopVmOnHypervisorMsg msg, NoErrorCompletion completion) {
        checkStatus();

        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getVmInventory().getUuid(), BareMetal2InstanceVO.class);
        BareMetal2IpmiChassisVO chassis = dbf.findByUuid(bm.getChassisUuid(), BareMetal2IpmiChassisVO.class);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("stop-baremetal2-instance-%s", bm.getUuid()));
        chain.then(new NoRollbackFlow() {
            final String __name__ = "power-off-baremetal2-chassis-of-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (chassis == null) {
                    trigger.next();
                    return;
                }

                StopBareMetal2InstanceType stopType = StopBareMetal2InstanceType.stopByIpmi;

                if (StopVmType.grace.toString().equals(msg.getType())) {
                    stopType = StopBareMetal2InstanceType.stopByAgent;
                }

                if (msg.getType() == null && bm.getStatus() == BareMetal2InstanceStatus.Connected) {
                    stopType = StopBareMetal2InstanceType.stopByAgentThenIpmi;
                }

                if (stopType == StopBareMetal2InstanceType.stopByIpmi) {
                    stopBareMetal2InstanceUsingIpmitool(BareMetal2IpmiChassisInventory.valueOf(chassis), trigger);
                    return;
                }

                StopBareMetal2InstanceCmd cmd = new StopBareMetal2InstanceCmd();
                cmd.setBmInstance(buildBmInstance(bm.getUuid()));
                cmd.setGatewayUuid(bm.getGatewayUuid());

                StopBareMetal2InstanceType finalStopType = stopType;
                String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.STOP_BAREMETAL2_INSTANCE_PATH, bm.getUuid());
                restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<StopBareMetal2InstanceRsp>(trigger) {
                    @Override
                    public void fail(ErrorCode err) {
                        if (finalStopType == StopBareMetal2InstanceType.stopByAgent) {
                            trigger.fail(err);
                        } else {
                            stopBareMetal2InstanceUsingIpmitool(BareMetal2IpmiChassisInventory.valueOf(chassis), trigger);
                        }
                    }

                    @Override
                    public void success(StopBareMetal2InstanceRsp ret) {
                        if (!ret.isSuccess()) {
                            if (finalStopType == StopBareMetal2InstanceType.stopByAgent) {
                                trigger.fail(operr("failed to power off baremetal2 instance[uuid:%s] " +
                                        "by bm agent, because %s", bm.getUuid(), ret.getError()));
                            } else {
                                stopBareMetal2InstanceUsingIpmitool(BareMetal2IpmiChassisInventory.valueOf(chassis), trigger);
                            }
                        } else {
                            trigger.next();
                        }
                    }

                    @Override
                    public Class<StopBareMetal2InstanceRsp> getReturnClass() {
                        return StopBareMetal2InstanceRsp.class;
                    }
                }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "check-and-make-sure-chassis-is-actually-power-off";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                int timeout = BareMetal2GlobalConfig.CHECK_CHASSIS_POWER_STATUS_TIMEOUT.value(Integer.class);
                boolean offed = chassisHelper.checkBareMetal2ChassisPowerStatus(BareMetal2IpmiChassisInventory.valueOf(chassis),
                        BareMetal2ChassisConstant.CHASSIS_POWER_OFF, timeout);
                if (offed) {
                    trigger.next();
                } else {
                    trigger.fail(operr("baremetal2 chassis[uuid:%s] is still not POWER_OFF %d seconds later", chassis.getUuid(), timeout));
                }
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "set-bm-agent-state-to-disconnected";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                BareMetal2InstanceVO bmVO = dbf.reload(bm);
                if (bmVO.getStatus() != BareMetal2InstanceStatus.Disconnected) {
                    bmVO.setStatus(BareMetal2InstanceStatus.Disconnected);
                }
                dbf.updateAndRefresh(bmVO);
                trigger.next();
            }
        }).then(getDestroyProvisionConfigurationsInGatewayFlow(bm));

        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                extEmitter.stopVmOnKvmSuccess(KVMHostInventory.valueOf(self), msg.getVmInventory());
                StopVmOnHypervisorReply reply = new StopVmOnHypervisorReply();
                bus.reply(msg, reply);
                completion.done();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                extEmitter.stopVmOnKvmFailed(KVMHostInventory.valueOf(self), msg.getVmInventory(), errCode);
                StopVmOnHypervisorReply reply = new StopVmOnHypervisorReply();
                reply.setError(errCode);
                bus.reply(msg, reply);
                completion.done();
            }
        }).start();
    }

    @Override
    protected void destroyVm(DestroyVmOnHypervisorMsg msg, NoErrorCompletion completion) {
        checkStatus();

        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getVmInventory().getUuid(), BareMetal2InstanceVO.class);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("destroy-baremetal2-instance-%s", bm.getUuid()));
        chain.then(new NoRollbackFlow() {
            final String __name__ = "power-off-baremetal2-chassis-of-instance";

            @Override
            public boolean skip(Map data) {
                return BareMetal2Utils.isNonReboot(bm.getChassisUuid(), bm.getProvisionType().toString());
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                BareMetal2IpmiChassisVO chassis = dbf.findByUuid(bm.getChassisUuid(), BareMetal2IpmiChassisVO.class);
                if (chassis == null) {
                    trigger.next();
                    return;
                }

                chassisHelper.powerOffBareMetal2Chassis(BareMetal2IpmiChassisInventory.valueOf(chassis));
                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "set-bm-agent-state-to-disconnected";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                BareMetal2InstanceVO bmVO = dbf.reload(bm);
                if (bmVO.getStatus() != BareMetal2InstanceStatus.Disconnected) {
                    bmVO.setStatus(BareMetal2InstanceStatus.Disconnected);
                    dbf.updateAndRefresh(bmVO);
                }
                trigger.next();
            }
        }).then(getDestroyProvisionConfigurationsInGatewayFlow(bm));

        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                extEmitter.destroyVmOnKvmSuccess(KVMHostInventory.valueOf(self), msg.getVmInventory());
                DestroyVmOnHypervisorReply reply = new DestroyVmOnHypervisorReply();
                bus.reply(msg, reply);
                completion.done();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                extEmitter.destroyVmOnKvmFailed(KVMHostInventory.valueOf(self), msg.getVmInventory(), errCode);
                DestroyVmOnHypervisorReply reply = new DestroyVmOnHypervisorReply();
                reply.setError(errCode);
                bus.reply(msg, reply);
                completion.done();
            }
        }).start();
    }

    private Flow getDestroyProvisionConfigurationsInGatewayFlow(BareMetal2InstanceVO bm) {
        return new NoRollbackFlow() {
            final String __name__ = "delete-provision-configurations-for-baremetal2-instance-in-all-gateways-on-provision-network";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String networkUuid = Q.New(BareMetal2ProvisionNetworkClusterRefVO.class)
                        .eq(BareMetal2ProvisionNetworkClusterRefVO_.clusterUuid, bm.getClusterUuid())
                        .select(BareMetal2ProvisionNetworkClusterRefVO_.networkUuid)
                        .findValue();
                if (networkUuid == null) {
                    trigger.next();
                    return;
                }

                List<String> gatewayUuids = BareMetal2Utils.getGatewayUuidsOnProvisionNetwork(networkUuid);
                if (gatewayUuids == null) {
                    trigger.fail(operr("make sure all baremetal2 gateways on provision network[uuid:%s] are Connected", networkUuid));
                    return;
                }

                String storageGatewayIpAddress = Q.New(BareMetal2GatewayProvisionNicVO.class)
                        .eq(BareMetal2GatewayProvisionNicVO_.uuid, bm.getGatewayUuid())
                        .select(BareMetal2GatewayProvisionNicVO_.ip)
                        .findValue();

                new While<>(gatewayUuids).each((uuid, cmpl) -> {
                    DeleteProvisionConfigurationInGatewayMsg dmsg = new DeleteProvisionConfigurationInGatewayMsg();
                    dmsg.setGatewayUuid(uuid);
                    dmsg.setInstanceUuid(bm.getUuid());
                    dmsg.setStorageGatewayIpAddress(storageGatewayIpAddress);
                    bus.makeTargetServiceIdByResourceUuid(dmsg, HostConstant.SERVICE_ID, dmsg.getGatewayUuid());
                    bus.send(dmsg, new CloudBusCallBack(cmpl) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                cmpl.done();
                            } else {
                                cmpl.addError(reply.getError());
                                cmpl.allDone();
                            }
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList.getCauses().get(0));
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        };
    }

    @Override
    protected void handle(CheckVmStateOnHypervisorMsg msg) {
        final CheckVmStateOnHypervisorReply reply = new CheckVmStateOnHypervisorReply();

        if (CollectionUtils.isEmpty(msg.getVmInstanceUuids())) {
            reply.setError(operr("vmInstanceUuids is empty"));
            bus.reply(msg, reply);
            return;
        }

        if (self.getStatus() != HostStatus.Connected) {
            reply.setError(operr("the baremetal2 gateway[uuid:%s, status:%s] is not Connected", self.getUuid(), self.getStatus()));
            bus.reply(msg, reply);
            return;
        }

        // check baremetal2 chassis state instead
        List<BareMetal2InstanceVO> instances = Q.New(BareMetal2InstanceVO.class)
                .in(BareMetal2InstanceVO_.uuid, msg.getVmInstanceUuids())
                .list();

        Map<String, String> states = new HashMap<>();
        for (BareMetal2InstanceVO instance : instances) {
            if (instance.getChassisUuid() == null) {
                states.put(instance.getUuid(), VmInstanceState.Stopped.toString());
                continue;
            }

            String chassisUuid = instance.getChassisUuid();
            BareMetal2IpmiChassisVO chassis = dbf.findByUuid(chassisUuid, BareMetal2IpmiChassisVO.class);
            if (chassis == null) {
                continue;
            }

            String powerStatus = chassisHelper.getBareMetal2ChassisPowerStatus(BareMetal2IpmiChassisInventory.valueOf(chassis));
            if (powerStatus.equals(BareMetal2ChassisConstant.CHASSIS_POWER_ON)) {
                states.put(instance.getUuid(), VmInstanceState.Running.toString());
            } else if (powerStatus.equals(BareMetal2ChassisConstant.CHASSIS_POWER_OFF)) {
                states.put(instance.getUuid(), VmInstanceState.Stopped.toString());
            } else {
                states.put(instance.getUuid(), VmInstanceState.Unknown.toString());
            }
        }

        reply.setStates(states);
        bus.reply(msg, reply);
    }

    private void completeNetworkService(VmAttachNicOnHypervisorMsg msg) {
        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getNicInventory().getVmInstanceUuid(), BareMetal2InstanceVO.class);
        List<BareMetal2BondingVO> bonds = Q.New(BareMetal2BondingVO.class)
                .eq(BareMetal2BondingVO_.chassisUuid, bm.getChassisUuid())
                .list();
        if (bonds.isEmpty()) {
            return;
        }

        VmNicVO nicVO = dbf.findByUuid(msg.getNicInventory().getUuid(), VmNicVO.class);
        Optional<BareMetal2BondingVO> opt = bonds.stream().filter(v -> {
            List<String> slaves = asList(v.getSlaves().split(","));
            return slaves.contains(nicVO.getMac());
        })
                .findFirst();
        if (!opt.isPresent()) {
            return;
        }

        BareMetal2BondingNicRefVO ref = new BareMetal2BondingNicRefVO();
        ref.setBondingUuid(opt.get().getUuid());
        ref.setNicUuid(nicVO.getUuid());
        ref.setInstanceUuid(bm.getUuid());
        dbf.persist(ref);
    }

    @Override
    protected void attachNic(VmAttachNicOnHypervisorMsg msg, NoErrorCompletion completion) {
        checkStatus();

        completeNetworkService(msg);

        final VmAttachNicOnHypervisorReply reply = new VmAttachNicOnHypervisorReply();

        String bmUuid = msg.getNicInventory().getVmInstanceUuid();
        String nicUuid = msg.getNicInventory().getUuid();
        String gatewayUuid = msg.getHostUuid();

        BareMetal2InstanceVO bm = dbf.findByUuid(bmUuid, BareMetal2InstanceVO.class);
        if (bm.getStatus() != BareMetal2InstanceStatus.Connected) {
            reply.setError(operr("baremetal2 instance[uuid:%s] not connected, cannot attach nic to it", bmUuid));
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        AttachNicToInstanceCmd cmd = new AttachNicToInstanceCmd();
        cmd.setBmInstance(buildBmInstance(bmUuid));
        cmd.setNic(buildNic(nicUuid));
        cmd.setGatewayUuid(gatewayUuid);

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.ATTACH_NIC_TO_BM_INSTANCE_PATH, bmUuid);
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<AttachNicToInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void success(AttachNicToInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to attach nic[uuid:%s] to baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", nicUuid, bmUuid, gatewayUuid, ret.getError()));
                } else {
                    logger.info(String.format("successfully attached nic[uuid:%s] to baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s]", nicUuid, bmUuid, gatewayUuid));
                }
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public Class<AttachNicToInstanceRsp> getReturnClass() {
                return AttachNicToInstanceRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    private void cleanUpNetworkService(String nicUuid) {
        String bondUuid = Q.New(BareMetal2BondingNicRefVO.class)
                .select(BareMetal2BondingNicRefVO_.bondingUuid)
                .eq(BareMetal2BondingNicRefVO_.nicUuid, nicUuid)
                .findValue();

        if (bondUuid == null) {
            return;
        }

        dbf.removeByPrimaryKey(bondUuid, BareMetal2BondingVO.class);
    }


    @Override
    protected void detachNic(DetachNicFromVmOnHypervisorMsg msg, NoErrorCompletion completion) {
        checkStatus();

        final DetachNicFromVmOnHypervisorReply reply = new DetachNicFromVmOnHypervisorReply();

        String bmUuid = msg.getVmInstanceUuid();
        String nicUuid = msg.getNic().getUuid();
        String gatewayUuid = msg.getHostUuid();

        BareMetal2InstanceVO bm = dbf.findByUuid(bmUuid, BareMetal2InstanceVO.class);
        if (bm.getStatus() != BareMetal2InstanceStatus.Connected) {
            reply.setError(operr("baremetal2 instance[uuid:%s] is not connected, cannot detach nic from it", bmUuid));
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        DetachNicFromInstanceCmd cmd = new DetachNicFromInstanceCmd();
        cmd.setBmInstance(buildBmInstance(bmUuid));
        cmd.setNic(buildNic(nicUuid));
        cmd.setGatewayUuid(gatewayUuid);

        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.DETACH_NIC_FROM_BM_INSTANCE_PATH, bmUuid);
        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<DetachNicFromInstanceRsp>(reply) {
            @Override
            public void fail(ErrorCode err) {
                reply.setError(err);
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public void success(DetachNicFromInstanceRsp ret) {
                if (!ret.isSuccess()) {
                    reply.setError(operr("failed to detach nic[uuid:%s] from baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s], because %s", nicUuid, bmUuid, gatewayUuid, ret.getError()));
                } else {
                    logger.info(String.format("successfully detached nic[uuid:%s] from baremetal2 instance[uuid:%s] " +
                            "through gateway[uuid:%s]", nicUuid, bmUuid, gatewayUuid));
                }
                cleanUpNetworkService(nicUuid);
                bus.reply(msg, reply);
                completion.done();
            }

            @Override
            public Class<DetachNicFromInstanceRsp> getReturnClass() {
                return DetachNicFromInstanceRsp.class;
            }
        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
    }

    private void rebuildBlockVolumeTo(VolumeTO volumeTO, String volumeUuid) {
        String primaryStorageUuid = Q.New(BlockVolumeVO.class).eq(BlockVolumeVO_.uuid, volumeUuid).select(BlockVolumeVO_.primaryStorageUuid).findValue();
        if (StringUtils.isEmpty(primaryStorageUuid)) {
            return;
        }

        if (isAddonPrimaryStorageVolume(primaryStorageUuid)) {
            return;
        }
        volumeTO.setToken(CephSystemTags.THIRDPARTY_PLATFORM.getTokenByResourceUuid(primaryStorageUuid, CephSystemTags.THIRDPARTY_PLATFORM_TOKEN));
        volumeTO.setMonIp(getCephMonIp(primaryStorageUuid));
        volumeTO.setTpTimeout(CephGlobalConfig.THIRD_PARTY_SDK_TIMEOUT.value(String.class));
    }

    private boolean isAddonPrimaryStorageVolume(String volumePrimaryStorageUuid) {
        return Q.New(PrimaryStorageVO.class)
                .eq(PrimaryStorageVO_.uuid, volumePrimaryStorageUuid)
                .eq(PrimaryStorageVO_.type, PrimaryStorageConstant.EXTERNAL_PRIMARY_STORAGE_TYPE)
                .isExists();
    }

    @Override
    protected void attachVolume(AttachVolumeToVmOnHypervisorMsg msg, NoErrorCompletion completion) {
        checkStatus();

        final String bmUuid = msg.getVmInventory().getUuid();
        final AttachVolumeToVmOnHypervisorReply reply = new AttachVolumeToVmOnHypervisorReply();

        BareMetal2InstanceVO bm = dbf.findByUuid(bmUuid, BareMetal2InstanceVO.class);
        if (bm.getStatus() != BareMetal2InstanceStatus.Connected) {
            reply.setError(operr("baremetal2 instance[uuid:%s] is not connected, cannot attach volume to it", bmUuid));
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName("attach-volume-to-baremetal2-instance");
        chain.then(new ShareFlow() {
            final String volumeUuid = msg.getInventory().getUuid();
            final String gatewayUuid = msg.getHostUuid();
            AccessPathInfo volumeAccessPathInfo = null;
            Integer attachVolumeLunId = null;

            @Override
            public void setup() {
                flow(new Flow() {
                    final String __name__ = "prepare-volume-in-gateway";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        PrepareVolumeForInstanceCmd cmd = new PrepareVolumeForInstanceCmd();
                        cmd.setBmInstance(buildBmInstance(bmUuid));
                        cmd.setVolume(buildVolume(volumeUuid, bmUuid));
                        cmd.setGatewayUuid(gatewayUuid);
                        rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);
                        restf.asyncJsonPost(prepareVolumeForInstancePath, cmd, new JsonAsyncRESTCallback<PrepareVolumeForInstanceRsp>(trigger) {
                            @Override
                            public void fail(ErrorCode err) {
                                trigger.fail(err);
                            }

                            @Override
                            public void success(PrepareVolumeForInstanceRsp ret) {
                                if (ret.isSuccess()) {
                                    logger.debug(String.format("successfully prepared volume[uuid:%s] for baremetal2 instance[uuid:%s] " +
                                            "through gateway[uuid:%s]", volumeUuid, bmUuid, gatewayUuid));
                                    attachVolumeLunId = ret.getLunId();
                                    trigger.next();
                                } else {
                                    trigger.fail(operr("failed to prepare volume[uuid:%s] for baremetal2 instance[uuid:%s] " +
                                            "through gateway[uuid:%s], because %s", volumeUuid, bmUuid, gatewayUuid, ret.getError()));
                                }
                            }

                            @Override
                            public Class<PrepareVolumeForInstanceRsp> getReturnClass() {
                                return PrepareVolumeForInstanceRsp.class;
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        DestroyVolumeForInstanceCmd cmd = new DestroyVolumeForInstanceCmd();
                        cmd.setBmInstance(buildBmInstance(bmUuid));
                        cmd.setVolume(buildVolume(volumeUuid, bmUuid));
                        cmd.setGatewayUuid(gatewayUuid);
                        rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);
                        restf.asyncJsonPost(destroyVolumeForInstancePath, cmd, new JsonAsyncRESTCallback<DestroyVolumeForInstanceRsp>(trigger) {
                            @Override
                            public void fail(ErrorCode err) {
                                trigger.rollback();
                            }

                            @Override
                            public void success(DestroyVolumeForInstanceRsp ret) {
                                trigger.rollback();
                            }

                            @Override
                            public Class<DestroyVolumeForInstanceRsp> getReturnClass() {
                                return DestroyVolumeForInstanceRsp.class;
                            }
                        });
                    }

                    @Override
                    public boolean skip(Map data) {
                        return isAddonPrimaryStorageVolume(msg.getInventory().getPrimaryStorageUuid());
                    }
                });

                flow(new NoRollbackFlow() {
                    final String __name__ = "get-gateway-ips-of-the-access-path-for-block-volume";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        GetAccessPathInfoCmd cmd = new GetAccessPathInfoCmd();
                        cmd.setVolume(buildVolume(volumeUuid, bmUuid));
                        cmd.setGatewayUuid(gatewayUuid);
                        rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);
                        restf.asyncJsonPost(GetAccessPathInfoPath, cmd, new JsonAsyncRESTCallback<GetAccessPathInfoRsp>(trigger) {
                            @Override
                            public void fail(ErrorCode err) {
                                trigger.fail(err);
                            }

                            @Override
                            public void success(GetAccessPathInfoRsp ret) {
                                if (ret.isSuccess()) {
                                    volumeAccessPathInfo = ret.getInfos().get(0);
                                    trigger.next();
                                } else {
                                    trigger.fail(operr("failed to get gateway ips of the access path[iscsiPath: %s] for block volume %s, because %s", cmd.getVolume().getIscsiPath(), volumeUuid, ret.getError()));
                                }
                            }

                            @Override
                            public Class<GetAccessPathInfoRsp> getReturnClass() {
                                return GetAccessPathInfoRsp.class;
                            }
                        });
                    }

                    @Override
                    public boolean skip(Map data) {
                        return !Q.New(BlockVolumeVO.class).eq(BlockVolumeVO_.uuid, volumeUuid).isExists() || isAddonPrimaryStorageVolume(msg.getInventory().getPrimaryStorageUuid());
                    }
                });

                flow(new NoRollbackFlow() {
                    final String __name__ = "attach-volume-to-instance";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        AttachVolumeToInstanceCmd cmd = new AttachVolumeToInstanceCmd();
                        cmd.setBmInstance(buildBmInstance(bmUuid));
                        cmd.setVolume(buildVolume(volumeUuid, bmUuid));
                        cmd.setGatewayUuid(gatewayUuid);
                        cmd.setVolumeAccessPathGatewayIps(volumeAccessPathInfo != null ? volumeAccessPathInfo.getGatewayIps() : getGatewayIps(msg.getInventory()));
                        rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);
                        cmd.getVolume().setDeviceId(attachVolumeLunId);

                        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.ATTACH_VOLUME_TO_BM_INSTANCE_PATH, bmUuid);
                        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<AttachVolumeToInstanceRsp>(trigger) {
                            @Override
                            public void fail(ErrorCode err) {
                                trigger.fail(err);
                            }

                            @Override
                            public void success(AttachVolumeToInstanceRsp ret) {
                                if (ret.isSuccess()) {
                                    logger.debug(String.format("successfully attached volume[uuid:%s] to baremetal2 instance[uuid:%s] " +
                                            "through gateway[uuid:%s]", volumeUuid, bmUuid, gatewayUuid));
                                    trigger.next();
                                } else {
                                    trigger.fail(operr("failed to attach volume[uuid:%s] to baremetal2 instance[uuid:%s] " +
                                            "through gateway[uuid:%s], because %s", volumeUuid, bmUuid, gatewayUuid, ret.getError()));
                                }
                            }

                            @Override
                            public Class<AttachVolumeToInstanceRsp> getReturnClass() {
                                return AttachVolumeToInstanceRsp.class;
                            }
                        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        bus.reply(msg, reply);
                        completion.done();
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        reply.setError(errCode);
                        bus.reply(msg, reply);
                        completion.done();
                    }
                });
            }
        }).start();
    }

    protected List<String> getGatewayIps(VolumeInventory volumeInventory) {
        BaseVolumeInfo vol = BaseVolumeInfo.valueOf(volumeInventory);
        PrimaryStorageNodeSvc nodeSvc = extPsFactory.getNodeSvc(vol.getPrimaryStorageUuid());
        if (nodeSvc == null) {
            return new ArrayList<>();
        }
        String path = nodeSvc.getActivePath(vol, HostInventory.valueOf(self), vol.isShareable());
        if (StringUtils.isEmpty(path) || !path.startsWith("iscsi")) {
            return new ArrayList<>();
        }
        String[] serverHostNames;
        try {
            URI uri = new URI(path);
            serverHostNames = uri.getAuthority().split(":")[0].split(",");
            Arrays.sort(serverHostNames);
        } catch (URISyntaxException e) {
            throw new OperationFailureException(operr(e.getMessage()));
        }
        return new ArrayList<>(Arrays.asList(serverHostNames));
    }

    @Override
    protected void detachVolume(DetachVolumeFromVmOnHypervisorMsg msg, NoErrorCompletion completion) {
        checkStatus();

        final String bmUuid = msg.getVmInventory().getUuid();
        final DetachVolumeFromVmOnHypervisorReply reply = new DetachVolumeFromVmOnHypervisorReply();

        BareMetal2InstanceVO bm = dbf.findByUuid(bmUuid, BareMetal2InstanceVO.class);
        if (bm.getStatus() != BareMetal2InstanceStatus.Connected) {
            reply.setError(operr("baremetal2 instance[uuid:%s] is not connected, cannot attach volume to it", bmUuid));
            bus.reply(msg, reply);
            completion.done();
            return;
        }

        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName("detach-volume-from-baremetal2-instance");
        chain.then(new ShareFlow() {
            final String volumeUuid = msg.getInventory().getUuid();
            final String gatewayUuid = msg.getHostUuid();
            final VolumeTO volumeTO = buildVolume(volumeUuid, bmUuid);
            AccessPathInfo volumeAccessPathInfo = null;

            @Override
            public void setup() {

                flow(new NoRollbackFlow() {
                    final String __name__ = "get-volume-lunid";

                    @Override
                    public boolean skip(Map data) {
                        return !Q.New(BlockVolumeVO.class).eq(BlockVolumeVO_.uuid, volumeUuid).isExists();
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (isAddonPrimaryStorageVolume(msg.getInventory().getPrimaryStorageUuid())) {
                            BlockVolumeVO volumeVO = Q.New(BlockVolumeVO.class).eq(BlockVolumeVO_.uuid, msg.getInventory().getUuid()).find();
                            Tuple tuple = Q.New(ExternalPrimaryStorageVO.class)
                                    .select(ExternalPrimaryStorageVO_.config, ExternalPrimaryStorageVO_.url)
                                    .eq(ExternalPrimaryStorageVO_.uuid, msg.getInventory().getPrimaryStorageUuid()).findTuple();
                            PrimaryStorageControllerSvc controller = extPsFactory.getControllerSvc(msg.getInventory().getPrimaryStorageUuid());
                            controller.connect((String) tuple.get(0), (String) tuple.get(1), new ReturnValueCompletion<LinkedHashMap>(completion) {
                                @Override
                                public void success(LinkedHashMap returnValue) {
                                    BlockExternalPrimaryStorageFactory storageFactory = extPsFactory.blockExternalPrimaryStorageFactories.get(volumeVO.getVendor());
                                    String volumeLunId = storageFactory.getVolumeLunId(volumeVO.getInstallPath());
                                    if (StringUtils.isEmpty(volumeLunId)) {
                                        trigger.fail(operr("fail to get volume lun id on external primary storage"));
                                    } else {
                                        volumeTO.setDeviceId(Integer.parseInt(volumeLunId));
                                        trigger.next();
                                    }
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            GetVolumeLunIdCmd cmd = new GetVolumeLunIdCmd();
                            cmd.setVolume(volumeTO);
                            cmd.setGatewayUuid(gatewayUuid);
                            cmd.setBmInstance(buildBmInstance(bmUuid));
                            rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);

                            restf.asyncJsonPost(getVolumeLunIdPath, cmd, new JsonAsyncRESTCallback<GetVolumeLunIdRsp>(trigger) {
                                @Override
                                public void fail(ErrorCode err) {
                                    trigger.fail(err);
                                }

                                @Override
                                public void success(GetVolumeLunIdRsp ret) {
                                    if (!ret.isSuccess()) {
                                        trigger.fail(operr("failed to get volume[uuid:%s] lunid for baremetal2 instance[uuid:%s] " +
                                                "in gateway[uuid:%s], because %s", volumeUuid, bmUuid, gatewayUuid, ret.getError()));
                                        return;
                                    }

                                    if (ret.getLunId() == null) {
                                        logger.info(String.format("cannot get volume[uuid:%s] lunid for baremetal2 instance[uuid:%s] " +
                                                "in gateway[uuid:%s], maybe it has been detached, skip detach volume flow", volumeUuid, bmUuid, gatewayUuid));
                                    } else {
                                        logger.info(String.format("successfully get volume[uuid:%s] lunid[%s] for baremetal2 instance[uuid:%s] " +
                                                "in gateway[uuid:%s]", volumeUuid, ret.getLunId(), bmUuid, gatewayUuid));
                                    }
                                    volumeTO.setDeviceId(ret.getLunId());
                                    trigger.next();
                                }

                                @Override
                                public Class<GetVolumeLunIdRsp> getReturnClass() {
                                    return GetVolumeLunIdRsp.class;
                                }
                            });
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    final String __name__ = "get-gateway-ips-of-the-access-path-for-block-volume";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        GetAccessPathInfoCmd cmd = new GetAccessPathInfoCmd();
                        cmd.setVolume(buildVolume(volumeUuid, bmUuid));
                        cmd.setGatewayUuid(gatewayUuid);
                        rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);
                        restf.asyncJsonPost(GetAccessPathInfoPath, cmd, new JsonAsyncRESTCallback<GetAccessPathInfoRsp>(trigger) {
                            @Override
                            public void fail(ErrorCode err) {
                                trigger.fail(err);
                            }

                            @Override
                            public void success(GetAccessPathInfoRsp ret) {
                                if (ret.isSuccess()) {
                                    volumeAccessPathInfo = ret.getInfos().get(0);
                                    trigger.next();
                                } else {
                                    trigger.fail(operr("failed to get gateway ips of the access path[iscsiPath: %s] for block volume %s, because %s", cmd.getVolume().getIscsiPath(), volumeUuid, ret.getError()));
                                }
                            }

                            @Override
                            public Class<GetAccessPathInfoRsp> getReturnClass() {
                                return GetAccessPathInfoRsp.class;
                            }
                        });
                    }

                    @Override
                    public boolean skip(Map data) {
                        return !Q.New(BlockVolumeVO.class).eq(BlockVolumeVO_.uuid, volumeUuid).isExists() || isAddonPrimaryStorageVolume(msg.getInventory().getPrimaryStorageUuid());
                    }
                });

                flow(new NoRollbackFlow() {
                    final String __name__ = "detach-volume-from-instance";

                    @Override
                    public boolean skip(Map data) {
                        return volumeTO.getDeviceId() == null;
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        DetachVolumeFromInstanceCmd cmd = new DetachVolumeFromInstanceCmd();
                        cmd.setVolume(volumeTO);
                        cmd.setBmInstance(buildBmInstance(bmUuid));
                        cmd.setGatewayUuid(gatewayUuid);
                        cmd.setVolumeAccessPathGatewayIps(volumeAccessPathInfo != null ? volumeAccessPathInfo.getGatewayIps() : getGatewayIps(msg.getInventory()));
                        rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);

                        String path = buildBmInstanceAgentUrl(BareMetal2InstanceConstant.DETACH_VOLUME_FROM_BM_INSTANCE_PATH, bmUuid);
                        restf.asyncJsonPost(path, cmd, new JsonAsyncRESTCallback<DetachVolumeFromInstanceRsp>(trigger) {
                            @Override
                            public void fail(ErrorCode err) {
                                trigger.fail(err);
                            }

                            @Override
                            public void success(DetachVolumeFromInstanceRsp ret) {
                                if (ret.isSuccess()) {
                                    logger.debug(String.format("successfully detached volume[uuid:%s] from baremetal2 instance[uuid:%s] " +
                                            "through gateway[uuid:%s]", volumeUuid, bmUuid, gatewayUuid));
                                    trigger.next();
                                    return;
                                }

                                trigger.fail(operr("failed to detach volume[uuid:%s] from baremetal2 instance[uuid:%s] " +
                                        "through gateway[uuid:%s], because %s", volumeUuid, bmUuid, gatewayUuid, ret.getError()));
                            }

                            @Override
                            public Class<DetachVolumeFromInstanceRsp> getReturnClass() {
                                return DetachVolumeFromInstanceRsp.class;
                            }
                        }, TimeUnit.SECONDS, BareMetal2GlobalProperty.BM_AGENT_TIMEOUT_IN_SECONDS);
                    }
                });

                flow(new NoRollbackFlow() {
                    final String __name__ = "destroy-volume-in-gateway";

                    @Override
                    public boolean skip(Map data) {
                        return volumeTO.getDeviceId() == null;
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (isAddonPrimaryStorageVolume(msg.getInventory().getPrimaryStorageUuid())) {
                            extEmitter.afterDetachVolume(KVMHostInventory.valueOf(self), msg.getVmInventory(),
                                    msg.getInventory(), new KVMAgentCommands.DetachDataVolumeCmd());
                        }
                        DestroyVolumeForInstanceCmd cmd = new DestroyVolumeForInstanceCmd();
                        cmd.setBmInstance(buildBmInstance(bmUuid));
                        cmd.setVolume(volumeTO);
                        cmd.setGatewayUuid(gatewayUuid);
                        rebuildBlockVolumeTo(cmd.getVolume(), volumeUuid);
                        restf.asyncJsonPost(destroyVolumeForInstancePath, cmd, new JsonAsyncRESTCallback<DestroyVolumeForInstanceRsp>(trigger) {
                            @Override
                            public void fail(ErrorCode err) {
                                trigger.fail(err);
                            }

                            @Override
                            public void success(DestroyVolumeForInstanceRsp ret) {
                                if (!ret.isSuccess()) {
                                    trigger.fail(operr("failed to destroy volume[uuid:%s] for baremetal2 instance[uuid:%s] " +
                                            "in gateway[uuid:%s], because %s", volumeUuid, bmUuid, gatewayUuid, ret.getError()));
                                    return;
                                }

                                logger.info(String.format("successfully destroyed volume[uuid:%s] for baremetal2 instance[uuid:%s] " +
                                        "in gateway[uuid:%s]", volumeUuid, bmUuid, gatewayUuid));
                                trigger.next();
                            }

                            @Override
                            public Class<DestroyVolumeForInstanceRsp> getReturnClass() {
                                return DestroyVolumeForInstanceRsp.class;
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        bus.reply(msg, reply);
                        completion.done();
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        reply.setError(errCode);
                        bus.reply(msg, reply);
                        completion.done();
                    }
                });
            }
        }).start();
    }

    @Override
    protected void completeTakeSnapshotCmd(TakeSnapshotOnHypervisorMsg msg, KVMAgentCommands.TakeSnapshotCmd cmd) {
        if (msg.getVmUuid() == null) {
            return;
        }

        BareMetal2InstanceVO bm = dbf.findByUuid(msg.getVmUuid(), BareMetal2InstanceVO.class);
        cmd.setBaremetal2InstanceOnlineSnapshot(bm != null && bm.getState() == VmInstanceState.Running);
    }

    private BareMetal2InstanceTO buildBmInstance(String bmUuid) {
        return buildBmInstance(bmUuid, null);
    }

    @Transactional(readOnly = true)
    private BareMetal2InstanceTO buildBmInstance(String bmUuid, String storageGatewayIpAddress) {
        BareMetal2InstanceTO instance = new BareMetal2InstanceTO();
        BareMetal2InstanceVO bmVO = dbf.findByUuid(bmUuid, BareMetal2InstanceVO.class);
        instance.setUuid(bmUuid);
        if (storageGatewayIpAddress == null) {
            String gatewayIp = SQL.New("select nic.ip from BareMetal2GatewayProvisionNicVO nic, " +
                    "BareMetal2GatewayVO gateway, BareMetal2InstanceVO instance " +
                    "where instance.uuid = :bmUuid and instance.gatewayUuid = gateway.uuid and nic.uuid = gateway.uuid")
                    .param("bmUuid", bmUuid)
                    .find();
            instance.setGatewayIp(gatewayIp);
        } else {
            instance.setGatewayIp(storageGatewayIpAddress);
        }

        BareMetal2InstanceProvisionNicVO instanceNic = dbf.findByUuid(bmUuid, BareMetal2InstanceProvisionNicVO.class);
        if (instanceNic != null) {
            instance.setProvisionIp(instanceNic.getIp());
            instance.setProvisionMac(instanceNic.getMac());
        }

        instance.setNics(buildNics(bmUuid));

        String imageUuid = Q.New(VmInstanceVO.class).select(VmInstanceVO_.imageUuid).eq(VmInstanceVO_.uuid, bmUuid).findValue();
        if (imageUuid != null) {
            String architecture = Q.New(ImageVO.class).select(ImageVO_.architecture).eq(ImageVO_.uuid, imageUuid).findValue();
            instance.setArchitecture(architecture);
            instance.setImageUuid(imageUuid);
        }
        instance.setProvisionType(bmVO.getProvisionType().toString());

        if (BareMetal2SystemTags.CUSTOM_IQN.hasTag(bmUuid)) {
            instance.setCustomIqn(BareMetal2SystemTags.CUSTOM_IQN.getTokenByResourceUuid(bmUuid, BareMetal2SystemTags.CUSTOM_IQN_TOKEN));
        }

        return instance;
    }

    private String getCephMonIp(String psUuid) {
        CephPrimaryStorageVO vo = dbf.findByUuid(psUuid, CephPrimaryStorageVO.class);
        return vo.getMons()
                .stream()
                .filter(v -> v.getStatus() == MonStatus.Connected)
                .map(CephPrimaryStorageMonVO::getHostname)
                .findAny()
                .orElseThrow(() -> new OperationFailureException(
                        operr("all ceph mons of primary storage[uuid:%s] are not in Connected state", vo.getUuid())
                ));
    }

    private VolumeTO buildVolume(String volumeUuid, String bmUuid) {
        VolumeVO vo = dbf.findByUuid(volumeUuid, VolumeVO.class);
        if (vo == null) {
            return null;
        }

        String psType = Q.New(PrimaryStorageVO.class)
                .eq(PrimaryStorageVO_.uuid, vo.getPrimaryStorageUuid())
                .select(PrimaryStorageVO_.type)
                .findValue();

        VolumeTO volume = new VolumeTO();
        volume.setUuid(vo.getUuid());
        volume.setName(vo.getName());
        volume.setType(vo.getType().toString());
        volume.setPath(vo.getInstallPath());
        volume.setFormat(vo.getFormat());
        volume.setDeviceId(vo.getDeviceId());
        volume.setPrimaryStorageType(psType);
        volume.setShareable(vo.isShareable());
        if (volume.isShareable()) {
            Integer deviceId = Q.New(ShareableVolumeVmInstanceRefVO.class).select(ShareableVolumeVmInstanceRefVO_.deviceId)
                    .eq(ShareableVolumeVmInstanceRefVO_.vmInstanceUuid, bmUuid)
                    .eq(ShareableVolumeVmInstanceRefVO_.volumeUuid, volumeUuid)
                    .findValue();
            if (deviceId != null) {
                volume.setDeviceId(deviceId);
            }
        }

        BlockVolumeVO blkVolume = Q.New(BlockVolumeVO.class).eq(BlockVolumeVO_.uuid, volume.getUuid()).find();
        if (blkVolume != null) {
            volume.setIscsiPath(blkVolume.getIscsiPath());
        }
        return volume;
    }

    private List<VolumeTO> buildVolumes(String bmUuid) {
        List<VolumeTO> volumes = new ArrayList<>();
        List<String> volumeUuids = Q.New(VolumeVO.class)
                .eq(VolumeVO_.vmInstanceUuid, bmUuid)
                .select(VolumeVO_.uuid)
                .listValues();

        for (String volumeUuid : volumeUuids) {
            volumes.add(buildVolume(volumeUuid, bmUuid));
        }
        return volumes;
    }

    private ProvisionNetworkTO buildProvisionNetwork(BareMetal2ProvisionNetworkInventory inventory) {
        ProvisionNetworkTO network = new ProvisionNetworkTO();
        network.setDhcpInterface(inventory.getDhcpInterface());
        network.setDhcpRangeStartIp(inventory.getDhcpRangeStartIp());
        network.setDhcpRangeEndIp(inventory.getDhcpRangeEndIp());
        network.setDhcpRangeNetmask(inventory.getDhcpRangeNetmask());
        network.setDhcpRangeGateway(inventory.getDhcpRangeGateway());
        network.setBmInstances(new ArrayList<>());
        List<String> instanceUuids = Q.New(BareMetal2InstanceProvisionNicVO.class)
                .eq(BareMetal2InstanceProvisionNicVO_.networkUuid, inventory.getUuid())
                .select(BareMetal2InstanceProvisionNicVO_.uuid)
                .listValues();
        instanceUuids.forEach(uuid -> {
            String gatewayIp = SQL.New("select nic.ip from BareMetal2InstanceVO instance, " +
                    "BareMetal2GatewayProvisionNicVO nic where instance.uuid = :bmUuid and nic.uuid = instance.gatewayUuid")
                    .param("bmUuid", uuid)
                    .find();
            if (gatewayIp != null) {
                network.getBmInstances().add(buildBmInstance(uuid, gatewayIp));
            }
        });
        return network;
    }

    private NicTO buildProvisionNic(String provisionNicUuid) {
        if (provisionNicUuid == null) {
            return null;
        }
        BareMetal2InstanceProvisionNicVO pNic = dbf.findByUuid(provisionNicUuid, BareMetal2InstanceProvisionNicVO.class);
        if (pNic == null) {
            return null;
        }

        String bondUuid = Q.New(BareMetal2BondingNicRefVO.class)
                .select(BareMetal2BondingNicRefVO_.bondingUuid)
                .eq(BareMetal2BondingNicRefVO_.provisionNicUuid, pNic.getUuid())
                .findValue();

        if (bondUuid == null) {
            return null;
        }

        NicTO nic = new NicTO();
        nic.setMac(pNic.getMac());
        nic.setIpAddress(pNic.getIp());
        nic.setNetmask(pNic.getNetmask());
        nic.setGateway(pNic.getGateway());
        nic.setDefaultRoute(false);

        BareMetal2BondingVO bondingVO = dbf.findByUuid(bondUuid, BareMetal2BondingVO.class);
        nic.setType(BareMetal2NetworkConstant.BONDING_NIC_TYPE);
        nic.setIfaceName(bondingVO.getName());
        nic.setParas(bondingVO.getFormattedJSON());
        return nic;
    }

    private NicTO buildNic(String nicUuid) {
        if (nicUuid == null) {
            return null;
        }

        VmNicVO vo = dbf.findByUuid(nicUuid, VmNicVO.class);
        if (vo == null) {
            return null;
        }

        String vmUuid = vo.getVmInstanceUuid();
        String l3Uuid = vo.getL3NetworkUuid();
        boolean isDefault = Q.New(VmInstanceVO.class)
                .eq(VmInstanceVO_.uuid, vmUuid)
                .eq(VmInstanceVO_.defaultL3NetworkUuid, l3Uuid)
                .isExists();

        String l2Uuid = Q.New(L3NetworkVO.class)
                .eq(L3NetworkVO_.uuid, l3Uuid)
                .select(L3NetworkVO_.l2NetworkUuid)
                .findValue();
        Integer vlanId = Q.New(L2VlanNetworkVO.class)
                .eq(L2VlanNetworkVO_.uuid, l2Uuid)
                .select(L2VlanNetworkVO_.vlan)
                .findValue();

        NicTO nic = new NicTO();
        nic.setMac(vo.getMac());
        nic.setIpAddress(vo.getIp());
        nic.setNetmask(vo.getNetmask());
        nic.setGateway(vo.getGateway());
        if (vlanId != null) {
            nic.setVlanId(vlanId);
        }
        nic.setDefaultRoute(isDefault);

        String bondUuid = Q.New(BareMetal2BondingNicRefVO.class)
                .select(BareMetal2BondingNicRefVO_.bondingUuid)
                .eq(BareMetal2BondingNicRefVO_.nicUuid, nicUuid)
                .findValue();

        if (bondUuid == null) {
            nic.setType(BareMetal2NetworkConstant.PHYSICAL_NIC_TYPE);
            return nic;
        }

        BareMetal2BondingVO bondingVO = dbf.findByUuid(bondUuid, BareMetal2BondingVO.class);
        nic.setType(BareMetal2NetworkConstant.BONDING_NIC_TYPE);
        nic.setIfaceName(bondingVO.getName());
        nic.setParas(bondingVO.getFormattedJSON());
        return nic;
    }

    private List<NicTO> buildNics(String bmUuid) {
        List<NicTO> nics = new ArrayList<>();
        List<String> nicUuids = Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, bmUuid)
                .select(VmNicVO_.uuid)
                .listValues();

        for (String nicUuid : nicUuids) {
            nics.add(buildNic(nicUuid));
        }

        NicTO pNic = buildProvisionNic(bmUuid);
        if (pNic != null) {
            nics.add(pNic);
        }
        return nics;
    }

    private enum StopBareMetal2InstanceType {
        stopByIpmi,
        stopByAgent,
        stopByAgentThenIpmi,
    }
}
