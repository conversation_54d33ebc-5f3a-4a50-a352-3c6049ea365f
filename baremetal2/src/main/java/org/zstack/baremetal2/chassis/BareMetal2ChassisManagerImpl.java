package org.zstack.baremetal2.chassis;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.baremetal2.BareMetal2GlobalProperty;
import org.zstack.baremetal2.BareMetal2SystemTags;
import org.zstack.baremetal2.BareMetal2Utils;
import org.zstack.baremetal2.chassis.allocator.BareMetal2ChassisInspectionStrategyFactory;
import org.zstack.baremetal2.chassis.ipmi.AddBareMetal2IpmiChassisMsg;
import org.zstack.baremetal2.chassis.ipmi.BareMetal2IpmiChassisApiInterceptor;
import org.zstack.baremetal2.cluster.BareMetal2ClusterConstant;
import org.zstack.baremetal2.gateway.*;
import org.zstack.baremetal2.instance.*;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.*;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.AbstractService;
import org.zstack.header.cluster.ClusterState;
import org.zstack.header.cluster.ClusterVO;
import org.zstack.header.cluster.ClusterVO_;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.HostConstant;
import org.zstack.header.host.HostState;
import org.zstack.header.host.HostStatus;
import org.zstack.header.longjob.LongJobConstants;
import org.zstack.header.longjob.SubmitLongJobMsg;
import org.zstack.header.longjob.SubmitLongJobReply;
import org.zstack.header.managementnode.ManagementNodeChangeListener;
import org.zstack.header.managementnode.ManagementNodeInventory;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.license.LicenseErrors;
import org.zstack.license.LicenseInfo;
import org.zstack.license.LicenseManager;
import org.zstack.license.LicenseType;
import org.zstack.tag.TagManager;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.verify.ParamValidator;
import org.zstack.utils.verify.Verifiable;

import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.*;
import static org.zstack.core.progress.ProgressReportService.reportProgress;
import static org.zstack.longjob.LongJobUtils.jobCanceled;

/**
 * Created by GuoYi on 9/25/20.
 */
public class BareMetal2ChassisManagerImpl extends AbstractService implements
        BareMetal2ChassisManager,
        ManagementNodeChangeListener,
        ManagementNodeReadyExtensionPoint {

    private static final CLogger logger = Utils.getLogger(BareMetal2ChassisManagerImpl.class);
    private static final String elasticBareMetalModuleName = "elastic-baremetal";
    private final Map<String, BareMetal2ChassisFactory> chassisFactories = new ConcurrentHashMap<>();
    private final Map<String, BareMetal2ChassisInspectionStrategyFactory> inspectionStrategyFactories = new ConcurrentHashMap<>();

    @Autowired
    protected CloudBus bus;
    @Autowired
    protected TagManager tagMgr;
    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    protected LicenseManager licMgr;
    @Autowired
    protected PluginRegistry pluginRegistry;
    @Autowired
    protected BareMetal2ChassisPingTracker pingTracker;
    @Autowired
    protected BareMetal2IpmiChassisApiInterceptor chassisValidator;

    @Override
    public void releaseBareMetal2Chassis(List<String> chassisUuids) {
        ReleaseBareMetal2ChassisMsg msg = new ReleaseBareMetal2ChassisMsg();
        msg.setUuids(chassisUuids);
        bus.makeLocalServiceId(msg, BareMetal2ChassisConstant.SERVICE_ID);
        bus.send(msg);
    }

    @Override
    public BareMetal2ChassisFactory getBareMetal2ChassisFactory(BareMetal2ChassisType type) {
        BareMetal2ChassisFactory factory = chassisFactories.get(type.toString());
        if (factory == null) {
            throw new CloudRuntimeException(String.format("No BareMetal2ChassisFactory for type: %s found", type));
        }
        return factory;
    }

    @Override
    public BareMetal2ChassisInspectionStrategyFactory getBareMetal2InspectionStrategyFactory(String type) {
        BareMetal2ChassisInspectionStrategyFactory factory = inspectionStrategyFactories.get(type);
        if (factory == null) {
            throw new CloudRuntimeException(String.format("No BareMetal2ChassisInspectionStrategyFactory for type: %s found", type));
        }
        return factory;
    }

    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIAddBareMetal2ChassisMsg) {
            handle((APIAddBareMetal2ChassisMsg) msg);
        } else if (msg instanceof APIBatchAddBareMetal2ChassisMsg) {
            handle((APIBatchAddBareMetal2ChassisMsg) msg);
        } else if (msg instanceof APICheckBareMetal2ChassisConfigFileMsg) {
            handle((APICheckBareMetal2ChassisConfigFileMsg) msg);
        } else if (msg instanceof APICreateBareMetal2ChassisHardwareInfoMsg) {
            handle((APICreateBareMetal2ChassisHardwareInfoMsg) msg);
        } else if (msg instanceof APIGetBareMetal2SupportedBootModeMsg) {
            handle((APIGetBareMetal2SupportedBootModeMsg) msg);
        } else if (msg instanceof BareMetal2ChassisMessage) {
            passThrough((BareMetal2ChassisMessage) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof AddBareMetal2ChassisMsg) {
            handle((AddBareMetal2ChassisMsg) msg);
        } else if (msg instanceof BatchAddBareMetal2ChassisMsg) {
            handle((BatchAddBareMetal2ChassisMsg) msg);
        } else if (msg instanceof AllocateBareMetal2ChassisMsg) {
            handle((AllocateBareMetal2ChassisMsg) msg);
        } else if (msg instanceof ReleaseBareMetal2ChassisMsg) {
            handle((ReleaseBareMetal2ChassisMsg) msg);
        } else if (msg instanceof BareMetal2ChassisMessage) {
            passThrough((BareMetal2ChassisMessage) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(ReleaseBareMetal2ChassisMsg msg) {
        logger.info(String.format("release baremetal2 chassis %s", msg.getUuids()));

        List<BareMetal2ChassisVO> chassises = Q.New(BareMetal2ChassisVO.class).in(BareMetal2ChassisVO_.uuid, msg.getUuids()).list();
        if (CollectionUtils.isEmpty(chassises)) {
            return;
        }

        String issuer = BareMetal2ChassisVO.class.getSimpleName();
        List<BareMetal2ChassisInventory> ctx = new ArrayList<>();
        ctx.addAll(BareMetal2ChassisInventory.valueOf(chassises));
        casf.asyncCascade(BareMetal2ChassisConstant.CHASSIS_RELEASE_CODE, issuer, ctx, new NopeCompletion());
    }

    private void handle(APIGetBareMetal2SupportedBootModeMsg msg) {
        APIGetBareMetal2SupportedBootModeReply reply = new APIGetBareMetal2SupportedBootModeReply();
        reply.setSupportedBootMode(String.join(",", BareMetal2GlobalProperty.BAREMETAL2_SUPPORTED_BOOT_MODE));
        bus.reply(msg, reply);
    }

    private void handle(APICreateBareMetal2ChassisHardwareInfoMsg msg) {
        APICreateBareMetal2ChassisHardwareReply reply = new APICreateBareMetal2ChassisHardwareReply();
        BareMetal2ChassisFactory factory = getBareMetal2ChassisFactory(BareMetal2ChassisType.valueOf(msg.getChassisType()));
        factory.createBareMetal2ChassisHardwareInfo(msg, new Completion(msg) {
            @Override
            public void success() {
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(APICheckBareMetal2ChassisConfigFileMsg msg) {
        APICheckBareMetal2ChassisConfigFileReply reply = new APICheckBareMetal2ChassisConfigFileReply();
        BareMetal2ChassisFactory factory = getBareMetal2ChassisFactory(BareMetal2ChassisType.valueOf(msg.getChassisType()));
        factory.buildAddBareMetal2ChassisMsgs(msg.getChassisInfo(), ParamValidator::validate);
        bus.reply(msg, reply);
    }

    private void handle(APIBatchAddBareMetal2ChassisMsg msg) {
        APIBatchAddBareMetal2ChassisEvent event = new APIBatchAddBareMetal2ChassisEvent(msg.getId());

        BatchAddBareMetal2ChassisMsg bmsg = new BatchAddBareMetal2ChassisMsg();
        bmsg.setChassisType(msg.getChassisType());
        bmsg.setChassisInfo(msg.getChassisInfo());
        bmsg.setAccountUuid(msg.getSession().getAccountUuid());

        SubmitLongJobMsg smsg = new SubmitLongJobMsg();
        smsg.setName(msg.getLongJobName());
        smsg.setDescription(msg.getLongJobDescription());
        smsg.setJobName(APIBatchAddBareMetal2ChassisMsg.class.getSimpleName());
        smsg.setJobData(JSONObjectUtil.toJsonString(bmsg));
        smsg.setAccountUuid(msg.getSession().getAccountUuid());
        bus.makeLocalServiceId(smsg, LongJobConstants.SERVICE_ID);
        bus.send(smsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    SubmitLongJobReply rly = reply.castReply();
                    event.setInventory(rly.getInventory());
                } else {
                    event.setError(reply.getError());
                }

                bus.publish(event);
            }
        });
    }

    private void handle(BatchAddBareMetal2ChassisMsg msg) {
        BatchAddBareMetal2ChassisReply reply = new BatchAddBareMetal2ChassisReply();
        batchAddBareMetal2Chassis(msg, new ReturnValueCompletion<List<AddBareMetal2ChassisResult>>(msg) {
            @Override
            public void success(List<AddBareMetal2ChassisResult> returnValue) {
                reply.setResults(returnValue);
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void batchAddBareMetal2Chassis(final BatchAddBareMetal2ChassisMsg msg,
                                           ReturnValueCompletion<List<AddBareMetal2ChassisResult>> completion) {
        List<AddBareMetal2ChassisResult> results = Collections.synchronizedList(new ArrayList<>());
        BareMetal2ChassisFactory factory = getBareMetal2ChassisFactory(BareMetal2ChassisType.valueOf(msg.getChassisType()));
        List<? extends AddBareMetal2ChassisMsg> amsgs = factory.buildAddBareMetal2ChassisMsgs(msg.getChassisInfo(), null)
                .stream().peek(amsg -> amsg.setAccountUuid(msg.getAccountUuid())).collect(Collectors.toList());

        reportProgress("0");

        new While<>(amsgs).each((amsg, compl) -> {
            if (jobCanceled()) {
                logger.debug("batch add baremetal2 chassis job has been canceled, abort the rest");
                compl.allDone();
            }

            ErrorCode err = validate(amsg);
            if (err != null) {
                results.add(factory.buildAddBareMetal2ChassisResult(amsg, err));
                reportProgress(new DecimalFormat("00").format((float) results.size() / amsgs.size() * 100));
                compl.done();
                return;
            }

            bus.makeLocalServiceId(amsg, BareMetal2ChassisConstant.SERVICE_ID);
            bus.send(amsg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    results.add(factory.buildAddBareMetal2ChassisResult(amsg, reply.getError()));
                    reportProgress(new DecimalFormat("00").format((float) results.size() / amsgs.size() * 100));
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                reportProgress("100");
                factory.sortAddBareMetal2ChassisResults(results);
                completion.success(results);
            }
        });
    }

    private ErrorCode validate(Verifiable msg) {
        try {
            ParamValidator.validate(msg);
        } catch (Exception e) {
            return argerr(e.getMessage());
        }
        return null;
    }

    private void handle(APIAddBareMetal2ChassisMsg msg) {
        APIAddBareMetal2ChassisEvent event = new APIAddBareMetal2ChassisEvent(msg.getId());
        BareMetal2ChassisFactory factory = getBareMetal2ChassisFactory(BareMetal2ChassisType.valueOf(msg.getChassisType()));
        AddBareMetal2ChassisMsg amsg = factory.buildAddBareMetal2ChassisMsg(msg);
        bus.makeTargetServiceIdByResourceUuid(amsg, BareMetal2ChassisConstant.SERVICE_ID, amsg.getClusterUuid());
        bus.send(amsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    AddBareMetal2ChassisReply rly = reply.castReply();
                    event.setInventory(rly.getInventory());
                } else {
                    event.setError(reply.getError());
                }

                bus.publish(event);
            }
        });
    }

    private void handle(AddBareMetal2ChassisMsg msg) {
        AddBareMetal2ChassisReply reply = new AddBareMetal2ChassisReply();
        addBareMetal2ChassisInQueue(msg, new ReturnValueCompletion<BareMetal2ChassisInventory>(msg) {
            @Override
            public void success(BareMetal2ChassisInventory chassis) {
                reply.setInventory(chassis);
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void addBareMetal2ChassisInQueue(final AddBareMetal2ChassisMsg msg,
                                             ReturnValueCompletion<BareMetal2ChassisInventory> completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return "add-baremetal2-chassis";
            }

            @Override
            public void run(SyncTaskChain chain) {
                ErrorCode err = checkBareMetal2License(msg);

                if (err == null && msg instanceof AddBareMetal2IpmiChassisMsg) {
                    err = chassisValidator.validate((AddBareMetal2IpmiChassisMsg) msg);
                }
                if (err != null) {
                    completion.fail(err);
                } else {
                    addBareMetal2Chassis(msg, completion);
                }

                chain.next();
            }

            @Override
            public String getName() {
                return String.format("add-baremetal2-chassis-%s", msg.getName());
            }
        });
    }

    private void addBareMetal2Chassis(final AddBareMetal2ChassisMsg msg,
                                      ReturnValueCompletion<BareMetal2ChassisInventory> completion) {
        // make sure baremetal2 cluster still exists
        ClusterVO cluster = Q.New(ClusterVO.class)
                .eq(ClusterVO_.uuid, msg.getClusterUuid())
                .eq(ClusterVO_.state, ClusterState.Enabled)
                .eq(ClusterVO_.type, BareMetal2ClusterConstant.BM2_CLUSTER_TYPE)
                .find();
        if (cluster == null) {
            completion.fail(argerr("BareMetal2 Chassis[uuid:%s] doesn't exist or is disabled", msg.getClusterUuid()));
            return;
        }

        final BareMetal2ChassisVO chassis = new BareMetal2ChassisVO();
        if (msg.getResourceUuid() == null) {
            chassis.setUuid(Platform.getUuid());
        } else {
            chassis.setUuid(msg.getResourceUuid());
        }
        chassis.setName(msg.getName());
        chassis.setDescription(msg.getDescription());
        chassis.setZoneUuid(cluster.getZoneUuid());
        chassis.setClusterUuid(msg.getClusterUuid());
        chassis.setState(BareMetal2ChassisState.Enabled);
        chassis.setStatus(BareMetal2ChassisStatus.HardwareInfoUnknown);
        chassis.setPowerStatus(BareMetal2ChassisPowerStatus.POWER_UNKNOWN);
        chassis.setProvisionType(msg.getProvisionType());

        final BareMetal2ChassisFactory factory = getBareMetal2ChassisFactory(BareMetal2ChassisType.valueOf(msg.getChassisType()));
        BareMetal2ChassisInventory inventory = factory.createBareMetal2Chassis(chassis, msg);
        tagMgr.createTags(msg.getSystemTags(), msg.getUserTags(), chassis.getUuid(), BareMetal2ChassisVO.class.getSimpleName());
        pingTracker.track(chassis.getUuid());

        if (msg.getReboot()) {
            InspectBareMetal2ChassisMsg imsg = new InspectBareMetal2ChassisMsg();
            imsg.setUuid(chassis.getUuid());
            bus.makeTargetServiceIdByResourceUuid(imsg, BareMetal2ChassisConstant.SERVICE_ID, chassis.getUuid());
            bus.send(imsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        InspectBareMetal2ChassisReply rly = reply.castReply();
                        completion.success(rly.getInventory());
                    } else {
                        completion.fail(reply.getError());
                    }
                }
            });
        } else if (BareMetal2Utils.isNonReboot(chassis.getUuid(), chassis.getProvisionType().toString())) {
            createBareMetal2InstanceNonReboot(chassis, msg, completion);
        } else {
            completion.success(inventory);
        }
    }

    private void createBareMetal2InstanceNonReboot(BareMetal2ChassisVO chassis, AddBareMetal2ChassisMsg msg, ReturnValueCompletion<BareMetal2ChassisInventory> completion) {
        CreateBareMetal2InstanceNonRebootMsg cmsg = new CreateBareMetal2InstanceNonRebootMsg();
        cmsg.setChassisUuid(chassis.getUuid());
        cmsg.setAccountUuid(msg.getAccountUuid());
        cmsg.setChassisName(chassis.getName());
        bus.makeLocalServiceId(cmsg, BareMetal2InstanceConstant.SERVICE_ID);
        bus.send(cmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                BareMetal2SystemTags.STATIC_PROVISION_IP.delete(chassis.getUuid(), BareMetal2ChassisVO.class);
                if (!reply.isSuccess()) {
                    dbf.removeByPrimaryKey(chassis.getUuid(), BareMetal2ChassisVO.class);
                    completion.fail(reply.getError());
                } else {
                    completion.success(BareMetal2ChassisInventory.valueOf(chassis));
                }
            }
        });
    }

    private void handle(AllocateBareMetal2ChassisMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return "baremetal2-chassis-allocator";
            }

            @Override
            public void run(SyncTaskChain chain) {
                List<BareMetal2ChassisInventory> results = getChassisCandidates(msg);

                if (msg.isDryRun()) {
                    final AllocateBareMetal2ChassisDryRunReply reply = new AllocateBareMetal2ChassisDryRunReply();
                    if (results.isEmpty()) {
                        reply.setError(operr("no available baremetal2 chassis found"));
                    } else {
                        reply.setChassisCandidates(results);
                    }
                    bus.reply(msg, reply);
                } else {
                    final AllocateBareMetal2ChassisReply reply = new AllocateBareMetal2ChassisReply();
                    if (results.isEmpty()) {
                        reply.setError(operr("no available baremetal2 chassis found " +
                                "in baremetal2 clusters[uuids:%s]", msg.getRequiredClusterUuids()));
                    } else {
                        reply.setChassis(results.get(0));

                        // if allocated new chassis, then old chassis should be released
                        String oldChassisUuid = Q.New(BareMetal2InstanceVO.class)
                                .eq(BareMetal2InstanceVO_.uuid, msg.getBareMetal2InstanceUuid())
                                .select(BareMetal2InstanceVO_.chassisUuid)
                                .findValue();
                        if (oldChassisUuid != null && !oldChassisUuid.equals(reply.getChassis().getUuid())) {
                            releaseBareMetal2Chassis(Collections.singletonList(oldChassisUuid));
                        }

                        // mark this chassis as allocated as soon as possible
                        SQL.New(BareMetal2ChassisVO.class)
                                .eq(BareMetal2ChassisVO_.uuid, reply.getChassis().getUuid())
                                .set(BareMetal2ChassisVO_.status, BareMetal2ChassisStatus.Allocated)
                                .update();
                    }
                    bus.reply(msg, reply);
                }

                chain.next();
            }

            @Override
            public String getName() {
                return "allocate-baremetal2-chassis-for-instance-" + msg.getBareMetal2InstanceUuid();
            }
        });
    }

    private List<BareMetal2ChassisInventory> getChassisCandidates(AllocateBareMetal2ChassisMsg msg) {
        if (msg.getRequiredChassisDiskUuid() != null) {
            BareMetal2ChassisDiskVO disk = dbf.findByUuid(msg.getRequiredChassisDiskUuid(), BareMetal2ChassisDiskVO.class);
            BareMetal2ChassisVO chassis = dbf.findByUuid(disk.getChassisUuid(), BareMetal2ChassisVO.class);
            return BareMetal2ChassisInventory.valueOf(Collections.singletonList(chassis));
        }

        if (msg.getRequiredChassisUuid() != null) {
            BareMetal2ChassisVO chassis = Q.New(BareMetal2ChassisVO.class)
                    .eq(BareMetal2ChassisVO_.uuid, msg.getRequiredChassisUuid())
                    .eq(BareMetal2ChassisVO_.state, BareMetal2ChassisState.Enabled)
                    .find();
            if (chassis == null) {
                return new ArrayList<>();
            }

            return BareMetal2ChassisInventory.valueOf(Collections.singletonList(chassis));
        }

        Q query = Q.New(BareMetal2ChassisVO.class)
                .eq(BareMetal2ChassisVO_.state, BareMetal2ChassisState.Enabled)
                .eq(BareMetal2ChassisVO_.status, BareMetal2ChassisStatus.Available);

        if (CollectionUtils.isNotEmpty(msg.getAvoidChassisUuids())) {
            query = query.notIn(BareMetal2ChassisVO_.uuid, msg.getAvoidChassisUuids());
        }

        if (msg.getChassisOfferingUuid() != null) {
            query = query.eq(BareMetal2ChassisVO_.chassisOfferingUuid, msg.getChassisOfferingUuid());
        }

        if (msg.getRequiredChassisUuid() != null) {
            query = query.eq(BareMetal2ChassisVO_.uuid, msg.getRequiredChassisUuid());
        }

        if (CollectionUtils.isNotEmpty(msg.getRequiredClusterUuids())) {
            query = query.in(BareMetal2ChassisVO_.clusterUuid, msg.getRequiredClusterUuids());
        }

        if (msg.getRequiredZoneUuid() != null) {
            query = query.eq(BareMetal2ChassisVO_.zoneUuid, msg.getRequiredZoneUuid());
        }

        return BareMetal2ChassisInventory.valueOf(query.list());
    }

    private void passThrough(BareMetal2ChassisMessage cmsg) {
        BareMetal2ChassisVO vo = dbf.findByUuid(cmsg.getChassisUuid(), BareMetal2ChassisVO.class);
        if (vo == null) {
            bus.replyErrorByMessageType((Message) cmsg, err(SysErrors.RESOURCE_NOT_FOUND,
                    "Cannot find BareMetal2 Chassis[uuid:%s], it may have been deleted", cmsg.getChassisUuid()));
            return;
        }

        BareMetal2ChassisFactory factory = getBareMetal2ChassisFactory(BareMetal2ChassisType.valueOf(vo.getType()));
        BareMetal2Chassis chassis = Platform.New(() -> factory.getBareMetal2Chassis(vo));
        chassis.handleMessage((Message) cmsg);
    }

    private Optional<LicenseInfo> getBareMetal2LicenseInfo() {
        return licMgr.getLicenseAddOns().stream()
                .filter(info -> info.getModules().contains(elasticBareMetalModuleName))
                .findFirst();
    }

    private ErrorCode checkBareMetal2License(final AddBareMetal2ChassisMsg msg) {
        Integer allowedChassisNum;
        LicenseType ltype = licMgr.getLicenseType();
        if (ltype == LicenseType.Trial) {
            allowedChassisNum = BareMetal2ChassisConstant.CHASSIS_NUM_TRIAL_LICENSE;
        } else {
            ErrorCode err = licMgr.checkAddonAvailability(elasticBareMetalModuleName);
            if (err != null) {
                return err;
            }
            Optional<LicenseInfo> bmLicense = getBareMetal2LicenseInfo();
            if (!bmLicense.isPresent()) {
                return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                        "License not found, please apply addon license for product elastic-baremetal."
                );
            }
            allowedChassisNum = bmLicense.get().getHostNum();
        }

        long currChassisNum = dbf.count(BareMetal2ChassisVO.class);
        // invalid addon license
        if (allowedChassisNum == null) {
            allowedChassisNum = BareMetal2ChassisConstant.CHASSIS_NUM_TRIAL_LICENSE;
        }

        if (currChassisNum > allowedChassisNum) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED, "Hijacked detected. Your license[%s] permits %s elastic-baremetal chassis, but we detect there are %s " +
                            "in the database. You can either delete additional chassis or apply a new license.",
                    licMgr.getLicenseType(), allowedChassisNum, currChassisNum
            );
        }

        if (currChassisNum == allowedChassisNum) {
            return err(LicenseErrors.LICENSE_NOT_PERMITTED,
                    "Insufficient elastic-baremetal chassis number licensed." +
                            " You can either delete additional chassis or apply a new license.");
        }

        return null;
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(BareMetal2ChassisConstant.SERVICE_ID);
    }

    @Override
    public boolean start() {
        populateExtensions();
        releaseBareMetal2Chassis();
        return true;
    }

    private void populateExtensions() {
        for (BareMetal2ChassisFactory factory : pluginRegistry.getExtensionList(BareMetal2ChassisFactory.class)) {
            BareMetal2ChassisFactory old = chassisFactories.get(factory.getChassisType().toString());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate BareMetal2ChassisFactory[%s, %s] for type[%s]",
                        factory.getClass().getName(), old.getClass().getName(), old.getChassisType()));
            }
            chassisFactories.put(factory.getChassisType().toString(), factory);
        }


        for (BareMetal2ChassisInspectionStrategyFactory factory : pluginRegistry.getExtensionList(BareMetal2ChassisInspectionStrategyFactory.class)) {
            BareMetal2ChassisInspectionStrategyFactory old = inspectionStrategyFactories.get(factory.getStrategyType());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate BareMetal2ChassisInspectionStrategyFactory[%s, %s] for type[%s]",
                    factory.getClass().getName(), old.getClass().getName(), old.getStrategyType()));
            }
            inspectionStrategyFactories.put(factory.getStrategyType(), factory);
        }
    }
    @Transactional
    private void releaseBareMetal2Chassis() {
        List<String> allocated = Q.New(BareMetal2ChassisVO.class)
                .eq(BareMetal2ChassisVO_.status, BareMetal2ChassisStatus.Allocated)
                .select(BareMetal2ChassisVO_.uuid)
                .listValues();

        if (allocated.isEmpty()) {
            return;
        }

        List<String> reallyAllocated = Q.New(BareMetal2InstanceVO.class)
                .notNull(BareMetal2InstanceVO_.chassisUuid)
                .select(BareMetal2InstanceVO_.chassisUuid)
                .groupBy(BareMetal2InstanceVO_.chassisUuid)
                .listValues();

        allocated.removeAll(reallyAllocated);
        if (allocated.isEmpty()) {
            return;
        }

        releaseBareMetal2Chassis(allocated);

    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void nodeJoin(ManagementNodeInventory inv) {

    }

    @Override
    public void nodeLeft(ManagementNodeInventory inv) {

    }

    @Override
    public void iAmDead(ManagementNodeInventory inv) {

    }

    @Override
    public void iJoin(ManagementNodeInventory inv) {

    }

    @Override
    public void managementNodeReady() {

    }
}
