package org.zstack.aliyun.nas.message

import org.zstack.nas.APICreateNasMountTargetEvent

doc {
    title "CreateAliyunNasMountTarget"

    category "aliyun.plugin"

    desc """创建阿里云NAS文件系统上的挂载点"""

    rest {
        request {
			url "POST /v1/nas/aliyun/mount"

			header (Authorization: 'OAuth the-session-uuid')

            clz APICreateAliyunNasMountTargetMsg.class

            desc """"""
            
			params {

				column {
					name "nasAccessGroupUuid"
					enclosedIn "params"
					desc "权限组UUID"
					location "body"
					type "String"
					optional false
					since "2.4.0"
				}
				column {
					name "vSwitchUuid"
					enclosedIn "params"
					desc "虚拟交换机UUID,如果是VPC网络则必填"
					location "body"
					type "String"
					optional true
					since "2.4.0"
				}
				column {
					name "nasFSUuid"
					enclosedIn "params"
					desc "NAS文件系统UUID"
					location "body"
					type "String"
					optional false
					since "2.4.0"
				}
				column {
					name "name"
					enclosedIn "params"
					desc "资源名称"
					location "body"
					type "String"
					optional false
					since "2.4.0"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "2.4.0"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc "资源UUID"
					location "body"
					type "String"
					optional true
					since "2.4.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "2.4.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "2.4.0"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "3.4.0"
				}
			}
        }

        response {
            clz APICreateNasMountTargetEvent.class
        }
    }
}