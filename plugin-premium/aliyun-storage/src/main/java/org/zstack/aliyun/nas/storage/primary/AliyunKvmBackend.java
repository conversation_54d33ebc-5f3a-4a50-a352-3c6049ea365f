package org.zstack.aliyun.nas.storage.primary;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.aliyun.core.AliyunPluginConstant;
import org.zstack.aliyun.core.BackupStorageKvmFactory;
import org.zstack.aliyun.nas.core.AliyunNasPrimaryStorageExtensionPoint;
import org.zstack.aliyun.nas.core.AliyunNasSystemTags;
import org.zstack.aliyun.nas.filesystem.AliyunNasAccessGroupVO;
import org.zstack.aliyun.nas.filesystem.AliyunNasFileSystemCanonicalEvent;
import org.zstack.aliyun.nas.filesystem.AliyunNasFileSystemVO;
import org.zstack.aliyun.nas.filesystem.AliyunNasMountTargetVO;
import org.zstack.aliyun.nas.message.*;
import org.zstack.aliyun.nas.storage.primary.imagestore.ImageStoreBackupStorageKvmUploader;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.asyncbatch.AsyncBatchRunner;
import org.zstack.core.asyncbatch.LoopAsyncBatch;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.db.*;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.upgrade.UpgradeChecker;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.aliyun.network.vpc.EcsVSwitchVO;
import org.zstack.header.aliyun.network.vpc.EcsVpcVO;
import org.zstack.header.aliyun.network.vpc.EcsVpcVO_;
import org.zstack.header.cluster.ClusterConnectionStatus;
import org.zstack.header.core.*;
import org.zstack.header.core.progress.TaskProgressRange;
import org.zstack.header.core.validation.Validation;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.host.*;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.image.*;
import org.zstack.header.message.MessageReply;
import org.zstack.header.storage.backup.*;
import org.zstack.header.storage.primary.*;
import org.zstack.header.storage.snapshot.VolumeSnapshotConstant;
import org.zstack.header.storage.snapshot.VolumeSnapshotInventory;
import org.zstack.header.vm.VmInstanceSpec;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmInstanceVO_;
import org.zstack.header.volume.*;
import org.zstack.hybrid.core.HybridUtilsForAliyun;
import org.zstack.identity.AccountManager;
import org.zstack.image.ImageExtensionPointEmitter;
import org.zstack.image.ImageManager;
import org.zstack.image.ImageSystemTags;
import org.zstack.kvm.*;
import org.zstack.nas.NasFileSystemConstant;
import org.zstack.nas.NasMountTargetVO;
import org.zstack.nas.NasMountTargetVO_;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageConstant;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageVO;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageVO_;
import org.zstack.header.storage.primary.ResizeVolumeOnPrimaryStorageReply;
import org.zstack.storage.primary.PrimaryStorageCapacityUpdater;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.path.PathUtil;

import javax.persistence.Tuple;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.err;
import static org.zstack.core.Platform.operr;
import static org.zstack.core.progress.ProgressReportService.*;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * Created by mingjian.deng on 2018/3/13.
 */
public class AliyunKvmBackend extends HypervisorBackend {
    private final static CLogger logger = Utils.getLogger(AliyunKvmBackend.class);

    @Autowired
    protected AccountManager acmgr;
    @Autowired
    private EventFacade eventf;
    @Autowired
    private ImageExtensionPointEmitter imageExtEmitter;
    @Autowired
    private ImageManager imageMgr;
    @Autowired
    protected UpgradeChecker upgradeChecker;

    protected AliyunNasPrimaryStorageExtensionPoint ext;

    protected String nasUuid;
    protected String accountUuid;

    final TaskProgressRange COMMIT_VOLUME_IMAGE_CREATE_SNAPSHOT_STAGE = new TaskProgressRange(0, 15);
    final TaskProgressRange COMMIT_VOLUME_IMAGE_COMMIT_SNAPSHOT_STAGE = new TaskProgressRange(15, 30);
    final TaskProgressRange COMMIT_VOLUME_IMAGE_UPLOAD_TEMPLATE_STAGE = new TaskProgressRange(30, 90);
    final TaskProgressRange COMMIT_VOLUME_IMAGE_SYNC_SIZE_STAGE = new TaskProgressRange(90, 100);
    final TaskProgressRange CREATE_ROOT_VOLUME_TEMPLATE_CREATE_TEMPORARY_TEMPLATE_STAGE = new TaskProgressRange(10, 30);
    final TaskProgressRange CREATE_ROOT_VOLUME_TEMPLATE_UPLOAD_STAGE = new TaskProgressRange(30, 90);
    final TaskProgressRange CREATE_ROOT_VOLUME_TEMPLATE_SUBSEQUENT_EVENT_STAGE = new TaskProgressRange(30, 90);


    public AliyunKvmBackend(PrimaryStorageVO self) {
        super(self);
        nasUuid = Q.New(AliyunNasPrimaryStorageFileSystemRefVO.class).
                eq(AliyunNasPrimaryStorageFileSystemRefVO_.primaryStorageUuid, self.getUuid()).
                select(AliyunNasPrimaryStorageFileSystemRefVO_.nasFileSystemUuid).findValue();
        accountUuid = acmgr.getOwnerAccountUuidOfResource(nasUuid);
        List<AliyunNasPrimaryStorageExtensionPoint> exts = pluginRgty.getExtensionList(AliyunNasPrimaryStorageExtensionPoint.class);
        DebugUtils.Assert(exts.size() == 1, "extensions [AliyunNasPrimaryStorageExtensionPoint] must be set 1, please check xmls.");
        ext = exts.get(0);
    }

    public static class AgentCmd {
        public String uuid;
    }

    public static class AgentRsp {
        public boolean success = true;
        public String error;
        public Long totalCapacity;
        public Long availableCapacity;
    }

    public static class MountCmd extends AgentCmd {
        public String mountPath;
        public String url;
        public String options;
    }

    public static class IsMountCmd extends AgentCmd {
        public String mountPath;
        public String url;
    }

    public static class IsMountRsp extends AgentRsp {
        public Boolean ismounted;
    }

    public static class MountDataCmd extends MountCmd {
        public String dataPath;
        public String basePath;
    }

    public static class PingCmd extends MountCmd {
        public String heartbeat;
    }

    public static class InitCmd extends MountCmd {
        public String common;
        public String data;
        public List<String> dirs;
    }

    public static class InitRsp extends AgentRsp {
        public Boolean mounted;
    }

    public static class GetCapacityCmd extends AgentCmd {
        public String mountPath;
    }

    public static class ListDirectionCmd extends AgentCmd {
        public String path;
    }

    public static class ListDirectionResponse extends AgentRsp {
        public List<String> paths;
    }

    public static class UpdateMountPointCmd extends MountCmd {
        public String newUrl;
    }

    public static class CheckBitsCmd extends AgentCmd {
        public String path;
    }

    public static class CheckBitsRsp extends AgentRsp {
        public boolean existing;
    }

    public static class CreateEmptyVolumeCmd extends AgentCmd {
        public String installPath;
        public long size;
        public String volumeUuid;
    }

    public static class CreateVolumeFromCacheCmd extends AgentCmd {
        public String templatePathInCache;
        public String installPath;
        public String volumeUuid;
    }

    public static class DeleteBitsCmd extends AgentCmd {
        public String path;
        public boolean folder = false;
    }

    public static class GetVolumeSizeCmd extends AgentCmd {
        public String volumeUuid;
        public String installPath;
    }

    public static class GetVolumeSizeRsp extends AgentRsp {
        public Long actualSize;
        public Long size;
    }

    public static class ReInitCmd extends AgentCmd {
        public String imagePath;
        public String volumePath;
    }

    public static class RevertVolumeFromSnapshotCmd extends AgentCmd {
        public String snapshotInstallPath;
    }

    public static class RevertVolumeFromSnapshotRsp extends AgentRsp {
        @Validation
        public String newVolumeInstallPath;
        @Validation
        protected long size;
    }

    public static class ResizeVolumeCmd extends AgentCmd {
        public String installPath;
        public long size;
    }

    public static class ResizeVolumeRsp extends AgentRsp {
        public long size;
    }

    public static class CommitVolumeAsImageCmd extends AgentCmd {
        public String primaryStorageInstallPath;
        public String description;
        public String hostname; // the host name of the image store backup storage
        public String imageUuid;
    }

    public static class CommitVolumeAsImageRsp extends AgentRsp {
        public String backupStorageInstallPath;
        public long size;
        public long actualSize;
    }

    public static class CreateTemplateFromVolumeCmd extends AgentCmd {
        public String installPath;
        public String volumePath;
    }

    public static class MergeSnapshotCmd extends AgentCmd {
        public String volumeUuid;
        public String snapshotInstallPath;
        public String workspaceInstallPath;
    }

    public static class MergeSnapshotRsp extends AgentRsp {
        public long actualSize;
        public long size;
    }

    public static class ReinitImageRsp extends AgentRsp {
        public String newVolumeInstallPath;
    }

    public static class CheckMountCmd extends AgentCmd {
        public String psUrl;
        public String hostUuid;
    }

    public static class CheckMountRsp extends AgentRsp {
        public List<NasMountData> datas = new ArrayList<>();
    }

    public static class OfflineMergeSnapshotCmd extends AgentCmd {
        public String srcPath;
        public String destPath;
        public boolean fullRebase;
    }

    public static class KvmSetupSelfFencerCmd extends MountCmd  {
        public long interval;
        public int maxAttempts;
        public String heartbeat;
        public String strategy;
    }

    public static class KvmCancelSelfFencerCmd extends AgentCmd {
    }

    // mount-path used for init, with commondir
    public static final String MOUNT_PATH = "/aliyun/nas/primarystorage/firstmount";
    public static final String IS_MOUNT_PATH = "/aliyun/nas/primarystorage/ismount";
    // mount-data-path used for datadir
    public static final String MOUNT_DATA_PATH = "/aliyun/nas/primarystorage/mountdata";
    public static final String INIT_PATH = "/aliyun/nas/primarystorage/init";
    public static final String PING_PATH = "/aliyun/nas/primarystorage/ping";
    public static final String GET_CAPACITY_PATH = "/aliyun/nas/primarystorage/getcapacity";
    public static final String UPDATE_MOUNT_POINT_PATH = "/aliyun/nas/primarystorage/updatemountpoint";
    public static final String REMOUNT_PATH = "/aliyun/nas/primarystorage/remount";
    public static final String UNMOUNT_PRIMARY_STORAGE_PATH = "/aliyun/nas/primarystorage/unmount";
    public static final String CHECK_BITS_PATH = "/aliyun/nas/primarystorage/checkbits";
    public static final String CREATE_EMPTY_VOLUME_PATH = "/aliyun/nas/primarystorage/createempty";
    public static final String CREATE_VOLUME_FROM_CACHE_PATH = "/aliyun/nas/primarystorage/createvolume";
    public static final String DELETE_BITS_PATH = "/aliyun/nas/primarystorage/deletebits";
    public static final String GET_VOLUME_SIZE_PATH = "/aliyun/nas/primarystorage/getvolumesize";
    public static final String REVERT_VOLUME_FROM_SNAPSHOT_PATH = "/aliyun/nas/primarystorage/revertvolume";
    public static final String REINIT_VOLUME_PATH = "/aliyun/nas/primarystorage/reinit";
    public static final String RESIZE_VOLUME_PATH = "/aliyun/nas/primarystorage/resize";
    public static final String COMMIT_PATH = "/aliyun/nas/primarystorage/commit";
    public static final String CREATE_TEMPLATE_FROM_VOLUME_PATH = "/aliyun/nas/primarystorage/createtemplatefromvolume";
    public static final String MERGE_SNAPSHOT_PATH = "/aliyun/nas/primarystorage/mergesnapshot";
    public static final String OFFLINE_MERGE_SNAPSHOT_PATH = "/aliyun/nas/primarystorage/snapshot/offlinemerge";
    public static final String CHECK_MOUNT_PATH = "/aliyun/nas/primarystorage/checkmount";
    public static final String ALIYUN_NAS_SELF_FENCER = "/ha/aliyun/nas/setupselffencer";
    public static final String CANCEL_ALIYUN_NAS_SELF_FENCER = "/ha/aliyun/nas/cancelselffencer";


    protected <T extends AgentRsp> void httpCall(String path, final String hostUuid, AgentCmd cmd, final Class<T> rspType, final ReturnValueCompletion<T> completion) {
        httpCall(path, hostUuid, cmd, false, rspType, completion);
    }

    private <T extends AgentRsp> void httpCall(String path, final String hostUuid, AgentCmd cmd, boolean noCheckStatus, final Class<T> rspType, final ReturnValueCompletion<T> completion) {
        DebugUtils.Assert(hostUuid != null, "host must be set here.");
        KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
        msg.setHostUuid(hostUuid);
        msg.setPath(path);
        msg.setNoStatusCheck(noCheckStatus);
        msg.setCommand(cmd);
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                KVMHostAsyncHttpCallReply r = reply.castReply();
                final T rsp = r.toResponse(rspType);
                if (!rsp.success) {
                    completion.fail(operr("operation error, because:%s", rsp.error));
                    return;
                }

                if (rsp.totalCapacity != null && rsp.availableCapacity != null) {
                    new PrimaryStorageCapacityUpdater(self.getUuid()).run(new PrimaryStorageCapacityUpdaterRunnable() {
                        @Override
                        public PrimaryStorageCapacityVO call(PrimaryStorageCapacityVO cap) {
                            if (cap.getTotalCapacity() == 0 || cap.getAvailableCapacity() == 0) {
                                cap.setAvailableCapacity(rsp.availableCapacity);
                            }

                            cap.setTotalCapacity(rsp.totalCapacity);
                            cap.setTotalPhysicalCapacity(rsp.totalCapacity);
                            cap.setAvailablePhysicalCapacity(rsp.availableCapacity);

                            return cap;
                        }
                    });
                }

                completion.success(rsp);
            }
        });
    }

    @Override
    public void attachHook(final String clusterUuid, final Completion completion) {
        connectByClusterUuid(clusterUuid, new ReturnValueCompletion<ClusterConnectionStatus>(completion) {
            @Override
            public void success(ClusterConnectionStatus clusterStatus) {
                if (clusterStatus == ClusterConnectionStatus.PartiallyConnected || clusterStatus == ClusterConnectionStatus.FullyConnected){
                    changeStatus(PrimaryStorageStatus.Connected);
                } else if (self.getStatus() == PrimaryStorageStatus.Disconnected && clusterStatus == ClusterConnectionStatus.Disconnected){
                    hookToKVMHostConnectedEventToChangeStatusToConnected();
                }
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private List<String> getConnectedHostByClusterUuid(String clusterUuid, boolean exceptionOnNotFound) {
        List<String> hostUuids = Q.New(HostVO.class).eq(HostVO_.clusterUuid, clusterUuid).eq(HostVO_.status, HostStatus.Connected).
                eq(HostVO_.hypervisorType, KVMConstant.KVM_HYPERVISOR_TYPE).notEq(HostVO_.state, HostState.Maintenance)
                .notEq(HostVO_.state, HostState.PreMaintenance).select(HostVO_.uuid).listValues();
        if (hostUuids.isEmpty() && exceptionOnNotFound) {
            throw new OperationFailureException(operr("no connected host found in the cluster[uuid:%s]", clusterUuid));
        }
        logger.debug(String.format("getHosts: %s", hostUuids));

        return hostUuids;
    }

    private void ismount(String url, String mountPath, String hostUuid, final ReturnValueCompletion<Boolean> completion) {
        IsMountCmd cmd = new IsMountCmd();
        cmd.uuid = self.getUuid();
        cmd.mountPath = mountPath;
        cmd.url = url;

        httpCall(IS_MOUNT_PATH, hostUuid, cmd, IsMountRsp.class, new ReturnValueCompletion<IsMountRsp>(completion) {
            @Override
            public void success(IsMountRsp rsp) {
                completion.success(rsp.ismounted);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void mount(String domain, String mountPath, String dataPath, String hostUuid, final Completion completion) {
        mount(domain, mountPath, dataPath, hostUuid, false, completion);
    }

    private void mount(String domain, String mountPath, String dataPath, String hostUuid, boolean noCheckStatus, final Completion completion) {
        MountDataCmd cmd = new MountDataCmd();
        cmd.uuid = self.getUuid();
        cmd.mountPath = mountPath;
        cmd.basePath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
        cmd.dataPath = dataPath;
        cmd.url = domain;
        cmd.options = AliyunNasSystemTags.MOUNT_OPTIONS.getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.MOUNT_OPTIONS_TOKEN);

        httpCall(MOUNT_DATA_PATH, hostUuid, cmd, noCheckStatus, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp rsp) {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void connect(String hostUuid, final Completion completion) {
        self = dbf.reload(self);
        MountCmd cmd = new MountCmd();
        cmd.uuid = self.getUuid();
        cmd.mountPath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
        cmd.url = self.getUrl();
        cmd.options = AliyunNasSystemTags.MOUNT_OPTIONS.getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.MOUNT_OPTIONS_TOKEN);

        httpCall(MOUNT_PATH, hostUuid, cmd, true, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp rsp) {
                updateHostPSStatus(PrimaryStorageHostStatus.Connected, hostUuid);
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void init(String hostUuid, String url, final ReturnValueCompletion<Boolean> completion) {
        InitCmd cmd = new InitCmd();
        cmd.uuid = self.getUuid();
        cmd.mountPath = self.getMountPath();   // --> /${mount-path}
        cmd.common = AliyunNasPrimaryStoragePathMaker.commonPath;
        cmd.data = AliyunNasPrimaryStoragePathMaker.dataPath;
        cmd.dirs = AliyunNasPrimaryStorageConstant.CommonDirs;
        cmd.url = url.contains(":") ? url : url + ":/";
        cmd.options = AliyunNasSystemTags.MOUNT_OPTIONS.getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.MOUNT_OPTIONS_TOKEN);

        httpCall(INIT_PATH, hostUuid, cmd, true, InitRsp.class, new ReturnValueCompletion<InitRsp>(completion) {
            @Override
            public void success(InitRsp rsp) {
                completion.success(rsp.mounted);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void uninit(String mountUuid, String hostUuid, final NoErrorCompletion completion) {
        unmount(hostUuid, mountUuid, self.getMountPath(), new Completion(completion) {
            @Override
            public void success() {
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.done();
            }
        });
    }

    private List<String> getAvailableHostUuidsFromZoneForOperation(String clusterUuid) {
        String sql = "select host.uuid from HostVO host where" +
                " host.zoneUuid = :zoneUuid and host.clusterUuid = :clusterUuid and host.status = :hstatus" +
                " and host.state = :hstate and host.hypervisorType in :htype";
        return SQL.New(sql).param("zoneUuid", self.getZoneUuid()).param("hstatus", HostStatus.Connected).
                param("clusterUuid", clusterUuid).
                param("hstate", HostState.Enabled).param("htype", KVMConstant.KVM_HYPERVISOR_TYPE).list();
    }

    private void setAccessGroup(CreateMountTargetInnerMsg msg) {
        String accessGroupUuid = AliyunNasSystemTags.ACCESS_GROUP_UUID.getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.ACCESS_GROUP_UUID_TOKEN);
        AliyunNasAccessGroupVO avo = dbf.findByUuid(accessGroupUuid, AliyunNasAccessGroupVO.class);
        if (avo == null) {
            throw new OperationFailureException(operr("AliyunNasAccessGroupVO[%s] is not existed, may be it has been deleted!", accessGroupUuid));
        }
        msg.setAccessGroupUuid(accessGroupUuid);
        String vSwitchUuid = AliyunNasSystemTags.VSWITCH_UUID.getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.VSWITCH_UUID_TOKEN);
        if (vSwitchUuid != null) {
            EcsVSwitchVO vvo = dbf.findByUuid(vSwitchUuid, EcsVSwitchVO.class);
            if (vvo == null) {
                throw new OperationFailureException(operr("EcsVSwitchVO[%s] is not existed, may be it has been deleted!", vSwitchUuid));
            }
            msg.setVswitchId(vvo.getvSwitchId());
            String vpcId = Q.New(EcsVpcVO.class).eq(EcsVpcVO_.uuid, vvo.getEcsVpcUuid()).select(EcsVpcVO_.ecsVpcId).findValue();
            msg.setVpcId(vpcId);
        }
    }

    private void createMountTarget(String accountUuid, String hostUuid, final ReturnValueCompletion<String> completion) {
        CreateMountTargetInnerMsg cmsg = new CreateMountTargetInnerMsg();
        cmsg.setNasFSUuid(nasUuid);
        cmsg.setAccountUuid(accountUuid);
        cmsg.setHostUuid(hostUuid);

        setAccessGroup(cmsg);

        bus.makeTargetServiceIdByResourceUuid(cmsg, NasFileSystemConstant.SERVICE_ID, cmsg.getAccessGroupUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    CreateMountTargetInnerReply reply1 = reply.castReply();
                    completion.success(reply1.getMountUuid());
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    public void deleteMountTarget(String mountUuid, final Completion completion) {
        DeleteMountTargetInnerMsg dmsg = new DeleteMountTargetInnerMsg();
        dmsg.setMountTargetUuid(mountUuid);
        // fake a session
        SessionInventory session = new SessionInventory();
        session.setAccountUuid(accountUuid);
        dmsg.setSession(session);
        bus.makeTargetServiceIdByResourceUuid(dmsg, NasFileSystemConstant.SERVICE_ID, mountUuid);
        bus.send(dmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    logger.warn(String.format("delete aliyun nas mount failed, caused: %s, skip and continue rollback",
                            reply.getError().getDetails()));
                }
                completion.success();
            }
        });
    }

    private void deleteMountTarget(String hostUuid, String mountUuid, String mountPath, final Completion completion) {
        if (hostUuid == null) {
            deleteMountTarget(mountUuid, completion);
        } else {
            unmount(hostUuid, mountUuid, mountPath, completion);
        }
    }

    private void deleteMountTarget(String hostUuid, String mountUuid, String mountPath, final NoErrorCompletion completion) {
        deleteMountTarget(hostUuid, mountUuid, mountPath, new Completion(completion) {
            @Override
            public void success() {
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.done();
            }
        });
    }

    private void init(String hostUuid, final Completion completion) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("init primarystorage: %s", self.getUuid()));
        chain.then(new Flow() {
            String __name__ = String.format("check if need create mount target in nas primary storage: %s", self.getUuid());
            @Override
            public void run(FlowTrigger trigger, Map data) {
                String commonMountTargetUuid = AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID.
                        getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID_TOKEN);
                NasMountTargetVO mvo = null;
                if (commonMountTargetUuid != null) {
                    logger.debug(String.format("found commonMountTargetUuid: %s", commonMountTargetUuid));
                    mvo = dbf.findByUuid(commonMountTargetUuid, NasMountTargetVO.class);
                }
                if (mvo == null) {
                    createMountTarget(accountUuid, hostUuid, new ReturnValueCompletion<String>(trigger) {
                        @Override
                        public void success(String mountTargetUuid) {
                            SystemTagCreator creator = AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID.newSystemTagCreator(self.getUuid());
                            creator.inherent = true;
                            creator.recreate = true;
                            creator.setTagByTokens(map(e(AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID_TOKEN,
                                    mountTargetUuid)));
                            creator.create();

                            data.put("commonMountTargetUuid", mountTargetUuid);
                            NasMountTargetVO mvo = dbf.findByUuid(mountTargetUuid, NasMountTargetVO.class);
                            self.setUrl(AliyunNasPrimaryStoragePathMaker.getBaseUrl(self, mvo.getMountDomain()));
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                } else {
                    self.setUrl(AliyunNasPrimaryStoragePathMaker.getBaseUrl(self, mvo.getMountDomain()));
                    trigger.next();
                }
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String mountUuid = (String)data.get("commonMountTargetUuid");
                if (mountUuid != null) {
                    deleteMountTarget(hostUuid, mountUuid, self.getMountPath(), new NoErrorCompletion(trigger) {
                        @Override
                        public void done() {
                            AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID.deleteInherentTag(self.getUuid(), PrimaryStorageVO.class);
                            trigger.rollback();
                        }
                    });
                } else {
                    trigger.rollback();
                }
            }
        });
        chain.then(new Flow() {
            String __name__ = String.format("init nas primary storage: %s", self.getUuid());
            @Override
            public void run(FlowTrigger trigger, Map data) {
                String mountUuid = (String)data.get("commonMountTargetUuid");
                if (mountUuid == null) {
                    trigger.next();
                    return;
                }
                init(hostUuid, self.getUrl(), new ReturnValueCompletion<Boolean>(trigger) {
                    @Override
                    public void success(Boolean newMount) {
                        if (newMount) {
                            data.put("mount", true);
                        }
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (data.get("mount") == null) {
                    trigger.rollback();
                    return;
                }
                boolean mount = (boolean)data.get("mount");
                if (mount) {
                    String mountUuid = (String)data.get("commonMountTargetUuid");
                    uninit(mountUuid, hostUuid, new NoErrorCompletion(trigger) {
                        @Override
                        public void done() {
                            trigger.rollback();
                        }
                    });
                } else {
                    trigger.rollback();
                }
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                dbf.updateAndRefresh(self);
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();

    }

    private void connect(List<String> huuids, String clusterUuid, final Completion completion) {
        class Result {
            Set<ErrorCode> errorCodes = new HashSet<>();
            List<String> huuids = Collections.synchronizedList(new ArrayList<String>());
        }

        final Result ret = new Result();
        if (huuids == null || huuids.isEmpty()) {
            completion.fail(operr("cannot find an available host to operation in primary storage: %s", self.getUuid()));
            return;
        }

        if (upgradeChecker.skipInnerDeployOrInitOnCurrentAgent(huuids.get(0))) {
            completion.success();
            return;
        }

        init(huuids.get(0), new Completion(completion) {
            @Override
            public void success() {
                final AsyncLatch latch = new AsyncLatch(huuids.size(), new NoErrorCompletion(completion) {
                    @Override
                    public void done() {
                        if (!ret.errorCodes.isEmpty()) {
                            StringBuffer mountPathErrorInfo = new StringBuffer("Can't access mount path on ");
                            for(String hostUuid : ret.huuids) {
                                mountPathErrorInfo.append(String.format("host[uuid:%s] ", hostUuid));
                            }
                            completion.fail(errf.stringToOperationError(
                                    String.format("unable to connect the shared mount point storage[uuid:%s, name:%s] to the cluster[uuid:%s], %s",
                                            self.getUuid(), self.getName(), clusterUuid, mountPathErrorInfo),
                                    new ArrayList<>(ret.errorCodes)
                            ));
                        } else {
                            dbf.updateAndRefresh(self);
                            completion.success();
                        }
                    }
                });

                // remove data to avoid rollback
                for (String huuid : huuids) {
                    connect(huuid, new Completion(latch) {
                        @Override
                        public void success() {
                            latch.ack();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            ret.errorCodes.add(errorCode);
                            ret.huuids.add(huuid);
                            latch.ack();
                        }
                    });
                }
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void connectByClusterUuid(String clusterUuid, final ReturnValueCompletion<ClusterConnectionStatus> completion) {
        List<String> huuids = getConnectedHostByClusterUuid(clusterUuid, false);
        if (huuids.isEmpty()) {
            // no host in the cluster
            completion.success(ClusterConnectionStatus.Disconnected);
            return;
        }

        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("connect primary storage: %s in cluster: %s", self.getUuid(), clusterUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                connect(huuids, clusterUuid, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success(ClusterConnectionStatus.FullyConnected);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    @Override
    void ping(String clusterUuid, Completion completion) {
        List<String> hostUuids = getAvailableHostUuidsFromZoneForOperation(clusterUuid);
        if (hostUuids.isEmpty()) {
            completion.fail(operr("cannot find an available host to operation in primary storage: %s", self.getUuid()));
            return;
        }

        hostUuids = hostUuids.stream()
                .filter(hostUuid -> upgradeChecker.skipInnerDeployOrInitOnCurrentAgent(hostUuid))
                .collect(Collectors.toList());
        if (hostUuids.isEmpty()) {
            completion.success();
            return;
        }

        pingFilter(hostUuids, new Completion(completion) {
            @Override
            public void success() {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void pingFilter(List<String> hostUuids, Completion completion){
        List<ErrorCode> errs = new ArrayList<>();
        new While<>(hostUuids).each((hostUuid, compl) -> {
            pingFilterStep(hostUuid, new Completion(compl) {
                @Override
                public void success() {
                    compl.allDone();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    errs.add(errorCode);
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if(errs.size() == hostUuids.size()){
                    completion.fail(errs.get(0));
                }else {
                    completion.success();
                }
            }
        });
    }

    private void pingFilterStep(String hostUuid, final Completion completion){
        doPing(hostUuid, new Completion(completion) {
            @Override
            public void success() {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void doPing(String hostUuid, Completion completion){
        PingCmd cmd = new PingCmd();
        cmd.uuid = self.getUuid();
        cmd.mountPath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
        cmd.url = self.getUrl();
        cmd.heartbeat = "heartbeat";

        KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
        msg.setCommand(cmd);
        msg.setPath(PING_PATH);
        msg.setHostUuid(hostUuid);
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    AgentRsp rsp = ((KVMHostAsyncHttpCallReply) reply).toResponse(AgentRsp.class);
                    if (rsp.success) {
                        updateHostPSStatus(PrimaryStorageHostStatus.Connected, hostUuid);
                        completion.success();
                    } else {
                        updateHostPSStatus(PrimaryStorageHostStatus.Disconnected, hostUuid);
                        completion.fail(operr("failed to ping aliyun nas primary storage[uuid:%s] from host[uuid:%s],because %s. " +
                                "disconnect this host-ps connection", self.getUuid(), hostUuid, rsp.error));
                    }
                } else {
                    updateHostPSStatus(PrimaryStorageHostStatus.Disconnected, hostUuid);
                    completion.fail(reply.getError());
                }
            }
        });
    }

    @Override
    public void getPhysicalCapacity(String clusterUuid, final ReturnValueCompletion<PhysicalCapacityUsage> completion) {
        String hostUuid = getAvailableHostUuidsFromZoneForOperation(clusterUuid).get(0);

        GetCapacityCmd cmd = new GetCapacityCmd();
        cmd.mountPath = self.getMountPath();
        cmd.uuid = self.getUuid();

        KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
        msg.setHostUuid(hostUuid);
        msg.setPath(GET_CAPACITY_PATH);
        msg.setCommand(cmd);
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                KVMHostAsyncHttpCallReply r = reply.castReply();
                AgentRsp rsp = r.toResponse(AgentRsp.class);
                if (!r.isSuccess()) {
                    completion.fail(operr("operation error, because:%s", rsp.error));
                    return;
                }

                PhysicalCapacityUsage usage = new PhysicalCapacityUsage();
                usage.totalPhysicalSize = rsp.totalCapacity;
                usage.availablePhysicalSize = rsp.availableCapacity;
                completion.success(usage);
            }
        });
    }

    @Override
    public void updateMountPoint(String clusterUuid, String oldMountPoint,
                                 String newMountPoint, Completion completion) {
        final List<String> huuids = getConnectedHostByClusterUuid(clusterUuid, true);

        String options = AliyunNasSystemTags.MOUNT_OPTIONS.getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.MOUNT_OPTIONS_TOKEN);

        new LoopAsyncBatch<String>(completion) {
            @Override
            protected Collection<String> collect() {
                return huuids;
            }

            @Override
            protected AsyncBatchRunner forEach(String hostUuid) {
                return new AsyncBatchRunner() {
                    @Override
                    public void run(NoErrorCompletion completion) {
                        UpdateMountPointCmd cmd = new UpdateMountPointCmd();
                        cmd.mountPath = self.getMountPath();
                        cmd.newUrl = newMountPoint;
                        cmd.url = oldMountPoint;
                        cmd.options = options;
                        cmd.uuid = self.getUuid();

                        httpCall(UPDATE_MOUNT_POINT_PATH, hostUuid, cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
                            @Override
                            public void success(AgentRsp rsp) {
                                completion.done();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                errors.add(errorCode);
                                logger.warn(String.format("unable to update the aliyun nas[uuid:%s, name:%s] mount point" +
                                                " from %s to %s on the host[uuid:%s], %s. Put the host-nas into Disconnected status",
                                        self.getUuid(), self.getName(), oldMountPoint, newMountPoint, hostUuid, errorCode));
                                updateHostPSStatus(PrimaryStorageHostStatus.Disconnected, hostUuid);
                                completion.done();
                            }
                        });

                    }
                };
            }

            @Override
            protected void done() {
                if(errors.size() == huuids.size()){
                    completion.fail(errors.get(0));
                }else {
                    completion.success();
                }
            }
        }.start();
    }

    private void remount(String huuid, Completion completion) {
        AliyunKvmBackend.MountCmd cmd = new AliyunKvmBackend.MountCmd();
        cmd.mountPath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
        cmd.url = self.getUrl();
        cmd.uuid = self.getUuid();
        cmd.options = AliyunNasSystemTags.MOUNT_OPTIONS.getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.MOUNT_OPTIONS_TOKEN);

        httpCall(REMOUNT_PATH, huuid, cmd, true, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp rsp) {
                updateHostPSStatus(PrimaryStorageHostStatus.Connected, huuid);
                logger.debug(String.format("succeed to mount aliyun nas[uuid:%s] from host[uuid:%s]"
                        , self.getUuid(), huuid));
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                updateHostPSStatus(PrimaryStorageHostStatus.Disconnected, huuid);
                logger.warn(String.format("fail to mount aliyun nas[uuid:%s] from host[uuid:%s], because:%s"
                        , self.getUuid(), huuid, errorCode.toString()));
                completion.fail(errorCode);
            }
        });
    }

    void mountHostToPrimary(String huuid, Completion completion) {
        if (self.getUrl().equals(AliyunPluginConstant.ALIYUN_NAS_PRIMARY_STORAGE_TYPE)) {
            thdf.chainSubmit(new ChainTask(completion) {
                @Override
                public String getSyncSignature() {
                    return String.format("init-primary-storage-%s", self.getUuid());
                }

                @Override
                public void run(SyncTaskChain chain) {
                    // check again
                    self = dbf.reload(self);
                    if (self.getUrl().equals(AliyunPluginConstant.ALIYUN_NAS_PRIMARY_STORAGE_TYPE)) {
                        init(huuid, new Completion(completion) {
                            @Override
                            public void success() {
                                completion.success();
                                chain.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                                chain.next();
                            }
                        });
                    } else {
                        remount(huuid, new Completion(completion) {
                            @Override
                            public void success() {
                                completion.success();
                                chain.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                                chain.next();
                            }
                        });
                    }
                }

                @Override
                public String getName() {
                    return getSyncSignature();
                }
            });
        } else {
            remount(huuid, completion);
        }
    }

    @Override
    public void detachFromCluster(String clusterUuid, Completion completion)  {
        logger.debug(String.format("detach from cluster: %s", clusterUuid));
        List<String> hostUuids = Q.New(HostVO.class).eq(HostVO_.status, HostStatus.Connected).
                eq(HostVO_.clusterUuid, clusterUuid).select(HostVO_.uuid).listValues();
        String umountPath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
        String mountUuid = AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID.
                getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID_TOKEN);
        if (hostUuids.isEmpty()) {
            completion.success();
        } else {
            new While<>(hostUuids).all((hostUuid, c) -> {
                syncUnmount(hostUuid, mountUuid, umountPath, false, new Completion(c) {
                    @Override
                    public void success() {
                        c.done();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        c.done();
                    }
                });
            }).run(new WhileDoneCompletion(completion) {
                @Override
                public void done(ErrorCodeList errorCodeList) {
                    completion.success();
                }
            });
        }
    }

    public void updateMount(String hostUuid, AliyunNasMountVolumeRefVO old, Completion completion) {
        String mountPath;
        String dataPath;
        DebugUtils.Assert(old != null, "AliyunNasMountVolumeRefVO cannot be null here!");

        if (old.getSourceType() == MountSourceType.imagecache) {
            ImageVO ivo = dbf.findByUuid(old.getImageUuid(), ImageVO.class);
            if (ivo == null) {
                // image is not existed, remove it
                dbf.remove(old);
                completion.success();
                return;
            }
            ImageInventory image = ImageInventory.valueOf(ivo);
            mountPath = AliyunNasPrimaryStoragePathMaker.makeCachedImageBaseDir(self, image);
            dataPath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
        } else {
            VolumeVO vvo = dbf.findByUuid(old.getVolumeUuid(), VolumeVO.class);
            if (vvo == null) {
                // volume is not existed, remove it
                dbf.remove(old);
                completion.success();
                return;
            }
            VolumeInventory volume = VolumeInventory.valueOf(vvo);
            mountPath = AliyunNasPrimaryStoragePathMaker.makeVolumeBaseDir(self, volume);
            dataPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
        }


        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName("unmount-old-target-and-update-to-new-target");
        chain.then(new ShareFlow() {
            NasMountTargetVO mvo;
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = String.format("create-new-mount-target-on-host-%s", hostUuid);
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        String accountUuid = acmgr.getOwnerAccountUuidOfResource(old.getNasMountUuid());
                        createMountTarget(accountUuid, hostUuid, new ReturnValueCompletion<String>(trigger) {
                            @Override
                            public void success(String mUuid) {
                                mvo = dbf.findByUuid(mUuid, NasMountTargetVO.class);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (mvo != null) {
                            deleteMountTarget(mvo.getUuid(), new NopeCompletion());
                        }
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("unmount-target-%s-on-host-%s", old.getNasMountUuid(), hostUuid);
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        unmount(hostUuid, old.getNasMountUuid(), mountPath, false, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("mount-new-target-to-host-%s", hostUuid);
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        makeSureVolumeMountTarget(mvo.getMountDomain(), mountPath, dataPath, hostUuid, new Completion(trigger) {
                            @Override
                            public void success() {
                                AliyunNasMountVolumeRefVO ref = new AliyunNasMountVolumeRefVO();
                                if (old.getSourceType() == MountSourceType.imagecache) {
                                    ref.setImageUuid(old.getImageUuid());
                                } else {
                                    ref.setVolumeUuid(old.getVolumeUuid());
                                }
                                ref.setHostUuid(hostUuid);
                                ref.setSourceType(old.getSourceType());
                                ref.setNasMountUuid(mvo.getUuid());
                                ref.setDataPath(dataPath);
                                new SQLBatch() {
                                    @Override
                                    protected void scripts() {
                                        dbf.persistAndRefresh(ref);
                                        dbf.removeByPrimaryKey(old.getNasMountUuid(), NasMountTargetVO.class);
                                    }
                                }.execute();
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success();
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    public void unmount(String hostUuid, String mountUuid, String mountPath, Completion completion) {
        unmount(hostUuid, mountUuid, mountPath, true, completion);
    }

    public void unmountCommonPath(String hostUuid, boolean deleteMount, Completion completion) {
        String mountUuid = AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID.
                getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID_TOKEN);

        if (mountUuid == null) {
            completion.success();
            return;
        }

        String mountPath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
        unmount(hostUuid, mountUuid, mountPath, deleteMount, completion);
    }

    public void mountCommonPath(String hostUuid, Completion completion) {
        String mountUuid = AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID.
                getTokenByResourceUuid(self.getUuid(), AliyunNasSystemTags.COMMONS_PATH_MOUNT_TARGET_UUID_TOKEN);

        if (mountUuid == null) {
            completion.success();
            return;
        }

        connect(hostUuid, completion);
    }

    public void unmount(String hostUuid, String mountUuid, String mountPath, boolean deleteMount, Completion completion) {
        MountCmd cmd = new MountCmd();
        cmd.uuid = self.getUuid();
        if (mountPath == null) {
            cmd.mountPath = self.getMountPath();
        } else {
            cmd.mountPath = mountPath;
        }

        httpCall(UNMOUNT_PRIMARY_STORAGE_PATH, hostUuid, cmd, true, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp rsp) {
                logger.debug(String.format("succeed to unmount aliyun nas mount[uuid:%s] from host[uuid:%s], on path[%s]",
                        mountPath, hostUuid, self.getUuid()));

                if (deleteMount) {
                    String domain = Q.New(NasMountTargetVO.class).eq(NasMountTargetVO_.uuid, mountUuid).select(NasMountTargetVO_.mountDomain).findValue();
                    AliyunNasFileSystemCanonicalEvent.MountTargetDeleteData d = new AliyunNasFileSystemCanonicalEvent.MountTargetDeleteData();
                    AliyunNasFileSystemVO nvo = dbf.findByUuid(nasUuid, AliyunNasFileSystemVO.class);
                    d.setAccountUuid(accountUuid);
                    d.setRegionId(HybridUtilsForAliyun.getRegionIdFromDcUuid(nvo.getDataCenterUuid()));
                    d.setFileSystemId(nvo.getFileSystemId());
                    d.setMountDomain(domain);
                    dbf.removeByPrimaryKey(mountUuid, AliyunNasMountTargetVO.class);
                    evtf.fire(AliyunNasFileSystemCanonicalEvent.MOUNT_TARGET_DELETE_PATH, d);
                }
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.warn(String.format("fail to unmount aliyun nas[uuid:%s] from host[uuid:%s], because:%s"
                        , self.getUuid(), hostUuid, errorCode.toString()));
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void handle(final DownloadIsoToPrimaryStorageMsg msg, ReturnValueCompletion<String> completion) {
        ext.isMounted(self, self.getUrl(), AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self), new ReturnValueCompletion<Boolean>(completion) {
            @Override
            public void success(Boolean isMounted) {
                if (isMounted) {
                    FlowChain chain = FlowChainBuilder.newShareFlowChain();
                    chain.setName(String.format("download-iso-%s-to-primarystorage-%s", msg.getIsoSpec().getInventory().getUuid(), msg.getPrimaryStorageUuid()));

                    chain.then(new ShareFlow() {
                        String installPath = AliyunNasPrimaryStoragePathMaker.makeCachedImageInstallPath(self, msg.getIsoSpec().getInventory());
                        @Override
                        public void setup() {
                            flow(new NoRollbackFlow() {
                                String __name__ = "create-mount-target-and-mount-to-host-for-download-iso";
                                @Override
                                public void run(FlowTrigger trigger, Map data) {
                                    createMountDomainForImage(msg.getIsoSpec().getInventory(), msg.getDestHostUuid(), new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                        @Override
                                        public void success(CreateMountDomainProperty returnValue) {
                                            trigger.next();
                                        }

                                        @Override
                                        public void fail(ErrorCode errorCode) {
                                            trigger.fail(errorCode);
                                        }
                                    });
                                }
                            });

                            flow(new NoRollbackFlow() {
                                String __name__ = "download-iso";
                                @Override
                                public void run(FlowTrigger trigger, Map data) {
                                    VmInstanceSpec.ImageSpec ispec = msg.getIsoSpec();
                                    ImageCache cache = new ImageCache();

                                    cache.image = ispec.getInventory();
                                    cache.primaryStorageInstallPath = installPath;
                                    cache.backupStorageUuid = ispec.getSelectedBackupStorage().getBackupStorageUuid();
                                    cache.backupStorageInstallPath = ispec.getSelectedBackupStorage().getInstallPath();
                                    cache.download(msg.getDestHostUuid(), new ReturnValueCompletion<String>(completion) {
                                        @Override
                                        public void success(String installPath) {
                                            trigger.next();
                                        }

                                        @Override
                                        public void fail(ErrorCode errorCode) {
                                            trigger.fail(errorCode);
                                        }
                                    });
                                }
                            });

                            done(new FlowDoneHandler(completion) {
                                @Override
                                public void handle(Map data) {
                                    completion.success(installPath);
                                }
                            });

                            error(new FlowErrorHandler(completion) {
                                @Override
                                public void handle(ErrorCode errCode, Map data) {
                                    completion.fail(errCode);
                                }
                            });
                        }
                    }).start();
                } else {
                    completion.fail(operr("nas primary storage not mounted, please init it first!"));
                }
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    class ImageCache {
        ImageInventory image;
        String backupStorageUuid;
        String primaryStorageInstallPath;
        String backupStorageInstallPath;

        public void download(String hostUuid, final ReturnValueCompletion<String> completion) {
            DebugUtils.Assert(image != null, "image cannot be null");
            DebugUtils.Assert(backupStorageUuid != null, "backup storage UUID cannot be null");
            DebugUtils.Assert(primaryStorageInstallPath != null, "primaryStorageInstallPath cannot be null");
            DebugUtils.Assert(backupStorageInstallPath != null, "backupStorageInstallPath cannot be null");

            thdf.chainSubmit(new ChainTask(completion) {
                @Override
                public String getSyncSignature() {
                    return String.format("download-image-%s-to-aliyun-nas-primary-storage-%s-cache", image.getUuid(), self.getUuid());
                }

                private void doDownload(final SyncTaskChain chain) {
                    FlowChain fchain = FlowChainBuilder.newShareFlowChain();
                    fchain.setName("download-image-chain");
                    fchain.then(new ShareFlow() {
                        @Override
                        public void setup() {
                        flow(new Flow() {
                            String __name__ = "allocate-primary-storage";
                            boolean s = false;
                            @Override
                            public void run(final FlowTrigger trigger, Map data) {
                                AllocatePrimaryStorageMsg amsg = new AllocatePrimaryStorageMsg();
                                amsg.setRequiredPrimaryStorageUuid(self.getUuid());
                                amsg.setSize(image.getActualSize());
                                amsg.setPurpose(PrimaryStorageAllocationPurpose.DownloadImage.toString());
                                amsg.setNoOverProvisioning(true);
                                bus.makeLocalServiceId(amsg, PrimaryStorageConstant.SERVICE_ID);
                                bus.send(amsg, new CloudBusCallBack(trigger) {
                                    @Override
                                    public void run(MessageReply reply) {
                                        if (reply.isSuccess()) {
                                            s = true;
                                            trigger.next();
                                        } else {
                                            trigger.fail(reply.getError());
                                        }
                                    }
                                });
                            }

                            @Override
                            public void rollback(FlowRollback trigger, Map data) {
                                if (s) {
                                    IncreasePrimaryStorageCapacityMsg imsg = new IncreasePrimaryStorageCapacityMsg();
                                    imsg.setDiskSize(image.getActualSize());
                                    imsg.setNoOverProvisioning(true);
                                    imsg.setPrimaryStorageUuid(self.getUuid());
                                    bus.makeLocalServiceId(imsg, PrimaryStorageConstant.SERVICE_ID);
                                    bus.send(imsg);
                                }

                                trigger.rollback();
                            }
                        });

                        flow(new NoRollbackFlow() {
                            String __name__ = "download";

                            @Override
                            public void run(final FlowTrigger trigger, Map data) {
                                BackupStorageKvmDownloader downloader = getBackupStorageKvmDownloader(backupStorageUuid);
                                downloader.downloadBits(backupStorageInstallPath, primaryStorageInstallPath, hostUuid, new Completion(trigger) {
                                    @Override
                                    public void success() {
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        trigger.fail(errorCode);
                                    }
                                });
                            }
                        });

                        done(new FlowDoneHandler(completion, chain) {
                            @Override
                            public void handle(Map data) {
                                ImageCacheVO vo = new ImageCacheVO();
                                vo.setState(ImageCacheState.ready);
                                vo.setMediaType(ImageConstant.ImageMediaType.valueOf(image.getMediaType()));
                                vo.setImageUuid(image.getUuid());
                                vo.setPrimaryStorageUuid(self.getUuid());
                                vo.setSize(image.getActualSize());
                                vo.setMd5sum("not calculated");
                                vo.setInstallUrl(primaryStorageInstallPath);
                                dbf.persist(vo);

                                logger.debug(String.format("downloaded image[uuid:%s, name:%s] to the image cache of aliyun nas primary storage[uuid: %s, installPath: %s]",
                                        image.getUuid(), image.getName(), self.getUuid(), primaryStorageInstallPath));

                                completion.success(primaryStorageInstallPath);
                                chain.next();
                            }
                        });

                        error(new FlowErrorHandler(completion, chain) {
                            @Override
                            public void handle(ErrorCode errCode, Map data) {
                                completion.fail(errCode);
                                chain.next();
                            }
                        });
                        }
                    }).start();
                }

                @Override
                public void run(final SyncTaskChain chain) {
                    String fullPath = Q.New(ImageCacheVO.class).eq(ImageCacheVO_.primaryStorageUuid, self.getUuid()).
                            eq(ImageCacheVO_.imageUuid, image.getUuid()).select(ImageCacheVO_.installUrl).findValue();
                    if (fullPath != null) {
                        CheckBitsCmd cmd = new CheckBitsCmd();
                        cmd.path = primaryStorageInstallPath;
                        httpCall(CHECK_BITS_PATH, hostUuid, cmd, CheckBitsRsp.class, new ReturnValueCompletion<CheckBitsRsp>(completion) {
                            @Override
                            public void success(CheckBitsRsp rsp) {
                                if (rsp.existing) {
                                    completion.success(primaryStorageInstallPath);
                                    chain.next();
                                    return;
                                }

                                // the image is removed on the host
                                // delete the cache object and re-download it
                                ImageCacheVO vo = Q.New(ImageCacheVO.class).eq(ImageCacheVO_.primaryStorageUuid, self.getUuid()).
                                        eq(ImageCacheVO_.imageUuid, image.getUuid()).find();

                                IncreasePrimaryStorageCapacityMsg imsg = new IncreasePrimaryStorageCapacityMsg();
                                imsg.setDiskSize(vo.getSize());
                                imsg.setPrimaryStorageUuid(vo.getPrimaryStorageUuid());
                                bus.makeTargetServiceIdByResourceUuid(imsg, PrimaryStorageConstant.SERVICE_ID, vo.getPrimaryStorageUuid());
                                bus.send(imsg);

                                dbf.remove(vo);
                                doDownload(chain);
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                                chain.next();
                            }
                        });
                    } else {
                        doDownload(chain);
                    }
                }

                @Override
                public String getName() {
                    return getSyncSignature();
                }
            });
        }
    }

    private BackupStorageKvmDownloader getBackupStorageKvmDownloader(String backupStorageUuid) {
        String bsType = Q.New(BackupStorageVO.class).eq(BackupStorageVO_.uuid, backupStorageUuid).select(BackupStorageVO_.type).findValue();

        for (BackupStorageKvmFactory f : pluginRgty.getExtensionList(BackupStorageKvmFactory.class)) {
            if (bsType.equals(f.getBackupStorageType())) {
                return f.createDownloader(getSelfInventory(), backupStorageUuid);
            }
        }
        throw new OperationFailureException(operr("cannot find any BackupStorageKvmFactory for the type[%s]", bsType));
    }

    @Override
    void handle(InstantiateVolumeOnPrimaryStorageMsg msg, ReturnValueCompletion<VolumeInventory> completion) {
        if (msg instanceof InstantiateRootVolumeFromTemplateOnPrimaryStorageMsg) {
            createRootVolume((InstantiateRootVolumeFromTemplateOnPrimaryStorageMsg) msg, completion);
        } else {
            createEmptyVolume(msg.getVolume(), msg.getDestHost().getUuid(), completion);
        }
    }

    private void createRootVolume(InstantiateRootVolumeFromTemplateOnPrimaryStorageMsg msg, final ReturnValueCompletion<VolumeInventory> completion) {
        final VmInstanceSpec.ImageSpec ispec = msg.getTemplateSpec();
        final ImageInventory image = ispec.getInventory();

        if (!ImageConstant.ImageMediaType.RootVolumeTemplate.toString().equals(image.getMediaType())) {
            createEmptyVolume(msg.getVolume(), msg.getDestHost().getUuid(), completion);
            return;
        }

        final VolumeInventory volume = msg.getVolume();
        final String hostUuid = msg.getDestHost().getUuid();

        String cachedPathInCache = AliyunNasPrimaryStoragePathMaker.makeCachedImageInstallPath(self, ispec.getInventory());
        String dataPathInCache = AliyunNasPrimaryStoragePathMaker.makeVolumeInstallPath(self, volume);

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("kvm-aliyun-nas-storage-create-root-volume-from-image-%s", image.getUuid()));
        chain.then(new NoRollbackFlow() {
            String __name__ = "download-image-to-cache";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                createMountDomainForImage(msg.getTemplateSpec().getInventory(), hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                    @Override
                    public void success(CreateMountDomainProperty property) {
                        ImageCache cache = new ImageCache();

                        cache.image = image;
                        cache.backupStorageUuid = ispec.getSelectedBackupStorage().getBackupStorageUuid();

                        cache.primaryStorageInstallPath = cachedPathInCache;
                        cache.backupStorageInstallPath = ispec.getSelectedBackupStorage().getInstallPath();
                        cache.download(hostUuid, new ReturnValueCompletion<String>(completion) {
                            @Override
                            public void success(String path) {
                                data.put("newCreate", property);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (data.get("newCreate") == null) {
                    trigger.rollback();
                    return;
                }

                CreateMountDomainProperty createImageCacheMount = (CreateMountDomainProperty)data.get("newCreate");

                if (createImageCacheMount.isNewCreate()) {
                    String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                    syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.rollback();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            logger.warn(errorCode.getDetails());
                            trigger.rollback();
                        }
                    });
                } else {
                    trigger.rollback();
                }
            }
        }).then(new Flow() {
            String __name__ = "create-template-from-cache";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                String __name__ = "create mount target if needed";
                createMountDomainForVolume(volume, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                    @Override
                    public void success(CreateMountDomainProperty property) {
                        data.put("newCreate", property);
                        String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeInstallPath(self, volume);
                        CreateVolumeFromCacheCmd cmd = new CreateVolumeFromCacheCmd();
                        cmd.installPath = dataPathInCache;
                        cmd.templatePathInCache = cachedPathInCache;
                        cmd.volumeUuid = volume.getUuid();
                        cmd.uuid = self.getUuid();
                        volume.setInstallPath(installPath);

                        httpCall(CREATE_VOLUME_FROM_CACHE_PATH, hostUuid, cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(trigger) {
                            @Override
                            public void success(AgentRsp rsp) {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (data.get("newCreate") == null) {
                    trigger.rollback();
                    return;
                }
                CreateMountDomainProperty createMount = (CreateMountDomainProperty)data.get("newCreate");
                if (createMount.isNewCreate()) {
                    String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                    syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new NopeCompletion());
                }
                trigger.rollback();
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success(volume);
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void createEmptyVolume(final VolumeInventory volume, String hostUuid, final ReturnValueCompletion<VolumeInventory> completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("create-empty-volume-%s", volume.getUuid());
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                createMountDomainForVolume(volume, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion) {
                    @Override
                    public void success(CreateMountDomainProperty property) {
                        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
                        chain.setName(String.format("mount-and-create-empty-volume-%s", volume.getUuid()));

                        chain.then(new NoRollbackFlow() {
                            String __name__ = "create empty volume";
                            @Override
                            public void run(FlowTrigger trigger, Map data) {
                                final CreateEmptyVolumeCmd cmd = new CreateEmptyVolumeCmd();
                                cmd.installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeInstallPath(self, volume);
                                cmd.size = volume.getSize();
                                cmd.volumeUuid = volume.getUuid();
                                cmd.uuid = self.getUuid();

                                httpCall(CREATE_EMPTY_VOLUME_PATH, hostUuid, cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
                                    @Override
                                    public void success(AgentRsp returnValue) {
                                        volume.setInstallPath(cmd.installPath);
                                        volume.setFormat(VolumeConstant.VOLUME_FORMAT_QCOW2);
                                        data.put("volume", volume);
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        trigger.fail(errorCode);
                                    }
                                });
                            }
                        }).done(new FlowDoneHandler(taskChain, completion) {
                            @Override
                            public void handle(Map data) {
                                VolumeInventory v = (VolumeInventory)data.get("volume");
                                completion.success(v);
                                taskChain.next();
                            }
                        }).error(new FlowErrorHandler(taskChain, completion) {
                            @Override
                            public void handle(ErrorCode errCode, Map data) {
                                completion.fail(errCode);
                                taskChain.next();
                            }
                        }).start();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        taskChain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private MountSourceType getMountSourceType(String type) {
        if (type.equals(VolumeType.Data.name())) {
            return MountSourceType.dataVolumes;
        } else {
            return MountSourceType.rootVolumes;
        }
    }

    private void mountDomainForImage(ImageInventory image, String hostUuid, String mountUuid, ReturnValueCompletion<CreateMountDomainProperty> completion) {
        NasMountTargetVO mvo = dbf.findByUuid(mountUuid, NasMountTargetVO.class);
        String url = AliyunNasPrimaryStoragePathMaker.getCachedImageUrl(self, mvo.getMountDomain(), image);
        String mountPath = AliyunNasPrimaryStoragePathMaker.makeCachedImageBaseDir(self, image);
        String dataPath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
        MountSpec spec = makeSpec(image.getUuid(), MountSourceType.imagecache, hostUuid, mvo.getUuid(),
                url, dataPath, mountPath);

        syncMount(spec, new Completion(completion) {
            @Override
            public void success() {
                CreateMountDomainProperty property = new CreateMountDomainProperty();
                property.setMountDomain(mvo.getMountDomain());
                property.setMountUuid(mvo.getUuid());
                completion.success(property);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    public void createMountDomainForImage(ImageInventory image, String hostUuid, ReturnValueCompletion<CreateMountDomainProperty> completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("create-mount-domain-for-image-%s-and-mount-on-host-%s", image.getUuid(), hostUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                // check if existed
                String mountUuid = Q.New(AliyunNasMountVolumeRefVO.class).eq(AliyunNasMountVolumeRefVO_.imageUuid, image.getUuid())
                        .eq(AliyunNasMountVolumeRefVO_.hostUuid, hostUuid).select(AliyunNasMountVolumeRefVO_.nasMountUuid).findValue();
                if (mountUuid == null) {
                    createMountTarget(accountUuid, hostUuid, new ReturnValueCompletion<String>(completion) {
                        @Override
                        public void success(String mountTargetUuid) {
                            mountDomainForImage(image, hostUuid, mountTargetUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    returnValue.setNewCreate(true);
                                    completion.success(returnValue);
                                    chain.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    deleteMountTarget(mountTargetUuid, new NopeCompletion());
                                    completion.fail(errorCode);
                                    chain.next();
                                }
                            });
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            AliyunNasMountVolumeRefVO mvo = Q.New(AliyunNasMountVolumeRefVO.class).
                                    eq(AliyunNasMountVolumeRefVO_.hostUuid, hostUuid).eq(AliyunNasMountVolumeRefVO_.imageUuid, image.getUuid()).find();
                            if (mvo != null) {
                                deleteMountTarget(mvo.getNasMountUuid(), new NopeCompletion());
                            }
                            completion.fail(errorCode);
                            chain.next();
                        }
                    });
                } else {
                    mountDomainForImage(image, hostUuid, mountUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion){
                        @Override
                        public void success(CreateMountDomainProperty returnValue) {
                            completion.success(returnValue);
                            chain.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            completion.fail(errorCode);
                            chain.next();
                        }
                    });
                }
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void mountDomainForVolume(VolumeInventory volume, String hostUuid, String mountUuid, ReturnValueCompletion<CreateMountDomainProperty> completion) {
        NasMountTargetVO mvo = dbf.findByUuid(mountUuid, NasMountTargetVO.class);
        String url = AliyunNasPrimaryStoragePathMaker.getVolumeBaseUrl(self, mvo.getMountDomain(), volume);
        String mountPath = AliyunNasPrimaryStoragePathMaker.makeVolumeBaseDir(self, volume);
        String dataPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
        MountSpec spec = makeSpec(volume, hostUuid, mvo.getUuid(),
                url, dataPath, mountPath);

        syncMount(spec, new Completion(completion) {
            @Override
            public void success() {
                CreateMountDomainProperty property = new CreateMountDomainProperty();
                property.setMountDomain(mvo.getMountDomain());
                property.setMountUuid(mvo.getUuid());
                completion.success(property);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    public void createMountDomainForVolume(VolumeInventory volume, String hostUuid, ReturnValueCompletion<CreateMountDomainProperty> completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("create-mount-domain-for-volume-%s-and-mount-on-host-%s", volume.getUuid(), hostUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                // check if it existed
                String mountUuid = Q.New(AliyunNasMountVolumeRefVO.class).eq(AliyunNasMountVolumeRefVO_.volumeUuid, volume.getUuid())
                        .eq(AliyunNasMountVolumeRefVO_.hostUuid, hostUuid).select(AliyunNasMountVolumeRefVO_.nasMountUuid).findValue();
                if (mountUuid == null) {
                    createMountTarget(accountUuid, hostUuid, new ReturnValueCompletion<String>(completion) {
                        @Override
                        public void success(String mountTargetUuid) {
                            mountDomainForVolume(volume, hostUuid, mountTargetUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    returnValue.setNewCreate(true);
                                    completion.success(returnValue);
                                    chain.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    deleteMountTarget(mountTargetUuid, new NopeCompletion());
                                    completion.fail(errorCode);
                                    chain.next();
                                }
                            });
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            AliyunNasMountVolumeRefVO mvo = Q.New(AliyunNasMountVolumeRefVO.class).
                                    eq(AliyunNasMountVolumeRefVO_.hostUuid, hostUuid).eq(AliyunNasMountVolumeRefVO_.volumeUuid, volume.getUuid()).find();
                            if (mvo != null) {
                                deleteMountTarget(mvo.getNasMountUuid(), new NopeCompletion());
                            }
                            completion.fail(errorCode);
                            chain.next();
                        }
                    });
                } else {
                    mountDomainForVolume(volume, hostUuid, mountUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion){
                        @Override
                        public void success(CreateMountDomainProperty returnValue) {
                            completion.success(returnValue);
                            chain.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            completion.fail(errorCode);
                            chain.next();
                        }
                    });
                }
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void getHostFromVolume(VolumeInventory vol, String checkUuid, ReturnValueCompletion<CreateMountDomainProperty> completion) {
        List<AliyunNasMountVolumeRefVO> refs = Q.New(AliyunNasMountVolumeRefVO.class).eq(AliyunNasMountVolumeRefVO_.volumeUuid, checkUuid).list();
        if (!refs.isEmpty()) {
            CreateMountDomainProperty property = new CreateMountDomainProperty();
            property.setHostUuid(refs.get(0).getHostUuid());
            completion.success(property);
        } else {
            String hostUuid = getAvailableHostFromVolume(vol);
            if (hostUuid == null) {
                completion.fail(operr("cannot find host to operate volume: [%s]", vol.getUuid()));
                return;
            }
            createMountDomainForVolume(vol, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion) {
                @Override
                public void success(CreateMountDomainProperty returnValue) {
                    returnValue.setHostUuid(hostUuid);
                    completion.success(returnValue);
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    completion.fail(errorCode);
                }
            });
        }
    }

    private void getHostFromVolumeSync(VolumeInventory vol, String checkUuid, ReturnValueCompletion<CreateMountDomainProperty> completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("get-host-for-operate-volume-%s", vol.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                getHostFromVolume(vol, checkUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion) {
                    @Override
                    public void success(CreateMountDomainProperty property) {
                        completion.success(property);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void getHostFromVolumeSync(VolumeInventory vol, ReturnValueCompletion<CreateMountDomainProperty> completion) {
        getHostFromVolumeSync(vol, vol.getUuid(), completion);
    }

    public String getAvailableHostFromVolume(VolumeInventory vol) {
        if (vol.getVmInstanceUuid() != null) {
            VmInstanceVO vvo = dbf.findByUuid(vol.getVmInstanceUuid(), VmInstanceVO.class);
            if (vvo.getHostUuid() != null) {
                return vvo.getHostUuid();
            }
        }
        String sql = "select h.uuid from HostVO h, AliyunNasMountVolumeRefVO ref where h.uuid = ref.hostUuid and h.status = :hstatus and ref.volumeUuid = :vuuid";
        List<String> hostUuids = SQL.New(sql).param("hstatus", HostStatus.Connected).param("vuuid", vol.getUuid()).list();
        if (hostUuids.size() > 0) {
            Collections.shuffle(hostUuids);
            return hostUuids.get(0);
        } else {
            return getAvailableHostUuidForOperation();
        }
    }

    @Override
    void handle(DeleteVolumeOnPrimaryStorageMsg msg, Completion completion) {
        String hostUuid = getAvailableHostFromVolume(msg.getVolume());
        deleteBits(msg.getVolume().getInstallPath(), hostUuid, new Completion(completion) {
            @Override
            public void success() {
                List<AliyunNasMountVolumeRefVO> refs = Q.New(AliyunNasMountVolumeRefVO.class).
                        eq(AliyunNasMountVolumeRefVO_.volumeUuid, msg.getVolume().getUuid()).list();
                if (refs.isEmpty()) {
                    completion.success();
                    return;
                }
                for (AliyunNasMountVolumeRefVO ref: refs) {
                    unmount(ref.getHostUuid(), ref.getNasMountUuid(), PathUtil.parentFolder(msg.getVolume().getInstallPath()), new NopeCompletion());
                }
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void deleteBits(String path, String hostUuid, final Completion completion) {
        deleteBits(path, false, hostUuid, completion);
    }

    @Override
    void handle(DeleteImageBitsOnPrimaryStorageMsg msg, Completion completion) {
        DeleteBitsCmd cmd = new DeleteBitsCmd();
        cmd.path = AliyunNasPrimaryStoragePathMaker.convertInstallPathToCommonPath(msg.getInstallPath());
        cmd.folder = msg.isFolder();
        cmd.uuid = self.getUuid();
        DebugUtils.Assert(msg.getHostUuid() != null, "HostUuid must be set here");
        httpCall(DELETE_BITS_PATH, msg.getHostUuid(), cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp rsp) {
                String mountUuid = Q.New(AliyunNasMountVolumeRefVO.class).select(AliyunNasMountVolumeRefVO_.nasMountUuid).
                        eq(AliyunNasMountVolumeRefVO_.hostUuid, msg.getHostUuid()).
                        eq(AliyunNasMountVolumeRefVO_.imageUuid, msg.getImageUuid()).findValue();
                if (mountUuid != null) {
                    syncUnmount(msg.getHostUuid(), mountUuid, msg.getInstallPath(), new Completion(completion) {
                        @Override
                        public void success() {
                            completion.success();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            completion.fail(errorCode);
                        }
                    });
                } else {
                    completion.success();
                }
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void deleteBits(String path, boolean folder, String hostUuid, final Completion completion) {
        DeleteBitsCmd cmd = new DeleteBitsCmd();
        cmd.path = AliyunNasPrimaryStoragePathMaker.convertInstallPathToCommonPath(path);
        cmd.folder = folder;
        cmd.uuid = self.getUuid();
        DebugUtils.Assert(hostUuid != null, String.format("no available host found for deleting bits: %s", cmd.path));
        httpCall(DELETE_BITS_PATH, hostUuid, cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp rsp) {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void handle(DeleteIsoFromPrimaryStorageMsg msg, Completion completion) {
        // The ISO is in the image cache, no need to delete it
        DeleteIsoFromPrimaryStorageReply reply = new DeleteIsoFromPrimaryStorageReply();
        bus.reply(msg, reply);
        completion.success();
    }

    @Override
    void handle(DownloadDataVolumeToPrimaryStorageMsg msg, ReturnValueCompletion<DownloadDataVolumeToPrimaryStorageReply> completion) {
        VolumeInventory vol = VolumeInventory.valueOf(dbf.findByUuid(msg.getVolumeUuid(), VolumeVO.class));
        String hostUuid = msg.getHostUuid() != null ? msg.getHostUuid() : getAvailableHostFromVolume(vol);
        createMountDomainForVolume(vol, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(completion) {
            @Override
            public void success(CreateMountDomainProperty property) {
                final String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeInstallPath(self, vol);
                BackupStorageKvmDownloader downloader = getBackupStorageKvmDownloader(msg.getBackupStorageRef().getBackupStorageUuid());
                downloader.downloadBits(msg.getBackupStorageRef().getInstallPath(), installPath, hostUuid, new Completion(completion) {
                    @Override
                    public void success() {
                        DownloadDataVolumeToPrimaryStorageReply reply = new DownloadDataVolumeToPrimaryStorageReply();
                        reply.setFormat(msg.getImage().getFormat());
                        reply.setInstallPath(installPath);
                        completion.success(reply);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void handle(DeleteBitsOnPrimaryStorageMsg msg, Completion completion) {
        deleteBits(msg.getInstallPath(), msg.isFolder(), msg.getHostUuid(), new Completion(completion) {
            @Override
            public void success() {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void handle(DeleteVolumeBitsOnPrimaryStorageMsg msg, Completion completion) {
        // no host indicated, we choose a ramdon host and use common dir
        String commonInstallPath = msg.getInstallPath();
        if (msg.getInstallPath().contains(AliyunNasPrimaryStoragePathMaker.dataPath)) {
            commonInstallPath = AliyunNasPrimaryStoragePathMaker.convertInstallPathToCommonPath(msg.getInstallPath());
        }
        String hostUuid = getAvailableHostUuidForOperation();
        deleteBits(commonInstallPath, msg.isFolder(), hostUuid, new Completion(completion) {
            @Override
            public void success() {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void syncVolumeSize(String volumeUuid, String installPath, ReturnValueCompletion<SyncVolumeSizeOnPrimaryStorageReply> completion) {
        String hostUuid = getAvailableHostUuidForOperation();
        if (hostUuid == null) {
            completion.fail(operr("cannot find and host to sync volume size in primary: %s", self.getUuid()));
            return;
        }
        final GetVolumeSizeCmd cmd = new GetVolumeSizeCmd();
        cmd.installPath = AliyunNasPrimaryStoragePathMaker.convertInstallPathToCommonPath(installPath);
        cmd.volumeUuid = volumeUuid;
        cmd.uuid = self.getUuid();
        httpCall(GET_VOLUME_SIZE_PATH, hostUuid, cmd, GetVolumeSizeRsp.class, new ReturnValueCompletion<GetVolumeSizeRsp>(completion){
            @Override
            public void success(GetVolumeSizeRsp returnValue) {
                SyncVolumeSizeOnPrimaryStorageReply reply = new SyncVolumeSizeOnPrimaryStorageReply();
                reply.setActualSize(returnValue.actualSize);
                reply.setSize(returnValue.size);
                completion.success(reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void handle(ReInitRootVolumeFromTemplateOnPrimaryStorageMsg msg, ReturnValueCompletion<String> completion) {
        ImageVO ivo = dbf.findByUuid(msg.getVolume().getRootImageUuid(), ImageVO.class);
        if (ivo == null) {
            completion.fail(operr("image [%s] has been deleted, cannot reinit root volume from it"));
            return;
        }
        ImageInventory image = ImageInventory.valueOf(ivo);

        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("reinit image on aliyun nas for volume: %s", msg.getVolume().getUuid()));

        chain.then(new ShareFlow() {
            CreateMountDomainProperty createMount;
            CreateMountDomainProperty createImageCacheMount;
            String hostUuid;

            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "find-host-to-reinit-rootvolume";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        getHostFromVolumeSync(msg.getVolume(), new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                hostUuid = returnValue.getHostUuid();
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, msg.getVolume());
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-imagecache-mount-to-reinit-rootvolume-if-needed";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (image != null) {
                            createMountDomainForImage(image, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    createImageCacheMount = returnValue;
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createImageCacheMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath,  new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "reinit-volume";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        ReInitCmd cmd = new ReInitCmd();
                        ImageInventory image = ImageInventory.valueOf(dbf.findByUuid(msg.getVolume().getRootImageUuid(), ImageVO.class));
                        cmd.imagePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageInstallPath(self, image);
                        cmd.volumePath = AliyunNasPrimaryStoragePathMaker.makeVolumeInstallPath(self, msg.getVolume());
                        cmd.uuid = self.getUuid();
                        httpCall(REINIT_VOLUME_PATH, hostUuid, cmd, ReinitImageRsp.class, new ReturnValueCompletion<ReinitImageRsp>(completion) {
                            @Override
                            public void success(ReinitImageRsp rsp) {
                                data.put("volumePath", rsp.newVolumeInstallPath);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        String volumePath = (String)data.get("volumePath");
                        completion.success(volumePath);
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    public void isMounted(String url, String mountPath, final ReturnValueCompletion<Boolean> completion) {
        String hostUuid = getAvailableHostUuidForOperation();
        if (hostUuid == null) {
            throw new OperationFailureException(operr("no available host could check mountPath!"));
        }
        ismount(url, mountPath, hostUuid, completion);
    }

    private void makeSureVolumeMountTarget(String url, String mountPath, String dataPath, String hostUuid, Completion completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("mount-target-dataPath-%s-on-host-%s", dataPath, hostUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                mount(url, mountPath, dataPath, hostUuid, true, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    public void syncMount(MountSpec spec, Completion completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("mount-target-dataPath-%s-on-host-%s", spec.getDataPath(), spec.getHostUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                updateMountTargetCount(spec.getMountUuid(), true);
                mount(spec.getUrl(), spec.getMountPath(), spec.getDataPath(), spec.getHostUuid(), new Completion(completion) {
                    @Override
                    public void success() {
                        if (!Q.New(AliyunNasMountVolumeRefVO.class).eq(AliyunNasMountVolumeRefVO_.nasMountUuid, spec.getMountUuid()).
                                eq(AliyunNasMountVolumeRefVO_.hostUuid, spec.getHostUuid()).eq(AliyunNasMountVolumeRefVO_.sourceType, spec.getSourceType())
                                .isExists()) {
                            AliyunNasMountVolumeRefVO rvo = new AliyunNasMountVolumeRefVO();
                            rvo.setNasMountUuid(spec.getMountUuid());
                            rvo.setSourceType(spec.getSourceType());
                            rvo.setDataPath(spec.getDataPath());
                            if (spec.getSourceType() == MountSourceType.imagecache) {
                                rvo.setImageUuid(spec.getSourceUuid());
                            } else {
                                rvo.setVolumeUuid(spec.getSourceUuid());
                            }
                            rvo.setHostUuid(spec.getHostUuid());
                            dbf.persistAndRefresh(rvo);
                        }
                        completion.success();
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    public void syncUnmount(String hostUuid, String mountUuid, String mountPath, Completion completion) {
        syncUnmount(hostUuid, mountUuid, mountPath, true, completion);
    }

    public void syncUnmount(String hostUuid, String mountUuid, String mountPath, boolean deleteMount, Completion completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("unmount-target-dataPath-%s-on-host-%s", mountPath, hostUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                int count = updateMountTargetCount(mountUuid, false);
                if (count > 0) {
                    logger.debug("can not unmount it because it was still been used.");
                    completion.success();
                    chain.next();
                    return;
                }
                unmount(hostUuid, mountUuid, mountPath, deleteMount, new Completion(completion) {
                    @Override
                    public void success() {
                        List<AliyunNasMountVolumeRefVO> refs = Q.New(AliyunNasMountVolumeRefVO.class).eq(AliyunNasMountVolumeRefVO_.hostUuid, hostUuid).eq(AliyunNasMountVolumeRefVO_.nasMountUuid, mountUuid).list();
                        if (!refs.isEmpty()) {
                            refs.forEach(ref -> dbf.remove(ref));
                        }
                        completion.success();
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void CopySystemTagAfterCommitIfNeeded(ImageInventory iinv, VolumeInventory vol) {
        if (VolumeType.Root.name().equals(vol.getType())) {
            if (ImageSystemTags.IMAGE_INJECT_QEMUGA.hasTag(vol.getRootImageUuid())
                    || VmSystemTags.VM_INJECT_QEMUGA.hasTag(vol.getVmInstanceUuid())) {
                SystemTagCreator creator = ImageSystemTags.IMAGE_INJECT_QEMUGA.newSystemTagCreator(iinv.getUuid());
                creator.inherent = false;
                creator.recreate = true;
                creator.create();
            }
        }
    }

    private BackupStorageInventory selectBackupStorage(VolumeInventory vol, long requiredSize) {
        return selectBackupStorage(vol, requiredSize, null);
    }

    private BackupStorageInventory selectBackupStorage(VolumeInventory vol, long requiredSize, List<String> requiredTypes) {
        for (CommitImageBackupStorageSelector s : pluginRgty.getExtensionList(CommitImageBackupStorageSelector.class)) {
            BackupStorageInventory bs = s.selectWithVolume(vol, requiredSize);
            if (bs == null || (requiredTypes != null && !requiredTypes.contains(bs.getType()))) {
                continue;
            }

            if (bs.getType().equals(ImageStoreBackupStorageConstant.IMAGE_STORE_BACKUP_STORAGE_TYPE)) {
                return bs;
            }

            logger.info(String.format("Root image for volume [%s] is not image store", vol.getUuid()));
        }

        return null;
    }

    private void commitVolume(final CommitVolumeAsImageMsg msg, final ReturnValueCompletion<String> completion) {
        final TaskProgressRange parentStage = getTaskStage();
        reportProgress(parentStage.getStart().toString());

        VolumeInventory volume = VolumeInventory.valueOf(dbf.findByUuid(msg.getVolumeUuid(), VolumeVO.class));
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-template-from-%s-volume-%s", volume.getType().toLowerCase(), volume.getUuid()));
        chain.then(new ShareFlow() {
            ImageVO imageVO;
            Long imageEstimateSize;
            List<BackupStorageInventory> backupStorages = new ArrayList<>();

            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = "get-volume-actual-size";

                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        EstimateVolumeTemplateSizeMsg msg = new EstimateVolumeTemplateSizeMsg();
                        msg.setVolumeUuid(volume.getUuid());
                        bus.makeTargetServiceIdByResourceUuid(msg, VolumeConstant.SERVICE_ID, volume.getPrimaryStorageUuid());
                        bus.send(msg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                EstimateVolumeTemplateSizeReply sr = reply.castReply();
                                imageEstimateSize = sr.getActualSize();
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new Flow() {
                    String __name__ = String.format("find-storage-to-commit-image-for-volume-%s", msg.getVolumeUuid());

                    void allocateSpecifiedBackupStorage(FlowTrigger trigger) {
                        ErrorCodeList errorCodes = new ErrorCodeList();
                        new While<>(msg.getBackupStorageUuids()).all((backupStorageUuid, completion) -> {
                            AllocateBackupStorageMsg abmsg = new AllocateBackupStorageMsg();
                            abmsg.setSize(imageEstimateSize);
                            abmsg.setBackupStorageUuid(backupStorageUuid);
                            bus.makeLocalServiceId(abmsg, BackupStorageConstant.SERVICE_ID);

                            bus.send(abmsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (reply.isSuccess()) {
                                        backupStorages.add(((AllocateBackupStorageReply) reply).getInventory());
                                    } else {
                                        errorCodes.getCauses().add(reply.getError());
                                    }
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                if (errorCodes.getCauses().isEmpty()) {
                                    trigger.next();
                                    return;
                                }

                                ErrorCode ec = operr("unable to allocate backup storage specified by uuids: %s, becasue: %s",
                                        String.join(",", msg.getBackupStorageUuids()), errorCodes.getCauses().get(0).getDetails()).causedBy(errorCodes);
                                trigger.fail(ec);
                            }
                        });
                    }

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        // If this message is called indirectly by APICloneVmInstanceMsg, we do not have
                        // backup storage uuid.  If APICreateRootVolumeTemplateFromRootVolumeMsg is called
                        // directly, its backup storage uuid field will be validated by the validator and
                        // can't be null (and in this case it will fail early before reaching here).
                        if (msg.getBackupStorageUuids() != null && !msg.getBackupStorageUuids().isEmpty()) {
                            allocateSpecifiedBackupStorage(trigger);
                            return;
                        }

                        final BackupStorageInventory bsinv = selectBackupStorage(volume, imageEstimateSize);

                        if (bsinv == null) {
                            trigger.fail(err(SysErrors.RESOURCE_NOT_FOUND, "No backup storage to commit volume [uuid: %s]", msg.getVolumeUuid()));
                            return;
                        }

                        AllocateBackupStorageMsg abmsg = new AllocateBackupStorageMsg();
                        abmsg.setSize(imageEstimateSize);
                        abmsg.setBackupStorageUuid(bsinv.getUuid());
                        bus.makeLocalServiceId(abmsg, BackupStorageConstant.SERVICE_ID);

                        bus.send(abmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                backupStorages.add(((AllocateBackupStorageReply) reply).getInventory());
                                msg.setBackupStorageUuids(Collections.singletonList(bsinv.getUuid()));
                                trigger.next();
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (backupStorages.isEmpty()) {
                            trigger.rollback();
                            return;
                        }

                        List<ReturnBackupStorageMsg> rmsgs = CollectionUtils.transformToList(backupStorages, new Function<ReturnBackupStorageMsg, BackupStorageInventory>() {
                            @Override
                            public ReturnBackupStorageMsg call(BackupStorageInventory arg) {
                                ReturnBackupStorageMsg rmsg = new ReturnBackupStorageMsg();
                                rmsg.setBackupStorageUuid(arg.getUuid());
                                rmsg.setSize(imageEstimateSize);
                                bus.makeLocalServiceId(rmsg, BackupStorageConstant.SERVICE_ID);
                                return rmsg;
                            }
                        });

                        new While<>(rmsgs).all((msg, completion) -> {
                            bus.send(msg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    logger.warn(String.format("failed to return %s bytes to backup storage[uuid:%s]", imageEstimateSize, msg.getBackupStorageUuid()));
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                trigger.rollback();
                            }
                        });
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-image-in-database";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        imageVO = imageMgr.createImageInDb(msg, imvo -> {
                            imvo.setFormat(volume.getFormat());
                            imvo.setUrl(String.format("volume://%s", msg.getVolumeUuid()));
                        });

                        backupStorages.forEach(bs -> imageExtEmitter.beforeCreateImage(
                                ImageInventory.valueOf(imageVO), bs.getUuid(), msg.getPrimaryStorageUuid()
                        ));

                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (imageVO != null) {
                            dbf.remove(imageVO);
                        }
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "commit-volume-as-image-on-primary-storage";

                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        List<ErrorCode> errorCodes = new ArrayList<ErrorCode>();
                        new LoopAsyncBatch<String>(trigger) {
                            @Override
                            protected Collection<String> collect() {
                                return backupStorages.stream().map(BackupStorageInventory::getUuid).collect(Collectors.toList());
                            }

                            @Override
                            protected AsyncBatchRunner forEach(String backupStorageUuid) {
                                return new AsyncBatchRunner() {
                                    @Override
                                    public void run(NoErrorCompletion completion) {
                                        BackupStorageVO bsvo = dbf.findByUuid(backupStorageUuid, BackupStorageVO.class);
                                        Completion comp = new Completion(completion) {
                                            @Override
                                            public void success() {
                                                imageVO = dbf.reload(imageVO);
                                                completion.done();
                                            }

                                            @Override
                                            public void fail(ErrorCode errorCode) {
                                                synchronized (errorCodes) {
                                                    errorCodes.add(errorCode);
                                                    completion.done();
                                                }
                                            }
                                        };
                                        if (bsvo.getType().equals(ImageStoreBackupStorageConstant.IMAGE_STORE_BACKUP_STORAGE_TYPE)) {
                                            doCommit(backupStorageUuid, volume.getUuid(), imageVO, comp);
                                        } else {
                                            comp.fail(operr("aliyun nas primarystorage only support imagestore bs, actually get type: %s", bsvo.getType()));
                                        }
                                    }
                                };
                            }

                            @Override
                            protected void done() {
                                if (backupStorages.size() == errorCodes.size()) {
                                    ErrorCode ec = operr(new ErrorCodeList().causedBy(errorCodes), "unable to commit backup storage specified by uuids: %s, becasue: %s",
                                            String.join(",", backupStorages.stream().map(BackupStorageInventory::getUuid).collect(Collectors.toList())),
                                            errorCodes.get(0).getDetails());
                                    trigger.fail(ec);
                                } else {
                                    trigger.next();
                                }
                            }
                        }.start();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "sync-image-size";

                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        TaskProgressRange stage = markTaskStage(parentStage, COMMIT_VOLUME_IMAGE_SYNC_SIZE_STAGE);
                        new While<>(backupStorages).all((bs, completion) -> {
                            SyncImageSizeMsg smsg = new SyncImageSizeMsg();
                            smsg.setBackupStorageUuid(bs.getUuid());
                            smsg.setImageUuid(imageVO.getUuid());
                            bus.makeTargetServiceIdByResourceUuid(smsg, ImageConstant.SERVICE_ID, imageVO.getUuid());
                            bus.send(smsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply reply) {
                                    completion.done();
                                }
                            });
                        }).run(new WhileDoneCompletion(trigger) {
                            @Override
                            public void done(ErrorCodeList errorCodeList) {
                                reportProgress(stage.getEnd().toString());
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "copy-system-tag-to-image";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        SyncSystemTagFromVolumeMsg smsg = new SyncSystemTagFromVolumeMsg();
                        smsg.setImageUuid(imageVO.getUuid());
                        smsg.setVolumeUuid(msg.getVolumeUuid());
                        bus.makeLocalServiceId(smsg, ImageConstant.SERVICE_ID);
                        bus.send(smsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    logger.warn(String.format("sync image[uuid:%s]system tag fail", msg.getVolumeUuid()));
                                }
                                trigger.next();
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        reportProgress(parentStage.getEnd().toString());
                        completion.success(imageVO.getUuid());
                    }
                });

                error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private ImageBackupStorageRefVO persistRefVOByBsInventory(String bsUuid, String imageUuid) {
        ImageBackupStorageRefVO ref = new ImageBackupStorageRefVO();
        ref.setBackupStorageUuid(bsUuid);
        ref.setStatus(ImageStatus.Creating);
        ref.setImageUuid(imageUuid);
        ref.setInstallPath("");
        return dbf.persistAndRefresh(ref);
    }

    private void doCommit(String backupStorageUuid, String volumeUuid, final ImageVO imageVO, Completion completion) {
        CommitVolumeAsImageOnPrimaryStorageMsg abmsg = new CommitVolumeAsImageOnPrimaryStorageMsg();
        abmsg.setPrimaryStorageUuid(self.getUuid());
        abmsg.setVolumeUuid(volumeUuid);
        abmsg.setBackupStorageUuid(backupStorageUuid);
        abmsg.setImageUuid(imageVO.getUuid());
        ImageBackupStorageRefVO ref = persistRefVOByBsInventory(backupStorageUuid,imageVO.getUuid());
        // send the message to primary storage backend for processing
        bus.makeTargetServiceIdByResourceUuid(abmsg, PrimaryStorageConstant.SERVICE_ID, self.getUuid());
        bus.send(abmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (dbf.reload(imageVO) == null) {
                    SQL.New("delete from ImageBackupStorageRefVO where imageUuid = :uuid")
                            .param("uuid", imageVO.getUuid())
                            .execute();
                    completion.fail(operr("image [uuid:%s] has been deleted", imageVO.getUuid()));
                    return;
                }

                if (!reply.isSuccess()) {
                    dbf.remove(ref);
                    completion.fail(reply.getError());
                    return;
                }

                CommitVolumeAsImageOnPrimaryStorageReply r = reply.castReply();

                ref.setInstallPath(r.getBackupStorageInstallPath());
                ref.setStatus(ImageStatus.Ready);
                dbf.update(ref);

                imageVO.setStatus(ImageStatus.Ready);
                imageVO.setActualSize(r.getActualSize());
                imageVO.setSize(r.getSize());
                if (r.getFormat() != null) {
                    imageVO.setFormat(r.getFormat());
                }
                ImageInventory inv = ImageInventory.valueOf(dbf.updateAndRefresh(imageVO));

                imageExtEmitter.afterCreateImage(inv);

                completion.success();
            }
        });
    }

    @Override
    void handle(SelectBackupStorageMsg msg, ReturnValueCompletion<SelectBackupStorageReply> completion) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("select-bs-for-volume-%s-to-commit-image", msg.getVolumeUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                SelectBackupStorageReply reply = new SelectBackupStorageReply();
                VolumeInventory volume = VolumeInventory.valueOf(dbf.findByUuid(msg.getVolumeUuid(), VolumeVO.class));

                reply.setInventory(selectBackupStorage(volume, msg.getRequiredSize(), msg.getRequiredBackupStorageTypes()));
                bus.reply(msg, reply);
                chain.next();
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    @Override
    void handle(final CommitVolumeAsImageMsg msg, final ReturnValueCompletion<CommitVolumeAsImageReply> completion) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("commit-volume-%s-as-image-on-aliyun-nas-primarystorage", msg.getVolumeUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                CommitVolumeAsImageReply reply = new CommitVolumeAsImageReply();
                commitVolume(msg, new ReturnValueCompletion<String>(chain) {
                    @Override
                    public void success(String imageUuid) {
                        ImageInventory image = ImageInventory.valueOf(dbf.findByUuid(imageUuid, ImageVO.class));
                        reply.setInventory(image);
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void commitSnapshot(final CommitVolumeAsImageOnPrimaryStorageMsg msg, final String installPath, String hostUuid, final ReturnValueCompletion<CommitVolumeAsImageRsp> completion) {
        ImageInventory inv = ImageInventory.valueOf(dbf.findByUuid(msg.getImageUuid(), ImageVO.class));

        // get the hostname of the backup storage
        SimpleQuery<ImageStoreBackupStorageVO> q2 = this.dbf.createQuery(ImageStoreBackupStorageVO.class);
        q2.select(ImageStoreBackupStorageVO_.hostname);
        q2.add(ImageStoreBackupStorageVO_.uuid, SimpleQuery.Op.EQ, msg.getBackupStorageUuid());
        String hostname = q2.findValue();

        CommitVolumeAsImageCmd cmd = new CommitVolumeAsImageCmd();
        cmd.primaryStorageInstallPath = installPath;
        cmd.hostname = hostname;
        cmd.imageUuid = msg.getImageUuid();
        cmd.uuid = self.getUuid();

        StringBuilder desc = new StringBuilder();
        for (CreateImageExtensionPoint ext : pluginRgty.getExtensionList(CreateImageExtensionPoint.class)) {
            String tmp = ext.getImageDescription(inv);
            if (tmp != null && !tmp.trim().equals("")) {
                desc.append(tmp);
            }
        }
        cmd.description = desc.toString();

        // send the command to agent
        this.httpCall(COMMIT_PATH, hostUuid, cmd, CommitVolumeAsImageRsp.class, new ReturnValueCompletion<CommitVolumeAsImageRsp>(completion) {
            @Override
            public void success(CommitVolumeAsImageRsp rsp) {
                completion.success(rsp);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void handle(final CommitVolumeAsImageOnPrimaryStorageMsg msg, final ReturnValueCompletion<CommitVolumeAsImageOnPrimaryStorageReply> completion) {
        // For image store, we just need to push the image to store.
        final TaskProgressRange parentStage = getTaskStage();

        final CommitVolumeAsImageOnPrimaryStorageReply reply = new CommitVolumeAsImageOnPrimaryStorageReply();
        reply.setBackupStorageUuid(msg.getBackupStorageUuid());

        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("commit-volume-%s-as-image", msg.getVolumeUuid()));
        chain.then(new ShareFlow() {
            String backupStorageInstallPath;
            VolumeCreateSnapshotReply vsReply;
            CommitVolumeAsImageRsp cvRsp;

            String hostUuid;
            CreateMountDomainProperty createMount;
            CreateMountDomainProperty createImageCacheMount;
            VolumeInventory vol = VolumeInventory.valueOf(dbf.findByUuid(msg.getVolumeUuid(), VolumeVO.class));
            ImageInventory image = ImageInventory.valueOf(dbf.findByUuid(msg.getImageUuid(), ImageVO.class));

            // The flow logic:
            // 1. Create a live snapshot (d2) of current image (d1)
            // 2. Push d1 to image store.
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "find-host-to-commit-volume";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        getHostFromVolumeSync(vol, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                hostUuid = returnValue.getHostUuid();
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, vol);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "take-live-snapshot-of-current-image";

                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        TaskProgressRange stage = markTaskStage(parentStage, COMMIT_VOLUME_IMAGE_CREATE_SNAPSHOT_STAGE);
                        VolumeCreateSnapshotMsg cmsg = new VolumeCreateSnapshotMsg();
                        String volUuid = msg.getVolumeUuid();
                        cmsg.setVolumeUuid(volUuid);
                        cmsg.setName("Snapshot-" + volUuid);
                        cmsg.setDescription("Take snapshot for " + volUuid);
                        cmsg.setAccountUuid(acmgr.getOwnerAccountUuidOfResource(volUuid));

                        bus.makeTargetServiceIdByResourceUuid(cmsg, VolumeConstant.SERVICE_ID, volUuid);
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                vsReply = reply.castReply();
                                reportProgress(stage.getEnd().toString());
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-imagecache-mount-if-needed-for-commit-volume-as-image";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (image != null) {
                            createMountDomainForImage(image, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    createImageCacheMount = returnValue;
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createImageCacheMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "commit-snapshot-to-local-image-registry";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        TaskProgressRange stage = markTaskStage(parentStage, COMMIT_VOLUME_IMAGE_COMMIT_SNAPSHOT_STAGE);
                        commitSnapshot(msg, vsReply.getInventory().getPrimaryStorageInstallPath(), hostUuid, new ReturnValueCompletion<CommitVolumeAsImageRsp>(trigger) {
                            @Override
                            public void success(CommitVolumeAsImageRsp returnValue) {
                                cvRsp = returnValue;
                                reportProgress(stage.getEnd().toString());
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "upload-template-to-backup-storage";

                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        TaskProgressRange stage = markTaskStage(parentStage, COMMIT_VOLUME_IMAGE_UPLOAD_TEMPLATE_STAGE);
                        ImageStoreBackupStorageKvmUploader uploader = ImageStoreBackupStorageKvmUploader.createUploader(getSelfInventory(), msg.getBackupStorageUuid());
                        String primaryStorageInstallPath = vsReply.getInventory().getPrimaryStorageInstallPath();
                        uploader.uploadBits(msg.getImageUuid(), backupStorageInstallPath, primaryStorageInstallPath, hostUuid, new ReturnValueCompletion<String>(trigger) {
                            @Override
                            public void success(String installPath) {
                                backupStorageInstallPath = installPath;
                                reportProgress(stage.getEnd().toString());
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createMount != null && createMount.isNewCreate()) {
                            syncUnmount(hostUuid, createMount.getMountUuid(), vol.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        reply.setBackupStorageInstallPath(backupStorageInstallPath);
                        reply.setFormat(vsReply.getInventory().getFormat());
                        reply.setSize(cvRsp.size);
                        reply.setActualSize(cvRsp.actualSize);
                        bus.reply(msg, reply);
                    }
                });

                error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        reply.setError(errCode);
                        bus.reply(msg, reply);
                    }
                });
            }
        }).start();
    }

    @Override
    void handle(final ResizeVolumeOnPrimaryStorageMsg msg, final ReturnValueCompletion<ResizeVolumeOnPrimaryStorageReply> completion) {
        VolumeInventory volume = msg.getVolume();
        final ResizeVolumeOnPrimaryStorageReply reply = new ResizeVolumeOnPrimaryStorageReply();
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("resize-volume-%s", msg.getVolume().getUuid()));

        chain.then(new ShareFlow() {
            CreateMountDomainProperty createMount;
            String hostUuid;

            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "find-host-to-resize";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        getHostFromVolumeSync(volume, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                hostUuid = returnValue.getHostUuid();
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name_ = "resize-volume";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        ResizeVolumeCmd cmd = new ResizeVolumeCmd();
                        cmd.installPath = volume.getInstallPath();
                        cmd.size = msg.getSize();
                        cmd.uuid = self.getUuid();
                        httpCall(RESIZE_VOLUME_PATH, hostUuid, cmd, ResizeVolumeRsp.class, new ReturnValueCompletion<ResizeVolumeRsp>(msg) {
                            @Override
                            public void success(ResizeVolumeRsp rsp) {
                                volume.setSize(rsp.size);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });


                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createMount != null && createMount.isNewCreate()) {
                            syncUnmount(hostUuid, createMount.getMountUuid(), volume.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        reply.setVolume(volume);
                        completion.success(reply);
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private String makeSnapshotInstallPath(VolumeInventory vol, VolumeSnapshotInventory snapshot) {
        String volPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, vol);
        File volDir = new File(volPath);
        return PathUtil.join(volDir.getAbsolutePath(), "snapshots", String.format("%s.qcow2", snapshot.getUuid()));
    }

    synchronized private int updateMountTargetCount(String mountUuid, boolean increase) {
        String old = AliyunNasSystemTags.MOUNT_TARGET_COUNTER.getTokenByResourceUuid(mountUuid, AliyunNasSystemTags.MOUNT_TARGET_COUNTER_TOKEN);
        if (old == null) {
            if (increase) {
                logger.debug(String.format("initialize MOUNT_TARGET_COUNTER for mount target: %s", mountUuid));
                SystemTagCreator creator = AliyunNasSystemTags.MOUNT_TARGET_COUNTER.newSystemTagCreator(mountUuid);
                creator.setTagByTokens(map(e(AliyunNasSystemTags.MOUNT_TARGET_COUNTER_TOKEN, "1")));
                creator.inherent = true;
                creator.recreate = true;
                creator.create();
                return 1;
            } else {
                return 0;
            }
        }

        int value = Integer.valueOf(old);
        if (increase) {
            value ++;
            AliyunNasSystemTags.MOUNT_TARGET_COUNTER.update(mountUuid, AliyunNasSystemTags.MOUNT_TARGET_COUNTER.instantiateTag(
                    map(e(AliyunNasSystemTags.MOUNT_TARGET_COUNTER_TOKEN, String.valueOf(value)))));
        } else {
            if (value <= 0) {
                return 0;
            } else {
                value --;
                AliyunNasSystemTags.MOUNT_TARGET_COUNTER.update(mountUuid, AliyunNasSystemTags.MOUNT_TARGET_COUNTER.instantiateTag(
                        map(e(AliyunNasSystemTags.MOUNT_TARGET_COUNTER_TOKEN, String.valueOf(value)))));
            }
        }
        return value;
    }

    @Override
    void handle(TakeSnapshotMsg msg, ReturnValueCompletion<TakeSnapshotReply> completion) {
        final VolumeSnapshotInventory sp = msg.getStruct().getCurrent();
        VolumeInventory vol = VolumeInventory.valueOf(dbf.findByUuid(sp.getVolumeUuid(), VolumeVO.class));
        ImageInventory img = null;
        if (vol.getRootImageUuid() != null) {
            ImageVO ivo = dbf.findByUuid(vol.getRootImageUuid(), ImageVO.class);
            if (ivo != null) {
                //TODO: support image deleted here
                img = ImageInventory.valueOf(ivo);
            }
        }
        final ImageInventory image = img;


        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("take-snapshot-from-volume-%s", vol.getUuid()));

        chain.then(new ShareFlow() {
            TakeSnapshotReply ret = new TakeSnapshotReply();
            CreateMountDomainProperty createMount;
            String hostUuid;
            CreateMountDomainProperty createImageCacheMount;
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "find-host";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (vol.getVmInstanceUuid() != null) {
                            VmInstanceVO vm = dbf.findByUuid(vol.getVmInstanceUuid(), VmInstanceVO.class);
                            if (vm.getHostUuid() != null) {
                                hostUuid = vm.getHostUuid();
                                trigger.next();
                                return;
                            }
                        }
                        getHostFromVolumeSync(vol, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                hostUuid = returnValue.getHostUuid();
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, vol);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-imagecache-mount-if-needed";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (image != null) {
                            createMountDomainForImage(image, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    createImageCacheMount = returnValue;
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createImageCacheMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "take-snapshot-on-hypervisor";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        TakeSnapshotOnHypervisorMsg hmsg = new TakeSnapshotOnHypervisorMsg();
                        hmsg.setHostUuid(hostUuid);
                        hmsg.setVmUuid(vol.getVmInstanceUuid());
                        hmsg.setVolume(vol);
                        hmsg.setSnapshotName(msg.getStruct().getCurrent().getUuid());
                        hmsg.setFullSnapshot(msg.getStruct().isFullSnapshot());
                        String installPath = makeSnapshotInstallPath(vol, sp);
                        hmsg.setInstallPath(installPath);
                        bus.makeTargetServiceIdByResourceUuid(hmsg, HostConstant.SERVICE_ID, hostUuid);
                        bus.send(hmsg, new CloudBusCallBack(completion) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                TakeSnapshotOnHypervisorReply treply = (TakeSnapshotOnHypervisorReply) reply;
                                sp.setSize(treply.getSize());
                                sp.setPrimaryStorageUuid(self.getUuid());
                                sp.setPrimaryStorageInstallPath(treply.getSnapshotInstallPath());
                                sp.setType(VolumeSnapshotConstant.HYPERVISOR_SNAPSHOT_TYPE.toString());

                                ret.setNewVolumeInstallPath(treply.getNewVolumeInstallPath());
                                ret.setInventory(sp);
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createMount != null && createMount.isNewCreate()) {
                            syncUnmount(hostUuid, createMount.getMountUuid(), vol.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-image-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createImageCacheMount != null && createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success(ret);
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private void deleteSnapshot(final DeleteSnapshotOnPrimaryStorageMsg msg, Completion completion) {
        VolumeInventory vol = VolumeInventory.valueOf(dbf.findByUuid(msg.getSnapshot().getVolumeUuid(), VolumeVO.class));
        String hostUuid = getAvailableHostFromVolume(vol);

        deleteBits(msg.getSnapshot().getPrimaryStorageInstallPath(), hostUuid, new Completion(completion) {
            @Override
            public void success() {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    void handle(DeleteSnapshotOnPrimaryStorageMsg msg, Completion completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("delete-snapshot-%s-on-primarystorage-%s", msg.getSnapshot().getUuid(), msg.getPrimaryStorageUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                deleteSnapshot(msg, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    @Override
    void handle(CreateTemplateFromVolumeOnPrimaryStorageMsg msg, ReturnValueCompletion<CreateTemplateFromVolumeOnPrimaryStorageReply> completion) {
        final CreateTemplateFromVolumeOnPrimaryStorageReply reply = new CreateTemplateFromVolumeOnPrimaryStorageReply();
        final VolumeInventory volume = msg.getVolumeInventory();
        final ImageInventory image = msg.getImageInventory();

        final TaskProgressRange parentStage = getTaskStage();
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-template-%s-from-volume-%s", image.getUuid(), volume.getUuid()));
        chain.then(new ShareFlow() {
            String temporaryTemplatePath = AliyunNasPrimaryStoragePathMaker.makeTemplateFromVolumeInWorkspacePath(self, msg.getImageInventory().getUuid());
            String backupStorageInstallPath;
            String hostUuid;
            CreateMountDomainProperty createMount;
            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
            CreateMountDomainProperty createImageCacheMount;

            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "find-host";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        getHostFromVolumeSync(volume, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                hostUuid = returnValue.getHostUuid();
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-imagecache-mount-if-needed";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        createMountDomainForImage(image, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                createImageCacheMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createImageCacheMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-temporary-template";

                    boolean success = false;

                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        TaskProgressRange stage = markTaskStage(parentStage, CREATE_ROOT_VOLUME_TEMPLATE_CREATE_TEMPORARY_TEMPLATE_STAGE);
                        CreateTemplateFromVolumeCmd cmd = new CreateTemplateFromVolumeCmd();
                        cmd.volumePath = volume.getInstallPath();
                        cmd.installPath = temporaryTemplatePath;
                        cmd.uuid = self.getUuid();
                        httpCall(CREATE_TEMPLATE_FROM_VOLUME_PATH, hostUuid, cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(trigger) {
                            @Override
                            public void success(AgentRsp returnValue) {
                                reportProgress(stage.getEnd().toString());
                                success = true;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (success) {
                            deleteBits(temporaryTemplatePath, hostUuid, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    //TODO GC
                                    logger.warn(String.format("failed to delete %s, %s", temporaryTemplatePath, errorCode));
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "upload-to-backup-storage";

                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        TaskProgressRange stage = markTaskStage(parentStage, CREATE_ROOT_VOLUME_TEMPLATE_UPLOAD_STAGE);
                        BackupStorageAskInstallPathMsg bmsg = new BackupStorageAskInstallPathMsg();
                        bmsg.setBackupStorageUuid(msg.getBackupStorageUuid());
                        bmsg.setImageMediaType(image.getMediaType());
                        bmsg.setImageUuid(image.getUuid());
                        bus.makeTargetServiceIdByResourceUuid(bmsg, BackupStorageConstant.SERVICE_ID, msg.getBackupStorageUuid());
                        MessageReply br = bus.call(bmsg);
                        if (!br.isSuccess()) {
                            trigger.fail(br.getError());
                            return;
                        }

                        backupStorageInstallPath = ((BackupStorageAskInstallPathReply) br).getInstallPath();

                        ImageStoreBackupStorageKvmUploader uploader = ImageStoreBackupStorageKvmUploader.createUploader(getSelfInventory(), msg.getBackupStorageUuid());
                        uploader.uploadBits(msg.getImageInventory().getUuid(), backupStorageInstallPath, temporaryTemplatePath, hostUuid, new ReturnValueCompletion<String>(trigger) {
                            @Override
                            public void success(String bsPath) {
                                reportProgress(stage.getEnd().toString());
                                backupStorageInstallPath = bsPath;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-template-on-primary-storage";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        TaskProgressRange stage = markTaskStage(parentStage, CREATE_ROOT_VOLUME_TEMPLATE_SUBSEQUENT_EVENT_STAGE);
                        deleteBits(temporaryTemplatePath, hostUuid, new Completion(trigger) {
                            @Override
                            public void success() {
                                reportProgress(stage.getEnd().toString());
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                //TODO: GC
                                logger.warn(String.format("failed to delete %s on aliyun nas primary storage[uuid: %s], %s; need a cleanup", temporaryTemplatePath, self.getUuid(), errorCode));
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createMount != null && createMount.isNewCreate()) {
                            syncUnmount(hostUuid, createMount.getMountUuid(), volume.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-image-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createImageCacheMount != null && createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        reply.setFormat(volume.getFormat());
                        reply.setTemplateBackupStorageInstallPath(backupStorageInstallPath);
                        completion.success(reply);
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private void createVolume(final CreateVolumeFromVolumeSnapshotOnPrimaryStorageMsg msg,
                                       ReturnValueCompletion<CreateVolumeFromVolumeSnapshotOnAliyunNasPrimaryStorageReply> completion) {
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-template-volume-from-snapshot-%s", msg.getSnapshot().getUuid()));

        VolumeInventory spVolume = VolumeInventory.valueOf(dbf.findByUuid(msg.getSnapshot().getVolumeUuid(), VolumeVO.class));
        VolumeInventory volume = VolumeInventory.valueOf(dbf.findByUuid(msg.getVolumeUuid(), VolumeVO.class));
        ImageInventory img = null;
        if (spVolume.getRootImageUuid() != null) {
            ImageVO ivo = dbf.findByUuid(spVolume.getRootImageUuid(), ImageVO.class);
            if (ivo != null) {
                //TODO: support image deleted here
                img = ImageInventory.valueOf(ivo);
            }
        }
        final ImageInventory image = img;

        chain.then(new ShareFlow() {
            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeInstallPath(self, volume);
            VolumeSnapshotInventory latest = msg.getSnapshot();
            String hostUuid;
            CreateMountDomainProperty createMount;
            CreateMountDomainProperty createImageCacheMount;
            CreateMountDomainProperty createVolumeMount;

            CreateVolumeFromVolumeSnapshotOnAliyunNasPrimaryStorageReply reply = new CreateVolumeFromVolumeSnapshotOnAliyunNasPrimaryStorageReply();
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "find-host-for-create-volume-from-snapshot";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        // use the same host with source volume
                        getHostFromVolumeSync(spVolume, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                hostUuid = returnValue.getHostUuid();
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, spVolume);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });


                flow(new Flow() {
                    String __name__ = "create-imagecache-mount-if-needed-for-create-volume-from-snapshot";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (image != null) {
                            createMountDomainForImage(image, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    createImageCacheMount = returnValue;
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createImageCacheMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-new-volume-mount-if-needed-for-create-volume-from-snapshot";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        createMountDomainForVolume(volume, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                createVolumeMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createVolumeMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createVolumeMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                            syncUnmount(hostUuid, createVolumeMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "merge-snapshot";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        MergeSnapshotCmd cmd = new MergeSnapshotCmd();
                        cmd.volumeUuid = latest.getVolumeUuid();
                        cmd.snapshotInstallPath = latest.getPrimaryStorageInstallPath();
                        cmd.workspaceInstallPath = installPath;
                        cmd.uuid = self.getUuid();

                        httpCall(MERGE_SNAPSHOT_PATH, hostUuid, cmd, MergeSnapshotRsp.class, new ReturnValueCompletion<MergeSnapshotRsp>(completion) {
                            @Override
                            public void success(MergeSnapshotRsp rsp) {
                                reply.setActualSize(rsp.actualSize);
                                reply.setInstallPath(installPath);
                                reply.setSize(rsp.size);
                                reply.setHostUuid(hostUuid);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createMount != null && createMount.isNewCreate()) {
                            syncUnmount(hostUuid, createMount.getMountUuid(), volume.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-image-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createImageCacheMount != null && createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-snapshot-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createVolumeMount != null && createVolumeMount.isNewCreate()) {
                            syncUnmount(hostUuid, createVolumeMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success(reply);
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }


        @Override
    void handle(CreateVolumeFromVolumeSnapshotOnPrimaryStorageMsg msg, ReturnValueCompletion<CreateVolumeFromVolumeSnapshotOnAliyunNasPrimaryStorageReply> completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("create-volume-%s-from-snapshot-%s", msg.getSnapshot().getVolumeUuid(), msg.getSnapshot().getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                createVolume(msg, new ReturnValueCompletion<CreateVolumeFromVolumeSnapshotOnAliyunNasPrimaryStorageReply>(completion) {
                    @Override
                    public void success(CreateVolumeFromVolumeSnapshotOnAliyunNasPrimaryStorageReply returnValue) {
                        completion.success(returnValue);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    @Override
    void handle(MergeVolumeSnapshotOnPrimaryStorageMsg msg, Completion completion) {
        VolumeInventory volume = msg.getTo();
        VolumeSnapshotInventory sp = msg.getFrom();
        VolumeInventory volumeSp = VolumeInventory.valueOf(dbf.findByUuid(sp.getVolumeUuid(), VolumeVO.class));

        ImageInventory img = null;
        if (volume.getRootImageUuid() != null) {
            ImageVO ivo = dbf.findByUuid(volume.getRootImageUuid(), ImageVO.class);
            if (ivo != null) {
                //TODO: support image deleted here
                img = ImageInventory.valueOf(ivo);
            }
        }
        final ImageInventory image = img;

        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName("merge-volume-snapshot-on-primarystorage");

        chain.then(new ShareFlow() {
            String hostUuid;
            CreateMountDomainProperty createMount;
            CreateMountDomainProperty createSpMount;
            CreateMountDomainProperty createImageCacheMount;

            boolean offline = true;
            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = "pre-find-host";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (volume.getVmInstanceUuid() != null) {
                            SimpleQuery<VmInstanceVO> q = dbf.createQuery(VmInstanceVO.class);
                            q.select(VmInstanceVO_.state, VmInstanceVO_.hostUuid);
                            q.add(VmInstanceVO_.uuid, SimpleQuery.Op.EQ, volume.getVmInstanceUuid());
                            Tuple t = q.findTuple();
                            VmInstanceState state = t.get(0, VmInstanceState.class);
                            hostUuid = t.get(1, String.class);

                            if (state != VmInstanceState.Stopped && state != VmInstanceState.Running
                                    && state != VmInstanceState.Destroyed && state != VmInstanceState.Paused) {
                                throw new OperationFailureException(operr("the volume[uuid;%s] is attached to a VM[uuid:%s] which is in state of %s, cannot do the snapshot merge",
                                        volume.getUuid(), volume.getVmInstanceUuid(), state));
                            }

                            offline = (state == VmInstanceState.Stopped || state == VmInstanceState.Destroyed);
                        }
                        trigger.next();
                    }
                });

                flow(new Flow() {
                    String __name__ = "find-host-for-merge-snapshot";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (hostUuid != null) {
                            createMountDomainForVolume(volume, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    createMount = returnValue;
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            getHostFromVolumeSync(volume, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    hostUuid = returnValue.getHostUuid();
                                    createMount = returnValue;
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        }
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "create-imagecache-mount-if-needed-for-merge-snapshot";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (image != null) {
                            createMountDomainForImage(image, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                                @Override
                                public void success(CreateMountDomainProperty returnValue) {
                                    createImageCacheMount = returnValue;
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createImageCacheMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new Flow() {
                    String __name__ = "mount-snapshot-dir-if-needed-for-merge-snapshot";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (volumeSp.getUuid().equals(volume.getUuid())) {
                            trigger.next();
                            return;
                        }
                        createMountDomainForVolume(volume, hostUuid, new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "offline-merge";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (offline) {
                            OfflineMergeSnapshotCmd cmd = new OfflineMergeSnapshotCmd();
                            cmd.fullRebase = msg.isFullRebase();
                            cmd.srcPath = sp.getPrimaryStorageInstallPath();
                            cmd.destPath = volume.getInstallPath();
                            cmd.uuid = self.getUuid();

                            httpCall(OFFLINE_MERGE_SNAPSHOT_PATH, hostUuid, cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
                                @Override
                                public void success(AgentRsp returnValue) {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            MergeVolumeSnapshotOnKvmMsg kmsg = new MergeVolumeSnapshotOnKvmMsg();
                            kmsg.setFullRebase(msg.isFullRebase());
                            kmsg.setHostUuid(hostUuid);
                            kmsg.setFrom(sp);
                            kmsg.setTo(volume);
                            bus.makeTargetServiceIdByResourceUuid(kmsg, HostConstant.SERVICE_ID, hostUuid);
                            bus.send(kmsg, new CloudBusCallBack(completion) {
                                @Override
                                public void run(MessageReply r) {
                                    if (r.isSuccess()) {
                                        trigger.next();
                                    } else {
                                        trigger.fail(r.getError());
                                    }
                                }
                            });
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createMount != null && createMount.isNewCreate()) {
                            syncUnmount(hostUuid, createMount.getMountUuid(), volume.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-snapshot-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createSpMount != null && createSpMount.isNewCreate()) {
                            syncUnmount(hostUuid, createSpMount.getMountUuid(), volumeSp.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-image-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createImageCacheMount != null && createImageCacheMount.isNewCreate()) {
                            String imageCachePath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                            syncUnmount(hostUuid, createImageCacheMount.getMountUuid(), imageCachePath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success();
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    @Override
    void handle(RevertVolumeFromSnapshotOnPrimaryStorageMsg msg, ReturnValueCompletion<RevertVolumeFromSnapshotOnPrimaryStorageReply> completion) {
        RevertVolumeFromSnapshotOnPrimaryStorageReply reply = new RevertVolumeFromSnapshotOnPrimaryStorageReply();
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("revert-volume-from-snapshot-%s", msg.getSnapshot().getUuid()));

        VolumeInventory volume = msg.getVolume();
        VolumeInventory spVolume = VolumeInventory.valueOf(dbf.findByUuid(msg.getSnapshot().getVolumeUuid(), VolumeVO.class));

        chain.then(new ShareFlow() {
            CreateMountDomainProperty createMount;
            String hostUuid;
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "find-host-to-revert-volume";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        getHostFromVolumeSync(volume, msg.getSnapshot().getVolumeUuid(), new ReturnValueCompletion<CreateMountDomainProperty>(trigger) {
                            @Override
                            public void success(CreateMountDomainProperty returnValue) {
                                hostUuid = returnValue.getHostUuid();
                                createMount = returnValue;
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (createMount == null) {
                            trigger.rollback();
                            return;
                        }

                        if (createMount.isNewCreate()) {
                            String installPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                            syncUnmount(hostUuid, createMount.getMountUuid(), installPath, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.rollback();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    logger.warn(errorCode.getDetails());
                                    trigger.rollback();
                                }
                            });
                        } else {
                            trigger.rollback();
                        }
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "revert-volume-from-snapshot";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        RevertVolumeFromSnapshotCmd cmd = new RevertVolumeFromSnapshotCmd();
                        cmd.snapshotInstallPath = msg.getSnapshot().getPrimaryStorageInstallPath();
                        cmd.uuid = self.getUuid();

                        httpCall(REVERT_VOLUME_FROM_SNAPSHOT_PATH, hostUuid, cmd, RevertVolumeFromSnapshotRsp.class, new ReturnValueCompletion<RevertVolumeFromSnapshotRsp>(completion) {
                            @Override
                            public void success(RevertVolumeFromSnapshotRsp rsp) {
                                reply.setNewVolumeInstallPath(rsp.newVolumeInstallPath);
                                reply.setSize(rsp.size);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "delete-temporary-mount-volume-if-it-is-new-created";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (createMount != null && createMount.isNewCreate()) {
                            syncUnmount(hostUuid, createMount.getMountUuid(), spVolume.getInstallPath(), new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                        } else {
                            trigger.next();
                        }
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success(reply);
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    @Override
    void handle(CreateTemporaryVolumeFromSnapshotMsg msg, ReturnValueCompletion<CreateTemporaryVolumeFromSnapshotReply> completion) {
        CreateTemporaryVolumeFromSnapshotReply r = new CreateTemporaryVolumeFromSnapshotReply();

        CreateVolumeFromVolumeSnapshotOnPrimaryStorageMsg cmsg = new CreateVolumeFromVolumeSnapshotOnPrimaryStorageMsg();
        cmsg.setPrimaryStorageUuid(self.getUuid());
        cmsg.setSnapshot(msg.getSnapshot());
        cmsg.setVolumeUuid(msg.getSnapshot().getVolumeUuid());
        bus.makeTargetServiceIdByResourceUuid(cmsg, PrimaryStorageConstant.SERVICE_ID, cmsg.getPrimaryStorageUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    CreateVolumeFromVolumeSnapshotOnAliyunNasPrimaryStorageReply cr = reply.castReply();
                    r.setInstallPath(cr.getInstallPath());
                    r.setSize(cr.getSize());
                    r.setActualSize(cr.getActualSize());
                    r.setHostUuid(cr.getHostUuid());
                    completion.success(r);
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    @Override
    void handle(ReMountHostToNasMsg msg, Completion completion) {
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("remount-host-%s-to-aliyun-nas-ps-%s", msg.getHost().getUuid(), msg.getPrimaryStorageUuid()));

        List<AliyunNasMountVolumeRefVO> refs = Q.New(AliyunNasMountVolumeRefVO.class).eq(AliyunNasMountVolumeRefVO_.hostUuid, msg.getHost().getUuid()).list();

        chain.then(new ShareFlow() {
            boolean timeout = false;
            @Override
            public void setup() {
                flow(new NoRollbackFlow() {
                    String __name__ = "mount-common-path";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        mountCommonPath(msg.getHost().getUuid(), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                for (AliyunNasMountVolumeRefVO ref: refs) {
                    flow(new NoRollbackFlow() {
                        String __name__ = String.format("check-mount-target-%s-still-take-effort", ref.getNasMountUuid());
                        @Override
                        public void run(FlowTrigger trigger, Map data) {
                            NasMountTargetVO mvo = dbf.findByUuid(ref.getNasMountUuid(), NasMountTargetVO.class);
                            CheckMountDomainMsg cmsg = new CheckMountDomainMsg();
                            cmsg.setHostUuid(msg.getHost().getUuid());
                            StringBuilder mount = new StringBuilder(mvo.getMountDomain());
                            if (!mount.toString().contains(":")) {
                                mount.append(":/");
                            }
                            cmsg.setMountDomain(mount.toString());
                            bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, msg.getHost().getUuid());
                            bus.send(cmsg, new CloudBusCallBack(trigger) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (reply.isSuccess()) {
                                        CheckMountDomainReply reply1 = reply.castReply();
                                        if (reply1.getActive()) {
                                            trigger.next();
                                        } else {
                                            logger.warn(String.format("mount domain not valid after %d milliseconds, delete it...", cmsg.getWait()));
                                            updateMount(msg.getHost().getUuid(), ref, new Completion(trigger) {
                                                @Override
                                                public void success() {
                                                    timeout = true;
                                                    trigger.next();
                                                }

                                                @Override
                                                public void fail(ErrorCode errorCode) {
                                                    trigger.fail(errorCode);
                                                }
                                            });
                                        }
                                    } else {
                                        trigger.fail(reply.getError());
                                    }
                                }
                            });
                        }
                    });

                    flow(new NoRollbackFlow() {
                        String __name__ = String.format("mount-data-path-for-mount-target-%s", ref.getNasMountUuid());
                        @Override
                        public void run(FlowTrigger trigger, Map data) {
                            if (timeout) {
                                timeout = false;
                                trigger.next();
                                return;
                            }
                            NasMountTargetVO mvo = dbf.findByUuid(ref.getNasMountUuid(), NasMountTargetVO.class);
                            if (ref.getSourceType() == MountSourceType.imagecache) {
                                ImageVO vo = dbf.findByUuid(ref.getImageUuid(), ImageVO.class);
                                if (vo == null) {
                                    // image is not existed, remove it
                                    dbf.remove(ref);
                                    trigger.next();
                                    return;
                                }
                                ImageInventory image = ImageInventory.valueOf(vo);

                                String mountPath = AliyunNasPrimaryStoragePathMaker.makeCachedImageBaseDir(self, image);
                                String dataPath = AliyunNasPrimaryStoragePathMaker.makeCachedImageDataDir(self, image);
                                String url = AliyunNasPrimaryStoragePathMaker.getCachedImageUrl(self, mvo.getMountDomain(), image);

                                mount(url, mountPath, dataPath, msg.getHost().getUuid(), true, new Completion(trigger) {
                                    @Override
                                    public void success() {
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        trigger.fail(errorCode);
                                    }
                                });
                            } else {
                                VolumeVO vo = dbf.findByUuid(ref.getVolumeUuid(), VolumeVO.class);
                                if (vo == null) {
                                    // volume is not existed, remove it
                                    dbf.remove(ref);
                                    trigger.next();
                                    return;
                                }
                                VolumeInventory volume = VolumeInventory.valueOf(vo);

                                String mountPath = AliyunNasPrimaryStoragePathMaker.makeVolumeBaseDir(self, volume);
                                String dataPath = AliyunNasPrimaryStoragePathMaker.makeVolumeDataDir(self, volume);
                                String url = AliyunNasPrimaryStoragePathMaker.getVolumeBaseUrl(self, mvo.getMountDomain(), volume);

                                mount(url, mountPath, dataPath, msg.getHost().getUuid(), true, new Completion(trigger) {
                                    @Override
                                    public void success() {
                                        trigger.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        trigger.fail(errorCode);
                                    }
                                });
                            }
                        }
                    });
                }

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success();
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        updateHostPSStatus(PrimaryStorageHostStatus.Disconnected, msg.getHost().getUuid());
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private MountSpec makeSpec(VolumeInventory volume, String hostUuid, String mountUuid, String url,
                               String dataPath, String mountPath) {
        if (VolumeType.valueOf(volume.getType()) == VolumeType.Root) {
            return makeSpec(volume.getUuid(), MountSourceType.rootVolumes, hostUuid, mountUuid, url, dataPath, mountPath);
        } else {
            return makeSpec(volume.getUuid(), MountSourceType.dataVolumes, hostUuid, mountUuid, url, dataPath, mountPath);
        }
    }

    private MountSpec makeSpec(String sourceUuid, MountSourceType sourceType, String hostUuid, String mountUuid, String url,
                               String dataPath, String mountPath) {
        MountSpec spec = new MountSpec();
        spec.setUrl(url);
        spec.setDataPath(dataPath);
        spec.setMountPath(mountPath);
        spec.setMountUuid(mountUuid);
        spec.setHostUuid(hostUuid);
        spec.setSourceType(sourceType);
        spec.setSourceUuid(sourceUuid);

        return spec;
    }

    @Override
    void checkMountOnHost(String hostUuid, Completion completion) {
        CheckMountCmd cmd = new CheckMountCmd();
        cmd.uuid = self.getUuid();
        cmd.psUrl = "prim-" + self.getUuid();
        cmd.hostUuid = hostUuid;

        httpCall(CHECK_MOUNT_PATH, hostUuid, cmd, true, CheckMountRsp.class, new ReturnValueCompletion<CheckMountRsp>(completion) {
            @Override
            public void success(CheckMountRsp rsp) {
                List<AliyunNasPrimaryStorageMountPointVO> olds = Q.New(AliyunNasPrimaryStorageMountPointVO.class).
                        eq(AliyunNasPrimaryStorageMountPointVO_.hostUuid, hostUuid).list();
                // all mounts still on hosts
                List<AliyunNasPrimaryStorageMountPointVO> mounts = new ArrayList<>();
                for (NasMountData data: rsp.datas) {
                    logger.debug(String.format("check mount path result: [host: %s, mountUrl: %s, mountPath: %s, mountStatus: %s]", hostUuid,
                            data.getMountUrl(), data.getMountPath(), data.getStatus().toString()));
                    AliyunNasPrimaryStorageMountPointVO old = CollectionUtils.find(olds, arg -> {
                        if (arg.getMountUrl().equals(data.getMountUrl())) {
                            if (!mounts.contains(arg)) {
                                mounts.add(arg);
                            }
                            return arg;
                        }
                        return null;
                    });

                    if (old == null) {
                        AliyunNasPrimaryStorageMountPointVO cvo = new AliyunNasPrimaryStorageMountPointVO();
                        cvo.setHostUuid(hostUuid);
                        cvo.setPrimaryStorageUuid(self.getUuid());
                        cvo.setMountPath(data.getMountPath());
                        cvo.setMountUrl(data.getMountUrl());
                        cvo.setStatus(data.getStatus());
                        cvo.setCheckTimes(1);
                        if (data.getInfo() != null) {
                            logger.debug(String.format("found aliyun nas mount path err: %s", data.getInfo()));
                            cvo.setLastErrInfo(data.getInfo());
                        }
                        if (data.getStatus() != NasMountStatus.Normal) {
                            cvo.setErrorTimes(1);
                            cvo.setLastNormalDistance(1);
                        } else {
                            cvo.setErrorTimes(0);
                            cvo.setLastNormalDistance(0);
                        }
                        dbf.persistAndRefresh(cvo);
                    } else {
                        new SQLBatch() {
                            @Override
                            protected void scripts() {
                                dbf.reload(old);
                                old.setCheckTimes(old.getCheckTimes() + 1);
                                old.setStatus(data.getStatus());
                                old.setMountPath(data.getMountPath());
                                if (data.getInfo() != null) {
                                    logger.debug(String.format("found aliyun nas mount path err: %s", data.getInfo()));
                                    old.setLastErrInfo(data.getInfo());
                                }
                                if (data.getStatus() != NasMountStatus.Normal) {
                                    old.setErrorTimes(old.getErrorTimes() + 1);
                                    old.setLastNormalDistance(old.getLastNormalDistance() + 1);
                                } else {
                                    old.setLastNormalDistance(0);
                                }
                                dbf.updateAndRefresh(old);
                            }
                        }.execute();
                    }
                }
                // clear the records
                String commonPath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
                for (AliyunNasPrimaryStorageMountPointVO old: olds) {
                    if (!mounts.contains(old)) {
                        dbf.remove(old);
                    }
                    if (commonPath.equals(old.getMountPath())) {
                        continue;
                    }
                    if (!Q.New(AliyunNasMountVolumeRefVO.class).eq(AliyunNasMountVolumeRefVO_.hostUuid, hostUuid).
                            eq(AliyunNasMountVolumeRefVO_.dataPath, old.getMountPath()).isExists()) {
                        String warning = String.format("found a mountpath [%s] on host [%s], but has no mount point in db. try to umount it by zstack.",
                                old.getMountPath(), hostUuid);
                        logger.warn(warning);
                        //TODO: delete image might delete AliyunNasMountVolumeRefVO records, so can not unmount it now...
//                        unmount(hostUuid, null, old.getMountPath(), false, new NopeCompletion());
                    }
                }
                sendWarnning(hostUuid);
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void sendWarnning(String hostUuid) {
        List<AliyunNasPrimaryStorageMountPointVO> faults = Q.New(AliyunNasPrimaryStorageMountPointVO.class).
                eq(AliyunNasPrimaryStorageMountPointVO_.hostUuid, hostUuid).
                notEq(AliyunNasPrimaryStorageMountPointVO_.status, NasMountStatus.Normal).list();
        if (faults.isEmpty()) {
            return;
        }

        StringBuilder builder = new StringBuilder();
        for (AliyunNasPrimaryStorageMountPointVO fault: faults) {
            builder.append(fault.getLastErrInfo());
            builder.append(";");
        }
        // only send warning once each host
        if (builder.length() > 1) {
            builder.deleteCharAt(builder.lastIndexOf(";"));
            HostCanonicalEvents.HostMountData data = new HostCanonicalEvents.HostMountData();
            data.hostUuid = hostUuid;
            data.psUuid = self.getUuid();
            data.details = builder.toString();
            eventf.fire(HostCanonicalEvents.HOST_CHECK_MOUNT_FAULT, data);
        }
    }

    void setupAliyunNasFencer(KvmSetupSelfFencerExtensionPoint.KvmSetupSelfFencerParam param, Completion completion) {
        KvmSetupSelfFencerCmd cmd = new KvmSetupSelfFencerCmd();
        cmd.uuid = self.getUuid();
        cmd.interval = param.getInterval();
        cmd.maxAttempts = param.getMaxAttempts();
        cmd.heartbeat = "heartbeat";
        cmd.mountPath = AliyunNasPrimaryStoragePathMaker.makeMountCommondPath(self);
        cmd.strategy = param.getStrategy();

        httpCall(ALIYUN_NAS_SELF_FENCER, param.getHostUuid(), cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp returnValue) {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    void cancelAliyunNasFencer(KvmSetupSelfFencerExtensionPoint.KvmCancelSelfFencerParam param, Completion completion) {
        KvmCancelSelfFencerCmd cmd = new KvmCancelSelfFencerCmd();
        cmd.uuid = self.getUuid();

        httpCall(CANCEL_ALIYUN_NAS_SELF_FENCER, param.getHostUuid(), cmd, AgentRsp.class, new ReturnValueCompletion<AgentRsp>(completion) {
            @Override
            public void success(AgentRsp returnValue) {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }
}
