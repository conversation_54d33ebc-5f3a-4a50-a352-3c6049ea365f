package org.zstack.sso.oauth2.service;

import com.auth0.jwt.interfaces.Claim;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.Platform;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatch;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.identity.SessionInventory;
import org.zstack.iam2.IAM2Manager;
import org.zstack.iam2.api.Attribute;
import org.zstack.iam2.entity.*;
import org.zstack.iam2.message.CreateIAM2VirtualIDMsg;
import org.zstack.sso.SSOConstants;
import org.zstack.sso.SSOSystemTags;
import org.zstack.sso.header.*;
import org.zstack.sso.oauth2.AuthGlobalProperty;
import org.zstack.sso.oauth2.OAuth2Constants;
import org.zstack.sso.service.SSOManager;
import org.zstack.sso.service.SSOServerExtensionPoint;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;

/**
 * @Author: DaoDao
 * @Date: 2022/8/25
 */
public class OAuth2LoginIAM2 implements OAuth2Login, SSOServerExtensionPoint {
    private static final CLogger logger = Utils.getLogger(OAuth2LoginIAM1.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private SSOManager sso;
    @Autowired
    private IAM2Manager iam2Manager;

    public static SSOLoginType type = new SSOLoginType("oauth2-iam2");
    @Override
    public SSOLoginType getLoginType() {
        return type;
    }

    @Override
    public String getUserNameFromToken(Map<String, Claim> tokenMap, Map<String, List<UserInfomappingRule>> attributeRuleMap, String clientDetailsUuid) {
        String ssoUseAsLoginName = getOAuth2UseAsLoginName(clientDetailsUuid);

        if (!tokenMap.containsKey(ssoUseAsLoginName)) {
            throw new OperationFailureException(operr("fail to get params[%s]", ssoUseAsLoginName));
        }
        String userName = tokenMap.get(ssoUseAsLoginName).asString();
        if (StringUtils.isEmpty(userName)) {
            throw new OperationFailureException(operr("get user name is null"));
        }

        createOrUpdateFromToken(userName, clientDetailsUuid, tokenMap, attributeRuleMap);
        return userName;
    }

    private void createOrUpdateFromToken(String userName, String clientUuid, Map<String, Claim> tokenMap,
                                         Map<String, List<UserInfomappingRule>> attributeRuleMap) {
        IAM2VirtualIDVO virtualID = Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.name, userName).find();
        OAuth2ClientVO clientVO = dbf.findByUuid(clientUuid, OAuth2ClientVO.class);
        if (virtualID != null && !virtualID.getType().equals(clientVO.getClientType())) {
            throw new OperationFailureException(operr(" iam2 has a user with the same name[%s]", userName));
        }

        if (virtualID == null) {
            CreateIAM2VirtualIDMsg msg = generateCreateIAM2VirtualIDMsgFromToken(userName, tokenMap, attributeRuleMap, clientUuid);
            IAM2VirtualIDInventory inventory = iam2Manager.doCreateIAM2VirtualID(msg);
            createThirdClientAccountRef(inventory.getUuid(), clientUuid);
        }

        Map<String, String> userInfoMap = new HashMap<>();
        tokenMap.forEach((key, value) -> { userInfoMap.put(key, value.asString()); });
        createOrUpdateUserInfo(userInfoMap, attributeRuleMap, userName);
        createOrUpdateGroupFromToken(tokenMap, clientUuid, userName);
    }

    private void createOrUpdateFromUserInfo(String userName, String clientUuid, Map<String, Object> tokenMap,
                                            Map<String, List<UserInfomappingRule>> attributeRuleMap) {
        IAM2VirtualIDVO virtualID = Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.name, userName).find();
        OAuth2ClientVO clientVO = dbf.findByUuid(clientUuid, OAuth2ClientVO.class);
        if (virtualID != null && !virtualID.getType().equals(clientVO.getClientType())) {
            throw new OperationFailureException(operr(" iam2 has a user with the same name[%s]", userName));
        }

        Map<String, String> token = tokenMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue())));

        if (virtualID == null) {
            CreateIAM2VirtualIDMsg msg = generateCreateIAM2VirtualIDMsg(userName, token, attributeRuleMap, clientUuid);
            IAM2VirtualIDInventory inventory = iam2Manager.doCreateIAM2VirtualID(msg);
            createThirdClientAccountRef(inventory.getUuid(), clientUuid);
        }

        createOrUpdateUserInfo(token, attributeRuleMap, userName);
        createOrUpdateGroupFromUserInfo(token, clientUuid, userName);
    }

    private void createThirdClientAccountRef(String iam2VirtualIDUuid, String clientUuid) {
        ThirdClientAccountRefVO refVO = new ThirdClientAccountRefVO();
        refVO.setUuid(Platform.getUuid());
        refVO.setResourceUuid(iam2VirtualIDUuid);
        refVO.setClientUuid(clientUuid);
        refVO.setResourceType(IAM2VirtualIDVO.class.getSimpleName());
        dbf.persist(refVO);
    }

    @Override
    public String getUserNameFromUserInfo(Map<String, Object> tokenMap, Map<String, List<UserInfomappingRule>> attributeRuleMap, String clientDetailsUuid) {
        String ssoUseAsLoginName = getOAuth2UseAsLoginName(clientDetailsUuid);
        if (!tokenMap.containsKey(ssoUseAsLoginName)) {
            throw new OperationFailureException(operr("fail to get params[%s]", ssoUseAsLoginName));
        }

        Object userName = tokenMap.get(ssoUseAsLoginName);
        if (StringUtils.isEmpty(userName.toString())) {
            throw new OperationFailureException(operr("get user name is null"));
        }

        createOrUpdateFromUserInfo(userName.toString(), clientDetailsUuid, tokenMap, attributeRuleMap);
        return userName.toString();
    }

    @Override
    public String getTokenName(String clientType) {
        if (AuthGlobalProperty.OAUTH2_TOKEN_USE) {
            return AuthGlobalProperty.OAUTH2_GET_TOKEN_USERINFO;
        }
        if (OAuth2Constants.OIDC_SSO_CLIENT_TYPE.equals(clientType)) {
            return OAuth2Constants.ID_TOKEN_VALUE;
        }
        if (OAuth2Constants.OAUTH2_SSO_CLIENT_TYPE.equals(clientType)) {
            return OAuth2Constants.ACCESS_TOKEN_VALUE;
        }
        return OAuth2Constants.ACCESS_TOKEN_VALUE;
    }

    private List<String> getGroupNamesFromToken(Map<String, Claim> tokenMap, String ssoUseAsGroupName) {
        Claim claim = tokenMap.get(ssoUseAsGroupName);
        if (claim == null) {
            return new ArrayList<>();
        }
        List<String> groupNames = claim.asList(String.class);
        if (groupNames.isEmpty()) {
            return new ArrayList<>();
        }

        return groupNames;
    }

    private List<String> getGroupNamesFromUserInfo(Map<String, String> tokenMap, String ssoUseAsGroupName) {
        if (!tokenMap.containsKey(ssoUseAsGroupName)) {
            return new ArrayList<>();
        }
        String groupValue = tokenMap.get(ssoUseAsGroupName);
        List<String> groupNames = Arrays.stream(groupValue.substring(1, groupValue.length() - 1).split(", "))
                .collect(Collectors.toList());
        if (groupNames.isEmpty()) {
            return new ArrayList<>();
        }

        return groupNames;
    }

    private void createOrUpdateUserInfo(Map<String, String> ssoInfoMap, Map<String, List<UserInfomappingRule>> attributeRuleMap, String userName) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                String virtualIDUuid = q(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.name, userName).select(IAM2VirtualIDVO_.uuid).findValue();
                if (virtualIDUuid == null) {
                    return;
                }
                sql(IAM2VirtualIDAttributeVO.class).eq(IAM2VirtualIDAttributeVO_.virtualIDUuid, virtualIDUuid)
                        .in(IAM2VirtualIDAttributeVO_.name, Arrays.asList(SSOConstants.Mail_NAME, SSOConstants.Phone_NAME, SSOConstants.Identifier_NAME))
                        .hardDelete();

                for (Map.Entry<String, List<UserInfomappingRule>> entry : attributeRuleMap.entrySet()) {
                    for (UserInfomappingRule rule : entry.getValue()) {
                        if (SSOConstants.FULL_NAME.equals(rule.getName()) || SSOConstants.USER_NAME.equals(rule.getName()) || SSOConstants.GROUP_NAME.equals(rule.getName())) {
                            continue;
                        }
                        IAM2VirtualIDAttributeVO vo = new IAM2VirtualIDAttributeVO();
                        vo.setUuid(Platform.getUuid());
                        vo.setVirtualIDUuid(virtualIDUuid);
                        vo.setName(rule.getName());
                        vo.setValue(ssoInfoMap.get(rule.getAttribute()));
                        vo.setType(AttributeType.Customized);
                        persist(vo);
                    }
                }

            }
        }.execute();
    }

    private void createOrUpdateGroupFromUserInfo(Map<String, String> tokenMap, String clientUuid, String userName) {
        String ssoUseAsGroupName = getIAM2VirtualIDGroupName(clientUuid);
        if (StringUtils.isEmpty(ssoUseAsGroupName)) {
            return;
        }
        List<String> groupNames = getGroupNamesFromUserInfo(tokenMap, ssoUseAsGroupName);
        createOrUpdateGroup(groupNames, clientUuid, userName);
    }

    private void createOrUpdateGroupFromToken(Map<String, Claim> tokenMap, String clientUuid, String userName) {
        String ssoUseAsGroupName = getIAM2VirtualIDGroupName(clientUuid);
        if (StringUtils.isEmpty(ssoUseAsGroupName)) {
            return;
        }
        List<String> groupNames = getGroupNamesFromToken(tokenMap, ssoUseAsGroupName);
        createOrUpdateGroup(groupNames, clientUuid, userName);
    }

    private void createOrUpdateGroup(List<String> groupNames, String clientUuid, String userName) {

        new SQLBatch() {
            @Override
            protected void scripts() {
                IAM2VirtualIDVO virtualIDVO = q(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.name, userName).find();
                sql(IAM2VirtualIDGroupRefVO.class)
                        .eq(IAM2VirtualIDGroupRefVO_.virtualIDUuid, virtualIDVO.getUuid())
                        .hardDelete();

                if (groupNames.isEmpty()) {
                    return;
                }

                for(String name : groupNames) {
                    if (StringUtils.isEmpty(name)) {
                        continue;
                    }
                    if (!q(IAM2VirtualIDGroupVO.class)
                            .eq(IAM2VirtualIDGroupVO_.name, name.trim())
                            .isExists()) {
                        IAM2VirtualIDGroupVO vo = new IAM2VirtualIDGroupVO();
                        vo.setUuid(Platform.getUuid());
                        vo.setName(name.trim());
                        vo.setState(IAM2State.Enabled);
                        vo.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
                        persist(vo);
                    }
                }

                List<String> groupUuid = q(IAM2VirtualIDGroupVO.class)
                        .in(IAM2VirtualIDGroupVO_.name, groupNames)
                        .select(IAM2VirtualIDGroupVO_.uuid)
                        .listValues();

                groupUuid.forEach(uuid -> {
                    IAM2VirtualIDGroupRefVO ref = new IAM2VirtualIDGroupRefVO();
                    ref.setGroupUuid(uuid);
                    ref.setVirtualIDUuid(virtualIDVO.getUuid());
                    persist(ref);
                });
            }
        }.execute();
    }

    public String getIAM2VirtualIDGroupName(String clientUuid) {
        if (SSOSystemTags.SSO_USE_AS_GROUP_NAME.hasTag(clientUuid)) {
            return SSOSystemTags.SSO_USE_AS_GROUP_NAME.getTokenByResourceUuid(clientUuid,
                    SSOSystemTags.SSO_USE_AS_GROUP_NAME_TOKEN);
        }
        return null;
    }

    @Override
    public SessionInventory getSession(String userName) {
        return sso.getIAM2Session(userName);
    }

    private CreateIAM2VirtualIDMsg generateCreateIAM2VirtualIDMsgFromToken(String userName, Map<String, Claim> tokenMap,
                                                                              Map<String, List<UserInfomappingRule>> attributeRuleMap,
                                                                              String clientUuid) {
        Map<String, String> token = tokenMap.entrySet().stream()
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue().asString()), HashMap::putAll);

        return generateCreateIAM2VirtualIDMsg(userName, token, attributeRuleMap, clientUuid);
    }

    private CreateIAM2VirtualIDMsg generateCreateIAM2VirtualIDMsg(String userName, Map<String, String> tokenMap,
                                                                  Map<String, List<UserInfomappingRule>> attributeRuleMap, String clientUuid) {
        OAuth2ClientVO clientVO = dbf.findByUuid(clientUuid, OAuth2ClientVO.class);

        List<Attribute> attributes = new ArrayList<>();

        CreateIAM2VirtualIDMsg msg = new CreateIAM2VirtualIDMsg();
        msg.setResourceUuid(Platform.getUuid());
        msg.setPassword(Platform.getUuid() + Platform.getUuid());
        msg.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
        msg.setType(clientVO.getClientType());
        msg.setName(userName);
        msg.setNoNeedChangePassword(true);

        for (Map.Entry<String, List<UserInfomappingRule>> entry : attributeRuleMap.entrySet()) {
            for (UserInfomappingRule rule : entry.getValue()) {
                if (!tokenMap.containsKey(rule.getAttribute())) {
                    logger.info(String.format("custom attribute[%s] is not exists", rule.getAttribute()));
                    continue;
                }
                if(RuleAttributeType.SYSTEM == rule.getType()) {
                    try {
                        Field field = msg.getClass().getDeclaredField(rule.getName());
                        field.setAccessible(true);
                        field.set(msg, tokenMap.get(rule.getAttribute()));
                    } catch (NoSuchFieldException|IllegalAccessException e) {
                        logger.debug("Failed to assign field, because " + e.getMessage());
                        continue;
                    }
                } else {
                    Attribute attrToCreate = new Attribute();
                    attrToCreate.setName(rule.getName());
                    attrToCreate.setValue(tokenMap.get(rule.getAttribute()));
                    attributes.add(attrToCreate);
                }
            }
        }

        if(!attributes.isEmpty()) {
            msg.setAttributes(attributes);
        }

        return msg;
    }

    @Override
    public void afterGetOAuth2Token(String accountUuid, Map<String, Object> additionalInventoryMap) {
        String iam2ProjectUuid = Q.New(IAM2ProjectAccountRefVO.class)
                .select(IAM2ProjectAccountRefVO_.projectUuid)
                .eq(IAM2ProjectAccountRefVO_.accountUuid, accountUuid)
                .findValue();

        if (iam2ProjectUuid == null) {
            return;
        }

        additionalInventoryMap.put("projectUuid", iam2ProjectUuid);
    }
}

