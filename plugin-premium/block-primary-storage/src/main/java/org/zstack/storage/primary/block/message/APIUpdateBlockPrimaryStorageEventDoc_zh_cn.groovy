package org.zstack.storage.primary.block.message

import org.zstack.storage.primary.block.BlockPrimaryStorageInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "修改块存储信息"

	ref {
		name "inventory"
		path "org.zstack.storage.primary.block.message.APIUpdateBlockPrimaryStorageEvent.inventory"
		desc "null"
		type "BlockPrimaryStorageInventory"
		since "0.6"
		clz BlockPrimaryStorageInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.storage.primary.block.message.APIUpdateBlockPrimaryStorageEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "0.6"
		clz ErrorCode.class
	}
}
