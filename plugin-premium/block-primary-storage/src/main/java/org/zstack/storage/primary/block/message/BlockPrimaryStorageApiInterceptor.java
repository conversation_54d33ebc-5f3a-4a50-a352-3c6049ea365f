package org.zstack.storage.primary.block.message;

import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.message.APIMessage;

/**
 * <AUTHOR>
 * @date 2022/4/11 11:27
 */
public class BlockPrimaryStorageApiInterceptor implements ApiMessageInterceptor{

    public BlockPrimaryStorageApiInterceptor() {}

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIAddBlockPrimaryStorageMsg) {
            validate((APIAddBlockPrimaryStorageMsg) msg);
        } else if (msg instanceof APIGetBlockPrimaryStorageMetadataMsg) {
            validate((APIGetBlockPrimaryStorageMetadataMsg) msg);
        }
        return msg;
    }

    private void validate(APIAddBlockPrimaryStorageMsg msg) {
        return;
    }

    private void validate(APIGetBlockPrimaryStorageMetadataMsg msg) {
        return;
    }
}
