package org.zstack.iam2.container;

import edu.emory.mathcs.backport.java.util.Collections;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1ResourceRequirements;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.container.ContainerClientExtensionPoint;
import org.zstack.container.entity.ContainerManagementEndpointVO;
import org.zstack.container.entity.ContainerManagementEndpointVO_;
import org.zstack.container.message.CreateDeploymentMsg;
import org.zstack.container.message.CreateKubernetesServiceMsg;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.AbstractService;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NoErrorCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.Flow;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.core.workflow.FlowDoneHandler;
import org.zstack.header.core.workflow.FlowErrorHandler;
import org.zstack.header.core.workflow.FlowRollback;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.identity.Quota;
import org.zstack.header.identity.ReportQuotaExtensionPoint;
import org.zstack.header.identity.quota.QuotaMessageHandler;
import org.zstack.header.message.Message;
import org.zstack.iam2.AddRolesToVirtualIDExtensionPoint;
import org.zstack.iam2.CreateIAM2VirtualIDExtensionPoint;
import org.zstack.iam2.IAM2ProjectExtensionPoint;
import org.zstack.iam2.RemoveRolesFromVirtualIDExtensionPoint;
import org.zstack.iam2.api.APIUpdateIAM2ProjectMsg;
import org.zstack.iam2.attribute.virtualid.IAM2PlatformAdmin;
import org.zstack.iam2.attribute.virtualid.IAM2PlatformUser;
import org.zstack.iam2.container.zaku.ZakuApiCaller;
import org.zstack.iam2.container.zaku.ZakuApiStructures;
import org.zstack.iam2.container.zaku.ZakuErrors;
import org.zstack.iam2.container.zaku.ZakuHttpClient;
import org.zstack.iam2.container.zaku.ZakuResponse;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO_;
import org.zstack.iam2.entity.IAM2ProjectInventory;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.iam2.entity.IAM2VirtualIDInventory;
import org.zstack.iam2.entity.IAM2VirtualIDVO;
import org.zstack.iam2.entity.IAM2VirtualIDVO_;
import org.zstack.identity.AccountManager;
import org.zstack.identity.QuotaExtensionPoint;
import org.zstack.identity.QuotaUtil;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;
import static org.zstack.utils.CollectionDSL.*;

public class IAM2ContainerManagerImpl extends AbstractService implements
        IAM2ContainerManager,
        IAM2ProjectExtensionPoint,
        CreateIAM2VirtualIDExtensionPoint,
        AddRolesToVirtualIDExtensionPoint,
        RemoveRolesFromVirtualIDExtensionPoint,
        QuotaExtensionPoint,
        ContainerClientExtensionPoint,
        ReportQuotaExtensionPoint {
    protected static final CLogger logger = Utils.getLogger(IAM2ContainerManagerImpl.class);

    private static final Map<String, String> cloudContainerQuotaMappings = new HashMap<>();

    @Autowired
    private AccountManager accountManager;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;
    @Autowired
    private ThreadFacade thdf;

    private void debug(String methodName, String accountUuid) {
        logger.debug(String.format("skip %s extension point," +
                " because no container is required by project[linkedAccountUuid: %s]",
                methodName,
                accountUuid)
        );
    }

    @Override
    public List<Quota> reportQuota() {
        ContainerQuotaDefinitionBuilder.GetContainerUsage usageFunction = (accountUuid, name) -> {
            ClusterApiCaller cc = getApiCallerByAccount(accountUuid);
            if (cc == null) {
                debug("reportQuota", accountUuid);
                return 0L;
            }

            ZakuResponse<ZakuApiStructures.ZakuQuota[]> response = cc.caller
                    .getProjectQuota(cc.getProjectUuid(),
                            cc.clusterId);
            if (!response.isSuccess()) {
                logger.debug(String.format(
                        "failed to query project %s quota from container %s cluster %s, because %s",
                        cc.projectUuid,
                        cc.containerManagerUuid,
                        cc.clusterId,
                        response.getError())
                );
                return 0L;
            }

            String zakuQuotaName = cloudContainerQuotaMappings.get(name);
            if (zakuQuotaName == null) {
                logger.debug("no quota name [cloud: %s, zaku: %s] mapped, report usage as 0");
                return 0L;
            }

            for (ZakuApiStructures.ZakuQuota zaku : response.getContent()) {
                if (zaku.getName().equals(zakuQuotaName)) {
                    return zaku.getUsed();
                }
            }

            return 0L;
        };
        Quota quota = new Quota();
        ContainerQuotaDefinitionBuilder builder = ContainerQuotaDefinitionBuilder.newBuilder();
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_CPU_NUM.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_CPU_NUM.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_CPU_NUM_LIMITATION.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_CPU_NUM_LIMITATION.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_MEMORY_SIZE.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_MEMORY_SIZE.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_MEMORY_SIZE_LIMITATION.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_MEMORY_SIZE_LIMITATION.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );
        quota.addQuotaMessageChecker(new QuotaMessageHandler<>(CreateDeploymentMsg.class)
                .addMessageRequiredQuotaHandler(IAM2ContainerQuotaGlobalConfig.CONTAINER_CPU_NUM_LIMITATION.getName(), msg -> {
                    Quantity cpu = getQuantityByKey(msg.getDeployment(), "cpu");

                    if (cpu == null) {
                        logger.trace("no cpu in request, skip this quota check");
                        return 0L;
                    }

                    return cpu.getNumber().longValue();
                }).addMessageRequiredQuotaHandler(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_CPU_NUM.getName(), msg -> {
                    Quantity cpu = getQuantityByKey(msg.getDeployment(), "cpu");

                    if (cpu == null) {
                        logger.trace("no cpu in request, skip this quota check");
                        return 0L;
                    }

                    return cpu.getNumber().longValue();
                }).addMessageRequiredQuotaHandler(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_MEMORY_SIZE.getName(), msg -> {
                    Quantity cpu = getQuantityByKey(msg.getDeployment(), "memory");

                    if (cpu == null) {
                        logger.trace("no memory in request, skip this quota check");
                        return 0L;
                    }

                    return cpu.getNumber().longValue();
                }).addMessageRequiredQuotaHandler(IAM2ContainerQuotaGlobalConfig.CONTAINER_MEMORY_SIZE_LIMITATION.getName(), msg -> {
                    Quantity cpu = getQuantityByKey(msg.getDeployment(), "memory");

                    if (cpu == null) {
                        logger.trace("no memory in request, skip this quota check");
                        return 0L;
                    }

                    return cpu.getNumber().longValue();
                }).addMessageRequiredQuotaHandler(IAM2ContainerQuotaGlobalConfig.CONTAINER_GPU_VIDEO_RAM_SIZE.getName(), msg -> {
                    Quantity gpu = getQuantityByKey(msg.getDeployment(), "nvidia.com/gpumem");

                    if (gpu == null) {
                        logger.trace("no gpu in request, skip this quota check");
                        return 0L;
                    }

                    Quantity gpuNum = getQuantityByKey(msg.getDeployment(), "nvidia.com/gpu");
                    if (gpuNum == null) {
                        logger.trace("no gpu num in request, skip this quota check");
                        return 0L;
                    }

                    if (gpuNum.getNumber().longValue() == 0) {
                        logger.trace("gpu num is 0, skip this quota check");
                        return 0L;
                    }

                    if (logger.isTraceEnabled()) {
                        logger.trace(String.format("gpu num is %s, gpu video ram size is %s", gpuNum.getNumber().longValue(), gpu.getNumber().longValue()));
                    }

                    return gpu.getNumber().longValue() * gpuNum.getNumber().longValue();
                }));

        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_GPU_VIDEO_RAM_SIZE.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_GPU_VIDEO_RAM_SIZE.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_DATA_VOLUME_SIZE.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_DATA_VOLUME_SIZE.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );

        quota.addQuotaMessageChecker(new QuotaMessageHandler<>(CreateDeploymentMsg.class)
                .addFixedRequiredSize(IAM2ContainerQuotaGlobalConfig.CONTAINER_GROUP_NUM.getName(), 1L));
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_GROUP_NUM.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_GROUP_NUM.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );

        quota.addQuotaMessageChecker(new QuotaMessageHandler<>(CreateKubernetesServiceMsg.class)
                .addFixedRequiredSize(IAM2ContainerQuotaGlobalConfig.CONTAINER_SERVICE_NUM.getName(), 1L));
        quota.addQuotaMessageChecker(new QuotaMessageHandler<>(CreateKubernetesServiceMsg.class)
                .addFixedRequiredSize(IAM2ContainerQuotaGlobalConfig.CONTAINER_LOAD_BALANCE_SERVICE_NUM.getName(), 1L));
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_SERVICE_NUM.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_SERVICE_NUM.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );
        quota.defineQuota(builder
                .name(IAM2ContainerQuotaGlobalConfig.CONTAINER_LOAD_BALANCE_SERVICE_NUM.getName())
                .defaultValue(IAM2ContainerQuotaGlobalConfig.CONTAINER_LOAD_BALANCE_SERVICE_NUM.defaultValue(Long.class))
                .getUsage(usageFunction)
                .build()
        );
        return list(quota);
    }

    private Quantity getQuantityByKey(V1Deployment deployment, String key) {
        if (deployment == null) {
            logger.trace("no deployment in request, skip this quota check");
            return null;
        }

        if (deployment.getSpec() == null) {
            logger.trace("no spec in deployment, skip this quota check");
            return null;
        }

        if (deployment.getSpec().getTemplate() == null) {
            logger.trace("no template in deployment, skip this quota check");
            return null;
        }

        if (deployment.getSpec().getTemplate().getSpec() == null) {
            logger.trace("no spec in template, skip this quota check");
            return null;
        }

        if (deployment.getSpec().getTemplate().getSpec().getContainers() == null) {
            logger.trace("no containers in template, skip this quota check");
            return null;
        }

        if (deployment.getSpec().getTemplate().getSpec().getContainers().isEmpty()) {
            logger.trace("no containers in template, skip this quota check");
            return null;
        }

        if (deployment.getSpec().getTemplate().getSpec().getContainers().get(0) == null) {
            logger.trace("no container in template, skip this quota check");
            return null;
        }

        V1ResourceRequirements resources = deployment.getSpec().getTemplate().getSpec().getContainers().get(0).getResources();
        if (resources == null) {
            logger.trace("no resources in container, skip this quota check");
            return null;
        }

        if (resources.getLimits() == null) {
            logger.trace("no limits in container, skip this quota check");
            return null;
        }

        Quantity quantity = resources.getLimits().get(key);
        if (quantity == null) {
            logger.trace(String.format("no %s in requests, skip this quota check", key));
            return null;
        }

        return quantity;
    }

    private void initProjectAndQuotas(IAM2ProjectInventory project, Completion completion) {
        ClusterApiCaller cc = getApiCallerByAccount(project.getLinkedAccountUuid());
        if (cc == null) {
            debug("afterCreate", project.getLinkedAccountUuid());
            completion.success();
            return;
        }

        ZakuResponse<ZakuApiStructures.ZakuProject> response = cc.caller.createProject(project.getUuid(), project.getName(), project.getDescription());
        if (!response.isSuccess()) {
            completion.fail(response.getError());
            return;
        }

        initQuotas(cc, project.getUuid(), project.getLinkedAccountUuid(), completion);
    }

    private void initQuotas(ClusterApiCaller cc, String uuid, String accountUuid, Completion completion) {
        Map<String, Quota.QuotaPair> pairs = new QuotaUtil().makeQuotaPairs(accountUuid);
        List<ZakuApiStructures.QuotaParam> params = fromQuotaPairsToQuotaParam(pairs);
        if (params.isEmpty()) {
            logger.debug(String.format("no quota of project[uuid: %s] need to be update on container" +
                    " side, skip this ext.", uuid));
            completion.success();
            return;
        }
        ZakuResponse<ZakuApiStructures.ZakuQuota[]> quotaResponse = cc.caller.setProjectQuota(uuid, cc.getClusterId(), params);
        if (!quotaResponse.isSuccess()) {
            logger.debug(String.format("failed to update quota of project[uuid: %s] on container side, because %s",
                    uuid, quotaResponse.getError()));
            if (quotaResponse.getError() != null) {
                completion.fail(quotaResponse.getError());
            } else {
                completion.fail(operr("failed to update quota of project[uuid: %s] on container side due to an unknown error", uuid));
            }
            return;
        }
        completion.success();
    }

    @Override
    public void afterCreate(IAM2ProjectInventory project, Completion completion) {
        initProjectAndQuotas(project, completion);
    }

    @Override
    public void beforeUpdate(APIUpdateIAM2ProjectMsg msg, IAM2ProjectInventory project, Completion completion) {
        ClusterApiCaller cc = getApiCallerByAccount(project.getLinkedAccountUuid());
        if (cc == null) {
            debug("beforeUpdate", project.getLinkedAccountUuid());
            completion.success();
            return;
        }

        ZakuResponse<ZakuApiStructures.ZakuProject> response = cc.caller.updateProject(project.getUuid(), msg.getName(), msg.getDescription());
        if (!response.isSuccess()) {
            completion.fail(response.getError());
            return;
        }

        completion.success();
    }

    @Override
    public void rollbackUpdate(APIUpdateIAM2ProjectMsg msg, IAM2ProjectInventory project, Completion completion) {
        ClusterApiCaller cc = getApiCallerByAccount(project.getLinkedAccountUuid());
        if (cc == null) {
            debug("rollbackUpdate", project.getLinkedAccountUuid());
            completion.success();
            return;
        }

        ZakuResponse<ZakuApiStructures.ZakuProject> response = cc.caller.updateProject(project.getUuid(), project.getName(), project.getDescription());
        if (!response.isSuccess()) {
            completion.fail(response.getError());
            return;
        }

        completion.success();
    }

    @Override
    public void beforeExpungeProject(IAM2ProjectInventory project, Completion completion) {
        ClusterApiCaller cc = getApiCallerByAccount(project.getLinkedAccountUuid());
        if (cc == null) {
            debug("beforeExpungeProject", project.getLinkedAccountUuid());
            completion.success();
            return;
        }

        ZakuResponse<String> response = cc.caller.deleteProject(project.getUuid());
        if (!response.isSuccess()) {
            ErrorCode err = response.getError();
            if (err != null && ZakuErrors.PROJECT_NOT_EXIST.toString().equals(err.getCode())) {
                logger.debug(String.format("project[uuid: %s] not exist on container side, skip this ext.", project.getUuid()));
                completion.success();
            } else {
                completion.fail(response.getError());
            }
            return;
        }

        completion.success();
    }

    @Override
    public void beforeAddIam2VirtualIdsToProject(IAM2ProjectInventory project, List<IAM2VirtualIDInventory> inventories, Completion completion) {
        ClusterApiCaller cc = getApiCallerByAccount(project.getLinkedAccountUuid());
        if (cc == null) {
            debug("beforeAddIam2VirtualIdsToProject", project.getLinkedAccountUuid());
            completion.success();
            return;
        }

        CollectionUtils.safeForEach(inventories, inventory -> {
            ZakuResponse<ZakuApiStructures.ZakuUser> response = cc.caller.createUser(
                    inventory.getName(), inventory.getDescription()
            );

            if (!response.isSuccess()) {
                logger.debug(String.format("failed to create user on container, because %s", response.getError()));
            }
        });

        ZakuResponse<ZakuApiStructures.ZakuProject> projectZakuResponse =
                cc.caller.createProject(project.getUuid(),
                        project.getName(), project.getDescription());
        if (!projectZakuResponse.isSuccess()) {
            logger.debug(String.format("failed to create project on container, because %s", projectZakuResponse.getError()));
        }

        ZakuResponse<String> response = cc.caller.addUsersToProjects(
                inventories.stream()
                        .map(IAM2VirtualIDInventory::getName)
                        .collect(Collectors.toList()),
                Collections.singletonList(project.getUuid()));
        if (!response.isSuccess()) {
            completion.fail(response.getError());
            return;
        }

        completion.success();
    }

    @Override
    public void beforeRemoveIam2VirtualIdsFromProject(IAM2ProjectInventory project, List<String> usernames, Completion completion) {
        ClusterApiCaller cc = getApiCallerByAccount(project.getLinkedAccountUuid());
        if (cc == null) {
            debug("beforeRemoveIam2VirtualIdsFromProject", project.getLinkedAccountUuid());
            completion.success();
            return;
        }

        ZakuResponse<String> response = cc.caller.removeUsersFromProjects(
                usernames,
                Collections.singletonList(project.getUuid()));
        if (!response.isSuccess()) {
            completion.fail(response.getError());
            return;
        }

        completion.success();
    }

    private List<ZakuApiStructures.QuotaParam> fromQuotaPairsToQuotaParam(Map<String, Quota.QuotaPair> pairs) {
        return pairs.entrySet()
                .stream()
                .filter(entry -> cloudContainerQuotaMappings.containsKey(entry.getKey()))
                .map(entry -> {
                    ZakuApiStructures.QuotaParam param = new ZakuApiStructures.QuotaParam();
                    param.setName(cloudContainerQuotaMappings.get(entry.getKey()));
                    param.setValue(entry.getValue().getValue());
                    return param;
                }).collect(Collectors.toList());
    }

    private void initQuotaMappings() {
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_CPU_NUM.getName(),
                        "requests.cpu");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_CPU_NUM_LIMITATION.getName(),
                        "limits.cpu");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_REQUESTED_MEMORY_SIZE.getName(),
                        "requests.memory");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_MEMORY_SIZE_LIMITATION.getName(),
                        "limits.memory");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_DATA_VOLUME_SIZE.getName(),
                        "requests.storage");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_GROUP_NUM.getName(),
                        "pods");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_SERVICE_NUM.getName(),
                        "services");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_LOAD_BALANCE_SERVICE_NUM.getName(),
                        "services.loadbalancers");
        cloudContainerQuotaMappings
                .put(IAM2ContainerQuotaGlobalConfig.CONTAINER_GPU_VIDEO_RAM_SIZE.getName(),
                        "requests.nvidia.com/gpumem");
    }

    @Override
    public boolean start() {
        initQuotaMappings();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void beforeUpdateQuota(String quotaName, long newValue, String accountUuid) {
        if (!cloudContainerQuotaMappings.containsKey(quotaName)) {
            logger.debug(String.format("skip IAM2ContainerManager's quota extension point, because quota: %s " +
                    "is not required by container", quotaName));
            return;
        }

        ClusterApiCaller cc = getApiCallerByAccount(accountUuid);
        if (cc == null) {
            debug("beforeUpdateQuota", accountUuid);
            return;
        }

        List<ZakuApiStructures.QuotaParam> params = new ArrayList<>();
        ZakuApiStructures.QuotaParam param = new ZakuApiStructures.QuotaParam();
        param.setName(cloudContainerQuotaMappings.get(quotaName));
        param.setValue(newValue);
        params.add(param);
        ZakuResponse<ZakuApiStructures.ZakuQuota[]> results = cc.caller
                .setProjectQuota(cc.projectUuid,
                        cc.clusterId,
                        params);

        if (!results.isSuccess()) {
            throw new OperationFailureException(
                    operr("failed to update quota on of container %s",
                            cc.containerManagerUuid)
            );
        }
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIGetIAM2ProjectContainerClusterCandidatesMsg) {
            handle((APIGetIAM2ProjectContainerClusterCandidatesMsg) msg);
        } else if (msg instanceof APIGetIAM2ProjectRepositoryMsg) {
            handle((APIGetIAM2ProjectRepositoryMsg) msg);
        } else if (msg instanceof APIGetIAM2ProjectContainerImagesMsg) {
            handle((APIGetIAM2ProjectContainerImagesMsg) msg);
        } else if (msg instanceof APIGetIAM2ProjectContainerImageTagsMsg) {
            handle((APIGetIAM2ProjectContainerImageTagsMsg) msg);
        } else if (msg instanceof APISetIAM2ProjectContainerClusterMsg) {
            handle((APISetIAM2ProjectContainerClusterMsg) msg);
        }
    }

    private void handle(APISetIAM2ProjectContainerClusterMsg msg) {
        APISetIAM2ProjectContainerClusterEvent evt = new APISetIAM2ProjectContainerClusterEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                setIAM2ProjectContainerCluster(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getSyncSignature() {
                return String.format("set-iam2-project-%s-container-ref", msg.getUuid());
            }

            @Override
            public String getName() {
                return String.format("set-iam2-project-%s-with-container-cluster-%s", msg.getUuid(), msg.getClusterId());
            }
        });
    }

    private void setIAM2ProjectContainerCluster(APISetIAM2ProjectContainerClusterMsg msg, Completion completion) {
        IAM2ProjectVO project = dbf.findByUuid(msg.getUuid(), IAM2ProjectVO.class);
        if (project == null) {
            completion.fail(operr("failed to setup project[uuid: %s] container," +
                    " because not found project in db"));
            return;
        }
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("iam2-project-%s-container-cluster-setup-chain", msg.getUuid()));
        chain.then(new ShareFlow() {
            String originalClusterId = null;
            String originalContainerUuid = null;
            boolean clusterNotChanged = false;
            boolean containerNotChanged = false;

            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "project-container-cluster-tag-recreate";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (IAM2ContainerSystemTags.PROJECT_CONTAINER_MANAGER_CLUSTER.hasTag(msg.getUuid())) {
                            Map<String, String> tokens = IAM2ContainerSystemTags.PROJECT_CONTAINER_MANAGER_CLUSTER.getTokensByResourceUuid(msg.getUuid());
                            originalClusterId = tokens.get(IAM2ContainerSystemTags.CLUSTER_TOKEN);
                            originalContainerUuid = tokens.get(IAM2ContainerSystemTags.CONTAINER_TOKEN);
                        }

                        clusterNotChanged = msg.getClusterId().toString().equals(originalClusterId);
                        containerNotChanged = msg.getContainerUuid().equals(originalContainerUuid);

                        if (clusterNotChanged && containerNotChanged) {
                            logger.trace(String.format("original cluster id is %s, container uuid is %s, no need to change", originalClusterId, originalContainerUuid));
                            trigger.next();
                            return;
                        }

                        SystemTagCreator creator = IAM2ContainerSystemTags.PROJECT_CONTAINER_MANAGER_CLUSTER.newSystemTagCreator(msg.getUuid());
                        creator.setTagByTokens(map(
                                e(IAM2ContainerSystemTags.CONTAINER_TOKEN, msg.getContainerUuid()),
                                e(IAM2ContainerSystemTags.CLUSTER_TOKEN, msg.getClusterId())
                        ));
                        creator.recreate = true;
                        creator.create();
                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (clusterNotChanged && containerNotChanged) {
                            trigger.rollback();
                            return;
                        }

                        if (originalClusterId == null || originalContainerUuid == null) {
                            logger.debug("empty originalClusterId and originalContainerUuid" +
                                    " before delete current tag for the sake of rollback");
                            IAM2ContainerSystemTags.PROJECT_CONTAINER_MANAGER_CLUSTER.delete(msg.getUuid());
                            trigger.rollback();
                            return;
                        }

                        logger.debug(String.format("rollback system tag to original cluster id %s" +
                                " and original container uuid %s", originalClusterId, originalContainerUuid));
                        SystemTagCreator creator = IAM2ContainerSystemTags.PROJECT_CONTAINER_MANAGER_CLUSTER.newSystemTagCreator(msg.getUuid());
                        creator.setTagByTokens(map(
                                e(IAM2ContainerSystemTags.CONTAINER_TOKEN, originalClusterId),
                                e(IAM2ContainerSystemTags.CLUSTER_TOKEN, originalContainerUuid)
                        ));
                        creator.recreate = true;
                        creator.create();
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "container-setup-flow";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        ClusterApiCaller cc = getApiCallerByAccount(project.getLinkedAccountUuid());
                        if (cc == null) {
                            debug("container-setup-flow", project.getLinkedAccountUuid());
                            trigger.next();
                            return;
                        }

                        initQuotas(cc, project.getUuid(), project.getLinkedAccountUuid(), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success();
                    }
                });
            }
        }).start();
    }

    private void handle(APIGetIAM2ProjectContainerImageTagsMsg msg) {
        APIGetIAM2ProjectContainerImageTagsReply reply = new APIGetIAM2ProjectContainerImageTagsReply();

        ContainerManagementEndpointVO vm = Q.New(ContainerManagementEndpointVO.class)
                .limit(1)
                .find();

        Integer projectId = Integer.valueOf(msg.getProjectId());
        Long repositoryId = Long.valueOf(msg.getRepositoryId());
        String imageName = msg.getImageName();
        Integer limit = msg.getLimit();
        Integer start = msg.getStart();

        String queryParameters = String.format("?limit=%s&start=%s", String.valueOf(limit), String.valueOf(start));

        List<ContainerImageTagInventory> inventories = new ArrayList<>();
        ZakuApiCaller caller = new ZakuApiCaller();
        caller.setClient(new ZakuHttpClient(vm.getManagementIp(), vm.getManagementPort(),
                "ze", vm.getAccessKeyId(), vm.getAccessKeySecret()));

        ZakuResponse<ZakuApiStructures.ImageTagPage> response = caller.getContainerImageTags(projectId, repositoryId, imageName, queryParameters);
        if (!response.isSuccess()) {
            logger.info(String.format("fail to get image tags because of %s", response.getError().toString()));
            reply.setError(response.getError());
            bus.reply(msg, reply);
            return;
        }

        if (response.getContent() == null) {
            reply.setError(operr("fail to get image tags, because of content is null"));
            bus.reply(msg, reply);
            return;
        }

        ZakuApiStructures.ImageTagPage imageTagPage = response.getContent();
        if (imageTagPage == null) {
            reply.setError(operr("fail to get image tags, because of image tag page is null"));
            bus.reply(msg, reply);
            return;
        }
        List<ContainerImageTagInventory> inventories1 = imageTagPage.getResult();
        inventories.addAll(inventories1);
        reply.setInventories(inventories);
        reply.setTotal(imageTagPage.getTotalCount());

        bus.reply(msg, reply);
    }

    private void handle(APIGetIAM2ProjectContainerImagesMsg msg) {
        APIGetIAM2ProjectContainerImagesReply reply = new APIGetIAM2ProjectContainerImagesReply();

        ContainerManagementEndpointVO vm = Q.New(ContainerManagementEndpointVO.class)
                .limit(1)
                .find();

        Integer projectId = Integer.valueOf(msg.getProjectId());
        Long repositoryId = Long.valueOf(msg.getRepositoryId());
        Integer limit = msg.getLimit();
        Integer start = msg.getStart();

        String queryParameters = String.format("?limit=%s&start=%s", String.valueOf(limit), String.valueOf(start));

        List<ContainerImageInventory> inventories = new ArrayList<>();
        ZakuApiCaller caller = new ZakuApiCaller();
        caller.setClient(new ZakuHttpClient(vm.getManagementIp(), vm.getManagementPort(), "ze", vm.getAccessKeyId(), vm.getAccessKeySecret()));

        ZakuResponse<ZakuApiStructures.ImagePage> response = caller.getContainerImagesInRegistry(projectId, repositoryId, queryParameters);
        if (!response.isSuccess()) {
            reply.setError(response.getError());
            bus.reply(msg, reply);
            return;
        }

        if (response.getContent() == null) {
            reply.setError(operr("fail to get images, because of content is null"));
            bus.reply(msg, reply);
            return;
        }

        ZakuApiStructures.ImagePage imagePage = response.getContent();
        List<ContainerImageInventory> inventories1 = imagePage.getResult();

        inventories.addAll(inventories1);
        reply.setInventories(inventories);
        reply.setTotal(imagePage.getTotalCount());
        bus.reply(msg, reply);
    }

    private List<ProjectRepositoryInventory> getRepositoriesInProject(ZakuApiCaller caller, Integer projectId) {
        List<ProjectRepositoryInventory> inventories = new ArrayList<>();
        if (projectId == null || projectId.equals(0)) {
            logger.info("no project id, so fail to get repositories in project");
            return Collections.emptyList();
        }
        ZakuResponse<ZakuApiStructures.RepositoryPage> response = caller.getRepositoriesInProject(Long.valueOf(projectId));
        if (response == null) {
            logger.info(String.format("no response, so fail to get repositories in project: %s", String.valueOf(projectId)));
            return Collections.emptyList();
        }
        if (response.getContent() == null) {
            logger.info(String.format("no content in response, so fail to get repositories in project: %s", String.valueOf(projectId)));
            return Collections.emptyList();
        }
        List<ZakuApiStructures.RepositoryPage> repositoryPages = Arrays.asList(response.getContent());
        repositoryPages.forEach(repositoryPage-> {
            List<ProjectRepositoryInventory> inventories1 = repositoryPage.getResult();
            // workaround jira: ZSTAC-68808, zaku api returns other project repositories.
            List<ProjectRepositoryInventory> filteredInv = inventories1.stream()
                    .filter(repository -> repository.getZeProjectID().equals(projectId)).collect(Collectors.toList());
            inventories.addAll(filteredInv);
        });
        return inventories;
    }

    private List<ProjectRepositoryInventory> getRepositoriesInAllProjects(ZakuApiCaller caller, ZakuResponse<ZakuApiStructures.ZakuProject[]> projects) {

        if (projects == null) {
            logger.info("no projects info, so fail to get repositories in project list");
            return Collections.emptyList();
        }

        if (projects.getContent() == null || projects.getContent().length == 0) {
            logger.info("no projects content info, so fail to get repositories in project list");
            return Collections.emptyList();
        }

        List<ProjectRepositoryInventory> projectRepositoryInventories = new ArrayList<>();
        List<Integer> projectIds = Arrays.stream(projects.getContent()).map(ZakuApiStructures.ZakuProject::getID).collect(Collectors.toList());
        logger.info(String.format("project id list:[%s]", projectIds.toString()));

        if (projectIds == null || projectIds.isEmpty()) {
            logger.info("projects' id are missing, so return empty repositories list");
            return Collections.emptyList();
        }

        projectIds.forEach(projectId -> {
            projectRepositoryInventories.addAll(getRepositoriesInProject(caller, projectId));

        });
        return projectRepositoryInventories;
    }

    private void handle(APIGetIAM2ProjectRepositoryMsg msg) {
        APIGetIAM2ProjectRepositoryReply reply = new APIGetIAM2ProjectRepositoryReply();

        ContainerManagementEndpointVO vm = Q.New(ContainerManagementEndpointVO.class)
                .limit(1)
                .find();

        ZakuApiCaller caller = new ZakuApiCaller();
        caller.setClient(new ZakuHttpClient(vm.getManagementIp(), vm.getManagementPort(), "ze", vm.getAccessKeyId(), vm.getAccessKeySecret()));

        ZakuResponse<ZakuApiStructures.ZakuProject[]> response = caller.getAuthorizedProjects();
        if (!response.isSuccess()) {
            reply.setError(response.getError());
            bus.reply(msg, reply);
            return;
        }

        if (response.getContent() == null) {
            reply.setError(operr("fail to get authorized projects, because of content is null"));
            bus.reply(msg, reply);
            return;
        }

        List<ProjectRepositoryInventory> inventories1 = getRepositoriesInAllProjects(caller, response);
        reply.setInventories(inventories1);
        bus.reply(msg, reply);
    }

    private void handle(APIGetIAM2ProjectContainerClusterCandidatesMsg msg) {
        APIGetIAM2ProjectContainerClusterCandidatesReply reply
                = new APIGetIAM2ProjectContainerClusterCandidatesReply();

        // TODO: change to get all container management endpoints
        List<ContainerManagementEndpointVO> vmList = Q.New(ContainerManagementEndpointVO.class)
                .list();

        List<ContainerClusterInventory> inventories = new ArrayList<>();
        new While<>(vmList).each((cvm, wc) -> {
            ZakuApiCaller caller = new ZakuApiCaller();
            caller.setClient(new ZakuHttpClient(cvm.getManagementIp(), cvm.getManagementPort(), "ze", cvm.getAccessKeyId(), cvm.getAccessKeySecret()));

            ZakuResponse<ZakuApiStructures.ZakuCluster[]> response = caller.getClusters();
            if (!response.isSuccess()) {
                wc.addError(response.getError());
                wc.done();
                return;
            }

            if (response.getContent() == null) {
                wc.done();
                return;
            }

            inventories.addAll(ContainerClusterInventory.valueOf(response.getContent(), cvm));
            wc.done();
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    reply.setError(errorCodeList.getCauses().get(0));
                } else {
                    reply.setInventories(inventories);
                }

                bus.reply(msg, reply);
            }
        });
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(SERVICE_ID);
    }

    @Override
    public ApiClient getContainerClient(String accountUuid) {
        ClusterApiCaller cc = getApiCallerByAccount(accountUuid);
        if (cc == null || cc.caller == null) {
            return null;
        }

        ZakuResponse<ZakuApiStructures.ClusterConfigView> clusterConfigViewZakuResponse = cc.caller
                .getClusterKubeConfig(cc.getClusterId());
        if (!clusterConfigViewZakuResponse.isSuccess()) {
            logger.debug(String.format(
                    "failed to get cluster config of project %s quota from container %s cluster %s",
                    cc.projectUuid,
                    cc.containerManagerUuid,
                    cc.clusterId)
            );
            return null;
        }

        ApiClient client;
        if (CoreGlobalProperty.UNIT_TEST_ON) {
            client = new ApiClient();
            return client;
        }

        try {
            client =
                    ClientBuilder.kubeconfig(KubeConfig.loadKubeConfig(new StringReader(clusterConfigViewZakuResponse.getContent().getKubeConfig()))).build();
            Configuration.setDefaultApiClient(client);
            return client;
        } catch (FileNotFoundException e) {
            throw new OperationFailureException(operr("fail to find kube config at path: %s", e.toString()));
        } catch (IOException e) {
            throw new OperationFailureException(operr("fail to open kube config file: %s", e.toString()));
        }
    }

    @Override
    public void afterAddRolesToVirtualID(List<String> roleUuids, String virtualIDUuid, String targetAccountUuid, String organizationUuid, NoErrorCompletion completion) {
        if (!IAM2PlatformAdmin.isIAM2PlatformAdmin(virtualIDUuid) && !IAM2PlatformUser.isPlatformUser(virtualIDUuid)) {
            logger.debug("Not a platform admin or platform user, skip afterAddRolesToVirtualID");
            completion.done();
            return;
        }

        ClusterApiCaller cc = getDefaultApiCaller();
        if (cc == null) {
            debug("afterAddRolesToVirtualID", targetAccountUuid);
            completion.done();
            return;
        }

        String name = Q.New(IAM2VirtualIDVO.class)
                .eq(IAM2VirtualIDVO_.uuid, virtualIDUuid)
                .select(IAM2VirtualIDVO_.name)
                .findValue();
        if (name == null) {
            logger.debug(String.format("no virtual id found by uuid %s", virtualIDUuid));
            completion.done();
            return;
        }
        ZakuResponse<String> response = cc.caller.setAdmin(name);
        if (!response.isSuccess()) {
            logger.debug(String.format("failed to set user %s as admin on container, because %s", name, response.getError()));
        }

        completion.done();
    }

    @Override
    public void beforeRemoveRolesFromVirtualID(List<String> roleUuids, String virtualIDUuid, String targetAccountUuid, String organizationUuid, NoErrorCompletion completion) {
        completion.done();
    }

    @Override
    public void afterRemoveRolesFromVirtualID(String virtualIDUuid, NoErrorCompletion completion) {
        if (IAM2PlatformAdmin.isIAM2PlatformAdmin(virtualIDUuid) || IAM2PlatformUser.isPlatformUser(virtualIDUuid)) {
            logger.debug("Platform admin or platform user, skip afterRemoveRolesFromVirtualID");
            completion.done();
            return;
        }

        ClusterApiCaller cc = getDefaultApiCaller();
        if (cc == null) {
            debug("afterRemoveRolesFromVirtualID", null);
            completion.done();
            return;
        }

        String name = Q.New(IAM2VirtualIDVO.class)
                .eq(IAM2VirtualIDVO_.uuid, virtualIDUuid)
                .select(IAM2VirtualIDVO_.name)
                .findValue();
        if (name == null) {
            logger.debug(String.format("no virtual id found by uuid %s", virtualIDUuid));
            completion.done();
            return;
        }
        ZakuResponse<String> response = cc.caller.unsetAdmin(name);
        if (!response.isSuccess()) {
            logger.debug(String.format("failed to unset user %s as admin on container, because %s", name, response.getError()));
        }

        completion.done();
    }

    @Override
    public void afterCreateIAM2VirtualID(IAM2VirtualIDVO vid) {
        ClusterApiCaller cc = getDefaultApiCaller();
        if (cc == null) {
            debug("afterCreateIAM2VirtualID", vid.getName());
            return;
        }

        ZakuResponse<ZakuApiStructures.ZakuUser> response = cc.caller.createUser(vid.getName(), vid.getDescription());
        if (!response.isSuccess()) {
            logger.debug(String.format("failed to create user on container, because %s", response.getError()));
        }
    }

    static class ClusterApiCaller {
        private ZakuApiCaller caller;
        private long clusterId;
        private String containerManagerUuid;
        private String projectUuid;

        public long getClusterId() {
            return clusterId;
        }

        public void setClusterId(long clusterId) {
            this.clusterId = clusterId;
        }

        public String getProjectUuid() {
            return projectUuid;
        }

        public void setProjectUuid(String projectUuid) {
            this.projectUuid = projectUuid;
        }
    }

    // TODO: change default api caller once multi endpoints are supported
    private ClusterApiCaller getDefaultApiCaller() {
        ClusterApiCaller caller = new ClusterApiCaller();

        List<ContainerManagementEndpointVO> endpoints = Q.New(ContainerManagementEndpointVO.class)
                .limit(1)
                .list();
        if (endpoints.isEmpty()) {
            return null;
        }

        ContainerManagementEndpointVO endpoint = endpoints.get(0);
        caller.containerManagerUuid = endpoint.getUuid();

        ZakuApiCaller zc = new ZakuApiCaller();
        zc.setClient(new ZakuHttpClient(endpoint.getManagementIp(), endpoint.getManagementPort(), "ze", endpoint.getAccessKeyId(), endpoint.getAccessKeySecret()));
        caller.caller = zc;

        return caller;
    }

    public ClusterApiCaller getApiCallerByAccount(String accountUuid) {
        IAM2ProjectAccountRefVO projectAccountRefVO = Q.New(IAM2ProjectAccountRefVO.class)
                .eq(IAM2ProjectAccountRefVO_.accountUuid, accountUuid)
                .find();
        if (projectAccountRefVO == null) {
            return null;
        }

        if (!IAM2ContainerSystemTags.PROJECT_CONTAINER_MANAGER_CLUSTER.hasTag(projectAccountRefVO.getProjectUuid())) {
            return null;
        }

        ClusterApiCaller caller = new ClusterApiCaller();

        Map<String, String> parameters = IAM2ContainerSystemTags.PROJECT_CONTAINER_MANAGER_CLUSTER.getTokensByResourceUuid(projectAccountRefVO.getProjectUuid());
        String clusterId = parameters.get(IAM2ContainerSystemTags.CLUSTER_TOKEN);
        String containerManagerUuid = parameters.get(IAM2ContainerSystemTags.CONTAINER_TOKEN);

        if (containerManagerUuid == null) {
            logger.warn(String.format("no container manager uuid found for project %s", projectAccountRefVO.getProjectUuid()));
            return null;
        }
        ContainerManagementEndpointVO vm = Q.New(ContainerManagementEndpointVO.class)
                .eq(ContainerManagementEndpointVO_.uuid, containerManagerUuid)
                .find();
        if (vm == null) {
            return null;
        }

        caller.containerManagerUuid = containerManagerUuid;
        caller.clusterId = Long.parseLong(clusterId);
        ZakuApiCaller zc = new ZakuApiCaller();
        zc.setClient(new ZakuHttpClient(vm.getManagementIp(), vm.getManagementPort(), "ze", vm.getAccessKeyId(), vm.getAccessKeySecret()));
        caller.caller = zc;
        caller.projectUuid = projectAccountRefVO.getProjectUuid();

        return caller;
    }
}
