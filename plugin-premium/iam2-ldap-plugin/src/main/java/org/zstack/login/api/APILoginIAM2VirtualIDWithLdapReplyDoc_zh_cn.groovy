package org.zstack.login.api

import org.zstack.header.errorcode.ErrorCode
import org.zstack.header.identity.SessionInventory

doc {

	title "通过AD/Ldap认证登录租户管理用户的结果"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.login.api.APILoginIAM2VirtualIDWithLdapReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "3.5.1"
		clz ErrorCode.class
	}
	ref {
		name "inventory"
		path "org.zstack.login.api.APILoginIAM2VirtualIDWithLdapReply.inventory"
		desc "null"
		type "SessionInventory"
		since "3.5.1"
		clz SessionInventory.class
	}
}
