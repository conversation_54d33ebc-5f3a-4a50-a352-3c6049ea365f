package org.zstack.login.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

@RestRequest(
        path = "/iam2/virtual-id/ldap/uid",
        method = HttpMethod.POST,
        parameterName = "params",
        responseClass = APICreateIAM2VirtualIDFromLdapUidEvent.class
)
public class APICreateIAM2VirtualIDFromLdapUidMsg extends APICreateMessage {
    @APIParam
    private String ldapUid;

    public String getLdapUid() {
        return ldapUid;
    }

    public void setLdapUid(String ldapUid) {
        this.ldapUid = ldapUid;
    }

    public static APICreateIAM2VirtualIDFromLdapUidMsg __example__() {
        APICreateIAM2VirtualIDFromLdapUidMsg msg = new APICreateIAM2VirtualIDFromLdapUidMsg();
        msg.setLdapUid("ou=Employee,uid=test");

        return msg;
    }
}
