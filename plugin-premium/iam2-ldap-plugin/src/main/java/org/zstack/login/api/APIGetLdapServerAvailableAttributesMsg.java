package org.zstack.login.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.rest.RestRequest;
import org.zstack.ldap.LdapServerVO;

@RestRequest(
        path = "/ldap/server/attributes/{uuid}",
        method = HttpMethod.GET,
        responseClass = APIGetLdapServerAvailableAttributesReply.class
)
public class APIGetLdapServerAvailableAttributesMsg extends APISyncCallMessage {
    @APIParam(resourceType = LdapServerVO.class)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
