package org.zstack.login;

public class IAM2LdapSyncRule {
    private String name;
    private String attribute;
    private SyncRuleAttributeType type;
    private Boolean optional;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }

    public SyncRuleAttributeType getType() {
        return type;
    }

    public void setType(SyncRuleAttributeType type) {
        this.type = type;
    }

    public Boolean getOptional() {
        return optional;
    }

    public void setOptional(Boolean optional) {
        this.optional = optional;
    }
}
