package org.zstack.login;

import org.zstack.header.tag.TagDefinition;
import org.zstack.ldap.LdapServerVO;
import org.zstack.tag.PatternedSystemTag;

@TagDefinition
public class IAM2LdapSystemTags {
    public static String VIRTUAL_ID_SYNC_CONFIGURATION_TOKEN = "virtualIDSyncConfiguration";
    public static PatternedSystemTag VIRTUAL_ID_SYNC_CONFIGURATION = new PatternedSystemTag(String.format("virtualIDSyncConfiguration::{%s}", VIRTUAL_ID_SYNC_CONFIGURATION_TOKEN), LdapServerVO.class);

    public static String ORGANIZATION_SYNC_CONFIGURATION_TOKEN = "organizationSyncConfiguration";
    public static PatternedSystemTag ORGANIZATION_SYNC_CONFIGURATION = new PatternedSystemTag(String.format("organizationSyncConfiguration::{%s}", ORGANIZATION_SYNC_CONFIGURATION_TOKEN), LdapServerVO.class);
}
