package org.zstack.login;

import java.util.ArrayList;
import java.util.List;

public class IAM2LdapGroup {
    private String groupUuid;
    /**
     * members match distinguishName attributes
     */
    private String dn;
    /**
     * members match objectGUID attributes
     */
    private String objectGUID;
    private String name;
    private String description;
    /**
     * members match ldap member attributes
     */
    private List<String> members = new ArrayList<>();
    /**
     * members match ldap memberOf attributes
     */
    private String memberOf;
    /**
     * members match ldap managedBy attributes
     */
    private String managedBy;
    private boolean existsNode = false;
    private boolean root = false;

    public boolean isRoot() {
        return root;
    }

    public void setRoot(boolean root) {
        this.root = root;
    }

    public String getManagedBy() {
        return managedBy;
    }

    public void setManagedBy(String managedBy) {
        this.managedBy = managedBy;
    }

    public String getDn() {
        return dn;
    }

    public void setDn(String dn) {
        this.dn = dn;
    }

    public String getObjectGUID() {
        return objectGUID;
    }

    public void setObjectGUID(String objectGUID) {
        this.objectGUID = objectGUID;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getMembers() {
        return members;
    }

    public void setMembers(List<String> members) {
        this.members = members;
    }

    public String getMemberOf() {
        return memberOf;
    }

    public void setMemberOf(String memberOf) {
        this.memberOf = memberOf;
    }

    public String getGroupUuid() {
        return groupUuid;
    }

    public void setGroupUuid(String groupUuid) {
        this.groupUuid = groupUuid;
    }

    public boolean isExistsNode() {
        return existsNode;
    }

    public void setExistsNode(boolean existsNode) {
        this.existsNode = existsNode;
    }
}
