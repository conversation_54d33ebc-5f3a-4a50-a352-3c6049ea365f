package org.zstack.login;

import com.google.gson.JsonSyntaxException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.NameAwareAttribute;
import org.springframework.ldap.filter.AndFilter;
import org.springframework.ldap.filter.HardcodedFilter;
import org.springframework.ldap.filter.NotFilter;
import org.springframework.ldap.filter.NotPresentFilter;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.*;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.UpdateQuery;
import org.zstack.core.progress.ProgressReportService;
import org.zstack.core.thread.*;
import org.zstack.core.workflow.SimpleFlowChain;
import org.zstack.header.AbstractService;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.core.*;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.identity.*;
import org.zstack.header.identity.login.*;
import org.zstack.header.longjob.LongJobConstants;
import org.zstack.header.longjob.SubmitLongJobMsg;
import org.zstack.header.longjob.SubmitLongJobReply;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.message.NeedReplyMessage;
import org.zstack.iam2.*;
import org.zstack.iam2.api.Attribute;
import org.zstack.iam2.attribute.organization.OrganizationSupervisor;
import org.zstack.iam2.entity.*;
import org.zstack.iam2.message.*;
import org.zstack.ldap.*;
import org.zstack.login.api.*;
import org.zstack.login.entity.LdapResourceRefInventory;
import org.zstack.login.entity.LdapResourceRefVO;
import org.zstack.login.entity.LdapResourceRefVO_;
import org.zstack.login.message.*;
import org.zstack.resourceconfig.ResourceConfig;
import org.zstack.resourceconfig.ResourceConfigFacade;
import org.zstack.tag.PatternedSystemTag;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.SystemTagUtils;
import org.zstack.tag.TagManager;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.TagUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import javax.naming.InvalidNameException;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.ldap.LdapName;
import javax.naming.ldap.Rdn;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.*;
import static org.zstack.core.progress.ProgressReportService.reportProgress;
import static org.zstack.utils.CollectionDSL.map;

public class LdapLoginManagerImpl extends AbstractService implements LdapLoginManager, LoginBackend, AddLdapExtensionPoint,
        DeleteLdapServerExtensionPoint, ChangeIAM2VirtualIDTypeExtensionPoint, DeleteIAM2OrganizationExtensionPoint,
        UpdateLdapServerExtensionPoint {
    private static final CLogger logger = Utils.getLogger(LdapLoginManagerImpl.class);

    public static final IAM2ResourceSourceType type = new IAM2ResourceSourceType("Ldap");

    protected static final LdapEffectiveScope scope = new LdapEffectiveScope(IAM2Constant.LOGIN_TYPE);
    protected static final LoginType loginType = new LoginType(IAM2LdapConstant.LOGIN_TYPE);

    public static LdapUtil ldapUtil = Platform.New(() -> new LdapUtil(IAM2Constant.LOGIN_TYPE));

    public static final String ROOT_ORG_NAME_STRING = "ldap-organization-tree-root";

    @Autowired
    private CloudBus bus;
    @Autowired
    private ThreadFacade thdf;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    private ProgressReportService progRpt;
    @Autowired
    private EventFacade evtf;
    @Autowired
    private ResourceConfigFacade rcf;
    @Autowired
    private ResourceDestinationMaker resourceDestinationMaker;

    private static boolean loadingOrganizationsFromDB = false;

    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof SyncLdapServerMsg) {
            handle((SyncLdapServerMsg) msg);
        } else if (msg instanceof CreateIAM2VirtualIDFromLdapUidMsg) {
            handle((CreateIAM2VirtualIDFromLdapUidMsg) msg);
        } else if (msg instanceof UpdateIAM2VirtualIDSyncedFromLdapMsg) {
            handle((UpdateIAM2VirtualIDSyncedFromLdapMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(UpdateIAM2VirtualIDSyncedFromLdapMsg msg) {
        UpdateIAM2VirtualIDSyncedFromLdapReply r = new UpdateIAM2VirtualIDSyncedFromLdapReply();
        String virtualIDName;

        if (msg.getName() != null) {
            virtualIDName = msg.getName();

            // if duplicate name
            if (Q.New(IAM2VirtualIDVO.class)
                    .eq(IAM2VirtualIDVO_.name, virtualIDName)
                    .notEq(IAM2VirtualIDVO_.uuid, msg.getUuid()).isExists()) {
                virtualIDName += String.format("-%s", Platform.getUuid());
            }
        } else {
            virtualIDName = String.format("ZStack-iam2-user-%s", Platform.getUuid());
        }

        UpdateIAM2VirtualIDMsg umsg = new UpdateIAM2VirtualIDMsg();
        umsg.setUuid(msg.getUuid());
        umsg.setDescription(msg.getDescription());
        umsg.setAttributes(msg.getAttributes());
        umsg.setName(virtualIDName);
        bus.makeTargetServiceIdByResourceUuid(umsg, IAM2Manager.SERVICE_ID, umsg.getVirtualIDUuid());
        bus.send(umsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    r.setError(reply.getError());
                }

                UpdateQuery sql = SQL.New(LdapResourceRefVO.class)
                        .eq(LdapResourceRefVO_.resourceUuid, msg.getUuid())
                        .set(LdapResourceRefVO_.ldapUid, msg.getLdapUid());

                if (msg.getGlobalUid() != null) {
                    sql.set(LdapResourceRefVO_.ldapGlobalUuid, msg.getGlobalUid());
                }

                sql.update();

                bus.reply(msg, r);
            }
        });
    }

    private void handle(CreateIAM2VirtualIDFromLdapUidMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                CreateIAM2VirtualIDFromLdapUidReply reply = new CreateIAM2VirtualIDFromLdapUidReply();

                if (Q.New(LdapResourceRefVO.class).eq(LdapResourceRefVO_.ldapUid, msg.getLdapUid()).isExists()) {
                    LdapResourceRefVO vo = Q.New(LdapResourceRefVO.class).eq(LdapResourceRefVO_.ldapUid, msg.getLdapUid()).find();
                    reply.setInventory(vo.toInventory());
                    bus.reply(msg, reply);
                    chain.next();
                    return;
                }

                doCreateIAM2VirtualIDFromLdapUid(msg, new ReturnValueCompletion<LdapResourceRefInventory>(msg) {
                    @Override
                    public void success(LdapResourceRefInventory inv) {
                        reply.setInventory(inv);
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("create-iam2-virtual-id-from-ldap-uid-%s", msg.getLdapUid());
            }
        });
    }

    private void handle(SyncLdapServerMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("sync-ldap-server-%s", msg.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                SyncLdapServerReply reply = new SyncLdapServerReply();

                if (loadingOrganizationsFromDB) {
                    reply.setError(operr("ZStack is loading ldap organizations from DB now, can not execute sync operation"));
                    bus.reply(msg, reply);
                    chain.next();
                    return;
                }

                doSyncLdapServer(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    @Override
    public void afterChangeIAM2VirtualIDTypeExtension(String virtualIDUuid) {
        SQL.New(LdapResourceRefVO.class).eq(LdapResourceRefVO_.resourceUuid, virtualIDUuid).delete();
    }

    @Override
    public void afterDeleteOrganization(String organizationUuid) {
        SQL.New(LdapResourceRefVO.class).eq(LdapResourceRefVO_.resourceUuid, organizationUuid).delete();
    }

    @Override
    public void afterUpdateLdapServer(APIUpdateLdapServerMsg msg) {
        String ldapServerUuid = msg.getLdapServerUuid();

        if (!Q.New(LdapServerVO.class)
                .eq(LdapServerVO_.uuid, ldapServerUuid)
                .eq(LdapServerVO_.scope, scope.toString())
                .isExists()) {
            return;
        }

        refreshOrganizationSyncConfig(msg.getSystemTags(), ldapServerUuid);
        refreshVirtualIDSyncConfig(msg.getSystemTags(), ldapServerUuid);
    }

    private void refreshVirtualIDSyncConfig(List<String> systemTags, String ldapServerUuid) {
        PatternedSystemTag tag =  IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION;
        String token = IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION_TOKEN;

        String newTagValue = null;
        if (systemTags != null && !systemTags.isEmpty()) {
            newTagValue = SystemTagUtils.findTagValue(systemTags, tag, token);
        }

        String originTagValue = IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION.getTokenByResourceUuid(ldapServerUuid, IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION_TOKEN);

        String tagValue;
        if (!StringUtils.isEmpty(newTagValue)) {
            tagValue = newTagValue;
        } else {
            tagValue = originTagValue;
        }

        if (StringUtils.isEmpty(tagValue)) {
            return;
        }

        SystemTagCreator creator = tag.newSystemTagCreator(ldapServerUuid);
        creator.recreate = true;
        creator.setTagByTokens(map(CollectionDSL.e(token, tagValue)));
        creator.create();
    }


    private void refreshOrganizationSyncConfig(List<String> systemTags, String ldapServerUuid) {
        PatternedSystemTag tag =  IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION;
        String token = IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION_TOKEN;

        String newTagValue = null;
        if (systemTags != null && !systemTags.isEmpty()) {
            newTagValue = SystemTagUtils.findTagValue(systemTags, tag, token);
        }

        String originTagValue = IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION.getTokenByResourceUuid(ldapServerUuid, IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION_TOKEN);

        String tagValue;
        if (!StringUtils.isEmpty(newTagValue)) {
            tagValue = newTagValue;
        } else {
            tagValue = originTagValue;
        }

        if (StringUtils.isEmpty(tagValue)) {
            return;
        }

        SystemTagCreator creator = tag.newSystemTagCreator(ldapServerUuid);
        creator.recreate = true;
        creator.setTagByTokens(map(CollectionDSL.e(token, tagValue)));
        creator.create();
    }

    @Override
    public LoginType getLoginType() {
        return loginType;
    }

    @Override
    public void login(LoginContext loginContext, ReturnValueCompletion<LoginSessionInfo> completion) {
        loginInWithoutCreateSession(loginContext.getUsername(), loginContext.getPassword(), new ReturnValueCompletion<String>(completion) {
            @Override
            public void success(String virtualIDUuid) {
                completion.success(LoginIAM2VirtualIDSessionHelper.getLoginSessionInfo(virtualIDUuid));
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    public boolean authenticate(String username, String password) {
        return ldapUtil.isValid(username, password);
    }

    @Override
    public String getUserIdByName(String username) {
        return ldapUtil.getFullUserDn(username);
    }

    @Override
    public void collectUserInfoIntoContext(LoginContext loginContext) {
        loginContext.setUserUuid(getUserIdByName(loginContext.getUsername()));
    }

    @Override
    public List<AdditionalAuthFeature> getRequiredAdditionalAuthFeature() {
        return Collections.singletonList(LoginAuthConstant.basicLoginControl);
    }

    class SyncVirtualID {
        Object ldapEntry;
        Map<String, List<IAM2LdapSyncRule>> attributeRuleMap;
        List<Attribute> attributesToCreate = new ArrayList<>();
        String globalUuidKey;

        void run(Completion syncCompletion) {
            NeedReplyMessage syncVidMessage = null;
            try {
                syncVidMessage = getMessage();
            } catch (Exception e) {
                syncCompletion.fail(operr("Failed to sync ldap entry[], because %s", e.getMessage()));
                return;
            }

            bus.makeLocalServiceId(syncVidMessage, LdapLoginManager.SERVICE_ID);
            bus.send(syncVidMessage, new CloudBusCallBack(syncCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        syncCompletion.fail(reply.getError());
                        return;
                    }

                    syncCompletion.success();
                }
            });
        }

        void transformLdapAttributesToMessageParams(List<NameAwareAttribute> attributes, CreateIAM2VirtualIDFromLdapUidMsg cmsg) throws NoSuchFieldException, IllegalAccessException {
            for (NameAwareAttribute attr : attributes) {
                if (!attr.getID().equals(globalUuidKey)) {
                    continue;
                }

                try {
                    cmsg.setLdapGlobalUuid(ldapUtil.decodeGlobalUuid(attr));
                } catch (NamingException e) {
                    logger.debug("Failed to get global UUID");
                }

                break;
            }

            for (Map.Entry<String, List<IAM2LdapSyncRule>> entry : attributeRuleMap.entrySet()) {
                Optional<NameAwareAttribute> opt = attributes.stream()
                        .filter(attr -> attr.getID().equals(entry.getKey()))
                        .findFirst();

                if (!opt.isPresent()) {
                    for (IAM2LdapSyncRule rule : entry.getValue()) {
                        if (rule.getType().equals(SyncRuleAttributeType.SYSTEM)) {
                            continue;
                        }

                        Attribute attrToCreate = new Attribute();
                        attrToCreate.setName(rule.getName());
                        attrToCreate.setValue(null);

                        attributesToCreate.add(attrToCreate);
                    }

                    continue;
                }

                NameAwareAttribute nameAwareAttribute = opt.get();
                for (IAM2LdapSyncRule rule : entry.getValue()) {
                    if (rule.getType().equals(SyncRuleAttributeType.SYSTEM)) {
                        try {
                            Field field = cmsg.getClass().getDeclaredField(rule.getName());
                            field.setAccessible(true);
                            field.set(cmsg, nameAwareAttribute.get(0).toString());
                        } catch (NamingException e) {
                            logger.debug("Failed to assign field, because " + e.getMessage());
                            continue;
                        }
                    } else {
                        Attribute attrToCreate = new Attribute();
                        attrToCreate.setName(rule.getName());
                        try {
                            attrToCreate.setValue(nameAwareAttribute.get(0).toString());
                        } catch (NamingException e) {
                            logger.debug("Failed to translate custom attribute");
                            continue;
                        }

                        attributesToCreate.add(attrToCreate);
                    }
                }
            }
        }

        NeedReplyMessage getMessage() throws NoSuchFieldException, IllegalAccessException {
            Map<String, Object> map = (Map<String, Object>) ldapEntry;

            List<NameAwareAttribute> attributes = (List<NameAwareAttribute>) map.get("attributes");

            CreateIAM2VirtualIDFromLdapUidMsg cmsg = new CreateIAM2VirtualIDFromLdapUidMsg();

            transformLdapAttributesToMessageParams(attributes, cmsg);

            String dn = (String) map.get(LdapConstant.LDAP_DN_KEY);
            String existsVid = getVirtualIDDnOrGUidExists(dn, cmsg.getLdapGlobalUuid());
            if (existsVid != null) {
                UpdateIAM2VirtualIDSyncedFromLdapMsg umsg = new UpdateIAM2VirtualIDSyncedFromLdapMsg();
                umsg.setUuid(existsVid);
                umsg.setLdapUid(dn);
                umsg.setGlobalUid(cmsg.getLdapGlobalUuid());
                umsg.setDescription(cmsg.getDescription());
                umsg.setAttributes(attributesToCreate);
                umsg.setName(cmsg.getName());

                return umsg;
            }

            logger.debug("Attribute to create is" + attributesToCreate);
            cmsg.setLdapUid(dn);
            cmsg.setAttributes(attributesToCreate);
            return cmsg;
        }

        private String getVirtualIDDnOrGUidExists(String dn, String guid) {
            if (dn != null) {
                String vid = Q.New(LdapResourceRefVO.class)
                        .select(LdapResourceRefVO_.resourceUuid)
                        .eq(LdapResourceRefVO_.resourceType, IAM2VirtualIDVO.class.getSimpleName())
                        .eq(LdapResourceRefVO_.ldapUid, dn)
                        .findValue();

                if (vid != null) {
                    return vid;
                }
            }

            if (guid != null) {
                String vid = Q.New(LdapResourceRefVO.class)
                        .select(LdapResourceRefVO_.resourceUuid)
                        .eq(LdapResourceRefVO_.resourceType, IAM2VirtualIDVO.class.getSimpleName())
                        .eq(LdapResourceRefVO_.ldapGlobalUuid, guid)
                        .findValue();

                if (vid != null) {
                    return vid;
                }
            }

            return null;
        }
    }

    class SyncLdapOrganizationByGroup {
        Map<String, List<IAM2LdapSyncRule>> attributeRuleMap;
        List<Object> result;
        String uuid;
        String globalKey = ldapUtil.getGlobalUuidKey();
        String memeberOf = ldapUtil.getMemberOfKey();
        String memeber = ldapUtil.getMemberKey();
        IAM2LdapGroup zstackNode;
        List<IAM2LdapGroup> existGroup;

        void run(NoErrorCompletion syncOrganizationCompletion) {
            existGroup = loadGroupCache();

            Optional opt = existGroup.stream().filter(group -> group.getDn().equals(ROOT_ORG_NAME_STRING)).findAny();

            IAM2LdapGroup rootNode = new IAM2LdapGroup();
            rootNode.setRoot(true);
            if (!existGroup.isEmpty() && opt.isPresent()) {
                rootNode = (IAM2LdapGroup) opt.get();
            } else {
                rootNode.setDn(ROOT_ORG_NAME_STRING);
                rootNode.setName(ROOT_ORG_NAME_STRING);
                rootNode.setObjectGUID(uuid);
                rootNode.setGroupUuid(Platform.getUuid());
            }

            zstackNode = rootNode;

            List<IAM2LdapGroup> organizationNodes = transformSearchResultToOrganizations(result);
            organizationNodes.add(rootNode);

            buildOrganizations(organizationNodes);
            syncOrganizationCompletion.done();
        }

        private void buildOrganizations(List<IAM2LdapGroup> groups) {
            for (IAM2LdapGroup group : groups) {
                if (group.isExistsNode()) {
                    updateOrganization(group);
                } else {
                    createOrganization(group);
                }
            }

            buildRelationsBetweenVirtualIDAndOrganization(groups);
        }

        private void buildRelationsBetweenVirtualIDAndOrganization(List<IAM2LdapGroup> groups) {
            for (IAM2LdapGroup group : groups) {
                if (group.isRoot()) {
                    continue;
                }

                updateVirtualIDsOfOrganization(group);
                connectOrganizations(group);
            }
        }

        private void connectOrganizations(IAM2LdapGroup group) {
            if (group.getName().equals(ROOT_ORG_NAME_STRING)) {
                return;
            }

            if (group.getMemberOf() == null) {
                SQL.New(IAM2OrganizationVO.class)
                        .eq(IAM2OrganizationVO_.uuid, group.getGroupUuid())
                        .set(IAM2OrganizationVO_.parentUuid, zstackNode.getGroupUuid())
                        .set(IAM2OrganizationVO_.rootOrganizationUuid, zstackNode.getGroupUuid())
                        .update();
                return;
            }

            String organizationUuid = Q.New(LdapResourceRefVO.class)
                    .select(LdapResourceRefVO_.resourceUuid)
                    .eq(LdapResourceRefVO_.ldapUid, group.getMemberOf())
                    .eq(LdapResourceRefVO_.resourceType, IAM2OrganizationVO.class.getSimpleName())
                    .findValue();

            if (organizationUuid == null) {
                SQL.New(IAM2OrganizationVO.class)
                        .eq(IAM2OrganizationVO_.uuid, group.getGroupUuid())
                        .set(IAM2OrganizationVO_.parentUuid, zstackNode.getGroupUuid())
                        .set(IAM2OrganizationVO_.rootOrganizationUuid, zstackNode.getGroupUuid())
                        .update();
            } else {
                String rootOrganizationUuid = Q.New(IAM2OrganizationVO.class)
                        .select(IAM2OrganizationVO_.rootOrganizationUuid)
                        .eq(IAM2OrganizationVO_.uuid, organizationUuid)
                        .findValue();

                SQL.New(IAM2OrganizationVO.class)
                        .eq(IAM2OrganizationVO_.uuid, group.getGroupUuid())
                        .set(IAM2OrganizationVO_.parentUuid, organizationUuid)
                        .set(IAM2OrganizationVO_.rootOrganizationUuid, rootOrganizationUuid)
                        .update();
            }
        }

        private void updateVirtualIDsOfOrganization(IAM2LdapGroup group) {
            if (group.getMembers() == null || group.getMembers().isEmpty()) {
                return;
            }

            List<String> virtualIDUuids = Q.New(LdapResourceRefVO.class)
                    .select(LdapResourceRefVO_.resourceUuid)
                    .in(LdapResourceRefVO_.ldapUid, group.getMembers())
                    .eq(LdapResourceRefVO_.resourceType, IAM2VirtualIDVO.class.getSimpleName())
                    .listValues();

            if (virtualIDUuids.isEmpty()) {
                return;
            }

            SQL.New(IAM2VirtualIDOrganizationRefVO.class)
                    .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, group.getGroupUuid())
                    .notIn(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid, virtualIDUuids)
                    .hardDelete();

            List<String> alreadyInOrganizationVids = Q.New(IAM2VirtualIDOrganizationRefVO.class)
                    .select(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid)
                    .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, group.getGroupUuid())
                    .listValues();

            virtualIDUuids.removeAll(alreadyInOrganizationVids);


            if (!virtualIDUuids.isEmpty()) {
                SQL.New(IAM2VirtualIDOrganizationRefVO.class)
                        .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, IAM2Constant.INITIAL_ORGANIZATION_DEFAULT_UUID)
                        .in(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid, virtualIDUuids)
                        .hardDelete();
            }

            for (String vid : virtualIDUuids) {
                IAM2VirtualIDOrganizationRefVO ref = new IAM2VirtualIDOrganizationRefVO();
                ref.setVirtualIDUuid(vid);
                ref.setOrganizationUuid(group.getGroupUuid());
                dbf.persist(ref);
            }
        }

        private void updateOrganization(IAM2LdapGroup group) {
            if (group.getName() != null) {
                SQL.New(IAM2OrganizationVO.class)
                        .eq(IAM2OrganizationVO_.uuid, group.getGroupUuid())
                        .set(IAM2OrganizationVO_.name, group.getName())
                        .set(IAM2OrganizationVO_.description, group.getDescription())
                        .update();
            }
        }

        private void createOrganization(IAM2LdapGroup group) {
            NeedReplyMessage cmsg = buildCreateOrganizationMessage(group);
            bus.makeLocalServiceId(cmsg, IAM2Manager.SERVICE_ID);
            MessageReply reply = bus.call(cmsg);

            if (!reply.isSuccess()) {
                throw new OperationFailureException(operr("Failed to sync organizations, because %s", reply.getError().getReadableDetails()));
            } else {
                IAM2OrganizationInventory inv = ((CreateIAM2OrganizationReply) reply).getInventory();
                LdapResourceRefVO ref = new LdapResourceRefVO();
                ref.setUuid(Platform.getUuid());
                ref.setResourceUuid(inv.getUuid());
                ref.setLdapUid(group.getDn());
                ref.setLdapServerUuid(uuid);
                ref.setResourceType(IAM2OrganizationVO.class.getSimpleName());
                ref.setLdapGlobalUuid(group.getObjectGUID());
                dbf.persist(ref);
            }
        }

        private NeedReplyMessage buildCreateOrganizationMessage(IAM2LdapGroup group) {
            CreateIAM2OrganizationMsg cmsg = new CreateIAM2OrganizationMsg();

            cmsg.setResourceUuid(group.getGroupUuid());
            // check if it is top organization
            if (group.isRoot()) {
                cmsg.setType(OrganizationType.Company);
            } else {
                cmsg.setType(OrganizationType.Department);
            }

            cmsg.setName(group.getName());
            cmsg.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            cmsg.setSrcType(type.toString());
            cmsg.setDescription(group.getDescription());

            if (group.getManagedBy() != null) {
                String vid = Q.New(LdapResourceRefVO.class)
                        .select(LdapResourceRefVO_.resourceUuid)
                        .eq(LdapResourceRefVO_.resourceType, IAM2VirtualIDVO.class.getSimpleName())
                        .eq(LdapResourceRefVO_.ldapUid, group.getManagedBy())
                        .findValue();

                if (vid != null) {
                    Attribute attribute = new Attribute();
                    attribute.setName(OrganizationSupervisor.ORGANIZATION_SUPERVISOR.getName());
                    attribute.setValue(vid);
                    cmsg.setAttributes(Collections.singletonList(attribute));
                }
            }

            return cmsg;
        }

        private List<IAM2LdapGroup> transformSearchResultToOrganizations(List<Object> result) {
            List<IAM2LdapGroup> groups = new ArrayList<>();
            for (Object ldapEntry : result) {
                IAM2LdapGroup group = transformLdapEntryToOrganizationNode(ldapEntry);

                if (group.getDn() == null) {
                    throw new OperationFailureException(operr("Failed to transform ldap entry to organization ndoe"));
                }

                groups.add(group);
            }

            return groups;
        }

        private void parseLdapAttributeValueToGroupParam(IAM2LdapGroup node, List<NameAwareAttribute> attributes) {
            for (Object attr : attributes) {
                NameAwareAttribute nameAwareAttribute = (NameAwareAttribute) attr;

                // get attribute id, for example 'email'
                String id = nameAwareAttribute.getID();

                if (id.equals(globalKey)) {
                    try {
                        node.setObjectGUID(ldapUtil.decodeGlobalUuid(nameAwareAttribute));
                    } catch (NamingException e) {
                        break;
                    }
                } else if (id.equals("managedBy")) {
                    try {
                        node.setManagedBy(nameAwareAttribute.get(0).toString());
                    } catch (NamingException e) {
                        break;
                    }

                } else if (id.equals(memeberOf)) {
                    try {
                        node.setMemberOf(nameAwareAttribute.get(0).toString());
                    } catch (NamingException e) {
                        break;
                    }
                } else if (id.equals(memeber)) {
                    try {
                        NamingEnumeration it = nameAwareAttribute.getAll();
                        while (it.hasMore()) {
                            node.getMembers().add(it.next().toString());
                        }
                    } catch (NamingException e) {
                        break;
                    }
                } else {
                    List<IAM2LdapSyncRule> rules = attributeRuleMap.get(id);

                    if (rules == null) {
                        continue;
                    }

                    for (IAM2LdapSyncRule rule : rules) {
                        if (rule.getType().equals(SyncRuleAttributeType.SYSTEM)) {
                            try {
                                Field field = node.getClass().getDeclaredField(rule.getName());
                                field.setAccessible(true);
                                field.set(node, nameAwareAttribute.get(0).toString());
                            } catch (NoSuchFieldException | IllegalAccessException | NamingException e) {
                                logger.debug("Failed to assign field, because " + e.getMessage());
                                break;
                            }
                        }
                    }
                }
            }
        }

        private IAM2LdapGroup transformLdapEntryToOrganizationNode(Object ldapEntry) {
            IAM2LdapGroup node = new IAM2LdapGroup();
            Map<String, Object> map = (Map<String, Object>) ldapEntry;
            String dn = (String) map.get(LdapConstant.LDAP_DN_KEY);

            List<NameAwareAttribute> attributes = (List<NameAwareAttribute>) map.get("attributes");

            parseLdapAttributeValueToGroupParam(node, attributes);

            node.setDn(dn);
            node.setGroupUuid(Platform.getUuid());

            // find exists node uuid
            Optional existNodeOpt = existGroup.stream()
                    .filter(existNode -> (existNode.getObjectGUID() != null)
                            && (existNode.getDn().equals(node.getDn())
                            || existNode.getObjectGUID().equals(node.getObjectGUID()))).findAny();

            if (existNodeOpt.isPresent()) {
                node.setGroupUuid(((IAM2LdapGroup)existNodeOpt.get()).getGroupUuid());
                node.setExistsNode(true);
            }

            return node;
        }
    }

    class SyncLdapOrganizationByOU {
        Map<String, List<IAM2LdapSyncRule>> attributeRuleMap;
        List<Object> result;
        String uuid;
        List<LdapOrganizationTree.LdapOrganizationNode> existNodeList;

        void run(NoErrorCompletion syncOrganizationCompletion) {
            LdapOrganizationTree tree;

            existNodeList = loadOrganizationCache();

            Optional opt = existNodeList.stream().filter(node -> node.getLevel() == 0).findAny();
            if (!existNodeList.isEmpty() && opt.isPresent()) {
                tree = new LdapOrganizationTree(ROOT_ORG_NAME_STRING, uuid, ((LdapOrganizationTree.LdapOrganizationNode)opt.get()).getOrganizationUuid());
                tree.getRoot().setExistNode(true);
            } else {
                tree = new LdapOrganizationTree(ROOT_ORG_NAME_STRING, uuid, Platform.getUuid());
            }

            Map<Integer, List<LdapOrganizationTree.LdapOrganizationNode>> nodeMap = divideOrganizationWithLevel(result);

            if (logger.isTraceEnabled()) {
                logger.debug("node map divide by org level is" + JSONObjectUtil.toJsonString(nodeMap));
            }

            buildLdapOrganizationTree(tree, nodeMap);

            buildZStackOrganizations(tree);

            existNodeList = nodeMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
            existNodeList.add(tree.getRoot());

            syncOrganizationCompletion.done();
        }

        private String getOrganizationDnStringFromFullDn(String dn) throws InvalidNameException {
            LdapName ln = new LdapName(dn);
            ln.getRdns().get(0).getValue();

            StringBuilder sb = new StringBuilder();
            for(Rdn rdn : ln.getRdns()) {
                if(!rdn.getType().equalsIgnoreCase(LdapConstant.LDAP_OU_KEY)) {
                    continue;
                }

                sb.append(rdn.toString());
                sb.append(",");
            }

            return sb.toString();
        }

        private void parseLdapAttributeValueToOrgParam(LdapOrganizationTree.LdapOrganizationNode node, List<NameAwareAttribute> attributes) {
            for (Object attr : attributes) {
                NameAwareAttribute nameAwareAttribute = (NameAwareAttribute) attr;

                // get attribute id, for example 'email'
                String id = nameAwareAttribute.getID();

                if (id.equals(ldapUtil.getGlobalUuidKey())) {
                    try {
                        node.setObjectUid(ldapUtil.decodeGlobalUuid(nameAwareAttribute));
                    } catch (NamingException e) {
                        break;
                    }
                } else if (id.equals("managedBy")) {
                    try {
                        node.setManagedBy(nameAwareAttribute.get(0).toString());
                    } catch (NamingException e) {
                        break;
                    }

                } else {
                    List<IAM2LdapSyncRule> rules = attributeRuleMap.get(id);

                    if (rules == null) {
                        continue;
                    }

                    for (IAM2LdapSyncRule rule : rules) {
                        if (rule.getType().equals(SyncRuleAttributeType.SYSTEM)) {
                            try {
                                Field field = node.getClass().getDeclaredField(rule.getName());
                                field.setAccessible(true);
                                field.set(node, nameAwareAttribute.get(0).toString());
                            } catch (NoSuchFieldException | IllegalAccessException | NamingException e) {
                                logger.debug("Failed to assign field, because " + e.getMessage());
                                break;
                            }
                        }
                    }
                }
            }
        }

        private LdapOrganizationTree.LdapOrganizationNode transformLdapEntryToOrganizationNode(Object ldapEntry) {
            LdapOrganizationTree.LdapOrganizationNode node = new LdapOrganizationTree.LdapOrganizationNode();
            Map<String, Object> map = (Map<String, Object>) ldapEntry;
            String dn = (String) map.get(LdapConstant.LDAP_DN_KEY);

            try {
                String orgDn = getOrganizationDnStringFromFullDn(dn);
                int orgLevel = orgDn.split(",").length;

                List<NameAwareAttribute> attributes = (List<NameAwareAttribute>) map.get("attributes");

                parseLdapAttributeValueToOrgParam(node, attributes);

                node.setDn(dn);
                node.setLevel(orgLevel);
                node.setOrgDn(orgDn);
                node.setOrganizationUuid(Platform.getUuid());

                // find exists node uuid
                Optional existNodeOpt = existNodeList.stream()
                        .filter(existNode -> (existNode.getDn() != null && existNode.getDn().equals(node.getDn())) ||
                                (existNode.getObjectUid() != null && existNode.getObjectUid().equals(node.getObjectUid())))
                                .findAny();

                if (existNodeOpt.isPresent()) {
                    node.setOrganizationUuid(((LdapOrganizationTree.LdapOrganizationNode)existNodeOpt.get()).getOrganizationUuid());
                    node.setExistNode(true);
                }

                return node;
            } catch (NamingException e) {
                throw new OperationFailureException(operr("failed to sync ldap organization"));
            }
        }

        private Map<Integer, List<LdapOrganizationTree.LdapOrganizationNode>> divideOrganizationWithLevel(List<Object> result) {
            Map<Integer, List<LdapOrganizationTree.LdapOrganizationNode>> nodeMap = new HashMap<>();

            for (Object ldapEntry : result) {
                LdapOrganizationTree.LdapOrganizationNode node = transformLdapEntryToOrganizationNode(ldapEntry);

                if (node.getDn() == null) {
                    throw new OperationFailureException(operr("Failed to transform ldap entry to organization ndoe"));
                }

                nodeMap.computeIfAbsent(node.getLevel(), x -> new ArrayList<>());
                nodeMap.get(node.getLevel()).add(node);
            }

            return nodeMap;
        }

        private void buildZStackOrganizations(LdapOrganizationTree tree) {
            executeOrganizationCreation(tree.getRoot());
        }

        private void executeOrganizationCreation(LdapOrganizationTree.LdapOrganizationNode node) {
            if (node.isExistNode()) {
                updateLdapOrganizationNode(node);
            } else {
                createLdapOrganizationNode(node);
            }

            if (node.getChildren() == null) {
                return;
            }

            node.getChildren().forEach(this::executeOrganizationCreation);
        }

        private void updateLdapOrganizationNode(LdapOrganizationTree.LdapOrganizationNode node) {
            if (node.getName() == null && node.getParent() == null) {
                return;
            }

            UpdateQuery sql = SQL.New(IAM2OrganizationVO.class)
                    .eq(IAM2OrganizationVO_.uuid, node.getOrganizationUuid())
                    .set(IAM2OrganizationVO_.name, node.getName());

            if (node.getParent() != null) {
                sql.set(IAM2OrganizationVO_.parentUuid, node.getParent().getOrganizationUuid());
            }

            sql.update();

            updateReferenceBetweenOrganizationAndVirtualIDs(node, node.getOrganizationUuid());
        }

        private void createLdapOrganizationNode(LdapOrganizationTree.LdapOrganizationNode node) {
            NeedReplyMessage cmsg = buildCreateOrganizationMessage(node);
            bus.makeLocalServiceId(cmsg, IAM2Manager.SERVICE_ID);
            MessageReply reply = bus.call(cmsg);

            if (!reply.isSuccess()) {
                throw new OperationFailureException(operr("Failed to sync organizations, because %s", reply.getError().getReadableDetails()));
            } else {
                IAM2OrganizationInventory inv = ((CreateIAM2OrganizationReply) reply).getInventory();
                LdapResourceRefVO ref = new LdapResourceRefVO();
                ref.setUuid(Platform.getUuid());
                ref.setResourceUuid(inv.getUuid());
                ref.setLdapUid(node.getDn());
                ref.setLdapServerUuid(uuid);
                ref.setResourceType(IAM2OrganizationVO.class.getSimpleName());
                ref.setLdapGlobalUuid(node.getObjectUid());
                dbf.persist(ref);
            }

            createReferenceBetweenOrganizationAndVirtualIDs(node, ((CreateIAM2OrganizationReply) reply).getInventory().getUuid());
        }

        private void createReferenceBetweenOrganizationAndVirtualIDs(LdapOrganizationTree.LdapOrganizationNode node, String organizationUuid) {
            // has dn and not root node
            if (node.getDn() != null && node.getParent() != null) {
                List<String> vids = SQL.New(String.format("select resourceUuid from LdapResourceRefVO where" +
                                " ldapUid like '%%%s' and ldapUid not like '%%ou=%%%s' and resourceType = '%s'", node.getDn(), node.getDn(),
                        IAM2VirtualIDVO.class.getSimpleName()), String.class)
                        .list();

                if (!vids.isEmpty()) {
                    SQL.New(IAM2VirtualIDOrganizationRefVO.class)
                            .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, IAM2Constant.INITIAL_ORGANIZATION_DEFAULT_UUID)
                            .in(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid, vids)
                            .hardDelete();
                }

                for (String vid : vids) {
                    IAM2VirtualIDOrganizationRefVO ref = new IAM2VirtualIDOrganizationRefVO();
                    ref.setVirtualIDUuid(vid);
                    ref.setOrganizationUuid(organizationUuid);
                    dbf.persist(ref);
                }
            }
        }

        private void updateReferenceBetweenOrganizationAndVirtualIDs(LdapOrganizationTree.LdapOrganizationNode node, String organizationUuid) {
            // has dn and not root node
            if (node.getDn() != null && node.getParent() != null) {
                List<String> vids = SQL.New(String.format("select resourceUuid from LdapResourceRefVO where" +
                                " ldapUid like '%%%s' and ldapUid not like '%%ou=%%%s' and resourceType = '%s'", node.getDn(), node.getDn(),
                        IAM2VirtualIDVO.class.getSimpleName()), String.class)
                        .list();

                if (vids.isEmpty()) {
                    return;
                }

                List<String> alreadyInOrganizationVids = Q.New(IAM2VirtualIDOrganizationRefVO.class)
                        .select(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid)
                        .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, organizationUuid)
                        .listValues();

                SQL.New(IAM2VirtualIDOrganizationRefVO.class)
                        .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, organizationUuid)
                        .notIn(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid, vids)
                        .hardDelete();

                vids.removeAll(alreadyInOrganizationVids);

                if (!vids.isEmpty()) {
                    SQL.New(IAM2VirtualIDOrganizationRefVO.class)
                            .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, IAM2Constant.INITIAL_ORGANIZATION_DEFAULT_UUID)
                            .in(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid, vids)
                            .hardDelete();
                }

                for (String vid : vids) {
                    IAM2VirtualIDOrganizationRefVO ref = new IAM2VirtualIDOrganizationRefVO();
                    ref.setVirtualIDUuid(vid);
                    ref.setOrganizationUuid(organizationUuid);
                    dbf.persist(ref);
                }
            }
        }

        private NeedReplyMessage buildCreateOrganizationMessage(LdapOrganizationTree.LdapOrganizationNode node) {
            CreateIAM2OrganizationMsg cmsg = new CreateIAM2OrganizationMsg();

            cmsg.setResourceUuid(node.getOrganizationUuid());
            // check if it is top organization
            if (node.getParent() == null) {
                cmsg.setType(OrganizationType.Company);
            } else {
                cmsg.setType(OrganizationType.Department);
                cmsg.setParentUuid(node.getParent().getOrganizationUuid());
            }

            cmsg.setName(node.getDn());
            cmsg.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            cmsg.setSrcType(type.toString());

            if (node.getManagedBy() != null) {
                String vid = Q.New(LdapResourceRefVO.class)
                        .select(LdapResourceRefVO_.resourceUuid)
                        .eq(LdapResourceRefVO_.resourceType, IAM2VirtualIDVO.class.getSimpleName())
                        .eq(LdapResourceRefVO_.ldapUid, node.getManagedBy())
                        .findValue();

                if (vid != null) {
                    Attribute attribute = new Attribute();
                    attribute.setName(OrganizationSupervisor.ORGANIZATION_SUPERVISOR.getName());
                    attribute.setValue(vid);
                    cmsg.setAttributes(Collections.singletonList(attribute));
                }
            }

            return cmsg;
        }

        private void buildLdapOrganizationTree(LdapOrganizationTree tree, Map<Integer, List<LdapOrganizationTree.LdapOrganizationNode>> nodeMap) {
            List<Integer> sortedKeys = nodeMap.keySet().stream().sorted(Integer::compare).collect(Collectors.toList());

            Integer lastLevel = null;
            for (Integer level : sortedKeys) {
                List<LdapOrganizationTree.LdapOrganizationNode> nodes = nodeMap.get(level);

                if (nodes.isEmpty()) {
                    continue;
                }

                if (lastLevel == null) {
                    nodes.forEach(node -> node.setParent(tree.getRoot()));
                    tree.getRoot().setChildren(nodes);
                } else {
                    List<LdapOrganizationTree.LdapOrganizationNode> lastLevelNodes = nodeMap.get(lastLevel);

                    // search all matched nodes in last level
                    // for example "OU=people" will be matched when current node's org dn is "OU=teachers,OU=people"
                    nodes.forEach(node -> {
                        Optional opt = lastLevelNodes.stream().filter(lastNode -> node.getOrgDn().contains(lastNode.getOrgDn())).findAny();

                        if (!opt.isPresent()) {
                            node.setParent(tree.getRoot());
                            tree.getRoot().getChildren().add(node);
                        } else {
                            LdapOrganizationTree.LdapOrganizationNode parentNode = (LdapOrganizationTree.LdapOrganizationNode) opt.get();

                            node.setParent(parentNode);

                            if (parentNode.getChildren() == null) {
                                parentNode.setChildren(new ArrayList<>());
                                parentNode.getChildren().add(node);
                            } else {
                                parentNode.getChildren().add(node);
                            }
                        }
                    });
                }

                lastLevel = level;
            }
        }
    }

    private void doSyncLdapServer(SyncLdapServerMsg msg, Completion completion) {
        String uuid = msg.getUuid();
        FlowChain chain = new SimpleFlowChain();
        chain.setName(String.format("sync-ldap-server-%s", uuid));
        chain.then(new NoRollbackFlow() {
            String __name__ = "sync-iam2-virtual-id";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                IAM2VirtualIDLdapSyncConfig config;
                if (!IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION.hasTag(uuid)) {
                    config = generateDefaultConfig();
                } else {
                    String configuration = IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION.getTokenByResourceUuid(uuid, IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION_TOKEN);
                    config = JSONObjectUtil.toObject(configuration, IAM2VirtualIDLdapSyncConfig.class);
                }

                Map<String, List<IAM2LdapSyncRule>> attributeRuleMap = new HashMap<>();
                for (IAM2LdapSyncRule rule : config.getRules()) {
                    attributeRuleMap.computeIfAbsent(rule.getAttribute(), x -> new ArrayList<>());
                    attributeRuleMap.get(rule.getAttribute()).add(rule);
                }

                Set<String> returnAttributeSet = new HashSet<>(attributeRuleMap.keySet());
                returnAttributeSet.addAll(Arrays.asList(LdapConstant.QUERY_LDAP_ENTRY_MUST_RETURN_ATTRIBUTES));
                returnAttributeSet.add(ldapUtil.getGlobalUuidKey());
                String[] returnAttributes = returnAttributeSet.toArray(new String[]{});

                AndFilter userFilter = new AndFilter();
                userFilter.and(new HardcodedFilter(IAM2LdapGlobalConfig.LDAP_USER_SYNC_FILTER.value()));

                // filter
                String filter = LdapSystemTags.LDAP_CLEAN_BINDING_FILTER.getTokenByResourceUuid(uuid, LdapSystemTags.LDAP_CLEAN_BINDING_FILTER_TOKEN);
                if(StringUtils.isNotEmpty(filter)){
                    userFilter.and(new NotFilter(new HardcodedFilter(filter)));
                }
                // allow list filter
                String allowListFilter = LdapSystemTags.LDAP_ALLOW_LIST_FILTER.getTokenByResourceUuid(uuid, LdapSystemTags.LDAP_ALLOW_LIST_FILTER_TOKEN);
                if(StringUtils.isNotEmpty(allowListFilter)){
                    userFilter.and(new HardcodedFilter(allowListFilter));
                }

                logger.debug("user filter is " + userFilter.toString());

                List<Object> result = ldapUtil.searchLdapEntry(userFilter.toString(), IAM2LdapGlobalConfig.LDAP_MAXIMUM_SYNC_USERS.value(Integer.class), returnAttributes, null, false);

                String gloablUuidKey = ldapUtil.getGlobalUuidKey();
                progRpt.reportProgressUntil(PROCESS_SYNC_LDAP_USER, (int) TimeUnit.MINUTES.toMillis(10));
                new While<>(result).each((object, whileCompletion) -> {
                    SyncVirtualID syncVirtualID = new SyncVirtualID();
                    syncVirtualID.ldapEntry = object;
                    syncVirtualID.attributeRuleMap = attributeRuleMap;
                    syncVirtualID.globalUuidKey = gloablUuidKey;
                    syncVirtualID.run(new Completion(whileCompletion) {
                        @Override
                        public void success() {
                            whileCompletion.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            logger.warn("skip updating virtual ID: " + errorCode.getDetails());
                            // Continue anyway.
                            // Interruption of this process will prevent the execution of
                            // the 'updating user's organization' operation.
                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        reportProgress(FINISH_SYNC_LDAP_USER);
                        trigger.next();
                    }
                });
            }

            private IAM2VirtualIDLdapSyncConfig generateDefaultConfig() {
                IAM2VirtualIDLdapSyncConfig config = new IAM2VirtualIDLdapSyncConfig();

                config.setRules(new ArrayList<>());

                IAM2LdapSyncRule nameRule = new IAM2LdapSyncRule();
                nameRule.setName("name");
                nameRule.setAttribute("distinguishedName");
                nameRule.setOptional(false);
                nameRule.setType(SyncRuleAttributeType.SYSTEM);

                config.getRules().add(nameRule);

                return config;
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "sync-ldap-organization";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION.hasTag(uuid)) {
                    logger.debug("no organization sync config detected, skip it");
                    trigger.next();
                    return;
                }

                String configuration = IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION.getTokenByResourceUuid(uuid, IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION_TOKEN);
                IAM2OrganizationLdapSyncConfig config = JSONObjectUtil.toObject(configuration, IAM2OrganizationLdapSyncConfig.class);
                Map<String, List<IAM2LdapSyncRule>> attributeRuleMap = new HashMap<>();
                for (IAM2LdapSyncRule rule : config.getRules()) {
                    attributeRuleMap.computeIfAbsent(rule.getAttribute(), x -> new ArrayList<>());
                    attributeRuleMap.get(rule.getAttribute()).add(rule);
                }

                Set<String> returnAttributeSet = new HashSet<>(attributeRuleMap.keySet());
                returnAttributeSet.add(ldapUtil.getGlobalUuidKey());
                returnAttributeSet.add(ldapUtil.getMemberOfKey());
                returnAttributeSet.add(ldapUtil.getMemberKey());
                String[] returnAttributes = returnAttributeSet.toArray(new String[]{});

                AndFilter orgFilter = new AndFilter();

                if (config.getStrategy().equals(OrganizationSyncStrategy.Organization)) {
                    orgFilter.and(new HardcodedFilter(IAM2LdapGlobalConfig.LDAP_ORGANIZATION_SYNC_FILTER.value()));
                } else {
                    orgFilter.and(new HardcodedFilter(IAM2LdapGlobalConfig.LDAP_GROUP_SYNC_FILTER.value()));
                }

                // filter
                String filter = LdapSystemTags.LDAP_CLEAN_BINDING_FILTER.getTokenByResourceUuid(uuid, LdapSystemTags.LDAP_CLEAN_BINDING_FILTER_TOKEN);
                if(StringUtils.isNotEmpty(filter)){
                    orgFilter.and(new NotFilter(new HardcodedFilter(filter)));
                }
                // allow list filter
                String allowListFilter = LdapSystemTags.LDAP_ALLOW_LIST_FILTER.getTokenByResourceUuid(uuid, LdapSystemTags.LDAP_ALLOW_LIST_FILTER_TOKEN);
                if(StringUtils.isNotEmpty(allowListFilter)){
                    orgFilter.and(new HardcodedFilter(allowListFilter));
                }

                logger.debug("organization filter is " + orgFilter);
                logger.debug("organization return attributes is " + returnAttributeSet);

                List<Object> result = ldapUtil.searchLdapEntry(orgFilter.toString(), IAM2LdapGlobalConfig.LDAP_MAXIMUM_SYNC_ORGANIZATIONS.value(Integer.class), returnAttributes, null, false);

                progRpt.reportProgressUntil(PROCESS_SYNC_LDAP_ORG, (int) TimeUnit.MINUTES.toMillis(10));

                if (config.getStrategy().equals(OrganizationSyncStrategy.Organization)) {
                    SyncLdapOrganizationByOU sync = new SyncLdapOrganizationByOU();
                    sync.attributeRuleMap = attributeRuleMap;
                    sync.result = result;
                    sync.uuid = uuid;
                    sync.run(new NoErrorCompletion(trigger) {
                        @Override
                        public void done() {
                            reportProgress(FINISH_SYNC_LDAP_ORG);
                            trigger.next();
                        }
                    });
                } else {
                    SyncLdapOrganizationByGroup sync = new SyncLdapOrganizationByGroup();
                    sync.attributeRuleMap = attributeRuleMap;
                    sync.result = result;
                    sync.uuid = uuid;
                    sync.run(new NoErrorCompletion(trigger) {
                        @Override
                        public void done() {
                            reportProgress(FINISH_SYNC_LDAP_ORG);
                            trigger.next();
                        }
                    });
                }
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "clean-stale-ldap-entry";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                reportProgress(START_CLEAN_UP_STALE_LDAP_BINDINGS);
                doCleanInvalidLdapIAM2VirtualIDBindings();
                doCleanupInvalidLdapIAM2OrganizationRefs();
                trigger.next();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).start();
    }

    private void doCleanupInvalidLdapIAM2OrganizationRefs() {

    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APILoginIAM2VirtualIDWithLdapMsg) {
            handle((APILoginIAM2VirtualIDWithLdapMsg) msg);
        } else if (msg instanceof APICreateIAM2VirtualIDLdapBindingMsg) {
            handle((APICreateIAM2VirtualIDLdapBindingMsg) msg);
        } else if (msg instanceof APIDeleteIAM2VirtualIDLdapBindingMsg) {
            handle((APIDeleteIAM2VirtualIDLdapBindingMsg) msg);
        } else if (msg instanceof APIGetCandidateLdapEntryForIAM2BindingMsg) {
            handle((APIGetCandidateLdapEntryForIAM2BindingMsg) msg);
        } else if (msg instanceof APICreateIAM2VirtualIDFromLdapUidMsg) {
            handle((APICreateIAM2VirtualIDFromLdapUidMsg) msg);
        } else if (msg instanceof APICleanInvalidLdapIAM2BindingMsg) {
            handle((APICleanInvalidLdapIAM2BindingMsg) msg);
        } else if (msg instanceof APISyncLdapServerMsg) {
            handle((APISyncLdapServerMsg) msg);
        } else if (msg instanceof APIGetLdapServerAvailableAttributesMsg) {
            handle((APIGetLdapServerAvailableAttributesMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private List<String> getLdapAttributes(String ldapServerUuid, String hardCodeFilter) {
        AndFilter userFilter = new AndFilter();
        userFilter.and(new HardcodedFilter(hardCodeFilter));
        List<Object> result = ldapUtil.searchLdapEntry(userFilter.toString(), 1, null, null, true);

        Map<String, Object> map = (Map<String, Object>) result.get(0);

        List<Object> attributesFromLdap = (List<Object>) map.get("attributes");

        if (logger.isTraceEnabled()) {
            logger.debug(String.format("Attributes of LDAP/AD server[uuid:%s] are %s", ldapServerUuid, JSONObjectUtil.toJsonString(attributesFromLdap)));
        }

        List<String> attributes = new ArrayList<>();
        for (Object attr : attributesFromLdap) {
            NameAwareAttribute nameAwareAttribute = (NameAwareAttribute) attr;

            // get attribute id, for example 'email'
            String id = nameAwareAttribute.getID();
            attributes.add(id);
        }

        return attributes;
    }

    private void handle(APIGetLdapServerAvailableAttributesMsg msg) {
        APIGetLdapServerAvailableAttributesReply reply = new APIGetLdapServerAvailableAttributesReply();
        reply.setInventories(new ArrayList<>());

        Map<String, Object> result = new HashMap<>();
        List<String> userAttributes = getLdapAttributes(msg.getUuid(), IAM2LdapGlobalConfig.LDAP_USER_SYNC_FILTER.value());
        result.put("user", userAttributes);

        reply.getInventories().add(result);

        result = new HashMap<>();
        List<String> orgAttribtues = getLdapAttributes(msg.getUuid(), IAM2LdapGlobalConfig.LDAP_ORGANIZATION_SYNC_FILTER.value());
        result.put("org", orgAttribtues);

        reply.getInventories().add(result);

        bus.reply(msg, reply);
    }

    private void handle(APISyncLdapServerMsg msg) {
        APISyncLdapServerEvent evt = new APISyncLdapServerEvent(msg.getId());

        if (!Q.New(LdapServerVO.class)
                .eq(LdapServerVO_.uuid, msg.getUuid())
                .eq(LdapServerVO_.scope, scope.toString())
                .isExists()) {
            throw new OperationFailureException(operr("Can not sync LDAP/AD server whose scope is not %s", scope.toString()));
        }

        SubmitLongJobMsg smsg = new SubmitLongJobMsg();
        smsg.setName("sync LDAP/AD server");
        smsg.setJobName(APISyncLdapServerMsg.class.getSimpleName());
        smsg.setJobData(JSONObjectUtil.toJsonString(msg));
        smsg.setAccountUuid(msg.getSession().getAccountUuid());
        bus.makeLocalServiceId(smsg, LongJobConstants.SERVICE_ID);
        bus.send(smsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                SubmitLongJobReply rly = reply.castReply();
                evt.setInventory(rly.getInventory());
                bus.publish(evt);
            }
        });
    }

    private List<String> doCleanInvalidLdapIAM2VirtualIDBindings() {
        List<LdapResourceRefVO> refList = Q.New(LdapResourceRefVO.class)
                .in(LdapResourceRefVO_.resourceType, Arrays.asList(IAM2VirtualIDVO.class.getSimpleName(), IAM2OrganizationVO.class.getSimpleName()))
                .list();

        ArrayList<String> iam2VirtualIDResourceIDList = new ArrayList<>();
        List<String> iam2OrganizationResourceIDList = new ArrayList<>();
        ArrayList<String> ldapResourceRefUuidList = new ArrayList<>();
        LdapTemplateContextSource ldapTemplateContextSource = ldapUtil.readLdapServerConfiguration();

        for (LdapResourceRefVO ldapResourceRefVO : refList) {
            // no data in ldap
            String ldapDn = ldapResourceRefVO.getLdapUid();

            boolean skipDnCheck = ldapResourceRefVO.getResourceType().equals(IAM2OrganizationVO.class.getSimpleName())
                    && Q.New(IAM2OrganizationVO.class).isNull(IAM2OrganizationVO_.parentUuid).isExists();

            if (skipDnCheck) {
                continue;
            }

            if(!ldapUtil.validateDnExist(ldapTemplateContextSource, ldapDn)){
                if (ldapResourceRefVO.getResourceType().equals(IAM2VirtualIDVO.class.getSimpleName())) {
                    iam2VirtualIDResourceIDList.add(ldapResourceRefVO.getResourceUuid());
                } else {
                    logger.debug("orgniaztion exists");
                    iam2OrganizationResourceIDList.add(ldapResourceRefVO.getResourceUuid());
                }

                ldapResourceRefUuidList.add(ldapResourceRefVO.getUuid());
                continue;
            }

            // filter
            String filter = LdapSystemTags.LDAP_CLEAN_BINDING_FILTER.getTokenByResourceUuid(ldapResourceRefVO.getLdapServerUuid(), LdapSystemTags.LDAP_CLEAN_BINDING_FILTER_TOKEN);
            // allow list filter
            String allowListFilter = LdapSystemTags.LDAP_ALLOW_LIST_FILTER.getTokenByResourceUuid(ldapResourceRefVO.getLdapServerUuid(), LdapSystemTags.LDAP_ALLOW_LIST_FILTER_TOKEN);
            if(StringUtils.isEmpty(filter) && StringUtils.isEmpty(allowListFilter)){
                continue;
            }
            if(StringUtils.isNotEmpty(filter)){
                HardcodedFilter hardcodedFilter = new HardcodedFilter(filter);
                if(ldapUtil.validateDnExist(ldapTemplateContextSource, ldapDn, hardcodedFilter)){
                    iam2VirtualIDResourceIDList.add(ldapResourceRefVO.getResourceUuid());
                    ldapResourceRefUuidList.add(ldapResourceRefVO.getUuid());
                }
            }

            if(StringUtils.isNotEmpty(allowListFilter)){
                HardcodedFilter hardcodedAllowListFilter = new HardcodedFilter(allowListFilter);
                if(!ldapUtil.validateDnExist(ldapTemplateContextSource, ldapDn, hardcodedAllowListFilter)){
                    iam2VirtualIDResourceIDList.add(ldapResourceRefVO.getResourceUuid());
                    ldapResourceRefUuidList.add(ldapResourceRefVO.getUuid());
                }
            }
        }

        if (!iam2VirtualIDResourceIDList.isEmpty()) {
            // remove ldap bindings
            dbf.removeByPrimaryKeys(ldapResourceRefUuidList, LdapResourceRefVO.class);
            // mark iam2 virtual ids as staled
            if (!iam2VirtualIDResourceIDList.isEmpty()) {
                SQL.New(IAM2VirtualIDVO.class)
                        .in(IAM2VirtualIDVO_.uuid, iam2VirtualIDResourceIDList)
                        .set(IAM2VirtualIDVO_.state, IAM2State.Staled)
                        .update();

                IAM2CanonicalEvents.IAM2VirtualIDStateChangedData d = new IAM2CanonicalEvents.IAM2VirtualIDStateChangedData();
                d.setVirtualIDUuids(iam2VirtualIDResourceIDList);
                d.setFrom(IAM2State.Enabled);
                d.setTo(IAM2State.Staled);
                evtf.fire(IAM2CanonicalEvents.IAM2_VIRTUALIDS_STATE_CHANGED_PATH, d);
            }

            // mark all iam2 organization ids as staled
            if (!iam2OrganizationResourceIDList.isEmpty()) {
                SQL.New(IAM2OrganizationVO.class)
                        .in(IAM2OrganizationVO_.uuid, iam2OrganizationResourceIDList)
                        .set(IAM2OrganizationVO_.state, IAM2State.Staled)
                        .update();
            }

        }

        List<String> result = new ArrayList<>();
        result.addAll(iam2VirtualIDResourceIDList);
        result.addAll(iam2OrganizationResourceIDList);
        return result;
    }

    @SyncThread(signature = "clean-invalid-ldap-iam2-binding")
    private void handle(APICleanInvalidLdapIAM2BindingMsg msg) {
        APICleanInvalidLdapIAM2BindingEvent evt = new APICleanInvalidLdapIAM2BindingEvent(msg.getId());

        if (!Q.New(LdapResourceRefVO.class).isExists()) {
            bus.publish(evt);
            return;
        }

        List<String> iam2VirtualIDList = doCleanInvalidLdapIAM2VirtualIDBindings();

        if (!iam2VirtualIDList.isEmpty()) {
            evt.setInventories(Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.uuid, iam2VirtualIDList).list());
        }

        bus.publish(evt);
    }

    private void doCreateIAM2VirtualIDFromLdapUid(CreateIAM2VirtualIDFromLdapUidMsg msg, ReturnValueCompletion<LdapResourceRefInventory> completion) {
        String uid = msg.getLdapUid();
        LdapTemplateContextSource ldapTemplateContextSource = ldapUtil.readLdapServerConfiguration();
        final String VIRTUALID_UUID = "VIRTUAL_UUID";
        final String LDAP_VIRTUALID_REF = "LDAP_VIRTUALID_REF";

        if (!ldapUtil.validateDnExist(ldapTemplateContextSource, uid)) {
            completion.fail(argerr("Failed to validate uid[%s]," +
                    " maybe it has been deleted", uid));
            return;
        }

        FlowChain chain = new SimpleFlowChain();
        chain.setName(String.format("create-virtual-id-and-ldap-binding-for-%s", uid));
        chain.then(new Flow() {
            String __name__ = String.format("create-iam2-user-for-%s", uid);

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String virtualIDName;

                if (msg.getName() != null) {
                    virtualIDName = msg.getName();

                    // if duplicate name
                    if (Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.name, virtualIDName).isExists()) {
                        virtualIDName += String.format("-%s", Platform.getUuid());
                    }
                } else {
                    virtualIDName = String.format("ZStack-iam2-user-%s", Platform.getUuid());
                }

                CreateIAM2VirtualIDMsg cmsg = new CreateIAM2VirtualIDMsg();
                cmsg.setPassword(Platform.getUuid() + Platform.getUuid());
                cmsg.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
                cmsg.setType(type.toString());
                cmsg.setName(virtualIDName);
                cmsg.setNoNeedChangePassword(true);
                cmsg.setDescription(msg.getDescription());

                if (msg.getAttributes() != null) {
                    cmsg.setAttributes(msg.getAttributes());
                }

                bus.makeTargetServiceIdByResourceUuid(cmsg, IAM2Manager.SERVICE_ID, uid);
                bus.send(cmsg, new CloudBusCallBack(completion) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(operr("Failed to create iam2 " +
                                    "virtual id for uid[%s], because %s", uid, reply.getError().getReadableDetails()));
                            return;
                        }

                        data.put(VIRTUALID_UUID, ((CreateIAM2VirtualIDReply) reply).getInventory().getUuid());
                        trigger.next();
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String virtualIdUUid = (String) data.get(VIRTUALID_UUID);
                if (virtualIdUUid != null) {
                    DeleteIAM2VirtualIDMsg dmsg = new DeleteIAM2VirtualIDMsg();
                    dmsg.setUuid(virtualIdUUid);
                    bus.makeTargetServiceIdByResourceUuid(dmsg, IAM2Manager.SERVICE_ID, dmsg.getVirtualIDUuid());
                    bus.send(dmsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.debug(String.format("Failed to delete iam2 virtual-id[uuid:%s]", dmsg.getUuid()));
                            }
                        }
                    });
                }

                trigger.rollback();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = String.format("create-binding-between-user-and-uid-%s", uid);

            @Override
            public void run(FlowTrigger trigger, Map data) {
                createIAM2VirtualIDLdapBinding((String) data.get(VIRTUALID_UUID),
                        uid, null, msg.getLdapGlobalUuid(), new ReturnValueCompletion<LdapResourceRefInventory>(completion) {
                            @Override
                            public void success(LdapResourceRefInventory ref) {
                                data.put(LDAP_VIRTUALID_REF, ref);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success((LdapResourceRefInventory) data.get(LDAP_VIRTUALID_REF));
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void handle(APICreateIAM2VirtualIDFromLdapUidMsg msg) {
        APICreateIAM2VirtualIDFromLdapUidEvent evt = new APICreateIAM2VirtualIDFromLdapUidEvent(msg.getId());

        CreateIAM2VirtualIDFromLdapUidMsg cmsg = new CreateIAM2VirtualIDFromLdapUidMsg();
        cmsg.setLdapUid(msg.getLdapUid());
        bus.makeTargetServiceIdByResourceUuid(cmsg, LdapLoginManager.SERVICE_ID, ldapUtil.getLdapServer().getUuid());
        bus.send(cmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    evt.setError(reply.getError());
                    bus.publish(evt);
                    return;
                }

                CreateIAM2VirtualIDFromLdapUidReply r = reply.castReply();
                evt.setInventory(r.getInventory());
                bus.publish(evt);
            }
        });
    }

    private void handle(APIGetCandidateLdapEntryForIAM2BindingMsg msg) {
        APIGetLdapEntryReply reply = new APIGetLdapEntryReply();

        AndFilter andFilter = new AndFilter();
        andFilter.and(new HardcodedFilter(msg.getLdapFilter()));

        List<String> boundLdapEntryList = Q.New(LdapResourceRefVO.class)
                .select(LdapResourceRefVO_.ldapUid)
                .listValues();

        List<Object> result = ldapUtil.searchLdapEntry(andFilter.toString(), msg.getLimit(), new ResultFilter() {
            @Override
            public boolean needSelect(String dn) {
                return !boundLdapEntryList.contains(dn);
            }
        });

        reply.setInventories(result);

        bus.reply(msg, reply);
    }

    private void handle(APIDeleteIAM2VirtualIDLdapBindingMsg msg) {
        APIDeleteIAM2VirtualIDLdapBindingEvent evt = new APIDeleteIAM2VirtualIDLdapBindingEvent(msg.getId());
        SQL.New(LdapResourceRefVO.class).eq(LdapResourceRefVO_.uuid, msg.getUuid()).delete();
        bus.publish(evt);
    }

    private void doCreateIAM2VirtualIDLdapBinding(String virtualIDUuid,
                                                  String ldapUid,
                                                  String resourceUuid,
                                                  String globalUuid,
                                                  ReturnValueCompletion<LdapResourceRefInventory> completion) {
        LdapResourceRefVO refVO = Q.New(LdapResourceRefVO.class)
                .eq(LdapResourceRefVO_.ldapUid, ldapUid)
                .find();
        if (refVO != null) {
            if (refVO.getLdapUid().equals(ldapUid)) {
                completion.success(refVO.toInventory());
                return;
            } else {
                completion.fail(err(LdapErrors.BIND_SAME_LDAP_UID_TO_MULTI_ACCOUNT,
                        "The ldap uid has been bound to an account. "));
                return;
            }
        }

        final LdapResourceRefVO vo = new LdapResourceRefVO();
        LdapTemplateContextSource ldapTemplateContextSource = ldapUtil.readLdapServerConfiguration();

        if (!ldapUtil.validateDnExist(ldapTemplateContextSource, ldapUid)) {
            completion.fail(argerr("Failed to validate dn [%s], maybe it has been deleted", ldapUid));
            return;
        }

        vo.setLdapServerUuid(ldapUtil.getLdapServer().getUuid());
        if (resourceUuid != null) {
            vo.setUuid(resourceUuid);
        } else {
            vo.setUuid(Platform.getUuid());
        }

        vo.setResourceType(IAM2VirtualIDVO.class.getSimpleName());
        vo.setLdapGlobalUuid(globalUuid);
        vo.setLdapUid(ldapUid);
        vo.setResourceUuid(virtualIDUuid);
        LdapResourceRefVO lvo = dbf.persistAndRefresh(vo);

        LdapResourceRefInventory inv = lvo.toInventory();
        completion.success(inv);
    }

    private void createIAM2VirtualIDLdapBinding(String virtualIDUuid,
                                                String ldapUid,
                                                String resourceUuid,
                                                String globalUuid,
                                                ReturnValueCompletion<LdapResourceRefInventory> completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                doCreateIAM2VirtualIDLdapBinding(virtualIDUuid,
                        ldapUid,
                        resourceUuid,
                        globalUuid,
                        new ReturnValueCompletion<LdapResourceRefInventory>(chain) {
                            @Override
                            public void success(LdapResourceRefInventory inv) {
                                completion.success(inv);
                                chain.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                                chain.next();
                            }
                        });
            }

            @Override
            public String getName() {
                return String.format("create-binding-for-ldap-uid-%s", ldapUid);
            }
        });
    }

    private void handle(final APICreateIAM2VirtualIDLdapBindingMsg msg) {
        final APICreateIAM2VirtualIDLdapBindingEvent evt = new APICreateIAM2VirtualIDLdapBindingEvent(msg.getId());

        createIAM2VirtualIDLdapBinding(msg.getVirtualIDUuid(),
                msg.getLdapUid(),
                msg.getResourceUuid(), null, new ReturnValueCompletion<LdapResourceRefInventory>(msg) {
                    @Override
                    public void success(LdapResourceRefInventory inv) {
                        evt.setInventory(inv);
                        bus.publish(evt);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                    }
                });
    }

    private void handle(final APILoginIAM2VirtualIDWithLdapMsg msg) {
        final APILogInReply reply = new APILogInReply();
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                doLoginIAM2VirtualIDWithLdap(msg.getUid(), msg.getPassword(), new ReturnValueCompletion<SessionInventory>(msg) {
                    @Override
                    public void success(SessionInventory session) {
                        reply.setInventory(session);
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("login-iam2-virtual-id-with-ldap-%s", msg.getUid());
            }
        });
    }

    private void loginInWithoutCreateSession(final String uid, final String password, final ReturnValueCompletion<String> completion) {
        if (!ldapUtil.isValid(uid, password)) {
            completion.fail(err(IdentityErrors.AUTHENTICATION_ERROR,
                    "Login validation failed in LDAP"));
            return;
        }

        String ldapUid = ldapUtil.getFullUserDn(uid);

        LdapResourceRefVO vo = Q.New(LdapResourceRefVO.class)
                .eq(LdapResourceRefVO_.ldapUid, ldapUid)
                .eq(LdapResourceRefVO_.resourceType, IAM2VirtualIDVO.class.getSimpleName())
                .find();

        if (vo != null) {
            completion.success(vo.getResourceUuid());
            return;
        }

        List<String> ldapUids = Q.New(LdapResourceRefVO.class).select(LdapResourceRefVO_.ldapUid).listValues();

        if (ldapUids.isEmpty()) {
            completion.fail(err(IdentityErrors.AUTHENTICATION_ERROR,
                    "The ldapUid does not have a binding account"));
            return;
        }

        List<String> dnGroups = ldapUtil.getUserDnGroups(ldapUids, ldapUid);

        if (dnGroups == null || dnGroups.isEmpty()) {
            completion.fail(err(IdentityErrors.AUTHENTICATION_ERROR,
                    "The ldapUid has no DN information from server. Maybe it has been deleted on server side"));
            return;
        }

        List<String> virtualIDUuids = Q.New(LdapResourceRefVO.class)
                .select(LdapResourceRefVO_.resourceType)
                .eq(LdapResourceRefVO_.resourceType, IAM2VirtualIDVO.class.getSimpleName())
                .in(LdapResourceRefVO_.ldapUid, dnGroups)
                .listValues();

        if (virtualIDUuids.size() != 1) {
            completion.fail(err(IdentityErrors.AUTHENTICATION_ERROR,
                    "Multi ldap binding found for ldap uid %s", uid));
            return;
        }

        completion.success(virtualIDUuids.get(0));
    }

    private void doLoginIAM2VirtualIDWithLdap(final String uid, final String password, final ReturnValueCompletion<SessionInventory> completion) {
        loginInWithoutCreateSession(uid, password, new ReturnValueCompletion<String>(completion) {
            @Override
            public void success(String virtualIDUuid) {
                completion.success(LoginIAM2VirtualIDSessionHelper.getIAM2VirtualIDSession(virtualIDUuid));
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    public String getId() {
        return bus.makeLocalServiceId(SERVICE_ID);
    }

    public boolean start() {
        installSyncRuleValidator();
        startAutoSyncTask();
        installGlobalConfigUpdater();
        setupCanonicalEvents();
        return true;
    }

    private void setupCanonicalEvents() {
        evtf.on(IAM2CanonicalEvents.IAM2_VIRTUALID_DELETED_PATH, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                IAM2CanonicalEvents.IAM2VirtualIDDeletedData d = (IAM2CanonicalEvents.IAM2VirtualIDDeletedData) data;
                String virtualID = ((IAM2CanonicalEvents.IAM2VirtualIDDeletedData) data).getVirtualID();
                SQL.New(LdapResourceRefVO.class).eq(LdapResourceRefVO_.resourceUuid, virtualID).delete();
            }
        });
    }

    private void installGlobalConfigUpdater() {
        IAM2LdapGlobalConfig.LDAP_AUTO_SYNC_INTERVAL.installUpdateExtension((oldConfig, newConfig) -> startAutoSyncTask());
        ResourceConfig cpuConfig = rcf.getResourceConfig(IAM2LdapGlobalConfig.LDAP_AUTO_SYNC_INTERVAL.getIdentity());
        cpuConfig.installUpdateExtension((config, resourceUuid, resourceType, oldValue, newValue) ->
                updateAutoSyncTask(resourceUuid));
    }

    private Map<String, Future> autoSyncTaskMap = Collections.synchronizedMap(new HashMap<>());

    private Future startAutoSyncTaskForServer(String ldapServerUuid) {
        return thdf.submitPeriodicTask(new PeriodicTask() {
            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.SECONDS;
            }

            @Override
            public long getInterval() {
                return rcf.getResourceConfigValue(IAM2LdapGlobalConfig.LDAP_AUTO_SYNC_INTERVAL, ldapServerUuid, Long.class);
            }

            @Override
            public String getName() {
                return String.format("ldap-server-%s-auto-sync-task", ldapServerUuid);
            }

            @Override
            public void run() {
                if (!resourceDestinationMaker.isManagedByUs(ldapServerUuid) || !rcf.getResourceConfigValue(IAM2LdapGlobalConfig.ENABLE_LDAP_AUTO_SYNC, ldapServerUuid, Boolean.class)) {
                    return;
                }

                SyncLdapServerMsg msg = new SyncLdapServerMsg();
                msg.setUuid(ldapServerUuid);
                bus.makeLocalServiceId(msg, LdapLoginManager.SERVICE_ID);
                bus.send(msg, new CloudBusCallBack(null) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format("Failed to sync LDAP/AD server[uuid:%s]", ldapServerUuid));
                        }
                    }
                });
            }
        });
    }

    private synchronized void updateAutoSyncTask(String ldapServerUuid) {
        Future task = autoSyncTaskMap.get(ldapServerUuid);
        if (task != null) {
            task.cancel(true);
        }

        autoSyncTaskMap.put(ldapServerUuid, startAutoSyncTaskForServer(ldapServerUuid));
    }

    private void startAutoSyncTask() {
        if (!autoSyncTaskMap.isEmpty()) {
            autoSyncTaskMap.values().forEach(t -> t.cancel(true));
        }

        List<String> ldapServerUuids = Q.New(LdapServerVO.class)
                .select(LdapServerVO_.uuid)
                .eq(LdapServerVO_.scope, scope.toString())
                .listValues();

        for (String ldapServerUuid : ldapServerUuids) {
            autoSyncTaskMap.put(ldapServerUuid, startAutoSyncTaskForServer(ldapServerUuid));
        }
    }

    private List<IAM2LdapGroup> loadGroupCache() {
        LdapServerVO ldapServerVO = ldapUtil.getLdapServer();
        List<LdapResourceRefVO> organizationRefs = Q.New(LdapResourceRefVO.class)
                .eq(LdapResourceRefVO_.resourceType, IAM2OrganizationVO.class.getSimpleName())
                .eq(LdapResourceRefVO_.ldapServerUuid, ldapServerVO.getUuid())
                .list();

        List<IAM2LdapGroup> groups = new ArrayList<>();

        for (LdapResourceRefVO ref : organizationRefs) {
            if (ref.getLdapUid().equals(ROOT_ORG_NAME_STRING)) {
                IAM2LdapGroup group = new IAM2LdapGroup();
                group.setExistsNode(true);
                group.setDn(ROOT_ORG_NAME_STRING);
                group.setGroupUuid(ref.getResourceUuid());
                group.setObjectGUID(ref.getLdapGlobalUuid());
                group.setName(ROOT_ORG_NAME_STRING);
                group.setRoot(true);
                groups.add(group);
                continue;
            }

            LdapName ln = null;
            try {
                ln = new LdapName(ref.getLdapUid());
            } catch (InvalidNameException e) {
                logger.warn(String.format("invalid ldap name %s", ref.getLdapUid()));
                continue;
            }

            IAM2LdapGroup group = new IAM2LdapGroup();
            group.setExistsNode(true);
            group.setDn(ln.toString());
            group.setGroupUuid(ref.getResourceUuid());
            group.setObjectGUID(ref.getLdapGlobalUuid());

            groups.add(group);
        }

        return groups;
    }

    private List<LdapOrganizationTree.LdapOrganizationNode> loadOrganizationCache() {
        LdapServerVO ldapServerVO = ldapUtil.getLdapServer();
        List<LdapResourceRefVO> organizationRefs = Q.New(LdapResourceRefVO.class)
                .eq(LdapResourceRefVO_.resourceType, IAM2OrganizationVO.class.getSimpleName())
                .eq(LdapResourceRefVO_.ldapServerUuid, ldapServerVO.getUuid())
                .list();

        List<LdapOrganizationTree.LdapOrganizationNode> existNodeList = new ArrayList<>();
        for (LdapResourceRefVO ref : organizationRefs) {
            if (ref.getLdapUid().equals(ROOT_ORG_NAME_STRING)) {
                LdapOrganizationTree.LdapOrganizationNode node = new LdapOrganizationTree.LdapOrganizationNode();
                node.setExistNode(true);
                node.setOrganizationUuid(ref.getResourceUuid());
                node.setLevel(0);
                node.setObjectUid(ref.getLdapGlobalUuid());
                node.setOrgDn(ref.getLdapUid());
                node.setDn(ref.getLdapUid());
                node.setName(ROOT_ORG_NAME_STRING);
                existNodeList.add(node);
                continue;
            }

            LdapName ln = null;
            try {
                ln = new LdapName(ref.getLdapUid());
            } catch (InvalidNameException e) {
                logger.warn(String.format("invalid ldap name %s", ref.getLdapUid()));
                continue;
            }

            ln.getRdns().get(0).getValue();

            int nodeLevelCount = 0;
            StringBuilder sb = new StringBuilder();
            for(Rdn rdn : ln.getRdns()) {
                if(!rdn.getType().equalsIgnoreCase("OU")) {
                    continue;
                }

                sb.append(rdn.toString());
                sb.append(",");
                nodeLevelCount++;
            }

            LdapOrganizationTree.LdapOrganizationNode node = new LdapOrganizationTree.LdapOrganizationNode();
            node.setExistNode(true);
            node.setOrganizationUuid(ref.getResourceUuid());
            node.setLevel(nodeLevelCount);
            node.setObjectUid(ref.getLdapGlobalUuid());
            node.setOrgDn(sb.toString());
            node.setDn(ref.getLdapUid());

            existNodeList.add(node);
        }

        return existNodeList;
    }

    private void installSyncRuleValidator() {
        IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION.installValidator((resourceUuid, resourceType, systemTag) -> {
            Map<String, String> token = TagUtils.parse(IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION.getTagFormat(), systemTag);
            String configuration = token.get(IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION_TOKEN);

            IAM2VirtualIDLdapSyncConfig config = null;
            try {
                config = JSONObjectUtil.toObject(configuration, IAM2VirtualIDLdapSyncConfig.class);
            } catch (JsonSyntaxException e) {
                throw new ApiMessageInterceptionException(argerr("invalid json format"));
            }

            List<String> fieldNames = Arrays.stream(CreateIAM2VirtualIDFromLdapUidMsg.class.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
            config.getRules().forEach(rule -> {
                if (rule.getName() == null) {
                    throw new ApiMessageInterceptionException(argerr("name is mandatory field %"));
                }

                if (rule.getAttribute() == null) {
                    throw new ApiMessageInterceptionException(argerr("attribute is mandatory field %"));
                }

                if (rule.getType() == null) {
                    throw new ApiMessageInterceptionException(argerr("type is mandatory field %"));
                }

                if (rule.getOptional() == null) {
                    throw new ApiMessageInterceptionException(argerr("optional is mandatory field %"));
                }

                if (rule.getType().equals(SyncRuleAttributeType.SYSTEM) && !fieldNames.contains(rule.getName())) {
                    throw new ApiMessageInterceptionException(argerr("name should use values in %s", fieldNames));
                }

                if (!rule.getOptional()) {
                    AndFilter conditionFilter = new AndFilter();

                    conditionFilter.and(new NotPresentFilter(rule.getAttribute()));
                    conditionFilter.and(new HardcodedFilter(IAM2LdapGlobalConfig.LDAP_USER_SYNC_FILTER.value(String.class)));
                    // filter
                    String cleanBindingFilter = LdapSystemTags.LDAP_CLEAN_BINDING_FILTER.getTokenByResourceUuid(resourceUuid, LdapSystemTags.LDAP_CLEAN_BINDING_FILTER_TOKEN);
                    if(StringUtils.isNotEmpty(cleanBindingFilter)){
                        conditionFilter.and(new NotFilter(new HardcodedFilter(cleanBindingFilter)));
                    }
                    // allow list filter
                    String whiteListFilter = LdapSystemTags.LDAP_ALLOW_LIST_FILTER.getTokenByResourceUuid(resourceUuid, LdapSystemTags.LDAP_ALLOW_LIST_FILTER_TOKEN);
                    if(StringUtils.isNotEmpty(whiteListFilter)){
                        conditionFilter.and(new HardcodedFilter(whiteListFilter));
                    }

                    logger.debug("user filter is" + conditionFilter.toString());

                    Set<String> returnAttributeSet = new HashSet<>(Arrays.asList(LdapConstant.QUERY_LDAP_ENTRY_MUST_RETURN_ATTRIBUTES));
                    returnAttributeSet.add(rule.getAttribute());
                    List<Object> result = ldapUtil.searchLdapEntry(conditionFilter.toString(), 5, returnAttributeSet.toArray(new String[]{}), null, false);

                    if (!result.isEmpty()) {
                        String dnValuesConcatenated = result.stream()
                                .map(map -> (String) (((HashMap) map).get(LdapConstant.LDAP_DN_KEY)))
                                .collect(Collectors.joining(" | "));
                        throw new ApiMessageInterceptionException(argerr("Invalid attribute. " +
                                "Attribute[%s] is required, but found there are some record [%s] not matched", rule.getAttribute(), dnValuesConcatenated));
                    }
                }
            });
        });

        IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION.installValidator((resourceUuid, resourceType, systemTag) -> {
            Map<String, String> token = TagUtils.parse(IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION.getTagFormat(), systemTag);
            String configuration = token.get(IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION_TOKEN);

            IAM2OrganizationLdapSyncConfig config = null;
            try {
                config = JSONObjectUtil.toObject(configuration, IAM2OrganizationLdapSyncConfig.class);
            } catch (JsonSyntaxException e) {
                throw new ApiMessageInterceptionException(argerr("invalid json format"));
            }

            if (config.getStrategy() == null) {
                throw new ApiMessageInterceptionException(argerr("strategy is mandatory field %"));
            }

            final OrganizationSyncStrategy strategy = config.getStrategy();
            List<String> fieldNames = Arrays.stream(CreateIAM2OrganizationMsg.class.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
            config.getRules().forEach(rule -> {
                if (rule.getName() == null) {
                    throw new ApiMessageInterceptionException(argerr("name is mandatory field %"));
                }

                if (rule.getAttribute() == null) {
                    throw new ApiMessageInterceptionException(argerr("attribute is mandatory field %"));
                }

                if (rule.getType() == null) {
                    throw new ApiMessageInterceptionException(argerr("type is mandatory field %"));
                }

                if (rule.getOptional() == null) {
                    throw new ApiMessageInterceptionException(argerr("optional is mandatory field %"));
                }

                if (rule.getType().equals(SyncRuleAttributeType.SYSTEM) && !fieldNames.contains(rule.getName())) {
                    throw new ApiMessageInterceptionException(argerr("name should use values in %s", fieldNames));
                }

                if (!rule.getOptional()) {
                    String filter = null;
                    if (strategy.equals(OrganizationSyncStrategy.Organization)) {
                        filter = IAM2LdapGlobalConfig.LDAP_ORGANIZATION_SYNC_FILTER.value();
                    } else {
                        filter = IAM2LdapGlobalConfig.LDAP_GROUP_SYNC_FILTER.value();
                    }

                    AndFilter conditionFilter = new AndFilter();
                    conditionFilter.and(new NotPresentFilter(rule.getAttribute()));
                    conditionFilter.and(new HardcodedFilter(filter));
                    // filter
                    String cleanBindingFilter = LdapSystemTags.LDAP_CLEAN_BINDING_FILTER.getTokenByResourceUuid(resourceUuid, LdapSystemTags.LDAP_CLEAN_BINDING_FILTER_TOKEN);
                    if(StringUtils.isNotEmpty(cleanBindingFilter)){
                        conditionFilter.and(new NotFilter(new HardcodedFilter(cleanBindingFilter)));
                    }
                    // allow list filter
                    String allowListFilter = LdapSystemTags.LDAP_ALLOW_LIST_FILTER.getTokenByResourceUuid(resourceUuid, LdapSystemTags.LDAP_ALLOW_LIST_FILTER_TOKEN);
                    if(StringUtils.isNotEmpty(allowListFilter)){
                        conditionFilter.and(new HardcodedFilter(allowListFilter));
                    }

                    logger.debug("org filter is" + conditionFilter.toString());

                    Set<String> returnAttributeSet = new HashSet<>(Arrays.asList(LdapConstant.QUERY_LDAP_ENTRY_MUST_RETURN_ATTRIBUTES));
                    returnAttributeSet.add(rule.getAttribute());
                    List<Object> result = ldapUtil.searchLdapEntry(conditionFilter.toString(), 1, returnAttributeSet.toArray(new String[]{}), null, false);

                    if (!result.isEmpty()) {
                        throw new ApiMessageInterceptionException(argerr("Invalid attribute. " +
                                "Attribute[%s] is required, but found there are some record not matched", rule.getAttribute()));
                    }
                }
            });
        });
    }

    public boolean stop() {
        return true;
    }

    @Override
    public void afterAddLdapServer(APIAddLdapServerMsg msg, String ldapServerUuid) {
        if (!Q.New(LdapServerVO.class)
                .eq(LdapServerVO_.uuid, ldapServerUuid)
                .eq(LdapServerVO_.scope, scope.toString())
                .isExists()) {
            return;
        }

        if(msg.getSystemTags() == null || msg.getSystemTags().isEmpty()) {
            return;
        }

        saveOrganizationSyncConfig(msg.getSystemTags(), ldapServerUuid);
        saveVirtualIDSyncConfig(msg.getSystemTags(), ldapServerUuid);
        autoSyncTaskMap.put(ldapServerUuid, startAutoSyncTaskForServer(ldapServerUuid));
    }

    private void saveVirtualIDSyncConfig(List<String> systemTags, String ldapServerUuid) {
        PatternedSystemTag tag =  IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION;
        String token = IAM2LdapSystemTags.VIRTUAL_ID_SYNC_CONFIGURATION_TOKEN;

        String tagValue = SystemTagUtils.findTagValue(systemTags, tag, token);
        if(StringUtils.isEmpty(tagValue)){
            return;
        }

        SystemTagCreator creator = tag.newSystemTagCreator(ldapServerUuid);
        creator.recreate = true;
        creator.setTagByTokens(map(CollectionDSL.e(token, tagValue)));
        creator.create();
    }

    private void saveOrganizationSyncConfig(List<String> systemTags, String ldapServerUuid) {
        PatternedSystemTag tag =  IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION;
        String token = IAM2LdapSystemTags.ORGANIZATION_SYNC_CONFIGURATION_TOKEN;

        String tagValue = SystemTagUtils.findTagValue(systemTags, tag, token);
        if(StringUtils.isEmpty(tagValue)){
            return;
        }

        SystemTagCreator creator = tag.newSystemTagCreator(ldapServerUuid);
        creator.recreate = true;
        creator.setTagByTokens(map(CollectionDSL.e(token, tagValue)));
        creator.create();
    }

    @Override
    public void beforeDeleteLdapServer(String ldapServerUuid, NoErrorCompletion completion) {
        if (!Q.New(LdapServerVO.class)
                .eq(LdapServerVO_.uuid, ldapServerUuid)
                .eq(LdapServerVO_.scope, scope.toString())
                .isExists()) {
            completion.done();
            return;
        }

        Future autoSyncTask = autoSyncTaskMap.get(ldapServerUuid);
        if (autoSyncTask != null) {
            autoSyncTask.cancel(true);
        }
        autoSyncTaskMap.remove(ldapServerUuid);

        FlowChain chain = new SimpleFlowChain();
        chain.setName("delete-ldap-synced-resource");
        chain.then(new NoRollbackFlow() {
            String __name__ = "delete-synced-virtual-id";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                long count = SQL.New("select count(virtualIDVO.uuid) from LdapResourceRefVO vo, IAM2VirtualIDVO virtualIDVO " +
                        "where vo.resourceType = :resourceType and vo.resourceUuid = virtualIDVO.uuid", Long.class)
                        .param("resourceType", IAM2VirtualIDVO.class.getSimpleName()).find();

                if (count == 0) {
                    trigger.next();
                    return;
                }

                SQL.New("select virtualIDVO.uuid from LdapResourceRefVO vo, IAM2VirtualIDVO virtualIDVO " +
                        "where vo.resourceType = :resourceType and vo.resourceUuid = virtualIDVO.uuid", String.class)
                        .param("resourceType", IAM2VirtualIDVO.class.getSimpleName())
                        .limit(1000).skipIncreaseOffset(true).paginate(count, (List<String> virtualIDUuids, PaginateCompletion paginateCompletion) -> {

                    List<DeleteIAM2VirtualIDMsg> msgs = new ArrayList<>();
                    for (String vid : virtualIDUuids) {
                        DeleteIAM2VirtualIDMsg dmsg = new DeleteIAM2VirtualIDMsg();
                        dmsg.setUuid(vid);
                        bus.makeTargetServiceIdByResourceUuid(dmsg, IAM2Manager.SERVICE_ID, dmsg.getVirtualIDUuid());
                        msgs.add(dmsg);
                    }

                    new While<>(msgs).step((dmsg, innerWhileCompletion) -> bus.send(dmsg, new CloudBusCallBack(innerWhileCompletion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.debug(String.format("Failed to delete iam2 virtual id[uuid:%s]", dmsg.getVirtualIDUuid()));
                            }

                            innerWhileCompletion.done();
                        }
                    }), 20).run(new WhileDoneCompletion(paginateCompletion) {
                        @Override
                        public void done(ErrorCodeList errorCodeList) {
                            paginateCompletion.done();
                        }
                    });
                }, new NoErrorCompletion() {
                    @Override
                    public void done() {
                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "delete-synced-organizations";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                long count = SQL.New("select count(organizationVO.uuid) from LdapResourceRefVO vo, IAM2OrganizationVO organizationVO " +
                        "where vo.resourceType = :resourceType and vo.resourceUuid = organizationVO.uuid", Long.class)
                        .param("resourceType", IAM2OrganizationVO.class.getSimpleName()).find();

                if (count == 0) {
                    trigger.next();
                    return;
                }

                SQL.New("select organizationVO.uuid from LdapResourceRefVO vo, IAM2OrganizationVO organizationVO " +
                        "where vo.resourceType = :resourceType and vo.resourceUuid = organizationVO.uuid", String.class)
                        .param("resourceType", IAM2OrganizationVO.class.getSimpleName())
                        .limit(1000).skipIncreaseOffset(true).paginate(count, (List<String> orgUuids, PaginateCompletion paginateCompletion) -> {

                    List<DeleteIAM2OrganizationMsg> msgs = new ArrayList<>();
                    for (String orgUuid : orgUuids) {
                        DeleteIAM2OrganizationMsg dmsg = new DeleteIAM2OrganizationMsg();
                        dmsg.setUuid(orgUuid);
                        bus.makeTargetServiceIdByResourceUuid(dmsg, IAM2Manager.SERVICE_ID, dmsg.getOrganizationUuid());
                        msgs.add(dmsg);
                    }

                    new While<>(msgs).step((dmsg, innerWhileCompletion) -> bus.send(dmsg, new CloudBusCallBack(innerWhileCompletion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.debug(String.format("Failed to delete iam2 organization[uuid:%s]", dmsg.getOrganizationUuid()));
                            }

                            innerWhileCompletion.done();
                        }
                    }), 20).run(new WhileDoneCompletion(paginateCompletion) {
                        @Override
                        public void done(ErrorCodeList errorCodeList) {
                            paginateCompletion.done();
                        }
                    });
                }, new NoErrorCompletion(trigger) {
                    @Override
                    public void done() {
                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.done();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.done();
            }
        }).start();
    }
}
