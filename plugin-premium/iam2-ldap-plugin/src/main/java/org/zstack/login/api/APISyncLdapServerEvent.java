package org.zstack.login.api;

import org.zstack.header.longjob.LongJobInventory;
import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse(allTo = "inventory")
public class APISyncLdapServerEvent extends APIEvent {
    private LongJobInventory inventory;

    public LongJobInventory getInventory() {
        return inventory;
    }

    public void setInventory(LongJobInventory inventory) {
        this.inventory = inventory;
    }

    public APISyncLdapServerEvent() {
    }

    public APISyncLdapServerEvent(String apiId) {
        super(apiId);
    }

    public static APISyncLdapServerEvent __example__() {
        APISyncLdapServerEvent evt = new APISyncLdapServerEvent();
        LongJobInventory inv = new LongJobInventory();
        inv.setUuid(uuid());
        evt.setInventory(inv);
        return evt;
    }
}
