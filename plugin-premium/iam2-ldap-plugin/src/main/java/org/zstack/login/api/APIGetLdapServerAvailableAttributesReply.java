package org.zstack.login.api;

import org.zstack.header.message.APIReply;
import org.zstack.header.message.NoJsonSchema;
import org.zstack.header.rest.RestResponse;
import org.zstack.ldap.APIGetLdapEntryReply;

import java.util.ArrayList;
import java.util.List;

@RestResponse(allTo = "inventories")
public class APIGetLdapServerAvailableAttributesReply extends APIReply {
    @NoJsonSchema
    private List inventories;

    public List getInventories() {
        return inventories;
    }

    public void setInventories(List inventories) {
        this.inventories = inventories;
    }

    public static APIGetLdapEntryReply __example__() {
        APIGetLdapEntryReply reply = new APIGetLdapEntryReply();
        reply.setInventories(new ArrayList());
        return reply;
    }
}
