package org.zstack.login.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.rest.RestRequest;

@RestRequest(
        path = "/iam2/ldap/bindings/actions",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APICleanInvalidLdapIAM2BindingEvent.class
)
public class APICleanInvalidLdapIAM2BindingMsg extends APIMessage {
    public static APICleanInvalidLdapIAM2BindingMsg __example__() {
        APICleanInvalidLdapIAM2BindingMsg msg = new APICleanInvalidLdapIAM2BindingMsg();

        return msg;
    }
}
