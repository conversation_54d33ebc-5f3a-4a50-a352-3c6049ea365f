package org.zstack.login.api

import org.zstack.header.errorcode.ErrorCode
import org.zstack.login.entity.LdapResourceRefInventory

doc {

	title "创建AD/Ldap用户和租户管理用户绑定关系的结果"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.login.api.APICreateIAM2VirtualIDLdapBindingEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "3.5.1"
		clz ErrorCode.class
	}
	ref {
		name "inventory"
		path "org.zstack.login.api.APICreateIAM2VirtualIDLdapBindingEvent.inventory"
		desc "null"
		type "LdapResourceRefInventory"
		since "3.5.1"
		clz LdapResourceRefInventory.class
	}
}
