package org.zstack.daho.core;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.Platform;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.componentloader.PluginDSL;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.daho.process.*;
import org.zstack.daho.utils.DahoUtils;
import org.zstack.header.AbstractService;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.daho.*;
import org.zstack.header.daho.process.*;
import org.zstack.header.datacenter.DataCenterInventory;
import org.zstack.header.datacenter.DataCenterVO;
import org.zstack.header.datacenter.SyncDataCenterExtensionPoint;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.hybrid.account.HybridAccountVO;
import org.zstack.hybrid.account.HybridAccountVO_;
import org.zstack.hybrid.core.HybridCascadeAction;
import org.zstack.hybrid.core.HybridLicenseChecker;
import org.zstack.hybrid.core.HybridType;
import org.zstack.identity.AccountManager;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * Created by mingjian.deng on 2017/12/13.
 */
public class DahoManagerImpl extends AbstractService implements GlobalApiMessageInterceptor,
                                                        SyncDataCenterExtensionPoint, DahoExtensionPoint {
    private static final CLogger logger = Utils.getLogger(DahoManagerImpl.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private HybridLicenseChecker hyimpl;
    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    private AccountManager acmgr;
    @Autowired
    protected DahoEmitter dahoEmitter;

    private static String createVOFlag = "vllVO";
    private static String createIdFlag = "vllId";
    private static String createTaskIdFlag = "taskId";

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APISyncDahoDataCenterConnectionMsg) {
            handle((APISyncDahoDataCenterConnectionMsg) msg);
        } else if (msg instanceof APIDeleteDahoDataCenterConnectionMsg) {
            handle((APIDeleteDahoDataCenterConnectionMsg) msg);
        } else if (msg instanceof APIUpdateDahoDataCenterConnectionMsg) {
            handle((APIUpdateDahoDataCenterConnectionMsg) msg);
        } else if (msg instanceof APISyncDahoCloudConnectionMsg) {
            handle((APISyncDahoCloudConnectionMsg) msg);
        } else if (msg instanceof APIDeleteDahoCloudConnectionMsg) {
            handle((APIDeleteDahoCloudConnectionMsg) msg);
        } else if (msg instanceof APIUpdateDahoCloudConnectionMsg) {
            handle((APIUpdateDahoCloudConnectionMsg) msg);
        } else if (msg instanceof APISyncDahoVllMsg) {
            handle((APISyncDahoVllMsg) msg);
        } else if (msg instanceof APIDeleteDahoVllMsg) {
            handle((APIDeleteDahoVllMsg) msg);
        } else if (msg instanceof APIUpdateDahoVllMsg) {
            handle((APIUpdateDahoVllMsg) msg);
        } else if (msg instanceof APICreateDahoVllRemoteMsg) {
            handle((APICreateDahoVllRemoteMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void createVlls(final APICreateDahoVllRemoteMsg msg, final SyncTaskChain taskChain) {
        APICreateDahoVllRemoteEvent evt = new APICreateDahoVllRemoteEvent(msg.getId());
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("create-daho-vll-remote-%s-chain", msg.getName()));

        chain.then(new Flow() {
            String __name__ = String.format("create-daho-vll-remote-%s", msg.getName());
            @Override
            public void run(FlowTrigger trigger, Map data) {
                CreateDahoVllMsg cvmsg = new CreateDahoVllMsg();
                cvmsg.setAccountUuid(msg.getSession().getAccountUuid());
                cvmsg.setBandwidth(msg.getBandwidth());
                cvmsg.setName(msg.getName());
                if (msg.getDescription() != null) {
                    cvmsg.setDescription(msg.getDescription());
                }
                cvmsg.setExpirePolicy(msg.getExpirePolicy());
                DahoConnectionVO dvo = dbf.findByUuid(msg.getDcConnUuid(), DahoConnectionVO.class);
                DebugUtils.Assert(dvo != null, String.format("uuid: %s not found in DahoConnectionVO", msg.getDcConnUuid()));

                DahoCloudConnectionVO dcvo = dbf.findByUuid(msg.getCloudConnUuid(), DahoCloudConnectionVO.class);
                DebugUtils.Assert(dcvo != null, String.format("uuid: %s not found in DahoCloudConnectionVO", msg.getCloudConnUuid()));

                cvmsg.setVlan(msg.getVlan());

                cvmsg.setIdA(dvo.getConnectionId());
                cvmsg.setTypeA("dc");

                cvmsg.setIdZ(dcvo.getConnectionId());
                cvmsg.setTypeZ("cloud");

                bus.makeLocalServiceId(cvmsg, DahoConstant.SDK_SERVICE_ID);
                bus.send(cvmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            CreateDahoVllReply reply1 = reply.castReply();
                            DahoVllsVO vvo = new DahoVllsVO();
                            if (msg.getResourceUuid() != null) {
                                vvo.setUuid(msg.getResourceUuid());
                            } else {
                                vvo.setUuid(Platform.getUuid());
                            }
                            vvo.setName(msg.getName());
                            vvo.setVlanId(msg.getVlan());
                            vvo.setDescription(msg.getDescription());
                            vvo.setDataCenterUuid(dcvo.getDataCenterUuid());
                            vvo.setExpirePolicy(DahoVllExpirePolicy.get(msg.getExpirePolicy()));
                            vvo.setBandwidthMbps(msg.getBandwidth());
                            vvo.setConnAUuid(msg.getCloudConnUuid());
                            vvo.setConnZUuid(msg.getDcConnUuid());
                            vvo.setStatus(DahoVllStatus.Idle);
                            data.put(createVOFlag, vvo);
                            data.put(createTaskIdFlag, reply1.getTaskId());
                            trigger.next();
                        } else {
                          trigger.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
//                DahoVllsVO vvo = (DahoVllsVO)data.get(createVOFlag);
//                if (vvo != null) {
//                    deleteVlls(CollectionDSL.list(vvo.getUuid()), new NopeCompletion());
//                }
                trigger.rollback();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = String.format("wait-create-vll-%s-remote-task", msg.getName());
            @Override
            public void run(FlowTrigger trigger, Map data) {
                String taskId = (String)data.get(createTaskIdFlag);

                GetDahoVllTaskMsg gtmsg = new GetDahoVllTaskMsg();
                gtmsg.setUrl(String.format("/tasks/%s", taskId));
                gtmsg.setAccountUuid(msg.getSession().getAccountUuid());
                gtmsg.setSuccssStatus(DahoVllTaskStatus.Completed.toString());
                gtmsg.setFailedStatus(DahoVllTaskStatus.Failed.toString());
                bus.makeLocalServiceId(gtmsg, DahoConstant.SDK_SERVICE_ID);
                bus.send(gtmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            GetDahoVllTaskReply reply1 = reply.castReply();
                            data.put(createIdFlag, reply1.getVllId());
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = String.format("sync-vll-%s-from-remote", msg.getName());
            @Override
            public void run(FlowTrigger trigger, Map data) {
                DahoVllsVO vvo = (DahoVllsVO)data.get(createVOFlag);
                String vllId = (String)data.get(createIdFlag);
                GetDahoVllsMsg gcmsg = new GetDahoVllsMsg();
                gcmsg.setAccountUuid(msg.getSession().getAccountUuid());
                gcmsg.setVllId(vllId);

                bus.makeLocalServiceId(gcmsg, DahoConstant.SDK_SERVICE_ID);
                bus.send(gcmsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            GetDahoVllsReply reply1 = (GetDahoVllsReply)reply;
                            DahoVllsProperty property = reply1.getProperties().get(0);
                            vvo.setVllId(vllId);
                            vvo.setType(DahoVllType.get(reply1.getProperties().get(0).getType()));
                            vvo.setStatus(property.getStatus());
                            vvo.setCreateDate(DahoUtils.fmtTime(property.getCreation_time()));
                            vvo.setStartDate(DahoUtils.fmtTime(property.getStart_time()));

                            vvo.setAccountUuid(msg.getSession().getAccountUuid());
                            dbf.persistAndRefresh(vvo);
                            data.put(createVOFlag, vvo);
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }
        }).done(new FlowDoneHandler(taskChain) {
            @Override
            public void handle(Map data) {
                DahoVllsVO vvo = (DahoVllsVO)data.get(createVOFlag);
                vvo = dbf.reload(vvo);
                evt.setInventory(DahoVllsInventory.valueOf(vvo));
                bus.publish(evt);
                taskChain.next();
            }
        }).error(new FlowErrorHandler(taskChain) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                evt.setError(errCode);
                bus.publish(evt);
                taskChain.next();
            }
        }).start();
    }

    private void handle(final APICreateDahoVllRemoteMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("create-daho-vll-%s", msg.getName());
            }

            @Override
            public void run(SyncTaskChain chain) {
                createVlls(msg, chain);
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void updateVlls(final APIUpdateDahoVllMsg msg, final SyncTaskChain chain) {
        APIUpdateDahoVllEvent evt = new APIUpdateDahoVllEvent(msg.getId());
        DahoVllsVO dvo = dbf.findByUuid(msg.getUuid(), DahoVllsVO.class);
        DebugUtils.Assert(dvo != null, "DahoVllsVO has been already deleted");

        UpdateDahoVllMsg uvmsg = new UpdateDahoVllMsg();
        uvmsg.setAccountUuid(msg.getSession().getAccountUuid());
        uvmsg.setUrl(String.format("/cb_vlls/%s", dvo.getVllId()));
        uvmsg.setVllId(dvo.getVllId());

        bus.makeLocalServiceId(uvmsg, DahoConstant.SDK_SERVICE_ID);

        if (msg.getDescription() != null) {
            dvo.setDescription(msg.getDescription());
            uvmsg.setDescription(msg.getDescription());
        }
        if (msg.getName() != null) {
            dvo.setName(msg.getName());
            uvmsg.setName(msg.getName());
        }
        if (msg.getBandWidthMbps() != null) {
            dvo.setBandwidthMbps(msg.getBandWidthMbps());
            uvmsg.setBandWidth(msg.getBandWidthMbps());
        }
        if (msg.getExpirePolicy() != null) {
            dvo.setExpirePolicy(DahoVllExpirePolicy.get(msg.getExpirePolicy()));
            uvmsg.setExpirePolicy(msg.getExpirePolicy());
        }
        final DahoVllsVO newVo = dvo;

        bus.send(uvmsg, new CloudBusCallBack(chain) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    dbf.updateAndRefresh(newVo);
                    evt.setInventory(DahoVllsInventory.valueOf(newVo));
                } else {
                    evt.setError(reply.getError());
                }
                bus.publish(evt);
                chain.next();
            }
        });
    }

    private void handle(final APIUpdateDahoVllMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("update-daho-vll-%s", msg.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                updateVlls(msg, chain);
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void handle(final APIDeleteDahoVllMsg msg) {
        APIDeleteDahoVllEvent evt = new APIDeleteDahoVllEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                deleteVlls(CollectionDSL.list(msg.getUuid()), true,
                        msg.getSession(), new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("delete-daho-vll-%s", msg.getUuid());
            }
        });
    }

    private Map<String, List<DahoVllsInventory>> syncVlls(final GetDahoVllsReply reply, String dcUuid, String accountUuid) {
        Map<String, List<DahoVllsInventory>> result = new HashMap<>();
        DataCenterVO dc = dbf.findByUuid(dcUuid, DataCenterVO.class);
        if (dc == null) {
            // if datacenter has been deleted, then return null
            return result;
        }
        //TODO：find the difference part
        List<DahoVllsProperty> synced = new ArrayList<>();
        for (DahoVllsProperty property: reply.getProperties()) {
            // we get the list in same datacenter
            if (property.getConnection_a() != null && property.getConnection_a().id != null) {
                if (Q.New(DahoCloudConnectionVO.class).eq(DahoCloudConnectionVO_.dataCenterUuid, dcUuid).
                        eq(DahoCloudConnectionVO_.connectionId, property.getConnection_a().id).isExists()) {
                    // if connection in dc, we add it
                    synced.add(property);
                }
            }
        }
        List<DahoVllsVO> vos = Q.New(DahoVllsVO.class).eq(DahoVllsVO_.dataCenterUuid, dcUuid).list();

        List<DahoVllsInventory> delList = new ArrayList<>();
        List<DahoVllsInventory> allList = new ArrayList<>();
        List<String> addList = new ArrayList<>();
        CollectionUtils.subtract(vos, synced).forEach(collection -> delList.add((DahoVllsInventory.valueOf((DahoVllsVO) collection))));
        CollectionUtils.subtract(synced, vos).forEach(collection -> addList.add(((DahoVllsProperty) collection).getId()));

        for (DahoVllsProperty property : synced) {
            DahoVllsVO cvo = null;
            boolean exists = false;

            if (addList.contains(property.getId())) {
                String uuid = Platform.getUuid();
                cvo = new DahoVllsVO();
                cvo.setUuid(uuid);
                cvo.setAccountUuid(accountUuid);
            } else {
                cvo = Q.New(DahoVllsVO.class).eq(DahoVllsVO_.dataCenterUuid, dcUuid).
                        eq(DahoVllsVO_.vllId, property.getId()).find();
                exists = true;
            }

            cvo.setName(property.getName());
            cvo.setVllId(property.getId());
            cvo.setDataCenterUuid(dcUuid);
            cvo.setStatus(property.getStatus());
            cvo.setType(DahoVllType.get(property.getType()));
            cvo.setDescription(property.getDescription());
            cvo.setExpirePolicy(DahoVllExpirePolicy.get(property.getExpire_policy()));
            cvo.setBandwidthMbps(Integer.valueOf(property.getBandwidth()));
            cvo.setCreateDate(DahoUtils.fmtTime(property.getCreation_time()));
            cvo.setStartDate(DahoUtils.fmtTime(property.getStart_time()));
            cvo.setVlanId(property.getEndpoint_z().vlan);

            String aUuid = Q.New(DahoCloudConnectionVO.class).eq(DahoCloudConnectionVO_.connectionId, property.getConnection_a().id).
                        select(DahoCloudConnectionVO_.uuid).findValue();
            String zUuid = Q.New(DahoConnectionVO.class).eq(DahoConnectionVO_.connectionId, property.getConnection_z().id).
                        select(DahoConnectionVO_.uuid).findValue();
            if (aUuid == null || zUuid == null) {
                // ignore vlls which connection is not in local
                continue;
            }
            cvo.setConnAUuid(aUuid);
            cvo.setConnZUuid(zUuid);

            DahoVllVbrRefVO ref = new DahoVllVbrRefVO();
            if (!exists) {
                String uuid = Platform.getUuid();
                cvo.setUuid(uuid);
                cvo.setAccountUuid(accountUuid);
                cvo = dbf.persistAndRefresh(cvo);
                if (property.getEndpoint_a() != null && property.getEndpoint_a().vbr != null && property.getEndpoint_a().vbr.id != null) {
                    // create DahoVllVbrRefVO
                    String vbrUuid = SQL.New("select uuid from VirtualBorderRouterVO where dataCenterUuid = :dcUuid and vbrId = :vbrId").
                            param("vbrId", property.getEndpoint_a().vbr.id).param("dcUuid", dcUuid).find();
                    ref.setVllUuid(uuid);
                    ref.setVbrUuid(vbrUuid);
                    if (vbrUuid != null) {
                        dbf.persistAndRefresh(ref);
                    }
                }
            } else {
                if (property.getEndpoint_a() != null && property.getEndpoint_a().vbr != null && property.getEndpoint_a().vbr.id != null) {
                    // create DahoVllVbrRefVO
                    String vbrUuid = SQL.New("select b.uuid from VirtualBorderRouterVO b where " +
                            "b.dataCenterUuid = :dcUuid and b.vbrId = :vbrId").
                            param("vbrId", property.getEndpoint_a().vbr.id).param("dcUuid", dcUuid).find();
                    ref.setVllUuid(cvo.getUuid());
                    ref.setVbrUuid(vbrUuid);
                    if (vbrUuid != null && !Q.New(DahoVllVbrRefVO.class).eq(DahoVllVbrRefVO_.vllUuid, cvo.getUuid()).isExists()) {
                        dbf.persistAndRefresh(ref);
                    }
                }
                cvo = dbf.updateAndRefresh(cvo);
            }
            allList.add(DahoVllsInventory.valueOf(cvo));
        }

        result.put("del", delList);
        result.put("all", allList);
        return result;
    }

    private void deleteVlls(List<String> uuids, final Completion completion) {
        deleteVlls(uuids, false, null, completion);
    }

    private void deleteVlls(List<String> uuids, boolean remote, final SessionInventory session, final Completion completion) {
        List<DahoVllsVO> dvos = Q.New(DahoVllsVO.class).in(DahoVllsVO_.uuid, uuids).list();
        if (dvos == null || dvos.size() == 0) {
            completion.success();
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-daho-vlls-%s",
                dvos.stream().map(DahoVllsVO::getUuid).collect(Collectors.toList()).toString()));
        final List<DahoVllsInventory> ctx = DahoVllsInventory.valueOf(dvos);
        String issuer = DahoVllsVO.class.getSimpleName();

        chain.then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer,
                        ctx, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        });
        if (remote && session != null) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    String issuer = DahoVllsVO.class.getSimpleName();
                    CascadeAction action = new HybridCascadeAction().
                            setRootIssuer(issuer).
                            setRootIssuerContext(ctx).
                            setParentIssuer(issuer).
                            setParentIssuerContext(ctx).
                            setActionCode(CascadeConstant.DELETION_FORCE_DELETE_CODE).
                            setFullTraverse(true);
                    ((HybridCascadeAction)action).setSession(session);
                    casf.asyncCascade(action, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer,
                        ctx, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CLEANUP_CODE, issuer,
                        ctx, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void syncVll(String dcUuid, SessionInventory session, ReturnValueCompletion<List<DahoVllsInventory>> completion) {
        GetDahoVllsMsg gcmsg = new GetDahoVllsMsg();
        gcmsg.setAccountUuid(session.getAccountUuid());

        bus.makeLocalServiceId(gcmsg, DahoConstant.SDK_SERVICE_ID);
        bus.send(gcmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    GetDahoVllsReply reply1 = reply.castReply();
                    Map<String, List<DahoVllsInventory>> result = syncVlls(reply1, dcUuid, session.getAccountUuid());
                    List<DahoVllsInventory> delList = result.get("del");
                    List<DahoVllsInventory> allList = result.get("all");
                    if (delList.size() > 0) {
                        deleteVlls(delList.stream().map(DahoVllsInventory::getUuid).collect(Collectors.toList()),
                                false, session, new Completion(completion) {
                            @Override
                            public void success() {
                                completion.success(allList);
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                            }
                        });
                    } else {
                        completion.success(allList);
                    }
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    private void handle(final APISyncDahoVllMsg msg) {
        APISyncDahoVllEvent evt = new APISyncDahoVllEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("sync-daho-vlls-%s", msg.getDataCenterUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                syncVll(msg.getDataCenterUuid(), msg.getSession(),
                        new ReturnValueCompletion<List<DahoVllsInventory>>(chain) {
                    @Override
                    public void success(List<DahoVllsInventory> invs) {
                        evt.setInventories(invs);
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void handle(final APIUpdateDahoCloudConnectionMsg msg) {
        APIUpdateDahoCloudConnectionEvent evt = new APIUpdateDahoCloudConnectionEvent(msg.getId());
        DahoCloudConnectionVO dvo = dbf.findByUuid(msg.getUuid(), DahoCloudConnectionVO.class);
        DebugUtils.Assert(dvo != null, "DahoCloudConnectionVO has been already deleted");

        boolean updated = false;
        if (msg.getDescription() != null) {
            dvo.setDescription(msg.getDescription());
            updated = true;
        }

        if (msg.getName() != null) {
            dvo.setName(msg.getName());
            updated = true;
        }
        if (updated) {
            dvo = dbf.updateAndRefresh(dvo);
        }
        evt.setInventory(DahoCloudConnectionInventory.valueOf(dvo));
        bus.publish(evt);
    }

    private void handle(final APIDeleteDahoCloudConnectionMsg msg) {
        APIDeleteDahoCloudConnectionEvent evt = new APIDeleteDahoCloudConnectionEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                deleteCloudConnection(CollectionDSL.list(msg.getUuid()), new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("delete-daho-cloud-connection-%s", msg.getUuid());
            }
        });
    }

    private Map<String, List<DahoCloudConnectionInventory>> syncCloudConns(final GetDahoCloudConnectionReply reply,
                                                                           String dcUuid, String accountUuid) {
        Map<String, List<DahoCloudConnectionInventory>> result = new HashMap<>();
        DataCenterVO dc = dbf.findByUuid(dcUuid, DataCenterVO.class);
        if (dc == null) {
            // if datacenter has been deleted, then return null
            return result;
        }
        List<DahoCloudConnectionProperty> synced = new ArrayList<>();
        for (DahoCloudConnectionProperty property: reply.getProperties()) {
            // we get the list in same datacenter
            if (property.getRegion().id != null && property.getRegion().id.equals(dc.getRegionId())) {
                synced.add(property);
            }
        }
        List<DahoCloudConnectionVO> vos = Q.New(DahoCloudConnectionVO.class).eq(DahoCloudConnectionVO_.dataCenterUuid, dcUuid).list();

        List<DahoCloudConnectionInventory> delList = new ArrayList<>();
        List<DahoCloudConnectionInventory> allList = new ArrayList<>();
        List<String> addList = new ArrayList<>();
        CollectionUtils.subtract(vos, synced).forEach(collection -> delList.add((DahoCloudConnectionInventory.valueOf((DahoCloudConnectionVO) collection))));
        CollectionUtils.subtract(synced, vos).forEach(collection -> addList.add(((DahoCloudConnectionProperty) collection).getId()));

        for (DahoCloudConnectionProperty property : synced) {
            DahoCloudConnectionVO cvo = null;
            boolean exists = false;

            if (addList.contains(property.getId())) {
                String uuid = Platform.getUuid();
                cvo = new DahoCloudConnectionVO();
                cvo.setUuid(uuid);
                cvo.setName(property.getId()); // name used id initially
                cvo.setAccountUuid(accountUuid);
            } else {
                cvo = Q.New(DahoCloudConnectionVO.class).
                        eq(DahoCloudConnectionVO_.connectionId, property.getId()).find();
                exists = true;
            }

            cvo.setConnectionId(property.getId());
            cvo.setCloud(DahoCloudType.valueOf(property.getCloud()));
            cvo.setBandwidth(property.getBandwidth());
            cvo.setUsedBandwidth(property.getUsed_bandwidth());
            cvo.setAccessPointId(property.getAccess_point().id);
            cvo.setAccessPointName(property.getAccess_point().name);
            cvo.setDataCenterUuid(dcUuid);

            if (!exists) {
                cvo = dbf.persistAndRefresh(cvo);
                createSystemTagForCloudConnection(cvo.getUuid(), DahoUtils.getHybridAccountUuidFromAccountUuid(accountUuid));
            } else {
                cvo = dbf.updateAndRefresh(cvo);
            }
            allList.add(DahoCloudConnectionInventory.valueOf(cvo));
        }

        result.put("del", delList);
        result.put("all", allList);
        return result;
    }

    private void syncCloudConnection(String dcUuid, SessionInventory session,
                                     final ReturnValueCompletion<List<DahoCloudConnectionInventory>> completion) {
        GetDahoCloudConnectionMsg gcmsg = new GetDahoCloudConnectionMsg();
        gcmsg.setAccountUuid(session.getAccountUuid());

        bus.makeLocalServiceId(gcmsg, DahoConstant.SDK_SERVICE_ID);
        bus.send(gcmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    GetDahoCloudConnectionReply reply1 = reply.castReply();
                    Map<String, List<DahoCloudConnectionInventory>> result = syncCloudConns(reply1, dcUuid, session.getAccountUuid());
                    List<DahoCloudConnectionInventory> delList = result.get("del");
                    List<DahoCloudConnectionInventory> allList = result.get("all");

                    if (delList.size() > 0) {
                        deleteCloudConnection(delList.stream().map(DahoCloudConnectionInventory::getUuid).collect(Collectors.toList()), new Completion(completion) {
                            @Override
                            public void success() {
                                completion.success(allList);
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                            }
                        });
                    } else {
                        completion.success(allList);
                    }
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    private void handle(final APISyncDahoCloudConnectionMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("sync-daho-cloud-side-connections-%s", msg.getDataCenterUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                APISyncDahoCloudConnectionEvent evt = new APISyncDahoCloudConnectionEvent(msg.getId());
                Map map = new HashMap();
                map.put("session", msg.getSession());
                syncCloudConnection(msg.getDataCenterUuid(), msg.getSession(),
                        new ReturnValueCompletion<List<DahoCloudConnectionInventory>>(chain) {
                            @Override
                            public void success(List<DahoCloudConnectionInventory> invs) {
                                afterSyncDahoCloudConnection(invs, map, new Completion(chain) {
                                    @Override
                                    public void success() {
                                        evt.setInventories(invs);
                                        bus.publish(evt);
                                        chain.next();
                                    }

                                    @Override
                                    public void fail(ErrorCode errorCode) {
                                        evt.setError(errorCode);
                                        bus.publish(evt);
                                        chain.next();
                                    }
                                });
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                evt.setError(errorCode);
                                bus.publish(evt);
                                chain.next();
                            }
                        });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void handle(final APIUpdateDahoDataCenterConnectionMsg msg) {
        APIUpdateDahoDataCenterConnectionEvent evt = new APIUpdateDahoDataCenterConnectionEvent(msg.getId());
        DahoConnectionVO dvo = dbf.findByUuid(msg.getUuid(), DahoConnectionVO.class);
        DebugUtils.Assert(dvo != null, "DahoConnectionVO has been already deleted");

        boolean updated = false;
        if (msg.getDescription() != null) {
            dvo.setDescription(msg.getDescription());
            updated = true;
        }

        if (msg.getName() != null) {
            dvo.setName(msg.getName());
            updated = true;
        }

        if (updated) {
            dvo = dbf.updateAndRefresh(dvo);
        }
        evt.setInventory(DahoConnectionInventory.valueOf(dvo));
        bus.publish(evt);
    }

    private void deleteCloudConnection(List<String> uuids, final Completion completion) {
        List<DahoCloudConnectionVO> dvos = Q.New(DahoCloudConnectionVO.class).in(DahoCloudConnectionVO_.uuid, uuids).list();
        if (dvos == null || dvos.size() == 0) {
            completion.success();
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-daho-cloud-connection-%s",
                dvos.stream().map(DahoCloudConnectionVO::getUuid).collect(Collectors.toList()).toString()));

        chain.then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, DahoCloudConnectionVO.class.getSimpleName(),
                        DahoCloudConnectionInventory.valueOf(dvos), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, DahoCloudConnectionVO.class.getSimpleName(),
                        DahoCloudConnectionInventory.valueOf(dvos), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CLEANUP_CODE, DahoCloudConnectionVO.class.getSimpleName(),
                        DahoCloudConnectionInventory.valueOf(dvos), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void deleteConnection(List<String> uuids, final Completion completion) {
        List<DahoConnectionVO> dvos = Q.New(DahoConnectionVO.class).in(DahoConnectionVO_.uuid, uuids).list();
        if (dvos == null || dvos.size() == 0) {
            completion.success();
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-daho-datacenter-connection-%s",
                dvos.stream().map(DahoConnectionVO::getUuid).collect(Collectors.toList()).toString()));

        chain.then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, DahoConnectionVO.class.getSimpleName(),
                        DahoConnectionInventory.valueOf(dvos), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(final FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, DahoConnectionVO.class.getSimpleName(),
                        DahoConnectionInventory.valueOf(dvos), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                casf.asyncCascade(CascadeConstant.DELETION_CLEANUP_CODE, DahoConnectionVO.class.getSimpleName(),
                        DahoConnectionInventory.valueOf(dvos), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void handle(final APIDeleteDahoDataCenterConnectionMsg msg) {
        APIDeleteDahoDataCenterConnectionEvent evt = new APIDeleteDahoDataCenterConnectionEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                deleteConnection(CollectionDSL.list(msg.getUuid()), new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("delete-daho-datacenter-connection-%s", msg.getUuid());
            }
        });
    }

    private DahoDCAccessVO getAccessDetails(DahoConnectionProperty.Access access) {
        DahoDCAccessVO details = new DahoDCAccessVO();
        details.setBuilding(access.building);
        details.setDcLocation(access.dc_location);
        details.setDcName(access.dc_name);
        details.setRoom(access.room);
        details.setDeviceNo(access.device_no);
        details.setDeviceType(access.device_type);
        details.setPortNo(access.port_no);
        details.setRackNo(access.rack_no);
        return details;
    }

    private void updateAccessDetails(DahoDCAccessVO old, DahoDCAccessVO update) {
        old.setRackNo(update.getRackNo());
        old.setBuilding(update.getBuilding());
        old.setDcName(update.getDcName());
        old.setDcLocation(update.getDcLocation());
        old.setRackNo(update.getRoom());
        old.setPortNo(update.getPortNo());
        old.setDeviceType(update.getDeviceType());
        old.setDeviceNo(update.getDeviceNo());
        dbf.update(old);
    }

    private void createSystemTagForCloudConnection(String connUuid, String hybridAccountUuid) {
        if (connUuid == null || hybridAccountUuid == null) {
            return;
        }
        SystemTagCreator creator = DahoConnectionSystemTags.HYBRID_ACCOUNT_CLOUD_UUID.newSystemTagCreator(connUuid);
        creator.inherent = true;
        creator.recreate = true;
        creator.setTagByTokens(map(e(
                DahoConnectionSystemTags.HYBRID_ACCOUNT_UUID_TOKEN, hybridAccountUuid)));
        creator.create();
    }

    private void createSystemTagForDCConnection(String connUuid, String hybridAccountUuid) {
        if (connUuid == null || hybridAccountUuid == null) {
            return;
        }
        SystemTagCreator creator = DahoConnectionSystemTags.HYBRID_ACCOUNT_DC_UUID.newSystemTagCreator(connUuid);
        creator.inherent = true;
        creator.recreate = true;
        creator.setTagByTokens(map(e(
                DahoConnectionSystemTags.HYBRID_ACCOUNT_UUID_TOKEN, hybridAccountUuid)));
        creator.create();
    }

    private Map<String, List<DahoConnectionInventory>> syncDataCenterConns(final GetDahoDataCenterConnectionsReply reply, String accountUuid) {
        Map<String, List<DahoConnectionInventory>> result = new HashMap<>();
        //TODO：find the difference part
        List<DahoConnectionVO> vos = Q.New(DahoConnectionVO.class).list();

        List<DahoConnectionInventory> delList = new ArrayList<>();
        List<DahoConnectionInventory> allList = new ArrayList<>();
        List<String> addList = new ArrayList<>();
        CollectionUtils.subtract(vos, reply.getProperties()).forEach(collection -> delList.add((DahoConnectionInventory.valueOf((DahoConnectionVO) collection))));
        CollectionUtils.subtract(reply.getProperties(), vos).forEach(collection -> addList.add(((DahoConnectionProperty) collection).getId()));

        for (DahoConnectionProperty property : reply.getProperties()) {
            DahoConnectionVO cvo = null;
            boolean exists = false;

            if (addList.contains(property.getId())) {
                cvo = new DahoConnectionVO();
                String uuid = Platform.getUuid();
                cvo.setUuid(uuid);
                cvo.setAccountUuid(accountUuid);
            } else {
                DahoConnectionVO old = Q.New(DahoConnectionVO.class).
                        eq(DahoConnectionVO_.connectionId, property.getId()).find();
                cvo = old;
                exists = true;
            }

            cvo.setName(property.getName());
            cvo.setConnectionId(property.getId());
            cvo.setStatus(DahoConnectionStatus.get(property.getStatus()));
            cvo.setBandwidthMbps(property.getBandwidth());
            cvo.setType(DahoConnectionType.dc);
            DahoDCAccessVO details = getAccessDetails(property.getAccess_detail());

            cvo.setContractEndTime(DahoUtils.fmtTime(property.getContract().end_time));

            Timestamp createTime = DahoUtils.fmtTime(property.getCreate_time());
            cvo.setCreateDate(createTime);
            if (!exists) {
                cvo = dbf.persistAndRefresh(cvo);
                details.setUuid(cvo.getUuid());
                dbf.persist(details);
                createSystemTagForDCConnection(cvo.getUuid(), DahoUtils.getHybridAccountUuidFromAccountUuid(accountUuid));
            } else {
                cvo = dbf.updateAndRefresh(cvo);
                updateAccessDetails(cvo.getAccess(), details);
            }
            allList.add(DahoConnectionInventory.valueOf(cvo));
        }

        result.put("del", delList);
        result.put("all", allList);
        return result;
    }

    private void syncDahoConnection(final SessionInventory session, ReturnValueCompletion<List<DahoConnectionInventory>> completion) {
        GetDahoDataCenterConnectionsMsg gcmsg = new GetDahoDataCenterConnectionsMsg();
        gcmsg.setAccountUuid(session.getAccountUuid());

        bus.makeLocalServiceId(gcmsg, DahoConstant.SDK_SERVICE_ID);
        bus.send(gcmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    GetDahoDataCenterConnectionsReply reply1 = reply.castReply();
                    Map<String, List<DahoConnectionInventory>> result = syncDataCenterConns(reply1, session.getAccountUuid());
                    List<DahoConnectionInventory> delList = result.get("del");
                    List<DahoConnectionInventory> allList = result.get("all");

                    if (delList.size() > 0) {
                        deleteConnection(delList.stream().map(DahoConnectionInventory::getUuid).collect(Collectors.toList()), new Completion(completion) {
                            @Override
                            public void success() {
                                completion.success(allList);
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                            }
                        });
                    } else {
                        completion.success(allList);
                    }
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    private void handle(final APISyncDahoDataCenterConnectionMsg msg) {
        APISyncDahoDataCenterConnectionEvent evt = new APISyncDahoDataCenterConnectionEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return "sync-daho-dc-side-connections";
            }

            @Override
            public void run(SyncTaskChain chain) {
                syncDahoConnection(msg.getSession(), new ReturnValueCompletion<List<DahoConnectionInventory>>(chain) {
                    @Override
                    public void success(List<DahoConnectionInventory> invs) {
                        evt.setInventories(invs);
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void handleLocalMessage(Message msg) {
        bus.dealWithUnknownMessage(msg);
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(DahoConstant.SERVICE_ID);
    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        return CollectionDSL.list(
                APIDeleteDahoCloudConnectionMsg.class, APIDeleteDahoDataCenterConnectionMsg.class,
                APIQueryDahoCloudConnectionMsg.class, APIQueryDahoDataCenterConnectionMsg.class,
                APIQueryDahoVllMsg.class, APISyncDahoCloudConnectionMsg.class, APISyncDahoDataCenterConnectionMsg.class,
                APISyncDahoVllMsg.class, APIUpdateDahoCloudConnectionMsg.class, APIUpdateDahoDataCenterConnectionMsg.class,
                APIDeleteDahoVllMsg.class, APIUpdateDahoVllMsg.class, APICreateDahoVllRemoteMsg.class,
                APIQueryDahoVllVbrRefMsg.class);
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.FRONT;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        hyimpl.intercept(msg);
        return msg;
    }

    {
        PluginDSL.PluginDefinition definition = new PluginDSL.PluginDefinition(DahoManagerImpl.class);
        definition.newExtension().extensionClass(GlobalApiMessageInterceptor.class);
    }

    @Override
    public void otherPointAfterDatacenterSync(DataCenterInventory inv, Map map, Completion completion) {
        if (!Q.New(HybridAccountVO.class).eq(HybridAccountVO_.type, HybridType.daho).
                eq(HybridAccountVO_.current, "true").isExists()) {
            // no daho ak, skip the following sync work
            completion.success();
            return;
        }
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("sync daho resources after sync datacenter [%s]", inv.getUuid()));
        chain.then(new NoRollbackFlow() {
            String __name__ = String.format("sync-daho-cloud-connections-in-datacenter-%s", inv.getUuid());
            @Override
            public void run(FlowTrigger trigger, Map data) {
                dahoEmitter.syncDahoCloudConnection(inv, map, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    @Override
    public void syncDahoCloudConnection(DataCenterInventory inv, Map map, Completion completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("sync-daho-cloud-side-connections-%s", inv.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                syncCloudConnection(inv.getUuid(), (SessionInventory) map.get("session"),
                        new ReturnValueCompletion<List<DahoCloudConnectionInventory>>(chain) {
                            @Override
                            public void success(List<DahoCloudConnectionInventory> invs) {
                                afterSyncDahoCloudConnection(invs, map, completion);
                                chain.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                completion.fail(errorCode);
                                chain.next();
                            }
                        });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    @Override
    public void afterSyncDahoCloudConnection(List<DahoCloudConnectionInventory> invs, Map map, Completion completion) {
        List<String> dcUuids = invs.stream().map(DahoCloudConnectionInventory::getDataCenterUuid).distinct().collect(Collectors.toList());
        if (invs.isEmpty()) {
            completion.success();
        } else {
            DataCenterInventory inv = DataCenterInventory.valueOf(dbf.findByUuid(dcUuids.get(0), DataCenterVO.class));
            syncDahoVll(inv, map, completion);
        }
    }

    @Override
    public void syncDahoVll(DataCenterInventory inv, Map map, Completion completion) {
        thdf.chainSubmit(new ChainTask(completion) {
            @Override
            public String getSyncSignature() {
                return String.format("sync-daho-vlls-%s", inv.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                syncVll(inv.getUuid(), (SessionInventory) map.get("session"),
                        new ReturnValueCompletion<List<DahoVllsInventory>>(chain) {
                    @Override
                    public void success(List<DahoVllsInventory> returnValue) {
                        completion.success();
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }
}
