package org.zstack.policyRoute;

import org.zstack.header.search.Inventory;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @ Author : yh.w
 * @ Date   : Created in 15:05 2019/7/22
 */
@Inventory(mappingVOClass = PolicyRouteTableVRouterRefVO.class)
public class PolicyRouteTableVRouterRefInventory {

    private long id;

    private String tableUuid;

    private String vRouterUuid;

    private Timestamp createDate;

    private Timestamp lastOpDate;

    public PolicyRouteTableVRouterRefInventory() {
    }

    public PolicyRouteTableVRouterRefInventory(PolicyRouteTableVRouterRefVO vo) {
        this.id = vo.getId();
        this.tableUuid = vo.getTableUuid();
        this.vRouterUuid = vo.getvRouterUuid();
        this.createDate = vo.getCreateDate();
        this.lastOpDate = vo.getLastOpDate();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getTableUuid() {
        return tableUuid;
    }

    public void setTableUuid(String tableUuid) {
        this.tableUuid = tableUuid;
    }

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public static PolicyRouteTableVRouterRefInventory valueOf(PolicyRouteTableVRouterRefVO vo) {
        return new PolicyRouteTableVRouterRefInventory(vo);
    }

    public static List<PolicyRouteTableVRouterRefInventory> valueOf(Collection<PolicyRouteTableVRouterRefVO> vos) {
        List<PolicyRouteTableVRouterRefInventory> invs = new ArrayList<>();
        for (PolicyRouteTableVRouterRefVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }
}
