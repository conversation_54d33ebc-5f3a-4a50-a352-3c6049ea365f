package org.zstack.policyRoute

import org.zstack.policyRoute.APICreatePolicyRouteTableEvent

doc {
    title "CreatePolicyRouteTable"

    category "policyRoute"

    desc """创建策略路由表"""

    rest {
        request {
			url "POST /v1/policy-routes/tables"

			header (Authorization: 'OAuth the-session-uuid')

            clz APICreatePolicyRouteTableMsg.class

            desc """"""
            
			params {

				column {
					name "vRouterUuid"
					enclosedIn "params"
					desc "云路由uuid"
					location "body"
					type "String"
					optional false
					since "3.6"
				}
				column {
					name "number"
					enclosedIn "params"
					desc "表名"
					location "body"
					type "Integer"
					optional false
					since "3.6"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "3.6"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "3.6"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "3.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.6"
				}
			}
        }

        response {
            clz APICreatePolicyRouteTableEvent.class
        }
    }
}