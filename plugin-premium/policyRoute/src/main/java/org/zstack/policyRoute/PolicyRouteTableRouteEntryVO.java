package org.zstack.policyRoute;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.vo.ForeignKey;
import org.zstack.header.vo.ResourceVO;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * @ Author : yh.w
 * @ Date   : Created in 14:11 2019/7/19
 */
@Entity
@Table
public class PolicyRouteTableRouteEntryVO extends ResourceVO implements OwnedByAccount {

    @Column
    @ForeignKey(parentEntityClass = PolicyRouteTableVO.class, onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String tableUuid;

    @Column
    private String destinationCidr;

    @Column
    private String nextHopIp;

    @Column
    private int distance;

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;

    @Transient
    private String accountUuid;

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public int getDistance() {
        return distance;
    }

    public void setDistance(int distance) {
        this.distance = distance;
    }

    public String getTableUuid() {
        return tableUuid;
    }

    public void setTableUuid(String tableUuid) {
        this.tableUuid = tableUuid;
    }

    public String getDestinationCidr() {
        return destinationCidr;
    }

    public void setDestinationCidr(String destinationCidr) {
        this.destinationCidr = destinationCidr;
    }

    public String getNextHopIp() {
        return nextHopIp;
    }

    public void setNextHopIp(String nextHopIp) {
        this.nextHopIp = nextHopIp;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }
}
