package org.zstack.policyRoute

import org.zstack.policyRoute.APIDeletePolicyRouteTableEvent

doc {
    title "DeletePolicyRouteTable"

    category "policyRoute"

    desc """删除策略路由表"""

    rest {
        request {
			url "DELETE /v1/policy-routes/tables/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeletePolicyRouteTableMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "3.6"
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc ""
					location "body"
					type "String"
					optional true
					since "3.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.6"
				}
			}
        }

        response {
            clz APIDeletePolicyRouteTableEvent.class
        }
    }
}