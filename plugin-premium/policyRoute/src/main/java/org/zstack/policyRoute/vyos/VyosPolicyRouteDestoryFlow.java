package org.zstack.policyRoute.vyos;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.header.vm.VmInstanceInventory;
import org.zstack.header.vm.VmInstanceSpec;
import org.zstack.header.vpc.VpcRouterVmVO;
import org.zstack.header.vpc.ha.VpcHaGroupApplianceVmRefVO;
import org.zstack.header.vpc.ha.VpcHaGroupApplianceVmRefVO_;
import org.zstack.network.service.virtualrouter.VirtualRouterConstant;
import org.zstack.network.service.virtualrouter.VirtualRouterVmInventory;
import org.zstack.network.service.virtualrouter.VirtualRouterVmVO;
import org.zstack.policyRoute.*;
import org.zstack.vpc.ha.VpcHaGroupOperator;

import java.util.List;
import java.util.Map;


/**
 * @ Author : yh.w
 * @ Date   : Created in 18:33 2019/7/30
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class VyosPolicyRouteDestoryFlow extends NoRollbackFlow {
    @Autowired
    private PolicyRouteTableConfigProxy tcp;
    @Autowired
    private PolicyRouteRuleSetConfigProxy rcp;
    @Autowired
    private DatabaseFacade dbf;

    String __name__ = "destroy-vrouter-policy-routes";

    @Override
    public void run(FlowTrigger trigger, Map data) {
        VmInstanceSpec spec = (VmInstanceSpec)data.get(VmInstanceConstant.Params.VmInstanceSpec.toString());
        VmInstanceInventory vm = spec.getVmInventory();

        VirtualRouterVmVO vrVo = dbf.findByUuid(vm.getUuid(), VirtualRouterVmVO.class);
        if (vrVo.isHaEnabled()) {
            trigger.next();
            return;
        }

        List<String> tables = tcp.getServiceUuidsByRouterUuid(vm.getUuid(), PolicyRouteTableVO.class.getSimpleName());
        if (CollectionUtils.isNotEmpty(tables)) {
            SQL.New(PolicyRouteTableRouteEntryVO.class).in(PolicyRouteTableRouteEntryVO_.tableUuid, tables).delete();
            SQL.New(PolicyRouteRuleVO.class).in(PolicyRouteRuleVO_.tableUuid, tables).delete();
            dbf.removeByPrimaryKeys(tables, PolicyRouteTableVO.class);
        }

        List<String> ruleSets = rcp.getServiceUuidsByRouterUuid(vm.getUuid(), PolicyRouteRuleSetVO.class.getSimpleName());
        if (CollectionUtils.isNotEmpty(ruleSets)) {
            SQL.New(PolicyRouteRuleVO.class).in(PolicyRouteRuleVO_.ruleSetUuid, ruleSets).delete();
            dbf.removeByPrimaryKeys(ruleSets, PolicyRouteRuleSetVO.class);
        }

        trigger.next();
    }
}
