package org.zstack.policyRoute;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * @ Author : yh.w
 * @ Date   : Created in 10:18 2019/7/26
 */
@RestResponse
public class APIDeletePolicyRouteRuleEvent extends APIEvent {
    public APIDeletePolicyRouteRuleEvent() {
    }

    public APIDeletePolicyRouteRuleEvent(String apiId) {
        super(apiId);
    }

    public static APIDeletePolicyRouteRuleEvent __example__() {
        APIDeletePolicyRouteRuleEvent event = new APIDeletePolicyRouteRuleEvent();
        return event;
    }
}
