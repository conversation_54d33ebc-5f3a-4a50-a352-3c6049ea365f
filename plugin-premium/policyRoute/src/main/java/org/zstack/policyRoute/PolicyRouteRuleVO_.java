package org.zstack.policyRoute;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

/**
 * @ Author : yh.w
 * @ Date   : Created in 11:29 2019/7/22
 */
@StaticMetamodel(PolicyRouteRuleVO.class)
public class PolicyRouteRuleVO_ extends ResourceVO_ {
    public static volatile SingularAttribute<PolicyRouteRuleVO, String> ruleNumber;
    public static volatile SingularAttribute<PolicyRouteRuleVO, String> destPort;
    public static volatile SingularAttribute<PolicyRouteRuleVO, String> sourcePort;
    public static volatile SingularAttribute<PolicyRouteRuleVO, String> sourceIp;
    public static volatile SingularAttribute<PolicyRouteRuleVO, String> destIp;
    public static volatile SingularAttribute<PolicyRouteRuleVO, PolicyRouteRuleProtocol> protocol;
    public static volatile SingularAttribute<PolicyRouteRuleVO, String> tableUuid;
    public static volatile SingularAttribute<PolicyRouteRuleVO, String> ruleSetUuid;
    public static volatile SingularAttribute<PolicyRouteRuleVO, PolicyRouteRuleState> state;
    public static volatile SingularAttribute<PolicyRouteRuleVO, Timestamp> createDate;
    public static volatile SingularAttribute<PolicyRouteRuleVO, Timestamp> lastOpDate;
}
