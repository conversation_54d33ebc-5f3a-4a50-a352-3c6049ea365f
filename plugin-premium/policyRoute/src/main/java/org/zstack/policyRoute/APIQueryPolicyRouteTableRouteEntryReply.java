package org.zstack.policyRoute;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;

import java.util.List;

import static java.util.Arrays.asList;

/**
 * @ Author : yh.w
 * @ Date   : Created in 13:57 2019/7/26
 */
@RestResponse(allTo = "inventories")
public class APIQueryPolicyRouteTableRouteEntryReply extends APIQueryReply {
    private List<PolicyRouteTableRouteEntryInventory> inventories;

    public List<PolicyRouteTableRouteEntryInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<PolicyRouteTableRouteEntryInventory> inventories) {
        this.inventories = inventories;
    }

    public static APIQueryPolicyRouteTableRouteEntryReply __example__() {
        APIQueryPolicyRouteTableRouteEntryReply reply = new APIQueryPolicyRouteTableRouteEntryReply();
        PolicyRouteTableRouteEntryInventory inventory = new PolicyRouteTableRouteEntryInventory();
        inventory.setDistance(1);
        inventory.setNextHopIp("***********");
        inventory.setTableUuid(uuid());
        inventory.setDestinationCidr("***********/24");
        reply.setInventories(asList(inventory));
        return reply;
    }
}
