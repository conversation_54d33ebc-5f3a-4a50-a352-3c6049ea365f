package org.zstack.policyRoute;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * @ Author : yh.w
 * @ Date   : Created in 11:20 2019/7/25
 */
@RestResponse
public class APIDeletePolicyRouteRuleSetEvent extends APIEvent {
    public APIDeletePolicyRouteRuleSetEvent() {
    }

    public APIDeletePolicyRouteRuleSetEvent(String apiId) {
        super(apiId);
    }

    public static APIDeletePolicyRouteRuleSetEvent __example__() {
        APIDeletePolicyRouteRuleSetEvent event = new APIDeletePolicyRouteRuleSetEvent();
        return event;
    }
}
