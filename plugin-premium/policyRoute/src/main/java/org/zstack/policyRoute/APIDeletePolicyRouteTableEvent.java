package org.zstack.policyRoute;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * @ Author : yh.w
 * @ Date   : Created in 19:11 2019/7/25
 */
@RestResponse
public class APIDeletePolicyRouteTableEvent extends APIEvent {

    public APIDeletePolicyRouteTableEvent() {
    }

    public APIDeletePolicyRouteTableEvent(String apiId) {
        super(apiId);
    }

    public static APIDeletePolicyRouteTableEvent __example__() {
        APIDeletePolicyRouteTableEvent event = new APIDeletePolicyRouteTableEvent();
        return event;
    }
}