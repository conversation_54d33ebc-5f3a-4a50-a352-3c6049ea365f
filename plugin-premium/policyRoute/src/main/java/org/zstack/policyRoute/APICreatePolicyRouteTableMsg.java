package org.zstack.policyRoute;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.network.service.virtualrouter.VirtualRouterVmVO;

/**
 * @ Author : yh.w
 * @ Date   : Created in 15:55 2019/7/18
 */
@TagResourceType(PolicyRouteTableVO.class)
@Action(category = PolicyRouteConstants.ACTION_CATEGORY)
@RestRequest(
        path = "/policy-routes/tables",
        method = HttpMethod.POST,
        responseClass = APICreatePolicyRouteTableEvent.class,
        parameterName = "params"
)
public class APICreatePolicyRouteTableMsg extends APICreateMessage implements APIAuditor {

    @APIParam(resourceType = VirtualRouterVmVO.class, nonempty = true, checkAccount = true)
    private String vRouterUuid;

    @APIParam(maxLength = 3, numberRange = {1,179})
    private Integer number;

    @APIParam(required = false, maxLength = 255)
    private String description;

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(rsp.isSuccess() ? ((APICreatePolicyRouteTableEvent)rsp).getInventory().getUuid() : "", PolicyRouteTableVO.class);
    }

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static APICreatePolicyRouteTableMsg __example__() {
        APICreatePolicyRouteTableMsg msg = new APICreatePolicyRouteTableMsg();
        msg.setNumber(1);
        msg.setvRouterUuid(uuid());
        msg.setDescription("description");
        return msg;
    }
}
