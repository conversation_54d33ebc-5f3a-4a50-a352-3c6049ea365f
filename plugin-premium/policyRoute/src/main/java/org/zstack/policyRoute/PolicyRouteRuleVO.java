package org.zstack.policyRoute;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.vo.ForeignKey;
import org.zstack.header.vo.ResourceVO;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * @ Author : yh.w
 * @ Date   : Created in 14:11 2019/7/19
 */
@Entity
@Table
public class PolicyRouteRuleVO extends ResourceVO implements OwnedByAccount {

    @Column
    @ForeignKey(parentEntityClass = PolicyRouteRuleSetVO.class, onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String ruleSetUuid;

    @Column
    @ForeignKey(parentEntityClass = PolicyRouteTableVO.class, onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String tableUuid;

    @Column
    private String destIp;

    @Column
    private String sourceIp;

    @Column
    @Enumerated(value = EnumType.STRING)
    private PolicyRouteRuleProtocol protocol;

    @Column
    private String destPort;

    @Column
    private String sourcePort;

    @Column
    private int ruleNumber;

    @Column
    @Enumerated(EnumType.STRING)
    private PolicyRouteRuleState state;

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;

    @Transient
    private String accountUuid;

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public PolicyRouteRuleProtocol getProtocol() {
        return protocol;
    }

    public void setProtocol(PolicyRouteRuleProtocol protocol) {
        this.protocol = protocol;
    }

    public int getRuleNumber() {
        return ruleNumber;
    }

    public void setRuleNumber(int ruleNumber) {
        this.ruleNumber = ruleNumber;
    }

    public String getRuleSetUuid() {
        return ruleSetUuid;
    }

    public void setRuleSetUuid(String ruleSetUuid) {
        this.ruleSetUuid = ruleSetUuid;
    }

    public String getTableUuid() {
        return tableUuid;
    }

    public void setTableUuid(String tableUuid) {
        this.tableUuid = tableUuid;
    }

    public String getDestIp() {
        return destIp;
    }

    public void setDestIp(String destIp) {
        this.destIp = destIp;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public String getDestPort() {
        return destPort;
    }

    public void setDestPort(String destPort) {
        this.destPort = destPort;
    }

    public String getSourcePort() {
        return sourcePort;
    }

    public void setSourcePort(String sourcePort) {
        this.sourcePort = sourcePort;
    }

    public PolicyRouteRuleState getState() {
        return state;
    }

    public void setState(PolicyRouteRuleState state) {
        this.state = state;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public PolicyRouteCommands.RuleInfo toRuleInfo() {
        PolicyRouteCommands.RuleInfo ruleInfo = new PolicyRouteCommands.RuleInfo();
        ruleInfo.setRuleNumber(this.ruleNumber);
        ruleInfo.setDestIp(this.destIp);
        if (this.protocol != null) {
            ruleInfo.setProtocol(this.protocol.toString());
        }
        ruleInfo.setSourceIp(this.sourceIp);
        ruleInfo.setDestPort(this.destPort);
        ruleInfo.setSourcePort(this.sourcePort);
        ruleInfo.setState(this.state.toString());
        return ruleInfo;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }
}
