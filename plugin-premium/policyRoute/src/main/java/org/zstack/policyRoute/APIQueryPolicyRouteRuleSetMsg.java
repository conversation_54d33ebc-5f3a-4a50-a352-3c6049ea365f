package org.zstack.policyRoute;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;

/**
 * @ Author : yh.w
 * @ Date   : Created in 13:27 2019/7/26
 */
@RestRequest(
        path = "/policy-routes/rulesets",
        optionalPaths = {"/policy-routes/rulesets/{uuid}"},
        responseClass = APIQueryPolicyRouteRuleSetReply.class,
        method = HttpMethod.GET
)
@Action(category = PolicyRouteConstants.ACTION_CATEGORY, names = {"read"})
@AutoQuery(replyClass = APIQueryPolicyRouteRuleSetReply.class, inventoryClass = PolicyRouteRuleSetInventory.class)
public class APIQueryPolicyRouteRuleSetMsg extends APIQueryMessage {
}
