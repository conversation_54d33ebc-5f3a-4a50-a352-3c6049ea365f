package org.zstack.policyRoute

import org.zstack.header.errorcode.ErrorCode
import org.zstack.policyRoute.PolicyRouteRuleSetInventory

doc {

	title "更新策略路由规则集属性返回"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.policyRoute.APIUpdatePolicyRouteRuleSetEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "3.6"
		clz ErrorCode.class
	}
	ref {
		name "inventory"
		path "org.zstack.policyRoute.APIUpdatePolicyRouteRuleSetEvent.inventory"
		desc "null"
		type "PolicyRouteRuleSetInventory"
		since "3.6"
		clz PolicyRouteRuleSetInventory.class
	}
}
