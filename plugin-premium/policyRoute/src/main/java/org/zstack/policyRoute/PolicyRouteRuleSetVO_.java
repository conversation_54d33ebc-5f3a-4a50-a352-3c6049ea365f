package org.zstack.policyRoute;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

/**
 * @ Author : yh.w
 * @ Date   : Created in 14:15 2019/7/22
 */
@StaticMetamodel(PolicyRouteRuleSetVO.class)
public class PolicyRouteRuleSetVO_ extends ResourceVO_ {
    public static volatile SingularAttribute<PolicyRouteRuleSetVO, String> name;
    public static volatile SingularAttribute<PolicyRouteRuleSetVO, String> vyosName;
    public static volatile SingularAttribute<PolicyRouteRuleSetVO, String> description;
    public static volatile SingularAttribute<PolicyRouteRuleSetVO, PolicyRouteType> type;
    public static volatile SingularAttribute<PolicyRouteRuleSetVO, Timestamp> createDate;
    public static volatile SingularAttribute<PolicyRouteRuleSetVO, Timestamp> lastOpDate;
}
