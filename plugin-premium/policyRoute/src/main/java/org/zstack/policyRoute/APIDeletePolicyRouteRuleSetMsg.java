package org.zstack.policyRoute;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.rest.RestRequest;

/**
 * @ Author : yh.w
 * @ Date   : Created in 11:00 2019/7/19
 */
@RestRequest(
        path = "/policy-routes/ruleSets/{uuid}",
        method = HttpMethod.DELETE,
        responseClass = APIDeletePolicyRouteRuleSetEvent.class
)
public class APIDeletePolicyRouteRuleSetMsg extends APIDeleteMessage {

    @APIParam(resourceType = PolicyRouteRuleSetVO.class, successIfResourceNotExisting = true, nonempty = true, checkAccount = true)
    private String uuid;

    @APINoSee
    private String vRouterUuid;

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public static APIDeletePolicyRouteRuleSetMsg __example__() {
        APIDeletePolicyRouteRuleSetMsg msg = new APIDeletePolicyRouteRuleSetMsg();
        msg.setUuid(uuid());
        return msg;
    }
}
