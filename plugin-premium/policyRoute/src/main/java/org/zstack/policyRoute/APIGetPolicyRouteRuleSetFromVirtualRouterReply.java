package org.zstack.policyRoute;

import org.zstack.header.message.APIReply;
import org.zstack.header.rest.RestResponse;

import java.util.Arrays;
import java.util.List;

@RestResponse(allTo = "inventories")
public class APIGetPolicyRouteRuleSetFromVirtualRouterReply extends APIReply {
    private List<PolicyRouteRuleSetInventory> inventories;

    public List<PolicyRouteRuleSetInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<PolicyRouteRuleSetInventory> inventories) {
        this.inventories = inventories;
    }

    public static APIGetPolicyRouteRuleSetFromVirtualRouterReply __example__() {
        APIGetPolicyRouteRuleSetFromVirtualRouterReply reply = new APIGetPolicyRouteRuleSetFromVirtualRouterReply();

        PolicyRouteRuleSetInventory ruleSetInventory = new PolicyRouteRuleSetInventory();
        ruleSetInventory.setUuid(uuid());
        ruleSetInventory.setName("policy-rule-set");
        ruleSetInventory.setDescription("policy rule set");
        ruleSetInventory.setType(PolicyRouteType.User.toString());

        reply.setInventories(Arrays.asList(ruleSetInventory));

        return reply;
    }
}
