package org.zstack.policyRoute

import org.zstack.policyRoute.APIUpdatePolicyRouteRuleSetEvent

doc {
    title "UpdatePolicyRouteRuleSet"

    category "policyRoute"

    desc """更新策略路由规则集属性"""

    rest {
        request {
			url "PUT /v1/policy-routes/ruleSets/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIUpdatePolicyRouteRuleSetMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn "updatePolicyRouteRuleSet"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "3.6"
				}
				column {
					name "name"
					enclosedIn "updatePolicyRouteRuleSet"
					desc "资源名称"
					location "body"
					type "String"
					optional true
					since "3.6"
				}
				column {
					name "description"
					enclosedIn "updatePolicyRouteRuleSet"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "3.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.6"
				}
			}
        }

        response {
            clz APIUpdatePolicyRouteRuleSetEvent.class
        }
    }
}