package org.zstack.policyRoute;

import org.zstack.header.search.Inventory;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @ Author : yh.w
 * @ Date   : Created in 15:05 2019/7/22
 */
@Inventory(mappingVOClass = PolicyRouteRuleSetVRouterRefVO.class)
public class PolicyRouteRuleSetVRouterRefInventory {

    private long id;

    private String vRouterUuid;

    private String ruleSetUuid;

    private Timestamp createDate;

    private Timestamp lastOpDate;

    public PolicyRouteRuleSetVRouterRefInventory() {
    }

    public PolicyRouteRuleSetVRouterRefInventory(PolicyRouteRuleSetVRouterRefVO vo) {
        this.id = vo.getId();
        this.vRouterUuid = vo.getvRouterUuid();
        this.ruleSetUuid = vo.getRuleSetUuid();
        this.createDate = vo.getCreateDate();
        this.lastOpDate = vo.getLastOpDate();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public String getRuleSetUuid() {
        return ruleSetUuid;
    }

    public void setRuleSetUuid(String ruleSetUuid) {
        this.ruleSetUuid = ruleSetUuid;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public static PolicyRouteRuleSetVRouterRefInventory valueOf(PolicyRouteRuleSetVRouterRefVO vo) {
        return new PolicyRouteRuleSetVRouterRefInventory(vo);
    }

    public static List<PolicyRouteRuleSetVRouterRefInventory> valueOf(Collection<PolicyRouteRuleSetVRouterRefVO> vos) {
        List<PolicyRouteRuleSetVRouterRefInventory> invs = new ArrayList<>();
        for (PolicyRouteRuleSetVRouterRefVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }
}
