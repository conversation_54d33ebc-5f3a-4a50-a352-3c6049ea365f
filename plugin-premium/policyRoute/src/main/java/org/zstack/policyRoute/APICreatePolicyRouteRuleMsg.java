package org.zstack.policyRoute;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;

/**
 * @ Author : yh.w
 * @ Date   : Created in 10:46 2019/7/19
 */
@TagResourceType(PolicyRouteRuleVO.class)
@Action(category = PolicyRouteConstants.ACTION_CATEGORY)
@RestRequest(
        path = "/policy-routes/rules",
        method = HttpMethod.POST,
        responseClass = APICreatePolicyRouteRuleEvent.class,
        parameterName = "params"
)
public class APICreatePolicyRouteRuleMsg extends APICreateMessage implements APIAuditor, PolicyRouteRuleSetMessage {

    @APINoSee
    private String vRouterUuid;

    @APIParam(resourceType = PolicyRouteRuleSetVO.class, nonempty = true)
    private String ruleSetUuid;

    @APIParam(resourceType = PolicyRouteTableVO.class, nonempty = true)
    private String tableUuid;

    @APIParam(maxLength = 4, numberRange = {1001, 2999})
    private int ruleNumber;

    @APIParam(required = false)
    private String destIp;

    @APIParam(required = false)
    private String sourceIp;

    @APIParam(required = false)
    private String destPort;

    @APIParam(required = false)
    private String sourcePort;

    @APIParam(required = false)
    private String protocol;

    @APINoSee
    private String state = "enable";

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(rsp.isSuccess() ? ((APICreatePolicyRouteRuleEvent)rsp).getInventory().getUuid() : "", PolicyRouteRuleVO.class);
    }

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public String getRuleSetUuid() {
        return ruleSetUuid;
    }

    public void setRuleSetUuid(String ruleSetUuid) {
        this.ruleSetUuid = ruleSetUuid;
    }

    public String getTableUuid() {
        return tableUuid;
    }

    public void setTableUuid(String tableUuid) {
        this.tableUuid = tableUuid;
    }

    public int getRuleNumber() {
        return ruleNumber;
    }

    public void setRuleNumber(int ruleNumber) {
        this.ruleNumber = ruleNumber;
    }

    public String getDestIp() {
        return destIp;
    }

    public void setDestIp(String destIp) {
        this.destIp = destIp;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public String getDestPort() {
        return destPort;
    }

    public void setDestPort(String destPort) {
        this.destPort = destPort;
    }

    public String getSourcePort() {
        return sourcePort;
    }

    public void setSourcePort(String sourcePort) {
        this.sourcePort = sourcePort;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public static APICreatePolicyRouteRuleMsg __example__() {
        APICreatePolicyRouteRuleMsg msg = new APICreatePolicyRouteRuleMsg();
        msg.setDestIp("***********/24");
        msg.setProtocol("tcp");
        msg.setRuleNumber(1);
        msg.setRuleSetUuid(uuid());
        msg.setSourceIp("***********/24");
        msg.setDestPort("22");
        msg.setSourcePort("33");
        msg.setTableUuid(uuid());
        return msg;
    }
}
