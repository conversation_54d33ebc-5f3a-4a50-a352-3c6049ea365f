package org.zstack.policyRoute;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.network.service.virtualrouter.VirtualRouterVmVO;

/**
 * @ Author : yh.w
 * @ Date   : Created in 10:46 2019/7/19
 */
@TagResourceType(PolicyRouteRuleSetVO.class)
@Action(category = PolicyRouteConstants.ACTION_CATEGORY)
@RestRequest(
        path = "/policy-routes/rulesets",
        method = HttpMethod.POST,
        responseClass = APICreatePolicyRouteRuleSetEvent.class,
        parameterName = "params"
)
public class APICreatePolicyRouteRuleSetMsg extends APICreateMessage implements APIAuditor {

    @APIParam(maxLength = 255)
    private String name;

    @APIParam(required = false)
    private String description;

    @APIParam(resourceType = VirtualRouterVmVO.class, nonempty = true, checkAccount = true)
    private String vRouterUuid;

    @APIParam(required = false, validValues = {"User", "EgressWhereComeFrom"})
    private String type;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(rsp.isSuccess() ? ((APICreatePolicyRouteRuleSetEvent)rsp).getInventory().getUuid() : "", PolicyRouteRuleSetVO.class);
    }

    public static APICreatePolicyRouteRuleSetMsg __example__() {
        APICreatePolicyRouteRuleSetMsg msg = new APICreatePolicyRouteRuleSetMsg();
        msg.setvRouterUuid(uuid());
        msg.setName("ruleSet");
        msg.setDescription("description");
        return msg;
    }
}
