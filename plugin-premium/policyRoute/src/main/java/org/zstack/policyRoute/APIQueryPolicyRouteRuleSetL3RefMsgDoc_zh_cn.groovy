package org.zstack.policyRoute

import org.zstack.policyRoute.APIQueryPolicyRouteRuleSetL3RefReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryPolicyRouteRuleSetL3Ref"

    category "policyRoute"

    desc """查询策略路由规则集网络关联"""

    rest {
        request {
			url "GET /v1/policy-routes/rulesets/l3networdks/refs"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryPolicyRouteRuleSetL3RefMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryPolicyRouteRuleSetL3RefReply.class
        }
    }
}