package org.zstack.policyRoute;

import org.zstack.header.identity.rbac.RBACDescription;
import org.zstack.network.service.virtualrouter.VirtualRouterVmVO;

/**
 * @ Author : yh.w
 * @ Date   : Created in 19:10 2019/7/7
 */
public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("policyRoute")
                .normalAPIs("org.zstack.policyRoute.**")
                .targetResources(PolicyRouteRuleSetVO.class, PolicyRouteTableVO.class)
                .build();
    }

    @Override
    public void contributeToRoles() {

    }

    @Override
    public void roles() {
        roleBuilder()
                .name("policyRoute")
                .permissionsByName("policyRoute")
                .uuid("c3b42792e2fb4466a0b979920f5784ee")
                .build();
    }

    @Override
    public void globalReadableResources() {

    }
}
