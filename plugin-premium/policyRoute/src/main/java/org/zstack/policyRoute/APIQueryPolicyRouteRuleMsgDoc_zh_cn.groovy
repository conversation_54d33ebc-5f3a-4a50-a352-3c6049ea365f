package org.zstack.policyRoute

import org.zstack.policyRoute.APIQueryPolicyRouteRuleReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryPolicyRouteRule"

    category "policyRoute"

    desc """查询策略路由规则"""

    rest {
        request {
			url "GET /v1/policy-routes/rules"
			url "GET /v1/policy-routes/rules/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryPolicyRouteRuleMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryPolicyRouteRuleReply.class
        }
    }
}