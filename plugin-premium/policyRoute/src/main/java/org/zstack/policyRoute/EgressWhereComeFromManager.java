package org.zstack.policyRoute;

import org.zstack.header.core.Completion;

/**
 * @ Author : shixin.ruan
 * @ Date   : Created in 2020/02/24
 */
public interface EgressWhereComeFromManager {
    void handleAPICreatePolicyRouteRuleSetMsg(APICreatePolicyRouteRuleSetMsg msg);
    void rollbackAPICreatePolicyRouteRuleSetMsg(APICreatePolicyRouteRuleSetMsg msg);
    boolean isDefaultRuleSet(PolicyRouteRuleSetInventory inv);
    void removeSystemPolicyRouteRuleSet(PolicyRouteRuleSetInventory inv, Completion completion);
}
