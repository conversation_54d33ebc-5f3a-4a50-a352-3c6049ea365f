package org.zstack.policyRoute;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.vo.NoView;
import org.zstack.header.vo.ResourceVO;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

/**
 * @ Author : yh.w
 * @ Date   : Created in 14:10 2019/7/19
 */
@Entity
@Table
public class PolicyRouteTableVO extends ResourceVO implements OwnedByAccount {

    @Column
    private int tableNumber;

    @Column
    private String description;

    @Column
    @Enumerated(EnumType.STRING)
    private PolicyRouteType type;

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;

    @Transient
    private String accountUuid;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "tableUuid", insertable = false, updatable = false)
    @NoView
    private Set<PolicyRouteTableRouteEntryVO> routes = new HashSet<>();

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public Set<PolicyRouteTableRouteEntryVO> getRoutes() {
        return routes;
    }

    public void setRoutes(Set<PolicyRouteTableRouteEntryVO> routes) {
        this.routes = routes;
    }

    public int getTableNumber() {
        return tableNumber;
    }

    public void setTableNumber(int tableNumber) {
        this.tableNumber = tableNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PolicyRouteType getType() {
        return type;
    }

    public void setType(PolicyRouteType type) {
        this.type = type;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }
}
