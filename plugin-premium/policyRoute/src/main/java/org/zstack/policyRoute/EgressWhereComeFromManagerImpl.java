package org.zstack.policyRoute;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.header.Component;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NoErrorCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l3.IpRangeInventory;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.service.NetworkServiceConstants;
import org.zstack.header.network.service.VirtualRouterAfterAttachNicExtensionPoint;
import org.zstack.header.network.service.VirtualRouterBeforeDetachNicExtensionPoint;
import org.zstack.header.vm.VmNicInventory;
import org.zstack.identity.Account;
import org.zstack.network.l3.IpRangeHelper;
import org.zstack.network.service.virtualrouter.VirtualRouterNicMetaData;
import org.zstack.network.service.virtualrouter.VirtualRouterVmInventory;
import org.zstack.network.service.virtualrouter.VirtualRouterVmVO;
import org.zstack.network.service.virtualrouter.ha.VirtualRouterHaBackend;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6Constants;
import org.zstack.utils.network.IPv6NetworkUtils;
import org.zstack.utils.network.NetworkUtils;

import java.util.*;
import java.util.stream.Collectors;


public class EgressWhereComeFromManagerImpl implements EgressWhereComeFromManager, Component, VirtualRouterAfterAttachNicExtensionPoint,
        VirtualRouterBeforeDetachNicExtensionPoint {
    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    private PolicyRouteTableConfigProxy tcp;
    @Autowired
    private PolicyRouteRuleSetConfigProxy rcp;
    @Autowired
    private PolicyRouteManager prMgr;
    @Autowired
    private CloudBus bus;
    @Autowired
    private VirtualRouterHaBackend haBackend;

    private static final CLogger logger = Utils.getLogger(PolicyRouteManagerImpl.class);

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    private String formatNetworkCidr(String cidr) {
        if (NetworkUtils.isCidr(cidr, IPv6Constants.IPv4)) {
            return NetworkUtils.fmtCidr(cidr);
        } else if (NetworkUtils.isCidr(cidr, IPv6Constants.IPv6)) {
            return IPv6NetworkUtils.getFormalCidrOfNetworkCidr(cidr);
        } else {
            return cidr;
        }
    }

    private String getGetPolicyRouteRuleSetName(int idx){
        return String.format("SYS-SET-%d", idx);
    }

    private String getTableDescription(String vrUuid, String l3Uuuid){
        /* can not use  */
        String haUuid = haBackend.getVirtualRouterHaUuid(vrUuid);
        if (haUuid == null) {
            return String.format("policy-route-table-for-virtual-router-%s-l3-%s", vrUuid, l3Uuuid);
        } else {
            return String.format("policy-route-table-for-ha-group-%s-l3-%s", haUuid, l3Uuuid);
        }
    }

    @Override
    public boolean isDefaultRuleSet(PolicyRouteRuleSetInventory inv) {
        return inv.getType().equals(PolicyRouteType.EgressWhereComeFrom.toString()) && inv.getRules().size() == 0;
    }

    @Transactional
    protected void deleteSystemPolicyRouteRuleSet(String vrUuid) {
        List<String> ruleSetUuids = rcp.getServiceUuidsByRouterUuid(vrUuid, PolicyRouteRuleSetVO.class.getSimpleName());
        List<String> tableUuids = tcp.getServiceUuidsByRouterUuid(vrUuid, PolicyRouteTableVO.class.getSimpleName());

        List<String> systemRuleSetUuids = Q.New(PolicyRouteRuleSetVO.class).in(PolicyRouteRuleSetVO_.uuid, ruleSetUuids)
                .eq(PolicyRouteRuleSetVO_.type, PolicyRouteType.EgressWhereComeFrom).select(PolicyRouteRuleSetVO_.uuid).listValues();
        if (!systemRuleSetUuids.isEmpty()) {
            SQL.New(PolicyRouteRuleVO.class).in(PolicyRouteRuleVO_.ruleSetUuid, systemRuleSetUuids).delete();
            SQL.New(PolicyRouteRuleSetL3RefVO.class).in(PolicyRouteRuleSetL3RefVO_.ruleSetUuid, systemRuleSetUuids).delete();
            rcp.detachNetworkService(vrUuid, PolicyRouteRuleSetVO.class.getSimpleName(), systemRuleSetUuids);
            SQL.New(PolicyRouteRuleSetVO.class).in(PolicyRouteRuleSetVO_.uuid, systemRuleSetUuids).delete();
        }

        List<String> systemTableUuids = Q.New(PolicyRouteTableVO.class).in(PolicyRouteTableVO_.uuid, tableUuids)
                .eq(PolicyRouteTableVO_.type, PolicyRouteType.EgressWhereComeFrom).select(PolicyRouteTableVO_.uuid).listValues();
        if (!systemTableUuids.isEmpty()) {
            SQL.New(PolicyRouteTableRouteEntryVO.class).in(PolicyRouteTableRouteEntryVO_.tableUuid, systemTableUuids).delete();
            tcp.detachNetworkService(vrUuid, PolicyRouteTableVO.class.getSimpleName(), systemTableUuids);
            SQL.New(PolicyRouteTableVO.class).in(PolicyRouteTableVO_.uuid, systemTableUuids).delete();
        }
    }

    @Override
    public void removeSystemPolicyRouteRuleSet(PolicyRouteRuleSetInventory inv, Completion completion) {
        List<String> vrUuids = rcp.getVrUuidsByNetworkService(PolicyRouteRuleSetVO.class.getSimpleName(), inv.getUuid());
        DebugUtils.Assert(vrUuids.size() == 1, "System policy route can be attached single virtual router");

        deleteSystemPolicyRouteRuleSet(vrUuids.get(0));

        SyncPolicyRouteMsg msg = new SyncPolicyRouteMsg();
        msg.setvRouterUuids(new HashSet<>(vrUuids));
        bus.makeTargetServiceIdByResourceUuid(msg, PolicyRouteConstants.SERVICE_ID, vrUuids.get(0));
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    logger.warn(String.format("remove policy route tables [uuid:%s] failed because %s",
                            msg.getvRouterUuids(), reply.getError().toString()));
                    reply.setError(reply.getError());
                    completion.fail(reply.getError());
                } else {
                    logger.debug(String.format("remove policy route tables [uuid:%s] success", msg.getvRouterUuids()));
                    completion.success();
                }
            }
        });
    }

    @Override
    @Transactional
    public void handleAPICreatePolicyRouteRuleSetMsg(APICreatePolicyRouteRuleSetMsg msg) {
        VirtualRouterVmInventory vrInv = VirtualRouterVmInventory.valueOf(dbf.findByUuid(msg.getvRouterUuid(), VirtualRouterVmVO.class));

        List<VmNicInventory> nics = vrInv.getAdditionalPublicNics();
        nics.add(vrInv.getPublicNic());
        nics = nics.stream().filter(n -> !VirtualRouterNicMetaData.isManagementNicOnly(n) && !n.isIpv6OnlyNic())
                .sorted(Comparator.comparingInt(VmNicInventory::getDeviceId)).collect(Collectors.toList());
        List<PolicyRouteTableVO> policyRouteTableVOS = new ArrayList<>();
        List<PolicyRouteTableRouteEntryVO> policyRouteTableRouteEntryVOList = new ArrayList<>();
        List<PolicyRouteRuleVO> policyRouteRuleVOs = new ArrayList<>();
        List<PolicyRouteRuleSetL3RefVO> l3RefVOS = new ArrayList<>();
        List<PolicyRouteRuleSetVO> ruleSetVOS = new ArrayList<>();

        List<Integer> ids = new ArrayList<>();
        for (VmNicInventory nic : nics) {
            if (VirtualRouterNicMetaData.isManagementNicOnly(nic)
                    || nic.getL3NetworkUuid().equals(vrInv.getDefaultRouteL3NetworkUuid())
                    || nic.isIpv6OnlyNic()) {
                continue;
            }

            int tableId = prMgr.getNextRuleSetName(msg.getvRouterUuid(), PolicyRouteType.EgressWhereComeFrom, ids);
            ids.add(tableId);
            L3NetworkInventory l3Inv = L3NetworkInventory.valueOf(dbf.findByUuid(nic.getL3NetworkUuid(), L3NetworkVO.class));
            List<IpRangeInventory> iprs = IpRangeHelper.getNormalIpRanges(l3Inv, IPv6Constants.IPv4);

            PolicyRouteRuleSetVO routeRuleSetVO = new PolicyRouteRuleSetVO();
            routeRuleSetVO.setUuid(Platform.getUuid());
            routeRuleSetVO.setName(getGetPolicyRouteRuleSetName(tableId));
            routeRuleSetVO.setVyosName(String.format("%s%d", PolicyRouteConstants.RULE_SET_PREFIX, tableId));
            routeRuleSetVO.setDescription(getTableDescription(vrInv.getUuid(), nic.getL3NetworkUuid()));
            routeRuleSetVO.setAccountUuid(msg.getSession().getAccountUuid());
            routeRuleSetVO.setType(PolicyRouteType.valueOf(msg.getType()));
            ruleSetVOS.add(routeRuleSetVO);

            /* create policy route table */
            PolicyRouteTableVO tableVO = new PolicyRouteTableVO();
            tableVO.setTableNumber(tableId);
            tableVO.setDescription(getTableDescription(vrInv.getUuid(), nic.getL3NetworkUuid()));
            tableVO.setUuid(Platform.getUuid());
            tableVO.setAccountUuid(msg.getSession().getAccountUuid());
            tableVO.setType(PolicyRouteType.EgressWhereComeFrom);
            policyRouteTableVOS.add(tableVO);

            /* create policy route table entries: default route */
            PolicyRouteTableRouteEntryVO tableEntryVO = new PolicyRouteTableRouteEntryVO();
            tableEntryVO.setTableUuid(tableVO.getUuid());
            tableEntryVO.setDestinationCidr(NetworkServiceConstants.DEFAULT_ROUTE_HOST_PREFIX);
            tableEntryVO.setNextHopIp(iprs.get(0).getGateway());
            tableEntryVO.setUuid(Platform.getUuid());
            tableEntryVO.setAccountUuid(msg.getSession().getAccountUuid());
            policyRouteTableRouteEntryVOList.add(tableEntryVO);

            /* create policy route table entries: public nic direct route */
            String formatCidr = formatNetworkCidr(iprs.get(0).getNetworkCidr());
            tableEntryVO = new PolicyRouteTableRouteEntryVO();
            tableEntryVO.setTableUuid(tableVO.getUuid());
            tableEntryVO.setDestinationCidr(formatCidr);
            tableEntryVO.setNextHopIp(nic.getIp());
            tableEntryVO.setUuid(Platform.getUuid());
            tableEntryVO.setAccountUuid(msg.getSession().getAccountUuid());
            policyRouteTableRouteEntryVOList.add(tableEntryVO);

            for (VmNicInventory pnic : vrInv.getGuestNics()) {
                if (pnic.isIpv6OnlyNic()) {
                    continue;
                }
                tableEntryVO = new PolicyRouteTableRouteEntryVO();
                L3NetworkInventory pl3Inv = L3NetworkInventory.valueOf(
                        dbf.findByUuid(pnic.getL3NetworkUuid(), L3NetworkVO.class));
                List<IpRangeInventory> piprs = IpRangeHelper.getNormalIpRanges(pl3Inv, IPv6Constants.IPv4);
                formatCidr = formatNetworkCidr(piprs.get(0).getNetworkCidr());
                tableEntryVO.setTableUuid(tableVO.getUuid());
                tableEntryVO.setDestinationCidr(formatCidr);
                tableEntryVO.setNextHopIp(piprs.get(0).getGateway());
                tableEntryVO.setUuid(Platform.getUuid());
                tableEntryVO.setAccountUuid(msg.getSession().getAccountUuid());
                policyRouteTableRouteEntryVOList.add(tableEntryVO);
            }

            /* create policy route rule */
            PolicyRouteRuleVO ruleVO = new PolicyRouteRuleVO();
            formatCidr = formatNetworkCidr(iprs.get(0).getNetworkCidr());
            ruleVO.setUuid(Platform.getUuid());
            ruleVO.setRuleNumber(PolicyRouteConstants.EgressWhereComeFromRuleNumber);
            ruleVO.setRuleSetUuid(routeRuleSetVO.getUuid());
            ruleVO.setTableUuid(tableVO.getUuid());
            ruleVO.setSourceIp(formatCidr);
            ruleVO.setState(PolicyRouteRuleState.enable);
            ruleVO.setAccountUuid(msg.getSession().getAccountUuid());
            policyRouteRuleVOs.add(ruleVO);

            PolicyRouteRuleSetL3RefVO ref = new PolicyRouteRuleSetL3RefVO();
            ref.setL3NetworkUuid(nic.getL3NetworkUuid());
            ref.setRuleSetUuid(routeRuleSetVO.getUuid());
            l3RefVOS.add(ref);
        }

        dbf.persistCollection(policyRouteTableVOS);
        dbf.persistCollection(policyRouteTableRouteEntryVOList);
        dbf.persistCollection(ruleSetVOS);
        dbf.persistCollection(policyRouteRuleVOs);
        dbf.persistCollection(l3RefVOS);

        tcp.attachNetworkService(msg.getvRouterUuid(), PolicyRouteTableVO.class.getSimpleName(),
                policyRouteTableVOS.stream().map(PolicyRouteTableVO::getUuid).collect(Collectors.toList()));
        rcp.attachNetworkService(msg.getvRouterUuid(), PolicyRouteRuleSetVO.class.getSimpleName(),
                ruleSetVOS.stream().map(PolicyRouteRuleSetVO::getUuid).collect(Collectors.toList()));
    }

    @Override
    @Transactional
    public void rollbackAPICreatePolicyRouteRuleSetMsg(APICreatePolicyRouteRuleSetMsg msg) {
        List<String> PolicyRouteRuleSetUuids = rcp.getServiceUuidsByRouterUuid(msg.getvRouterUuid(),
                PolicyRouteRuleSetVO.class.getSimpleName());
        if (!PolicyRouteRuleSetUuids.isEmpty()) {
            SQL.New(PolicyRouteRuleSetL3RefVO.class).in(PolicyRouteRuleSetL3RefVO_.ruleSetUuid, PolicyRouteRuleSetUuids).delete();
            SQL.New(PolicyRouteRuleVO.class).in(PolicyRouteRuleVO_.ruleSetUuid, PolicyRouteRuleSetUuids).delete();
            rcp.detachNetworkService(msg.getvRouterUuid(), PolicyRouteRuleSetVO.class.getSimpleName(), PolicyRouteRuleSetUuids);
            SQL.New(PolicyRouteRuleSetVO.class).in(PolicyRouteRuleSetVO_.uuid, PolicyRouteRuleSetUuids).delete();
        }

        List<String> PolicyRouteTableUuids = tcp.getServiceUuidsByRouterUuid(msg.getvRouterUuid(),
                PolicyRouteTableVO.class.getSimpleName());
        if (!PolicyRouteTableUuids.isEmpty()) {
            SQL.New(PolicyRouteTableRouteEntryVO.class).in(PolicyRouteTableRouteEntryVO_.tableUuid, PolicyRouteTableUuids).delete();
            tcp.detachNetworkService(msg.getvRouterUuid(), PolicyRouteTableVO.class.getSimpleName(),
                    PolicyRouteTableUuids);
            SQL.New(PolicyRouteTableVO.class).in(PolicyRouteTableVO_.uuid, PolicyRouteTableUuids).delete();
        }
    }

    private List<PolicyRouteRuleSetVO> getPolicyRouteRuleSetVO(String vrUuid) {
        List<PolicyRouteRuleSetVO> ruleSetVOS = new ArrayList<>();
        VirtualRouterVmVO vrVO = dbf.findByUuid(vrUuid, VirtualRouterVmVO.class);
        if (vrVO == null) {
            return ruleSetVOS;
        }

        List<String> PolicyRouteRuleSetUuids = rcp.getServiceUuidsByRouterUuid(vrUuid, PolicyRouteRuleSetVO.class.getSimpleName());
        if (PolicyRouteRuleSetUuids.isEmpty()) {
            return ruleSetVOS;
        }

        List<PolicyRouteRuleSetVO> routeRuleSetVOs = dbf.listByPrimaryKeys(PolicyRouteRuleSetUuids, PolicyRouteRuleSetVO.class);
        for (PolicyRouteRuleSetVO vo : routeRuleSetVOs) {
            if (vo.getType() == PolicyRouteType.EgressWhereComeFrom){
                ruleSetVOS.add(vo);
                break;
            }
        }

        return ruleSetVOS;
    }

    /* return true -- will send change to backend */
    @Transactional
    protected boolean attachSystemPolicyRoute(VmNicInventory nic) {
        VirtualRouterVmVO vrVO = dbf.findByUuid(nic.getVmInstanceUuid(), VirtualRouterVmVO.class);
        L3NetworkInventory l3Inv = L3NetworkInventory.valueOf(dbf.findByUuid(nic.getL3NetworkUuid(), L3NetworkVO.class));
        List<String> tableUuids = tcp.getServiceUuidsByRouterUuid(nic.getVmInstanceUuid(),
                PolicyRouteTableVO.class.getSimpleName());

        List<IpRangeInventory> iprs = IpRangeHelper.getNormalIpRanges(l3Inv, IPv6Constants.IPv4);
        if (nic.isIpv6OnlyNic()) {
            return false;
        }
        String accountUuid = Account.getAccountUuidOfResource(vrVO.getUuid());
        VirtualRouterVmInventory vrInv = VirtualRouterVmInventory.valueOf(vrVO);
        if (VirtualRouterNicMetaData.isGuestNic(nic)) {
            if (tableUuids.isEmpty()) {
                return false;
            }

            /* modify policy route table entry */
            List<PolicyRouteTableRouteEntryVO> routeEntryVos = new ArrayList<>();
            for (String tableUuid : tableUuids) {
                /* for ha router, create once */
                String formatCidr = formatNetworkCidr(iprs.get(0).getNetworkCidr());
                if (Q.New(PolicyRouteTableRouteEntryVO.class)
                        .eq(PolicyRouteTableRouteEntryVO_.tableUuid, tableUuid)
                        .eq(PolicyRouteTableRouteEntryVO_.destinationCidr, formatCidr).isExists()) {
                    continue;
                }
                PolicyRouteTableRouteEntryVO tableEntryVO = new PolicyRouteTableRouteEntryVO();
                tableEntryVO.setTableUuid(tableUuid);
                tableEntryVO.setDestinationCidr(formatCidr);
                tableEntryVO.setNextHopIp(iprs.get(0).getGateway());
                tableEntryVO.setUuid(Platform.getUuid());
                tableEntryVO.setAccountUuid(accountUuid);
                routeEntryVos.add(tableEntryVO);
            }

            dbf.persistCollection(routeEntryVos);
            return true;
        } else if (VirtualRouterNicMetaData.isAddinitionalPublicNic(nic)){
            int maxTableId = prMgr.getNextRuleSetName(nic.getVmInstanceUuid(), PolicyRouteType.EgressWhereComeFrom);

            PolicyRouteRuleSetVO routeRuleSetVO = Q.New(PolicyRouteRuleSetVO.class)
                    .eq(PolicyRouteRuleSetVO_.description, getTableDescription(vrInv.getUuid(), nic.getL3NetworkUuid())).find();
            /* for ha table create once */
            /* description is unique for system rule set and route table */
            if (routeRuleSetVO == null) {
                routeRuleSetVO = new PolicyRouteRuleSetVO();
                routeRuleSetVO.setUuid(Platform.getUuid());
                routeRuleSetVO.setName(getGetPolicyRouteRuleSetName(maxTableId));
                routeRuleSetVO.setVyosName(String.format("%s%d", PolicyRouteConstants.RULE_SET_PREFIX, maxTableId));
                routeRuleSetVO.setDescription(getTableDescription(vrInv.getUuid(), nic.getL3NetworkUuid()));
                routeRuleSetVO.setAccountUuid(accountUuid);
                routeRuleSetVO.setType(PolicyRouteType.EgressWhereComeFrom);
                dbf.persist(routeRuleSetVO);
                rcp.attachNetworkService(vrInv.getUuid(), PolicyRouteRuleSetVO.class.getSimpleName(),
                        Arrays.asList(routeRuleSetVO.getUuid()));
            }

            PolicyRouteTableVO tableVO = Q.New(PolicyRouteTableVO.class)
                    .eq(PolicyRouteTableVO_.description, getTableDescription(vrInv.getUuid(), nic.getL3NetworkUuid())).find();
            if (tableVO == null) {
                /* create policy route table */
                tableVO = new PolicyRouteTableVO();
                tableVO.setTableNumber(maxTableId);
                tableVO.setDescription(getTableDescription(vrInv.getUuid(), nic.getL3NetworkUuid()));
                tableVO.setUuid(Platform.getUuid());
                tableVO.setAccountUuid(accountUuid);
                tableVO.setType(PolicyRouteType.EgressWhereComeFrom);
                dbf.persist(tableVO);
                tcp.attachNetworkService(vrInv.getUuid(), PolicyRouteTableVO.class.getSimpleName(),
                        Arrays.asList(tableVO.getUuid()));
            }

            /* create policy route table entries */
            List<PolicyRouteTableRouteEntryVO> entryVOS = new ArrayList<>();
            if (!Q.New(PolicyRouteTableRouteEntryVO.class).eq(PolicyRouteTableRouteEntryVO_.tableUuid, tableVO.getUuid())
                    .eq(PolicyRouteTableRouteEntryVO_.destinationCidr, NetworkServiceConstants.DEFAULT_ROUTE_HOST_PREFIX).isExists()) {
                PolicyRouteTableRouteEntryVO tableEntryVO = new PolicyRouteTableRouteEntryVO();
                tableEntryVO.setTableUuid(tableVO.getUuid());
                tableEntryVO.setDestinationCidr(NetworkServiceConstants.DEFAULT_ROUTE_HOST_PREFIX);
                tableEntryVO.setNextHopIp(iprs.get(0).getGateway());
                tableEntryVO.setUuid(Platform.getUuid());
                tableEntryVO.setAccountUuid(accountUuid);
                entryVOS.add(tableEntryVO);
            }

            /* direct route for public nic */
            String formatCidr = formatNetworkCidr(iprs.get(0).getNetworkCidr());
            if (!Q.New(PolicyRouteTableRouteEntryVO.class).eq(PolicyRouteTableRouteEntryVO_.tableUuid, tableVO.getUuid())
                    .eq(PolicyRouteTableRouteEntryVO_.destinationCidr, formatCidr).isExists()) {
                PolicyRouteTableRouteEntryVO tableEntryVO = new PolicyRouteTableRouteEntryVO();
                tableEntryVO.setTableUuid(tableVO.getUuid());
                tableEntryVO.setDestinationCidr(formatCidr);
                tableEntryVO.setNextHopIp(nic.getIp());
                tableEntryVO.setUuid(Platform.getUuid());
                tableEntryVO.setAccountUuid(accountUuid);
                entryVOS.add(tableEntryVO);
            }

            boolean hasIpv4 = false;
            for (VmNicInventory pnic : vrInv.getGuestNics()) {
                if (pnic.isIpv6OnlyNic()) {
                    continue;
                }
                hasIpv4 = true;
                L3NetworkInventory pl3Inv = L3NetworkInventory.valueOf(
                        dbf.findByUuid(pnic.getL3NetworkUuid(), L3NetworkVO.class));
                List<IpRangeInventory> piprs = IpRangeHelper.getNormalIpRanges(pl3Inv, IPv6Constants.IPv4);

                formatCidr = formatNetworkCidr(piprs.get(0).getNetworkCidr());
                if (!Q.New(PolicyRouteTableRouteEntryVO.class).eq(PolicyRouteTableRouteEntryVO_.tableUuid, tableVO.getUuid())
                        .eq(PolicyRouteTableRouteEntryVO_.destinationCidr, formatCidr).isExists()) {
                    PolicyRouteTableRouteEntryVO tableEntryVO = new PolicyRouteTableRouteEntryVO();
                    tableEntryVO.setTableUuid(tableVO.getUuid());
                    tableEntryVO.setDestinationCidr(formatCidr);
                    tableEntryVO.setNextHopIp(piprs.get(0).getGateway());
                    tableEntryVO.setUuid(Platform.getUuid());
                    tableEntryVO.setAccountUuid(accountUuid);
                    entryVOS.add(tableEntryVO);
                }
            }
            if (!entryVOS.isEmpty()) {
                dbf.persistCollection(entryVOS);
            }

            /* create policy route rule */
            if (!Q.New(PolicyRouteRuleVO.class).eq(PolicyRouteRuleVO_.ruleSetUuid, routeRuleSetVO.getUuid()).isExists()) {
                PolicyRouteRuleVO ruleVO = new PolicyRouteRuleVO();
                formatCidr = formatNetworkCidr(iprs.get(0).getNetworkCidr());
                ruleVO.setUuid(Platform.getUuid());
                ruleVO.setRuleNumber(PolicyRouteConstants.EgressWhereComeFromRuleNumber);
                ruleVO.setRuleSetUuid(routeRuleSetVO.getUuid());
                ruleVO.setTableUuid(tableVO.getUuid());
                ruleVO.setSourceIp(formatCidr);
                ruleVO.setState(PolicyRouteRuleState.enable);
                ruleVO.setAccountUuid(accountUuid);
                dbf.persist(ruleVO);
            }

            if (!Q.New(PolicyRouteRuleSetL3RefVO.class).eq(PolicyRouteRuleSetL3RefVO_.ruleSetUuid, routeRuleSetVO.getUuid())
                    .eq(PolicyRouteRuleSetL3RefVO_.l3NetworkUuid, nic.getL3NetworkUuid()).isExists()) {
                PolicyRouteRuleSetL3RefVO l3RefVO = new PolicyRouteRuleSetL3RefVO();
                l3RefVO.setL3NetworkUuid(nic.getL3NetworkUuid());
                l3RefVO.setRuleSetUuid(routeRuleSetVO.getUuid());
                dbf.persist(l3RefVO);
            }

            /* if there is no guestNic, no need to update backend */
            return hasIpv4;
        } else {
            return false;
        }
    }

    @Override
    public void afterAttachNic(VmNicInventory nic, Completion completion) {
        List<PolicyRouteRuleSetVO> ruleSetVos = getPolicyRouteRuleSetVO(nic.getVmInstanceUuid());
        if (ruleSetVos.isEmpty()) {
            logger.debug(String.format("system policy route is not enabled for virtual router [uuid:%s]",
                    nic.getVmInstanceUuid()));
            completion.success();
            return;
        }

        if (attachSystemPolicyRoute(nic)) {
            prMgr.syncPolicyRoute(nic.getVmInstanceUuid(), false, completion);
        } else {
            completion.success();
        }
    }

    @Transactional
    protected boolean detachSystemPolicyRoute(VmNicInventory nic) {
        List<String> tableUuids = tcp.getServiceUuidsByRouterUuid(nic.getVmInstanceUuid(),
                PolicyRouteTableVO.class.getSimpleName());
        if (tableUuids.isEmpty()) {
            return false;
        }

        L3NetworkInventory l3Inv = L3NetworkInventory.valueOf(dbf.findByUuid(nic.getL3NetworkUuid(), L3NetworkVO.class));
        List<IpRangeInventory> iprs = IpRangeHelper.getNormalIpRanges(l3Inv, IPv6Constants.IPv4);
        if (VirtualRouterNicMetaData.isGuestNic(nic) && !nic.isIpv6OnlyNic()) {
            /* modify policy route table entry */
            String formatCidr = formatNetworkCidr(iprs.get(0).getNetworkCidr());
            SQL.New(PolicyRouteTableRouteEntryVO.class).in(PolicyRouteTableRouteEntryVO_.tableUuid, tableUuids)
                    .eq(PolicyRouteTableRouteEntryVO_.destinationCidr, formatCidr).delete();
            return true;
        } else if (VirtualRouterNicMetaData.isAddinitionalPublicNic(nic) && !nic.isIpv6OnlyNic()){
            VirtualRouterVmVO vrVO = dbf.findByUuid(nic.getVmInstanceUuid(), VirtualRouterVmVO.class);
            VirtualRouterVmInventory vrInv = VirtualRouterVmInventory.valueOf(vrVO);
            String description = getTableDescription(nic.getVmInstanceUuid(), nic.getL3NetworkUuid());
            PolicyRouteTableVO tableVO = Q.New(PolicyRouteTableVO.class)
                    .eq(PolicyRouteTableVO_.description, description).limit(1).find();

            PolicyRouteRuleSetVO ruleSetVO = Q.New(PolicyRouteRuleSetVO.class).eq(PolicyRouteRuleSetVO_.description, description).limit(1).find();

            /* they will both null or both both not null */
            if (ruleSetVO != null && tableVO != null) {
                SQL.New(PolicyRouteRuleVO.class).eq(PolicyRouteRuleVO_.ruleSetUuid, ruleSetVO.getUuid()).delete();
                SQL.New(PolicyRouteRuleSetL3RefVO.class).eq(PolicyRouteRuleSetL3RefVO_.ruleSetUuid, ruleSetVO.getUuid())
                        .eq(PolicyRouteRuleSetL3RefVO_.l3NetworkUuid, nic.getL3NetworkUuid()).delete();
                rcp.detachNetworkService(vrInv.getUuid(), PolicyRouteRuleSetVO.class.getSimpleName(), Arrays.asList(ruleSetVO.getUuid()));
                SQL.New(PolicyRouteRuleSetVO.class).eq(PolicyRouteRuleSetVO_.uuid, ruleSetVO.getUuid()).delete();

                tcp.detachNetworkService(vrInv.getUuid(), PolicyRouteTableVO.class.getSimpleName(),
                        Arrays.asList(tableVO.getUuid()));
                SQL.New(PolicyRouteTableRouteEntryVO.class).eq(PolicyRouteTableRouteEntryVO_.tableUuid, tableVO.getUuid()).delete();
                SQL.New(PolicyRouteTableVO.class).eq(PolicyRouteTableVO_.uuid, tableVO.getUuid()).delete();
            }

            return true;
        } else {
            return false;
        }
    }

    @Override
    public void afterAttachNicRollback(VmNicInventory nic, NoErrorCompletion completion) {
        List<PolicyRouteRuleSetVO> ruleSetVos = getPolicyRouteRuleSetVO(nic.getVmInstanceUuid());
        if (ruleSetVos.isEmpty()) {
            logger.debug(String.format("system policy route is not enabled for virtual router [uuid:%s]",
                    nic.getVmInstanceUuid()));
            completion.done();
            return;
        }

        if (detachSystemPolicyRoute(nic)) {
            prMgr.syncPolicyRoute(nic.getVmInstanceUuid(), false, new Completion(completion) {
                @Override
                public void success() {
                    completion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    completion.done();
                }
            });
        } else {
            completion.done();
        }
    }

    @Override
    public void beforeDetachNic(VmNicInventory nic, Completion completion) {
        List<PolicyRouteRuleSetVO> ruleSetVos = getPolicyRouteRuleSetVO(nic.getVmInstanceUuid());
        if (ruleSetVos.isEmpty()) {
            logger.debug(String.format("system policy route is not enabled for virtual router [uuid:%s]",
                    nic.getVmInstanceUuid()));
            completion.success();
            return;
        }

        if (detachSystemPolicyRoute(nic)) {
            prMgr.syncPolicyRoute(nic.getVmInstanceUuid(), false, new Completion(completion) {
                @Override
                public void success() {
                    completion.success();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    completion.fail(errorCode);
                }
            });
        } else {
            completion.success();
        }
    }

    @Override
    public void beforeDetachNicRollback(VmNicInventory nic, NoErrorCompletion completion) {
        List<PolicyRouteRuleSetVO> ruleSetVos = getPolicyRouteRuleSetVO(nic.getVmInstanceUuid());
        if (ruleSetVos.isEmpty()) {
            logger.debug(String.format("system policy route is not enabled for virtual router [uuid:%s]",
                    nic.getVmInstanceUuid()));
            completion.done();
            return;
        }

        if (attachSystemPolicyRoute(nic)) {
            prMgr.syncPolicyRoute(nic.getVmInstanceUuid(), false, new Completion(completion) {
                @Override
                public void success() {
                    completion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    completion.done();
                }
            });
        } else {
            completion.done();
        }
    }
}
