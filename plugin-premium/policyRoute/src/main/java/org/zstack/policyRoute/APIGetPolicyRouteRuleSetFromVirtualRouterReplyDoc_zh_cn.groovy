package org.zstack.policyRoute

import org.zstack.header.errorcode.ErrorCode
import org.zstack.policyRoute.PolicyRouteRuleSetInventory

doc {

	title "虚拟路由器的策略路由集合清单"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.policyRoute.APIGetPolicyRouteRuleSetFromVirtualRouterReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "3.9"
		clz ErrorCode.class
	}
	ref {
		name "inventories"
		path "org.zstack.policyRoute.APIGetPolicyRouteRuleSetFromVirtualRouterReply.inventories"
		desc "null"
		type "List"
		since "3.9"
		clz PolicyRouteRuleSetInventory.class
	}
}
