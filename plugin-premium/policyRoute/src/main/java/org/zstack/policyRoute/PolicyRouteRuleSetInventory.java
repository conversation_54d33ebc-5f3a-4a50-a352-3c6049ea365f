package org.zstack.policyRoute;

import org.zstack.header.search.Inventory;

import java.sql.Timestamp;
import java.util.*;

/**
 * @ Author : yh.w
 * @ Date   : Created in 15:05 2019/7/22
 */
@Inventory(mappingVOClass = PolicyRouteRuleSetVO.class)
public class PolicyRouteRuleSetInventory {

    private String uuid;

    private String name;

    private String description;

    private String type;

    private Timestamp createDate;

    private Timestamp lastOpDate;

    private List<PolicyRouteRuleInventory> rules = new ArrayList<>();

    private List<PolicyRouteRuleSetL3RefInventory> l3Refs = new ArrayList<>();

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public List<PolicyRouteRuleInventory> getRules() {
        return rules;
    }

    public void setRules(List<PolicyRouteRuleInventory> rules) {
        this.rules = rules;
    }

    public PolicyRouteRuleSetInventory() {
    }

    public PolicyRouteRuleSetInventory(PolicyRouteRuleSetVO vo) {
        this.uuid = vo.getUuid();
        this.name = vo.getName();
        this.description = vo.getDescription();
        this.createDate = vo.getCreateDate();
        this.lastOpDate = vo.getLastOpDate();
        this.type = vo.getType().toString();
        this.rules = PolicyRouteRuleInventory.valueOf(vo.getRules());
        this.l3Refs = PolicyRouteRuleSetL3RefInventory.valueOf(vo.getL3Refs());
    }

    public static PolicyRouteRuleSetInventory valueOf(PolicyRouteRuleSetVO vo) {
        return new PolicyRouteRuleSetInventory(vo);
    }

    public static List<PolicyRouteRuleSetInventory> valueOf(Collection<PolicyRouteRuleSetVO> vos) {
        List<PolicyRouteRuleSetInventory> invs = new ArrayList<>();
        for (PolicyRouteRuleSetVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }
}
