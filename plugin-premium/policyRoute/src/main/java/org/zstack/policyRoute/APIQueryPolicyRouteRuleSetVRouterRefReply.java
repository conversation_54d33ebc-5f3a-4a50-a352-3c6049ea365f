package org.zstack.policyRoute;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;

import java.util.List;

import static java.util.Arrays.asList;

/**
 * @ Author : yh.w
 * @ Date   : Created in 13:57 2019/7/26
 */
@RestResponse(allTo = "inventories")
public class APIQueryPolicyRouteRuleSetVRouterRefReply extends APIQueryReply {
    private List<PolicyRouteRuleSetVRouterRefInventory> inventories;

    public List<PolicyRouteRuleSetVRouterRefInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<PolicyRouteRuleSetVRouterRefInventory> inventories) {
        this.inventories = inventories;
    }

    public static APIQueryPolicyRouteRuleSetVRouterRefReply __example__() {
        APIQueryPolicyRouteRuleSetVRouterRefReply reply = new APIQueryPolicyRouteRuleSetVRouterRefReply();
        PolicyRouteRuleSetVRouterRefInventory inventory = new PolicyRouteRuleSetVRouterRefInventory();
        inventory.setId(1);
        inventory.setvRouterUuid(uuid());
        inventory.setRuleSetUuid(uuid());
        reply.setInventories(asList(inventory));
        return reply;
    }
}
