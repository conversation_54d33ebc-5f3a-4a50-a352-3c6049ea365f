package org.zstack.policyRoute

import org.zstack.policyRoute.APIQueryPolicyRouteRuleSetReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryPolicyRouteRuleSet"

    category "policyRoute"

    desc """在这里填写API描述"""

    rest {
        request {
			url "GET /v1/policy-routes/rulesets"
			url "GET /v1/policy-routes/rulesets/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryPolicyRouteRuleSetMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryPolicyRouteRuleSetReply.class
        }
    }
}