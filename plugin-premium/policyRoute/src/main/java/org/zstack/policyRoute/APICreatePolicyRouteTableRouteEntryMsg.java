package org.zstack.policyRoute;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.network.service.virtualrouter.VirtualRouterVmVO;

/**
 * @ Author : yh.w
 * @ Date   : Created in 10:45 2019/7/19
 */
@TagResourceType(PolicyRouteTableRouteEntryVO.class)
@Action(category = PolicyRouteConstants.ACTION_CATEGORY)
@RestRequest(
        path = "/policy-routes/routes",
        method = HttpMethod.POST,
        responseClass = APICreatePolicyRouteTableRouteEntryEvent.class,
        parameterName = "params"
)
public class APICreatePolicyRouteTableRouteEntryMsg extends APICreateMessage implements APIAuditor, PolicyRouteTableMessage {

    @APIParam(resourceType = PolicyRouteTableVO.class, nonempty = true, checkAccount = true)
    private String tableUuid;

    @APIParam
    private String destinationCidr;

    @APIParam
    private String nextHopIp;

    @APIParam(required = false, numberRange = {1,100})
    private Integer distance;

    @APINoSee
    private String vRouterUuid;

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(rsp.isSuccess() ? ((APICreatePolicyRouteTableRouteEntryEvent)rsp).getInventory().getUuid() : "", PolicyRouteTableRouteEntryVO.class);
    }

    public String getTableUuid() {
        return tableUuid;
    }

    public void setTableUuid(String tableUuid) {
        this.tableUuid = tableUuid;
    }

    public String getDestinationCidr() {
        return destinationCidr;
    }

    public void setDestinationCidr(String destinationCidr) {
        this.destinationCidr = destinationCidr;
    }

    public String getNextHopIp() {
        return nextHopIp;
    }

    public void setNextHopIp(String nextHopIp) {
        this.nextHopIp = nextHopIp;
    }

    public Integer getDistance() {
        return distance;
    }

    public void setDistance(Integer distance) {
        this.distance = distance;
    }

    public String getvRouterUuid() {
        return vRouterUuid;
    }

    public void setvRouterUuid(String vRouterUuid) {
        this.vRouterUuid = vRouterUuid;
    }

    public static APICreatePolicyRouteTableRouteEntryMsg __example__() {
        APICreatePolicyRouteTableRouteEntryMsg msg = new APICreatePolicyRouteTableRouteEntryMsg();
        msg.setDistance(1);
        msg.setNextHopIp("***********");
        msg.setTableUuid(uuid());
        msg.setDestinationCidr("***********/24");
        return msg;
    }
}
