package org.zstack.policyRoute;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

/**
 * @ Author : yh.w
 * @ Date   : Created in 12:11 2019/7/22
 */
@StaticMetamodel(PolicyRouteRuleSetL3RefVO.class)
public class PolicyRouteRuleSetL3RefVO_ extends ResourceVO_ {
    public static volatile SingularAttribute<PolicyRouteRuleSetVRouterRefVO, Long> id;
    public static volatile SingularAttribute<PolicyRouteRuleSetVRouterRefVO, String> ruleSetUuid;
    public static volatile SingularAttribute<PolicyRouteRuleSetVRouterRefVO, String> l3NetworkUuid;
    public static volatile SingularAttribute<PolicyRouteRuleSetVRouterRefVO, Timestamp> createDate;
    public static volatile SingularAttribute<PolicyRouteRuleSetVRouterRefVO, Timestamp> lastOpDate;
}
