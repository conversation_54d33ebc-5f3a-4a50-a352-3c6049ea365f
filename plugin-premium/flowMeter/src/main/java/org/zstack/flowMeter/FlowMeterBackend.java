package org.zstack.flowMeter;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.appliancevm.ApplianceVmStatus;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.upgrade.GrayVersion;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.flowMeter.*;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l3.IpRangeInventory;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.service.VirtualRouterHaCallbackInterface;
import org.zstack.header.network.service.VirtualRouterHaCallbackStruct;
import org.zstack.header.network.service.VirtualRouterHaGetCallbackExtensionPoint;
import org.zstack.header.network.service.VirtualRouterHaTask;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmNicInventory;
import org.zstack.network.l3.IpRangeHelper;
import org.zstack.network.service.virtualrouter.*;
import org.zstack.network.service.virtualrouter.ha.VirtualRouterHaBackend;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6Constants;

import java.util.ArrayList;
import java.util.List;

import static org.zstack.core.Platform.operr;

/**
 * @author: zhanyong.miao
 * @date: 2019-07-02
 **/
public class FlowMeterBackend implements VirtualRouterHaGetCallbackExtensionPoint {
    private final static CLogger logger = Utils.getLogger(FlowMeterBackend.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    protected VirtualRouterHaBackend haBackend;

    public static final String FLOW_METER_REFRESH = "/flowmeter/refresh";
    public static final String FLOW_METER_GET_COUNT = "/flowmeter/count";

    private String APPLY_FLOWMETER_TASK = "applyFlowMeter";

    public static class NetworkInfo {
        private String nicMac;
        private String network;

        public String getNicMac() {
            return nicMac;
        }

        public void setNicMac(String nicMac) {
            this.nicMac = nicMac;
        }

        public String getNetwork() {
            return network;
        }

        public void setNetwork(String network) {
            this.network = network;
        }
    }

    public static class FlowMeterInfo {
        private String sampleRate;
        private List<FlowCollectorInfo> collectors;
        private List<NetworkInfo>  networkInfos;
        private FlowMeterConstants.TYPE type;
        private FlowMeterConstants.VERSION version;
        private String routerId;
        private Long activeTimeout;
        private Long expireInterval;

        public static class FlowCollectorInfo {
            private String server;
            private Long port;

            public FlowCollectorInfo(FlowCollectorVO vo) {
                this.server = vo.getServer();
                this.port = vo.getPort();
            }
            public static FlowCollectorInfo valueOf(FlowCollectorVO vo) {
                return new FlowCollectorInfo(vo);
            }

            public static List<FlowCollectorInfo> valueOf(List<FlowCollectorVO> vos) {
                List<FlowCollectorInfo> invs = new ArrayList<FlowCollectorInfo>();
                for (FlowCollectorVO vo : vos) {
                    invs.add(valueOf(vo));
                }
                return invs;
            }
        }

        public FlowMeterInfo(FlowMeterVO vo) {
            this.type = vo.getType();
            this.version = vo.getVersion();
            this.sampleRate = vo.getSample().toString();
            this.collectors = FlowCollectorInfo.valueOf(new ArrayList(vo.getCollectors()));
            FlowMeterBase base = new FlowMeterBase(vo);
            this.expireInterval = base.GetFlowExpireInterval();
            this.activeTimeout = base.GetFlowActiveTimeout();
        }

        @Override
        public int hashCode() {
            return collectors.hashCode();
        }

        @Override
        public boolean equals(Object obj) {
            if (obj instanceof  FlowMeterInfo) {
                return getCollectors().equals(((FlowMeterInfo) obj).getCollectors()) ;
            } else {
                return false;
            }
        }

        public List<NetworkInfo> getNetworkInfos() {
            return networkInfos;
        }

        public void setNetworkInfos(List<NetworkInfo> networkInfos) {
            this.networkInfos = networkInfos;
        }

        public String getSampleRate() {
            return sampleRate;
        }

        public void setSampleRate(String sampleRate) {
            this.sampleRate = sampleRate;
        }

        public List<FlowCollectorInfo> getCollectors() {
            return collectors;
        }

        public void setCollectors(List<FlowCollectorInfo> collectors) {
            this.collectors = collectors;
        }

        public FlowMeterConstants.TYPE getType() {
            return type;
        }

        public void setType(FlowMeterConstants.TYPE type) {
            this.type = type;
        }

        public FlowMeterConstants.VERSION getVersion() {
            return version;
        }

        public void setVersion(FlowMeterConstants.VERSION version) {
            this.version = version;
        }

        public String getRouterId() {
            return routerId;
        }

        public void setRouterId(String routerId) {
            this.routerId = routerId;
        }

        public Long getActiveTimeout() {
            return activeTimeout;
        }

        public void setActiveTimeout(Long activeTimeout) {
            this.activeTimeout = activeTimeout;
        }

        public Long getExpireInterval() {
            return expireInterval;
        }

        public void setExpireInterval(Long expireInterval) {
            this.expireInterval = expireInterval;
        }
    }
    public static class SetFlowMeterCmd extends VirtualRouterCommands.AgentCommand {
        @GrayVersion(value = "5.0.0")
        private FlowMeterInfo flowMeterInfor;

        public FlowMeterInfo getFlowMeterInfor() {
            return flowMeterInfor;
        }

        public void setFlowMeterInfor(FlowMeterInfo flowMeterInfor) {
            this.flowMeterInfor = flowMeterInfor;
        }
    }

    public static class SetFlowMeterRsp extends VirtualRouterCommands.AgentResponse {}

    public static class GetFlowMeterCounterCmd extends VirtualRouterCommands.AgentCommand {
    }

    public static class GetFlowCounterRsp extends VirtualRouterCommands.AgentResponse {
        @GrayVersion(value = "5.0.0")
        public List<FlowCounter> counters;
    }


    private FlowMeterInfo makeFlowMeterInfo(String flowRouterUuid, String vRouterUuid) {
        List<NetworkInfo> networks = new ArrayList<>();
        List<NetworkRouterFlowMeterRefVO> refs = Q.New(NetworkRouterFlowMeterRefVO.class)
                                                  .eq(NetworkRouterFlowMeterRefVO_.vFlowRouterUuid, flowRouterUuid).list();

        FlowMeterVO vo = SQL.New("select meter from FlowMeterVO meter, NetworkRouterFlowMeterRefVO ref" +
                " where meter.uuid = ref.flowMeterUuid and " +
                "ref.vFlowRouterUuid = (:routerUuid)").param("routerUuid", flowRouterUuid).find();

        if ( vo == null) {
            return null;
        }

        final VirtualRouterVmVO vrvo = Q.New(VirtualRouterVmVO.class).eq(VirtualRouterVmVO_.uuid, vRouterUuid).find();
        final VirtualRouterVmInventory vrinv = VirtualRouterVmInventory.valueOf(vrvo);

        FlowMeterInfo info = new FlowMeterInfo(vo);
        for (NetworkRouterFlowMeterRefVO ref: refs) {
            NetworkInfo network = new NetworkInfo();

            VmNicInventory nic = vrinv.getGuestNicByL3NetworkUuid(ref.getL3NetworkUuid());
            if (nic != null) {
                network.setNicMac(nic.getMac());
            } else {
                continue;
            }

            final L3NetworkInventory l3 = L3NetworkInventory.valueOf(dbf.findByUuid(ref.getL3NetworkUuid(), L3NetworkVO.class));
            if (l3 == null) {
                continue;
            }
            List<IpRangeInventory> iprs = IpRangeHelper.getNormalIpRanges(l3, IPv6Constants.IPv4);
            if (iprs.isEmpty()) {
                continue;
            }
            network.setNetwork(iprs.get(0).getNetworkCidr());
            if (!networks.contains(network)) {
                networks.add(network);
            }
        }
        info.setNetworkInfos(networks);
        Long id = Q.New(FlowRouterVO.class).select(FlowRouterVO_.systemID).eq(FlowRouterVO_.uuid, flowRouterUuid).findValue();
        info.setRouterId(id.toString());

        return info;
    }

    void applyToAgent(String virtualRouterUuid, String flowRouterUuid, Completion completion) {
        applyToAgent(virtualRouterUuid, flowRouterUuid, true, completion);
    }

    void applyToAgent(String virtualRouterUuid, String flowRouterUuid, Boolean applyEmpty, Completion completion) {
        logger.debug(String.format("Start apply flowmeter on vRouter: %s", virtualRouterUuid));
        FlowMeterInfo info = makeFlowMeterInfo(flowRouterUuid, virtualRouterUuid);
        if (info == null && !applyEmpty) {
            completion.success();
            return;
        }

        SetFlowMeterCmd cmd = new SetFlowMeterCmd();
        cmd.setFlowMeterInfor(info);

        VirtualRouterAsyncHttpCallMsg vrMsg = new VirtualRouterAsyncHttpCallMsg();
        vrMsg.setVmInstanceUuid(virtualRouterUuid);
        vrMsg.setPath(FLOW_METER_REFRESH);
        vrMsg.setCommand(cmd);
        vrMsg.setCheckStatus(false);
        bus.makeTargetServiceIdByResourceUuid(vrMsg, VmInstanceConstant.SERVICE_ID, virtualRouterUuid);
        bus.send(vrMsg, new CloudBusCallBack(completion) {
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    VirtualRouterAsyncHttpCallReply ar = reply.castReply();
                    SetFlowMeterRsp rsp = ar.toResponse(SetFlowMeterRsp.class);
                    if (rsp.isSuccess()) {
                        completion.success();
                    } else {
                        completion.fail(operr("operation error, because:%s", rsp.getError()));
                    }
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }
    /*
   always apply the flow services to agent under any case, if aggressive is true
    */
    void apply(String virtualRouterUuid, String flowRouterUuid, boolean aggressive, Completion completion) {
        /*in ha case, the the both vpc may be not master, so virtualRouterUuid is null*/
        FlowMeterInfo info = makeFlowMeterInfo(flowRouterUuid, virtualRouterUuid);

        if (virtualRouterUuid == null || (info == null || info.getNetworkInfos() == null || info.getNetworkInfos().isEmpty()) && !aggressive) {
            completion.success();
            return;
        }

        /*reconnect router case, the status is Connecting*/
        final VirtualRouterVmVO vo = Q.New(VirtualRouterVmVO.class).eq(VirtualRouterVmVO_.uuid, virtualRouterUuid).find();
        if (!VmInstanceState.Running.equals(vo.getState()) ||
                !ApplianceVmStatus.Connected.equals( vo.getStatus())) {
            completion.success();
            return;
        }

        applyToAgent(virtualRouterUuid, flowRouterUuid, new Completion(completion) {
            @Override
            public void success() {
                submitapplyToHaRouter(virtualRouterUuid, flowRouterUuid, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void submitapplyToHaRouter(String vrUuid, String flowRouterUuid, Completion completion) {
        VirtualRouterHaTask task = new VirtualRouterHaTask();
        task.setTaskName(APPLY_FLOWMETER_TASK);
        task.setOriginRouterUuid(vrUuid);
        task.setJsonData(flowRouterUuid);
        haBackend.submitVirtualRouterHaTask(task, completion);
    }

    void getFlowMeterCounter(String virtualRouterUuid, ReturnValueCompletion<List<FlowCounter>> completion) {
        GetFlowMeterCounterCmd cmd = new GetFlowMeterCounterCmd();
        VirtualRouterVmVO vo = Q.New(VirtualRouterVmVO.class).eq(VirtualRouterVmVO_.uuid, virtualRouterUuid).find();

        if (!VmInstanceState.Running.equals(vo.getState()) ||
                !ApplianceVmStatus.Connected.equals( vo.getStatus())) {
            completion.success(new ArrayList<>());
            return;
        }

        VirtualRouterAsyncHttpCallMsg vrMsg = new VirtualRouterAsyncHttpCallMsg();
        vrMsg.setVmInstanceUuid(virtualRouterUuid);
        vrMsg.setPath(FLOW_METER_GET_COUNT);
        vrMsg.setCommand(cmd);
        vrMsg.setCheckStatus(true);
        bus.makeTargetServiceIdByResourceUuid(vrMsg, VmInstanceConstant.SERVICE_ID, virtualRouterUuid);
        bus.send(vrMsg, new CloudBusCallBack(completion) {
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    VirtualRouterAsyncHttpCallReply ar = reply.castReply();
                    GetFlowCounterRsp rsp = ar.toResponse(GetFlowCounterRsp.class);
                    if (rsp.isSuccess()) {
                        completion.success(rsp.counters);
                    } else {
                        completion.fail(operr("operation error, because:%s", rsp.getError()));
                    }
                } else {
                    completion.fail(reply.getError());
                }
            }
        });
    }

    @Override
    public List<VirtualRouterHaCallbackStruct> getCallback() {
        List<VirtualRouterHaCallbackStruct> structs = new ArrayList<>();

        VirtualRouterHaCallbackStruct applyFlowMeter = new VirtualRouterHaCallbackStruct();
        applyFlowMeter.type = APPLY_FLOWMETER_TASK;
        applyFlowMeter.callback = new VirtualRouterHaCallbackInterface() {
            @Override
            public void callBack(String vrUuid, VirtualRouterHaTask task, Completion completion) {
                String flowRouterUuid = task.getJsonData();
                applyToAgent(vrUuid, flowRouterUuid, completion);
            }
        };
        structs.add(applyFlowMeter);

        return structs;
    }
}