package org.zstack.iam2.securitygroup;

import org.zstack.core.db.Q;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO_;
import org.zstack.iam2.entity.IAM2ProjectResourceRefVO;
import org.zstack.iam2.entity.IAM2ProjectResourceRefVO_;
import org.zstack.identity.Account;
import org.zstack.network.securitygroup.SecurityGroupVO;
import org.zstack.network.securitygroup.VmNicSecurityGroupRefVO;
import org.zstack.network.securitygroup.VmNicSecurityGroupRefVO_;

/**
 * Created by LiangHanYu on 2022/3/9 23:38
 */
public class IAM2ProjectResourceUtils {
    public static boolean isDefaultSecurityGroup(String securityGroupUuid) {
        return Q.New(IAM2ProjectResourceRefVO.class)
                .eq(IAM2ProjectResourceRefVO_.resourceType, SecurityGroupVO.class.getSimpleName())
                .eq(IAM2ProjectResourceRefVO_.resourceUuid, securityGroupUuid)
                .isExists();
    }

    public static boolean isSecurityGroupBindVmNic(String securityGroupUuid) {
        return Q.New(VmNicSecurityGroupRefVO.class)
                .eq(VmNicSecurityGroupRefVO_.securityGroupUuid, securityGroupUuid)
                .isExists();
    }

    public static String getSecurityGroupProjectUuid(String securityGroupUuid) {
        return Q.New(IAM2ProjectAccountRefVO.class)
                .select(IAM2ProjectAccountRefVO_.projectUuid)
                .eq(IAM2ProjectAccountRefVO_.accountUuid, Account.getAccountUuidOfResource(securityGroupUuid))
                .findValue();
    }

    public static boolean isSecurityGroupCreatedByAdmin(String securityGroupUuid) {
        return getSecurityGroupProjectUuid(securityGroupUuid) == null;
    }
}
