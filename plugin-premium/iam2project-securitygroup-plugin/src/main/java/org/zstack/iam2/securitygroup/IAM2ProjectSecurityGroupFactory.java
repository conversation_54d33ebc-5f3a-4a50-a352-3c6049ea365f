package org.zstack.iam2.securitygroup;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.autoscaling.template.AutoScalingVmTemplateSystemTags;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SQL;
import org.zstack.core.db.UpdateQuery;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.identity.AccountVO;
import org.zstack.header.identity.QuotaVO;
import org.zstack.header.message.MessageReply;
import org.zstack.iam2.IAM2APIRequestCheckerExtensionPoint;
import org.zstack.iam2.IAM2ProjectResourceFactory;
import org.zstack.iam2.entity.IAM2ProjectResourceRefVO;
import org.zstack.iam2.entity.IAM2ProjectResourceRefVO_;
import org.zstack.network.securitygroup.*;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.network.IPv6Constants;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.zstack.core.Platform.operr;

/**
 * Created by LiangHanYu on 2022/3/4 15:12
 */
public class IAM2ProjectSecurityGroupFactory implements IAM2ProjectResourceFactory, IAM2APIRequestCheckerExtensionPoint {
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    final static String rulesKey = "rules";
    final static String nameKey = "name";

    @Override
    public String getIAM2ResourceType() {
        return SecurityGroupVO.class.getSimpleName();
    }

    @Override
    public ErrorCode beforeCreateIAM2ResourceCheck(String projectUuid) {
        QuotaVO quota = SQL.New("select t0 from QuotaVO t0 left join IAM2ProjectAccountRefVO ref on t0.identityUuid = ref.accountUuid " +
                "where t0.name = :name and t0.identityType = :identityType and ref.projectUuid = :projectUuid")
                .param("projectUuid", projectUuid)
                .param("identityType", AccountVO.class.getSimpleName())
                .param("name", SecurityGroupQuotaConstant.SG_NUM)
                .find();
        if (quota == null) {
            return operr("can't find the quota for the security group for the corresponding project %s", projectUuid);
        }

        if (quota.getValue() == 0) {
            return operr("security group quota cannot less than 1");
        }

        return null;
    }

    @Override
    public void createIAM2Resource(String projectUuid, String accountUuid, JSONObject resourceTemplate, ReturnValueCompletion<String> completion) {
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-resource-for-iam2-project-%s", projectUuid));
        chain.then(new ShareFlow() {
            SecurityGroupInventory securityGroupInventory;
            List<APIAddSecurityGroupRuleMsg.SecurityGroupRuleAO> rules = new ArrayList<>();

            @Override
            public void setup() {

                flow(new NoRollbackFlow() {
                    String __name__ = "create-security-group-resources";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        CreateSecurityGroupMsg cmsg = new CreateSecurityGroupMsg();
                        cmsg.setName(resourceTemplate.getString(nameKey));
                        cmsg.setAccountUuid(accountUuid);
                        // the security group has not yet been created, and there is no resource uuid. so use localService
                        bus.makeLocalServiceId(cmsg, SecurityGroupConstant.SERVICE_ID);
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                CreateSecurityGroupReply createSecurityGroupReply = reply.castReply();
                                securityGroupInventory = createSecurityGroupReply.getInventory();
                                trigger.next();
                            }
                        });
                    }
                });

                flow(new Flow() {
                    String __name__ = "add-security-group-rule";

                    @Override
                    public boolean skip(Map data) {
                        return resourceTemplate.isNull(rulesKey);
                    }
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        resourceTemplate.getJSONArray(rulesKey).forEach(rule -> {
                            APIAddSecurityGroupRuleMsg.SecurityGroupRuleAO ao = JSONObjectUtil.toObject(rule.toString(), APIAddSecurityGroupRuleMsg.SecurityGroupRuleAO.class);
                            if (ao.getAllowedCidr() == null) {
                                if (ao.getIpVersion() == IPv6Constants.IPv4) {
                                    ao.setAllowedCidr(SecurityGroupConstant.WORLD_OPEN_CIDR);
                                } else {
                                    ao.setAllowedCidr(SecurityGroupConstant.WORLD_OPEN_CIDR_IPV6);
                                }
                            }
                            rules.add(ao);
                        });

                        AddSecurityGroupRuleMsg amsg = new AddSecurityGroupRuleMsg();
                        amsg.setSecurityGroupUuid(securityGroupInventory.getUuid());
                        amsg.setRules(rules);
                        bus.makeTargetServiceIdByResourceUuid(amsg, SecurityGroupConstant.SERVICE_ID, amsg.getSecurityGroupUuid());
                        bus.send(amsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                trigger.next();
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        dbf.removeByPrimaryKey(securityGroupInventory.getUuid(), SecurityGroupVO.class);
                        trigger.rollback();
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success(securityGroupInventory.getUuid());
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    @Override
    public void addResourceToIAM2Project(String projectUuid, String resourceUuid) {
        IAM2ProjectResourceRefVO refVO = new IAM2ProjectResourceRefVO();
        refVO.setProjectUuid(projectUuid);
        refVO.setResourceUuid(resourceUuid);
        refVO.setResourceType(getIAM2ResourceType());
        dbf.persist(refVO);
    }

    @Override
    public void deleteResourceFromIAM2Project(String projectUuid, String resourceUuid) {
        UpdateQuery.New(IAM2ProjectResourceRefVO.class)
                .eq(IAM2ProjectResourceRefVO_.projectUuid, projectUuid)
                .eq(IAM2ProjectResourceRefVO_.resourceType, getIAM2ResourceType())
                .eq(IAM2ProjectResourceRefVO_.resourceUuid, resourceUuid).hardDelete();
    }

    @Override
    public String checkTagsIncludedSecurityGroupTag(List<String> systemtags) {
        String refTag = null;
        if (systemtags != null && !systemtags.isEmpty()) {
            refTag = systemtags.stream()
                    .filter(systemtag -> VmSystemTags.L3_NETWORK_SECURITY_GROUP_UUIDS_REF.isMatch(systemtag) ||
                            AutoScalingVmTemplateSystemTags.SECURITY_GROUP_UUID.isMatch(systemtag))
                    .findFirst().orElse(null);
        }
        return refTag;
    }
}
