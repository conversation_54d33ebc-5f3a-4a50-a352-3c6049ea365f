package org.zstack.iam2.securitygroup;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.message.APIMessage;
import org.zstack.header.vm.APIAttachL3NetworkToVmMsg;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmNicVO;
import org.zstack.iam2.IAM2GlobalConfig;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO_;
import org.zstack.iam2.entity.IAM2ProjectResourceRefVO;
import org.zstack.iam2.entity.IAM2ProjectResourceRefVO_;
import org.zstack.identity.AccountManager;
import org.zstack.network.securitygroup.*;
import org.zstack.resourceconfig.ResourceConfigFacade;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.operr;

public class IAM2ProjectSecurityGroupApiInterceptor implements ApiMessageInterceptor, GlobalApiMessageInterceptor {
    @Autowired
    private AccountManager acntMgr;
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private ResourceConfigFacade rcf;

    @Override
    public List<Class> getMessageClassToIntercept() {
        List<Class> ret = new ArrayList<Class>();
        ret.add(APIDeleteVmNicFromSecurityGroupMsg.class);
        ret.add(APIUpdateSecurityGroupMsg.class);
        ret.add(APIChangeSecurityGroupStateMsg.class);
        ret.add(APIAddSecurityGroupRuleMsg.class);
        ret.add(APIDeleteSecurityGroupRuleMsg.class);
        ret.add(APIDeleteSecurityGroupMsg.class);
        ret.add(APIDetachSecurityGroupFromL3NetworkMsg.class);
        ret.add(APIAttachL3NetworkToVmMsg.class);
        return ret;
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.END;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIDeleteVmNicFromSecurityGroupMsg) {
            validate((APIDeleteVmNicFromSecurityGroupMsg) msg);
        } else if (msg instanceof APIUpdateSecurityGroupMsg) {
            validate((APIUpdateSecurityGroupMsg) msg);
        } else if (msg instanceof APIChangeSecurityGroupStateMsg) {
            validate((APIChangeSecurityGroupStateMsg) msg);
        } else if (msg instanceof APIAddSecurityGroupRuleMsg) {
            validate((APIAddSecurityGroupRuleMsg) msg);
        } else if (msg instanceof APIDeleteSecurityGroupRuleMsg) {
            validate((APIDeleteSecurityGroupRuleMsg) msg);
        } else if (msg instanceof APIDeleteSecurityGroupMsg) {
            validate((APIDeleteSecurityGroupMsg) msg);
        } else if (msg instanceof APIDetachSecurityGroupFromL3NetworkMsg) {
            validate((APIDetachSecurityGroupFromL3NetworkMsg) msg);
        } else if (msg instanceof APIAttachL3NetworkToVmMsg) {
            validate((APIAttachL3NetworkToVmMsg) msg);
        }

        return msg;
    }

    private void validate(APIAttachL3NetworkToVmMsg msg) {
        String projectUuid = SQL.New("select acc.projectUuid from AccountResourceRefVO ref, IAM2ProjectAccountRefVO acc " +
                "where acc.accountUuid = ref.accountUuid and ref.resourceType = :resourceType " +
                "and ref.resourceUuid = :resourceUuid")
                .param("resourceType", VmInstanceVO.class.getSimpleName())
                .param("resourceUuid", msg.getVmInstanceUuid())
                .find();

        if (StringUtils.isEmpty(projectUuid)) {
            return;
        }

        if (msg.getSystemTags() == null || msg.getSystemTags().isEmpty()) {
            if (isForceSecurityGroupEnabled(projectUuid)) {
                throw new ApiMessageInterceptionException(argerr(
                    "system tag requested. need specify default security group for vm nic by system tag L3_NETWORK_SECURITY_GROUP_UUIDS_REF with format l3::{%s}::SecurityGroupUuids::{%s}, because force security group is enabled"));
            }

            return;
        }

        String l3SecurityGroupRefTag = msg.getSystemTags().stream().filter(VmSystemTags.L3_NETWORK_SECURITY_GROUP_UUIDS_REF::isMatch).findFirst().orElse(null);

        if (StringUtils.isEmpty(l3SecurityGroupRefTag)) {
            if (isForceSecurityGroupEnabled(projectUuid)) {
                throw new ApiMessageInterceptionException(argerr(
                    "system tag requested. need specify default security group for vm nic by system tag L3_NETWORK_SECURITY_GROUP_UUIDS_REF with format l3::{%s}::SecurityGroupUuids::{%s}, because force security group is enabled"));
            }

            return;
        }

        Map<String, String> tokens = VmSystemTags.L3_NETWORK_SECURITY_GROUP_UUIDS_REF.getTokensByTag(l3SecurityGroupRefTag);
        String l3Uuid = tokens.get(VmSystemTags.L3_UUID_TOKEN);
        if (!msg.getL3NetworkUuid().equals(l3Uuid)) {
            throw new ApiMessageInterceptionException(argerr(
                    "the l3Uuid[%s] in the label is inconsistent with the l3Uuid[%s] in the parameter", l3Uuid, msg.getL3NetworkUuid()));
        }

        List<String> securityGroupUuids = Arrays.asList(tokens.get(VmSystemTags.SECURITY_GROUP_UUIDS_TOKEN).split(","));
        String vmAccountUuid = acntMgr.getOwnerAccountUuidOfResource(msg.getVmInstanceUuid());
        for (String sgUuid : securityGroupUuids) {
            String sgAccountUuid = acntMgr.getOwnerAccountUuidOfResource(sgUuid);
            if (!AccountConstant.isAdminPermission(sgAccountUuid) && !sgAccountUuid.equals(vmAccountUuid)) {
                throw new ApiMessageInterceptionException(argerr("the owner[uuid:%s] of security group[uuid:%s] is another IAM2 project", sgAccountUuid, sgUuid));
            }
        }

        if (isForceSecurityGroupEnabled(projectUuid)) {
            List<String> isProjectSgs = Arrays.asList(tokens.get(VmSystemTags.SECURITY_GROUP_UUIDS_TOKEN).split(",")).stream()
                    .filter(sgUuid -> Q.New(IAM2ProjectAccountRefVO.class)
                            .eq(IAM2ProjectAccountRefVO_.projectUuid, projectUuid)
                            .eq(IAM2ProjectAccountRefVO_.accountUuid, acntMgr.getOwnerAccountUuidOfResource(sgUuid)).isExists())
                    .collect(Collectors.toList());
            if (isProjectSgs.isEmpty()) {
                throw new ApiMessageInterceptionException(argerr(
                        "since force security group is enabled, securityGroupUuid in the tag must be in the project[%s]", projectUuid));
            }
        }
    }

    private void validate(APIDetachSecurityGroupFromL3NetworkMsg msg) {
        if (IAM2ProjectResourceUtils.isSecurityGroupCreatedByAdmin(msg.getSecurityGroupUuid())) {
            return;
        }

        if (!isForceSecurityGroupEnabled(IAM2ProjectResourceUtils.getSecurityGroupProjectUuid(msg.getSecurityGroupUuid()))) {
            return;
        }

        List<VmNicVO> nicVOS = SQL.New("select nic from VmNicSecurityGroupRefVO nicRef, VmNicVO nic " +
                "where nic.uuid = nicRef.vmNicUuid and nic.l3NetworkUuid = :l3NetworkUuid " +
                "and nicRef.securityGroupUuid = :securityGroupUuid")
                .param("l3NetworkUuid", msg.getL3NetworkUuid())
                .param("securityGroupUuid", msg.getSecurityGroupUuid())
                .list();
        if (!nicVOS.isEmpty()) {
            throw new ApiMessageInterceptionException(argerr("nics on the l3Network[uuid:%s] are attached to the securityGroup. " +
                            "before you can detach the l3Network from the securityGroup, you need to detach the nics from the securityGroup.",
                    msg.getL3NetworkUuid(), msg.getSecurityGroupUuid()));
        }
    }

    private void validate(APIDeleteSecurityGroupMsg msg) {
        if (IAM2ProjectResourceUtils.isSecurityGroupCreatedByAdmin(msg.getUuid())) {
            return;
        }

        if (!isForceSecurityGroupEnabled(IAM2ProjectResourceUtils.getSecurityGroupProjectUuid(msg.getUuid()))) {
            return;
        }

        if (IAM2ProjectResourceUtils.isDefaultSecurityGroup(msg.getUuid())) {
            throw new ApiMessageInterceptionException(operr("the default security group %s cannot be deleted by enabling the enforced security group function", msg.getUuid()));
        }

        if (IAM2ProjectResourceUtils.isSecurityGroupBindVmNic(msg.getUuid())) {
            throw new ApiMessageInterceptionException(operr("this security group %s is bound to vm, please try again after unbinding", msg.getUuid()));
        }
    }

    private boolean isForceSecurityGroupEnabled(String resourceUuid) {
        return rcf.getResourceConfigValue(IAM2GlobalConfig.IAM2_FORCE_ENABLE_SECURITY_GROUP, resourceUuid, Boolean.class);
    }

    private void validateSecurityGroup(SessionInventory sessionInventory, String securityGroupUuid) {
        if (!acntMgr.isAdmin(sessionInventory) && isDefaultSecurityGroup(securityGroupUuid)) {
            throw new ApiMessageInterceptionException(argerr("account[%s] cannot operation the default securityGroup[%s]",
                    sessionInventory.getAccountUuid(), securityGroupUuid));
        }
    }

    private void validate(APIDeleteSecurityGroupRuleMsg msg) {
        if (!acntMgr.isAdmin(msg.getSession())) {
            List<String> ruleUuids = SQL.New("select rule.uuid from SecurityGroupRuleVO rule, IAM2ProjectResourceRefVO ref " +
                    "where rule.securityGroupUuid = ref.resourceUuid " +
                    "and rule.uuid in (:uuid)").param("uuid", msg.getRuleUuids()).list();

            if (!ruleUuids.isEmpty()) {
                throw new ApiMessageInterceptionException(argerr("account[%s] not allowed to operate on default securityGroup",
                        msg.getSession().getAccountUuid()));
            }
        }
    }


    private void validate(APIAddSecurityGroupRuleMsg msg) {
        validateSecurityGroup(msg.getSession(), msg.getSecurityGroupUuid());
    }


    private void validate(APIChangeSecurityGroupStateMsg msg) {
        validateSecurityGroup(msg.getSession(), msg.getUuid());
    }

    private void validate(APIUpdateSecurityGroupMsg msg) {
        validateSecurityGroup(msg.getSession(), msg.getSecurityGroupUuid());
    }

    private boolean isDefaultSecurityGroup(String securityGroupUuid) {
        return Q.New(IAM2ProjectResourceRefVO.class)
                .eq(IAM2ProjectResourceRefVO_.resourceType, SecurityGroupVO.class.getSimpleName())
                .eq(IAM2ProjectResourceRefVO_.resourceUuid, securityGroupUuid)
                .isExists();
    }

    private void validate(APIDeleteVmNicFromSecurityGroupMsg msg) {
        String projectUuid = SQL.New("select acc.projectUuid from AccountResourceRefVO ref, IAM2ProjectAccountRefVO acc " +
                "where acc.accountUuid = ref.accountUuid and ref.resourceType = :resourceType " +
                "and ref.resourceUuid = :resourceUuid")
                .param("resourceType", SecurityGroupVO.class.getSimpleName())
                .param("resourceUuid", msg.getSecurityGroupUuid())
                .find();

        if (StringUtils.isEmpty(projectUuid)) {
            return;
        }

        List<String> isProjectNics = msg.getVmNicUuids().stream()
                .filter(nicUuid -> Q.New(IAM2ProjectAccountRefVO.class)
                        .eq(IAM2ProjectAccountRefVO_.projectUuid, projectUuid)
                        .eq(IAM2ProjectAccountRefVO_.accountUuid, acntMgr.getOwnerAccountUuidOfResource(nicUuid)).isExists())
                .collect(Collectors.toList());
        if (isProjectNics.isEmpty()) {
            return;
        }

        if (isForceSecurityGroupEnabled(projectUuid)) {
            List<VmNicSecurityGroupRefVO> refVOS = SQL.New("select ref from VmNicSecurityGroupRefVO ref where ref.vmNicUuid in (:vmNicUuid) " +
                    "group by ref.vmInstanceUuid  having count(ref.vmInstanceUuid) = 1")
                    .param("vmNicUuid", msg.getVmNicUuids())
                    .list();
            if (!refVOS.isEmpty()) {
                throw new ApiMessageInterceptionException(argerr("vm's nic[uuid:%s] only has one security group, can not delete the nic from security group[uuid:%s]", refVOS.stream()
                        .map(VmNicSecurityGroupRefVO::getVmNicUuid).collect(Collectors.joining(",")), msg.getSecurityGroupUuid()));
            }
        }
    }
}
