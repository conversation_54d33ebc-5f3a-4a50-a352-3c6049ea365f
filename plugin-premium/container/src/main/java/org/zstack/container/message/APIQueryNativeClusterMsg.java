package org.zstack.container.message;

import org.springframework.http.HttpMethod;
import org.zstack.container.entity.NativeClusterInventory;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;

@AutoQuery(replyClass = APIQueryNativeClusterReply.class,
        inventoryClass = NativeClusterInventory.class)
@RestRequest(
        path = "/container/native/cluster",
        optionalPaths = {"/container/native/cluster/{uuid}"},
        responseClass = APIQueryNativeClusterReply.class,
        method = HttpMethod.GET
)
public class APIQueryNativeClusterMsg extends APIQueryMessage {
    public static APIQueryNativeClusterMsg __example__() {
        APIQueryNativeClusterMsg msg = new APIQueryNativeClusterMsg();
        return msg;
    }
}
