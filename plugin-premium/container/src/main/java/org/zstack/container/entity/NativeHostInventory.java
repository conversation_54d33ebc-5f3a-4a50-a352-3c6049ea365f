package org.zstack.container.entity;

import org.zstack.core.Platform;
import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.host.HostInventory;
import org.zstack.header.search.Inventory;
import org.zstack.header.search.Parent;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@PythonClassInventory
@Inventory(mappingVOClass = NativeHostVO.class, collectionValueOfMethod = "valueOf1",
        parent = {@Parent(inventoryClass = HostInventory.class, type = "Native")})
public class NativeHostInventory extends HostInventory {

    private String endpointUuid;

    public NativeHostInventory() {
    }

    protected NativeHostInventory(NativeHostVO vo) {
        super(vo);
        this.setEndpointUuid(vo.getEndpointUuid());
    }

    public static NativeHostInventory valueOf(NativeHostVO vo) {
        return new NativeHostInventory(vo);
    }

    public static List<NativeHostInventory> valueOf1(Collection<NativeHostVO> vos) {
        List<NativeHostInventory> invs = new ArrayList<NativeHostInventory>();
        for (NativeHostVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }

    public String getEndpointUuid() {
        return endpointUuid;
    }

    public void setEndpointUuid(String endpointUuid) {
        this.endpointUuid = endpointUuid;
    }

    public static NativeHostInventory __example__() {
        NativeHostInventory inv = new NativeHostInventory();
        inv.setUuid(Platform.getUuid());
        inv.setClusterUuid(Platform.getUuid());
        inv.setZoneUuid(Platform.getUuid());
        inv.setManagementIp("127.0.0.1");
        inv.setHypervisorType("KVM");
        inv.setCpuSockets(2);
        inv.setName("example");
        return inv;
    }
}
