package org.zstack.container;

import org.zstack.pciDevice.PciDeviceTO;
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.Map;

public enum HamiDeviceProtocolMapping {
    NVIDIA("hami.io/node-nvidia-register", new NvidiaGpuInfoParser()),
    HYGON("hami.io/node-dcu-register", new HygonGpuInfoParser()),
    ASCEND310P("hami.io/node-register-Ascend310P", new AscendGpuInfoParser()),
    ASCEND910B("hami.io/node-register-Ascend910A", new AscendGpuInfoParser()),
    ASCEND910B3("hami.io/node-register-Ascend910B", new AscendGpuInfoParser()),
    ASCEND910B4("hami.io/node-register-Ascend910B4", new AscendGpuInfoParser()),
    ASCEND910ProB("hami.io/node-register-Ascend910ProB", new AscendGpuInfoParser());

    private static final CLogger logger = Utils.getLogger(HamiDeviceProtocolMapping.class);

    private final String annotationKey;
    private final GpuInfoParser parser;

    HamiDeviceProtocolMapping(String annotationKey, GpuInfoParser parser) {
        this.annotationKey = annotationKey;
        this.parser = parser;
    }

    public String getAnnotationKey() {
        return annotationKey;
    }

    public GpuInfoParser getParser() {
        return parser;
    }

    /**
     * Create a PciDeviceTO object
     *
     * @param name gpu device name will be used by PciDeviceSpec
     * @param deviceId gpu device id
     * @param deviceName gpu device name
     * @param vendor gpu device vendor name
     * @param ramSize gpu device ram size
     * @param hostUuid host uuid
     * @param virtStatus gpu device virtualization status
     * @param addOnInfo additional information
     * @return PciDeviceTO object
     */
    public static PciDeviceTO createPciDeviceTO(String name, String deviceId, String deviceName, String vendor, String ramSize, String hostUuid, PciDeviceVirtStatus virtStatus, Map<String, String> addOnInfo) {
        PciDeviceTO pciDevice = new PciDeviceTO();
        pciDevice.setName(name);

        pciDevice.setDeviceId(deviceId);
        pciDevice.setDevice(deviceName);

        pciDevice.setVendor(vendor);
        pciDevice.setVendorId(vendor.toLowerCase().contains("nvidia") ? "10de" : "Unknown");

        pciDevice.setRamSize(ramSize);
        pciDevice.setType("GPU_Video_Controller");
        pciDevice.setVirtStatus(virtStatus.toString());
        pciDevice.setHostUuid(hostUuid);
        pciDevice.setPciDeviceAddress("0000:00:00.0");
        pciDevice.setIommuGroup(deviceId);
        if (addOnInfo != null) {
            pciDevice.setAddonInfo(addOnInfo);
        }
        return pciDevice;
    }
}