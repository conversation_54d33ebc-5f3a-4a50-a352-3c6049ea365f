package org.zstack.container.message;

import io.kubernetes.client.openapi.models.V1Service;
import org.zstack.header.message.MessageReply;

/**
 * <AUTHOR>
 * @date 2024/7/8 18:55
 */
public class CreateKubernetesServiceReply extends MessageReply {
    private V1Service service;

    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    public V1Service getService() {
        return service;
    }

    public void setService(V1Service service) {
        this.service = service;
    }
}
