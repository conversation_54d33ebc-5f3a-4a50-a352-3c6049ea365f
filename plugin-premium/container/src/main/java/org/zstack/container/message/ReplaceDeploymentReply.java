package org.zstack.container.message;

import io.kubernetes.client.openapi.models.V1Deployment;
import org.zstack.header.message.MessageReply;

public class ReplaceDeploymentReply extends MessageReply {
    private V1Deployment deployment;
    private String gpuDeviceId;
    private String podUuid;

    public void setDeployment(V1Deployment deployment) {
        this.deployment = deployment;
    }

    public V1Deployment getDeployment() {
        return deployment;
    }

    public String getGpuDeviceId() {
        return gpuDeviceId;
    }

    public void setGpuDeviceId(String gpuDeviceId) {
        this.gpuDeviceId = gpuDeviceId;
    }

    public String getPodUuid() {
        return podUuid;
    }

    public void setPodUuid(String podUuid) {
        this.podUuid = podUuid;
    }
}
