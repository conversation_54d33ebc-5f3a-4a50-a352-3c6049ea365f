package org.zstack.container.message;

import org.zstack.container.ContainerUsage;
import org.zstack.header.message.APIReply;
import org.zstack.header.rest.RestResponse;

import java.util.Collections;
import java.util.List;

@RestResponse(fieldsTo = "all")
public class APIGetContainerUsageReply extends APIReply {
    private List<ContainerUsage> usages;

    public List<ContainerUsage> getUsages() {
        return usages;
    }

    public void setUsages(List<ContainerUsage> usages) {
        this.usages = usages;
    }

    public static APIGetContainerUsageReply __example__() {
        APIGetContainerUsageReply reply = new APIGetContainerUsageReply();
        ContainerUsage usage = new ContainerUsage();
        usage.setName("container.requested.cpu.num");
        usage.setValue(10L);
        reply.setUsages(Collections.singletonList(usage));
        return reply;
    }
}
