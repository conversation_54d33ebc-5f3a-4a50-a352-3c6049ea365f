package org.zstack.container;

import org.zstack.pciDevice.PciDeviceTO;
import org.zstack.pciDevice.gpu.GpuConstant;
import org.zstack.pciDevice.gpu.GpuVendor;
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HygonGpuInfoParser implements GpuInfoParser {
    private static final CLogger logger = Utils.getLogger(HygonGpuInfoParser.class);

    @Override
    public List<PciDeviceTO> parse(String annotationValue, String hostUuid) {
        List<PciDeviceTO> pciDevices = new ArrayList<>();
        String[] gpuInfos = annotationValue.split(":");
        for (String gpuInfo : gpuInfos) {
            String[] gpuInfoArray = gpuInfo.split(",");
            if (gpuInfoArray.length != 7) {
                logger.debug(String.format("Invalid hygon dcu info: %s", gpuInfo));
                continue;
            }

            // DCU-0,4,32752,100,DCU-Z100L,0,true
            logger.debug(String.format("Got hygon dcu info: %s", gpuInfo));
            String deviceId = gpuInfoArray[4];
            String ramSize = gpuInfoArray[2];
            Map<String, String> pciDeviceAddOnInfo = new HashMap<>();
            pciDeviceAddOnInfo.put(GpuConstant.GPU_MEMORY_NAME, String.format("%sM", ramSize));
            pciDeviceAddOnInfo.put(GpuConstant.GPU_SERIALNUMBER_NAME, deviceId);
            pciDeviceAddOnInfo.put(GpuConstant.GPU_IS_DRIVER_LOADED_NAME, Boolean.TRUE.toString());
            PciDeviceTO pciDevice = HamiDeviceProtocolMapping.createPciDeviceTO(
                    deviceId,
                    deviceId,
                    "HYGON",
                    GpuVendor.HAIGUANG.getVendorName(),
                    ramSize,
                    hostUuid,
                    PciDeviceVirtStatus.VIRTUALIZED_BYPASS_ZSTACK,
                    pciDeviceAddOnInfo
            );
            String maxPartNum = gpuInfoArray[1];
            pciDevice.setMaxPartNum(Integer.parseInt(maxPartNum));
            pciDevices.add(pciDevice);
        }
        return pciDevices;
    }
}