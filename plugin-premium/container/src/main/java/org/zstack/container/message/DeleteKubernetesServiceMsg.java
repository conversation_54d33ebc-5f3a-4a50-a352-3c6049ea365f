package org.zstack.container.message;

import org.zstack.header.message.NeedReplyMessage;

/**
 * <AUTHOR>
 * @date 2024/7/9 09:56
 */
public class DeleteKubernetesServiceMsg extends NeedReplyMessage {
    private String serviceName;
    private String namespace;
    private String accountUuid;
    private Long clusterId;

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }
}
