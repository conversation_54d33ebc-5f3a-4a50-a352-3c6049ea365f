package org.zstack.container.message

import org.zstack.container.ContainerUsage
import org.zstack.header.errorcode.ErrorCode

doc {

	title "获取容器使用量的返回"

	ref {
		name "usages"
		path "org.zstack.container.message.APIGetContainerUsageReply.usages"
		desc "null"
		type "List"
		since "5.2.1"
		clz ContainerUsage.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "5.2.1"
	}
	ref {
		name "error"
		path "org.zstack.container.message.APIGetContainerUsageReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.2.1"
		clz ErrorCode.class
	}
}
