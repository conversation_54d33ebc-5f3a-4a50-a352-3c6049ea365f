package org.zstack.container;

import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1ContainerState;
import io.kubernetes.client.openapi.models.V1ContainerStatus;
import io.kubernetes.client.openapi.models.V1DeleteOptions;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1LoadBalancerIngress;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodCondition;
import io.kubernetes.client.openapi.models.V1PodList;
import io.kubernetes.client.openapi.models.V1Service;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.container.entity.ContainerManagementEndpointInventory;
import org.zstack.container.entity.ContainerManagementEndpointVO;
import org.zstack.container.message.*;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.thread.PeriodicTask;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.AbstractService;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.ErrorableValue;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmInstanceVO_;
import org.zstack.pciDevice.PciDeviceVO;
import org.zstack.pciDevice.PciDeviceVO_;
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;

public class ContainerManagerImpl extends AbstractService implements ContainerManager {
    protected static final CLogger logger = Utils.getLogger(ContainerManagerImpl.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private PluginRegistry pluginRegistry;
    @Autowired
    private ContainerUtils containerUtils;
    @Autowired
    private ResourceDestinationMaker destinationMaker;
    @Autowired
    private ThreadFacade thdf;

    Future<Void> syncContainerManagementEndpointTask = null;

    private void handle(APIAddContainerManagementEndpointMsg msg) {
        ContainerManagementEndpointVO cvm = new ContainerManagementEndpointVO();

        if (msg.getResourceUuid() != null) {
            cvm.setUuid(msg.getResourceUuid());
        } else {
            cvm.setUuid(Platform.getUuid());
        }

        cvm.setManagementIp(msg.getManagementIp());
        cvm.setVendor(msg.getVendor());
        cvm.setManagementPort(msg.getManagementPort());
        cvm.setAccessKeyId(msg.getContainerAccessKeyId());
        cvm.setAccessKeySecret(msg.getContainerAccessKeySecret());
        cvm.setDescription(msg.getDescription());
        cvm.setName(msg.getName());
        dbf.persist(cvm);

        APIAddContainerManagementEndpointEvent event = new APIAddContainerManagementEndpointEvent(msg.getId());
        event.setInventory(ContainerManagementEndpointInventory.valueOf(cvm));
        bus.publish(event);
    }

    private void handle(APIUpdateContainerManagementEndpointMsg msg) {
        ContainerManagementEndpointVO cvm = dbf.findByUuid(msg.getUuid(), ContainerManagementEndpointVO.class);

        if (msg.getManagementIp() != null) {
            cvm.setManagementIp(msg.getManagementIp());
        }

        if (msg.getVendor() != null) {
            cvm.setVendor(msg.getVendor());
        }

        if (msg.getManagementPort() != null) {
            cvm.setManagementPort(msg.getManagementPort());
        }

        cvm = dbf.updateAndRefresh(cvm);

        APIUpdateContainerManagementEndpointEvent event = new APIUpdateContainerManagementEndpointEvent(msg.getId());
        event.setInventory(ContainerManagementEndpointInventory.valueOf(cvm));
        bus.publish(event);
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof ContainerMessage) {
            passThrough(msg);
        } else if (msg instanceof APIMessage) {
            handleApiMessage(msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void passThrough(Message msg) {
        ContainerManagementEndpointVO vo = dbf.findByUuid(((ContainerMessage) msg).getContainerEndpointUuid(), ContainerManagementEndpointVO.class);
        if (vo == null) {
            throw new OperationFailureException(operr("Cannot find ContainerManagementEndpointVO[uuid:%s]", ((ContainerMessage) msg).getContainerEndpointUuid()));
        }

        ContainerEndpointBase base = new ContainerEndpointBase(vo);
        base.handleMessage(msg);
    }

    protected void handleApiMessage(Message msg) {
        if (msg instanceof APIAddContainerManagementEndpointMsg) {
            handle((APIAddContainerManagementEndpointMsg) msg);
        } else if (msg instanceof APIUpdateContainerManagementEndpointMsg) {
            handle((APIUpdateContainerManagementEndpointMsg) msg);
        } else if (msg instanceof APIGetContainerUsageMsg) {
            handle((APIGetContainerUsageMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APIGetContainerUsageMsg msg) {
        APIGetContainerUsageReply reply = new APIGetContainerUsageReply();
        List<ContainerUsage> usages = new ArrayList<>();
        CollectionUtils.safeForEach(pluginRegistry.getExtensionList(ContainerUsageExtensionPoint.class), ext -> {
            usages.addAll(ext.reportUsage());
        });
        reply.setUsages(usages);
        bus.reply(msg, reply);
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(SERVICE_ID);
    }

    @Override
    public boolean start() {
        containerUtils.init();
        submitTaskToSyncContainerManagementEndpoint();

        ContainerGlobalConfig.SYNC_CONTAINER_RESOURCE_INTERVAL_SECONDS.installUpdateExtension((oldConfig, newConfig) -> {
            submitTaskToSyncContainerManagementEndpoint();
        });

        return true;
    }

    protected synchronized void submitTaskToSyncContainerManagementEndpoint() {
        if (syncContainerManagementEndpointTask != null) {
            syncContainerManagementEndpointTask.cancel(true);
        }

        syncContainerManagementEndpointTask = thdf.submitPeriodicTask(new PeriodicTask() {
            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.SECONDS;
            }

            @Override
            public long getInterval() {
                return ContainerGlobalConfig.SYNC_CONTAINER_RESOURCE_INTERVAL_SECONDS.value(Long.class);
            }

            @Override
            public String getName() {
                return "sync-container-management-endpoint";
            }

            @Override
            public void run() {
                try {
                    syncContainerManagementEndpoint();
                } catch (Exception e) {
                    logger.warn(String.format("Failed to sync container management endpoint, because %s", e.getMessage()));
                }
            }
        });
    }

    protected synchronized void syncContainerManagementEndpoint() {
        if (!destinationMaker.isManagedByUs(ContainerConstant.SYNC_CONTAINER_RESOURCE_TASK)) {
            logger.debug("This management node is not responsible for syncing container management endpoint");
            return;
        }

        List<SyncContainerManagementEndpointMsg> msgs = new ArrayList<>();
        dbf.listAll(ContainerManagementEndpointVO.class).forEach(cvm -> {
            SyncContainerManagementEndpointMsg msg = new SyncContainerManagementEndpointMsg();
            msg.setUuid(cvm.getUuid());
            bus.makeTargetServiceIdByResourceUuid(msg, SERVICE_ID, cvm.getUuid());
            msgs.add(msg);
        });

        new While<>(msgs).each((msg, completion) -> {
            bus.send(msg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("Failed to sync container management endpoint[uuid:%s], %s", msg.getUuid(), reply.getError()));
                        completion.addError(reply.getError());
                    }

                    completion.done();
                }
            });
        }).run(new WhileDoneCompletion(null) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (errorCodeList != null && !errorCodeList.getCauses().isEmpty()) {
                    logger.warn(String.format("Failed to sync container management endpoint, %s", errorCodeList));
                }

                logger.debug("Successfully sync container management endpoint");
            }
        });
    }

    @Override
    public boolean stop() {
        return true;
    }

    protected void handleLocalMessage(Message msg) {
        if (msg instanceof CreateDeploymentMsg) {
            handle((CreateDeploymentMsg) msg);
        } else if (msg instanceof ReplaceDeploymentMsg) {
            handle((ReplaceDeploymentMsg) msg);
        } else if (msg instanceof ReadDeploymentMsg) {
            handle((ReadDeploymentMsg) msg);
        } else if (msg instanceof CreateKubernetesServiceMsg) {
            handle((CreateKubernetesServiceMsg) msg);
        } else if (msg instanceof ReplaceKubernetesServiceMsg) {
            handle((ReplaceKubernetesServiceMsg) msg);
        } else if (msg instanceof DeleteDeploymentMsg) {
            handle((DeleteDeploymentMsg) msg);
        } else if (msg instanceof DeleteKubernetesServiceMsg) {
            handle((DeleteKubernetesServiceMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private ErrorableValue<List<String>> doDeleteDeployment(AppsV1Api appsV1Api, CoreV1Api coreV1Api, String deploymentName, String namespace, String resourceName) {
        List<String> nativeHostUuids = new ArrayList<>();
        try {
            String resourceLabelKey = ContainerGlobalConfig.RESOURCE_LABEL_KEY.value(String.class);
            V1PodList lastPodList = coreV1Api.listNamespacedPod(namespace, null, null, null, null,
                    String.format("%s=%s",resourceLabelKey, resourceName), null, null, null, null, null);

            List<String> podUuids = lastPodList.getItems().stream()
                    .map(this::extractPodUuidFromPod)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!podUuids.isEmpty()) {
                logger.info(String.format("Start to delete pods in cloud db before deleting deployment: %s, pods with gpu uuids: %s", deploymentName, podUuids));
                new SQLBatch() {
                    @Override
                    protected void scripts() {
                        // only delete pod related vgpus
                        sql(PciDeviceVO.class)
                                .eq(PciDeviceVO_.virtStatus, PciDeviceVirtStatus.HAMI_VIRTUALIZED)
                                .in(PciDeviceVO_.vmInstanceUuid, podUuids)
                                .hardDelete();
                        sql(VmInstanceVO.class).in(VmInstanceVO_.uuid, podUuids).hardDelete();
                    }
                }.execute();
                logger.info(String.format("Successfully delete pods in cloud db before deleting deployment: %s", deploymentName));
            } else {
                logger.info(String.format("No pods to delete in cloud db before deleting deployment: %s", deploymentName));
            }
        } catch (Exception e) {
            logger.warn("Failed to list pods for deployment: " + deploymentName, e);
        }

        try {
            V1DeleteOptions deleteOptions = new V1DeleteOptions();
            appsV1Api.deleteNamespacedDeployment(deploymentName, namespace,
                    null, null, null, null, null, deleteOptions);
            logger.info("Deployment deleted: " + deploymentName);
        } catch (ApiException e) {
            if (e.getCode() == HttpStatus.SC_NOT_FOUND) {
                logger.info("Deployment not found, is already deleted: " + deploymentName);
            } else {
                if (e.getMessage() != null) {
                    return ErrorableValue.ofErrorCode(operr("Fail to delete deployment, because of %s", e.getMessage()));
                }

                return ErrorableValue.ofErrorCode(operr("Fail to delete deployment, because of %s", e.getResponseBody()));
            }
        }

        return ErrorableValue.of(nativeHostUuids);
    }

    private void handle(DeleteDeploymentMsg msg) {
        String resourceLabelKey = ContainerGlobalConfig.RESOURCE_LABEL_KEY.value(String.class);

        String clusterUuid = containerUtils.getClusterUuidByClusterId(msg.getClusterId(), false);
        if (msg.getClusterId() != null && clusterUuid == null) {
            logger.warn(String.format("Cannot find cluster[uuid:%s] for account[uuid:%s], reply success because on way to delete deployment",
                    msg.getClusterId(), msg.getAccountUuid()));
            DeleteDeploymentReply reply = new DeleteDeploymentReply();
            bus.reply(msg, reply);
            return;
        }

        ApiClient client = containerUtils.getK8SApiClient(msg.getAccountUuid(), clusterUuid);
        Configuration.setDefaultApiClient(client);
        AppsV1Api appsV1Api = new AppsV1Api();
        CoreV1Api coreV1Api = new CoreV1Api();
        DeleteDeploymentReply reply = new DeleteDeploymentReply();
        ErrorableValue<List<String>> result = doDeleteDeployment(appsV1Api, coreV1Api, msg.getDeploymentName(), msg.getNamespace(), msg.getResourceName());
        if (!result.isSuccess()) {
            if (result.error != null) {
                reply.setError(result.error);
            } else {
                reply.setError(operr("Fail to delete deployment, but no error message"));
            }

            bus.reply(msg, reply);
            return;
        }

        reply.setNativeHostUuids(result.result);

        // wait 30 seconds for deployment and pod to be deleted
        for (int i = 0; i < 120; i++) {
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException ignored) {
                Thread.currentThread().interrupt();
            }
            try {
                V1PodList lastPodList = coreV1Api.listNamespacedPod(msg.getNamespace(), null, null, null, null,
                        String.format("%s=%s",resourceLabelKey, msg.getResourceName()), null, null, null, null, null);
                if (lastPodList.getItems().isEmpty()) {
                    logger.info("Pods deleted: " + msg.getDeploymentName());
                } else {
                    logger.info("Pods not deleted yet: " + msg.getDeploymentName());
                    continue;
                }

                V1Deployment deployment = appsV1Api.readNamespacedDeployment(msg.getDeploymentName(), msg.getNamespace(), "false");
                if (deployment == null) {
                    logger.info("Deployment deleted: " + msg.getDeploymentName());
                    break;
                }
            } catch (ApiException e) {
                if (e.getCode() == HttpStatus.SC_NOT_FOUND) {
                    logger.info("Deployment not found, is already deleted: " + msg.getDeploymentName());
                    break;
                }
            }
        }

        bus.reply(msg, reply);
    }

    private void handle(DeleteKubernetesServiceMsg msg) {
        String clusterUuid = containerUtils.getClusterUuidByClusterId(msg.getClusterId(), false);
        if (msg.getClusterId() != null && clusterUuid == null) {
            logger.warn(String.format("Cannot find cluster[uuid:%s] for account[uuid:%s], reply success because on way to delete service",
                    msg.getClusterId(), msg.getAccountUuid()));
            DeleteKubernetesServiceReply reply = new DeleteKubernetesServiceReply();
            bus.reply(msg, reply);
            return;
        }

        ApiClient client = containerUtils.getK8SApiClient(msg.getAccountUuid(), clusterUuid);
        Configuration.setDefaultApiClient(client);
        CoreV1Api coreV1Api = new CoreV1Api();
        DeleteKubernetesServiceReply reply = new DeleteKubernetesServiceReply();
        try {
            V1DeleteOptions deleteOptions = new V1DeleteOptions();
            coreV1Api.deleteNamespacedService(msg.getServiceName(), msg.getNamespace(),
                    null, null, null, null, null, deleteOptions);
            logger.info("Service deleted: " + msg.getServiceName());
        } catch (ApiException e) {
            if (e.getCode() == HttpStatus.SC_NOT_FOUND) {
                logger.info("Service not found, is already deleted: " + msg.getServiceName());
            } else {
                if (e.getMessage() != null) {
                    reply.setError(operr("Fail to delete service, because of %s", e.getMessage()));
                } else {
                    reply.setError(operr("Fail to delete service, because of %s", e.getResponseBody()));
                }
            }
        }
        bus.reply(msg, reply);
    }

    private static class PodStatusResult {
        boolean podReady = false;
        boolean shouldExit = false;
        String error = null;
        String gpuDeviceId = null;
        String podUuid = null;
    }

    private boolean isAllContainersReady(V1Pod pod) {
        return pod.getStatus().getContainerStatuses().stream().allMatch(V1ContainerStatus::getReady);
    }

    private String checkPendingReason(V1Pod pod) {
        V1PodCondition condition = pod.getStatus().getConditions().stream()
                .filter(c -> "PodScheduled".equals(c.getType().trim()) && "False".equals(c.getStatus()))
                .findFirst().orElse(null);
        logger.debug(String.format("Pod: %s, condition 0, type: %s, status %s",
                pod.getMetadata().getName(), pod.getStatus().getConditions().get(0).getType(), pod.getStatus().getConditions().get(0).getStatus()));
        if (condition != null) {
            String reason = String.format("Reason: %s, Message: %s", condition.getReason(), condition.getMessage());
            logger.debug(String.format("Pod: %s pending reason: %s", pod.getMetadata().getName(), reason));
            // Note: Unschedulable, Preemption is not helpful for scheduling should also be considered as insufficient
            if (reason != null &&
                    (reason.contains("Insufficient")
                            || reason.contains("SchedulingFailed")
                            || reason.contains("Unschedulable"))) {
                return reason;
            }
        }
        return null;
    }

    private String getPodStatusDetails(V1Pod pod) {
        StringBuilder status = new StringBuilder("Pod ").append(pod.getMetadata().getName())
                .append(" status: ").append(pod.getStatus().getPhase());

        if (pod.getStatus().getContainerStatuses() != null) {
            for (V1ContainerStatus containerStatus : pod.getStatus().getContainerStatuses()) {
                if (!containerStatus.getReady()) {
                    status.append(", Container ").append(containerStatus.getName())
                            .append(" not ready. State: ").append(getContainerState(containerStatus.getState()));
                }
            }
        }

        return status.toString();
    }

    private String getPodListStatusDetails(V1PodList podList) {
        StringBuilder status = new StringBuilder();
        for (V1Pod pod : podList.getItems()) {
            status.append(getPodStatusDetails(pod)).append("; ");
        }
        return status.toString();
    }

    private String extractPodUuidFromPod(V1Pod pod) {
        if (pod.getMetadata() == null) {
            return null;
        }

        if (pod.getMetadata().getUid() == null) {
            return null;
        }

        return pod.getMetadata().getUid().replace("-", "");
    }

    private String extractDeviceIdFromPod(V1Pod pod) {
        if (pod.getMetadata() == null) {
            return null;
        }

        if (pod.getMetadata().getAnnotations() != null && pod.getMetadata().getAnnotations().containsKey("hami.io/vgpu-devices-allocated")) {
            String[] gpuInfos = pod.getMetadata().getAnnotations().get("hami.io/vgpu-devices-allocated").split(":");
            for (String gpuInfo : gpuInfos) {
                // GPU-68881cae-d1a9-a54a-1448-5e260b5b5206,NVIDIA,6144,0:GPU-f399d4c9-adf1-cf0e-e872-fcbe04ec1e60,NVIDIA,6144,0:
                String[] gpuInfoArray = gpuInfo.split(",");
                if (gpuInfoArray.length != 4) {
                    logger.debug(String.format("Invalid vgpu info: %s", gpuInfo));
                    continue;
                }

                String deviceId = gpuInfoArray[0];
                if (logger.isTraceEnabled()) {
                    logger.trace(String.format("Got gpu device id: %s", gpuInfoArray[0]));
                    logger.trace(String.format("Got gpu vendor: %s", gpuInfoArray[1]));
                    logger.trace(String.format("Got gpu memory: %s", gpuInfoArray[2]));
                    logger.trace(String.format("Got gpu utils: %s", gpuInfoArray[3]));
                }

                if (!deviceId.isEmpty()) {
                    logger.debug(String.format("Got gpu device id: %s", deviceId));
                    return deviceId;
                } else {
                    logger.debug(String.format("Invalid vgpu info: %s with empty device id", gpuInfo));
                }
            }
        }

        return null;
    }

    private PodStatusResult checkPodStatus(V1PodList podList) {
        PodStatusResult result = new PodStatusResult();
        for (V1Pod pod : podList.getItems()) {
            PodPhase podPhase = PodPhase.fromString(pod.getStatus().getPhase());
            logger.debug(String.format("Pod: %s is on phase: %s", pod.getMetadata().getName(), podPhase.name()));
            switch (podPhase) {
                case RUNNING:
                    if (isAllContainersReady(pod)) {
                        result.podReady = true;
                        result.shouldExit = true;
                        result.gpuDeviceId = extractDeviceIdFromPod(pod);
                        if (pod.getMetadata() != null && pod.getMetadata().getUid() != null) {
                            result.podUuid = pod.getMetadata().getUid().replace("-", "");
                        }

                        if (logger.isTraceEnabled()) {
                            logger.trace(String.format("Pod %s is ready", pod.getMetadata().getName()));
                            logger.trace(String.format("Gpu device id: %s", result.gpuDeviceId));
                            logger.trace(String.format("Should exit: %s", result.shouldExit));
                            logger.trace(String.format("Pod uuid: %s", result.podUuid));
                        }

                        return result;
                    }
                    // If not all containers are ready, continue waiting
                    break;
                case PENDING:
                    String pendingReason = checkPendingReason(pod);
                    if (pendingReason != null) {
                        result.shouldExit = true;
                        result.error = "Pod is pending: " + pendingReason;
                        return result;
                    }
                    // If it's pending due to image pulling, continue waiting
                    break;
                case FAILED:
                case SUCCEEDED: // For completeness, though unlikely in a Deployment scenario
                    result.shouldExit = true;
                    result.error = "Pod is in " + podPhase + " state: " + getPodStatusDetails(pod);
                    if (logger.isTraceEnabled()) {
                        logger.trace(String.format("Pod %s is in %s state", pod.getMetadata().getName(), podPhase.name()));
                        logger.trace(String.format("Error: %s", result.error));
                    }

                    return result;
                case UNKNOWN:
                    logger.warn(String.format("Pod %s is in UNKNOWN state", pod.getMetadata().getName()));
                    break;
            }
        }

        if (logger.isTraceEnabled()) {
            logger.trace("checkPodStatus: Pod return result");
            logger.trace(String.format("Pod uuid: %s", result.podUuid));
            logger.trace(String.format("Gpu device id: %s", result.gpuDeviceId));
            logger.trace(String.format("Should exit: %s", result.shouldExit));
            logger.trace(String.format("Pod ready: %s", result.podReady));
        }
        return result;
    }

    private String getContainerState(V1ContainerState state) {
        if (state.getWaiting() != null) {
            return "Waiting: " + state.getWaiting().getReason();
        } else if (state.getTerminated() != null) {
            return "Terminated: " + state.getTerminated().getReason();
        } else if (state.getRunning() != null) {
            return "Running";
        }
        return "Unknown";
    }

    private void handle(ReadDeploymentMsg msg) {
        String clusterUuid = containerUtils.getClusterUuidByClusterId(msg.getClusterId());
        ApiClient client = containerUtils.getK8SApiClient(msg.getAccountUuid(), clusterUuid);
        Configuration.setDefaultApiClient(client);
        AppsV1Api appsV1Api = new AppsV1Api();

        V1Deployment deployment = null;
        ErrorCode errorCode = null;
        try {
            deployment = appsV1Api.readNamespacedDeployment(
                    msg.getDeploymentName(),
                    msg.getNamespace(),
                    "false");
        } catch (ApiException e) {
            if (e.getCode() == 404) {
                logger.error("Deployment not found: " + msg.getDeploymentName());
                errorCode = operr("Deployment not found: " + msg.getDeploymentName());
            } else {
                if (e.getResponseBody() != null) {
                    logger.error("API error: " + e.getResponseBody());
                    errorCode = operr("API error: " + e.getResponseBody());
                } else {
                    logger.error("API error: " + e.getMessage());
                    errorCode = operr("API error: " + e.getMessage());
                }
            }
        }

        ReadDeploymentReply reply = new ReadDeploymentReply();

        if (errorCode != null) {
            reply.setError(errorCode);
        } else {
            if (deployment != null) {
                reply.setDeployment(deployment);
            } else {
                reply.setError(operr("Deployment not found: " + msg.getDeploymentName()));
            }
        }

        bus.reply(msg, reply);
    }

    private void handle(ReplaceDeploymentMsg msg) {
        ApiClient client = containerUtils.getK8SApiClient(msg.getAccountUuid(), msg.getClusterUuid());
        Configuration.setDefaultApiClient(client);
        AppsV1Api appsV1Api = new AppsV1Api();
        ReplaceDeploymentReply reply = new ReplaceDeploymentReply();

        String resourceLabelKey = ContainerGlobalConfig.RESOURCE_LABEL_KEY.value(String.class);
        String resourceName = msg.getDeployment().getSpec().getTemplate().getMetadata().getLabels().get(resourceLabelKey);

        try {
            V1Deployment createDeployment = appsV1Api.replaceNamespacedDeployment(
                    msg.getDeploymentName(),
                    msg.getNamespace(),
                    msg.getDeployment(),
                    "false",
                    null,
                    null,
                    null);
            reply.setDeployment(createDeployment);
        } catch (ApiException e) {
            if (e.getResponseBody() != null) {
                reply.setError(operr("Fail to replace deployment, because of %s", e.getResponseBody()));
            } else {
                reply.setError(operr("Fail to replace deployment, because of %s", e.getMessage()));
            }
            bus.reply(msg, reply);
            return;
        }

        if (!msg.getWaitPodReady()) {
            bus.reply(msg, reply);
            return;
        }

        String nameSpace = msg.getNamespace();

        waitPodUntilReady(client, resourceName, nameSpace, msg.getCheckPodStatusInterval(), msg.getTimeToWaitPodReady(), new ReturnValueCompletion<PodStatusResult>(msg) {
            @Override
            public void success(PodStatusResult result) {
                if (result.error != null) {
                    reply.setError(operr(result.error));
                }
                if (result.gpuDeviceId != null) {
                    reply.setGpuDeviceId(result.gpuDeviceId);
                }
                if (result.podUuid != null) {
                    reply.setPodUuid(result.podUuid);
                }
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void waitPodUntilReady(ApiClient client, String resourceName, String namespace, long checkInterval, long timeToWaitPodReady, ReturnValueCompletion<PodStatusResult> completion) {
        long timeoutToWait = timeToWaitPodReady - checkInterval;

        PodStatusResult result = new PodStatusResult();
        if (timeoutToWait < 0) {
            completion.success(result);
            return;
        }

        Configuration.setDefaultApiClient(client);
        CoreV1Api coreV1Api = new CoreV1Api();
        String resourceLabelKey = ContainerGlobalConfig.RESOURCE_LABEL_KEY.value(String.class);
        V1PodList lastPodList = null;
        for (long i = 0; i < timeoutToWait; i += checkInterval) {
            //sleep first, to wait k8s create pod
            try {
                TimeUnit.SECONDS.sleep(checkInterval);
            } catch (InterruptedException ignored) {
                Thread.currentThread().interrupt();
            }
            try {
                logger.debug(String.format("try to sync pod: %s status, round %s", resourceName, String.valueOf(i)));
                lastPodList = coreV1Api.listNamespacedPod(namespace, null, null, null, null,
                        String.format("%s=%s", resourceLabelKey, resourceName), null, null, null, null, null);
                logger.debug(String.format("Get %s pods to sync status", lastPodList.getItems().toString()));
                // wait 3 round to let k8s create pod
                if (lastPodList.getItems().isEmpty() && i > 3 * checkInterval) {
                    result.error = String.format("fail to get pod for deploy %s", resourceName);
                    completion.success(result);
                    return;
                }
                result = checkPodStatus(lastPodList);

                if (result.shouldExit) {
                    completion.success(result);
                    return;
                }
            } catch (ApiException e) {
                logger.error("Error while checking pod status", e);
            }

            if (result.podReady) {
                break;
            }
        }

        if (logger.isTraceEnabled()) {
            logger.trace(String.format("Pod %s is ready", resourceName));
            logger.trace(String.format("Finally, got gpu device id: %s", result.gpuDeviceId));
        }

        if (!result.podReady && lastPodList != null) {
            String lastPodStatus = getPodListStatusDetails(lastPodList);
            result.error = String.format("The pod of deploy %s is not ready in %s seconds. Last status: %s",
                    resourceName, timeoutToWait, lastPodStatus);
        }

        completion.success(result);
    }

    private void handle(CreateDeploymentMsg msg) {
        ApiClient client = containerUtils.getK8SApiClient(msg.getAccountUuid(), msg.getClusterUuid());
        Configuration.setDefaultApiClient(client);
        AppsV1Api appsV1Api = new AppsV1Api();
        CreateDeploymentReply reply = new CreateDeploymentReply();
        try {
            V1Deployment createDeployment = appsV1Api.createNamespacedDeployment(
                    msg.getNamespace(), msg.getDeployment(), "false", null, null, null);
            reply.setDeployment(createDeployment);
        } catch (ApiException e) {
            if (e.getResponseBody() != null) {
                reply.setError(operr("Fail to create deployment, because of %s", e.getResponseBody()));
            } else {
                reply.setError(operr("Fail to create deployment, because of %s", e.getMessage()));
            }

            bus.reply(msg, reply);
            return;
        }

        if (!msg.getWaitPodReady()) {
            bus.reply(msg, reply);
            return;
        }

        long checkInterval = msg.getCheckPodStatusInterval();
        long timeoutToWait = msg.getTimeToWaitPodReady() - checkInterval;

        if (timeoutToWait < 0) {
            bus.reply(msg, reply);
            return;
        }

        String resourceLabelKey = ContainerGlobalConfig.RESOURCE_LABEL_KEY.value(String.class);
        String resourceName = msg.getDeployment().getSpec().getTemplate().getMetadata().getLabels().get(resourceLabelKey);

        String nameSpace = msg.getNamespace();

        waitPodUntilReady(client, resourceName, nameSpace, checkInterval, msg.getTimeToWaitPodReady(), new ReturnValueCompletion<PodStatusResult>(msg) {
            @Override
            public void success(PodStatusResult result) {
                if (result.error != null) {
                    reply.setError(operr(result.error));
                }
                if (result.gpuDeviceId != null) {
                    reply.setGpuDeviceId(result.gpuDeviceId);
                }
                if (result.podUuid != null) {
                    reply.setPodUuid(result.podUuid);
                }
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(ReplaceKubernetesServiceMsg msg) {
        String clusterUuid = containerUtils.getClusterUuidByClusterId(msg.getClusterId());
        ApiClient client = containerUtils.getK8SApiClient(msg.getAccountUuid(), clusterUuid);
        Configuration.setDefaultApiClient(client);
        CoreV1Api coreV1Api = new CoreV1Api();
        ReplaceKubernetesServiceReply reply = new ReplaceKubernetesServiceReply();
        String serviceName = msg.getService().getMetadata().getName();

        try {
            V1Service createdService = coreV1Api.replaceNamespacedService(serviceName,
                    msg.getNamespace(),
                    msg.getService(),
                    msg.getPretty(),
                    msg.getDryRun() != null ? msg.getDryRun().toString() : null,
                    msg.getFieldManager() != null ? msg.getFieldManager().toString() : null,
                    msg.getFieldValidation() != null ? msg.getFieldValidation().toString() : null);
            reply.setService(createdService);
        } catch (ApiException e) {
            if (e.getResponseBody() != null) {
                reply.setError(operr("Fail to create service, because of %s", e.getResponseBody()));
            } else {
                reply.setError(operr("Fail to create service, because of %s", e.getMessage()));
            }

            bus.reply(msg, reply);
            return;
        }

        K8sServiceBundle bundle = waitUntilServiceReady(coreV1Api, serviceName, msg.getNamespace(), msg.getPort());
        reply.setService(bundle.service);
        reply.setUrl(bundle.url);
        if (bundle.errorCode != null) {
            reply.setError(bundle.errorCode);
        }

        bus.reply(msg, reply);
    }

    private static class K8sServiceBundle {
        V1Service service;
        String url;
        ErrorCode errorCode;
    }

    private K8sServiceBundle waitUntilServiceReady(CoreV1Api coreV1Api, String serviceName, String namespace, String port) {
        K8sServiceBundle bundle = new K8sServiceBundle();
        for (int i = 0; i < 10; i++) {
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException ignored) {
                Thread.currentThread().interrupt();
            }
            logger.info(String.format("Try to sync service[%s] status, round: %s", serviceName, i));
            try {
                V1Service serviceWithIp = coreV1Api.readNamespacedService(serviceName, namespace, "false");
                if (serviceWithIp.getStatus() != null && serviceWithIp.getStatus().getLoadBalancer() != null
                        && serviceWithIp.getStatus().getLoadBalancer().getIngress() != null
                        && !serviceWithIp.getStatus().getLoadBalancer().getIngress().isEmpty()) {
                    String url = serviceWithIp.getStatus().getLoadBalancer().getIngress().get(0).getIp() + ":" + port;
                    logger.info(String.format("Successfully sync service status with url %s", url));
                    bundle.url = url;
                    bundle.service = serviceWithIp;
                    break;
                }
            } catch (ApiException e) {
                if ( i == 5) {
                    if (e.getResponseBody() != null) {
                        bundle.errorCode = operr("Fail to query service: %s, because of %s", serviceName, e.getResponseBody());
                    } else {
                        bundle.errorCode = operr("Fail to query service: %s, because of %s", serviceName, e.getMessage());
                    }
                    break;
                }
            }
        }

        return bundle;
    }

    private void handle(CreateKubernetesServiceMsg msg) {
        String clusterUuid = containerUtils.getClusterUuidByClusterId(msg.getClusterId());
        ApiClient client = containerUtils.getK8SApiClient(msg.getAccountUuid(), clusterUuid);
        Configuration.setDefaultApiClient(client);
        CoreV1Api coreV1Api = new CoreV1Api();
        CreateKubernetesServiceReply reply = new CreateKubernetesServiceReply();
        String nameSpace = msg.getNamespace();
        String serviceName = msg.getService().getMetadata().getName();

        try {
            V1Service createdService = coreV1Api.createNamespacedService(msg.getNamespace(), msg.getService(),
                    "false", null, null, null);
            reply.setService(createdService);
        } catch (ApiException e) {
            if (e.getResponseBody() != null) {
                reply.setError(operr("Fail to create service, because of %s", e.getResponseBody()));
            } else {
                reply.setError(operr("Fail to create service, because of %s", e.getMessage()));
            }

            bus.reply(msg, reply);
            return;
        }

        K8sServiceBundle bundle = waitUntilServiceReady(coreV1Api, serviceName, nameSpace, msg.getPort());
        reply.setService(bundle.service);
        reply.setUrl(bundle.url);
        if (bundle.errorCode != null) {
            reply.setError(bundle.errorCode);
        }

        bus.reply(msg, reply);
    }
}
