package org.zstack.container.message

import org.zstack.container.message.APISyncContainerManagementEndpointEvent

doc {
    title "SyncContainerManagementEndpoint"

    category "container"

    desc """同步容器管理端点"""

    rest {
        request {
			url "PUT /v1/container/management/endpoint/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APISyncContainerManagementEndpointMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn "syncContainerManagementEndpoint"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "5.3.6"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.3.6"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.3.6"
				}
				column {
					name "zoneUuid"
					enclosedIn "syncContainerManagementEndpoint"
					desc "区域UUID"
					location "body"
					type "String"
					optional false
					since "5.3.0"
				}
			}
        }

        response {
            clz APISyncContainerManagementEndpointEvent.class
        }
    }
}