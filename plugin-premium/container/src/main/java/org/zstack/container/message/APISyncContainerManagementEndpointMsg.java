package org.zstack.container.message;

import org.springframework.http.HttpMethod;
import org.zstack.container.entity.ContainerManagementEndpointVO;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.zone.ZoneVO;

@RestRequest(
        path = "/container/management/endpoint/{uuid}/actions",
        method = HttpMethod.PUT,
        responseClass = APISyncContainerManagementEndpointEvent.class,
        isAction = true
)
public class APISyncContainerManagementEndpointMsg extends APIMessage implements ContainerMessage {
    @APIParam(resourceType = ContainerManagementEndpointVO.class)
    private String uuid;
    @APIParam(resourceType = ZoneVO.class)
    private String zoneUuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    @Override
    public String getContainerEndpointUuid() {
        return getUuid();
    }

    public static APISyncContainerManagementEndpointMsg __example__() {
        APISyncContainerManagementEndpointMsg msg = new APISyncContainerManagementEndpointMsg();
        msg.setUuid(uuid());
        msg.setZoneUuid(uuid());
        return msg;
    }
}

