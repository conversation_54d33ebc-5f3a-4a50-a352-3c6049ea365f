package org.zstack.container.entity;

import org.zstack.pciDevice.PciDeviceTO;

import java.util.List;
import java.util.Map;

public class KubernetesPodInventory {
    private String uuid;
    private String name;
    private String namespace;
    private String description;
    private String zoneUuid;
    private String clusterUuid;
    private String hostUuid;
    private String imageUuid;
    private String systemDiskUuid;
    private String networkUuid;
    private String type;           // UserContainer
    private String hypervisorType; // container/zaku
    private Integer cpuNum;
    private Long memorySize;
    private String architecture;   // amd64
    private String platform;
    private String state;

    private List<PciDeviceTO> pciDeviceTOs;

    private Map<String, String> labels;

    // 默认构造函数
    public KubernetesPodInventory() {}

    // Getters and Setters
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    public String getClusterUuid() {
        return clusterUuid;
    }

    public void setClusterUuid(String clusterUuid) {
        this.clusterUuid = clusterUuid;
    }

    public String getHostUuid() {
        return hostUuid;
    }

    public void setHostUuid(String hostUuid) {
        this.hostUuid = hostUuid;
    }

    public String getImageUuid() {
        return imageUuid;
    }

    public void setImageUuid(String imageUuid) {
        this.imageUuid = imageUuid;
    }

    public String getSystemDiskUuid() {
        return systemDiskUuid;
    }

    public void setSystemDiskUuid(String systemDiskUuid) {
        this.systemDiskUuid = systemDiskUuid;
    }

    public String getNetworkUuid() {
        return networkUuid;
    }

    public void setNetworkUuid(String networkUuid) {
        this.networkUuid = networkUuid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getHypervisorType() {
        return hypervisorType;
    }

    public void setHypervisorType(String hypervisorType) {
        this.hypervisorType = hypervisorType;
    }

    public Integer getCpuNum() {
        return cpuNum;
    }

    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }

    public Long getMemorySize() {
        return memorySize;
    }

    public void setMemorySize(Long memorySize) {
        this.memorySize = memorySize;
    }

    public String getArchitecture() {
        return architecture;
    }

    public void setArchitecture(String architecture) {
        this.architecture = architecture;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public List<PciDeviceTO> getPciDeviceTOs() {
        return pciDeviceTOs;
    }

    public void setPciDeviceTOs(List<PciDeviceTO> pciDeviceTOs) {
        this.pciDeviceTOs = pciDeviceTOs;
    }

    public Map<String, String> getLabels() {
        return labels;
    }

    public void setLabels(Map<String, String> labels) {
        this.labels = labels;
    }
}
