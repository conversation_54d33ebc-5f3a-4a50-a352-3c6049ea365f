package org.zstack.container.entity;

public class KubernetesClusterInventory {
    /**
     * 业务网vip_url
     */
    private String bizUrl;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建类型
     */
    private String createType;

    /**
     * 集群ID
     */
    private long id;

    /**
     * Kubeconfig：使用业务网VIP展示时候，将masterUrl替换为bizUrl
     */
    private String kubeConfig;

    /**
     * 管理网vip_url
     */
    private String masterUrl;

    /**
     * prometheus地址，3.2.0版本及以上支持
     */
    private String prometheusURL;

    /**
     * 名称
     */
    private String name;

    /**
     * 集群节点数
     */
    private int nodeCount;

    /**
     * 证书有效期结束时间
     */
    private String notAfter;

    /**
     * 证书有效期开始时间
     */
    private String notBefore;

    /**
     * 当前集群状态
     */
    private String status;

    /**
     * 集群版本
     */
    private String version;

    public String getBizUrl() {
        return bizUrl;
    }

    public void setBizUrl(String bizUrl) {
        this.bizUrl = bizUrl;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateType() {
        return createType;
    }

    public void setCreateType(String createType) {
        this.createType = createType;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getKubeConfig() {
        return kubeConfig;
    }

    public void setKubeConfig(String kubeConfig) {
        this.kubeConfig = kubeConfig;
    }

    public String getMasterUrl() {
        return masterUrl;
    }

    public void setMasterUrl(String masterUrl) {
        this.masterUrl = masterUrl;
    }

    public String getPrometheusURL() {
        return prometheusURL;
    }

    public void setPrometheusURL(String prometheusURL) {
        this.prometheusURL = prometheusURL;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getNodeCount() {
        return nodeCount;
    }

    public void setNodeCount(int nodeCount) {
        this.nodeCount = nodeCount;
    }

    public String getNotAfter() {
        return notAfter;
    }

    public void setNotAfter(String notAfter) {
        this.notAfter = notAfter;
    }

    public String getNotBefore() {
        return notBefore;
    }

    public void setNotBefore(String notBefore) {
        this.notBefore = notBefore;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
