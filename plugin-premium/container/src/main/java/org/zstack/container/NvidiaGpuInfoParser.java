package org.zstack.container;

import org.zstack.pciDevice.PciDeviceTO;
import org.zstack.pciDevice.gpu.GpuConstant;
import org.zstack.pciDevice.gpu.GpuVendor;
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NvidiaGpuInfoParser implements GpuInfoParser {
    private static final CLogger logger = Utils.getLogger(NvidiaGpuInfoParser.class);

    @Override
    public List<PciDeviceTO> parse(String annotationValue, String hostUuid) {
        List<PciDeviceTO> pciDevices = new ArrayList<>();
        String[] gpuInfos = annotationValue.split(":");
        for (String gpuInfo : gpuInfos) {
            String[] gpuInfoArray = gpuInfo.split(",");
            if (gpuInfoArray.length != 7) {
                logger.debug(String.format("Invalid gpu info: %s", gpuInfo));
                continue;
            }

            // GPU-f399d4c9-adf1-cf0e-e872-fcbe04ec1e60,10,22528,100,NVIDIA-NVIDIA GeForce RTX 2080 Ti,0,true:
            // GPU-68881cae-d1a9-a54a-1448-5e260b5b5206,10,46080,100,NVIDIA-Quadro RTX 8000,0,true:
            logger.debug(String.format("Got gpu info: %s", gpuInfo));
            String deviceId = gpuInfoArray[0];
            String deviceName = gpuInfoArray[4];
            String ramSize = gpuInfoArray[2];
            Map<String, String> pciDeviceAddOnInfo = new HashMap<>();
            pciDeviceAddOnInfo.put(GpuConstant.GPU_MEMORY_NAME, String.format("%sM", ramSize));
            pciDeviceAddOnInfo.put(GpuConstant.GPU_SERIALNUMBER_NAME, deviceId);
            pciDeviceAddOnInfo.put(GpuConstant.GPU_IS_DRIVER_LOADED_NAME, Boolean.TRUE.toString());

            PciDeviceTO pciDevice = HamiDeviceProtocolMapping.createPciDeviceTO(
                    deviceName,
                    deviceId,
                    GpuVendor.NVIDIA.getVendorName(),
                    GpuVendor.NVIDIA.getVendorName(),
                    ramSize,
                    hostUuid,
                    PciDeviceVirtStatus.VIRTUALIZED_BYPASS_ZSTACK,
                    pciDeviceAddOnInfo
            );
            String maxPartNum = gpuInfoArray[1];
            pciDevice.setMaxPartNum(Integer.parseInt(maxPartNum));
            pciDevices.add(pciDevice);
        }
        return pciDevices;
    }
}