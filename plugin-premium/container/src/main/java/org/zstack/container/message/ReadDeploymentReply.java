package org.zstack.container.message;

import io.kubernetes.client.openapi.models.V1Deployment;
import org.zstack.header.message.MessageReply;

public class ReadDeploymentReply extends MessageReply {
    private V1Deployment deployment;

    public V1Deployment getDeployment() {
        return deployment;
    }

    public void setDeployment(V1Deployment deployment) {
        this.deployment = deployment;
    }
}
