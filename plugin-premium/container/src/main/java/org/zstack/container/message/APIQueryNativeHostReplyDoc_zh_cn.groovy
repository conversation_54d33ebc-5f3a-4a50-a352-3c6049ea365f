package org.zstack.container.message

import org.zstack.container.entity.NativeHostInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "查询原生容器主机结果"

	ref {
		name "inventories"
		path "org.zstack.container.message.APIQueryNativeHostReply.inventories"
		desc "原生容器主机列表"
		type "List"
		since "5.3.6"
		clz NativeHostInventory.class
	}
	field {
		name "success"
		desc "查询接口调用是否成功"
		type "boolean"
		since "5.3.6"
	}
	ref {
		name "error"
		path "org.zstack.container.message.APIQueryNativeHostReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.3.6"
		clz ErrorCode.class
	}
}
