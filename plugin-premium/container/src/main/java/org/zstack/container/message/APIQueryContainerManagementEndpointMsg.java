package org.zstack.container.message;

import org.springframework.http.HttpMethod;
import org.zstack.container.entity.ContainerManagementEndpointInventory;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.vm.APIQueryVmInstanceReply;

@AutoQuery(replyClass = APIQueryContainerManagementEndpointReply.class,
        inventoryClass = ContainerManagementEndpointInventory.class)
@RestRequest(
        path = "/container/management/endpoint",
        optionalPaths = {"/container/management/endpoint/{uuid}"},
        responseClass = APIQueryContainerManagementEndpointReply.class,
        method = HttpMethod.GET
)
public class APIQueryContainerManagementEndpointMsg extends APIQueryMessage {
    public static APIQueryContainerManagementEndpointMsg __example__() {
        APIQueryContainerManagementEndpointMsg msg = new APIQueryContainerManagementEndpointMsg();
        return msg;
    }
}
