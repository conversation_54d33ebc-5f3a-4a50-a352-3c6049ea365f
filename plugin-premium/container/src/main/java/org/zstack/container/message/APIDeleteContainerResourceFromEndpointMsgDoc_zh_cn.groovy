package org.zstack.container.message

import org.zstack.container.message.APIDeleteContainerResourceFromEndpointEvent

doc {
    title "DeleteContainerResourceFromEndpoint"

    category "container"

    desc """从容器端点删除容器资源"""

    rest {
        request {
			url "DELETE /v1/container/management/endpoint/{uuid}/resources/delete"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeleteContainerResourceFromEndpointMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "5.3.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
			}
        }

        response {
            clz APIDeleteContainerResourceFromEndpointEvent.class
        }
    }
}