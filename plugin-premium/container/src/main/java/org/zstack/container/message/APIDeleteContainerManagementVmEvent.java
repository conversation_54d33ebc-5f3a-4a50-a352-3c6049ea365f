package org.zstack.container.message;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse
public class APIDeleteContainerManagementVmEvent extends APIEvent {
    public APIDeleteContainerManagementVmEvent() { }

    public APIDeleteContainerManagementVmEvent(String apiId) {
        super(apiId);
    }

    public static APIDeleteContainerManagementVmEvent __example__() {
        APIDeleteContainerManagementVmEvent event = new APIDeleteContainerManagementVmEvent();
        event.setSuccess(true);
        return event;
    }
}
