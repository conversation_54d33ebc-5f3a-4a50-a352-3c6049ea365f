package org.zstack.container.message

import org.zstack.container.message.APIQueryContainerManagementEndpointReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryContainerManagementVm"

    category "container"

    desc """查询容器管控节点"""

    rest {
        request {
			url "GET /v1/container/management/vm"
			url "GET /v1/container/management/vm/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryContainerManagementEndpointMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryContainerManagementEndpointReply.class
        }
    }
}