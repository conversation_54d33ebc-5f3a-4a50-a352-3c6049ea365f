package org.zstack.container.entity;

import org.zstack.core.Platform;
import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Inventory(mappingVOClass = ContainerManagementEndpointVO.class)
@PythonClassInventory
public class ContainerManagementEndpointInventory implements Serializable {
    private String uuid;
    private String name;
    private String description;
    private String accessKeyId;
    private String managementIp;
    private Integer managementPort;
    private String vendor;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getManagementPort() {
        return managementPort;
    }

    public void setManagementPort(Integer managementPort) {
        this.managementPort = managementPort;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public String getManagementIp() {
        return managementIp;
    }

    public void setManagementIp(String managementIp) {
        this.managementIp = managementIp;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public ContainerManagementEndpointInventory() {}

    public ContainerManagementEndpointInventory(ContainerManagementEndpointVO vo) {
        this.uuid = vo.getUuid();
        this.name = vo.getName();
        this.description = vo.getDescription();
        this.managementIp = vo.getManagementIp();
        this.vendor = vo.getVendor();
        this.managementPort = vo.getManagementPort();
        this.lastOpDate = vo.getLastOpDate();
        this.createDate = vo.getCreateDate();
    }

    public static ContainerManagementEndpointInventory valueOf(ContainerManagementEndpointVO vo) {
        return new ContainerManagementEndpointInventory(vo);
    }

    public static List<ContainerManagementEndpointInventory> valueOf(Collection<ContainerManagementEndpointVO> vos) {
        List<ContainerManagementEndpointInventory> invs = new ArrayList<>();
        for (ContainerManagementEndpointVO vo : vos) {
            invs.add(ContainerManagementEndpointInventory.valueOf(vo));
        }
        return invs;
    }

    public static ContainerManagementEndpointInventory __example__() {
        ContainerManagementEndpointInventory inv = new ContainerManagementEndpointInventory();
        inv.setUuid(Platform.getUuid());
        inv.setName("containerEndpoint1");
        inv.setDescription("This is a container management endpoint");
        inv.setManagementIp("***********");
        inv.setVendor("VendorName");
        inv.setManagementPort(8080);
        inv.setAccessKeyId("accessKeyId");
        inv.setCreateDate(new Timestamp(System.currentTimeMillis()));
        inv.setLastOpDate(new Timestamp(System.currentTimeMillis()));
        return inv;
    }
}
