package org.zstack.container.entity

import java.lang.Integer
import java.sql.Timestamp

doc {

	title "容器管理节点"

	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "5.3.0"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "5.3.0"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "5.3.0"
	}
	field {
		name "accessKeyId"
		desc ""
		type "String"
		since "5.3.0"
	}
	field {
		name "managementIp"
		desc "容器管理节点的IP地址"
		type "String"
		since "5.3.0"
	}
	field {
		name "managementPort"
		desc "容器管理节点的端口"
		type "Integer"
		since "5.3.0"
	}
	field {
		name "vendor"
		desc "容器管理节点的厂商"
		type "String"
		since "5.3.0"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "5.3.0"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "5.3.0"
	}
}
