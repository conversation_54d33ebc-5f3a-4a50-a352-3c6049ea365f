package org.zstack.container.message

import org.zstack.header.errorcode.ErrorCode

doc {

	title "从容器端点删除容器资源事件"

	field {
		name "success"
		desc ""
		type "boolean"
		since "5.3.0"
	}
	ref {
		name "error"
		path "org.zstack.container.message.APIDeleteContainerResourceFromEndpointEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.3.0"
		clz ErrorCode.class
	}
}
