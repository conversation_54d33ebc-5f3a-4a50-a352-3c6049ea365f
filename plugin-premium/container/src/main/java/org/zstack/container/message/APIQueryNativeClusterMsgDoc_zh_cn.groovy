package org.zstack.container.message

import org.zstack.container.message.APIQueryNativeClusterReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryNativeCluster"

    category "container"

    desc """查询原生容器集群"""

    rest {
        request {
			url "GET /v1/container/native/cluster"
			url "GET /v1/container/native/cluster/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryNativeClusterMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryNativeClusterReply.class
        }
    }
}