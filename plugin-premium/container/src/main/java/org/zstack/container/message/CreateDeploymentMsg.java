package org.zstack.container.message;

import io.kubernetes.client.openapi.models.V1Deployment;
import org.zstack.header.message.NeedQuotaCheckMessage;
import org.zstack.header.message.NeedReplyMessage;

/**
 * <AUTHOR>
 * @date 2024/7/8 16:39
 */
public class CreateDeploymentMsg extends NeedReplyMessage implements NeedQuotaCheckMessage {
    private String namespace;
    private V1Deployment deployment;
    private String pretty;
    private Boolean dryRun;
    private Boolean fieldManager;
    private Boolean fieldValidation;
    private String accountUuid;
    private Integer checkPodStatusInterval;
    private Boolean waitPodReady;
    private Long timeToWaitPodReady;
    private String clusterUuid;

    public Long getTimeToWaitPodReady() {
        return timeToWaitPodReady;
    }

    public void setTimeToWaitPodReady(Long timeToWaitPodReady) {
        this.timeToWaitPodReady = timeToWaitPodReady;
    }

    public Integer getCheckPodStatusInterval() {
        return checkPodStatusInterval;
    }

    public void setCheckPodStatusInterval(Integer checkPodStatusInterval) {
        this.checkPodStatusInterval = checkPodStatusInterval;
    }

    public Boolean getWaitPodReady() {
        return waitPodReady;
    }

    public void setWaitPodReady(Boolean waitPodReady) {
        this.waitPodReady = waitPodReady;
    }

    public Boolean getDryRun() {
        return dryRun;
    }

    public Boolean getFieldManager() {
        return fieldManager;
    }

    public Boolean getFieldValidation() {
        return fieldValidation;
    }

    public V1Deployment getDeployment() {
        return deployment;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setDeployment(V1Deployment deployment) {
        this.deployment = deployment;
    }

    public String getPretty() {
        return pretty;
    }

    public void setDryRun(Boolean dryRun) {
        this.dryRun = dryRun;
    }

    public void setFieldManager(Boolean fieldManager) {
        this.fieldManager = fieldManager;
    }

    public void setFieldValidation(Boolean fieldValidation) {
        this.fieldValidation = fieldValidation;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public void setPretty(String pretty) {
        this.pretty = pretty;
    }

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public String getClusterUuid() {
        return clusterUuid;
    }

    public void setClusterUuid(String clusterUuid) {
        this.clusterUuid = clusterUuid;
    }
}
