package org.zstack.container;

import com.google.common.base.Strings;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.util.ClientBuilder;
import io.kubernetes.client.util.KubeConfig;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.container.entity.KubernetesClusterInventory;
import org.zstack.container.entity.KubernetesNodeInventory;
import org.zstack.container.entity.KubernetesPodInventory;
import org.zstack.container.entity.NativeClusterInventory;
import org.zstack.container.entity.NativeClusterVO;
import org.zstack.container.entity.NativeClusterVO_;
import org.zstack.container.entity.NativeHostVO;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.cluster.ClusterState;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.host.CpuArchitecture;
import org.zstack.header.host.HostState;
import org.zstack.header.host.HostStatus;
import org.zstack.header.image.ImageArchitecture;
import org.zstack.header.vm.VmInstanceSequenceNumberVO;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.zstack.core.Platform.operr;
import static org.zstack.header.configuration.ConfigurationConstant.USER_VM_INSTANCE_OFFERING_TYPE;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class ContainerUtils {
    protected static final CLogger logger = Utils.getLogger(ContainerUtils.class);

    private final Map<String, NativeProvider> nativeProviders = new ConcurrentHashMap<>();

    @Autowired
    private PluginRegistry pluginRegistry;
    @Autowired
    private DatabaseFacade dbf;

    public CoreV1Api getK8SApi(String clusterUuid) {
        NativeClusterVO cluster = dbf.findByUuid(clusterUuid, NativeClusterVO.class);
        return getK8SApiClientWithKubeConfig(cluster.getKubeConfig());
    }

    public CoreV1Api getK8SApi(NativeClusterInventory cluster) {
        return getK8SApiClientWithKubeConfig(cluster.getKubeConfig());
    }

    public CoreV1Api getK8SApiClientWithKubeConfig(String kubeConfig) {
        ApiClient client;
        if (CoreGlobalProperty.UNIT_TEST_ON) {
            client = new ApiClient();
        } else {
            try {
                client = ClientBuilder.kubeconfig(KubeConfig
                                .loadKubeConfig(new StringReader(kubeConfig)))
                        .build();
            } catch (IOException e) {
                throw new OperationFailureException(operr("fail to use kube config file content: %s", e.toString()));
            }
        }

        CoreV1Api api = Platform.New(CoreV1Api::new);
        api.setApiClient(client);
        return api;
    }

    public String getClusterUuidByClusterId(Long clusterId, boolean errorOutIfNotClusterVOCanBeFound) {
        if (clusterId == null) {
            logger.warn("cluster id is null, cannot find cluster by cluster id");
            return null;
        }

        String clusterUuid = Q.New(NativeClusterVO.class)
                .select(NativeClusterVO_.uuid)
                .eq(NativeClusterVO_.id, clusterId)
                .findValue();
        if (clusterUuid == null) {
            if (errorOutIfNotClusterVOCanBeFound) {
                throw new OperationFailureException(operr("Cannot find cluster by cluster id: %s", clusterId));
            } else {
                return null;
            }
        }

        return clusterUuid;
    }

    public String getClusterUuidByClusterId(Long clusterId) {
        return getClusterUuidByClusterId(clusterId, true);
    }

    public ApiClient getK8SApiClient(String accountUuid, String clusterUuid) {
        if (clusterUuid != null) {
            NativeClusterVO cluster = dbf.findByUuid(clusterUuid, NativeClusterVO.class);

            if (Strings.isNullOrEmpty(cluster.getKubeConfig())) {
                throw new OperationFailureException(operr("kube config of cluster %s is null", clusterUuid));
            }

            return createK8sApiClientByKubeConfigContent(cluster.getKubeConfig());
        }

        for (ContainerClientExtensionPoint ext : pluginRegistry.getExtensionList(ContainerClientExtensionPoint.class)) {
            ApiClient client = ext.getContainerClient(accountUuid);
            if (client != null) {
                logger.debug("get k8s client from extension point, override default config from kube.config.path");
                return client;
            }
        }

        String kubeConfigPath = ContainerGlobalConfig.KUBE_CONFIG_PATH.value(String.class);
        return createK8sApiClientByKubeConfigPath(kubeConfigPath);
    }

    private ApiClient createK8sApiClientByKubeConfigContent(String kubeConfigContent) {
        ApiClient client;
        if (CoreGlobalProperty.UNIT_TEST_ON) {
            client = new ApiClient();
            return client;
        }

        try {
            client =
                    ClientBuilder.kubeconfig(KubeConfig.loadKubeConfig(new StringReader(kubeConfigContent))).build();
            Configuration.setDefaultApiClient(client);
            return client;
        } catch (IOException e) {
            throw new OperationFailureException(operr("fail to use kube config file content: %s", e.toString()));
        }
    }

    private ApiClient createK8sApiClientByKubeConfigPath(String kubeConfigPath) {
        ApiClient client;
        if (CoreGlobalProperty.UNIT_TEST_ON) {
            client = new ApiClient();
            return client;
        }
        try {
            client =
                    ClientBuilder.kubeconfig(KubeConfig.loadKubeConfig(new FileReader(kubeConfigPath))).build();
            Configuration.setDefaultApiClient(client);
            return client;
        } catch (FileNotFoundException e) {
            throw new OperationFailureException(operr("fail to find kube config at path: %s", e.toString()));
        } catch (IOException e) {
            throw new OperationFailureException(operr("fail to open kube config file: %s", e.toString()));
        }
    }

    public NativeProvider getNativeProvider(String type) {
        return nativeProviders.get(type);
    }

    protected void init() {
        for (NativeProvider provider : pluginRegistry.getExtensionList(NativeProvider.class)) {
            nativeProviders.put(provider.getNativeProviderType().toString(), provider);
        }
    }

    protected NativeHostVO toNativeHostVO(KubernetesNodeInventory inventory) {
        NativeHostVO host = new NativeHostVO();

        host.setUuid(inventory.getUuid());
        host.setName(inventory.getName());
        host.setClusterUuid(inventory.getClusterUuid());
        host.setManagementIp(inventory.getInternalIp());

        // TODO: Fix k8s node status hard code
        host.setStatus(HostStatus.Connected);

        host.setHypervisorType(NativeFactory.hypervisorType.toString());
        // The reason for this change is that "amd64" and "x86_64" are often
        // used interchangeably to refer to the same 64-bit architecture.
        // To maintain consistency, we convert "amd64" to "x86_64".
        if ("amd64".equals(inventory.getArchitecture())) {
            host.setArchitecture(CpuArchitecture.x86_64.toString());
        } else {
            host.setArchitecture(inventory.getArchitecture());
        }
        host.setState(HostState.Enabled);

        return host;
    }

    protected NativeClusterVO toNativeClusterVO(KubernetesClusterInventory inventory) {
        NativeClusterVO cluster = new NativeClusterVO();

        // 设置基本信息
        cluster.setName(inventory.getName());
        cluster.setDescription("native cluster for kubernetes");

        // 设置类型 - zaku
        cluster.setType("zaku");

        // 设置虚拟化技术
        cluster.setHypervisorType(NativeFactory.hypervisorType.toString());

        // 设置状态
        cluster.setState(ClusterState.Enabled);

        // 生成UUID
        if (cluster.getUuid() == null) {
            cluster.setUuid(Platform.getUuid());
        }

        // 设置 Kubernetes 集群特有字段
        // no url validation only save the url
        cluster.setBizUrl(inventory.getBizUrl());
        cluster.setMasterUrl(inventory.getMasterUrl());
        cluster.setKubeConfig(inventory.getKubeConfig());
        cluster.setPrometheusURL(inventory.getPrometheusURL());
        cluster.setId(inventory.getId());

        // 设置版本和节点数
        cluster.setVersion(inventory.getVersion());
        cluster.setNodeCount(inventory.getNodeCount());

        // 设置创建类型
        cluster.setCreateType(inventory.getCreateType());

        return cluster;
    }

    private static final Map<String, VmInstanceState> POD_STATE_MAPPING = new HashMap<>();
    static {
        POD_STATE_MAPPING.put("Pending", VmInstanceState.Stopped);
        POD_STATE_MAPPING.put("Running", VmInstanceState.Running);
        POD_STATE_MAPPING.put("Succeeded", VmInstanceState.Stopped);
        POD_STATE_MAPPING.put("Failed", VmInstanceState.Stopped);
        POD_STATE_MAPPING.put("Unknown", VmInstanceState.Unknown);
        POD_STATE_MAPPING.put(null, VmInstanceState.Unknown);
    }

    protected VmInstanceVO podToVmInstanceVO(KubernetesPodInventory pod) {
        VmInstanceVO vo = new VmInstanceVO();
        vo.setUuid(pod.getUuid());
        vo.setName(pod.getName());
        vo.setDescription(pod.getNamespace());
        vo.setZoneUuid(pod.getZoneUuid());
        vo.setClusterUuid(pod.getClusterUuid());
        vo.setHostUuid(pod.getHostUuid());
        vo.setHypervisorType(pod.getHypervisorType());
        vo.setCpuNum(pod.getCpuNum());
        vo.setMemorySize(pod.getMemorySize());
        vo.setArchitecture(pod.getArchitecture());
        vo.setPlatform(pod.getPlatform());
        vo.setType(USER_VM_INSTANCE_OFFERING_TYPE);
        vo.setState(POD_STATE_MAPPING.get(pod.getState()));

        vo.setCpuSpeed(0);
        if (vo.getPlatform() == null) {
            vo.setPlatform("other");
            vo.setGuestOsType(vo.getPlatform());
        }

        if (vo.getArchitecture() == null) {
            vo.setArchitecture(ImageArchitecture.x86_64.toString());
        }

        if (vo.getHypervisorType() == null) {
            vo.setHypervisorType(NativeFactory.hypervisorType.toString());
        }
        vo.setInternalId(dbf.generateSequenceNumber(VmInstanceSequenceNumberVO.class));

        return vo;
    }
}
