package org.zstack.container.message

import org.zstack.container.message.APIGetContainerUsageReply

doc {
    title "GetContainerUsage"

    category "container"

    desc """获取容器使用量"""

    rest {
        request {
			url "GET /v1/container/usage"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIGetContainerUsageMsg.class

            desc """"""
            
			params {

				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "query"
					type "List"
					optional true
					since "5.2.1"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "query"
					type "List"
					optional true
					since "5.2.1"
				}
			}
        }

        response {
            clz APIGetContainerUsageReply.class
        }
    }
}