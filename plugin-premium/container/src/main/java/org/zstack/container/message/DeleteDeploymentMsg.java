package org.zstack.container.message;

import org.zstack.header.message.NeedReplyMessage;

/**
 * <AUTHOR>
 * @date 2024/7/9 09:55
 */
public class DeleteDeploymentMsg extends NeedReplyMessage {
    private String deploymentName;
    private String namespace;
    private String accountUuid;
    private String resourceName;
    private Long clusterId;

    public String getDeploymentName() {
        return deploymentName;
    }

    public void setDeploymentName(String deploymentName) {
        this.deploymentName = deploymentName;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }
}
