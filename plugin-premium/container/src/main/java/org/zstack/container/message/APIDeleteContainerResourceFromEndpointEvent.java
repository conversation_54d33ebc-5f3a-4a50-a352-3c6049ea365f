package org.zstack.container.message;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse
public class APIDeleteContainerResourceFromEndpointEvent extends APIEvent {
    public APIDeleteContainerResourceFromEndpointEvent() { }

    public APIDeleteContainerResourceFromEndpointEvent(String apiId) {
        super(apiId);
    }

    public static APIDeleteContainerManagementVmEvent __example__() {
        APIDeleteContainerManagementVmEvent event = new APIDeleteContainerManagementVmEvent();
        event.setSuccess(true);
        return event;
    }
}