package org.zstack.container.message

doc {
    title "UpdateContainerManagementVm"

    category "container"

    desc """更新容器管控节点"""

    rest {
        request {
			url "PUT /v1/container/management/endpoint/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIUpdateContainerManagementEndpointMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn "updateContainerManagementVm"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "5.1.8"
				}
				column {
					name "managementIp"
					enclosedIn "updateContainerManagementVm"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.1.8"
				}
				column {
					name "managementPort"
					enclosedIn "updateContainerManagementVm"
					desc ""
					location "body"
					type "Integer"
					optional true
					since "5.1.8"
				}
				column {
					name "vendor"
					enclosedIn "updateContainerManagementVm"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.1.8"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.1.8"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.1.8"
				}
			}
        }

        response {
            clz APIUpdateContainerManagementEndpointEvent.class
        }
    }
}