package org.zstack.container.message;

import io.kubernetes.client.openapi.models.V1Service;
import org.zstack.header.message.NeedReplyMessage;

/**
 * <AUTHOR>
 * @date 2024/7/8 16:41
 */
public class ReplaceKubernetesServiceMsg extends NeedReplyMessage {
    private String namespace;
    private String port;
    private V1Service service;
    private String pretty;
    private Boolean dryRun;
    private Boolean fieldManager;
    private Boolean fieldValidation;
    private String accountUuid;
    private Long clusterId;

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public void setPretty(String pretty) {
        this.pretty = pretty;
    }

    public String getPretty() {
        return pretty;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setFieldValidation(Boolean fieldValidation) {
        this.fieldValidation = fieldValidation;
    }

    public Boolean getFieldValidation() {
        return fieldValidation;
    }

    public void setFieldManager(Boolean fieldManager) {
        this.fieldManager = fieldManager;
    }

    public Boolean getFieldManager() {
        return fieldManager;
    }

    public void setDryRun(Boolean dryRun) {
        this.dryRun = dryRun;
    }

    public Boolean getDryRun() {
        return dryRun;
    }

    public void setService(V1Service service) {
        this.service = service;
    }

    public V1Service getService() {
        return service;
    }

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public Long getClusterId() {
        return clusterId;
    }

    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }
}