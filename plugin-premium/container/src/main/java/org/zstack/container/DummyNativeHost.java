package org.zstack.container;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.header.host.Host;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.PingHostMsg;
import org.zstack.header.host.PingHostReply;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class DummyNativeHost implements Host {
    protected static final CLogger logger = Utils.getLogger(DummyNativeHost.class);

    @Autowired
    private CloudBus bus;

    private HostVO self;

    public DummyNativeHost(HostVO vo) {
        this.self = vo;
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof PingHostMsg) {
            bus.reply(msg, new PingHostReply());
        } else {
            logger.debug(String.format("Unhandled message[%s] for host[uuid:%s, name:%s]", msg.getClass(), self.getUuid(), self.getName()));
            bus.reply(msg, new MessageReply());
        }
    }

    @Override
    public String getId() {
        return String.format("dummy-native-host-%s", self.getUuid());
    }
}
