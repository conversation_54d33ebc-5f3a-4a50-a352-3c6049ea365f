package org.zstack.container.message;

import org.zstack.container.entity.ContainerManagementEndpointInventory;
import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse(allTo = "inventory")
public class APIAddContainerManagementEndpointEvent extends APIEvent {
    private ContainerManagementEndpointInventory inventory;

    public APIAddContainerManagementEndpointEvent() {
    }

    public APIAddContainerManagementEndpointEvent(String apiId) {
        super(apiId);
    }

    public ContainerManagementEndpointInventory getInventory() {
        return inventory;
    }

    public void setInventory(ContainerManagementEndpointInventory inventory) {
        this.inventory = inventory;
    }

    public static APIAddContainerManagementEndpointEvent __example__() {
        APIAddContainerManagementEndpointEvent event = new APIAddContainerManagementEndpointEvent();
        ContainerManagementEndpointInventory inventory = new ContainerManagementEndpointInventory();
        inventory.setName("containerEndpoint1");
        inventory.setDescription("This is a container management endpoint");
        inventory.setManagementIp("***********");
        inventory.setVendor("VendorName");
        inventory.setManagementPort(8080);
        event.setInventory(inventory);
        event.setSuccess(true);
        return event;
    }
}

