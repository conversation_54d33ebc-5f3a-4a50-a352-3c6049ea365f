package org.zstack.container;

import io.kubernetes.client.openapi.models.V1Node;
import org.zstack.pciDevice.PciDeviceTO;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.Utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HamiGpuAnnotationUtils {
    private static final CLogger logger = Utils.getLogger(HamiGpuAnnotationUtils.class);

    public static List<PciDeviceTO> getGpuInfos(V1Node node, String hostUuid) {
        List<PciDeviceTO> pciDevices = new ArrayList<>();

        if (node.getMetadata() == null) {
            logger.trace(String.format("Node[hostUuid: %s] do not have metadata, skipping parse gpu info", hostUuid));
            return pciDevices;
        }

        Map<String, String> annotations = node.getMetadata().getAnnotations();
        if (annotations != null) {
            for (HamiDeviceProtocolMapping mapping : HamiDeviceProtocolMapping.values()) {
                if (annotations.containsKey(mapping.getAnnotationKey())) {
                    try {
                        List<PciDeviceTO> parsedDevices = mapping.getParser().parse(annotations.get(mapping.getAnnotationKey()), hostUuid);
                        pciDevices.addAll(parsedDevices);
                    } catch (Exception e) {
                        logger.error(String.format("Failed to parse GPU info for annotation key: %s, error: %s", mapping.getAnnotationKey(), e.getMessage()), e);
                    }
                }
            }
        }

        return pciDevices;
    }
}