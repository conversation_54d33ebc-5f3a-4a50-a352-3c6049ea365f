package org.zstack.container.message;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.header.vm.VmInstanceVO;

@TagResourceType(VmInstanceVO.class)
@RestRequest(
        path = "/container/management/endpoint",
        method = HttpMethod.POST,
        responseClass = APIAddContainerManagementEndpointEvent.class,
        parameterName = "params"
)
public class APIAddContainerManagementEndpointMsg extends APICreateMessage {
    @APIParam
    private String name;
    @APIParam(required = false)
    private String description;
    @APIParam
    private String managementIp;
    @APIParam
    private String vendor;
    @APIParam
    private Integer managementPort;
    @APIParam
    private String containerAccessKeyId;
    @APIParam
    private String containerAccessKeySecret;

    public String getManagementIp() {
        return managementIp;
    }

    public void setManagementIp(String managementIp) {
        this.managementIp = managementIp;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public Integer getManagementPort() {
        return managementPort;
    }

    public void setManagementPort(Integer managementPort) {
        this.managementPort = managementPort;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContainerAccessKeyId() {
        return containerAccessKeyId;
    }

    public void setContainerAccessKeyId(String containerAccessKeyId) {
        this.containerAccessKeyId = containerAccessKeyId;
    }

    public String getContainerAccessKeySecret() {
        return containerAccessKeySecret;
    }

    public void setContainerAccessKeySecret(String containerAccessKeySecret) {
        this.containerAccessKeySecret = containerAccessKeySecret;
    }

    public static APIAddContainerManagementEndpointMsg __example__() {
        APIAddContainerManagementEndpointMsg msg = new APIAddContainerManagementEndpointMsg();
        msg.setName("containerEndpoint1");
        msg.setDescription("This is a container management endpoint");
        msg.setManagementIp("***********");
        msg.setVendor("VendorName");
        msg.setManagementPort(8080);
        msg.setContainerAccessKeyId("accessKeyId");
        msg.setContainerAccessKeySecret("accessKeySecret");
        return msg;
    }
}
