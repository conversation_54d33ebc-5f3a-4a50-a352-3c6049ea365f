package org.zstack.container;

import java.util.*;

public class NativeProviderType {
    private static Map<String, NativeProviderType> types = Collections.synchronizedMap(new HashMap<String, NativeProviderType>());
    private final String typeName;
    private boolean exposed = true;

    public NativeProviderType(String typeName) {
        if (typeName == null || typeName.trim().isEmpty()) {
            throw new IllegalArgumentException("typeName cannot be null");
        }
        this.typeName = typeName;
        types.put(typeName, this);
    }

    public NativeProviderType(String typeName, boolean exposed) {
        this(typeName);
        this.exposed = exposed;
    }

    public static boolean hasType(String type) {
        return types.containsKey(type);
    }

    public static NativeProviderType valueOf(String typeName) {
        NativeProviderType type = types.get(typeName);
        if (type == null) {
            throw new IllegalArgumentException("Native provider type: " + typeName + " was not registered by any NativeProviderFactory");
        }
        return type;
    }

    public boolean isExposed() {
        return exposed;
    }

    public void setExposed(boolean exposed) {
        this.exposed = exposed;
    }

    @Override
    public String toString() {
        return typeName;
    }

    @Override
    public boolean equals(Object t) {
        if (!(t instanceof NativeProviderType)) {
            return false;
        }

        NativeProviderType type = (NativeProviderType) t;
        return type.toString().equals(typeName);
    }

    @Override
    public int hashCode() {
        return typeName.hashCode();
    }

    public static Set<String> getAllTypeNames() {
        HashSet<String> exposedTypes = new HashSet<String>();
        for (NativeProviderType type : types.values()) {
            if (type.exposed) {
                exposedTypes.add(type.toString());
            }
        }
        return exposedTypes;
    }
}
