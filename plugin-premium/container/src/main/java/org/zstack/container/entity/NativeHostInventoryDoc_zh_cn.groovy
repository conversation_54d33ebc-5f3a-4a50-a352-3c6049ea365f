package org.zstack.container.entity

import java.lang.Long
import java.lang.Integer
import org.zstack.header.host.HwMonitorStatus
import java.sql.Timestamp

doc {

	title "原生主机"

	field {
		name "endpointUuid"
		desc "容器管理端点UUID"
		type "String"
		since "5.3.6"
	}
	field {
		name "zoneUuid"
		desc "区域UUID"
		type "String"
		since "5.3.6"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "5.3.6"
	}
	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "5.3.6"
	}
	field {
		name "clusterUuid"
		desc "集群UUID"
		type "String"
		since "5.3.6"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "5.3.6"
	}
	field {
		name "managementIp"
		desc "管理IP"
		type "String"
		since "5.3.6"
	}
	field {
		name "hypervisorType"
		desc "hypervisor类型"
		type "String"
		since "5.3.6"
	}
	field {
		name "state"
		desc "启用状态"
		type "String"
		since "5.3.6"
	}
	field {
		name "status"
		desc "连接状态"
		type "String"
		since "5.3.6"
	}
	field {
		name "totalCpuCapacity"
		desc "总CPU容量"
		type "Long"
		since "5.3.6"
	}
	field {
		name "availableCpuCapacity"
		desc "可用的CPU容量"
		type "Long"
		since "5.3.6"
	}
	field {
		name "cpuSockets"
		desc "CPU插槽数量"
		type "Integer"
		since "5.3.6"
	}
	field {
		name "totalMemoryCapacity"
		desc "总内存容量"
		type "Long"
		since "5.3.6"
	}
	field {
		name "availableMemoryCapacity"
		desc "可用的内存容量"
		type "Long"
		since "5.3.6"
	}
	field {
		name "cpuNum"
		desc "CPU数量"
		type "Integer"
		since "5.3.6"
	}
	field {
		name "ipmiAddress"
		desc "IPMI地址"
		type "String"
		since "5.3.6"
	}
	field {
		name "ipmiUsername"
		desc "IPMI用户名"
		type "String"
		since "5.3.6"
	}
	field {
		name "ipmiPort"
		desc "IPMI端口"
		type "Integer"
		since "5.3.6"
	}
	field {
		name "ipmiPowerStatus"
		desc "IPMI电源状态"
		type "String"
		since "5.3.6"
	}
	ref {
		name 'cpuStatus'
		desc "CPU状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "memoryStatus"
		desc "内存状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "diskStatus"
		desc "磁盘状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "nicStatus"
		desc "网卡状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "gpuStatus"
		desc "GPU状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "powerSupplyStatus"
		desc "电源状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "fanStatus"
		desc "风扇状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "raidStatus"
		desc "RAID状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	ref {
		name "temperatureStatus"
		desc "温度状态"
		type "HwMonitorStatus"
		since "5.3.6"
		clz HwMonitorStatus.class
	}
	field {
		name "architecture"
		desc "架构"
		type "String"
		since "5.3.6"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "5.3.6"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "5.3.6"
	}
}
