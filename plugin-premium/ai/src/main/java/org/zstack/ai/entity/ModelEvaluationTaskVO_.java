package org.zstack.ai.entity;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@StaticMetamodel(value = ModelEvaluationTaskVO.class)
public class ModelEvaluationTaskVO_ extends ResourceVO_ {
    public static volatile SingularAttribute<ModelEvaluationTaskVO, String> status;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, Integer> percentage;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, Integer> limits;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, String> opaque;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, String> name;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, String> description;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, String> modelServiceGroupUuid;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, String> evaluatedServiceGroupUuid;
    public static volatile SingularAttribute<ModelEvaluationTaskVO, String> datasetUuid;
}
