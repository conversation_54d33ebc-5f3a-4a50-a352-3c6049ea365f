package org.zstack.ai.vm;

import com.google.gson.JsonSyntaxException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.ai.AIConfigYaml;
import org.zstack.ai.ModelServiceBackend;
import org.zstack.ai.ModelServiceBackendType;
import org.zstack.ai.entity.ModelCenterVO;
import org.zstack.ai.entity.ModelEvaluationTaskStatus;
import org.zstack.ai.entity.ModelEvaluationTaskVO;
import org.zstack.ai.entity.ModelEvaluationTaskVO_;
import org.zstack.ai.entity.ModelServiceInstanceGroupStatus;
import org.zstack.ai.entity.ModelServiceInstanceGroupVO;
import org.zstack.ai.entity.ModelServiceInstanceGroupVO_;
import org.zstack.ai.entity.ModelServiceInstanceVO;
import org.zstack.ai.entity.ModelServiceInventory;
import org.zstack.ai.evaluation.ModelEvaluationTaskProgress;
import org.zstack.ai.evaluation.ModelEvaluationTaskTracker;
import org.zstack.ai.message.ModelService;
import org.zstack.ai.service.ModelEvaluationClient;
import org.zstack.ai.service.ModelEvaluationCommands;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.ha.HaSystemTags;
import org.zstack.ha.VmHaLevel;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.Flow;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.core.workflow.FlowDoneHandler;
import org.zstack.header.core.workflow.FlowErrorHandler;
import org.zstack.header.core.workflow.FlowRollback;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.image.ImageVO;
import org.zstack.header.image.ImageVO_;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.vm.CreateVmInstanceMsg;
import org.zstack.header.vm.CreateVmInstanceReply;
import org.zstack.header.vm.DestroyVmInstanceMsg;
import org.zstack.header.vm.StartVmInstanceMsg;
import org.zstack.header.vm.StopVmInstanceMsg;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.header.vm.VmInstanceDeletionPolicyManager;
import org.zstack.header.vm.VmInstanceInventory;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmNicInventory;
import org.zstack.header.vm.VmNicSpec;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.TagManager;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;
import static org.zstack.header.vm.VmCreationStrategy.CreateStopped;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

public class VmModelServiceBackend implements ModelServiceBackend {
    protected static final CLogger logger = Utils.getLogger(VmModelServiceBackend.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;
    @Autowired
    private RESTFacade restf;
    @Autowired
    private TagManager tagMgr;

    @Override
    public ModelServiceBackendType getType() {
        return ModelServiceBackendType.VirtualMachine;
    }

    private ErrorCode validateServiceVmImageBeforeDeployment(ModelService modelService, ModelServiceInventory service) {
        String imageUuid = service.getVmImageUuid();
        imageUuid = StringUtils.strip(imageUuid, " \t\n\r");

        // use imageUuid in msg instead of service default
        if (!StringUtils.isBlank(modelService.getVmImageUuid())) {
            imageUuid = modelService.getVmImageUuid();
        }

        if (!Q.New(ImageVO.class).eq(ImageVO_.uuid, imageUuid).isExists()) {
            return operr("cannot find image [uuid: %s]", imageUuid);
        }

        if (imageUuid == null) {
            return operr("cannot get imageUuid from yaml: %s", service.getYaml());
        }

        return null;
    }

    private void prepareYamlFormatServiceMetadata(AIConfigYaml configYaml, ModelService modelService, VmInstanceInventory vm) {
        List<String> environmentValuesFromYaml = configYaml.getEnv();
        if (logger.isTraceEnabled()) {
            logger.trace(String.format("environmentValues from model service yaml: %s", JSONObjectUtil.dumpPretty(environmentValuesFromYaml)));
        }

        Map<String, String> environmentToService = new HashMap<>();
        if (!CollectionUtils.isEmpty(environmentValuesFromYaml)) {
            for (String env : environmentValuesFromYaml) {
                if (env == null) {
                    continue;
                }

                // HTTP_PROXY:http://zstack.one:8888
                // split by first ":"
                String[] envParts = env.split(":", 2);
                if (envParts.length != 2) {
                    if (logger.isTraceEnabled()) {
                        logger.trace(String.format("invalid env format: %s", env));
                    }
                    continue;
                }

                environmentToService.put(envParts[0], envParts[1]);
            }
        }

        if (modelService.getEnvironmentVariables() != null) {
            if (logger.isTraceEnabled()) {
                logger.trace(String.format("environmentValues from msg: %s", JSONObjectUtil.dumpPretty(modelService.getEnvironmentVariables())));
            }
            environmentToService.putAll(modelService.getEnvironmentVariables());
            if (logger.isTraceEnabled()) {
                logger.trace(String.format("environmentValues after merge: %s", JSONObjectUtil.dumpPretty(environmentToService)));
            }
        }

        if (modelService.getStartupParameters() != null && !modelService.getStartupParameters().isEmpty()) {
            for (Map.Entry<String, String> entry : modelService.getStartupParameters().entrySet()) {
                configYaml.addStartupParameter(entry.getKey(), entry.getValue());
            }
        }

        if (!environmentToService.isEmpty()) {
            for (Map.Entry<String, String> entry : environmentToService.entrySet()) {
                configYaml.addEnvironmentParameter(entry.getKey(), entry.getValue());
            }
        }

        if (modelService.getNodeNumber() != null) {
            configYaml.addInferenceParam("nodeNumber", String.valueOf(modelService.getNodeNumber()));
            configYaml.addInferenceParam("nodeRank", String.valueOf(modelService.getNodeRank()));
            configYaml.addInferenceParam("tensorParallelSize", String.valueOf(modelService.getTensorParallelSize()));
            if (modelService.getInitialAddress() != null) {
                configYaml.addInferenceParam("distributeInitAddress", modelService.getInitialAddress());
            } else {
                if (vm.getVmNics().stream().anyMatch(nic -> nic.getL3NetworkUuid().equals(vm.getDefaultL3NetworkUuid()))) {
                    configYaml.addInferenceParam("distributeInitAddress", vm.getVmNics().stream().filter(nic -> nic.getL3NetworkUuid().equals(vm.getDefaultL3NetworkUuid())).findFirst().get().getIp());
                } else {
                    throw new OperationFailureException(operr("Cannot find vm nic's ip in default l3 network[uuid: %s] which is required for distributed model service", vm.getDefaultL3NetworkUuid()));
                }
            }
        }
    }

    @Override
    public void deployModelService(ModelService modelService, String groupUuid, ModelCenterVO modelCenter, ModelServiceInventory service, ReturnValueCompletion<String> completion) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("deploy-model-service-%s", service.getName()));
        chain.then(new NoRollbackFlow() {
            String __name__ = "validate-service-vm-image-before-deployment";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                ErrorCode err = validateServiceVmImageBeforeDeployment(modelService, service);
                if (err != null) {
                    trigger.fail(err);
                    return;
                }
                trigger.next();
            }
        }).then(new Flow() {
            String __name__ = "create-stopped-vm-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                CreateVmInstanceMsg cmsg = new CreateVmInstanceMsg();
                cmsg.setSystemTags(modelService.getSystemTags());
                cmsg.setZoneUuid(modelService.getZoneUuid());
                if (modelService.getCpuNum() != null) {
                    cmsg.setCpuNum(modelService.getCpuNum());
                } else {
                    cmsg.setCpuNum(service.getRequestCpu());
                }

                if (modelService.getMemorySize() != null) {
                    cmsg.setMemorySize(modelService.getMemorySize());
                } else {
                    cmsg.setMemorySize(service.getRequestMemory());
                }
                if (modelService.getPrimaryStorageUuid() != null) {
                    cmsg.setPrimaryStorageUuidForRootVolume(modelService.getPrimaryStorageUuid());
                }

                if (modelService.getInstanceName() != null) {
                    cmsg.setName(modelService.getInstanceName());
                } else {
                    cmsg.setName(service.getName());
                }

                cmsg.setSystemTags(modelService.getSystemTags());

                if (modelService.getVmImageUuid() != null) {
                    cmsg.setImageUuid(modelService.getVmImageUuid());
                } else {
                    cmsg.setImageUuid(StringUtils.strip(service.getVmImageUuid(), " \t\n\r"));
                }

                cmsg.setAccountUuid(modelService.getSession().getAccountUuid());
                cmsg.setStrategy(CreateStopped.toString());

                if (modelService.getRootDiskSize() != null) {
                    cmsg.setRootDiskSize(modelService.getRootDiskSize());
                }


                List<String> networkUuids = new ArrayList<>();
                networkUuids.add(modelCenter.getServiceNetworkUuid());

                // override configure yaml if needed
                if (modelService.getL3NetworkUuids() != null && !modelService.getL3NetworkUuids().isEmpty()) {
                    networkUuids.addAll(modelService.getL3NetworkUuids());
                }
                cmsg.setDefaultL3NetworkUuid(modelCenter.getServiceNetworkUuid());
                networkUuids.add(modelCenter.getStorageNetworkUuid());

                List<L3NetworkVO> l3vos = dbf.listByPrimaryKeys(networkUuids, L3NetworkVO.class);
                cmsg.setL3NetworkSpecs(l3vos.stream().map(l3vo ->
                                new VmNicSpec(L3NetworkInventory.valueOf(l3vo)))
                        .collect(Collectors.toList()));
                List<String> systemTags = modelService.getSystemTags();
                if (systemTags == null) {
                    systemTags = new ArrayList<>();
                }
                cmsg.setSystemTags(systemTags);
                bus.makeLocalServiceId(cmsg, VmInstanceConstant.SERVICE_ID);
                bus.send(cmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        CreateVmInstanceReply createVmInstanceReply = reply.castReply();
                        data.put("vm", createVmInstanceReply.getInventory());
                        trigger.next();
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (data.get("vm") == null) {
                    trigger.rollback();
                    return;
                }

                VmInstanceInventory vm = (VmInstanceInventory) data.get("vm");
                DestroyVmInstanceMsg dmsg = new DestroyVmInstanceMsg();
                dmsg.setVmInstanceUuid(vm.getUuid());
                dmsg.setDeletionPolicy(VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.Direct);
                bus.makeTargetServiceIdByResourceUuid(dmsg, VmInstanceConstant.SERVICE_ID, vm.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.rollback();
                            return;
                        }

                        trigger.rollback();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "set-metadata-to-vm-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                AIConfigYaml configYaml = AIConfigYaml.fromYaml(service.getYaml());
                if (configYaml == null) {
                    trigger.fail(operr("cannot parse yaml from model service, service yaml: %s", service.getYaml()));
                    return;
                }

                if (modelService.getPort() != null) {
                    configYaml.getServices().forEach(sc -> {
                        sc.setPorts(Collections.singletonList(Integer.valueOf(modelService.getPort())));
                    });
                }

                VmInstanceInventory vm = (VmInstanceInventory) data.get("vm");

                prepareYamlFormatServiceMetadata(configYaml, modelService, vm);
                String serviceYaml = configYaml.toYaml();

                if (logger.isTraceEnabled()) {
                    logger.trace(String.format("service yaml after prepare: %s", serviceYaml));
                }

                String base64Yaml = Base64.getEncoder().encodeToString(serviceYaml.getBytes());
                String userdata = "userdata::" + base64Yaml;

                List<String> systemTags = new ArrayList<>();
                String haTag = "ha::NeverStop";
                String autoReleaseSpecReleatedPhysicalPciDevice = "autoReleaseSpecReleatedPhysicalPciDevice";
                systemTags.add(userdata);
                systemTags.add(haTag);
                systemTags.add(autoReleaseSpecReleatedPhysicalPciDevice);
                tagMgr.createTags(systemTags, null, vm.getUuid(), VmInstanceVO.class.getSimpleName());

                data.put("serviceYaml", serviceYaml);
                data.put("configYaml", configYaml);

                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "start-vm-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                StartVmInstanceMsg msg = new StartVmInstanceMsg();
                msg.setVmInstanceUuid(((VmInstanceInventory) data.get("vm")).getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "save-model-service-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                VmInstanceInventory vm = (VmInstanceInventory) data.get("vm");
                AIConfigYaml configYaml = (AIConfigYaml) data.get("configYaml");
                String serviceYaml = (String) data.get("serviceYaml");

                List<Integer> ports = configYaml.getServices().get(0).getPorts();
                Integer port = ports.get(0);
                String url = getUrl(vm, port);
                String internalUrl = getInternalUrl(vm, modelCenter.getStorageNetworkUuid(), port);
                ModelServiceInstanceVO instanceVO = new ModelServiceInstanceVO();
                instanceVO.setModelServiceGroupUuid(groupUuid);
                instanceVO.setUuid(configYaml.getInstanceUuid());
                instanceVO.setUrl(url);
                instanceVO.setInternalUrl(internalUrl);
                instanceVO.setStatus(ModelServiceInstanceGroupStatus.Starting.toString());
                instanceVO.setYaml(serviceYaml);
                instanceVO.setAccountUuid(modelService.getSession().getAccountUuid());
                instanceVO.setVmInstanceUuid(vm.getUuid());
                dbf.persist(instanceVO);

                data.put("uuid", instanceVO.getUuid());
                trigger.next();
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success((String) data.get("uuid"));
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private String getInternalUrl(VmInstanceInventory inventory, String networkUuid, Integer port) {
        String storageIP = getIpAccordingNetworkUuid(inventory, networkUuid);
        if(StringUtils.isEmpty(storageIP)) {
            return "";
        }
        String internalUrl = storageIP + ":" + String.valueOf(port);
        return internalUrl;
    }

    private String getUrl(VmInstanceInventory inventory, Integer port) {
        String defaultIp = getIpAccordingNetworkUuid(inventory, inventory.getDefaultL3NetworkUuid());
        if (StringUtils.isEmpty(defaultIp)) {
            defaultIp = inventory.getVmNics().get(0).getIp();
        }
        String url = defaultIp + ":" + String.valueOf(port);
        return url;
    }

    private String getIpAccordingNetworkUuid(VmInstanceInventory inventory, String networkUuid) {
        String ip = "";
        VmNicInventory vmNicInventory = inventory.getVmNics().stream().filter(nic ->nic.getL3NetworkUuid().equals(networkUuid)).findFirst().orElse(null);
        if (vmNicInventory != null) {
            ip = vmNicInventory.getIp();
        }
        return ip;
    }

    @Override
    public void deleteModelServiceInstance(ModelServiceInstanceVO instance, Completion completion) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-model-service-instance-group-%s", instance.getUuid()));
        chain.then(new NoRollbackFlow() {
            @Override
            public boolean skip(Map data) {
                return instance.getVmInstanceUuid() == null;
            }

            String __name__ = "delete-vm-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                StopVmInstanceMsg msg = new StopVmInstanceMsg();
                msg.setVmInstanceUuid(instance.getVmInstanceUuid());
                msg.setStopHA(true);
                bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public boolean skip(Map data) {
                return instance.getVmInstanceUuid() == null;
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                DestroyVmInstanceMsg msg = new DestroyVmInstanceMsg();
                msg.setVmInstanceUuid(instance.getVmInstanceUuid());
                msg.setDeletionPolicy(VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.Direct);
                bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).start();
    }

    @Override
    public void cancelTask(ModelServiceInstanceVO instance, String taskUuid, Completion completion) {
        ModelEvaluationClient client = createModelEvaluationClient(instance);

        try {
            ModelEvaluationCommands.CancelTaskResponse response = client.cancelEvaluation(taskUuid);
            if (logger.isTraceEnabled()) {
                logger.trace(String.format("cancel task response: %s", JSONObjectUtil.dumpPretty(response)));
            }

            if (!response.isSuccess()) {
                completion.fail(operr("failed to cancel task, %s", response.getError()));
                return;
            }
        } catch (Exception e) {
            logger.warn(String.format("failed to cancel task, %s", e.getMessage()));
            completion.fail(operr("failed to cancel task, %s", e.getMessage()));
            return;
        }

        completion.success();
    }

    private ModelEvaluationClient createModelEvaluationClient(ModelServiceInstanceVO instance) {
        ModelEvaluationClient client;

        if (CoreGlobalProperty.UNIT_TEST_ON) {
            client = new ModelEvaluationClient("http://127.0.0.1:8989", restf);
        } else {
            String instanceUrl = instance.getUrl();
            if (!instanceUrl.startsWith("http://") && !instanceUrl.startsWith("https://"))
                instanceUrl = "http://" + instanceUrl;

            client = new ModelEvaluationClient(instanceUrl, restf);
        }
        return client;
    }

    @Override
    public void getTaskProgress(ModelServiceInstanceVO instance, ReturnValueCompletion<List<ModelEvaluationTaskProgress>> completion) {
        if (instance.getUrl() == null) {
            completion.fail(operr("cannot get status from instance with empty url, report failed"));
            return;
        }

        ModelEvaluationClient client = createModelEvaluationClient(instance);

        // skip failed model evaluation tasks
        List<String> taskUuids = Q.New(ModelEvaluationTaskVO.class)
                .select(ModelEvaluationTaskVO_.uuid)
                .eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, instance.getModelServiceGroupUuid())
                .in(ModelEvaluationTaskVO_.status, ModelEvaluationTaskTracker.agentStatus)
                .listValues();
        if (logger.isTraceEnabled()) {
            logger.debug(String.format("getTaskProgress, taskUuids: %s", taskUuids));
        }
        List<ModelEvaluationTaskProgress> progressList = new ArrayList<>();
        for (String taskUuid : taskUuids) {
            ModelEvaluationCommands.TaskStatusResponse response = client.getTaskStatus(taskUuid);

            ModelEvaluationTaskProgress progress = new ModelEvaluationTaskProgress();
            if ("running".equals(response.getStatus()) || "pending".equals(response.getStatus())) {
                progress.setStatus(ModelEvaluationTaskStatus.Running);
            } else if ("completed".equals(response.getStatus())) {
                progress.setStatus(ModelEvaluationTaskStatus.Completed);
            } else if ("partial_completed".equals(response.getStatus())) {
                progress.setStatus(ModelEvaluationTaskStatus.PartialCompleted);
            } else if ("idle".equals(response.getStatus())) {
                progress.setStatus(ModelEvaluationTaskStatus.Failed);
            } else if ("failed".equals(response.getStatus())) {
                progress.setStatus(ModelEvaluationTaskStatus.Failed);
            } else {
                progress.setStatus(ModelEvaluationTaskStatus.Failed);
            }

            if (response.getProgress() != null && response.getProgress().getPercentage() != null) {
                progress.setPercentage((int) (response.getProgress().getPercentage() * 100));
            }

            if (StringUtils.isNotEmpty(response.getResultPath())
                    && !StringUtils.startsWith(response.getResultPath(), "file://")) {
                response.setResultPath(String.format("file://%s", response.getResultPath()));
            }

            try {
                progress.setOpaque(JSONObjectUtil.rehashObject(response, LinkedHashMap.class));
                if (ModelEvaluationTaskStatus.Failed.equals(progress.getStatus())) {
                    ModelEvaluationCommands.LogResponse log = client.getLog();
                    progress.getOpaque().put("logs", log.getLogs());
                }
            } catch (JsonSyntaxException exception) {
                logger.debug("Failed to parse response and save to opaque");
            }

            progress.setTaskUuid(taskUuid);
            progressList.add(progress);
        }

        completion.success(progressList);
    }

    @Override
    public void restartModelServiceGroup(ModelServiceInstanceGroupVO group, Completion completion) {
        if (CollectionUtils.isEmpty(group.getInstances())) {
            completion.success();
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.then(new NoRollbackFlow() {
            String __name__ = "update-metadata-of-vm-instance";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                for (ModelServiceInstanceVO instance : group.getInstances()) {
                    if (instance == null) {
                        logger.warn("instance is null, skip user data update");
                        continue;
                    }

                    String userdata = VmSystemTags.USERDATA.getTokenByResourceUuid(instance.getVmInstanceUuid(), VmSystemTags.USERDATA_TOKEN);
                    if (userdata == null) {
                        logger.warn("userdata is null, skip user data update");
                        continue;
                    }

                    VmSystemTags.USERDATA.delete(instance.getVmInstanceUuid());
                    logger.warn(String.format("delete userdata for vm instance: %s", instance.getVmInstanceUuid()));

                    logger.debug(String.format("update userdata for vm instance: %s, new userdata: %s", instance.getVmInstanceUuid(), instance.getYaml()));
                    String base64Yaml = Base64.getEncoder().encodeToString(instance.getYaml().getBytes());
                    SystemTagCreator creator = VmSystemTags.USERDATA.newSystemTagCreator(instance.getVmInstanceUuid());
                    creator.setTagByTokens(map(e(VmSystemTags.USERDATA_TOKEN, base64Yaml)));
                    creator.recreate = true;
                    creator.create();
                }

                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "stop-vm-instances";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                new While<>(group.getInstances()).each((instance, whileCompletion) -> {
                    if (instance.getVmInstanceUuid() == null) {
                        whileCompletion.done();
                        return;
                    }

                    StopVmInstanceMsg msg = new StopVmInstanceMsg();
                    msg.setVmInstanceUuid(instance.getVmInstanceUuid());
                    msg.setStopHA(true);
                    bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                    bus.send(msg, new CloudBusCallBack(whileCompletion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                whileCompletion.addError(reply.getError());
                                whileCompletion.allDone();
                                return;
                            }

                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList);
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "start-vm-instances";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                new While<>(group.getInstances()).each((instance, whileCompletion) -> {
                    if (instance.getVmInstanceUuid() == null) {
                        whileCompletion.done();
                        return;
                    }

                    StartVmInstanceMsg msg = new StartVmInstanceMsg();
                    msg.setVmInstanceUuid(instance.getVmInstanceUuid());
                    bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                    bus.send(msg, new CloudBusCallBack(whileCompletion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                whileCompletion.addError(reply.getError());
                                return;
                            }

                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList);
                            return;
                        }

                        trigger.next();
                    }
                });
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).start();
    }
}
