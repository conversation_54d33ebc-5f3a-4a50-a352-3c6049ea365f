package org.zstack.ai.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Transient;

@Entity
@Table
public class ApplicationDevelopmentServiceVO extends ModelServiceInstanceGroupVO {
    @Column
    @Enumerated(EnumType.STRING)
    private DeploymentStatus deploymentStatus;
    @Transient
    private String accountUuid;

    public ApplicationDevelopmentServiceVO() {
    }

    public ApplicationDevelopmentServiceVO(ModelServiceInstanceGroupVO vo) {
        super(vo);
    }

    public DeploymentStatus getDeploymentStatus() {
        return deploymentStatus;
    }

    public void setDeploymentStatus(DeploymentStatus deploymentStatus) {
        this.deploymentStatus = deploymentStatus;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    @Override
    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }
}
