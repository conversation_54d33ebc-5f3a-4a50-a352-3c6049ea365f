package org.zstack.ai.entity;

import org.zstack.core.Platform;
import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Base64;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/6 08:25
 */

@Inventory(mappingVOClass = ModelVO.class)
@PythonClassInventory
public class ModelInventory implements Serializable {
    private String uuid;

    private String name;

    private String description;

    private String installPath;

    private String parameters;

    private String modelCenterUuid;

    private String logo;

    private String vendor;

    private String modelId;

    private String introduction;

    private Long size;

    private String version;

    private String type;

    private Long minGpuMemory;

    private List<ModelServiceRefInventory> modelServiceRefs;
    // 2 * 1024 * 1024 * 1024 = 2147483648; 2G
    @APINoSee
    private Long extraGpuMemRequest = 2147483648l;

    @APINoSee
    private int SAMPLE_SIZE = 1000;

    @APINoSee
    static final String BASE64_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+=/";

    private Timestamp createDate;
    private Timestamp lastOpDate;

    public ModelInventory() {};

    public ModelInventory(ModelVO modelVO) {
        uuid = modelVO.getUuid();
        name = modelVO.getName();
        description = modelVO.getDescription();
        installPath = modelVO.getInstallPath();
        parameters = decodeIfBase64(modelVO.getParameters());
        modelCenterUuid = modelVO.getModelCenterUuid();
        logo = modelVO.getLogo();
        vendor = modelVO.getVendor();
        introduction = decodeIfBase64(modelVO.getIntroduction());
        size = modelVO.getSize();
        minGpuMemory = modelVO.getSize() + extraGpuMemRequest;
        version = modelVO.getVersion();
        modelServiceRefs = ModelServiceRefInventory.valueOf1(modelVO.getModelServiceRefs());
        type = modelVO.getType().toString();
        createDate = modelVO.getCreateDate();
        lastOpDate = modelVO.getLastOpDate();
        modelId = modelVO.getModelId();
    }

    private String decodeIfBase64(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        if (!isLikelyBase64(input)) {
            return input;
        }

        try {
            byte[] decodedBytes = Base64.getDecoder().decode(input);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            return input;
        }
    }

    private boolean isLikelyBase64(String str) {
        if (str.length() % 4 != 0) {
            return false;
        }

        int paddingCount = 0;
        for (int i = str.length() - 1; i >= Math.max(0, str.length() - 3); i--) {
            if (str.charAt(i) == '=') {
                paddingCount++;
            } else {
                break;
            }
        }

        if (paddingCount > 2) {
            return false;
        }

        int effectiveLength = str.length() - paddingCount;

        if (effectiveLength <= 3000) {
            // 如果字符串较短，检查所有字符
            return checkAllChars(str, effectiveLength);
        } else {
            // 检查头部、尾部和中间的字符
            return checkSampleParts(str, effectiveLength);
        }
    }

    private boolean isBase64Char(char c) {
        return BASE64_CHARS.indexOf(c) != -1;
    }

    private boolean checkAllChars(String str, int length) {
        for (int i = 0; i < length; i++) {
            char c = str.charAt(i);
            if (!isBase64Char(c)) {
                return false;
            }
        }
        return true;
    }

    private boolean checkSampleParts(String str, int effectiveLength) {
        int validCount = 0;
        int totalSamples = SAMPLE_SIZE * 3;

        // 检查头部
        validCount += checkPart(str, 0, SAMPLE_SIZE);

        // 检查尾部
        validCount += checkPart(str, effectiveLength - SAMPLE_SIZE, SAMPLE_SIZE);

        // 检查中间部分
        int middleStart = (effectiveLength - SAMPLE_SIZE) / 2;
        validCount += checkPart(str, middleStart, SAMPLE_SIZE);

        // 如果95%的采样字符都是有效的Base64字符，我们认为它可能是Base64编码
        return (double) validCount / totalSamples > 0.95;
    }

    private int checkPart(String str, int start, int length) {
        int validCount = 0;
        for (int i = 0; i < length; i++) {
            char c = str.charAt(start + i);
            if (isBase64Char(c)) {
                validCount++;
            }
        }
        return validCount;
    }

    public static ModelInventory valueOf(ModelVO modelVO) {
        return new ModelInventory(modelVO);
    }

    public static List<ModelInventory> valueOf(Collection<ModelVO> modelVOList) {
        return modelVOList.stream().map(ModelInventory::valueOf).collect(Collectors.toList());
    }

    public Long getMinGpuMemory() {
        return minGpuMemory;
    }

    public void setMinGpuMemory(Long minGpuMemory) {
        this.minGpuMemory = minGpuMemory;
    }

    public void setModelCenterUuid(String modelCenterUuid) {
        this.modelCenterUuid = modelCenterUuid;
    }

    public String getModelCenterUuid() {
        return modelCenterUuid;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    public String getParameters() {
        return parameters;
    }

    public void setInstallPath(String installPath) {
        this.installPath = installPath;
    }

    public String getInstallPath() {
        return installPath;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getIntroduction() {
        return introduction;
    }

    public String getVersion() {
        return version;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public List<ModelServiceRefInventory> getModelServiceRefs() {
        return modelServiceRefs;
    }

    public void setModelServiceRefs(List<ModelServiceRefInventory> modelServiceRefs) {
        this.modelServiceRefs = modelServiceRefs;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public static ModelInventory __example__() {
        ModelInventory inventory = new ModelInventory();
        inventory.setUuid(Platform.getUuid());
        inventory.setName("test-model");
        inventory.setDescription("test model");
        inventory.setInstallPath("/usr/local/test-model");
        inventory.setParameters("test parameters");
        inventory.setModelCenterUuid(Platform.getUuid());
        inventory.setLogo("BASE64data");
        inventory.setIntroduction("This is a md5 format string");
        inventory.setSize(1024000l);
        inventory.setVendor("ZStack");
        inventory.setVersion("CustomizeVersion");
        inventory.setType(ModelType.System.toString());
        inventory.setModelId("test-model-id");
        return inventory;
    }
}
