package org.zstack.ai.entity;

import org.zstack.core.Platform;
import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;
import org.zstack.header.zdfs.ZdfsInventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/6 08:25
 */

@Inventory(mappingVOClass = ModelCenterVO.class)
@PythonClassInventory
public class ModelCenterInventory implements Serializable {
    private String uuid;
    private String name;
    private String description;
    private String url;
    private String status;
    private String parameters;
    private String managementIp;
    private int managementPort;
    private String storageNetworkUuid;
    private String serviceNetworkUuid;
    private String containerRegistry;
    private String containerStorageNetwork;
    private String containerNetwork;
    private ModelCenterCapacityInventory capacity;
    private ZdfsInventory zdfs;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public ModelCenterInventory() {};

    public ModelCenterInventory(ModelCenterVO modelCenterVO) {
        uuid = modelCenterVO.getUuid();
        name = modelCenterVO.getName();
        description = modelCenterVO.getDescription();
        url = modelCenterVO.getUrl();
        parameters = modelCenterVO.getParameters();
        managementIp = modelCenterVO.getManagementIp();
        managementPort = modelCenterVO.getManagementPort();
        storageNetworkUuid = modelCenterVO.getStorageNetworkUuid();
        serviceNetworkUuid = modelCenterVO.getServiceNetworkUuid();
        containerNetwork = modelCenterVO.getContainerNetwork();
        containerStorageNetwork = modelCenterVO.getContainerStorageNetwork();
        containerRegistry = modelCenterVO.getContainerRegistry();
        createDate = modelCenterVO.getCreateDate();
        lastOpDate = modelCenterVO.getLastOpDate();
        status = modelCenterVO.getStatus().toString();
        if (modelCenterVO.getCapacity() != null) {
            capacity = ModelCenterCapacityInventory.valueOf(modelCenterVO.getCapacity());
        }

        if (modelCenterVO.getZdfs() != null) {
            zdfs = ZdfsInventory.valueOf(modelCenterVO.getZdfs());
        }
    }

    public static ModelCenterInventory valueOf(ModelCenterVO modelCenterVO) {
        return new ModelCenterInventory(modelCenterVO);
    }

    public static List<ModelCenterInventory> valueOf(Collection<ModelCenterVO> modelCenterVOList) {
        return modelCenterVOList.stream().map(ModelCenterInventory::valueOf).collect(Collectors.toList());
    }

    public ZdfsInventory getZdfs() {
        return zdfs;
    }

    public void setZdfs(ZdfsInventory zdfs) {
        this.zdfs = zdfs;
    }

    public String getContainerStorageNetwork() {
        return containerStorageNetwork;
    }

    public void setContainerStorageNetwork(String containerStorageNetwork) {
        this.containerStorageNetwork = containerStorageNetwork;
    }

    public void setContainerNetwork(String containerNetwork) {
        this.containerNetwork = containerNetwork;
    }

    public String getContainerNetwork() {
        return containerNetwork;
    }
    public void setStorageNetworkUuid(String storageNetworkUuid) {
        this.storageNetworkUuid = storageNetworkUuid;
    }

    public String getStorageNetworkUuid() {
        return storageNetworkUuid;
    }

    public void setServiceNetworkUuid(String serviceNetworkUuid) {
        this.serviceNetworkUuid = serviceNetworkUuid;
    }

    public String getServiceNetworkUuid() {
        return serviceNetworkUuid;
    }

    public void setContainerRegistry(String containerRegistry) {
        this.containerRegistry = containerRegistry;
    }

    public String getContainerRegistry() {
        return containerRegistry;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    public String getParameters() {
        return parameters;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrl() {
        return url;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getManagementIp() {
        return managementIp;
    }

    public void setManagementIp(String managementIp) {
        this.managementIp = managementIp;
    }

    public int getManagementPort() {
        return managementPort;
    }

    public void setManagementPort(int managementPort) {
        this.managementPort = managementPort;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ModelCenterCapacityInventory getCapacity() {
        return capacity;
    }

    public void setCapacity(ModelCenterCapacityInventory capacity) {
        this.capacity = capacity;
    }

    public static ModelCenterInventory __example__() {
        ModelCenterInventory inv = new ModelCenterInventory();
        inv.setUuid(Platform.getUuid());
        inv.setName("modelCenter");
        inv.setDescription("modelCenter");
        inv.setUrl("http://modelCenter");
        inv.setParameters("parameters");
        inv.setManagementIp("***********");
        inv.setManagementPort(5000);
        return inv;
    }
}
