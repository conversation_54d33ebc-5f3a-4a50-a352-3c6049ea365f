package org.zstack.ai.entity;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.vo.ResourceVO;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.sql.Timestamp;

@Table
@Entity
public class ModelEvaluationTaskVO extends ResourceVO implements OwnedByAccount {
    @Column
    private String name;
    @Column
    private String type;
    @Column
    private String description;
    @Column
    private int percentage;
    @Column
    private String opaque;
    @Column
    @Enumerated(EnumType.STRING)
    private ModelEvaluationTaskStatus status;
    @Column
    private String modelServiceGroupUuid;
    @Column
    private String evaluatedServiceGroupUuid;
    @Column
    private String datasetUuid;
    @Column
    private Integer limits;

    @Column
    private String taskRequestInJson;

    @Transient
    private String accountUuid;

    @Column
    private Timestamp createDate;
    @Column
    private Timestamp lastOpDate;

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public ModelEvaluationTaskStatus getStatus() {
        return status;
    }

    public void setStatus(ModelEvaluationTaskStatus status) {
        this.status = status;
    }

    public String getModelServiceGroupUuid() {
        return modelServiceGroupUuid;
    }

    public void setModelServiceGroupUuid(String modelServiceGroupUuid) {
        this.modelServiceGroupUuid = modelServiceGroupUuid;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    @Override
    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getPercentage() {
        return percentage;
    }

    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    public String getOpaque() {
        return opaque;
    }

    public void setOpaque(String opaque) {
        this.opaque = opaque;
    }

    public String getDatasetUuid() {
        return datasetUuid;
    }

    public void setDatasetUuid(String datasetUuid) {
        this.datasetUuid = datasetUuid;
    }

    public String getEvaluatedServiceGroupUuid() {
        return evaluatedServiceGroupUuid;
    }

    public void setEvaluatedServiceGroupUuid(String evaluatedServiceGroupUuid) {
        this.evaluatedServiceGroupUuid = evaluatedServiceGroupUuid;
    }

    public Integer getLimits() {
        return limits;
    }

    public void setLimits(Integer limits) {
        this.limits = limits;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTaskRequestInJson() {
        return taskRequestInJson;
    }

    public void setTaskRequestInJson(String taskRequestInJson) {
        this.taskRequestInJson = taskRequestInJson;
    }
}
