package org.zstack.ai.entity;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@StaticMetamodel(ApplicationDevelopmentServiceVO.class)
public class ApplicationDevelopmentServiceVO_ extends ResourceVO_ {
    public static volatile SingularAttribute<ApplicationDevelopmentServiceVO, String> name;
    public static volatile SingularAttribute<ApplicationDevelopmentServiceVO, String> modelServiceGroupUuid;
    public static volatile SingularAttribute<ApplicationDevelopmentServiceVO, String> modelServiceUuid;
    public static volatile SingularAttribute<ApplicationDevelopmentServiceVO, DeploymentStatus> deploymentStatus;
}
