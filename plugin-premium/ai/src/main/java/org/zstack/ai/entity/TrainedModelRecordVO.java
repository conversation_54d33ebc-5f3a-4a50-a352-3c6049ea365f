package org.zstack.ai.entity;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.vo.EntityGraph;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.vo.ToInventory;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/8/8 21:31
 */

@Entity
@Table
@org.zstack.header.vo.EntityGraph(
        friends = {
                @EntityGraph.Neighbour(type = ModelVO.class, myField = "modelUuid", targetField = "uuid"),
                @EntityGraph.Neighbour(type = ModelServiceInstanceGroupVO.class, myField = "modelServiceInstanceGroupUuid", targetField = "uuid"),
                @EntityGraph.Neighbour(type = DatasetVO.class, myField = "datasetUuid", targetField = "uuid")
        }
)
public class TrainedModelRecordVO extends ResourceVO implements ToInventory, OwnedByAccount {
        @Column
        @Id
        private String uuid;

        @Column
        private String modelUuid;

        @Column
        private String sourceModelUuid;

        @Column
        private String modelServiceInstanceGroupUuid;

        @Column
        private String datasetUuid;

        @Column
        private Timestamp createDate;

        @Column
        private Timestamp lastOpDate;

        @Transient
        private String accountUuid;

        @PreUpdate
        private void preUpdate() {
                lastOpDate = null;
        }

        public void setUuid(String uuid) {
                this.uuid = uuid;
        }

        public String getUuid() {
                return uuid;
        }

        public String getSourceModelUuid() {
                return sourceModelUuid;
        }

        public void setSourceModelUuid(String sourceModelUuid) {
                this.sourceModelUuid = sourceModelUuid;
        }

        public String getDatasetUuid() {
                return datasetUuid;
        }

        public void setDatasetUuid(String datasetUuid) {
                this.datasetUuid = datasetUuid;
        }

        public String getModelUuid() {
                return modelUuid;
        }

        public void setModelUuid(String modelUuid) {
                this.modelUuid = modelUuid;
        }

        public String getModelServiceInstanceGroupUuid() {
                return modelServiceInstanceGroupUuid;
        }

        public void setModelServiceInstanceGroupUuid(String modelServiceInstanceGroupUuid) {
                this.modelServiceInstanceGroupUuid = modelServiceInstanceGroupUuid;
        }

        @Override
        public String getAccountUuid() {
                return this.accountUuid;
        }

        @Override
        public void setAccountUuid(String accountUuid) {
                this.accountUuid = accountUuid;
        }

        public Timestamp getCreateDate() {
                return createDate;
        }

        public void setCreateDate(Timestamp createDate) {
                this.createDate = createDate;
        }

        public Timestamp getLastOpDate() {
                return lastOpDate;
        }

        public void setLastOpDate(Timestamp lastOpDate) {
                this.lastOpDate = lastOpDate;
        }
}
