package org.zstack.ai;

import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.Synchronized;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.zstack.ai.entity.*;
import org.zstack.ai.message.*;
import org.zstack.ai.service.ModelServiceFactory;
import org.zstack.ai.vm.VmModelServiceBackend;
import org.zstack.container.ContainerUsage;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.*;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.PeriodicTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.timeout.TimeHelper;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.AbstractService;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.configuration.APIDeleteInstanceOfferingEvent;
import org.zstack.header.configuration.InstanceOfferingInventory;
import org.zstack.header.configuration.InstanceOfferingVO;
import org.zstack.header.core.*;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.identity.Quota;
import org.zstack.header.identity.ReportQuotaExtensionPoint;
import org.zstack.header.identity.quota.QuotaDefBuilder;
import org.zstack.header.identity.quota.QuotaMessageHandler;
import org.zstack.header.managementnode.*;
import org.zstack.header.message.*;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.tag.TagPatternType;
import org.zstack.header.tag.TagPatternVO;
import org.zstack.header.tag.UserTagVO;
import org.zstack.header.tag.UserTagVO_;
import org.zstack.header.zdfs.*;
import org.zstack.zdfs.ZdfsConstant;
import org.zstack.premium.externalservice.prometheus.PrometheusConfig;
import org.zstack.proxy.*;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.TagManager;
import org.zstack.utils.*;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.linux.ServiceUtils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.path.PathUtil;
import org.zstack.zwatch.ZWatchManager;
import org.zstack.zwatch.prometheus.PrometheusCanonicalEvents;
import org.zstack.zwatch.prometheus.PrometheusStaticConfigManager;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.zstack.core.Platform.*;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

/**
 * <AUTHOR>
 * @date 2024/6/13 13:44
 */
public class AIModelManagerImpl extends AbstractService implements
        AIModelManager,
        ManagementNodeChangeListener,
        ManagementNodeReadyExtensionPoint,
        ProxyResourceExtensionPoint,
        ReportQuotaExtensionPoint,
        PrepareDbInitialValueExtensionPoint
{
    protected static final CLogger logger = Utils.getLogger(AIModelManagerImpl.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    protected ThreadFacade thdf;
    @Autowired
    protected EventFacade evtf;
    @Autowired
    private RESTFacade restf;
    @Autowired
    private PluginRegistry pluginRegistry;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    private PrometheusStaticConfigManager pscMgr;
    @Autowired
    private ResourceDestinationMaker resourceDestinationMaker;
    @Autowired
    private ModelCenterTracker tracker;
    @Autowired
    private AILicenseQuotaChecker aiLicenseQuotaChecker;
    @Autowired
    private TimeHelper zTimer;
    @Autowired
    private ZWatchManager zWatchManager;

    private static final AIUtils aiUtils = new AIUtils();
    private static final Map<ModelServiceBackendType, ModelServiceBackend> backends = new HashMap<>();
    private static final Map<ModelServiceType, ModelServiceFactory> factories = new HashMap<>();

    private static final List<ModelServiceInstanceGroupStatus> intermediateStatus = Arrays.asList(
            ModelServiceInstanceGroupStatus.Deploying,
            ModelServiceInstanceGroupStatus.Starting,
            ModelServiceInstanceGroupStatus.ConfigSettingUp,
            ModelServiceInstanceGroupStatus.ServiceBootingUp
    );

    private static final String ALLOWED_MODE = "develop";

    public static ModelServiceBackend getModelServiceBackendByType(ModelServiceBackendType type) {
        return backends.get(type);
    }

    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    public String getId() {
        return bus.makeLocalServiceId(SERVICE_ID);
    }

    protected void handleLocalMessage(Message msg) {
        if (msg instanceof AllocateModelServiceMsg) {
            handle((AllocateModelServiceMsg) msg);
        } else if (msg instanceof ConnectModelCenterMsg) {
            handle((ConnectModelCenterMsg) msg);
        } else if (msg instanceof DeployModelServiceMsg) {
            handle((DeployModelServiceMsg) msg);
        } else if (msg instanceof AddModelMsg) {
            handle((AddModelMsg) msg);
        } else if (msg instanceof AddModelServiceMsg) {
            handle((AddModelServiceMsg) msg);
        } else if (msg instanceof DeleteModelServiceInstanceGroupMsg) {
            handle((DeleteModelServiceInstanceGroupMsg) msg);
        } else if (msg instanceof DeleteModelServiceMsg) {
            handle((DeleteModelServiceMsg) msg);
        } else if (msg instanceof CreateDatasetMsg) {
            handle((CreateDatasetMsg) msg);
        } else if (msg instanceof SyncAINginxConfigurationMsg) {
            handle((SyncAINginxConfigurationMsg) msg);
        } else if (msg instanceof ModelCenterPingMsg) {
            handle((ModelCenterPingMsg) msg);
        } else if (msg instanceof DeleteInstanceNginxRuleMsg) {
            handle((DeleteInstanceNginxRuleMsg) msg);
        } else if (msg instanceof RestartModelServiceGroupMsg) {
            handle((RestartModelServiceGroupMsg) msg);
        } else if (msg instanceof ModelCenterDeletionMsg) {
            handle((ModelCenterDeletionMsg) msg);
        } else if (msg instanceof ModelServiceDeletionMsg) {
            handle((ModelServiceDeletionMsg) msg);
        } else if (msg instanceof ModelDeletionMsg) {
            handle((ModelDeletionMsg) msg);
        } else if (msg instanceof DatasetDeletionMsg) {
            handle((DatasetDeletionMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(ModelCenterDeletionMsg msg) {
        if (!Q.New(ModelCenterVO.class)
                .eq(ModelCenterVO_.uuid, msg.getUuid())
                .isExists()) {
            logger.warn(String.format("model center[uuid: %s] not found, skip deletion", msg.getUuid()));
            tracker.untrack(msg.getUuid());
            ModelCenterDeletionReply reply = new ModelCenterDeletionReply();
            bus.reply(msg, reply);
            return;
        }

        SQL.New(ModelCenterVO.class)
                .eq(ModelCenterVO_.uuid, msg.getUuid())
                .hardDelete();

        tracker.untrack(msg.getUuid());

        ModelCenterDeletionReply reply = new ModelCenterDeletionReply();
        bus.reply(msg, reply);
    }

    private void handle(DatasetDeletionMsg msg) {
        DatasetVO dataset = Q.New(DatasetVO.class)
                .eq(DatasetVO_.uuid, msg.getUuid())
                .find();
        if (dataset == null) {
            logger.warn(String.format("dataset[uuid: %s] not found, skip deletion", msg.getUuid()));
            DatasetDeletionReply reply = new DatasetDeletionReply();
            bus.reply(msg, reply);
            return;
        }

        SQL.New(DatasetVO.class)
                .eq(DatasetVO_.uuid, msg.getUuid())
                .hardDelete();

        DatasetDeletionReply reply = new DatasetDeletionReply();
        bus.reply(msg, reply);
    }

    private void handle(ModelDeletionMsg msg) {
        if (!Q.New(ModelVO.class)
                .eq(ModelVO_.uuid, msg.getUuid())
                .isExists()) {
            logger.warn(String.format("model[uuid: %s] not found, skip deletion", msg.getUuid()));
            ModelDeletionReply reply = new ModelDeletionReply();
            bus.reply(msg, reply);
            return;
        }

        SQL.New(ModelVO.class)
                .eq(ModelVO_.uuid, msg.getUuid())
                .hardDelete();

        ModelDeletionReply reply = new ModelDeletionReply();
        bus.reply(msg, reply);
    }

    private void handle(ModelServiceDeletionMsg msg) {
        if (!Q.New(ModelServiceVO.class)
                .eq(ModelServiceVO_.uuid, msg.getUuid())
                .isExists()) {
            logger.warn(String.format("model service[uuid: %s] not found, skip deletion", msg.getUuid()));
            ModelServiceDeletionReply reply = new ModelServiceDeletionReply();
            bus.reply(msg, reply);
            return;
        }

        new SQLBatch() {
            @Override
            protected void scripts() {
                sql(ModelServiceRefVO.class)
                        .eq(ModelServiceRefVO_.modelServiceUuid, msg.getUuid())
                        .hardDelete();
                sql(ModelServiceVO.class)
                        .eq(ModelServiceVO_.uuid, msg.getUuid())
                        .hardDelete();
            }
        }.execute();

        ModelServiceDeletionReply reply = new ModelServiceDeletionReply();
        bus.reply(msg, reply);
    }

    private void handle(AllocateModelServiceMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                String namePrefix = msg.getModelServiceGroupName();
                // format name like "ComfyUI-1_241216_wlsmfn" Refer to ZSTAC-71938
                long index = Q.New(ModelServiceInstanceGroupVO.class).eq(ModelServiceInstanceGroupVO_.modelServiceUuid, msg.getUuid()).count();
                String name = String.format("%s-%s_%s_%s", namePrefix, index, TimeUtils.getShortDateFormat(), RandomStringUtils.randomAlphanumeric(6));
                AllocateModelServiceReply reply = new AllocateModelServiceReply();
                reply.setName(name);
                bus.reply(msg, reply);
                chain.next();
            }

            @Override
            public String getSyncSignature() {
                return "allocate-model-service-" + msg.getUuid();
            }

            @Override
            public String getName() {
                return "allocate-model-service-" + msg.getUuid();
            }
        });
    }

    private void handle(DeleteInstanceNginxRuleMsg msg) {
        MessageReply reply = new MessageReply();
        ModelCenterVO mc = Q.New(ModelCenterVO.class)
                .eq(ModelCenterVO_.uuid, msg.getModelCenterUuid())
                .find();
        if (mc == null) {
            logger.warn(String.format("model center[uuid: %s] not found", msg.getModelCenterUuid()));
            reply.setError(operr("model center[uuid: %s] not found", msg.getModelCenterUuid()));
            bus.reply(msg, reply);
            return;
        }

        BentoBasedModelStorageBackend backend = new BentoBasedModelStorageBackend(mc);
        backend.deleteNginxRule(msg.getUuid(), msg.getInternalUrl(), new Completion(msg) {
            @Override
            public void success() {
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(ModelCenterPingMsg msg) {
        ModelCenterPingReply reply = new ModelCenterPingReply();
        ModelCenterVO modelCenter = Q.New(ModelCenterVO.class)
                .eq(ModelCenterVO_.uuid, msg.getUuid())
                .find();
        ModelStorageBackend backend = new BentoBasedModelStorageBackend(modelCenter);
        backend.ping(new Completion(msg) {
            @Override
            public void success() {
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(SyncAINginxConfigurationMsg msg) {
        SyncAINginxConfigurationReply reply = new SyncAINginxConfigurationReply();
        List<ModelCenterVO> modelCenterVOs = Q.New(ModelCenterVO.class)
                .list();
        ModelStorageBackend backend = new BentoBasedModelStorageBackend(modelCenterVOs.get(0));
        List<NginxRedirectRule> nginxConfigs = msg.getNginxConfigRules();

        if (msg.getReloadMNNginx()) {
            try {
                setupNginxConfig();
            } catch (Exception e) {
                reply.setError(operr("Failed to reload nginx configuration: %s", e.getMessage()));
                bus.reply(msg, reply);
                return;
            }
        }

        // if no nginx config is specified in msg, then return
        if (nginxConfigs == null || nginxConfigs.isEmpty()) {
            bus.reply(msg, reply);
            return;
        }

        Boolean dryRun = true;
        if(msg.getDryRun() != null) {
            dryRun = msg.getDryRun();
        }

        Integer endOfList = nginxConfigs.size() / 100 + 1;
        List<Integer> steps = IntStream.range(0, endOfList).boxed().collect(Collectors.toList());

        Boolean finalDryRun = dryRun;
        List<NginxRedirectRule> unSyncedRules = new ArrayList<>();
        List<NginxRedirectRule> finalNginxConfigs = nginxConfigs;
        new While<>(steps).each((step, whileCompletion) -> {
            Integer endPos = (step + 1) * 100 <= finalNginxConfigs.size() ? (step + 1) * 100 : finalNginxConfigs.size();;
            Integer startPos = step * 100;
            List<NginxRedirectRule>  nginxConfigsToSync = finalNginxConfigs.subList(startPos, endPos);
            backend.syncNginxRules(nginxConfigsToSync, finalDryRun, new ReturnValueCompletion<List<NginxRedirectRule>>(msg) {
                @Override
                public void success(List<NginxRedirectRule> returnValue) {
                    unSyncedRules.addAll(returnValue);
                    whileCompletion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    whileCompletion.addError(errorCode);
                    whileCompletion.allDone();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if(!errorCodeList.getCauses().isEmpty()) {
                    reply.setError(errorCodeList.getCauses().get(0));
                }
                reply.setUnSyncedRules(unSyncedRules);
                bus.reply(msg, reply);
            }
        });
    }

    private void addModelService(AddModelServiceMsg addModelServiceMsg, ReturnValueCompletion<ModelServiceVO> completion) {
        APIAddModelServiceMsg msg = addModelServiceMsg.getApiMessage();
        ModelCenterVO center = dbf.findByUuid(msg.getModelCenterUuid(), ModelCenterVO.class);
        if (msg.getResourceUuid() == null) {
            msg.setResourceUuid(Platform.getUuid());
        }

        ModelServiceVO modelServiceVO = new ModelServiceVO();
        modelServiceVO.setUuid(msg.getResourceUuid());
        modelServiceVO.setDescription(msg.getDescription());
        modelServiceVO.setName(msg.getName());
        modelServiceVO.setYaml(msg.getYaml());
        modelServiceVO.setRequestCpu(msg.getRequestCpu());
        modelServiceVO.setRequestMemory(msg.getRequestMemory());
        modelServiceVO.setAccountUuid(msg.getSession().getAccountUuid());
        modelServiceVO.setSource(ModelServiceSource.fromString(msg.getSource()));
        if (msg.getFramework() != null) {
            modelServiceVO.setFramework(msg.getFramework());
        }
        modelServiceVO.setCondaVersion(msg.getCondaVersion());
        modelServiceVO.setDockerImage(msg.getDockerImage());
        modelServiceVO.setVmImageUuid(msg.getVmImageUuid());
        modelServiceVO.setStartCommand(msg.getStartCommand());
        modelServiceVO.setPythonVersion(msg.getPythonVersion());
        modelServiceVO.setSystem(false);
        if (modelServiceVO.getGpuComputeCapability() != null) {
            modelServiceVO.setGpuComputeCapability(GpuComputeCapability.fromComputeCapability(msg.getGpuComputeCapability()));
        }
        if (msg.isSystem() != null) {
            modelServiceVO.setSystem(msg.isSystem());
        }

        modelServiceVO.setModelCenterUuid(msg.getModelCenterUuid());
        modelServiceVO.setType(ModelServiceType.valueOf(msg.getType()));

        BentoBasedModelStorageBackend backend = new BentoBasedModelStorageBackend(center);
        backend.addModelService(msg, new ReturnValueCompletion<BentoBasedModelStorageBackend.UploadModelServiceResponse>(addModelServiceMsg) {
            @Override
            public void success(BentoBasedModelStorageBackend.UploadModelServiceResponse returnValue) {
                modelServiceVO.setInstallPath(returnValue.installPath);
                if (ModelServiceSource.HuggingFace.toString().equals(msg.getSource())) {
                    modelServiceVO.setDockerImage(returnValue.dockerImage);
                }
                modelServiceVO.setReadme(returnValue.readme);
                modelServiceVO.setSize(returnValue.size);

                dbf.persist(modelServiceVO);

                tagMgr.createTagsFromAPICreateMessage(msg, modelServiceVO.getUuid(), ModelServiceVO.class.getSimpleName());
                completion.success(modelServiceVO);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    class ModelServiceBundle {
        ModelServiceVO modelService;
    }

    private void handle(AddModelServiceMsg addModelServiceMsg) {
        AddModelServiceReply reply = new AddModelServiceReply();

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        ModelServiceBundle bundle = new ModelServiceBundle();
        chain.setName("add-model-service-flows");
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                addModelService(addModelServiceMsg, new ReturnValueCompletion<ModelServiceVO>(trigger) {
                    @Override
                    public void success(ModelServiceVO modelService) {
                        bundle.modelService = modelService;
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "bind-service-with-models-by-model-uuid-list-param";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                APIAddModelServiceMsg msg = addModelServiceMsg.getApiMessage();
                if (CollectionUtils.isEmpty(msg.getModelUuids())) {
                    trigger.next();
                    return;
                }

                List<ModelServiceRefVO> modelServiceRefVOS = new ArrayList<>();
                msg.getModelUuids().forEach(modelUuid -> {
                    if (Q.New(ModelServiceRefVO.class)
                            .eq(ModelServiceRefVO_.modelServiceUuid, bundle.modelService.getUuid())
                            .eq(ModelServiceRefVO_.modelUuid, modelUuid).isExists()) {
                        return;
                    }

                    ModelServiceRefVO modelServiceRefVO = new ModelServiceRefVO();
                    modelServiceRefVO.setUuid(Platform.getUuid());
                    modelServiceRefVO.setModelServiceUuid(bundle.modelService.getUuid());
                    modelServiceRefVO.setModelUuid(modelUuid);
                    modelServiceRefVOS.add(modelServiceRefVO);
                });
                dbf.persistCollection(modelServiceRefVOS);
                trigger.next();
            }
        }).done(new FlowDoneHandler(addModelServiceMsg) {
            @Override
            public void handle(Map data) {
                ModelServiceVO refreshed = dbf.reload(bundle.modelService);
                reply.setInventory(refreshed.toInventory());
                bus.reply(addModelServiceMsg, reply);
            }
        }).error(new FlowErrorHandler(addModelServiceMsg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(addModelServiceMsg, reply);
            }
        }).start();
    }

    private void saveTrainedModelRecord(String modelUuid, String installPath, String accountUuid) {
        String exportTrainedModelPath = "/root/bentoml/fine_tune_save/";
        if (!installPath.contains(exportTrainedModelPath)) {
            return;
        }

        // installPath is like "/root/bentoml/fine_tune_save/{UUID}/train_{UUID}_{current_time}"
        String uuidWithPosix = installPath.split("/root/bentoml/fine_tune_save/")[1];
        String uuid = uuidWithPosix.split("/train_")[0];

        // uuid is null or "" just return
        // uuid is not zstack uuid format just return
        if (StringUtils.isBlank(uuid) || uuid.length() != 32) {
            return;
        }

        ModelServiceInstanceGroupVO groupVO = Q.New(ModelServiceInstanceGroupVO.class)
                .eq(ModelServiceInstanceGroupVO_.uuid, uuid)
                .find();

        if (groupVO == null) {
            return;
        }

        String sourceModelUuid = groupVO.getModelUuid();

        List<ModelServiceGroupDatasetRefVO> modelServiceGroupDatasetRefVOS = Q.New(ModelServiceGroupDatasetRefVO.class)
                .eq(ModelServiceGroupDatasetRefVO_.modelServiceInstanceGroupUuid, uuid)
                .list();

        List<TrainedModelRecordVO> recordVOS = new ArrayList<>();
        modelServiceGroupDatasetRefVOS.forEach(modelServiceGroupDatasetRefVO -> {
            TrainedModelRecordVO trainedModelRecordVO = new TrainedModelRecordVO();
            trainedModelRecordVO.setUuid(Platform.getUuid());
            trainedModelRecordVO.setAccountUuid(accountUuid);
            trainedModelRecordVO.setModelUuid(modelUuid);
            trainedModelRecordVO.setSourceModelUuid(sourceModelUuid);
            trainedModelRecordVO.setModelServiceInstanceGroupUuid(uuid);
            trainedModelRecordVO.setDatasetUuid(modelServiceGroupDatasetRefVO.getDatasetUuid());
            recordVOS.add(trainedModelRecordVO);
        });

        // recordVOS is empty, which mean no dataset selected. just record other info
        if (recordVOS.isEmpty()) {
            TrainedModelRecordVO trainedModelRecordVO = new TrainedModelRecordVO();
            trainedModelRecordVO.setUuid(Platform.getUuid());
            trainedModelRecordVO.setAccountUuid(accountUuid);
            trainedModelRecordVO.setModelUuid(modelUuid);
            trainedModelRecordVO.setSourceModelUuid(sourceModelUuid);
            trainedModelRecordVO.setModelServiceInstanceGroupUuid(uuid);
            dbf.persist(trainedModelRecordVO);
            return;
        }
        dbf.persistCollection(recordVOS);
    }

    private void doAddModel(AddModelMsg addModelMsg, ReturnValueCompletion<ModelBundle> completion) {
        ModelBundle bundle = new ModelBundle();
        APIAddModelMsg msg = addModelMsg.getApiMessage();
        ModelCenterVO center = dbf.findByUuid(msg.getModelCenterUuid(), ModelCenterVO.class);
        if (msg.getResourceUuid() == null) {
            msg.setResourceUuid(Platform.getUuid());
        }

        String originInstall = msg.getInstallPath();

        BentoBasedModelStorageBackend backend = new BentoBasedModelStorageBackend(center);
        backend.addModel(msg, new ReturnValueCompletion<BentoBasedModelStorageBackend.UploadModelResponse>(addModelMsg) {
            @Override
            public void success(BentoBasedModelStorageBackend.UploadModelResponse rsp) {
                ModelVO modelVO = new ModelVO();
                modelVO.setUuid(msg.getResourceUuid());
                modelVO.setDescription(msg.getDescription());
                modelVO.setIntroduction(rsp.introduction);
                modelVO.setName(msg.getName());
                ModelCenterVO modelCenterVO = Q.New(ModelCenterVO.class)
                        .eq(ModelCenterVO_.uuid, msg.getModelCenterUuid())
                        .find();
                modelVO.setModelCenterUuid(modelCenterVO.getUuid());
                modelVO.setParameters(msg.getParameters());
                modelVO.setInstallPath(rsp.installPath);
                modelVO.setLogo(msg.getLogo());
                modelVO.setType(ModelType.Custom);
                modelVO.setModelId(rsp.modelId);

                if (rsp.size != null) {
                    modelVO.setSize((long) SizeUtils.sizeStringToBytes2(rsp.size.replace(" ", "")));
                } else {
                    modelVO.setSize(0L);
                }

                modelVO.setVendor(msg.getVendor());
                modelVO.setVersion(rsp.tag);
                modelVO.setAccountUuid(msg.getSession().getAccountUuid());

                dbf.persist(modelVO);

                saveTrainedModelRecord(modelVO.getUuid(), originInstall, msg.getSession().getAccountUuid());

                bundle.model = modelVO;
                bundle.pipelineTag = rsp.pipeline_tag;
                completion.success(bundle);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    class ModelBundle {
        private ModelVO model;
        private String pipelineTag;

        private void pourTo(ModelBundle to) {
            to.model = this.model;
            to.pipelineTag = this.pipelineTag;
        }
    }

    private void handle(AddModelMsg addModelMsg) {
        AddModelReply reply = new AddModelReply();

        ModelBundle bundle = new ModelBundle();
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("add-model-inner-flows");
        chain.then(new NoRollbackFlow() {
            String __name__ = "add-model-to-storage-by-ai-model-center-agent";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                doAddModel(addModelMsg, new ReturnValueCompletion<ModelBundle>(trigger) {
                    @Override
                    public void success(ModelBundle returnValue) {
                        returnValue.pourTo(bundle);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "auto-bind-model-to-service-by-pipeline-tag";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                APIAddModelMsg msg = addModelMsg.getApiMessage();
                tagMgr.createTagsFromAPICreateMessage(msg, bundle.model.getUuid(), ModelVO.class.getSimpleName());
                List<ModelServiceRefVO> modelServiceRefVOS = new ArrayList<>();
                List<String> modelServiceUuidList = Q.New(UserTagVO.class)
                        .select(UserTagVO_.resourceUuid)
                        .eq(UserTagVO_.tag, "AI::" + bundle.pipelineTag)
                        .eq(UserTagVO_.resourceType, ModelServiceVO.class.getSimpleName())
                        .listValues();
                logger.debug(String.format("get same tag model service uuid list %s",modelServiceUuidList.toString()));
                modelServiceUuidList.forEach(modelServiceUuid -> {
                    ModelServiceRefVO modelServiceRefVO = new ModelServiceRefVO();
                    modelServiceRefVO.setUuid(Platform.getUuid());
                    modelServiceRefVO.setModelServiceUuid(modelServiceUuid);
                    modelServiceRefVO.setModelUuid(bundle.model.getUuid());
                    modelServiceRefVOS.add(modelServiceRefVO);
                });
                dbf.persistCollection(modelServiceRefVOS);
                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "bind-model-to-service-by-model-service-uuid-list-param";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                APIAddModelMsg msg = addModelMsg.getApiMessage();
                if (CollectionUtils.isEmpty(msg.getModelServiceUuids())) {
                    trigger.next();
                    return;
                }

                List<ModelServiceRefVO> modelServiceRefVOS = new ArrayList<>();
                msg.getModelServiceUuids().forEach(modelServiceUuid -> {
                    if (Q.New(ModelServiceRefVO.class)
                            .eq(ModelServiceRefVO_.modelServiceUuid, modelServiceUuid)
                            .eq(ModelServiceRefVO_.modelUuid, bundle.model.getUuid()).isExists()) {
                        return;
                    }

                    ModelServiceRefVO modelServiceRefVO = new ModelServiceRefVO();
                    modelServiceRefVO.setUuid(Platform.getUuid());
                    modelServiceRefVO.setModelServiceUuid(modelServiceUuid);
                    modelServiceRefVO.setModelUuid(bundle.model.getUuid());
                    modelServiceRefVOS.add(modelServiceRefVO);
                });
                dbf.persistCollection(modelServiceRefVOS);
                trigger.next();
            }
        }).done(new FlowDoneHandler(addModelMsg) {
            @Override
            public void handle(Map data) {
                ModelVO refreshed = dbf.reload(bundle.model);
                reply.setInventory(refreshed.toInventory());
                bus.reply(addModelMsg, reply);
            }
        }).error(new FlowErrorHandler(addModelMsg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(addModelMsg, reply);
            }
        }).start();
    }

    public static class ModelInstanceBundle {
        private String instanceUuid;
        private String internalUrl;

        public String getInstanceUuid() {
            return instanceUuid;
        }

        public void setInstanceUuid(String instanceUuid) {
            this.instanceUuid = instanceUuid;
        }

        public String getInternalUrl() {
            return internalUrl;
        }

        public void setInternalUrl(String internalUrl) {
            this.internalUrl = internalUrl;
        }
    }

    private void handle(DeleteModelServiceInstanceGroupMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                doDeleteModelServiceInstanceGroup(msg);
                chain.next();
            }

            @Override
            public String getSyncSignature() {
                return "delete-model-service-instance-group-" + msg.getUuid();
            }

            @Override
            public String getName() {
                return "delete-model-service-instance-group-" + msg.getUuid();
            }
        });
    }

    private void doDeleteModelServiceInstanceGroup(DeleteModelServiceInstanceGroupMsg msg) {
        DeleteModelServiceInstanceGroupReply reply = new DeleteModelServiceInstanceGroupReply();
        final ModelServiceInstanceGroupVO groupVO = dbf.findByUuid(msg.getUuid(), ModelServiceInstanceGroupVO.class);

        if (groupVO == null) {
            logger.debug(String.format("replay success that the model service group[uuid: %s] maybe already deleted", msg.getUuid()));
            bus.reply(msg, reply);
            deleteScrapeConfig(msg.getUuid());
            return;
        }

        if (CollectionUtils.isEmpty(groupVO.getInstances())) {
            dbf.remove(groupVO);
            bus.reply(msg, reply);
            deleteScrapeConfig(groupVO.getUuid());
            return;
        }

        String modelCenterUuid = Q.New(ModelVO.class)
                .select(ModelVO_.modelCenterUuid)
                .eq(ModelVO_.uuid, groupVO.getModelUuid())
                .findValue();
        ModelCenterVO modelCenterVO;
        if (modelCenterUuid == null) {
            modelCenterVO = Q.New(ModelCenterVO.class)
                    .limit(1)
                    .find();
        } else {
            modelCenterVO = Q.New(ModelCenterVO.class)
                    .eq(ModelCenterVO_.uuid, modelCenterUuid)
                    .find();
        }

        ModelServiceFactory factory = getFactoryByModelServiceType(ModelServiceType.valueOf(groupVO.getModelServiceType()));

        BentoBasedModelStorageBackend modelStorageBackend = new BentoBasedModelStorageBackend(modelCenterVO);
        List<ModelInstanceBundle> instanceBundles = new ArrayList<>();
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("delete-model-service-instance-group-flows");
        chain.then(new NoRollbackFlow() {
            String __name__ = "delete-instances";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                deleteGroupInstances(groupVO, new ReturnValueCompletion<List<ModelInstanceBundle>>(trigger) {
                    @Override
                    public void success(List<ModelInstanceBundle> returnValue) {
                        instanceBundles.addAll(returnValue);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        chain.then(new NoRollbackFlow() {
            String __name__ = "delete-nginx-rule-configs";

            @Override
            public boolean skip(Map data) {
                return CollectionUtils.isEmpty(instanceBundles);
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<DeleteInstanceNginxRuleMsg> msgs = Collections.synchronizedList(new ArrayList<>());
                instanceBundles.forEach(bundle -> {
                    DeleteInstanceNginxRuleMsg deleteInstanceNginxRuleMsg = new DeleteInstanceNginxRuleMsg();
                    deleteInstanceNginxRuleMsg.setUuid(bundle.getInstanceUuid());
                    deleteInstanceNginxRuleMsg.setInternalUrl(bundle.getInternalUrl());
                    deleteInstanceNginxRuleMsg.setModelCenterUuid(modelCenterVO.getUuid());
                    bus.makeTargetServiceIdByResourceUuid(deleteInstanceNginxRuleMsg, SERVICE_ID, bundle.getInstanceUuid());
                    msgs.add(deleteInstanceNginxRuleMsg);
                });

                new While<>(instanceBundles).each((bundle, whileCompletion) -> {
                    DeleteInstanceNginxRuleMsg deleteInstanceNginxRuleMsg = msgs.get(instanceBundles.indexOf(bundle));
                    bus.send(deleteInstanceNginxRuleMsg, new CloudBusCallBack(whileCompletion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.warn(String.format("failed to delete nginx rule for instance[uuid: %s], %s", bundle.getInstanceUuid(), reply.getError()));
                            }
                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList.getCauses().get(0));
                            return;
                        }
                        trigger.next();
                    }
                });
            }
        });

        if (factory != null) {
            Flow f = factory.createAfterServiceDeletedFlow(groupVO);
            if (f != null) {
                chain.then(f);
            }
        }

        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(groupVO.getUuid(), ModelServiceInstanceGroupVO.class);

                if (reloaded != null && !CollectionUtils.isEmpty(reloaded.getInstances())) {
                    dbf.removeByPrimaryKeys(reloaded
                            .getInstances()
                            .stream()
                            .map(ModelServiceInstanceVO::getUuid)
                            .collect(Collectors.toList()),  ModelServiceInstanceVO.class);
                }

                if (reloaded != null) {
                    dbf.removeByPrimaryKey(reloaded.getUuid(), ModelServiceInstanceGroupVO.class);
                }

                deleteScrapeConfig(groupVO.getUuid());
                bus.reply(msg, reply);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(msg, reply);
            }
        }).start();
    }

    protected void deleteGroupInstances(ModelServiceInstanceGroupVO groupVO, ReturnValueCompletion<List<ModelInstanceBundle>> completion) {
        List<ModelInstanceBundle> instanceBundles = Collections.synchronizedList(new ArrayList<>());
        new While<>(groupVO.getInstances()).each((instance, whileCompletion) -> {
            // delete instances
            ModelServiceBackend backend;
            if (ModelServiceBackendType.VirtualMachine.toString().equals(groupVO.getType())) {
                backend = backends.get(ModelServiceBackendType.VirtualMachine);
            } else {
                backend = backends.get(ModelServiceBackendType.Container);
            }
            backend.deleteModelServiceInstance(instance, new Completion(whileCompletion) {
                @Override
                public void success() {
                    dbf.removeByPrimaryKey(instance.getUuid(), ModelServiceInstanceVO.class);
                    ModelInstanceBundle bundle = new ModelInstanceBundle();
                    bundle.setInstanceUuid(instance.getUuid());
                    bundle.setInternalUrl(instance.getInternalUrl());
                    instanceBundles.add(bundle);
                    whileCompletion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    whileCompletion.addError(errorCode);
                    whileCompletion.allDone();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    completion.fail(errorCodeList);
                    return;
                }
                completion.success(instanceBundles);
            }
        });
    }

    private List getDatasetsFromUuids(List<String> datasetUuids){
        if (datasetUuids == null || datasetUuids.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        List<DatasetVO> datasetVOS = Q.New(DatasetVO.class)
                .in(DatasetVO_.uuid, datasetUuids)
                .list();
        if (datasetVOS == null || datasetUuids.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        return datasetVOS;
    }

    private String modelServiceGroupSyncId(String uuid) {
        return String.format("model-service-group-%s", uuid);
    }

    private void handle(DeployModelServiceMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                DeployModelServiceReply reply = new DeployModelServiceReply();
                doDeployModelServices(msg, new Completion(msg, chain) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getSyncSignature() {
                return modelServiceGroupSyncId(msg.getUuid());
            }

            @Override
            public String getName() {
                return modelServiceGroupSyncId(msg.getUuid());
            }
        });
    }

    private void checkService(String url, Completion completion) {
        logger.info(String.format("check service %s", url));
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "*/*");
        // 200 means health just return success
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setAll(headers);
        HttpEntity<String> req = new HttpEntity<String>(null, requestHeaders);
        Integer checkInterval = AIGlobalConfig.SERVICE_STARTUP_CHECK_INTERVAL_SECONDS.value(Integer.class);
        try {
            ResponseEntity<String> rsp = restf.syncRawJson(url, req, HttpMethod.GET, TimeUnit.SECONDS, checkInterval);
        } catch(Exception e) {
            completion.fail(operr("failed to get ready status, because %s", e.toString()));
            return;
        }
        completion.success();
    }

    public void isAlive(ModelServiceInstanceInventory instance, String redirectIP, Completion completion) {

        String livePath = instance.getLivePath();
        String url = String.format("http://%s:%s/%s%s",
                redirectIP, String.valueOf(AIGlobalConfig.AGENT_MODEL_SERVICE_NGINX_LISTEN_PORT.value(Integer.class)),
                instance.getUuid(), livePath);

        if (CoreGlobalProperty.UNIT_TEST_ON) {
            if (redirectIP == null) {
                completion.fail(operr("no redirect ip, return failure for ut test"));
                return;
            }

            url = String.format("http://127.0.0.1:8989%s%s",
                    AIConstants.UT_MOCK_PATH_PREFIX, livePath);
        }
        checkService(url, completion);
    }

    private AIConfigYaml buildModelServiceYaml(
            ModelServiceInventory service,
            ModelInventory model,
            ModelService modelServiceFromUserInput,
            String groupUuid,
            String modelStorageUrl) {
        String serviceYaml = service.getYaml();
        if (logger.isTraceEnabled()) {
            logger.trace(String.format("before model deployment, list model service yaml: %s", service.getYaml()));
        }

        AIConfigYaml configYaml = AIConfigYaml.fromYaml(serviceYaml);
        if (configYaml == null) {
            throw new OperationFailureException(operr("unexpected content: %s service's yaml parsed as null", serviceYaml));
        }

        AIConfigYaml.Service aiService;
        if (configYaml.getServices().isEmpty()) {
            aiService = new AIConfigYaml.Service();
            configYaml.setServices(Collections.singletonList(aiService));
        } else {
            aiService = configYaml.getServices().get(0);
        }

        aiService.setInstallPath(service.getInstallPath());
        aiService.setStartCommand(service.getStartCommand());

        if (configYaml.getEnvironmentParameters() != null) {
            configYaml.getEnvironmentParameters().forEach(
                    (key, value) -> {
                        AIModelServiceEnvironments env = AIModelServiceEnvironments.fromString(key);
                        if (env != null) {
                            env.applyToService(aiService, value);
                        }
                    }
            );
        }

        // override config yaml with user input
        List<DatasetVO> datasetVOS = getDatasetsFromUuids(modelServiceFromUserInput.getDatasetUuids());
        if (!datasetVOS.isEmpty()) {
            datasetVOS.forEach(datasetVO -> {
                configYaml.addDataset(datasetVO.getName(), datasetVO.getInstallPath());
            });
        }

        if (modelServiceFromUserInput.getEnvironmentVariables() != null) {
            for (AIModelServiceEnvironments envVar : AIModelServiceEnvironments.values()) {
                String envVarValue = modelServiceFromUserInput.getEnvironmentVariables().get(envVar.toString());
                if (envVarValue != null) {
                    envVar.applyToService(aiService, envVarValue);
                }
            }

            modelServiceFromUserInput.getEnvironmentVariables().forEach(
                    configYaml::addEnvironmentParameter
            );
        }

        if (modelServiceFromUserInput.getStartupParameters() != null) {
            modelServiceFromUserInput.getStartupParameters().forEach(
                    configYaml::addStartupParameter
            );
        }

        if (modelServiceFromUserInput.getPort() != null) {
            aiService.setPorts(Collections.singletonList(Integer.valueOf(modelServiceFromUserInput.getPort())));
        }

        if (modelServiceFromUserInput.getServiceBootUptime() != null) {
            aiService.setServiceBootupTime(modelServiceFromUserInput.getServiceBootUptime());
        }

        if (service.getFramework() != null) {
            configYaml.setFramework(service.getFramework());
        }

        if (model != null) {
            if (!StringUtils.isEmpty(model.getName())) {
                configYaml.addModel(model.getName(), model.getInstallPath());
            } else {
                logger.trace("model name is empty, skip add model to yaml");
            }
        }


        String instanceUuid = Platform.getUuid();
        configYaml.setUuid(groupUuid);
        configYaml.setInstanceUuid(instanceUuid);
        configYaml.addMountPath(modelStorageUrl);

        List<String> modelTags = AISystemTags.AI_MODEL_CLASSIFICATION.getTags(groupUuid);
        configYaml.setModelTags(modelTags);
        service.setYaml(configYaml.toYaml());

        if (logger.isTraceEnabled()) {
            logger.trace(String.format("after buildModelServiceYaml, list model service yaml: %s", service.getYaml()));
        }

        return configYaml;
    }

    private void doDeployModelService(ModelService modelService, DeployContext context, String groupUuid, Completion completion) {
        ModelServiceBackend backend = backends
                .get(ModelServiceBackendType.valueOf(modelService.getType()));
        ModelServiceInstanceGroupVO groupVO = dbf.findByUuid(groupUuid, ModelServiceInstanceGroupVO.class);

        if (groupVO == null) {
            completion.fail(operr("model service instance group[uuid: %s] not found", groupUuid));
            return;
        }

        if (groupVO.getModelServiceUuid() == null) {
            completion.fail(operr("model service instance group[uuid: %s] model service uuid is null", groupUuid));
            return;
        }

        ModelServiceVO serviceVO = dbf.findByUuid(groupVO.getModelServiceUuid(), ModelServiceVO.class);
        ModelServiceInventory service = new ModelServiceInventory(serviceVO);

        ModelInventory model = new ModelInventory();
        String modelCenterUuid;
        if (StringUtils.isEmpty(modelService.getModelUuid())) {
            modelCenterUuid = service.getModelCenterUuid();
            logger.debug(String.format("model uuid is empty, use model service's model center uuid %s", modelCenterUuid));
        } else {
            ModelVO modelVO = dbf.findByUuid(groupVO.getModelUuid(), ModelVO.class);
            model = new ModelInventory(modelVO);
            modelCenterUuid = model.getModelCenterUuid();
            logger.debug(String.format("model uuid is not empty, use model's model center uuid %s", modelCenterUuid));
        }
        ModelCenterVO modelCenter = dbf.findByUuid(modelCenterUuid, ModelCenterVO.class);
        if (modelCenter == null) {
            completion.fail(operr("model center %s not found, check model and model service's model center uuid", modelCenterUuid));
            return;
        }
        BentoBasedModelStorageBackend modelStorageBackend = new BentoBasedModelStorageBackend(modelCenter);

        // build yaml and update service's yaml field for the usage during deployment
        AIConfigYaml configYaml = buildModelServiceYaml(
                service,
                model,
                modelService,
                groupUuid,
                modelCenter.getUrl()
        );

        String modelServiceInstanceToken = "modelServiceInstanceToken";

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("deploy model service");
        chain.then(new NoRollbackFlow() {
            String __name__ = "allocate-service-instance-name";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                AllocateModelServiceMsg amsg = new AllocateModelServiceMsg();
                amsg.setUuid(modelService.getUuid());
                amsg.setModelServiceGroupName(groupVO.getName());
                bus.makeTargetServiceIdByResourceUuid(amsg, AIModelManager.SERVICE_ID, modelService.getUuid());
                bus.send(amsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        AllocateModelServiceReply areply = reply.castReply();
                        logger.debug(String.format("allocate model service instance name: %s", areply.getName()));
                        modelService.setInstanceName(areply.getName());
                        trigger.next();
                    }
                });
            }
        }).then(new Flow() {
            String __name__ = "create-resource-to-run-service";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                final ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(groupVO.getUuid(), ModelServiceInstanceGroupVO.class);
                if (reloaded == null) {
                    trigger.fail(operr("model service instance group[uuid: %s] not found," +
                            " skip create-resource-to-run-service flow", groupVO.getUuid()));
                    return;
                }

                reloaded.setStatus(ModelServiceInstanceGroupStatus.Starting);
                dbf.update(reloaded);
                backend.deployModelService(modelService, groupVO.getUuid(), modelCenter, service, new ReturnValueCompletion<String>(trigger) {
                    @Override
                    public void success(String modelServiceInstanceUuid) {
                        if (!dbf.isExist(groupVO.getUuid(), ModelServiceInstanceGroupVO.class)) {
                            trigger.fail(operr("deploy model service reply success, but model service instance group[uuid: %s] not found", groupVO.getUuid()));
                            return;
                        }

                        if (modelServiceInstanceUuid == null) {
                            trigger.fail(operr("deploy model service reply success, but model service instance uuid is null"));
                            return;
                        }

                        ModelServiceInstanceVO modelServiceInstance = Q.New(ModelServiceInstanceVO.class)
                                .eq(ModelServiceInstanceVO_.uuid, modelServiceInstanceUuid)
                                .find();
                        ModelServiceInstanceInventory modelServiceInstanceInventory = new ModelServiceInstanceInventory(modelServiceInstance);
                        Map<String, String> urlMap = new HashMap<>();
                        urlMap.put("url", modelServiceInstance.getUrl());
                        urlMap.put("internalUrl", modelServiceInstance.getInternalUrl());
                        urlMap.put("jupyterUrl", modelServiceInstanceInventory.getJupyterUrl());
                        // set oneapi url for fastgpt app
                        if (service.getUuid().equals("c6aab22271us44aqt44ih692fc5825cb")) {
                            String ip = modelServiceInstanceInventory.getUrl().split(":")[0];
                            String opeApiUrl = String.format("%s:3001", ip);
                            urlMap.put("oneApiUrl", opeApiUrl);
                        }
                        modelServiceInstanceInventory.setUrlMaps(urlMap);
                        modelServiceInstance.setUrlMaps(urlMap.toString());
                        // update url maps
                        dbf.updateAndRefresh(modelServiceInstance);
                        data.put(modelServiceInstanceToken, modelServiceInstanceInventory);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                DeleteModelServiceInstanceGroupMsg msg = new DeleteModelServiceInstanceGroupMsg();
                msg.setUuid(groupVO.getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, AIModelManager.SERVICE_ID, msg.getUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.info(String.format("fail to delete model service instance, because of %s", reply.getError().toString()));
                        }
                        trigger.rollback();
                    }
                });
            }
        }).then(new Flow() {
            String __name__ = "setup-nginx-config-for-service";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                final ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(groupVO.getUuid(), ModelServiceInstanceGroupVO.class);
                if (reloaded == null) {
                    trigger.fail(operr("model service instance group[uuid: %s] not found," +
                            " skip setup-nginx-config-for-service flow", groupVO.getUuid()));
                    return;
                }

                reloaded.setStatus(ModelServiceInstanceGroupStatus.ConfigSettingUp);
                dbf.update(reloaded);
                ModelServiceInstanceInventory modelServiceInstance = (ModelServiceInstanceInventory) data.get(modelServiceInstanceToken);
                String jupyterUrl = modelServiceInstance.getJupyterUrl();
                String internalUrl = modelServiceInstance.getInternalUrl();
                modelStorageBackend.createNginxRule(modelServiceInstance.getUuid(), internalUrl, jupyterUrl, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                ModelServiceInstanceInventory modelServiceInstance = (ModelServiceInstanceInventory) data.get(modelServiceInstanceToken);
                String internalUrl = modelServiceInstance.getInternalUrl();
                modelStorageBackend.deleteNginxRule(modelServiceInstance.getUuid(), internalUrl, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        logger.debug(String.format("fail to delete nginx when rollback because of: %s", errorCode.toString()));
                        trigger.rollback();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            @Override
            public boolean skip(Map data) {
                return context.skipWaitForServiceReady;
            }

            String __name__ = "waiting-for-service-start-up";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                final ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(groupVO.getUuid(), ModelServiceInstanceGroupVO.class);
                if (reloaded == null) {
                    trigger.fail(operr("model service instance group[uuid: %s] not found," +
                            " skip waiting-for-service-start-up flow", groupVO.getUuid()));
                    return;
                }

                reloaded.setStatus(ModelServiceInstanceGroupStatus.ServiceBootingUp);
                dbf.update(reloaded);
                Integer serviceBootUptime;
                if (modelService.getServiceBootUptime() != null) {
                    serviceBootUptime = modelService.getServiceBootUptime();
                } else {
                    serviceBootUptime = configYaml.getServices().get(0).getServiceBootupTime();
                }

                if (serviceBootUptime == null || serviceBootUptime.equals(0)) {
                    Integer osStartUptime = AIGlobalConfig.OS_STARTUP_TIMES_IN_SECONDS.value(Integer.class);
                    Integer serviceStartUptime = AIGlobalConfig.SERVICE_STARTUP_TIMES_IN_SECONDS.value(Integer.class);
                    serviceBootUptime = osStartUptime + serviceStartUptime;
                }
                Integer serviceStartupCheckInterval = AIGlobalConfig.SERVICE_STARTUP_CHECK_INTERVAL_SECONDS.value(Integer.class);

                final Boolean[] readyInstance = {false};
                ErrorCode unexpectedError = null;
                long startTime = zTimer.getCurrentTimeMillis();
                String groupUuid = groupVO.getUuid();
                for (int i = 0; i < serviceBootUptime; i=i+serviceStartupCheckInterval) {
                    ModelServiceInstanceGroupVO groupVO = dbf.findByUuid(groupUuid, ModelServiceInstanceGroupVO.class);
                    if (groupVO == null) {
                        unexpectedError = operr("model service instance group[uuid: %s] not found," +
                                " break waiting-for-service-start-up aliveness check", groupUuid);
                        break;
                    }
                    ModelServiceInstanceInventory modelServiceInstance = (ModelServiceInstanceInventory) data.get(modelServiceInstanceToken);

                    // current only one instance per instance group
                    // multi instance must use new while to check, not foreach
                    // TODO: support multi instance
                    isAlive(modelServiceInstance, modelCenter.getManagementIp(), new Completion(trigger) {
                        @Override
                        public void success() {
                            logger.info("service instance is ready");
                            readyInstance[0] = true;
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            logger.info(String.format("service instance: %s is not ready, waiting for service ready", modelServiceInstance.getInternalUrl()));
                        }
                    });
                    if (readyInstance[0]) {
                        reloaded.setStatus(ModelServiceInstanceGroupStatus.Running);
                        dbf.update(reloaded);

                        SQL.New(ModelServiceInstanceVO.class)
                                .eq(ModelServiceInstanceVO_.modelServiceGroupUuid, reloaded.getUuid())
                                .set(ModelServiceInstanceVO_.status, ModelServiceInstanceGroupStatus.Running.toString())
                                .update();

                        trigger.next();
                        return;
                    }
                    try {
                        TimeUnit.SECONDS.sleep(serviceStartupCheckInterval);
                    } catch (InterruptedException ignored) {
                        Thread.currentThread().interrupt();
                    }
                    long endTime = zTimer.getCurrentTimeMillis();
                    if (endTime - startTime > serviceBootUptime * 1000) {
                        logger.warn(String.format("service instance is not ready in %s seconds," +
                                " please adjust service startup time", serviceBootUptime));
                        break;
                    }
                }

                reloaded.setStatus(ModelServiceInstanceGroupStatus.Unknown);
                dbf.update(reloaded);

                SQL.New(ModelServiceInstanceVO.class)
                        .eq(ModelServiceInstanceVO_.modelServiceGroupUuid, reloaded.getUuid())
                        .set(ModelServiceInstanceVO_.status, ModelServiceInstanceGroupStatus.Unknown.toString())
                        .update();

                if (AISystemTags.MODEL_SERVICE_MODE.hasTag(groupVO.getUuid())) {
                    logger.info(String.format("service: %s is not ready, but debug mode is set, do not fail this deployment", groupVO.getUuid()));
                    trigger.next();
                    return;
                }

                if (unexpectedError != null) {
                    logger.debug(String.format("service group[uuid: %s]," +
                            " maybe the group is deleted or rollback due to some reason," +
                            " mark deploy as failure. Because %s", groupVO.getUuid(), unexpectedError));
                    trigger.fail(unexpectedError);
                    return;
                }

                logger.debug(String.format("service is not ready in %s seconds," +
                        " please adjust service startup time", serviceBootUptime));
                trigger.next();
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    class ModelServiceUrlBundle {
        private String url;
        private String jupyterUrl;
        private String internalUrl;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getJupyterUrl() {
            return jupyterUrl;
        }

        public void setJupyterUrl(String jupyterUrl) {
            this.jupyterUrl = jupyterUrl;
        }

        public String getInternalUrl() {
            return internalUrl;
        }

        public void setInternalUrl(String internalUrl) {
            this.internalUrl = internalUrl;
        }
    }

    static class DeployContext {
        boolean skipWaitForServiceReady;
    }

    private void doDeployModelServices(DeployModelServiceMsg msg, Completion completion) {
        ModelServiceInstanceGroupVO groupVO = dbf.findByUuid(msg.getUuid(), ModelServiceInstanceGroupVO.class);
        if (groupVO == null) {
            completion.fail(operr("Model service instance group %s not found", msg.getUuid()));
            return;
        }

        List<ModelService> modelServices = msg.getModelServices().stream()
                .map(ms -> JSONObjectUtil.rehashObject(ms, ModelService.class))
                .sorted(Comparator.comparingInt(ModelService::getNodeRank))
                .collect(Collectors.toList());
        AtomicReference<String> initAddressFromFirstNode = new AtomicReference<>(null);
        List<ModelService> modelServicesCopy = new ArrayList<>(modelServices);

        ModelServiceUrlBundle bundle = new ModelServiceUrlBundle();

        DeployContext context = new DeployContext();
        if (modelServicesCopy.size() > 1) {
            logger.debug("more than one model service to deploy, skip waiting for service ready");
            context.skipWaitForServiceReady = true;
        }
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("deploy-model-services");
        chain.then(new NoRollbackFlow() {
            String __name__ = "deploy-initiator-node";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                doDeployModelService(modelServicesCopy.get(0), context, groupVO.getUuid(), new Completion(trigger) {
                    @Override
                    public void success() {
                        ModelServiceInstanceGroupVO reloaded = dbf.reload(groupVO);
                        reloaded.getInstances().forEach(instance -> {
                            AIConfigYaml configYaml = AIConfigYaml.fromYaml(instance.getYaml());
                            if (configYaml == null) {
                                trigger.fail(operr("unexpected content: %s service's yaml parsed as null", instance.getYaml()));
                                return;
                            }

                            if (configYaml.getInferenceParams() != null) {
                                initAddressFromFirstNode.set(configYaml.getInferenceParams().get("distributeInitAddress"));
                            }

                            ModelServiceInstanceInventory inventory = ModelServiceInstanceInventory.valueOf(instance);
                            bundle.setUrl(inventory.getUrl());
                            bundle.setJupyterUrl(inventory.getJupyterUrl());
                            bundle.setInternalUrl(inventory.getInternalUrl());
                            logger.debug(String.format("init address from first node: %s", initAddressFromFirstNode.get()));
                        });

                        modelServicesCopy.remove(0);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "deploy-remaining-nodes";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (modelServicesCopy.isEmpty()) {
                    logger.debug("no remaining nodes to deploy");
                    trigger.next();
                    return;
                }

                modelServicesCopy.forEach(ms -> ms.setInitialAddress(initAddressFromFirstNode.get()));

                new While<>(modelServicesCopy).step((ms, whileCompletion) -> {
                    doDeployModelService(ms, context, groupVO.getUuid(), new Completion(whileCompletion) {
                        @Override
                        public void success() {
                            whileCompletion.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            whileCompletion.addError(errorCode);
                            whileCompletion.done();
                        }
                    });
                }, 16).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                            return;
                        }

                        trigger.fail(errorCodeList.getCauses().get(0));
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "update-model-service-instance-urls";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                ModelServiceInstanceGroupVO reloaded = dbf.reload(groupVO);
                if (reloaded.getInstances().size() <= 1) {
                    logger.debug("no need to update model service instance urls");
                    trigger.next();
                    return;
                }

                // TODO: Fix url hardcode
                reloaded.getInstances().forEach(instance -> {
                    Map<String, String> urlMaps = new HashMap<>();
                    urlMaps.put("url", bundle.getUrl());
                    urlMaps.put("jupyterUrl", bundle.getJupyterUrl());
                    urlMaps.put("internalUrl", bundle.getInternalUrl());

                    instance.setUrl(bundle.getUrl());
                    instance.setInternalUrl(bundle.getInternalUrl());
                    instance.setUrlMaps(urlMaps.toString());
                });

                dbf.updateCollection(reloaded.getInstances());
                trigger.next();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                ModelServiceInstanceGroupVO reloaded = dbf.reload(groupVO);
                if (reloaded != null) {
                    reloaded.setStatus(ModelServiceInstanceGroupStatus.Unknown);
                    dbf.update(reloaded);
                } else {
                    logger.warn(String.format("model service instance group[uuid: %s] not found, skip FlowErrorHandler", groupVO.getUuid()));
                }

                completion.fail(errCode);
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                ModelServiceInstanceGroupVO reloaded = dbf.reload(groupVO);
                if (reloaded != null) {
                    createScrapeConfig(reloaded);
                    if (intermediateStatus.contains(reloaded.getStatus())) {
                        reloaded.setStatus(ModelServiceInstanceGroupStatus.Unknown);
                        dbf.update(reloaded);
                        logger.trace(String.format("model service instance group[uuid: %s] status is %s, set to Unknown",
                                reloaded.getUuid(), reloaded.getStatus()));
                    }
                } else {
                    logger.warn(String.format("model service instance group[uuid: %s] not found, skip create config and update state to running",
                            groupVO.getUuid()));
                }

                completion.success();
            }
        }).start();
    }

    protected void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIAddModelCenterMsg) {
            handle((APIAddModelCenterMsg) msg);
        } else if (msg instanceof APIUpdateModelCenterMsg) {
            handle((APIUpdateModelCenterMsg) msg);
        } else if (msg instanceof APIGetModelCenterServicesMsg) {
            handle((APIGetModelCenterServicesMsg) msg);
        } else if (msg instanceof APIDeleteModelCenterMsg) {
            handle((APIDeleteModelCenterMsg) msg);
        } else if (msg instanceof APIAddModelMsg) {
            handle((APIAddModelMsg) msg);
        } else if (msg instanceof APIUpdateModelMsg) {
            handle((APIUpdateModelMsg) msg);
        } else if (msg instanceof APIDeleteModelMsg) {
            handle((APIDeleteModelMsg) msg);
        } else if (msg instanceof APIDeleteModelsMsg) {
            handle((APIDeleteModelsMsg) msg);
        } else if (msg instanceof APIAddModelServiceMsg) {
            handle((APIAddModelServiceMsg) msg);
        } else if (msg instanceof APIUpdateModelServiceMsg) {
            handle((APIUpdateModelServiceMsg) msg);
        } else if (msg instanceof APICloneModelServiceMsg) {
            handle((APICloneModelServiceMsg) msg);
        } else if (msg instanceof APIDeleteModelServiceMsg) {
            handle((APIDeleteModelServiceMsg) msg);
        } else if (msg instanceof APIDeleteModelServicesMsg) {
            handle((APIDeleteModelServicesMsg) msg);
        } else if (msg instanceof APIUpdateModelEvaluationTaskMsg) {
            handle ((APIUpdateModelEvaluationTaskMsg) msg);
        } else if (msg instanceof APIBindModelToServiceMsg) {
            handle((APIBindModelToServiceMsg) msg);
        } else if (msg instanceof APIUnbindModelFromServiceMsg) {
            handle((APIUnbindModelFromServiceMsg) msg);
        } else if (msg instanceof APIDeployDistributedModelServiceMsg) {
            handle((APIDeployDistributedModelServiceMsg) msg);
        } else if (msg instanceof AbstractAPIDeployModelServiceMsg) {
            handle((AbstractAPIDeployModelServiceMsg) msg);
        } else if (msg instanceof APIDeleteModelServiceInstanceGroupMsg) {
            handle((APIDeleteModelServiceInstanceGroupMsg) msg);
        } else if (msg instanceof APIDeleteModelServiceInstanceGroupsMsg) {
            handle((APIDeleteModelServiceInstanceGroupsMsg) msg);
        } else if (msg instanceof APIUpdateModelServiceInstanceGroupMsg) {
            handle((APIUpdateModelServiceInstanceGroupMsg) msg);
        } else if (msg instanceof APICreateDatasetMsg) {
            handle((APICreateDatasetMsg) msg);
        } else if (msg instanceof APIUpdateDatasetMsg) {
            handle((APIUpdateDatasetMsg) msg);
        } else if (msg instanceof APIUpdateDatasetsMsg) {
            handle((APIUpdateDatasetsMsg) msg);
        } else if (msg instanceof APIDeleteDatasetMsg) {
            handle((APIDeleteDatasetMsg) msg);
        } else if (msg instanceof APIDeleteDatasetsMsg) {
            handle((APIDeleteDatasetsMsg) msg);
        } else if (msg instanceof APIDeleteModelEvaluationTaskMsg) {
            handle((APIDeleteModelEvaluationTaskMsg) msg);
        } else if (msg instanceof APIDeleteModelEvaluationTasksMsg) {
            handle((APIDeleteModelEvaluationTasksMsg) msg);
        } else if (msg instanceof APISyncAINginxConfigurationMsg) {
            handle((APISyncAINginxConfigurationMsg) msg);
        } else if (msg instanceof APIGetMaaSUsageMsg) {
            handle((APIGetMaaSUsageMsg) msg);
        } else if (msg instanceof APIRestartModelServiceGroupsMsg) {
            handle((APIRestartModelServiceGroupsMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(RestartModelServiceGroupMsg msg) {
        RestartModelServiceGroupReply reply = new RestartModelServiceGroupReply();
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("restart-model-service-groups");
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                ModelServiceInstanceGroupVO group = Q.New(ModelServiceInstanceGroupVO.class)
                        .eq(ModelServiceInstanceGroupVO_.uuid, msg.getUuid())
                        .find();
                if (group == null) {
                    trigger.fail(operr("Cannot find model service group[uuid: %s]", msg.getUuid()));
                    return;
                }

                ModelServiceBackend backend = backends.get(ModelServiceBackendType.valueOf(group.getType()));
                if (backend == null) {
                    trigger.fail(operr("Unsupported model service type: %s, can not find related backend to handle restart", group.getType()));
                    return;
                }
                backend.restartModelServiceGroup(group, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        chain.then(buildSyncGroupNginxRulesFlow(Collections.singletonList(msg.getUuid())));

        List<ModelCenterVO> modelCenterVOS = Q.New(ModelCenterVO.class)
                .limit(1)
                .list();
        if (!modelCenterVOS.isEmpty()) {
            chain.then(buildWaitForServiceBootUpFlow(
                    msg.getUuid(),
                    AIGlobalConfig.SERVICE_STARTUP_TIMES_IN_SECONDS.value(Integer.class),
                    modelCenterVOS.get(0))
            );
        } else {
            logger.warn("No model center can be found in db, skip waiting for service boot up");
        }

        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(msg.getUuid(), ModelServiceInstanceGroupVO.class);
                if (reloaded != null) {
                    createScrapeConfig(reloaded);
                    if (intermediateStatus.contains(reloaded.getStatus())) {
                        reloaded.setStatus(ModelServiceInstanceGroupStatus.Unknown);
                        dbf.update(reloaded);
                        logger.trace(String.format("model service instance group[uuid: %s] status is %s, set to Unknown",
                                reloaded.getUuid(), reloaded.getStatus()));
                    }
                } else {
                    logger.warn(String.format("model service instance group[uuid: %s] not found, skip create config and update state to running",
                            msg.getUuid()));
                }

                bus.reply(msg, reply);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                reply.setError(errCode);
                bus.reply(msg, reply);
            }
        }).start();
    }

    private void handle(APIRestartModelServiceGroupsMsg msg) {
        APIRestartModelServiceGroupsEvent event = new APIRestartModelServiceGroupsEvent(msg.getId());

        List<RestartModelServiceGroupMsg> rmsgs = new ArrayList<>();
        for (String uuid : msg.getUuids()) {
            RestartModelServiceGroupMsg rmsg = new RestartModelServiceGroupMsg();
            rmsg.setUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(rmsg, AIModelManagerImpl.SERVICE_ID, uuid);
            rmsgs.add(rmsg);
        }

        List<APIBatchRequest.BatchOperationResult<Void>> results = Collections.synchronizedList(new ArrayList<>());
        new While<>(rmsgs).step((rmsg, whileCompletion) -> {
            APIBatchRequest.BatchOperationResult<Void> result = new APIBatchRequest.BatchOperationResult<>();
            result.setUuid(rmsg.getUuid());
            // by default mark request failure unless message successfully replied
            result.setSuccess(false);
            results.add(result);
            bus.send(rmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("Failed to restart model service group %s, because %s", rmsg.getUuid(), reply.getError()));
                        result.setError(reply.getError());
                        result.setSuccess(false);
                    } else {
                        logger.debug(String.format("Restart model service group %s successfully", rmsg.getUuid()));
                        result.setSuccess(true);
                    }

                    whileCompletion.done();
                }
            });
        }, 5).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                   logger.warn("Error happened before, check RestartModelServiceGroupMsg related log for more information");
                }

                event.setResults(results);
                bus.publish(event);
            }
        });
    }

    private NoRollbackFlow buildWaitForServiceBootUpFlow(String groupUuid, int serviceBootUptime, ModelCenterVO modelCenter) {
        return new NoRollbackFlow() {
            String __name__ = "waiting-for-service-start-up";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                final ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(groupUuid, ModelServiceInstanceGroupVO.class);
                if (reloaded == null) {
                    trigger.fail(operr("model service instance group[uuid: %s] not found," +
                            " skip waiting-for-service-start-up flow", groupUuid));
                    return;
                }

                if (reloaded.getInstances().isEmpty()) {
                    logger.debug(String.format("model service instance group[uuid: %s] has no instance, skip waiting-for-service-start-up flow", groupUuid));
                    trigger.next();
                    return;
                }

                reloaded.setStatus(ModelServiceInstanceGroupStatus.ServiceBootingUp);
                dbf.update(reloaded);

                ModelServiceInstanceVO modelServiceInstanceVO = reloaded.getInstances().stream().findFirst().orElse(null);
                if (modelServiceInstanceVO == null) {
                    logger.debug(String.format("model service instance group[uuid: %s] has no instance, skip waiting-for-service-start-up flow", groupUuid));
                    trigger.next();
                    return;
                }

                ModelServiceInstanceInventory modelServiceInstance = new ModelServiceInstanceInventory(modelServiceInstanceVO);
                Integer serviceStartupCheckInterval = AIGlobalConfig.SERVICE_STARTUP_CHECK_INTERVAL_SECONDS.value(Integer.class);

                final Boolean[] readyInstance = {false};
                ErrorCode unexpectedError = null;
                long startTime = zTimer.getCurrentTimeMillis();
                for (int i = 0; i < serviceBootUptime; i=i+serviceStartupCheckInterval) {
                    ModelServiceInstanceGroupVO groupVO = dbf.findByUuid(groupUuid, ModelServiceInstanceGroupVO.class);
                    if (groupVO == null) {
                        unexpectedError = operr("model service instance group[uuid: %s] not found," +
                                " break waiting-for-service-start-up aliveness check", groupUuid);
                        break;
                    }

                    // current only one instance per instance group
                    // multi instance must use new while to check, not foreach
                    // TODO: support multi instance
                    isAlive(modelServiceInstance, modelCenter.getManagementIp(), new Completion(trigger) {
                        @Override
                        public void success() {
                            logger.info("service instance is ready");
                            readyInstance[0] = true;
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            logger.info(String.format("service instance: %s is not ready, waiting for service ready", modelServiceInstance.getInternalUrl()));
                        }
                    });

                    if (readyInstance[0]) {
                        logger.debug(String.format("service instance: %s is ready, update model service instance group status to running", modelServiceInstance.getInternalUrl()));
                        reloaded.setStatus(ModelServiceInstanceGroupStatus.Running);
                        dbf.update(reloaded);
                        trigger.next();
                        return;
                    }

                    try {
                        TimeUnit.SECONDS.sleep(serviceStartupCheckInterval);
                    } catch (InterruptedException ignored) {
                        Thread.currentThread().interrupt();
                    }

                    long endTime = zTimer.getCurrentTimeMillis();
                    if (endTime - startTime > serviceBootUptime * 1000L) {
                        logger.warn(String.format("service instance is not ready in %s seconds," +
                                " please adjust service startup time", serviceBootUptime));
                        break;
                    }
                }

                if (AISystemTags.MODEL_SERVICE_MODE.hasTag(groupUuid)) {
                    reloaded.setStatus(ModelServiceInstanceGroupStatus.Unknown);
                    dbf.update(reloaded);
                    logger.info(String.format("service: %s is not ready, but debug mode is set, do not fail this deployment, " +
                            "update model service instance group status to unknown", groupUuid));
                    trigger.next();
                    return;
                }

                if (unexpectedError != null) {
                    logger.debug(String.format("service group[uuid: %s]," +
                            " maybe the group is deleted or rollback due to some reason," +
                            " mark deploy as failure. Because %s", groupUuid, unexpectedError));
                    trigger.fail(unexpectedError);
                    return;
                }

                logger.debug(String.format("service is not ready in %s seconds," +
                        " please adjust service startup time", serviceBootUptime));
                trigger.next();
            }
        };
    }

    private NoRollbackFlow buildSyncGroupNginxRulesFlow(List<String> groupUuids) {
        return new NoRollbackFlow() {
            String __name__ = "sync-nginx-configuration";

            @Override
            public boolean skip(Map data) {
                return CollectionUtils.isEmpty(groupUuids);
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                SyncAINginxConfigurationMsg syncAINginxConfigurationMsg = new SyncAINginxConfigurationMsg();
                List<ModelServiceInstanceVO> instanceVOS = Q.New(ModelServiceInstanceVO.class)
                        .in(ModelServiceInstanceVO_.modelServiceGroupUuid,groupUuids)
                        .list();
                if (instanceVOS.isEmpty()) {
                    logger.warn("no model service instance found, skip sync nginx configuration");
                    trigger.next();
                    return;
                }

                SQL.New(ModelServiceInstanceGroupVO.class)
                        .in(ModelServiceInstanceGroupVO_.uuid, groupUuids)
                        .set(ModelServiceInstanceGroupVO_.status, ModelServiceInstanceGroupStatus.ConfigSettingUp)
                        .update();

                List<NginxRedirectRule> nginxRedirectRules = new ArrayList<>();
                instanceVOS.forEach(modelServiceInstanceVO -> {
                    ModelServiceInstanceInventory inv = new ModelServiceInstanceInventory(modelServiceInstanceVO);
                    NginxRedirectRule rule = new NginxRedirectRule();
                    rule.setUuid(inv.getUuid());
                    rule.setDestUrl(inv.getInternalUrl());
                    rule.setJupyterUrl(inv.getJupyterUrl());
                    nginxRedirectRules.add(rule);
                });
                syncAINginxConfigurationMsg.setNginxConfigRules(nginxRedirectRules);
                syncAINginxConfigurationMsg.setDryRun(false);

                bus.makeTargetServiceIdByResourceUuid(syncAINginxConfigurationMsg, AIModelManager.SERVICE_ID, SyncAINginxConfigurationMsg.class.getSimpleName());
                bus.send(syncAINginxConfigurationMsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.warn(String.format("failed to sync nginx configuration, %s", reply.getError()));
                        }

                        trigger.next();
                    }
                });
            }
        };
    }
    private void handle(APIGetMaaSUsageMsg msg) {
        APIGetMaaSUsageReply reply = new APIGetMaaSUsageReply();
        List<MaaSUsage> usages = new ArrayList<>();
        new SQLBatch() {
            @Override
            protected void scripts() {
                for (ModelServiceType type : ModelServiceType.values()) {
                    MaaSUsage usage = new MaaSUsage();
                    usage.setValue(countGroupByModelServiceType(type));
                    usage.setName(String.format("%s.model.service.instance.group", type.toString().toLowerCase()));
                    usages.add(usage);
                }

                List<ContainerUsage> containerUsages = aiLicenseQuotaChecker.reportUsage();
                for (ContainerUsage cu : containerUsages) {
                    MaaSUsage usage = new MaaSUsage();
                    usage.setName(cu.getName());
                    usage.setValue(cu.getValue());
                    usages.add(usage);
                }

                MaaSUsage usage = new MaaSUsage();
                usage.setName("vm.gpu.number");
                usage.setValue((long) aiLicenseQuotaChecker.getVmUsedGpuNum());
                usages.add(usage);
            }

            private long countGroupByModelServiceType(ModelServiceType type) {
                SQL query;
                if (AccountConstant.INITIAL_SYSTEM_ADMIN_UUID.equals(msg.getSession().getAccountUuid())) {
                    query = sql("select count(g.uuid) from ModelServiceInstanceGroupVO g where " +
                            "g.modelServiceType = :modelServiceType", Long.class);
                } else {
                    query = sql("select count(g) from ModelServiceInstanceGroupVO g, AccountResourceRefVO accountref where " +
                            "g.modelServiceType = :modelServiceType and " +
                            "g.uuid = accountref.resourceUuid and " +
                            "accountref.accountUuid = :accountUuid", Long.class);
                }

                query.param("modelServiceType", type.toString());
                if (!AccountConstant.INITIAL_SYSTEM_ADMIN_UUID.equals(msg.getSession().getAccountUuid())) {
                    query.param("accountUuid", msg.getSession().getAccountUuid());
                }

                return query.find();
            }
        }.execute();
        reply.setUsages(usages);
        bus.reply(msg, reply);
    }

    private void handle(APISyncAINginxConfigurationMsg msg) {
        APISyncAINginxConfigurationReply reply = new APISyncAINginxConfigurationReply();

        SyncAINginxConfigurationMsg syncAINginxConfigurationMsg = new SyncAINginxConfigurationMsg();
        syncAINginxConfigurationMsg.setDryRun(msg.getDryRun());

        if ((msg.getInstanceUuids() == null || msg.getInstanceUuids().isEmpty()) && !msg.getSyncAll()) {
            reply.setError(operr("Either 'InstanceUuids' or 'SyncAll' must be provided"));
            bus.reply(msg, reply);
            return;
        }

        List<ModelServiceInstanceVO> modelServiceInstanceVOS = new ArrayList<>();

        if (msg.getSyncAll()) {
            List<ModelServiceInstanceVO> instanceVOS = Q.New(ModelServiceInstanceVO.class).list();
            modelServiceInstanceVOS.addAll(instanceVOS);
        }

        if (msg.getInstanceUuids() != null && !msg.getInstanceUuids().isEmpty()) {
            List<ModelServiceInstanceVO> modelServiceInstanceVOS1 = Q.New(ModelServiceInstanceVO.class)
                    .in(ModelServiceInstanceVO_.uuid, msg.getInstanceUuids())
                    .list();
            modelServiceInstanceVOS.addAll(modelServiceInstanceVOS1);
        }
        if (modelServiceInstanceVOS.isEmpty()) {
            reply.setError(operr("Can not find any service instance"));
            bus.reply(msg, reply);
            return;
        }

        List<NginxRedirectRule> nginxRedirectRules = new ArrayList<>();
        modelServiceInstanceVOS.forEach(modelServiceInstanceVO -> {
            ModelServiceInstanceInventory inv = new ModelServiceInstanceInventory(modelServiceInstanceVO);
            NginxRedirectRule rule = new NginxRedirectRule();
            rule.setUuid(inv.getUuid());
            rule.setDestUrl(inv.getInternalUrl());
            rule.setJupyterUrl(inv.getJupyterUrl());
            nginxRedirectRules.add(rule);
        });
        syncAINginxConfigurationMsg.setNginxConfigRules(nginxRedirectRules);

        bus.makeTargetServiceIdByResourceUuid(syncAINginxConfigurationMsg, AIModelManager.SERVICE_ID, msg.getClass().getSimpleName());
        bus.send(syncAINginxConfigurationMsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply1) {
                if (!reply1.isSuccess()) {
                    reply.setError(reply1.getError());
                }
                SyncAINginxConfigurationReply r = reply1.castReply();
                reply.setUnSyncedRules(r.getUnSyncedRules());
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(APIDeleteModelEvaluationTasksMsg msg) {
        APIDeleteModelEvaluationTasksEvent event = new APIDeleteModelEvaluationTasksEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                List<APIBatchRequest.BatchOperationResult<Void>> results = Collections.synchronizedList(new ArrayList<>());
                new While<>(msg.getUuids()).each((taskUuid, whileCompletion) -> {
                    APIBatchRequest.BatchOperationResult<Void> result = new APIBatchRequest.BatchOperationResult<>();
                    doDeleteModelEvaluationTaskByUuid(taskUuid, new Completion(whileCompletion) {
                        @Override
                        public void success() {
                            result.setUuid(taskUuid);
                            results.add(result);
                            whileCompletion.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            result.setError(errorCode);
                            results.add(result);
                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(msg, chain) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        event.setResults(results);
                        bus.publish(event);
                        chain.next();
                    }
                });
            }

            @Override
            public String getSyncSignature() {
                return "batch-model-evaluation-task";
            }

            @Override
            public String getName() {
                return "batch-model-evaluation-task";
            }
        });
    }

    private void handle(APIDeleteModelEvaluationTaskMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                APIDeleteModelEvaluationTaskEvent event = new APIDeleteModelEvaluationTaskEvent(msg.getId());
                doDeleteModelEvaluationTask(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(event);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        event.setError(errorCode);
                        bus.publish(event);
                        chain.next();
                    }
                });
            }

            @Override
            public String getSyncSignature() {
                return String.format("model-evaluation-task-%s", msg.getUuid());
            }

            @Override
            public String getName() {
                return String.format("model-evaluation-task-%s", msg.getUuid());
            }
        });
    }

    private void doDeleteModelEvaluationTaskByUuid(String taskUuid, Completion completion) {
        ModelEvaluationTaskVO task = Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.uuid, taskUuid)
                .find();
        if (task == null) {
            completion.success();
            return;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.then(new NoRollbackFlow() {
            String __name__ = "cancel-task";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (task.getModelServiceGroupUuid() == null) {
                    logger.debug(String.format("model evaluation task %s has no model service group, skip delete model service group", taskUuid));
                    trigger.next();
                    return;
                }

                if (!Q.New(ModelServiceInstanceGroupVO.class).eq(ModelServiceInstanceGroupVO_.uuid, task.getModelServiceGroupUuid()).isExists()) {
                    logger.debug(String.format("model service group %s not found, skip delete model service group", task.getModelServiceGroupUuid()));
                    trigger.next();
                    return;
                }

                // check if still remaining tasks, do nothing to the model service group
                if (Q.New(ModelEvaluationTaskVO.class).eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, task.getModelServiceGroupUuid()).count() == 1) {
                    logger.debug(String.format("model service group %s has no other tasks, skip cancel task", task.getModelServiceGroupUuid()));
                    trigger.next();
                    return;
                }

                ModelServiceInstanceGroupVO group = dbf.findByUuid(task.getModelServiceGroupUuid(), ModelServiceInstanceGroupVO.class);
                ModelServiceBackendType type = ModelServiceBackendType.valueOf(group.getType());
                ModelServiceBackend backend = AIModelManagerImpl.getModelServiceBackendByType(type);
                new While<>(group.getInstances()).each((instance, whileCompletion) -> {
                    backend.cancelTask(instance, task.getUuid(), new Completion(whileCompletion) {
                        @Override
                        public void success() {
                            whileCompletion.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            whileCompletion.addError(errorCode);
                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                        } else {
                            logger.warn(String.format("failed to cancel model evaluation task %s, %s", taskUuid, errorCodeList.getCauses().get(0)));
                            trigger.fail(errorCodeList.getCauses().get(0));
                        }
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "delete-model-service-group-if-needed";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                // check if still remaining tasks, do nothing to the model service group
                if (Q.New(ModelEvaluationTaskVO.class).eq(ModelEvaluationTaskVO_.modelServiceGroupUuid, task.getModelServiceGroupUuid()).count() > 1) {
                    logger.debug(String.format("model service group %s still has tasks on it, skip delete model service group", task.getModelServiceGroupUuid()));
                    trigger.next();
                    return;
                }

                DeleteModelServiceInstanceGroupMsg dmsg = new DeleteModelServiceInstanceGroupMsg();
                dmsg.setUuid(task.getModelServiceGroupUuid());
                bus.makeTargetServiceIdByResourceUuid(dmsg, SERVICE_ID, dmsg.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                // delete task
                dbf.remove(task);
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void doDeleteModelEvaluationTask(APIDeleteModelEvaluationTaskMsg msg, Completion completion) {
        doDeleteModelEvaluationTaskByUuid(msg.getUuid(), completion);
    }

    private void handle(APIUpdateModelServiceInstanceGroupMsg msg) {
        ModelServiceInstanceGroupVO modelServiceInstanceGroup = dbf.findByUuid(msg.getUuid(), ModelServiceInstanceGroupVO.class);

        if (msg.getName() != null) {
            modelServiceInstanceGroup.setName(msg.getName());
        }

        if (msg.getDescription() != null) {
            modelServiceInstanceGroup.setDescription(msg.getDescription());
        }

        if (!CollectionUtils.isEmpty(modelServiceInstanceGroup.getInstances())) {
            modelServiceInstanceGroup.getInstances().forEach(instance -> {
                AIConfigYaml configYaml = updateYamlConfig(instance.getYaml(), msg.getEnvironmentVariables(), msg.getStartupParameters());
                if (configYaml != null) {
                    if (configYaml.getEnvironmentParameters() == null) {
                        return;
                    }

                    String port = configYaml.getEnvironmentParameters().get(AIModelServiceEnvironments.MAAS_SERVICE_PORT.toString());
                    if (port != null) {
                        configYaml.getServices().forEach(service -> service.setPorts(Collections.singletonList(Integer.valueOf(port))));

                        if (!Strings.isNullOrEmpty(instance.getInternalUrl())) {
                            instance.setInternalUrl(instance.getInternalUrl().split(":")[0] + ":" + port);
                        }
                        if (!Strings.isNullOrEmpty(instance.getUrl())) {
                            instance.setUrl(instance.getUrl().split(":")[0] + ":" + port);
                        }

                        try {
                            if (instance.getUrlMaps() != null) {
                                // legacy code
                                Map<String, String> urlMap = null;
                                if (instance.getUrlMaps().contains("url=")) {
                                    // means current db record is in map to string format
                                    // {jupyterUrl=172.20.9.27:8888, internalUrl=172.20.9.27:3000, url=172.20.9.27:3000}
                                    String urlMaps = instance.getUrlMaps().replace("{", "").replace("}", "");
                                    String[] urlMapArray = urlMaps.split(",");
                                    urlMap = new HashMap<>();
                                    for (String urlMapStr : urlMapArray) {
                                        String[] urlMapStrArray = urlMapStr.split("=");
                                        urlMap.put(urlMapStrArray[0].replace(" ", ""), urlMapStrArray[1].replace(" ", ""));
                                    }
                                }

                                logger.debug(String.format("Update url maps for instance %s, urlMaps %s", instance.getUuid(), instance.getUrlMaps()));
                                if (urlMap != null) {
                                    if (urlMap.containsKey("url")) {
                                        urlMap.put("url", urlMap.get("url").split(":")[0] + ":" + port);
                                    }
                                    if (urlMap.containsKey("internalUrl")) {
                                        urlMap.put("internalUrl", urlMap.get("internalUrl").split(":")[0] + ":" + port);
                                    }
                                    instance.setUrlMaps(urlMap.toString());
                                }
                            }
                        } catch (Exception e) {
                            logger.warn(String.format("Failed to update url maps for instance %s, urlMaps %s", instance.getUuid(), instance.getUrlMaps()), e);
                        }
                    }

                    String bootUpTime = configYaml.getEnvironmentParameters().get(AIModelServiceEnvironments.MAAS_SERVICE_TIMEOUT.toString());
                    if (bootUpTime != null) {
                        try {
                            int serviceBootUptime = Integer.parseInt(bootUpTime);
                            configYaml.getServices().forEach(service -> service.setServiceBootupTime(serviceBootUptime));
                        } catch (NumberFormatException e) {
                            logger.warn(String.format("Failed to parse MAAS_SERVICE_TIMEOUT %s for instance %s", bootUpTime, instance.getUuid()), e);
                        }
                    }

                    instance.setYaml(configYaml.toYaml());
                    dbf.updateAndRefresh(instance);
                } else {
                    logger.warn(String.format("configYaml is null, skip update for instance %s", instance.getUuid()));
                }
            });
        }

        AIConfigYaml groupConfigYaml = updateYamlConfig(modelServiceInstanceGroup.getYaml(), msg.getEnvironmentVariables(), msg.getStartupParameters());
        if (groupConfigYaml != null) {
            modelServiceInstanceGroup.setYaml(groupConfigYaml.toYaml());
        }

        modelServiceInstanceGroup = dbf.updateAndRefresh(modelServiceInstanceGroup);
        APIUpdateModelServiceInstanceGroupEvent event = new APIUpdateModelServiceInstanceGroupEvent(msg.getId());
        event.setInventory(ModelServiceInstanceGroupInventory.valueOf(modelServiceInstanceGroup));
        bus.publish(event);
    }

    private AIConfigYaml updateYamlConfig(String yaml, Map<String, String> environmentVariables, Map<String, String> startupParameters) {
        if (yaml == null) {
            logger.warn("yaml is null, skip update");
            return null;
        }

        AIConfigYaml configYaml = AIConfigYaml.fromYaml(yaml);
        if (configYaml == null) {
            logger.warn("configYaml is null, skip update");
            return null;
        }

        configYaml.setEnvironmentParameters(new HashMap<>());
        if (environmentVariables != null) {
            environmentVariables.forEach((key, value) -> {
                logger.trace(String.format("Adding environment parameter: %s with value: %s", key, value));
                configYaml.getEnvironmentParameters().put(key, value);
            });
        }

        configYaml.setStartupParameters(new HashMap<>());
        if (startupParameters != null) {
            startupParameters.forEach((key, value) -> {
                if (configYaml.getStartupParameters() != null && configYaml.getStartupParameters().containsKey(key)) {
                    logger.trace(String.format("Overriding startup parameter: %s", key));
                }
                configYaml.getStartupParameters().put(key, value);
            });
        }

        if (configYaml.getEnvironmentParameters() != null && !configYaml.getEnvironmentParameters().isEmpty()) {
            for (AIModelServiceEnvironments env : AIModelServiceEnvironments.values()) {
                if (!configYaml.getEnvironmentParameters().containsKey(env.toString())) {
                    logger.trace(String.format("no environment parameter for %s", env));
                    continue;
                }

                String value = configYaml.getEnvironmentParameters().get(env.toString());
                for (AIConfigYaml.Service service : configYaml.getServices()) {
                    logger.trace(String.format("Adding environment parameter %s to service %s", env, service.getName()));
                    env.applyToService(service, value);
                }
            }
        }

        return configYaml;
    }

    private void doDeleteDataset(String datasetUuid, Completion completion) {
        DatasetVO dataset = dbf.findByUuid(datasetUuid, DatasetVO.class);
        ModelCenterVO modelCenter = dbf.findByUuid(dataset.getModelCenterUuid(), ModelCenterVO.class);
        ModelStorageBackend backend = new BentoBasedModelStorageBackend(modelCenter);

        backend.deleteDataset(datasetUuid, new Completion(completion) {
            @Override
            public void success() {
                dbf.remove(dataset);
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void handle(APIDeleteDatasetMsg msg) {
        APIDeleteDatasetEvent event = new APIDeleteDatasetEvent(msg.getId());
        doDeleteDataset(msg.getUuid(), new Completion(msg) {
            @Override
            public void success() {
                bus.publish(event);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                event.setError(errorCode);
                bus.publish(event);
            }
        });
    }

    private void handle(APIUpdateDatasetMsg msg) {
        DatasetVO dataset = dbf.findByUuid(msg.getUuid(), DatasetVO.class);

        if (msg.getName() != null) {
            dataset.setName(msg.getName());
        }

        if (msg.getDescription() != null) {
            dataset.setDescription(msg.getDescription());
        }

        updateSystemTags(dataset.getUuid(), msg.getUsageScenarios(), msg.getDataType());
        dataset = dbf.updateAndRefresh(dataset);
        APIUpdateDatasetEvent event = new APIUpdateDatasetEvent(msg.getId());
        event.setInventory(DatasetInventory.valueOf(dataset));
        bus.publish(event);
    }

    private void handle(APIUpdateDatasetsMsg msg) {
        APIUpdateDatasetsEvent event = new APIUpdateDatasetsEvent(msg.getId());
        List<APIBatchRequest.BatchOperationResult<Void>> results = new ArrayList<>();

        for (APIUpdateDatasetsMsg.UpdateDatasetStruct struct : msg.getUpdateDatasetStructs()) {
            APIBatchRequest.BatchOperationResult<Void> result = new APIBatchRequest.BatchOperationResult<>();
            result.setUuid(struct.getUuid());

            try {
                // 查找dataset
                DatasetVO dataset = dbf.findByUuid(struct.getUuid(), DatasetVO.class);
                if (dataset == null) {
                    result.setError(operr("Cannot find dataset with uuid [%s]", struct.getUuid()));
                    result.setSuccess(false);
                    results.add(result);
                    continue;
                }

                updateSystemTags(dataset.getUuid(), struct.getUsageScenarios(), struct.getDataType());

                // 保存更新
                dataset = dbf.updateAndRefresh(dataset);
                result.setSuccess(true);
            } catch (Exception e) {
                logger.error(String.format("Failed to update dataset [uuid:%s]", struct.getUuid()), e);
                result.setError(operr(e.getMessage()));
                result.setSuccess(false);
            }

            results.add(result);
        }

        event.setResults(results);
        bus.publish(event);
    }

    private void updateSystemTags(String uuid, List<String> scenarios, String dataType) {
        if (scenarios != null) {
            AISystemTags.DATA_SET_USAGE_SCENARIOS_APP.delete(uuid);
            AISystemTags.DATA_SET_USAGE_SCENARIOS_END_POINT.delete(uuid);
            AISystemTags.DATA_SET_USAGE_SCENARIOS_FINE_TUNE.delete(uuid);
            AISystemTags.DATA_SET_USAGE_SCENARIOS_MODEL_EVAL.delete(uuid);
            AISystemTags.DATA_SET_USAGE_SCENARIOS_MODEL_PERF.delete(uuid);
            for (String scenario : scenarios) {
                SystemTagCreator creator;
                switch (scenario) {
                    case "App":
                        creator = AISystemTags.DATA_SET_USAGE_SCENARIOS_APP.newSystemTagCreator(uuid);
                        break;
                    case "Endpoint":
                        creator = AISystemTags.DATA_SET_USAGE_SCENARIOS_END_POINT.newSystemTagCreator(uuid);
                        break;
                    case "FineTune":
                        creator = AISystemTags.DATA_SET_USAGE_SCENARIOS_FINE_TUNE.newSystemTagCreator(uuid);
                        break;
                    case "ModelEval":
                        creator = AISystemTags.DATA_SET_USAGE_SCENARIOS_MODEL_EVAL.newSystemTagCreator(uuid);
                        break;
                    case "ModelPerf":
                        creator = AISystemTags.DATA_SET_USAGE_SCENARIOS_MODEL_PERF.newSystemTagCreator(uuid);
                        break;
                    default:
                        throw new IllegalArgumentException("Unknown scenario: " + scenario);
                }
                creator.recreate = true;
                creator.create();
            }
        }

        if (dataType != null) {
            AISystemTags.DATA_SET_DATA_TYPE.delete(uuid);
            SystemTagCreator creator = AISystemTags.DATA_SET_DATA_TYPE.newSystemTagCreator(uuid);
            creator.recreate = true;
            creator.tag = AISystemTags.DATA_SET_DATA_TYPE.instantiateTag(
                    map(e(
                            AISystemTags.DATA_SET_DATA_TYPE_TOKEN, dataType
                    ))
            );
            creator.create();
        }
    }

    private void handle(CreateDatasetMsg msg) {
        CreateDatasetReply reply = new CreateDatasetReply();
        APICreateDatasetMsg apiMsg = msg.getApiMessage();
        ModelCenterVO modelCenter = dbf.findByUuid(apiMsg.getModelCenterUuid(), ModelCenterVO.class);
        ModelStorageBackend backend = new BentoBasedModelStorageBackend(modelCenter);

        DatasetVO datasetVO = getDatasetVO(apiMsg);

        final DatasetVO finalDataset = dbf.persistAndRefresh(datasetVO);
        try {
            tagMgr.createTagsFromAPICreateMessage(msg.getApiMessage(), datasetVO.getUuid(), DatasetVO.class.getSimpleName());
        } catch (Exception e) {
            dbf.remove(finalDataset);
            reply.setError(operr(e.getMessage()));
            bus.reply(msg, reply);
            return;
        }

        backend.addDataset(finalDataset, apiMsg.getToken(), new ReturnValueCompletion<BentoBasedModelStorageBackend.UploadDatasetResponse>(msg) {
            @Override
            public void success(BentoBasedModelStorageBackend.UploadDatasetResponse returnValue) {
                finalDataset.setInstallPath(returnValue.installPath);
                finalDataset.setSize(returnValue.size);
                dbf.updateAndRefresh(finalDataset);
                reply.setInventory(DatasetInventory.valueOf(finalDataset));
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                dbf.remove(finalDataset);
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    @NotNull
    private static DatasetVO getDatasetVO(APICreateDatasetMsg apiMsg) {
        DatasetVO datasetVO = new DatasetVO();
        if (apiMsg.getResourceUuid() != null) {
            datasetVO.setUuid(apiMsg.getResourceUuid());
        } else {
            datasetVO.setUuid(Platform.getUuid());
        }
        datasetVO.setUrl(apiMsg.getUrl());
        datasetVO.setName(apiMsg.getName());

        if (apiMsg.getSystem() == null) {
            datasetVO.setSystem(false);
        } else {
            datasetVO.setSystem(apiMsg.getSystem());
        }
        datasetVO.setDescription(apiMsg.getDescription());
        datasetVO.setAccountUuid(apiMsg.getSession().getAccountUuid());
        datasetVO.setModelCenterUuid(apiMsg.getModelCenterUuid());
        return datasetVO;
    }

    private void handle(APICreateDatasetMsg msg) {
        APICreateDatasetEvent event = new APICreateDatasetEvent(msg.getId());
        CreateDatasetMsg cmsg = new CreateDatasetMsg();
        cmsg.setApiMessage(msg);
        bus.makeTargetServiceIdByResourceUuid(cmsg, AIModelManager.SERVICE_ID, msg.getClass().getSimpleName());
        bus.send(cmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    event.setError(reply.getError());
                } else {
                    event.setInventory(((CreateDatasetReply) reply.castReply()).getInventory());
                }

                bus.publish(event);
            }
        });
    }

    private void deleteModelCenterScrapeConfig(String uuid) {
        pscMgr.deleteModelCenterConfig(uuid);
    }

    private void deleteScrapeConfig(String uuid) {
        pscMgr.deleteModelServiceInstanceGroupConfig(uuid);
    }

    private void handle(APIDeleteModelServiceInstanceGroupsMsg msg) {
        List<DeleteModelServiceInstanceGroupMsg> deleteMessages = new ArrayList<>();
        for (String uuid : msg.getUuids()) {
            DeleteModelServiceInstanceGroupMsg dmsg = new DeleteModelServiceInstanceGroupMsg();
            dmsg.setUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(dmsg, AIModelManager.SERVICE_ID, dmsg.getUuid());
            deleteMessages.add(dmsg);
        }

        List<APIBatchRequest.BatchOperationResult<Void>> results = Collections.synchronizedList(new ArrayList<>());
        new While<>(deleteMessages).each((dmsg, whileCompletion) -> {
            APIBatchRequest.BatchOperationResult<Void> result = new APIBatchRequest.BatchOperationResult<>();
            bus.send(dmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        result.setError(reply.getError());
                    } else {
                        result.setSuccess(true);
                    }
                    result.setUuid(dmsg.getUuid());
                    results.add(result);
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    logger.error(String.format("delete model service instance group failed, because of %s", errorCodeList.getCauses().get(0).toString()));
                }

                APIDeleteModelServiceInstanceGroupsEvent event = new APIDeleteModelServiceInstanceGroupsEvent(msg.getId());
                event.setResults(results);
                bus.publish(event);
            }
        });
    }

    private void handle(APIDeleteModelServiceInstanceGroupMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                APIDeleteModelServiceInstanceGroupEvent event = new APIDeleteModelServiceInstanceGroupEvent(msg.getId());
                deleteModelServiceInQueue(msg, new Completion(msg, chain) {
                    @Override
                    public void success() {
                        bus.publish(event);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        event.setError(errorCode);
                        bus.publish(event);
                        chain.next();
                    }
                });
            }

            @Override
            public String getSyncSignature() {
                return modelServiceGroupSyncId(msg.getUuid());
            }

            @Override
            public String getName() {
                return modelServiceGroupSyncId(msg.getUuid());
            }
        });
    }

    private void deleteModelServiceInQueue(APIDeleteModelServiceInstanceGroupMsg msg, Completion completion) {
        DeleteModelServiceInstanceGroupMsg dmsg = new DeleteModelServiceInstanceGroupMsg();
        dmsg.setUuid(msg.getUuid());

        bus.makeTargetServiceIdByResourceUuid(dmsg, AIModelManager.SERVICE_ID, dmsg.getUuid());
        bus.send(dmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                completion.success();
            }
        });
    }

    private void createSystemTagFromDeployModelMsg(AbstractAPIDeployModelServiceMsg msg, String uuid) {
        if (CollectionUtils.isEmpty(msg.getSystemTags())) {
            return;
        }

        msg.getSystemTags().stream()
                .filter(Objects::nonNull)
                .filter(systemTag -> AISystemTags.MODEL_SERVICE_MODE.isMatch(systemTag))
                .findFirst().ifPresent(mode -> tagMgr.createNonInherentSystemTag(uuid, mode, ModelServiceInstanceGroupVO.class.getSimpleName()));

        msg.getSystemTags().removeIf(systemTag -> AISystemTags.MODEL_SERVICE_MODE.isMatch(systemTag));
    }

    private void handle(APIDeployDistributedModelServiceMsg msg) {
        List<ModelService> modelServices = msg.getModelServices();
        if (modelServices == null || modelServices.isEmpty()) {
            throw new IllegalArgumentException("modelServices is null or empty");
        }

        ModelServiceInstanceGroupVO groupVO = new ModelServiceInstanceGroupVO();
        groupVO.setUuid(Platform.getUuid());
        groupVO.setName(msg.getName());
        groupVO.setDescription(msg.getDescription());
        groupVO.setModelUuid(msg.getModelServices().get(0).getModelUuid());
        groupVO.setAccountUuid(msg.getSession().getAccountUuid());
        groupVO.setStatus(ModelServiceInstanceGroupStatus.Deploying);
        groupVO.setType(msg.getModelServices().get(0).getType());

        ModelServiceVO modelService = dbf.findByUuid(msg.getModelServices().get(0).getUuid(), ModelServiceVO.class);
        ModelServiceInventory modelServiceInventory = ModelServiceInventory.valueOf(modelService);

        AIConfigYaml configYaml = buildModelServiceYaml(
                modelServiceInventory,
                null,
                modelServices.get(0),
                groupVO.getUuid(),
                null
        );
        groupVO.setYaml(configYaml.toYaml());

        ModelServiceVO vo = getModelServiceByUuid(msg.getModelServices().get(0).getUuid());
        groupVO.setModelServiceType(vo.getType().toString());
        groupVO.setModelServiceUuid(msg.getModelServices().get(0).getUuid());

        dbf.persist(groupVO);

        String modelCenterUuid;
        if (!StringUtils.isEmpty(modelService.getModelCenterUuid())) {
            modelCenterUuid = modelService.getModelCenterUuid();
            logger.debug(String.format("model service's model center uuid is not empty, use model service's model center uuid %s", modelCenterUuid));
        } else {
            ModelVO modelVO = dbf.findByUuid(groupVO.getModelUuid(), ModelVO.class);
            modelCenterUuid = modelVO.getModelCenterUuid();
            logger.debug(String.format("model uuid is not empty, use model's model center uuid %s", modelCenterUuid));
        }
        ModelCenterVO modelCenter = dbf.findByUuid(modelCenterUuid, ModelCenterVO.class);
        if (modelCenter == null) {
            throw new OperationFailureException(operr("model center[uuid:%s] not found", modelCenterUuid));
        }

        Integer serviceBootUptime;
        if (msg.getModelServices().get(0).getServiceBootUptime() != null) {
            serviceBootUptime = msg.getModelServices().get(0).getServiceBootUptime();
        } else {
            serviceBootUptime = configYaml.getServices().get(0).getServiceBootupTime();
        }

        if (serviceBootUptime == null || serviceBootUptime.equals(0)) {
            Integer osStartUptime = AIGlobalConfig.OS_STARTUP_TIMES_IN_SECONDS.value(Integer.class);
            Integer serviceStartUptime = AIGlobalConfig.SERVICE_STARTUP_TIMES_IN_SECONDS.value(Integer.class);
            serviceBootUptime = osStartUptime + serviceStartUptime;
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("deploy-distributed-model-service");
        chain.then(new NoRollbackFlow() {
            String __name__ = "copy-properties";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (configYaml.getEnvironmentParameters() == null) {
                    logger.debug("no environment parameters, skip");
                    trigger.next();
                    return;
                }

                for (AIModelServiceEnvironments env : AIModelServiceEnvironments.values()) {
                    String value = configYaml.getEnvironmentParameters().get(env.toString());
                    if (value != null) {
                        msg.getModelServices().forEach(ms -> env.applyToService(ms, value));
                    }
                }

                msg.getModelServices().forEach(service -> aiUtils.copyTagAsModelServiceProperty(service.getModelUuid(), groupVO.getUuid()));
                trigger.next();
            }
        }).then(new Flow() {
            String __name__ = "deploy-model-service-flow";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                DeployModelServiceMsg dmsg = new DeployModelServiceMsg();
                dmsg.setUuid(groupVO.getUuid());
                dmsg.setMessageType(msg.getClass().toString());
                dmsg.setModelServices(msg.getModelServices().stream()
                        .map(service -> {
                            service.setSession(msg.getSession());
                            service.setTimeout(msg.getTimeout());
                            return JSONObjectUtil.rehashObject(service, LinkedHashMap.class);
                        })
                        .collect(Collectors.toList()));
                bus.makeTargetServiceIdByResourceUuid(dmsg, AIModelManager.SERVICE_ID, dmsg.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        trigger.next();
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(groupVO.getUuid(), ModelServiceInstanceGroupVO.class);
                if (reloaded == null) {
                    logger.debug(String.format(
                            "model service instance group %s not exists, skip rollback",
                            groupVO.getUuid()
                    ));
                    trigger.rollback();
                    return;
                }

                DeleteModelServiceInstanceGroupMsg dmsg = new DeleteModelServiceInstanceGroupMsg();
                dmsg.setUuid(reloaded.getUuid());
                bus.makeTargetServiceIdByResourceUuid(dmsg, SERVICE_ID, dmsg.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format(
                                    "failed to delete model service instance group %s",
                                    reloaded.getUuid()
                            ));
                        }

                        trigger.rollback();
                    }
                });
            }
        }).then(buildSyncGroupNginxRulesFlow(Collections.singletonList(groupVO.getUuid())))
                .then(buildWaitForServiceBootUpFlow(groupVO.getUuid(), serviceBootUptime, modelCenter));
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                APIDeployModelServiceEvent event = new APIDeployModelServiceEvent(msg.getId());
                event.setInventory(ModelServiceInstanceGroupInventory.valueOf(dbf.reload(groupVO)));
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                APIEvent event = new APIEvent(msg.getId());
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    private void handle(AbstractAPIDeployModelServiceMsg msg) {
        final ModelServiceInstanceGroupVO groupVO = new ModelServiceInstanceGroupVO();
        groupVO.setModelServiceUuid(msg.getUuid());
        groupVO.setUuid(Platform.getUuid());
        groupVO.setName(msg.getName());
        groupVO.setDescription(msg.getDescription());
        groupVO.setModelUuid(msg.getModelUuid());
        groupVO.setAccountUuid(msg.getSession().getAccountUuid());
        groupVO.setStatus(ModelServiceInstanceGroupStatus.Deploying);
        groupVO.setType(msg.getType());

        ModelServiceVO service = dbf.findByUuid(msg.getUuid(), ModelServiceVO.class);
        ModelServiceInventory modelServiceInventory = ModelServiceInventory.valueOf(service);
        ModelService modelService = JSONObjectUtil.rehashObject(msg, ModelService.class);

        AIConfigYaml configYaml = buildModelServiceYaml(
                modelServiceInventory,
                null,
                modelService,
                groupVO.getUuid(),
                null
        );
        groupVO.setYaml(configYaml.toYaml());

        ModelServiceVO vo = getModelServiceByUuid(msg.getUuid());
        groupVO.setModelServiceType(vo.getType().toString());

        ModelServiceFactory factory = getFactoryByModelServiceType(vo.getType());
        if (factory != null) {
            factory.createModelService(groupVO, msg);
        } else {
            dbf.persist(groupVO);
        }

        try {
            createSystemTagFromDeployModelMsg(msg, groupVO.getUuid());
        } catch (Exception e) {
            ModelServiceInstanceGroupVO refreshedGroup = dbf.reload(groupVO);
            dbf.remove(refreshedGroup);
            APIEvent event = new APIEvent(msg.getId());
            event.setError(operr("failed to create system tag to ModelServiceInstanceGroupVO[uuid: %s], because %s", groupVO.getUuid(), e));
            bus.publish(event);
            return;
        }

        if (msg.getDatasetUuids() != null && !msg.getDatasetUuids().isEmpty()) {
            List<ModelServiceGroupDatasetRefVO> modelServiceGroupDatasetRefVOS = new ArrayList<>();
            msg.getDatasetUuids().forEach(datasetUuid -> {
                ModelServiceGroupDatasetRefVO modelServiceGroupDatasetRefVO = new ModelServiceGroupDatasetRefVO();
                modelServiceGroupDatasetRefVO.setUuid(Platform.getUuid());
                modelServiceGroupDatasetRefVO.setModelServiceInstanceGroupUuid(groupVO.getUuid());
                modelServiceGroupDatasetRefVO.setDatasetUuid(datasetUuid);
                modelServiceGroupDatasetRefVOS.add(modelServiceGroupDatasetRefVO);
            });
            dbf.persistCollection(modelServiceGroupDatasetRefVOS);
        }

        if (!CollectionUtils.isEmpty(msg.getModelServiceGroupUuids())) {
            List<ModelServiceGroupModelServiceRefVO> modelServiceGroupRefs = new ArrayList<>();
            msg.getModelServiceGroupUuids().stream().distinct().forEach(modelServiceGroupUuid -> {
                ModelServiceGroupModelServiceRefVO modelServiceGroupModelServiceRefVO = new ModelServiceGroupModelServiceRefVO();
                modelServiceGroupModelServiceRefVO.setUuid(Platform.getUuid());
                modelServiceGroupModelServiceRefVO.setModelServiceInstanceGroupUuid(groupVO.getUuid());
                modelServiceGroupModelServiceRefVO.setDependModelServiceInstanceGroupUuid(modelServiceGroupUuid);
                modelServiceGroupRefs.add(modelServiceGroupModelServiceRefVO);
            });
            dbf.persistCollection(modelServiceGroupRefs);
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("deploy-model-service");

        if (factory != null) {
            Flow beforeFlow = factory.createBeforeServiceDeployedFlow(msg, groupVO);
            if (beforeFlow != null) {
                chain.then(beforeFlow);
            }
        }

        chain.then(new NoRollbackFlow() {
            String __name__ = "copy-properties";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                aiUtils.copyTagAsModelServiceProperty(msg.getModelUuid(), groupVO.getUuid());
                trigger.next();
            }
        }).then(new Flow() {
            String __name__ = "deploy-model-service-flow";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                DeployModelServiceMsg dmsg = new DeployModelServiceMsg();
                dmsg.setUuid(groupVO.getUuid());
                dmsg.setMessageType(msg.getClass().toString());

                ModelService modelService = JSONObjectUtil.rehashObject(msg, ModelService.class);

                if (msg.getEnvironmentVariables() != null) {
                    for (AIModelServiceEnvironments env : AIModelServiceEnvironments.values()) {
                        env.applyToService(modelService, msg.getEnvironmentVariables());
                    }
                }

                LinkedHashMap linkedHashMap = JSONObjectUtil.rehashObject(modelService, LinkedHashMap.class);
                dmsg.setModelServices(Arrays.asList(linkedHashMap));

                bus.makeTargetServiceIdByResourceUuid(dmsg, AIModelManager.SERVICE_ID, dmsg.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        trigger.next();
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                ModelServiceInstanceGroupVO reloaded = dbf.findByUuid(groupVO.getUuid(), ModelServiceInstanceGroupVO.class);
                if (reloaded == null) {
                    logger.debug(String.format(
                            "model service instance group %s not exists, skip rollback",
                            groupVO.getUuid()
                    ));
                    trigger.rollback();
                    return;
                }

                DeleteModelServiceInstanceGroupMsg dmsg = new DeleteModelServiceInstanceGroupMsg();
                dmsg.setUuid(reloaded.getUuid());
                bus.makeTargetServiceIdByResourceUuid(dmsg, SERVICE_ID, dmsg.getUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format(
                                    "failed to delete model service instance group %s",
                                    reloaded.getUuid()
                            ));
                        }

                        trigger.rollback();
                    }
                });
            }
        });

        if (factory != null) {
            Flow afterFlow = factory.createAfterServiceDeployedFlow(groupVO);
            if (afterFlow != null) {
                chain.then(afterFlow);
            }
        }

        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                if (factory != null) {
                    APIEvent event = factory.createModelServiceAPIEvent(groupVO, msg);
                    bus.publish(event);
                } else {
                    APIDeployModelServiceEvent event = new APIDeployModelServiceEvent(msg.getId());
                    event.setInventory(ModelServiceInstanceGroupInventory.valueOf(dbf.reload(groupVO)));
                    bus.publish(event);
                }
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                APIEvent event = new APIEvent(msg.getId());
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();

    }

    private void createScrapeConfig(ModelServiceInstanceGroupVO group) {
        if (!resourceDestinationMaker.isManagedByUs(group.getUuid())) {
            logger.debug(String.format("invalid create scrape config request, check your group uuid: %s", group.getUuid()));
            pscMgr.deleteModelServiceInstanceGroupConfig(group.getUuid());
            return;
        }

        PrometheusConfig.StaticConfig sc = new PrometheusConfig.StaticConfig();
        HashSet<String> targets = new HashSet<>();
        group.getInstances().forEach(instance -> targets.add(instance.getUrl()));

        sc.targets = targets;
        pscMgr.writeModelServiceInstanceGroupConfig(group.getUuid(), group.getUuid(), ModelServiceInstanceGroupVO.class, sc);
    }

    private void createScrapeConfig(ModelCenterVO modelCenter) {
        if (!resourceDestinationMaker.isManagedByUs(modelCenter.getUuid())) {
            logger.debug(String.format("invalid create scrape config request, check your group uuid: %s", modelCenter.getUuid()));
            pscMgr.deleteModelServiceInstanceGroupConfig(modelCenter.getUuid());
            return;
        }

        PrometheusConfig.StaticConfig sc = new PrometheusConfig.StaticConfig();
        HashSet<String> targets = new HashSet<>();

        targets.add(String.format("%s:%d", modelCenter.getManagementIp(), AIGlobalProperty.EXPORTER_PORT));

        sc.targets = targets;
        pscMgr.writeModelCenterConfig(modelCenter.getUuid(), modelCenter.getManagementIp(), ModelCenterVO.class, sc);
    }

    private static Future trackTask = null;

    @Override
    public void afterConfigUpdated(UserProxyConfigInventory proxy, String resourceUuid) {
        ModelCenterVO mc = Q.New(ModelCenterVO.class)
                .eq(ModelCenterVO_.uuid, resourceUuid)
                .find();
        if (mc == null) {
            logger.debug(
                    String.format("resource[uuid: %s] is not ModelCenterVO, skip afterAddToResource",
                            resourceUuid
                    ));
            return;
        }

        ModelStorageBackend backend = new BentoBasedModelStorageBackend(mc);
        ModelCenterSpec spec = new ModelCenterSpec();
        spec.setProxy(proxy != null ? ModelCenterSpec.ProxySpec.fromProxyConfig(proxy) : null);
        backend.setup(spec, new Completion(null) {
            @Override
            public void success() {
                logger.debug(String.format("successfully setup proxy to model center[uuid: %s] after proxy added", mc.getUuid()));
            }

            @Override
            public void fail(ErrorCode errorCode) {
                logger.debug(String.format("failed setup proxy to model center[uuid: %s] after proxy added, because %s", mc.getUuid(), errorCode));
            }
        });
    }

    @Override
    public void managementNodeReady() {
        setupNginxConfig();
        updateScrapeConfigs();
        loadModelCenters();
    }

    private void loadModelCenters() {
        List<ModelCenterVO> modelCenterVOList = Q.New(ModelCenterVO.class)
                .list();
        for (ModelCenterVO mc : modelCenterVOList) {
            if (!resourceDestinationMaker.isManagedByUs(mc.getUuid())) {
                logger.debug(String.format("model center[uuid: %s] is not allowed to be managed by current node, untrack it", mc.getUuid()));
                tracker.untrack(mc.getUuid());
                continue;
            }
            ConnectModelCenterMsg cmsg = new ConnectModelCenterMsg();
            cmsg.setUuid(mc.getUuid());
            bus.makeLocalServiceId(cmsg, SERVICE_ID);
            bus.send(cmsg, new CloudBusCallBack(cmsg) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        logger.debug(String.format("connect model center %s success", mc.getUuid()));
                    } else {
                        logger.debug(String.format("connect model center %s failed, because %s", mc.getUuid(), reply.getError()));
                    }
                }
            });
        }
    }

    private boolean configurationExists(List<String> lines, String serverListen, String serverInclude) {
        for (String line : lines) {
            if (line.trim().equals(serverListen) || line.trim().contains(serverInclude)) {
                logger.debug(String.format("nginx configuration already exist in conf, match line: %s", line));
                return true;
            }
        }
        return false;
    }

    private void updateMainNginxConfig(String mainNginxConfigPath, String serverListen, String serverInclude) throws IOException {
        Path path = Paths.get(mainNginxConfigPath);
        String agentRedirectConf = String.format(
                "    server {\n        %s;\n        %s;\n    }\n",
                serverListen, serverInclude);

        List<String> lines = Files.readAllLines(path);

        if (configurationExists(lines, serverListen, serverInclude)) {
            return;
        }

        List<String> newLines = getRedirectNewConfLines(lines, agentRedirectConf);

        Files.write(path, newLines);
    }

    @NotNull
    private static List<String> getRedirectNewConfLines(List<String> lines, String agentRedirectConf) {
        List<String> newLines = new ArrayList<>();
        boolean httpBlockFound = false;
        boolean serverBlockAdded = false;

        for (String line : lines) {
            newLines.add(line);
            if (line.trim().equals("http {")) {
                httpBlockFound = true;
            } else if (httpBlockFound && line.trim().equals("}") && !serverBlockAdded) {
                newLines.add(agentRedirectConf);
                serverBlockAdded = true;
            }
        }

        if (!serverBlockAdded) {
            newLines.add(agentRedirectConf);
        }
        return newLines;
    }

    private void createNewConfig() throws IOException {
        String nginxConfigPath = AIGlobalConfig.NGINX_CONFIG_PATH.value(String.class);
        if (!nginxConfigPath.endsWith("/")) {
            nginxConfigPath += "/";
        }

        String nginxConfigPidFile = PathUtil.join(nginxConfigPath, AIConstants.NGINX_PID_FILE_NAME);
        String DEFAULT_CONTENT = String.format(
            "user zstack;\n" +
            "worker_processes auto;\n" +
            "error_log %1$serror.log;\n" +
            "pid %2$s;\n" +
            "include /usr/share/nginx/modules/*.conf;\n" +
            "events {\n" +
            "    worker_connections 1024;\n" +
            "}\n" +
            "http {\n" +
            "    access_log          %1$saccess.log;\n" +
            "    proxy_temp_path     /var/lib/zstack/nginx/temp/proxy;\n" +
            "    sendfile            on;\n" +
            "    tcp_nopush          on;\n" +
            "    tcp_nodelay         on;\n" +
            "    server_tokens       off;\n" +
            "    keepalive_timeout   1000;\n" +
            "    types_hash_max_size 2048;\n" +
            "    include             /etc/nginx/mime.types;\n" +
            "    default_type        application/octet-stream;\n\n" +
            "    map $http_upgrade $connection_upgrade {\n" +
            "        default upgrade;\n" +
            "        ''      close;\n" +
            "    }\n\n" +
            "}\n",
            nginxConfigPath, nginxConfigPidFile
        );

        File file = new File(PathUtil.join(nginxConfigPath, AIConstants.NGINX_CONFIG_FILE_NAME));
        if(!PathUtil.exists(nginxConfigPath)) {
            ShellUtils.run(String.format("mkdir -p %s", nginxConfigPath));
        }
        ShellUtils.run(String.format("chown -R %s:%s %s", "zstack", "zstack", nginxConfigPath));
        String tempProxyDir = "/var/lib/zstack/nginx/temp/proxy";
        if(!PathUtil.exists(tempProxyDir)) {
            ShellUtils.run(String.format("mkdir -p %s", tempProxyDir));
        }
        ShellUtils.run(String.format("chown -R %s:%s %s", "zstack", "zstack", tempProxyDir));
        Files.write(file.toPath(), DEFAULT_CONTENT.getBytes());
    }

    private void reloadNginxConfig() {
        String nginxConfigPath = AIGlobalConfig.NGINX_CONFIG_PATH.value(String.class);
        String nginxConfigFile = PathUtil.join(nginxConfigPath, AIConstants.NGINX_CONFIG_FILE_NAME);

        if (CoreGlobalProperty.UNIT_TEST_ON) {
            return;
        }

        List<String> dependencies = Arrays.asList("network.target", "remote-fs.target", "nss-lookup.target");
        ServiceUtils.installService(
                "aios-management-node-nginx",
                "aios-management-node-nginx",
                "https://zstack.io",
                dependencies,
                String.format("nginx -c %s", nginxConfigFile)
        );

        try {
            ServiceUtils.startService("aios-management-node-nginx");
        } catch (Exception e) {
            logger.warn(String.format("failed to start nginx service, because %s", e.getMessage()));
        }
    }

    private void createAllNginxConf(String modelCenterManagementIp) {
        String serverPort = String.valueOf(AIGlobalConfig.AGENT_MODEL_SERVICE_NGINX_LISTEN_PORT.value(Integer.class));
        String browserPort = String.valueOf(AIGlobalConfig.AGENT_FILE_BROWSER_NGINX_LISTEN_PORT.value(Integer.class));
        String jupyterPort = String.valueOf(AIGlobalConfig.JUPYTER_SERVICE_NGINX_LISTEN_PORT.value(Integer.class));

        String serverRedirectDest = String.format("%s:%s", modelCenterManagementIp, serverPort);
        String browserRedirectDest = String.format("%s:%s", modelCenterManagementIp, browserPort);
        String jupyterRedirectDest = String.format("%s:%s", modelCenterManagementIp, jupyterPort);

        String aiMNNginxConfigPath = AIGlobalConfig.AI_SERVICE_NGINX_CONFIG_PATH.value(String.class);
        if (!PathUtil.exists(aiMNNginxConfigPath)) {
            boolean createMnNginxResult = new File(aiMNNginxConfigPath).mkdirs();
            if (!createMnNginxResult) {
                throw new CloudRuntimeException(String.format("Failed to create model service nginx config path %s", aiMNNginxConfigPath));
            }
        }

        String fileBrowserNginxConfigPath = AIGlobalConfig.AI_FILE_BROWSER_NGINX_CONFIG_PATH.value(String.class);
        if (!PathUtil.exists(fileBrowserNginxConfigPath)) {
            boolean createBrowserNginxConfigResult = new File(fileBrowserNginxConfigPath).mkdirs();
            if (!createBrowserNginxConfigResult) {
                throw new CloudRuntimeException(String.format("Failed to create file browser nginx config path %s", fileBrowserNginxConfigPath));
            }
        }

        String jupyterNginxConfigPath = AIGlobalConfig.JUPYTER_NGINX_CONFIG_PATH.value(String.class);
        if (!PathUtil.exists(jupyterNginxConfigPath)) {
            boolean createJupyterNginxDirResult = new File(jupyterNginxConfigPath).mkdirs();
            if (!createJupyterNginxDirResult) {
                throw new CloudRuntimeException(String.format("Failed to create jupyter nginx config path %s", jupyterNginxConfigPath));
            }
        }
        // create model service redirect conf
        createSubNginxConf(serverRedirectDest, aiMNNginxConfigPath, AIConstants.SERVICE_NGINX_CONFIG_FILE_NAME);
        // create file browser redirect conf
        createSubNginxConf(jupyterRedirectDest, jupyterNginxConfigPath, AIConstants.JUPYTER_NGINX_CONFIG_FILE_NAME);
        // create file browser redirect conf
        createSubNginxConf(browserRedirectDest, fileBrowserNginxConfigPath, AIConstants.FILE_BROWSER_NGINX_CONFIG_FILE_NAME);
    }

    private void createSubNginxConf(String redirectDest, String dir, String fileName) {
        String DEFAULT_CONTENT = String.format(
                "location / {\n" +
                        "    proxy_pass http://%s;\n" +
                        "    proxy_http_version 1.1;\n" +
                        "    proxy_set_header Upgrade $http_upgrade;\n" +
                        "    proxy_set_header Connection 'upgrade';\n" +
                        "    proxy_set_header Host $host;\n" +
                        "    proxy_set_header X-Real-IP $remote_addr;\n" +
                        "    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n" +
                        "    proxy_set_header X-Forwarded-Proto $scheme;\n" +
                        "    proxy_cache_bypass $http_upgrade;\n" +
                        "    # 错误日志配置\n" +
                        "    error_log %2$sai_error.log;\n" +
                        "    access_log %2$sai_access.log;\n" +
                        "}\n",
                redirectDest, dir
        );

        try {
            File file = new File(PathUtil.join(dir, fileName));
            if(!PathUtil.exists(dir)) {
                boolean subDirNginxConfig = new File(dir).mkdirs();
                if (!subDirNginxConfig) {
                    throw new CloudRuntimeException(String.format("Failed to create sub nginx config path %s", dir));
                }
            }
            Files.write(file.toPath(), DEFAULT_CONTENT.getBytes());
        } catch (IOException e) {
            throw new CloudRuntimeException(e);
        }
    }

    private void setupNginxConfig() {
        String nginxConfigPath = AIGlobalConfig.NGINX_CONFIG_PATH.value(String.class);
        File file = new File(PathUtil.join(nginxConfigPath, AIConstants.NGINX_CONFIG_FILE_NAME));

        // prepare model service nginx config dirs
        String aiMNNginxConfigPath = AIGlobalConfig.AI_SERVICE_NGINX_CONFIG_PATH.value(String.class);
        String serverPort = String.valueOf(AIGlobalConfig.AGENT_MODEL_SERVICE_NGINX_LISTEN_PORT.value(Integer.class));
        String serverListen = String.format("listen %s", serverPort);
        String serverInclude = String.format("include %s*.conf", aiMNNginxConfigPath);

        String fileBrowserNginxConfigPath = AIGlobalConfig.AI_FILE_BROWSER_NGINX_CONFIG_PATH.value(String.class);
        String browserPort = String.valueOf(AIGlobalConfig.AGENT_FILE_BROWSER_NGINX_LISTEN_PORT.value(Integer.class));
        String browserListen = String.format("listen %s", browserPort);
        String browserInclude = String.format("include %s*.conf", fileBrowserNginxConfigPath);

        String jupyterNginxConfigPath = AIGlobalConfig.JUPYTER_NGINX_CONFIG_PATH.value(String.class);
        String jupyterPort = String.valueOf(AIGlobalConfig.JUPYTER_SERVICE_NGINX_LISTEN_PORT.value(Integer.class));
        String jupyterListen = String.format("listen %s", jupyterPort);
        String jupyterInclude = String.format("include %s*.conf", jupyterNginxConfigPath);
        List<ModelCenterVO> modelCenterVOs = Q.New(ModelCenterVO.class).list();

        try {
            if (!Files.exists(file.toPath())){
                createNewConfig();
            }

            // update model service redirect rule in main nginx conf
            updateMainNginxConfig(file.getPath(), serverListen, serverInclude);
            // update file browser redirect rule in main nginx conf
            updateMainNginxConfig(file.getPath(), browserListen, browserInclude);
            // update jupyter redirect rule in main nginx conf
            updateMainNginxConfig(file.getPath(), jupyterListen, jupyterInclude);

            createAllNginxConf(modelCenterVOs.get(0).getManagementIp());
        } catch (IOException e) {
            throw new CloudRuntimeException(e);
        }

        // reload nginx
        reloadNginxConfig();
    }

    public static class EvaluationTaskResult {
        private ModelEvaluationTaskStatus status;
        private ErrorCode error;
        private LinkedHashMap details;

        public ModelEvaluationTaskStatus getStatus() {
            return status;
        }

        public void setStatus(ModelEvaluationTaskStatus status) {
            this.status = status;
        }

        public ErrorCode getError() {
            return error;
        }

        public void setError(ErrorCode error) {
            this.error = error;
        }

        public LinkedHashMap getDetails() {
            return details;
        }

        public void setDetails(LinkedHashMap details) {
            this.details = details;
        }

        public static EvaluationTaskResult formatErrorResult(ErrorCode errorCode) {
            EvaluationTaskResult result = new EvaluationTaskResult();
            result.setError(errorCode);
            result.setStatus(ModelEvaluationTaskStatus.Failed);
            return result;
        }

        public static EvaluationTaskResult formatEmptyResult() {
            EvaluationTaskResult result = new EvaluationTaskResult();
            result.setStatus(ModelEvaluationTaskStatus.Failed);
            return result;
        }

        public static EvaluationTaskResult formatRunningResult(ModelEvaluationTaskStatus status, LinkedHashMap opaque) {
            EvaluationTaskResult result = new EvaluationTaskResult();
            result.setStatus(status);
            result.setDetails(opaque);
            return result;
        }
    }

    @Synchronized
    private void trackModelServiceInstanceGroup() {
        if (trackTask != null) {
            trackTask.cancel(true);
        }
        trackTask = thdf.submitPeriodicTask(new ModelServiceInstanceGroupTracker());
    }

    private void updateScrapeConfigs() {
        List<ModelServiceInstanceGroupVO> groups = Q.New(ModelServiceInstanceGroupVO.class)
                .list();

        groups.forEach(this::createScrapeConfig);

        List<ModelCenterVO> modelCenterVOS = Q.New(ModelCenterVO.class)
                .list();

        modelCenterVOS.forEach(this::createScrapeConfig);
    }

    @Override
    public void nodeJoin(ManagementNodeInventory inv) {
        updateScrapeConfigs();
        loadModelCenters();
    }

    @Override
    public void nodeLeft(ManagementNodeInventory inv) {
        updateScrapeConfigs();
        loadModelCenters();
    }

    @Override
    public void iAmDead(ManagementNodeInventory inv) {

    }

    @Override
    public void iJoin(ManagementNodeInventory inv) {

    }

    @Override
    public List<Quota> reportQuota() {
        Quota quota = new Quota();
        QuotaDefBuilder builder = new QuotaDefBuilder();
        builder.name(AIQuotaConstant.MODEL_CAPACITY);
        builder.defaultValue(AIQuotaGlobalConfig.MODEL_CAPACITY.defaultValue(Long.class));
        builder.repairValue(0L);
        builder.getUsage((accountUuid, name) -> {
            Long usage = SQL.New("select sum(model.size) from ModelVO model, " +
                    "AccountResourceRefVO ref where " +
                    "ref.accountUuid = :accountUuid and " +
                    "ref.resourceType = :rtype and " +
                    "ref.resourceUuid = model.uuid", Long.class)
                    .param("accountUuid", accountUuid)
                    .param("rtype", ModelVO.class.getSimpleName())
                    .find();

            if (usage == null) {
                return 0L;
            }

            return usage;
        });
        quota.defineQuota(builder.build());
        quota.addQuotaMessageChecker(new QuotaMessageHandler<>(APIAddModelMsg.class)
                .addFixedRequiredSize(AIQuotaConstant.MODEL_CAPACITY, 1L));

        builder = new QuotaDefBuilder();
        builder.name(AIQuotaConstant.DATASET_CAPACITY);
        builder.defaultValue(AIQuotaGlobalConfig.DATASET_CAPACITY.defaultValue(Long.class));
        builder.repairValue(0L);
        builder.getUsage((accountUuid, name) -> {
            Long usage = SQL.New("select sum(dataset.size) from DatasetVO dataset, " +
                            "AccountResourceRefVO ref where " +
                            "ref.accountUuid = :accountUuid and " +
                            "ref.resourceType = :rtype and " +
                            "ref.resourceUuid = dataset.uuid", Long.class)
                    .param("accountUuid", accountUuid)
                    .param("rtype", DatasetVO.class.getSimpleName())
                    .find();

            if (usage == null) {
                return 0L;
            }

            return usage;
        });
        quota.defineQuota(builder.build());
        quota.addQuotaMessageChecker(new QuotaMessageHandler<>(APICreateDatasetMsg.class)
                .addFixedRequiredSize(AIQuotaConstant.DATASET_CAPACITY, 1L));

        builder = new QuotaDefBuilder();
        builder.name(AIQuotaConstant.MODEL_SERVICE_CAPACITY);
        builder.defaultValue(AIQuotaGlobalConfig.MODEL_SERVICE_CAPACITY.defaultValue(Long.class));
        builder.repairValue(0L);
        builder.getUsage((accountUuid, name) -> {
            Long usage = SQL.New("select sum(ms.size) from ModelServiceVO ms, " +
                            "AccountResourceRefVO ref where " +
                            "ref.accountUuid = :accountUuid and " +
                            "ref.resourceType = :rtype and " +
                            "ref.resourceUuid = ms.uuid", Long.class)
                    .param("accountUuid", accountUuid)
                    .param("rtype", ModelServiceVO.class.getSimpleName())
                    .find();

            if (usage == null) {
                return 0L;
            }

            return usage;
        });
        quota.defineQuota(builder.build());
        quota.addQuotaMessageChecker(new QuotaMessageHandler<>(APIAddModelServiceMsg.class)
                .addFixedRequiredSize(AIQuotaConstant.MODEL_SERVICE_CAPACITY, 1L));

        return Collections.singletonList(quota);
    }

    static class SystemDefinedTag {
        private String uuid;
        private String color;
        private String name;
        private String tag;
        private String desc;

        SystemDefinedTag(String uuid, String color, String name, String tag, String desc) {
            this.uuid = uuid;
            this.color = color;
            this.name = name;
            this.tag = tag;
            this.desc = desc;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
    private static List<SystemDefinedTag> tags = new ArrayList<>();
    static {
        tags.add(new SystemDefinedTag("3e88dfda748f4706a497143ff0c9d8bc",
                "red",
                "AI::Others",
                "AI::Others",
                "System Defined tag"));
        // LLM
        tags.add(new SystemDefinedTag("f2a1b3c4d5e6a7b8c9d0e1f2a3b4c5d6",
                "blue",
                "AI::LLM",
                "AI::LLM",
                "System Defined tag"));

        // Audio
        tags.add(new SystemDefinedTag("a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
                "teal",
                "AI::Audio",
                "AI::Audio",
                "System Defined tag"));

        // Image
        tags.add(new SystemDefinedTag("b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7",
                "violet",
                "AI::Image",
                "AI::Image",
                "System Defined tag"));

        // Rerank
        tags.add(new SystemDefinedTag("c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8",
                "purple",
                "AI::Rerank",
                "AI::Rerank",
                "System Defined tag"));

        // Text Embedding
        tags.add(new SystemDefinedTag("d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9",
                "yellow",
                "AI::TextEmbedding",
                "AI::TextEmbedding",
                "System Defined tag"));

        // Multimodal
        tags.add(new SystemDefinedTag("e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
                "green",
                "AI::Multimodal",
                "AI::Multimodal",
                "System Defined tag"));
    }

    @Override
    public void prepareDbInitialValue() {
        new SQLBatch() {
            @Override
            protected void scripts() {
                tags.forEach(tag -> {
                    TagPatternVO vo = findByUuid(tag.getUuid(), TagPatternVO.class);
                    if (vo != null) {
                        return;
                    }

                    vo = new TagPatternVO();
                    vo.setUuid(tag.getUuid());
                    vo.setValue(tag.getTag());
                    vo.setName(tag.getName());
                    vo.setDescription(tag.getDesc());
                    vo.setColor(tag.getColor());
                    vo.setType(TagPatternType.simple);
                    vo.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
                    persist(vo);
                });
            }
        }.execute();
        SystemModels systemModels = new SystemModels();
        String modelCenterUuid = systemModels.initModelCenter();
        SystemDatasets datasets = new SystemDatasets();
        datasets.initSystemDatasets(modelCenterUuid);
        try {
            logger.info("Initializing system development applications...");
            SystemDevelopmentApplications systemDevelopmentApplications = new SystemDevelopmentApplications();
            systemDevelopmentApplications.initSystemDevelopmentApplications(modelCenterUuid);
            logger.info("Successfully initialized system development applications");
        } catch (Exception e) {
            logger.error("Failed to initialize system development applications", e);
            throw new CloudRuntimeException("Failed to initialize system development applications", e);
        }
    }


    public class ModelServiceInstanceGroupTracker implements PeriodicTask {
        private final String name = "model-service-instance-group-tracker";

        @Override
        public TimeUnit getTimeUnit() {
            return TimeUnit.SECONDS;
        }

        @Override
        public long getInterval() {
            return AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.value(Long.class);
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public void run() {
            track();
        }

        private void doTrackOneGroupStatus(ModelServiceInstanceGroupVO group) {
            if (!resourceDestinationMaker.isManagedByUs(group.getUuid())) {
                logger.trace(String.format("Model service instance group %s is not managed by us", group.getUuid()));
                return;
            }

            String modelCenterManagementIp;
            if (group.getModelUuid() != null) {
                modelCenterManagementIp = SQL.New("select managementIp from ModelCenterVO where " +
                                "uuid = (select modelCenterUuid from ModelVO where uuid= :modelUuid)", String.class)
                        .param("modelUuid", group.getModelUuid())
                        .find();
            } else {
                modelCenterManagementIp = SQL.New("select managementIp from ModelCenterVO where " +
                                "uuid = (select modelCenterUuid from ModelServiceVO where uuid= :modelServiceUuid)", String.class)
                        .param("modelServiceUuid", group.getModelServiceUuid())
                        .find();
            }

            if (modelCenterManagementIp == null) {
                logger.debug(String.format("cannot get redirect management ip of group[uuid: %s]", group.getUuid()));
                return;
            }

            ConcurrentHashMap<String, String> instanceStatus = new ConcurrentHashMap<>();
            new While<>(group.getInstances()).each((instance, whileCompletion) -> {
                try {
                    checkInstanceIsAlive(instance, instanceStatus, modelCenterManagementIp, whileCompletion);
                } catch (Exception e) {
                    instanceStatus.put(instance.getUuid(), ModelServiceInstanceGroupStatus.Unknown.toString());
                    whileCompletion.done();
                }
            }).run(new WhileDoneCompletion(null) {
                @Override
                public void done(ErrorCodeList errorCodeList) {
                    updateGroupStatusInQueue(group, instanceStatus);
                }
            });
        }

        private void checkInstanceIsAlive(ModelServiceInstanceVO instance, ConcurrentHashMap<String, String> instanceStatus, String modelCenterManagementIp, WhileCompletion whileCompletion) {
            if (instance == null) {
                whileCompletion.done();
                return;
            }

            ModelServiceInstanceInventory modelServiceInstanceInventory = new ModelServiceInstanceInventory(instance);

            isAlive(modelServiceInstanceInventory, modelCenterManagementIp, new Completion(whileCompletion) {
                @Override
                public void success() {
                    instanceStatus.put(instance.getUuid(), ModelServiceInstanceGroupStatus.Running.toString());
                    whileCompletion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    instanceStatus.put(instance.getUuid(), ModelServiceInstanceGroupStatus.Unknown.toString());
                    whileCompletion.addError(errorCode);
                    whileCompletion.done();
                }
            });
        }

        @ExceptionSafe
        public void track() {
            List<ModelServiceInstanceGroupVO> groups = Q.New(ModelServiceInstanceGroupVO.class)
                    .notIn(ModelServiceInstanceGroupVO_.status, intermediateStatus)
                    .list();

            groups.forEach(this::doTrackOneGroupStatus);
        }
    }

    private void updateGroupStatusInQueue(ModelServiceInstanceGroupVO group, ConcurrentHashMap<String, String> instanceStatus) {
        thdf.chainSubmit(new ChainTask(null) {
            @Override
            public void run(SyncTaskChain chain) {
                Map<String, List<String>> statusMap = new HashMap<>();
                new SQLBatch() {
                    @Override
                    protected void scripts() {
                        instanceStatus.forEach((uuid ,status) -> {
                            sql(ModelServiceInstanceVO.class)
                                    .set(ModelServiceInstanceVO_.status, status)
                                    .eq(ModelServiceInstanceVO_.uuid, uuid)
                                    .update();

                            statusMap.putIfAbsent(status, new ArrayList<>());
                            statusMap.get(status).add(uuid);
                        });

                        ModelServiceInstanceGroupVO reloaded = findByUuid(group.getUuid(), ModelServiceInstanceGroupVO.class);
                        if (reloaded == null) {
                            return;
                        }

                        ModelServiceInstanceGroupStatus origin = reloaded.getStatus();
                        List<String> runningInstance = statusMap.get(ModelServiceInstanceGroupStatus.Running.toString());
                        if (runningInstance != null && runningInstance.size() == reloaded.getInstances().size()) {
                            reloaded.setStatus(ModelServiceInstanceGroupStatus.Running);
                        }

                        List<String> unknown = statusMap.get(ModelServiceInstanceGroupStatus.Unknown.toString());
                        if (unknown != null && unknown.size() == reloaded.getInstances().size()) {
                            reloaded.setStatus(ModelServiceInstanceGroupStatus.Unknown);
                        }

                        logger.debug(String.format("Update model service instance group %s status from %s to %s", reloaded.getUuid(), origin, reloaded.getStatus()));
                        merge(reloaded);
                    }
                }.execute();
                chain.next();
            }

            @Override
            public String getSyncSignature() {
                return "model-service-instance-status-queue";
            }

            @Override
            public String getName() {
                return "group-status";
            }
        });
    }

    private void handle(ConnectModelCenterMsg msg) {
        ModelCenterVO modelCenterVO = dbf.findByUuid(msg.getUuid(), ModelCenterVO.class);
        if (modelCenterVO == null) {
            throw new OperationFailureException(operr("cannot find model center[uuid: %s]", msg.getUuid()));
        }

        ConnectModelCenterReply reply = new ConnectModelCenterReply();

        ModelStorageBackend backend = new BentoBasedModelStorageBackend(modelCenterVO);
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("connect-model-center-flow-chain");
        chain.then(new NoRollbackFlow() {
            String __name__ = "setup-model-center-agent";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                ModelCenterSpec spec = buildModelCenterSpecFromVO(modelCenterVO);

                backend.setup(spec, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "deploy-zdfs-agent";

            public NeedReplyMessage prepareMsg() {
                if (modelCenterVO.getZdfs() == null) {
                    AddZdfsMsg msg = new AddZdfsMsg();
                    msg.setHostName(modelCenterVO.getManagementIp());
                    msg.setUrl(modelCenterVO.getUrl());
                    bus.makeTargetServiceIdByResourceUuid(msg, ZdfsConstant.SERVICE_ID, modelCenterVO.getUuid());
                    return msg;
                }

                ConnectZdfsMsg msg = new ConnectZdfsMsg();
                msg.setUuid(modelCenterVO.getZdfs().getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, ZdfsConstant.SERVICE_ID, modelCenterVO.getZdfs().getUuid());
                return msg;
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                NeedReplyMessage msg = prepareMsg();
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.next();
                            return;
                        }

                        if (msg instanceof AddZdfsMsg) {
                            AddZdfsReply reply1 = reply.castReply();
                            modelCenterVO.setZdfs(dbf.findByUuid(reply1.getInv().getUuid(), ZdfsVO.class));
                            dbf.update(modelCenterVO);
                        }

                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                createScrapeConfig(modelCenterVO);
                tracker.track(modelCenterVO.getUuid());
                bus.reply(msg, reply);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                deleteModelCenterScrapeConfig(modelCenterVO.getUuid());
                tracker.untrack(modelCenterVO.getUuid());
                reply.setError(errCode);
                bus.reply(msg, reply);
            }
        }).start();
    }

    private void handle(APIAddModelCenterMsg msg) {
        APIAddModelCenterEvent event = new APIAddModelCenterEvent(msg.getId());

        ModelCenterVO modelCenterVO = getModelCenterVO(msg);
        dbf.persist(modelCenterVO);

        ConnectModelCenterMsg amsg = new ConnectModelCenterMsg();
        amsg.setUuid(modelCenterVO.getUuid());
        bus.makeTargetServiceIdByResourceUuid(amsg, SERVICE_ID, modelCenterVO.getUuid());
        bus.send(amsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    ModelCenterVO reloaded = dbf.reload(modelCenterVO);
                    event.setInventory(ModelCenterInventory.valueOf(reloaded));
                    bus.publish(event);
                } else {
                    dbf.remove(modelCenterVO);
                    event.setError(reply.getError());
                    bus.publish(event);
                }
            }
        });
    }

    private ModelCenterSpec buildModelCenterSpecFromVO(ModelCenterVO modelCenterVO) {
        ModelCenterSpec spec = new ModelCenterSpec();
        UserProxyConfigResourceRefVO ref = Q.New(UserProxyConfigResourceRefVO.class)
                .eq(UserProxyConfigResourceRefVO_.resourceUuid, modelCenterVO.getUuid())
                .find();
        if (ref != null) {
            UserProxyConfigVO proxy = dbf.findByUuid(ref.getProxyUuid(), UserProxyConfigVO.class);
            spec.setProxy(ModelCenterSpec.ProxySpec.fromProxyConfig(proxy));
        }

        return spec;
    }

    @NotNull
    private static ModelCenterVO getModelCenterVO(APIAddModelCenterMsg msg) {
        ModelCenterVO modelCenterVO = new ModelCenterVO();
        if (msg.getResourceUuid() != null) {
            modelCenterVO.setUuid(msg.getResourceUuid());
        } else {
            modelCenterVO.setUuid(Platform.getUuid());
        }
        modelCenterVO.setZoneUuid(msg.getZoneUuid());
        modelCenterVO.setDescription(msg.getDescription());
        modelCenterVO.setName(msg.getName());
        modelCenterVO.setParameters(msg.getParameters());
        modelCenterVO.setUrl(msg.getUrl());
        modelCenterVO.setManagementIp(msg.getManagementIp());
        modelCenterVO.setManagementPort(msg.getManagementPort());
        modelCenterVO.setStatus(ModelCenterStatus.Connected);
        return modelCenterVO;
    }

    private void handle(APIGetModelCenterServicesMsg msg) {
        APIGetModelCenterServicesReply reply = new APIGetModelCenterServicesReply();
        List<ModelCenterServiceInventory> inventories = new ArrayList<>();

        List<ModelCenterVO> vos;
        if (!CollectionUtils.isEmpty(msg.getModelCenterUuids())) {
            vos = Q.New(ModelCenterVO.class)
                    .in(ModelCenterVO_.uuid, msg.getModelCenterUuids())
                    .list();
        } else {
            vos = Q.New(ModelCenterVO.class)
                    .list();
        }

        new While<>(vos).each((mc, whileCompletion) -> {
            BentoBasedModelStorageBackend backend = new BentoBasedModelStorageBackend(mc);
            backend.listServices(new ReturnValueCompletion<ModelCenterServiceInventory>(whileCompletion) {
                @Override
                public void success(ModelCenterServiceInventory inventory) {
                    if (inventory == null) {
                        whileCompletion.done();
                        return;
                    }

                    inventories.add(inventory);
                    whileCompletion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    whileCompletion.addError(errorCode);
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    APIGetModelCenterServicesReply reply = new APIGetModelCenterServicesReply();
                    reply.setError(errorCodeList.getCauses().get(0));
                    bus.reply(msg, reply);
                    return;
                }

                reply.setServices(inventories);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(APIUpdateModelCenterMsg msg) {
        APIUpdateModelCenterEvent event = new APIUpdateModelCenterEvent(msg.getId());
        ModelCenterVO originCopy = dbf.findByUuid(msg.getUuid(), ModelCenterVO.class);

        ModelCenterVO modelCenterVO = Q.New(ModelCenterVO.class)
                        .eq(ModelCenterVO_.uuid, msg.getUuid())
                        .find();
        if (msg.getDescription() != null) {
            modelCenterVO.setDescription(msg.getDescription());
        }
        if (msg.getName() != null) {
            modelCenterVO.setName(msg.getName());
        }
        if (msg.getParameters() != null) {
            modelCenterVO.setParameters(msg.getParameters());
        }
        if (msg.getContainerRegistry() != null) {
            modelCenterVO.setContainerRegistry(msg.getContainerRegistry());
        }

        if (msg.getServiceNetworkUuid() != null) {
            modelCenterVO.setServiceNetworkUuid(msg.getServiceNetworkUuid());
        }

        if (msg.getUrl() != null) {
            modelCenterVO.setUrl(msg.getUrl());
        }

        if (msg.getStorageNetworkUuid() != null) {
            modelCenterVO.setStorageNetworkUuid(msg.getStorageNetworkUuid());
        }

        if (msg.getContainerNetwork() != null) {
            modelCenterVO.setContainerNetwork(msg.getContainerNetwork());
        }

        if (msg.getContainerStorageNetwork() != null) {
            modelCenterVO.setContainerStorageNetwork(msg.getContainerStorageNetwork());
        }

        if (msg.getManagementPort() != null) {
            modelCenterVO.setManagementPort(msg.getManagementPort());
        }

        if (msg.getManagementIp() != null) {
            modelCenterVO.setManagementIp(msg.getManagementIp());
            dbf.updateAndRefresh(modelCenterVO);
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName("reload mn nginx");
        chain.then(new NoRollbackFlow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<String> managementNodeUuids = Q.New(ManagementNodeVO.class)
                        .select(ManagementNodeVO_.uuid)
                        .listValues();
                new While<>(managementNodeUuids).each((uuid, completion) -> {
                    SyncAINginxConfigurationMsg syncAINginxConfigurationMsg = new SyncAINginxConfigurationMsg();
                    syncAINginxConfigurationMsg.setReloadMNNginx(true);
                    bus.makeServiceIdByManagementNodeId(syncAINginxConfigurationMsg, SERVICE_ID, uuid);
                    bus.send(syncAINginxConfigurationMsg, new CloudBusCallBack(completion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                completion.addError(reply.getError());
                            }
                            completion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (!errorCodeList.getCauses().isEmpty()) {
                            trigger.fail(errorCodeList);
                            return;
                        }

                        trigger.next();
                    }
                });
            }

            @Override
            public boolean skip(Map data) {
                if (StringUtils.isEmpty(msg.getManagementIp())) {
                    return true;
                }
                return false;
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "update-zdfs-config";

            @Override
            public boolean skip(Map data) {
                return msg.getManagementIp() == null ||
                        modelCenterVO.getZdfs() == null;
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                UpdateZdfsMsg umsg = new UpdateZdfsMsg();
                umsg.setUuid(modelCenterVO.getZdfs().getUuid());
                umsg.setHostName(modelCenterVO.getManagementIp());
                umsg.setUrl(modelCenterVO.getUrl());
                bus.makeTargetServiceIdByResourceUuid(umsg, ZdfsConstant.SERVICE_ID, umsg.getUuid());
                bus.send(umsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        trigger.next();
                    }
                });

            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                dbf.update(modelCenterVO);
                ModelCenterInventory modelCenterInventory = new ModelCenterInventory(modelCenterVO);
                event.setInventory(modelCenterInventory);
                bus.publish(event);
                createScrapeConfig(modelCenterVO);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                dbf.updateAndRefresh(originCopy);
                ModelCenterInventory modelCenterInventory = new ModelCenterInventory(originCopy);
                event.setInventory(modelCenterInventory);
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    private void handle(APIDeleteModelCenterMsg msg) {
        final APIDeleteModelCenterEvent evt = new APIDeleteModelCenterEvent(msg.getId());
        final String issuer = ModelCenterVO.class.getSimpleName();
        ModelCenterVO modelCenter = Q.New(ModelCenterVO.class).eq(ModelCenterVO_.uuid, msg.getUuid()).find();
        if (modelCenter == null) {
            logger.debug(String.format("cannot find model center[uuid: %s]", msg.getUuid()));
            bus.publish(evt);
            return;
        }

        final List<ModelCenterInventory> ctx = Arrays.asList(ModelCenterInventory.valueOf(modelCenter));
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-model-center-%s", msg.getUuid()));
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }

        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(evt);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                evt.setError(err(SysErrors.DELETE_RESOURCE_ERROR, errCode, errCode.getDetails()));
                bus.publish(evt);
            }
        }).start();
    }

    private void handle(APIAddModelMsg msg) {
        APIAddModelEvent event = new APIAddModelEvent(msg.getId());

        AddModelMsg amsg = new AddModelMsg();
        amsg.setApiMessage(msg);
        bus.makeTargetServiceIdByResourceUuid(amsg, SERVICE_ID, msg.getClass().getSimpleName());
        bus.send(amsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    event.setError(reply.getError());
                    bus.publish(event);
                    return;
                }

                AddModelReply r = reply.castReply();
                event.setInventory(r.getInventory());
                bus.publish(event);
            }
        });
    }

    private void handle(APIUpdateModelMsg msg) {
        APIUpdateModelEvent event = new APIUpdateModelEvent(msg.getId());

        ModelVO modelVO = Q.New(ModelVO.class)
                        .eq(ModelVO_.uuid, msg.getUuid())
                        .find();
        if (msg.getDescription() != null) {
            modelVO.setDescription(msg.getDescription());
        }

        if (msg.getName() != null) {
            modelVO.setName(msg.getName());
        }

        if (msg.getParameters() != null) {
            modelVO.setParameters(msg.getParameters());
        }

        if (msg.getModelCenterUuid() != null) {
            modelVO.setModelCenterUuid(msg.getModelCenterUuid());
        }

        if (msg.getInstallPath() != null) {
            modelVO.setInstallPath(msg.getInstallPath());
        }

        if (!CollectionUtils.isEmpty(msg.getModelClassifications())) {
            SQL.New(UserTagVO.class)
                    .like(UserTagVO_.tag, "AI::%%")
                    .eq(UserTagVO_.resourceType, ModelVO.class.getSimpleName())
                    .eq(UserTagVO_.resourceUuid, modelVO.getUuid())
                    .hardDelete();
            List<UserTagVO> userTagVOS = new ArrayList<>();
            for (String classification : msg.getModelClassifications()) {
                UserTagVO userTagVO = new UserTagVO();
                switch (classification) {
                    case "AI::Others":
                        userTagVO.setTagPatternUuid(AIConstants.OTHER_TAG_PATTERN_UUID);
                        break;
                    case "AI::LLM":
                        userTagVO.setTagPatternUuid(AIConstants.LLM_TAG_PATTERN_UUID);
                        break;
                    case "AI::Audio":
                        userTagVO.setTagPatternUuid(AIConstants.AUDIO_PATTERN_UUID);
                        break;
                    case "AI::Image":
                        userTagVO.setTagPatternUuid(AIConstants.IMAGE_PATTERN_UUID);
                        break;
                    case "AI::Rerank":
                        userTagVO.setTagPatternUuid(AIConstants.RERANK_PATTERN_UUID);
                        break;
                    case "AI::TextEmbedding":
                        userTagVO.setTagPatternUuid(AIConstants.TEXT_EMBEDDING_PATTERN_UUID);
                        break;
                    case "AI::Multimodal":
                        userTagVO.setTagPatternUuid(AIConstants.MULTI_MODEL_PATTERN_UUID);
                        break;
                    default:
                        throw new OperationFailureException(operr("Invalid model classification: %s", classification));
                }

                userTagVO.setResourceType(ModelVO.class.getSimpleName());
                userTagVO.setResourceUuid(modelVO.getUuid());
                userTagVO.setUuid(Platform.getUuid());
                userTagVO.setTag(classification);
                userTagVOS.add(userTagVO);
            }
            userTagVOS.forEach(userTagVO -> dbf.persist(userTagVO));
        }

        modelVO = dbf.updateAndRefresh(modelVO);
        ModelInventory modelInventory = new ModelInventory(modelVO);
        event.setInventory(modelInventory);
        bus.publish(event);
    }

    private void handle(APIDeleteDatasetsMsg msg) {
        APIDeleteDatasetsEvent event = new APIDeleteDatasetsEvent(msg.getId());
        List<APIBatchRequest.BatchOperationResult<Void>> results = Collections.synchronizedList(new ArrayList<>());

        new While<>(msg.getUuids()).each((datasetUuid, whileCompletion) -> {
            APIBatchRequest.BatchOperationResult<Void> result = new APIBatchRequest.BatchOperationResult<Void>();
            result.setUuid(datasetUuid);
            doDeleteDataset(datasetUuid, new Completion(whileCompletion) {
                @Override
                public void success() {
                    result.setSuccess(true);
                    results.add(result);
                    whileCompletion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    result.setSuccess(false);
                    result.setError(errorCode);
                    results.add(result);
                    whileCompletion.addError(errorCode);
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                event.setResults(results);
                bus.publish(event);
            }
        });
    }

    private void handle(APIDeleteModelsMsg msg) {
        APIDeleteModelsEvent event = new APIDeleteModelsEvent(msg.getId());
        List<APIBatchRequest.BatchOperationResult<Void>> results = Collections.synchronizedList(new ArrayList<>());
        new While<>(msg.getUuids()).each((modelUuid, whileCompletion) -> {
            APIBatchRequest.BatchOperationResult<Void> result = new APIBatchRequest.BatchOperationResult<Void>();
            result.setUuid(modelUuid);
            doDeleteModel(modelUuid, new Completion(whileCompletion) {
                @Override
                public void success() {
                    result.setSuccess(true);
                    results.add(result);
                    whileCompletion.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    result.setSuccess(false);
                    result.setError(errorCode);
                    results.add(result);
                    whileCompletion.addError(errorCode);
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                event.setResults(results);
                bus.publish(event);
            }
        });
    }

    private void handle(APIDeleteModelMsg msg) {
        APIDeleteModelEvent event = new APIDeleteModelEvent(msg.getId());
        doDeleteModel(msg.getUuid(), new Completion(msg) {
            @Override
            public void success() {
                bus.publish(event);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                event.setError(errorCode);
                bus.publish(event);
            }
        });
    }

    private void doDeleteModel(String modelUuid, Completion completion) {
        ModelVO model = Q.New(ModelVO.class)
                .eq(ModelVO_.uuid, modelUuid)
                .find();

        if (model == null) {
            completion.success();
            return;
        }

        ModelCenterVO center = dbf.findByUuid(model.getModelCenterUuid(), ModelCenterVO.class);
        ModelStorageBackend backend = new BentoBasedModelStorageBackend(center);
        backend.deleteModel(model.getVersion(), model.getInstallPath(), new Completion(completion) {
            @Override
            public void success() {
                SQL.New(ModelVO.class)
                        .eq(ModelVO_.uuid, modelUuid)
                        .delete();
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void handle(APIAddModelServiceMsg msg) {
        APIAddModelServiceEvent event = new APIAddModelServiceEvent(msg.getId());

        AddModelServiceMsg amsg = new AddModelServiceMsg();
        amsg.setApiMessage(msg);
        bus.makeTargetServiceIdByResourceUuid(amsg, SERVICE_ID, msg.getClass().getSimpleName());
        bus.send(amsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    event.setError(reply.getError());
                    bus.publish(event);
                    return;
                }

                AddModelServiceReply r = reply.castReply();
                event.setInventory(r.getInventory());
                bus.publish(event);
            }
        });
    }

    private void handle(APICloneModelServiceMsg msg) {
        APICloneModelServiceEvent event = new APICloneModelServiceEvent(msg.getId());

        ModelServiceVO original = Q.New(ModelServiceVO.class)
                .eq(ModelServiceVO_.uuid, msg.getUuid())
                .find();

        ModelServiceVO clone = new ModelServiceVO();
        ObjectUtils.copy(clone, original);
        clone.getModelServiceRefs().clear();

        if (msg.getResourceUuid() != null) {
            clone.setUuid(msg.getResourceUuid());
        } else {
            clone.setUuid(Platform.getUuid());
        }

        if (msg.getName() != null) {
            clone.setName(msg.getName());
        }

        if (msg.getDescription() != null) {
            clone.setDescription(msg.getDescription());
        }

        if (msg.getReadme() != null) {
            clone.setReadme(msg.getReadme());
        }

        if (msg.getVmImageUuid() != null) {
            clone.setVmImageUuid(msg.getVmImageUuid());
        }

        if (msg.getDockerImage() != null) {
            clone.setDockerImage(msg.getDockerImage());
        }

        if (msg.getSize() != 0) {
            clone.setSize(msg.getSize());
        }

        if (msg.getGpuComputeCapability() != null) {
            clone.setGpuComputeCapability(GpuComputeCapability.fromComputeCapability(msg.getGpuComputeCapability()));
        }

        if (msg.getInstallPath() != null) {
            clone.setInstallPath(msg.getInstallPath());
        }

        if (msg.getStartCommand() != null) {
            clone.setStartCommand(msg.getStartCommand());
        }

        if (msg.getPythonVersion() != null) {
            clone.setPythonVersion(msg.getPythonVersion());
        }

        if (msg.getCondaVersion() != null) {
            clone.setCondaVersion(msg.getCondaVersion());
        }

        if (msg.getSystem() != null) {
            clone.setSystem(msg.getSystem());
        }

        if (msg.getType() != null) {
            clone.setType(ModelServiceType.valueOf(msg.getType()));
        }

        if (msg.getYaml() != null) {
            clone.setYaml(msg.getYaml());
        }
        if (msg.getSource() != null) {
            clone.setSource(ModelServiceSource.fromString(msg.getSource()));
        }

        if (msg.getFramework() != null) {
            clone.setFramework(msg.getFramework());
        }

        if (msg.getRequestCpu() != null) {
            clone.setRequestCpu(msg.getRequestCpu());
        }

        if (msg.getRequestMemory() != null) {
            clone.setRequestMemory(msg.getRequestMemory());
        }

        clone.setSystem(false);
        clone.setModelCenterUuid(original.getModelCenterUuid());
        clone.setAccountUuid(msg.getSession().getAccountUuid());
        clone.setCreateDate(null);

        clone = dbf.persistAndRefresh(clone);
        event.setInventory(ModelServiceInventory.valueOf(clone));
        bus.publish(event);
    }

    private void handle(APIUpdateModelServiceMsg msg) {
        APIUpdateModelServiceEvent event = new APIUpdateModelServiceEvent(msg.getId());

        ModelServiceVO modelServiceVO = Q.New(ModelServiceVO.class)
                .eq(ModelServiceVO_.uuid, msg.getUuid())
                .find();

        // 更新可选属性，仅当属性不为 null 时进行更新
        if (msg.getDescription() != null) {
            modelServiceVO.setDescription(msg.getDescription());
        }

        if (msg.getName() != null) {
            modelServiceVO.setName(msg.getName());
        }

        if (msg.getYaml() != null) {
            modelServiceVO.setYaml(msg.getYaml());
        }

        if (msg.getDockerImage() != null) {
            modelServiceVO.setDockerImage(msg.getDockerImage());
        }

        if (msg.getVmImageUuid() != null) {
            modelServiceVO.setVmImageUuid(msg.getVmImageUuid());
        }

        if (msg.getRequestCpu() != null) {
            modelServiceVO.setRequestCpu(msg.getRequestCpu());
        }

        if (msg.getRequestMemory() != null) {
            modelServiceVO.setRequestMemory(msg.getRequestMemory());
        }

        if (msg.getGpuComputeCapability() != null) {
            modelServiceVO.setGpuComputeCapability(GpuComputeCapability.fromComputeCapability(msg.getGpuComputeCapability()));
        }

        if (msg.getStartCommand() != null) {
            modelServiceVO.setStartCommand(msg.getStartCommand());
        }

        if (msg.getPythonVersion() != null) {
            modelServiceVO.setPythonVersion(msg.getPythonVersion());
        }

        if (msg.getType() != null) {
            modelServiceVO.setType(ModelServiceType.valueOf(msg.getType()));
        }

        if (msg.getSource() != null) {
            modelServiceVO.setSource(ModelServiceSource.fromString(msg.getSource()));
        }

        if (msg.getFramework() != null) {
            modelServiceVO.setFramework(msg.getFramework());
        }

        dbf.update(modelServiceVO);

        ModelServiceInventory modelServiceInventory = new ModelServiceInventory(modelServiceVO);
        event.setInventory(modelServiceInventory);
        bus.publish(event);
    }

    private void handle(APIUpdateModelEvaluationTaskMsg msg) {
        APIUpdateModelEvaluationTaskEvent event = new APIUpdateModelEvaluationTaskEvent(msg.getId());

        ModelEvaluationTaskVO task = Q.New(ModelEvaluationTaskVO.class)
                .eq(ModelEvaluationTaskVO_.uuid, msg.getUuid())
                .find();

        if (msg.getName() != null) {
            task.setName(msg.getName());
        }

        if (msg.getDescription() != null) {
            task.setDescription(msg.getDescription());
        }

        task = dbf.updateAndRefresh(task);
        event.setInventory(ModelEvaluationTaskInventory.valueOf(task));
        bus.publish(event);
    }

    private void handle(DeleteModelServiceMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public void run(SyncTaskChain chain) {
                doDeleteModelService(msg);
                chain.next();
            }

            @Override
            public String getSyncSignature() {
                return String.format("delete-model-service-%s", msg.getUuid());
            }

            @Override
            public String getName() {
                return String.format("delete-model-service-%s", msg.getUuid());
            }
        });
    }

    private void doDeleteModelService(DeleteModelServiceMsg msg) {
        DeleteModelServiceReply reply = new DeleteModelServiceReply();
        ModelServiceVO modelService = Q.New(ModelServiceVO.class)
                .eq(ModelServiceVO_.uuid, msg.getUuid())
                .find();

        if (modelService.getInstallPath() == null || modelService.getInstallPath().isEmpty()) {
            SQL.New(ModelServiceVO.class)
                    .eq(ModelServiceVO_.uuid, msg.getUuid())
                    .delete();
            bus.reply(msg, reply);
            return;
        }

        ModelCenterVO center = dbf.findByUuid(modelService.getModelCenterUuid(), ModelCenterVO.class);
        ModelStorageBackend backend = new BentoBasedModelStorageBackend(center);
        backend.deleteModelService(modelService.getInstallPath(), new Completion(msg) {
            @Override
            public void success() {
                SQL.New(ModelServiceVO.class)
                        .eq(ModelServiceVO_.uuid, msg.getUuid())
                        .delete();
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(APIDeleteModelServicesMsg msg) {
        APIDeleteModelServicesEvent event = new APIDeleteModelServicesEvent(msg.getId());

        List<DeleteModelServiceMsg> deleteModelServiceMsgs = new ArrayList<>();
        for (String uuid : msg.getUuids()) {
            DeleteModelServiceMsg dmsg = new DeleteModelServiceMsg();
            dmsg.setUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(dmsg, SERVICE_ID, uuid);
            deleteModelServiceMsgs.add(dmsg);
        }

        List<APIBatchRequest.BatchOperationResult<Void>> results = Collections.synchronizedList(new ArrayList<>());
        new While<>(deleteModelServiceMsgs).each((dmsg, whileCompletion) -> {
            APIBatchRequest.BatchOperationResult<Void> result = new APIBatchRequest.BatchOperationResult<Void>();
            result.setUuid(dmsg.getUuid());
            bus.send(dmsg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        result.setSuccess(true);
                    } else {
                        result.setSuccess(false);
                        result.setError(reply.getError());
                    }

                    results.add(result);
                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    event.setError(errorCodeList.getCauses().get(0));
                } else {
                    event.setResults(results);
                }

                bus.publish(event);
            }
        });
    }

    private void handle(APIDeleteModelServiceMsg msg) {
        APIDeleteModelServiceEvent event = new APIDeleteModelServiceEvent(msg.getId());

        DeleteModelServiceMsg dmsg = new DeleteModelServiceMsg();
        dmsg.setUuid(msg.getUuid());
        bus.makeTargetServiceIdByResourceUuid(dmsg, SERVICE_ID, msg.getUuid());
        bus.send(dmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (reply.isSuccess()) {
                    bus.publish(event);
                } else {
                    event.setError(reply.getError());
                    bus.publish(event);
                }
            }
        });
    }

    private void handle(APIUnbindModelFromServiceMsg msg) {
        APIUnbindModelFromServiceEvent event = new APIUnbindModelFromServiceEvent(msg.getId());
        ModelServiceRefVO ref = Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelServiceUuid, msg.getModelServiceUuid())
                .eq(ModelServiceRefVO_.modelUuid, msg.getModelUuid())
                .find();
        if (ref != null) {
            dbf.remove(ref);
        }
        event.setInventory(ModelServiceInventory.valueOf(dbf.findByUuid(msg.getModelServiceUuid(), ModelServiceVO.class)));
        bus.publish(event);
    }

    private void handle(APIBindModelToServiceMsg msg) {
        APIBindModelToServiceEvent event = new APIBindModelToServiceEvent(msg.getId());
        ModelServiceRefVO ref = Q.New(ModelServiceRefVO.class)
                .eq(ModelServiceRefVO_.modelServiceUuid, msg.getModelServiceUuid())
                .eq(ModelServiceRefVO_.modelUuid, msg.getModelUuid())
                .find();
        if (ref != null) {
            event.setInventory(ModelServiceInventory.valueOf(dbf.findByUuid(msg.getModelServiceUuid(), ModelServiceVO.class)));
            bus.publish(event);
            return;
        }
        ref = new ModelServiceRefVO();
        ref.setUuid(Platform.getUuid());
        ref.setModelUuid(msg.getModelUuid());
        ref.setModelServiceUuid(msg.getModelServiceUuid());
        dbf.persist(ref);

        event.setInventory(ModelServiceInventory.valueOf(dbf.findByUuid(msg.getModelServiceUuid(), ModelServiceVO.class)));
        bus.publish(event);
    }

    private ModelServiceVO getModelServiceByUuid(String uuid) {
        if (uuid == null) {
            return null;
        }

        return Q.New(ModelServiceVO.class)
                .eq(ModelServiceVO_.uuid, uuid)
                .find();
    }

    private ModelServiceFactory getFactoryByModelServiceType(ModelServiceType modelServiceType) {
        if (logger.isTraceEnabled()) {
            logger.trace(String.format("get model service factory by modelServiceType %s", modelServiceType));
        }

        if (logger.isTraceEnabled()) {
            logger.trace(String.format("found ModelServiceFactory %s", factories.get(modelServiceType)));
        }

        return factories.get(modelServiceType);
    }

    public boolean start() {
        pluginRegistry.getExtensionList(ModelServiceBackend.class).forEach(it -> {
            backends.put(it.getType(), it);
        });

        pluginRegistry.getExtensionList(ModelServiceFactory.class).forEach(it -> {
            factories.put(it.getModelServiceType(), it);
        });

        trackModelServiceInstanceGroup();
        AIGlobalConfig.INSTANCE_TRACK_INTERVAL_SECONDS.installUpdateExtension((oldConfig, newConfig) -> trackModelServiceInstanceGroup());

        evtf.on(PrometheusCanonicalEvents.STATIC_CONFIG_WRITE, new EventCallback() {
            // other node writing the static config file means the resource
            // is owned by him and we need remove ours if any
            @Override
            @ExceptionSafe
            protected void run(Map tokens, Object o) {
                if (evtf.isFromThisManagementNode(tokens)) {
                    return;
                }

                PrometheusCanonicalEvents.StaticConfigWriteData d = (PrometheusCanonicalEvents.StaticConfigWriteData) o;
                if (ModelServiceInstanceGroupVO.class.getSimpleName().equals(d.getResourceType())) {
                    deleteScrapeConfig(d.getUuid());
                } else if (ModelCenterVO.class.getSimpleName().equals(d.getResourceType())) {
                    deleteModelCenterScrapeConfig(d.getUuid());
                }
            }
        });

        AISystemTags.MODEL_SERVICE_MODE.installValidator((resourceUuid, resourceType, systemTag) -> {
            String mode = AISystemTags.MODEL_SERVICE_MODE.getTokenByTag(systemTag, AISystemTags.MODEL_SERVICE_MODE_TOKEN);
            if (Strings.isNullOrEmpty(mode)) {
                throw new ApiMessageInterceptionException(argerr("mode must be not empty"));
            }

            if (!ALLOWED_MODE.equals(mode)) {
                throw new ApiMessageInterceptionException(argerr("current allowed values are ['%s']", ALLOWED_MODE));
            }
        });

        installSystemTagValidators();
        return true;
    }

    private void installSystemTagValidators() {
        AISystemTags.DATA_SET_DATA_TYPE.installValidator((resourceUuid, resourceType, systemTag) -> {
            String dataType = AISystemTags.DATA_SET_DATA_TYPE
                    .getTokenByTag(systemTag, AISystemTags.DATA_SET_DATA_TYPE_TOKEN);
            if (dataType == null) {
                throw new ApiMessageInterceptionException(
                        argerr("Unexpected value got: null from %s of token: %s", systemTag, AISystemTags.DATA_SET_DATA_TYPE_TOKEN)
                );
            }

            boolean allowedType = false;
            for (DatasetDataType expectedType: DatasetDataType.values()) {
                if (expectedType.toString().equals(dataType)) {
                    allowedType = true;
                }
            }

            if (!allowedType) {
                throw new ApiMessageInterceptionException(
                        argerr("Unexpected value got: %s from %s, expected values are: %s", dataType, systemTag, Joiner.on(",").join(DatasetDataType.values()))
                );
            }
        });
    }

    public boolean stop() {
        return true;
    }
}
