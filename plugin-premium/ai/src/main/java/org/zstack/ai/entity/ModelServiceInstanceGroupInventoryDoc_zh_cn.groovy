package org.zstack.ai.entity

import org.zstack.ai.entity.ModelServiceInstanceInventory
import org.zstack.ai.entity.ModelServiceGroupDatasetRefInventory
import java.sql.Timestamp

doc {

	title "在这里输入结构的名称"

	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "5.2.1"
	}
	field {
		name "modelServiceUuid"
		desc ""
		type "String"
		since "5.2.1"
	}
	field {
		name "modelUuid"
		desc ""
		type "String"
		since "5.2.1"
	}
	ref {
		name "instances"
		path "org.zstack.ai.entity.ModelServiceInstanceGroupInventory.instances"
		desc "null"
		type "List"
		since "5.2.1"
		clz ModelServiceInstanceInventory.class
	}
	ref {
		name "datasetRefInventories"
		path "org.zstack.ai.entity.ModelServiceInstanceGroupInventory.datasetRefInventories"
		desc "null"
		type "List"
		since "5.2.1"
		clz ModelServiceGroupDatasetRefInventory.class
	}
	field {
		name "status"
		desc ""
		type "String"
		since "5.2.1"
	}
	field {
		name "modelServiceType"
		desc ""
		type "String"
		since "5.2.1"
	}
	field {
		name "type"
		desc ""
		type "String"
		since "5.2.1"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "5.2.1"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "5.3.20"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "5.2.1"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "5.2.1"
	}
}
