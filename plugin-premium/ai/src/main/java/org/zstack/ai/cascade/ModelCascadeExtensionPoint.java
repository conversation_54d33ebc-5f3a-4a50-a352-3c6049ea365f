package org.zstack.ai.cascade;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.ai.AIModelManager;
import org.zstack.ai.entity.ModelCenterInventory;
import org.zstack.ai.entity.ModelCenterVO;
import org.zstack.ai.entity.ModelInventory;
import org.zstack.ai.entity.ModelVO;
import org.zstack.ai.entity.ModelVO_;
import org.zstack.ai.message.ModelDeletionMsg;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class ModelCascadeExtensionPoint extends AbstractAsyncCascadeExtension {

    @Autowired
    private CloudBus bus;

    protected static final CLogger logger = Utils.getLogger(ModelCascadeExtensionPoint.class);

    private static final String NAME = ModelVO.class.getSimpleName();

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            completion.success();
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            completion.success();
        } else {
            completion.success();
        }
    }

    private void handleDeletion(CascadeAction action, Completion completion) {
        final List<ModelInventory> models = modelFromAction(action);
        if (CollectionUtils.isEmpty(models)) {
            completion.success();
            return;
        }

        List<ModelDeletionMsg> deletionMsgList = new ArrayList<>();
        for (ModelInventory model : models) {
            ModelDeletionMsg msg = new ModelDeletionMsg();
            msg.setUuid(model.getUuid());
            bus.makeTargetServiceIdByResourceUuid(msg, AIModelManager.SERVICE_ID, model.getUuid());
            deletionMsgList.add(msg);
        }

        new While<>(deletionMsgList).each((msg, whileCompletion) -> {
            bus.send(msg, new CloudBusCallBack(whileCompletion) {
                @Override
                public void run(MessageReply reply) {
                    if (reply.isSuccess()) {
                        logger.debug(String.format("Model [uuid: %s] deleted successfully", msg.getUuid()));
                    } else {
                        logger.warn(String.format("Failed to delete model [uuid: %s]: %s", msg.getUuid(), reply.getError()));
                        whileCompletion.addError(reply.getError());
                    }

                    whileCompletion.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    logger.debug("Failed to delete model centers: " + errorCodeList);
                }

                completion.success();
            }
        });
    }

    private List<ModelInventory> modelFromAction(CascadeAction action) {
        if (NAME.equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        } if (ModelCenterVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<ModelCenterInventory> modelCenterInventories = action.getParentIssuerContext();
            if (CollectionUtils.isEmpty(modelCenterInventories)) {
                return Collections.emptyList();
            }
            List<String> modelCenterUuids = modelCenterInventories.stream()
                    .map(ModelCenterInventory::getUuid)
                    .collect(Collectors.toList());

            List<ModelVO> models = Q.New(ModelVO.class)
                    .in(ModelVO_.modelCenterUuid, modelCenterUuids)
                    .list();
            return ModelInventory.valueOf(models);
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(ModelCenterVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<ModelInventory> invs = modelFromAction(action);
            if (invs != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(invs);
            }
        }

        return null;
    }
}

