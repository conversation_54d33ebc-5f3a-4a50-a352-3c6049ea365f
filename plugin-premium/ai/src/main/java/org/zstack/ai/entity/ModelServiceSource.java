package org.zstack.ai.entity;

/**
 * <AUTHOR>
 * @date 2024/7/21 09:24
 */
public enum ModelServiceSource {
    Bentoml,
    HuggingFace,
    Other;

    public static ModelServiceSource fromString(String frameworkString) {
        if (frameworkString == null || frameworkString.isEmpty()) {
            return ModelServiceSource.Other;
        }

        try {
            return ModelServiceSource.valueOf(frameworkString);
        } catch (IllegalArgumentException e) {
            return ModelServiceSource.Other;
        }
    }
}
