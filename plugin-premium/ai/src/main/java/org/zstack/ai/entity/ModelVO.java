package org.zstack.ai.entity;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.tag.AutoDeleteTag;
import org.zstack.header.vo.*;
import org.zstack.header.vo.EntityGraph;
import org.zstack.header.vo.ForeignKey;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/6/6 10:00
 */

@Entity
@Table
@AutoDeleteTag
@org.zstack.header.vo.EntityGraph(
        friends = {
                @EntityGraph.Neighbour(type = ModelServiceRefVO.class, myField = "uuid", targetField = "modelServiceUuid")
        }
)
public class ModelVO extends ResourceVO implements ToInventory, OwnedByAccount {
    @Column
    private String name;

    @Column
    private String installPath;

    @Column
    private String description;

    @Column
    private String parameters;

    @Column
    private String logo;

    @Column
    private String vendor;

    @Column
    private String introduction;

    @Column
    private Long size;

    @Column
    private String version;

    @Column
    @Enumerated(EnumType.STRING)
    private ModelType type;

    @Column
    private String modelId;

    @Column
    @ForeignKey(parentEntityClass = ModelCenterVO.class, parentKey = "uuid")
    private String modelCenterUuid;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "modelUuid", insertable = false, updatable = false)
    @NoView
    private Set<ModelServiceRefVO> modelServiceRefs = new HashSet<>();

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;

    @Transient
    private String accountUuid;

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getSize() {
        return size;
    }

    public String getIntroduction() {
        return introduction;
    }

    public String getLogo() {
        return logo;
    }

    public String getVendor() {
        return vendor;
    }

    public String getVersion() {
        return version;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getParameters() {
        return parameters;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    public String getInstallPath() {
        return installPath;
    }

    public String getModelCenterUuid() {
        return modelCenterUuid;
    }

    public void setModelCenterUuid(String modelCenterUuid) {
        this.modelCenterUuid = modelCenterUuid;
    }

    public void setInstallPath(String installPath) {
        this.installPath = installPath;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    @Override
    public String getAccountUuid() {
        return this.accountUuid;
    }

    @Override
    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    public Set<ModelServiceRefVO> getModelServiceRefs() {
        return modelServiceRefs;
    }

    public void setModelServiceRefs(Set<ModelServiceRefVO> modelServiceRefs) {
        this.modelServiceRefs = modelServiceRefs;
    }

    public ModelType getType() {
        return type;
    }

    public void setType(ModelType type) {
        this.type = type;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }
}
