package org.zstack.ai.entity;

import org.apache.commons.lang.StringUtils;
import org.zstack.ai.AIConfigYaml;
import org.zstack.ai.AIGlobalConfig;
import org.zstack.core.Platform;
import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;
import org.zstack.header.vm.VmInstanceInventory;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Inventory(mappingVOClass = ModelServiceInstanceVO.class)
@PythonClassInventory
public class ModelServiceInstanceInventory {
    private String uuid;
    private String modelServiceGroupUuid;
    private String yaml;
    private String k8sResourceYaml;
    private String status;
    private String url;
    private Map<String, String> urlMaps;
    private String internalUrl;
    private String jupyterUrl;
    private String vmInstanceUuid;
    private VmInstanceInventory vm;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getModelServiceGroupUuid() {
        return modelServiceGroupUuid;
    }

    public void setModelServiceGroupUuid(String modelServiceGroupUuid) {
        this.modelServiceGroupUuid = modelServiceGroupUuid;
    }

    public ModelServiceInstanceInventory() {

    }

    public ModelServiceInstanceInventory(ModelServiceInstanceVO modelServiceInstanceVO) {
        this.uuid = modelServiceInstanceVO.getUuid();
        this.modelServiceGroupUuid = modelServiceInstanceVO.getModelServiceGroupUuid();
        this.yaml = modelServiceInstanceVO.getYaml();
        this.status = modelServiceInstanceVO.getStatus();
        this.url = modelServiceInstanceVO.getUrl();
        this.k8sResourceYaml = modelServiceInstanceVO.getK8sResourceYaml();
        this.internalUrl = modelServiceInstanceVO.getInternalUrl();
        if (modelServiceInstanceVO.getVmInstanceUuid() != null) {
            this.vm = VmInstanceInventory.valueOf(modelServiceInstanceVO.getVm());
        }
        if (!StringUtils.isEmpty(this.internalUrl)) {
            String[] parts = this.internalUrl.split(":");
            String host = parts[0];
            String newPort = String.valueOf(AIGlobalConfig.JUPYTER_LISTEN_PORT.value(Integer.class));

            String newUrl = host + ":" + newPort;
            // 如果原始 URL 包含其他部分(如路径、查询参数等),将其附加到新的 URL
            if (parts.length > 2) {
                for (int i = 2; i < parts.length; i++) {
                    newUrl += ":" + parts[i];
                }
            }
            this.jupyterUrl = newUrl;
        }

        if(!StringUtils.isEmpty(modelServiceInstanceVO.getUrlMaps())) {
            this.urlMaps = parseStringToMap(modelServiceInstanceVO.getUrlMaps());
        }

        this.createDate = modelServiceInstanceVO.getCreateDate();
        this.lastOpDate = modelServiceInstanceVO.getLastOpDate();
        this.vmInstanceUuid = modelServiceInstanceVO.getVmInstanceUuid();
    }

    public static ModelServiceInstanceInventory valueOf(ModelServiceInstanceVO modelServiceInstanceVO) {
        return new ModelServiceInstanceInventory(modelServiceInstanceVO);
    }

    public static List<ModelServiceInstanceInventory> valueOf(Collection<ModelServiceInstanceVO> modelServiceInstanceVOS) {
        return modelServiceInstanceVOS.stream().map(ModelServiceInstanceInventory::valueOf).collect(Collectors.toList());
    }

    private Map<String, String> parseStringToMap(String urlMapsString) {
        // 去除字符串开头和结尾的大括号
        urlMapsString = urlMapsString.substring(1, urlMapsString.length() - 1);
        Map<String, String> urlMap = new HashMap<>();

        // 使用逗号分隔键值对
        String[] keyValuePairs = urlMapsString.split(", ");
        for (String pair : keyValuePairs) {
            // 使用等号分隔键和值
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                urlMap.put(keyValue[0], keyValue[1]);
            }
        }
        return urlMap;
    }

    public void setUrlMaps(Map<String, String> urlMaps) {
        this.urlMaps = urlMaps;
    }

    public Map<String, String> getUrlMaps() {
        return urlMaps;
    }

    public String getK8sResourceYaml() {
        return k8sResourceYaml;
    }

    public void setK8sResourceYaml(String k8sResourceYaml) {
        this.k8sResourceYaml = k8sResourceYaml;
    }

    public void setInternalUrl(String internalUrl) {
        this.internalUrl = internalUrl;
    }

    public String getJupyterUrl() {
        return jupyterUrl;
    }

    public void setJupyterUrl(String jupyterUrl) {
        this.jupyterUrl = jupyterUrl;
    }

    public String getInternalUrl() {
        return internalUrl;
    }

    public String getYaml() {
        return yaml;
    }

    public void setYaml(String yaml) {
        this.yaml = yaml;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public VmInstanceInventory getVm() {
        return vm;
    }

    public void setVm(VmInstanceInventory vm) {
        this.vm = vm;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    public String getLivePath() {
        String serviceYaml = getYaml();
        AIConfigYaml yaml = AIConfigYaml.fromYaml(serviceYaml);
        String livePath = yaml.getServices().get(0).getLivez();
        if (!livePath.startsWith("/")) {
            livePath = "/" + livePath;
        }
        return livePath;
    }

    public static ModelServiceInstanceInventory __example__() {
        ModelServiceInstanceInventory inv = new ModelServiceInstanceInventory();
        inv.setUuid(Platform.getUuid());
        inv.setUrl("http://127.0.0.1:3000");
        inv.setCreateDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        inv.setLastOpDate(new Timestamp(org.zstack.header.message.DocUtils.date));
        inv.setStatus(ModelServiceInstanceGroupStatus.Deploying.toString());
        inv.setModelServiceGroupUuid(Platform.getUuid());
        inv.setYaml("service: service");
        inv.setVmInstanceUuid(Platform.getUuid());
        inv.setModelServiceGroupUuid(Platform.getUuid());
        return inv;
    }
}
