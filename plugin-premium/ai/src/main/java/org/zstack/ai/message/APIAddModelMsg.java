package org.zstack.ai.message;


import org.springframework.http.HttpMethod;
import org.zstack.ai.entity.ModelCenterVO;
import org.zstack.ai.entity.ModelInventory;
import org.zstack.ai.entity.ModelServiceVO;
import org.zstack.ai.entity.ModelVO;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.DefaultTimeout;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.other.APIMultiAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/6/6 10:15
 */
@RestRequest(
        path = "/ai/models",
        method = HttpMethod.POST,
        responseClass = APIAddModelEvent.class,
        parameterName = "param"
)
@DefaultTimeout(timeunit = TimeUnit.HOURS, value = 72)
public class APIAddModelMsg extends APICreateMessage implements APIMultiAuditor {
    @APIParam()
    private String name;

    @APIParam()
    private String installPath;

    @APIParam(required = false)
    private String description;

    @APIParam(required = false)
    private String parameters;

    @APIParam(required = false)
    private String token;

    @APIParam(resourceType = ModelCenterVO.class)
    private String modelCenterUuid;

    @APIParam(required = false)
    private String logo;

    @APIParam(required = false)
    private String vendor;

    @APIParam(required = false)
    private String introduction;

    @APIParam(required = false)
    private Long size;

    @APIParam(required = false)
    private String version;

    @APIParam(required = false, resourceType = ModelServiceVO.class)
    private List<String> modelServiceUuids;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getSize() {
        return size;
    }

    public String getIntroduction() {
        return introduction;
    }

    public String getVendor() {
        return vendor;
    }

    public String getVersion() {
        return version;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getParameters() {
        return parameters;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    public String getInstallPath() {
        return installPath;
    }

    public String getModelCenterUuid() {
        return modelCenterUuid;
    }

    public void setModelCenterUuid(String modelCenterUuid) {
        this.modelCenterUuid = modelCenterUuid;
    }

    public void setInstallPath(String installPath) {
        this.installPath = installPath;
    }

    public List<String> getModelServiceUuids() {
        return modelServiceUuids;
    }

    public void setModelServiceUuids(List<String> modelServiceUuids) {
        this.modelServiceUuids = modelServiceUuids;
    }

    public static APIAddModelMsg __example__ () {
        APIAddModelMsg msg = new APIAddModelMsg();
        msg.setName("Qwen 1.5 32B");
        msg.setDescription("This is qwen 1.5 32B ai model");
        msg.setInstallPath("*************:/ai/model_center/Qwen_1.5_23B");
        msg.setParameters("model parameters");
        msg.setModelCenterUuid("uuid");
        msg.setIntroduction("This is a md5 format string");
        msg.setLogo("logo base64 string");
        msg.setSize(102400l);
        msg.setVendor("Alibaba");
        msg.setVersion("CustomizeVersion");
        return msg;
    }

    @Override
    public List<APIAuditor.Result> multiAudit(APIMessage msg, APIEvent rsp) {
        List<APIAuditor.Result> results = new ArrayList<>();

        if (!rsp.isSuccess()) {
            return results;
        }
        ModelInventory inventory = ((APIAddModelEvent) rsp).getInventory();
        results.add(new APIAuditor.Result(inventory.getUuid(), ModelVO.class));

        if (!CollectionUtils.isEmpty(inventory.getModelServiceRefs())) {
            inventory.getModelServiceRefs()
                    .forEach(ref -> results.add(new APIAuditor
                            .Result(ref.getModelServiceUuid(), ModelServiceVO.class)));
        }

        return results;
    }
}
