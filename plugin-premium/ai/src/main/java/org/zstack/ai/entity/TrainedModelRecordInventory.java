package org.zstack.ai.entity;

import org.zstack.core.Platform;
import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/8 21:46
 */

@Inventory(mappingVOClass = TrainedModelRecordVO.class)
@PythonClassInventory
public class TrainedModelRecordInventory implements Serializable {
    private String uuid;
    private String modelUuid;
    private String sourceModelUuid;
    private String modelServiceInstanceGroupUuid;
    private String datasetUuid;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public TrainedModelRecordInventory() {}

    public TrainedModelRecordInventory(TrainedModelRecordVO trainedModelRecordVO) {
        uuid = trainedModelRecordVO.getUuid();
        modelUuid = trainedModelRecordVO.getModelUuid();
        modelServiceInstanceGroupUuid = trainedModelRecordVO.getModelServiceInstanceGroupUuid();
        datasetUuid = trainedModelRecordVO.getDatasetUuid();
        sourceModelUuid = trainedModelRecordVO.getSourceModelUuid();
        createDate = trainedModelRecordVO.getCreateDate();
        lastOpDate = trainedModelRecordVO.getLastOpDate();
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public void setSourceModelUuid(String sourceModelUuid) {
        this.sourceModelUuid = sourceModelUuid;
    }

    public String getSourceModelUuid() {
        return sourceModelUuid;
    }

    public void setDatasetUuid(String datasetUuid) {
        this.datasetUuid = datasetUuid;
    }

    public String getDatasetUuid() {
        return datasetUuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setModelUuid(String modelUuid) {
        this.modelUuid = modelUuid;
    }

    public String getModelUuid() {
        return modelUuid;
    }

    public void setModelServiceInstanceGroupUuid(String modelServiceInstanceGroupUuid) {
        this.modelServiceInstanceGroupUuid = modelServiceInstanceGroupUuid;
    }

    public String getModelServiceInstanceGroupUuid() {
        return modelServiceInstanceGroupUuid;
    }

    public static TrainedModelRecordInventory valueOf(TrainedModelRecordVO trainedModelRecordVO) {
        return new TrainedModelRecordInventory(trainedModelRecordVO);
    }

    public static List<TrainedModelRecordInventory> valueOf(List<TrainedModelRecordVO> trainedModelRecordVOS) {
        return trainedModelRecordVOS.stream().map(TrainedModelRecordInventory::valueOf).collect(Collectors.toList());
    }

    public static TrainedModelRecordInventory __example__() {
        TrainedModelRecordInventory inventory = new TrainedModelRecordInventory();
        inventory.setUuid(Platform.getUuid());
        inventory.setModelUuid(Platform.getUuid());
        inventory.setModelServiceInstanceGroupUuid(Platform.getUuid());
        inventory.setDatasetUuid(Platform.getUuid());
        inventory.setCreateDate(new Timestamp(System.currentTimeMillis()));
        inventory.setLastOpDate(new Timestamp(System.currentTimeMillis()));
        return inventory;
    }
}
