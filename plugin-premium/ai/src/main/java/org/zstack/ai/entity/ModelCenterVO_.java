package org.zstack.ai.entity;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@StaticMetamodel(ModelCenterVO.class)
public class ModelCenterVO_ extends ResourceVO_ {
    public static volatile SingularAttribute<ModelCenterVO, String> name;
    public static volatile SingularAttribute<ModelCenterVO, String> zoneUuid;
    public static volatile SingularAttribute<ModelCenterVO, String> description;
    public static volatile SingularAttribute<ModelCenterVO, String> url;
    public static volatile SingularAttribute<ModelCenterVO, String> parameters;
    public static volatile SingularAttribute<ModelCenterVO, String> containerRegistry;
    public static volatile SingularAttribute<ModelCenterVO, String> managementIp;
    public static volatile SingularAttribute<ModelCenterVO, ModelCenterStatus> status;
}
