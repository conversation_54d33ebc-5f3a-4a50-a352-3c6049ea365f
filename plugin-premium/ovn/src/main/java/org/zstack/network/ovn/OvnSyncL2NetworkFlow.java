package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.network.l2.L2NetworkInventory;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l2.L2NetworkVO_;
import org.zstack.sdnController.header.SdnControllerVO;

import javax.persistence.Tuple;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.zstack.sdnController.header.SdnControllerFlowDataParam.*;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnSyncL2NetworkFlow extends NoRollbackFlow {

    @Autowired
    DatabaseFacade dbf;

    private static class OvnSyncL2NetworkStruct {
        int vniMax;
        int vniMin;
        List<String> l2Uuids;
    }

    @Override
    public void run(FlowTrigger trigger, Map data) {
        String controllerUuid = (String)data.get(SDN_CONTROLLER_UUID);
        SdnControllerVO vo = dbf.findByUuid(controllerUuid, SdnControllerVO.class);
        OvnController controller = new OvnController(vo);

        List<Tuple> l2Tuples = controller.getL2NetworkOfSdnController();

        if (l2Tuples.isEmpty()) {
            controller.syncL2Network(new Completion(trigger) {
                @Override
                public void success() {
                    trigger.next();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    trigger.fail(errorCode);
                }
            });
            return;
        }

        int step = 100;
        int size = l2Tuples.size();
        int count = size / step;
        if (size - count * step > 0) {
            count++;
        }

        List<OvnSyncL2NetworkStruct> l2Structs = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            OvnSyncL2NetworkStruct struct = new OvnSyncL2NetworkStruct();

            int end = (i + 1) * step;
            List<Tuple> subTuples = l2Tuples.subList(i * step, Math.min(end, l2Tuples.size()));
            Tuple last = subTuples.get(subTuples.size() - 1);
            if (i == 0) {
                struct.vniMin = OvnControllerHelper.L2_NETWORK_INDEX_MIN;
            } else {
                struct.vniMin = l2Structs.get(l2Structs.size() - 1).vniMax + 1;
            }

            if (i == count - 1) {
                struct.vniMax = OvnControllerHelper.L2_NETWORK_INDEX_MAX;
            } else {
                struct.vniMax = OvnControllerHelper.getOvnSyncL2Index(last.get(1, String.class), last.get(2, Integer.class));
            }

            struct.l2Uuids = subTuples.stream().map(t -> t.get(0, String.class)).collect(Collectors.toList());

            l2Structs.add(struct);
        }

        new While<>(l2Structs).each((struct, wc) -> {
            List<L2NetworkVO> vos = Q.New(L2NetworkVO.class)
                    .in(L2NetworkVO_.uuid, struct.l2Uuids).list();
            controller.syncL2Network(struct.vniMin, struct.vniMax, L2NetworkInventory.valueOf(vos), new Completion(wc) {
                @Override
                public void success() {
                    wc.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    wc.addError(errorCode);
                    wc.allDone();
                }
            });
        }).run(new WhileDoneCompletion(trigger) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    trigger.fail(errorCodeList.getCauses().get(0));
                    return;
                }

                trigger.next();
            }
        });
    }
}
