package org.zstack.network.ovn;

import org.springframework.http.HttpMethod;
import org.zstack.header.configuration.APICreateInstanceOfferingEvent;
import org.zstack.header.configuration.APICreateInstanceOfferingMsg;
import org.zstack.header.configuration.InstanceOfferingVO;
import org.zstack.header.identity.Action;
import org.zstack.header.image.ImageVO;
import org.zstack.header.message.APIParam;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.header.zone.ZoneVO;

@Action(category = OvnControllerConstant.ACTION_CATEGORY)
@TagResourceType(InstanceOfferingVO.class)
@RestRequest(
		path = "/instance-offerings/ovn",
		responseClass = APICreateInstanceOfferingEvent.class,
		parameterName = "params",
		method = HttpMethod.POST
)
public class APICreateOvnControllerOfferingMsg extends APICreateInstanceOfferingMsg {
	@APIParam(resourceType = ZoneVO.class)
	private String zoneUuid;
	@APIParam(resourceType = L3NetworkVO.class, checkAccount = true)
	private String managementNetworkUuid;
	@APIParam(resourceType = ImageVO.class, checkAccount = true)
	private String imageUuid;

    public String getType() {
        return OvnControllerConstant.OVN_CONTROLLER_APPLIANCE_VM_TYPE;
    }

	public String getZoneUuid() {
		return zoneUuid;
	}
	public void setZoneUuid(String zoneUuid) {
		this.zoneUuid = zoneUuid;
	}
	public String getManagementNetworkUuid() {
		return managementNetworkUuid;
	}
	public void setManagementNetworkUuid(String managementNetworkUuid) {
		this.managementNetworkUuid = managementNetworkUuid;
	}

	public String getImageUuid() {
		return imageUuid;
	}
	public void setImageUuid(String imageUuid) {
		this.imageUuid = imageUuid;
	}
 
    public static APICreateOvnControllerOfferingMsg __example__() {
		APICreateOvnControllerOfferingMsg msg = new APICreateOvnControllerOfferingMsg();

		msg.setName("ovn-Offering");
		msg.setType(OvnControllerConstant.OVN_CONTROLLER_APPLIANCE_VM_TYPE);
		msg.setCpuNum(2);
		msg.setCpuSpeed(1);
		msg.setMemorySize(1024);
        msg.setZoneUuid(uuid());
        msg.setManagementNetworkUuid(uuid());
        msg.setImageUuid(uuid());

        return msg;
    }

}
