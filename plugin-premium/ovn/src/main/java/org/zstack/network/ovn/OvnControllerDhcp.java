package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.http.HttpMethod;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l3.IpRangeInventory;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkUpdateExtensionPoint;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.network.service.flat.FlatDhcpBackend;
import org.zstack.header.network.service.SdnControllerDhcp;
import org.zstack.sdnController.SdnControllerManager;
import org.zstack.sdnController.header.SdnControllerVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.IPv6Constants;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.zstack.core.Platform.operr;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnControllerDhcp implements L3NetworkUpdateExtensionPoint, SdnControllerDhcp {
    private static final CLogger logger = Utils.getLogger(OvnControllerDhcp.class);
    private OvnControllerHelper OvnHelper = new OvnControllerHelper();

    @Autowired
    protected CloudBus bus;
    @Autowired
    DatabaseFacade dbf;
    @Autowired
    SdnControllerManager sdnMgr;
    @Autowired
    FlatDhcpBackend flatDhcpBackend;

    private SdnControllerVO self;

    public OvnControllerDhcp(SdnControllerVO self) {
        this.self = self;
    }

    @Override
    public void allocateDhcpAndEnableDhcp(L3NetworkVO vo, List<String> systemTags, Completion completion) {
        flatDhcpBackend.enableNetworkService(vo, systemTags, new Completion(completion) {
            @Override
            public void success() {
                enableDhcp(Collections.singletonList(L3NetworkInventory.valueOf(vo)), new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    @Override
    public void enableDhcp(List<L3NetworkInventory> invs, Completion completion) {
        enableDhcp(OvnControllerHelper.L3_NETWORK_INDEX_MIN, OvnControllerHelper.L3_NETWORK_INDEX_MAX, invs, false, completion);
    }

    @Override
    public void enableDhcp(long l3Min, long L3Max, List<L3NetworkInventory> invs, boolean sync, Completion completion) {
        List<OvnControllerCommands.Dhcp4OptionTo> dhcp4Opts = new ArrayList<>();
        List<OvnControllerCommands.Dhcp6OptionTo> dhcp6Opts = new ArrayList<>();

        for (L3NetworkInventory inv : invs) {
            OvnControllerCommands.Dhcp4OptionTo option4 = null;
            OvnControllerCommands.Dhcp6OptionTo option6 = null;
            for (IpRangeInventory ipr : inv.getIpRanges()) {
                if (ipr.getIpVersion() == IPv6Constants.IPv4 && option4 == null) {
                    option4 = OvnHelper.Dhcp4OptionFromIpRange(inv, ipr);
                } else if ((ipr.getIpVersion() == IPv6Constants.IPv6) &&
                        (!ipr.getAddressMode().equals(IPv6Constants.SLAAC)) &&
                        option6 == null) {
                    option6 = OvnHelper.Dhcp6OptionFromIpRange(inv, ipr);
                }
            }
            if (option4 != null) {
                dhcp4Opts.add(option4);
            }
            if (option6 != null) {
                dhcp6Opts.add(option6);
            }
        }

        OvnControllerCommands.DhcpOptionsCmd cmd = new OvnControllerCommands
                .DhcpOptionsCmd(self, dhcp4Opts, dhcp6Opts, sync);

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setCommand(cmd);
        cmsg.setMethod(HttpMethod.POST);
        cmsg.setPath(OvnControllerCommands.OVN_DHCP_OPTIONS_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        if (!sync) {
            cmsg.setCheckStatus(true);
        }
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.DhcpOptionsRsp rsp = re.toResponse(OvnControllerCommands.DhcpOptionsRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully add dhcp options to ovn controller[uuid:%s, ip:%s]",
                            self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to add dhcp options to  ovn controller[uuid:%s, ip:%s], because %s",
                            self.getUuid(), self.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }

    @Override
    public void disableDhcp(List<L3NetworkInventory> invs, int ipversion, Completion completion) {
        List<OvnControllerCommands.Dhcp4OptionTo> dhcp4Opts = new ArrayList<>();
        List<OvnControllerCommands.Dhcp6OptionTo> dhcp6Opts = new ArrayList<>();

        for (L3NetworkInventory inv : invs) {
            if (ipversion == IPv6Constants.IPv4 || ipversion == IPv6Constants.DUAL_STACK) {
                OvnControllerCommands.Dhcp4OptionTo option4 = new OvnControllerCommands.Dhcp4OptionTo();
                option4.setId(OvnHelper.getDhcp4OptionsID(inv.getUuid()));
                dhcp4Opts.add(option4);
            }

            if (ipversion == IPv6Constants.IPv6 || ipversion == IPv6Constants.DUAL_STACK) {
                OvnControllerCommands.Dhcp6OptionTo option6 = new OvnControllerCommands.Dhcp6OptionTo();
                option6.setId(OvnHelper.getDhcp6OptionsID(inv.getUuid()));
                dhcp6Opts.add(option6);
            }
        }

        OvnControllerCommands.DhcpOptionsCmd cmd = new OvnControllerCommands
                .DhcpOptionsCmd(self, dhcp4Opts, dhcp6Opts, false);

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setCommand(cmd);
        cmsg.setMethod(HttpMethod.DELETE);
        cmsg.setPath(OvnControllerCommands.OVN_DHCP_OPTIONS_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        cmsg.setCheckStatus(true);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.DhcpOptionsRsp rsp = re.toResponse(OvnControllerCommands.DhcpOptionsRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully remove dhcp options from ovn controller[uuid:%s, ip:%s]",
                            self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    completion.fail(ErrorCode.fromString(String.format("failed to remove dhcp options from  ovn controller[uuid:%s, ip:%s], because %s",
                            self.getUuid(), self.getIp(), rsp.getError())));
                }
            }
        });
    }

    @Override
    public void updateL3NetworkMtu(L3NetworkInventory inv) {
        enableDhcp(Collections.singletonList(inv), new NopeCompletion());
    }
}
