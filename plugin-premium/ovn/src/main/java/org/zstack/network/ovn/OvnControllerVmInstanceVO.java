package org.zstack.network.ovn;

import org.zstack.appliancevm.ApplianceVmVO;
import org.zstack.header.tag.AutoDeleteTag;
import org.zstack.header.vm.VmInstanceEO;
import org.zstack.header.vo.EO;

import javax.persistence.*;

@Entity
@Table
@PrimaryKeyJoinColumn(name="uuid", referencedColumnName="uuid")
@EO(EOClazz = VmInstanceEO.class, needView = false)
@AutoDeleteTag
public class OvnControllerVmInstanceVO extends ApplianceVmVO {
    public OvnControllerVmInstanceVO(ApplianceVmVO other) {
        super(other);
    }

    public OvnControllerVmInstanceVO() {
    }
}
