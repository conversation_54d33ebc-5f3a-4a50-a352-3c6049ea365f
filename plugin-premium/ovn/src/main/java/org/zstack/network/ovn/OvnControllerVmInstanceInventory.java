package org.zstack.network.ovn;

import org.zstack.appliancevm.ApplianceVmInventory;
import org.zstack.header.search.Inventory;
import org.zstack.header.search.Parent;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Inventory(mappingVOClass = OvnControllerVmInstanceVO.class, collectionValueOfMethod="valueOf2",
        parent = {@Parent(inventoryClass = ApplianceVmInventory.class, type = OvnControllerConstant.OVN_CONTROLLER_APPLIANCE_VM_TYPE)})
public class OvnControllerVmInstanceInventory extends ApplianceVmInventory {

    protected OvnControllerVmInstanceInventory(OvnControllerVmInstanceVO vo) {
        super(vo);
    }

    public OvnControllerVmInstanceInventory() {
    }

    public static OvnControllerVmInstanceInventory valueOf(OvnControllerVmInstanceVO vo) {
        return new OvnControllerVmInstanceInventory(vo);
    }

    public static List<OvnControllerVmInstanceInventory> valueOf2(Collection<OvnControllerVmInstanceVO> ovnVmInstanceVOS) {
        List<OvnControllerVmInstanceInventory> invs = new ArrayList<OvnControllerVmInstanceInventory>();
        for (OvnControllerVmInstanceVO vo : ovnVmInstanceVOS) {
            invs.add(valueOf(vo));
        }
        return invs;
    }
}
