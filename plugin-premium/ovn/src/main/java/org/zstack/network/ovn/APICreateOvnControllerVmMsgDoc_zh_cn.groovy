package org.zstack.network.ovn

import org.zstack.network.ovn.APICreateOvnControllerVmEvent

doc {
    title "CreateOvnControllerVm"

    category "Ovn"

    desc """创建OVN控制器实例"""

    rest {
        request {
			url "POST /v1/ovn/instances"

			header (Authorization: 'OAuth the-session-uuid')

            clz APICreateOvnControllerVmMsg.class

            desc """"""
            
			params {

				column {
					name "name"
					enclosedIn "params"
					desc "OVN控制器实例名称"
					location "body"
					type "String"
					optional false
					since "5.3.0"
				}
				column {
					name "instanceOfferingUuid"
					enclosedIn "params"
					desc "OVN控制器规格Uuid"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "cpuNum"
					enclosedIn "params"
					desc ""
					location "body"
					type "Integer"
					optional true
					since "5.3.0"
				}
				column {
					name "memorySize"
					enclosedIn "params"
					desc ""
					location "body"
					type "Long"
					optional true
					since "5.3.0"
				}
				column {
					name "reservedMemorySize"
					enclosedIn "params"
					desc ""
					location "body"
					type "Long"
					optional true
					since "5.3.0"
				}
				column {
					name "imageUuid"
					enclosedIn "params"
					desc "镜像UUID"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "l3NetworkUuids"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "vmNicParams"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "type"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
					values ("UserVm","ApplianceVm")
				}
				column {
					name "rootDiskOfferingUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "rootDiskSize"
					enclosedIn "params"
					desc ""
					location "body"
					type "Long"
					optional true
					since "5.3.0"
				}
				column {
					name "dataDiskSizes"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "dataDiskOfferingUuids"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "zoneUuid"
					enclosedIn "params"
					desc "区域UUID"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "clusterUuid"
					enclosedIn "params"
					desc "集群UUID"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "hostUuid"
					enclosedIn "params"
					desc "物理机UUID"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "primaryStorageUuidForRootVolume"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "defaultL3NetworkUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "strategy"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
					values ("InstantStart","JustCreate","CreateStopped")
				}
				column {
					name "rootVolumeSystemTags"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "dataVolumeSystemTags"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "dataVolumeSystemTagsOnIndex"
					enclosedIn "params"
					desc ""
					location "body"
					type "Map"
					optional true
					since "5.3.0"
				}
				column {
					name "sshKeyPairUuids"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "platform"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
					values ("Linux","Windows","Other","Paravirtualization","WindowsVirtio")
				}
				column {
					name "guestOsType"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "architecture"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
					values ("x86_64","aarch64","mips64el","loongarch64")
				}
				column {
					name "virtio"
					enclosedIn "params"
					desc ""
					location "body"
					type "Boolean"
					optional true
					since "5.3.0"
				}
				column {
					name "diskAOs"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc "资源UUID"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
			}
        }

        response {
            clz APICreateOvnControllerVmEvent.class
        }
    }
}