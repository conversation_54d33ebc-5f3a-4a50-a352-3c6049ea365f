package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.appliancevm.ApplianceVmBase;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.ha.HaSystemTags;
import org.zstack.ha.VmHaLevel;
import org.zstack.header.core.Completion;
import org.zstack.header.core.workflow.Flow;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.host.HypervisorType;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.vm.*;
import org.zstack.network.service.virtualrouter.vyos.VyosDeployAgentFlow;
import org.zstack.network.service.virtualrouter.vyos.VyosGetVersionFlow;
import org.zstack.network.service.virtualrouter.vyos.VyosWaitAgentStartFlow;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnControllerVm extends ApplianceVmBase {

    private static final CLogger logger = Utils.getLogger(OvnControllerVm.class);

    @Autowired
    private OvnControllerVmFactory ovnf;

    public OvnControllerVm(OvnControllerVmInstanceVO vo) {
        super(vo);
    }

    protected OvnControllerVmInstanceVO getSelf() {
        return (OvnControllerVmInstanceVO)self;
    }

    public OvnControllerVm(VmInstanceVO vo) {
        super(vo);
    }

    protected OvnControllerVmInstanceInventory getSelfInv() {
        return OvnControllerVmInstanceInventory.valueOf((OvnControllerVmInstanceVO)self);
    }

    @Override
    public List<String> getSnatL3NetworkOnRouter(String vrUuid) {
        return new ArrayList<>();
    }

    @Override
    public void detachNetworkService(String vrUuid, String networkServiceType, String l3NetworkUuid) {

    }

    @Override
    public void attachNetworkService(String vrUuid, String networkServiceType, String l3NetworkUuid) {

    }

    protected List<Flow> createBootstrapFlows(HypervisorType hvType) {
        List<Flow> flows = new ArrayList<>();
        return flows;
    }

    List<Flow> getOvnControllerVmPostStartFlows() {
        List<Flow> flows = new ArrayList<>();
        flows.add(new OvnControllerVmStartOvnFlow());
        return flows;
    }

    private Flow getSetHaLevelFlow() {
        return new NoRollbackFlow() {
            String __name__ = "set-ha-level";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                final VmInstanceSpec spec = (VmInstanceSpec) data.get(VmInstanceConstant.Params.VmInstanceSpec.toString());
                OvnControllerVmInstanceInventory ovn = OvnControllerVmInstanceInventory.valueOf(
                        dbf.findByUuid(spec.getVmInventory().getUuid(), OvnControllerVmInstanceVO.class));

                SystemTagCreator creator = HaSystemTags.HA.newSystemTagCreator(ovn.getUuid());
                creator.inherent = true;
                creator.setTagByTokens(map(e(HaSystemTags.HA_TOKEN, VmHaLevel.NeverStop.toString())));
                creator.create();

                trigger.next();
            }
        };
    }

    @Override
    protected List<Flow> getPostCreateFlows() {
        List<Flow> flows = new ArrayList<>();
        flows.add(getSetHaLevelFlow());
        flows.addAll(getOvnControllerVmPostStartFlows());
        return flows;
    }

    @Override
    protected List<Flow> getPostStartFlows() {
        return getOvnControllerVmPostStartFlows();
    }

    @Override
    protected List<Flow> getPostStopFlows() {
        return new ArrayList<>();
    }

    @Override
    protected List<Flow> getPostRebootFlows() {
        return getOvnControllerVmPostStartFlows();
    }

    @Override
    protected List<Flow> getPostDestroyFlows() {
        return new ArrayList<>();
    }

    @Override
    protected List<Flow> getPostMigrateFlows() {
        return new ArrayList<>();
    }

    @Override
    protected void afterAttachNic(VmNicInventory nicInventory, Completion completion) {
        afterAttachNic(nicInventory, true, completion);
    }

    @Override
    protected void afterAttachNic(VmNicInventory nicInventory, boolean applyToBackend, Completion completion) {
        completion.success();
    }

    @Override
    protected void afterDetachNic(VmNicInventory nicInventory, boolean isRollback, Completion completion) {
                completion.success();
    }

    @Override
    protected void beforeDetachNic(VmNicInventory nicInventory, Completion completion) {
        completion.success();
    }

    @Override
    @MessageSafe
    protected void handleLocalMessage(Message msg) {
        super.handleLocalMessage(msg);
    }

    @Override
    @MessageSafe
    protected void handleApiMessage(APIMessage msg) {
        super.handleApiMessage(msg);
    }
}
