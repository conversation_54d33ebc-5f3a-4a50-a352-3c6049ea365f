package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.config.GlobalConfig;
import org.zstack.core.config.GlobalConfigUpdateExtensionPoint;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.thread.AsyncThread;
import org.zstack.core.tracker.PingTracker;
import org.zstack.header.core.Completion;
import org.zstack.header.managementnode.ManagementNodeChangeListener;
import org.zstack.header.managementnode.ManagementNodeInventory;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.message.MessageReply;
import org.zstack.header.message.NeedReplyMessage;
import org.zstack.header.network.l2.SdnControllerDeleteExtensionPoint;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.network.service.virtualrouter.PingVirtualRouterVmReply;
import org.zstack.sdnController.SdnControllerManager;
import org.zstack.sdnController.SdnControllerManagerImpl;
import org.zstack.sdnController.header.*;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.util.List;

/**
 * Created by shixin on 12/12/2024.
 */
public class OvnControllerPingTracker extends PingTracker implements
        ManagementNodeChangeListener, ManagementNodeReadyExtensionPoint, SdnControllerDeleteExtensionPoint {
    private final static CLogger logger = Utils.getLogger(OvnControllerPingTracker.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private ResourceDestinationMaker destinationMaker;
    @Autowired
    protected PluginRegistry pluginRgty;
    @Autowired
    protected SdnControllerManager sdnMgr;

    public String getResourceName() {
        return "ovn controller";
    }

    // ovn controller will only ping when it's connected or disconnected,
    // if ping failed, it will send ReconnectSdnControllerMsg,
    @Override
    public NeedReplyMessage getPingMessage(String resUuid) {
        SdnControllerVO vo = dbf.findByUuid(resUuid, SdnControllerVO.class);
        if (vo.getStatus() == SdnControllerStatus.Connecting) {
            return null;
        }

        OvnControllerPingMsg msg = new OvnControllerPingMsg();
        msg.setOvnControllerUuid(resUuid);
        bus.makeTargetServiceIdByResourceUuid(msg, OvnControllerConstant.SERVICE_ID, resUuid);
        return msg;
    }

    @Override
    public int getPingInterval() {
        return OvnControllerGlobalConfig.PING_INTERVAL.value(Integer.class);
    }

    @Override
    public int getParallelismDegree() {
        return OvnControllerGlobalConfig.PING_PARALLELISM_DEGREE.value(Integer.class);
    }

    @Override
    public void handleReply(final String resourceUuid, MessageReply reply) {
        if (!reply.isSuccess()) {
            logger.warn(String.format("[Ovn Ping Tracker]: unable to ping the ovn controller[uuid: %s], %s", resourceUuid, reply.getError()));
            return;
        }

        OvnControllerPingReply pr = reply.castReply();
        if (pr.doReconnect) {
            ReconnectSdnControllerMsg msg = new ReconnectSdnControllerMsg();
            msg.setControllerUUid(resourceUuid);
            bus.makeTargetServiceIdByResourceUuid(msg, SdnControllerConstant.SERVICE_ID, resourceUuid);
            bus.send(msg);
        }
    }

    private void trackOurs() {
        List<String> ovnControllerUuids = Q.New(SdnControllerVO.class)
                .eq(SdnControllerVO_.vendorType, OvnControllerConstant.OVN_CONTROLLER_TYPE)
                .select(SdnControllerVO_.uuid).listValues();
        List<String> toTrack = CollectionUtils.transformToList(ovnControllerUuids, new Function<String, String>() {
            @Override
            public String call(String arg) {
                return destinationMaker.isManagedByUs(arg) ? arg : null;
            }
        });

        untrackAll();
        track(toTrack);
    }

    @Override
    public void nodeJoin(ManagementNodeInventory inv) {
        trackOurs();
    }

    @Override
    public void nodeLeft(ManagementNodeInventory inv) {
        trackOurs();
    }

    @Override
    public void iAmDead(ManagementNodeInventory inv) {

    }

    @Override
    public void iJoin(ManagementNodeInventory inv) {
    }

    @Override
    protected void startHook() {
        OvnControllerGlobalConfig.PING_INTERVAL.installUpdateExtension(new GlobalConfigUpdateExtensionPoint() {
            @Override
            public void updateGlobalConfig(GlobalConfig oldConfig, GlobalConfig newConfig) {
                pingIntervalChanged();
            }
        });
    }

    @Override
    @AsyncThread
    public void managementNodeReady() {
        trackOurs();
    }

    @Override
    protected void trackHook(String resourceUuid) {
        super.trackHook(resourceUuid);
    }

    @Override
    public void deleteNetworkServiceOfSdnController(String sdnControllerUuid, Completion completion) {
        super.untrack(sdnControllerUuid);
        completion.success();
    }
}
