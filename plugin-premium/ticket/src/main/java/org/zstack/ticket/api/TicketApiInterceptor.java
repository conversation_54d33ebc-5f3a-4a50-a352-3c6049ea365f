package org.zstack.ticket.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.db.Q;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.apimediator.StopRoutingException;
import org.zstack.header.identity.IdentityByPassCheck;
import org.zstack.header.longjob.APISubmitLongJobEvent;
import org.zstack.header.longjob.LongJobInventory;
import org.zstack.header.longjob.LongJobVO;
import org.zstack.header.message.APIMessage;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO_;
import org.zstack.identity.AccountManager;
import org.zstack.portal.apimediator.ApiMediator;
import org.zstack.portal.apimediator.PortApiValidator;
import org.zstack.ticket.TicketAccountSystemType;
import org.zstack.ticket.TicketExecuteHelper;
import org.zstack.ticket.TicketExecutor;
import org.zstack.ticket.iam2.entity.IAM2TicketFlowCollectionVO;
import org.zstack.ticket.iam2.entity.IAM2TicketFlowCollectionVO_;
import org.zstack.ticket.type.TicketTypeHelper;
import org.zstack.ticket.entity.*;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.*;

import static org.zstack.core.Platform.argerr;

public class TicketApiInterceptor implements ApiMessageInterceptor {
    private static final CLogger logger = Utils.getLogger(TicketApiInterceptor.class);

    @Autowired
    private AccountManager accountManager;
    @Autowired
    private ApiMediator apiMediator;

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreateTicketMsg) {
            validate((APICreateTicketMsg) msg);
        } else if (msg instanceof APIChangeTicketStatusMsg) {
            validate((APIChangeTicketStatusMsg) msg);
        }

        return msg;
    }

    private void validate(APIChangeTicketStatusMsg msg) {
        String flowCollectionUuid = Q.New(TicketVO.class).select(TicketVO_.flowCollectionUuid).eq(TicketVO_.uuid, msg.getUuid()).findValue();

        boolean invalid = Q.New(TicketFlowCollectionVO.class).eq(TicketFlowCollectionVO_.status, TicketFlowCollectionStatus.Invalid).eq(TicketFlowCollectionVO_.uuid, flowCollectionUuid).isExists();
        if (invalid) {
            throw new ApiMessageInterceptionException(argerr("Ticket flow collection[uuid:%s] is invalid, contact admin to correct it", flowCollectionUuid));
        }

        boolean disabled = Q.New(TicketFlowCollectionVO.class).eq(TicketFlowCollectionVO_.state, TicketFlowCollectionState.Disabled).eq(TicketFlowCollectionVO_.uuid, flowCollectionUuid).isExists();
        if (disabled) {
            throw new ApiMessageInterceptionException(argerr("Ticket flow collection[uuid:%s] is disable, can not be used", flowCollectionUuid));
        }
    }

    private void validate(APICreateTicketMsg msg) {
        if (!TicketAccountSystemType.hasType(msg.getAccountSystemType())) {
            throw new ApiMessageInterceptionException(argerr("no accountSystemType[%s] defined in system", msg.getAccountSystemType()));
        }

        String ticketTypeUuid = getSuitableTicketType(msg.getRequests());

        if (ticketTypeUuid == null) {
            throw new ApiMessageInterceptionException(argerr("not matched ticket type found"));
        }

        msg.setTicketTypeUuid(ticketTypeUuid);

        if (msg.getFlowCollectionUuid() == null) {
            String flowCollectionUuid = TicketTypeHelper.getSuitableFlowCollectionByAccount(msg.getSession().getAccountUuid(), msg.getTicketTypeUuid());

            if (flowCollectionUuid == null) {
                throw new ApiMessageInterceptionException(argerr("no matched ticket flow collection or no default ticket flow collection found," +
                        " you must specify the flowCollectionUuid or create a default ticket flow collection in system"));
            }

            msg.setFlowCollectionUuid(flowCollectionUuid);
        } else {
            if (notDefaultFlowAndTicketTypeMismatch(msg.getFlowCollectionUuid(), msg.getTicketTypeUuid())) {
                throw new ApiMessageInterceptionException(argerr("Ticket flow collection[uuid:%s] not matches ticket type[uuid:%s]",
                        msg.getFlowCollectionUuid(), ticketTypeUuid));
            }
        }

        boolean invalid = Q.New(TicketFlowCollectionVO.class).eq(TicketFlowCollectionVO_.status, TicketFlowCollectionStatus.Invalid).eq(TicketFlowCollectionVO_.uuid, msg.getFlowCollectionUuid()).isExists();
        if (invalid) {
            throw new ApiMessageInterceptionException(argerr("Ticket flow collection[uuid:%s] is invalid, contact admin to correct it", msg.getFlowCollectionUuid()));
        }

        boolean disabled = Q.New(TicketFlowCollectionVO.class).eq(TicketFlowCollectionVO_.state, TicketFlowCollectionState.Disabled).eq(TicketFlowCollectionVO_.uuid, msg.getFlowCollectionUuid()).isExists();
        if (disabled) {
            throw new ApiMessageInterceptionException(argerr("Ticket flow collection[uuid:%s] is disable, can not be used", msg.getFlowCollectionUuid()));
        }

        msg.getRequests().forEach(req -> {
            Class clz;
            try {
                clz = Class.forName(req.apiName);
            } catch (ClassNotFoundException e) {
                throw new ApiMessageInterceptionException(argerr("invalid request. no API[%s] found", req.apiName));
            }

            APIMessage api;
            try {
                api = JSONObjectUtil.rehashObject(req.apiBody, (Class<APIMessage>) clz);
            } catch (Exception e) {
                logger.warn(e.getMessage(), e);
                throw new ApiMessageInterceptionException(argerr("invalid request, cannot create API[%s] from apiBody, %s", clz, e.getMessage()));
            }

            TicketExecutor ticketExecutor = TicketExecuteHelper.newTicketExecutor(req.apiName);
            TicketInventory inv = new TicketInventory();
            inv.setAccountSystemType(msg.getAccountSystemType());
            inv.setAccountSystemContext(msg.getAccountSystemContext());
            api.setSession(ticketExecutor.createSessionFromTicket(inv));
            api.putHeaderEntry(IdentityByPassCheck.NoRBACCheck.toString(), true);
            api.putHeaderEntry(IdentityByPassCheck.NoSessionEvaluation.toString(), true);

            try {
                apiMediator.getProcesser().process(api);
            } catch (StopRoutingException ignored) { }
        });
    }

    private boolean notDefaultFlowAndTicketTypeMismatch(String flowCollectionUuid, String ticketTypeUuid) {
        return !Q.New(TicketFlowCollectionVO.class)
                .eq(TicketFlowCollectionVO_.uuid, flowCollectionUuid)
                .eq(TicketFlowCollectionVO_.isDefault, true).isExists() &&
                !Q.New(TicketTypeTicketFlowCollectionRefVO.class)
                        .eq(TicketTypeTicketFlowCollectionRefVO_.ticketTypeUuid, ticketTypeUuid)
                        .eq(TicketTypeTicketFlowCollectionRefVO_.ticketFlowCollectionUuid, flowCollectionUuid)
                        .isExists();
    }

    private String getSuitableTicketType(List<TicketRequest> requests) {
        // TODO: get types and api requests from db
        Map<String, Integer> types = TicketTypeHelper.getTicketTypesFromApiRequests(requests);

        if (types.size() != 1){
            logger.debug(String.format("ticket can not match specific ticket type, requests: %s", requests.toString()));
            return null;
        }

        Optional opt = types.entrySet().stream().max(Comparator.comparing(Map.Entry::getValue));

        if (!opt.isPresent()) {
           return null;
        }

        return ((Map.Entry<String, Integer>)opt.get()).getKey();
    }
}
