package org.zstack.ticket.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.ticket.entity.TicketFlowCollectionInventory;

import java.util.ArrayList;

/**
 * Created by kayo on 2018/8/10.
 */
@RestResponse(allTo = "inventory")
public class APIChangeTicketFlowCollectionStateEvent extends APIEvent {
    private TicketFlowCollectionInventory inventory;

    public TicketFlowCollectionInventory getInventory() {
        return inventory;
    }

    public void setInventory(TicketFlowCollectionInventory inventory) {
        this.inventory = inventory;
    }

    public APIChangeTicketFlowCollectionStateEvent(String apiId) {
        super(apiId);
    }

    public APIChangeTicketFlowCollectionStateEvent() {}

    public static APIChangeTicketFlowCollectionStateEvent __example__() {
        APIChangeTicketFlowCollectionStateEvent event = new APIChangeTicketFlowCollectionStateEvent();
        event.setInventory(TicketFlowCollectionInventory.__example__());
        return event;
    }
}
