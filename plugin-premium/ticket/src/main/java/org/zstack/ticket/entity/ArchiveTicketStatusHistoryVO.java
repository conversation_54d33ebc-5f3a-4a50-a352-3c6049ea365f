package org.zstack.ticket.entity;

import org.zstack.core.Platform;
import org.zstack.header.vo.ToInventory;

import javax.persistence.*;
import java.sql.Timestamp;

@Entity
@Table
public class ArchiveTicketStatusHistoryVO implements ToInventory {
    @Column
    @Id
    private String uuid;
    @Column
    private int sequence;
    @Column
    private String historyUuid;
    @Column
    private String accountUuid;
    @Column
    private String ticketUuid;
    @Column
    @Enumerated(EnumType.STRING)
    private TicketStatus fromStatus;
    @Column
    @Enumerated(EnumType.STRING)
    private TicketStatus toStatus;
    @Column
    private String comment;
    @Column
    private String operationContextType;
    @Column
    private String operationContext;
    @Column
    private String operatorUuid;
    @Column
    private String operatorType;
    @Column
    private String flowName;
    @Column
    private Timestamp createDate;
    @Column
    private Timestamp lastOpDate;

    public ArchiveTicketStatusHistoryVO() {
    }

    public ArchiveTicketStatusHistoryVO(TicketStatusHistoryVO other) {
        this.uuid = Platform.getUuid();
        this.historyUuid = other.getUuid();
        this.ticketUuid = other.getTicketUuid();
        this.fromStatus = other.getFromStatus();
        this.toStatus = other.getToStatus();
        this.accountUuid = other.getAccountUuid();
        this.comment = other.getComment();
        this.operationContextType = other.getOperationContextType();
        this.operationContext = other.getOperationContext();
        this.operatorUuid = other.getOperatorUuid();
        this.operatorType = other.getOperatorType();
        this.flowName = other.getFlowName();
        this.createDate = other.getCreateDate();
        this.lastOpDate = other.getLastOpDate();
    }

    public String getAccountUuid() {
        return accountUuid;
    }

    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getHistoryUuid() {
        return historyUuid;
    }

    public void setHistoryUuid(String historyUuid) {
        this.historyUuid = historyUuid;
    }

    public String getOperationContextType() {
        return operationContextType;
    }

    public void setOperationContextType(String operationContextType) {
        this.operationContextType = operationContextType;
    }

    public String getOperationContext() {
        return operationContext;
    }

    public void setOperationContext(String operationContext) {
        this.operationContext = operationContext;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }

    public String getTicketUuid() {
        return ticketUuid;
    }

    public void setTicketUuid(String ticketUuid) {
        this.ticketUuid = ticketUuid;
    }

    public TicketStatus getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(TicketStatus fromStatus) {
        this.fromStatus = fromStatus;
    }

    public TicketStatus getToStatus() {
        return toStatus;
    }

    public void setToStatus(TicketStatus toStatus) {
        this.toStatus = toStatus;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getOperatorUuid() {
        return operatorUuid;
    }

    public void setOperatorUuid(String operatorUuid) {
        this.operatorUuid = operatorUuid;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public int getSequence() { return sequence; }

    public void setSequence(int sequence) { this.sequence = sequence; }
}
