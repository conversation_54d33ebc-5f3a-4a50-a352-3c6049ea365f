package org.zstack.ticket.message;

import org.zstack.header.message.NeedReplyMessage;
import org.zstack.ticket.api.TicketMessage;

/**
 * Created by kayo on 2018/8/15.
 */
public class ResetTicketMsg extends NeedReplyMessage implements TicketMessage {
    private String uuid;
    private String collectionUuid;

    public String getCollectionUuid() {
        return collectionUuid;
    }

    public void setCollectionUuid(String collectionUuid) {
        this.collectionUuid = collectionUuid;
    }

    @Override
    public String getTicketUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
