package org.zstack.ticket.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.api.APIAddIAM2VirtualIDsToOrganizationEvent;

@RestResponse
public class APIAddTicketTypesToTicketFlowCollectionEvent extends APIEvent {
    public static APIAddTicketTypesToTicketFlowCollectionEvent __example__() {
        APIAddTicketTypesToTicketFlowCollectionEvent evt = new APIAddTicketTypesToTicketFlowCollectionEvent();
        return evt;
    }

    public APIAddTicketTypesToTicketFlowCollectionEvent() {
    }

    public APIAddTicketTypesToTicketFlowCollectionEvent(String apiId) {
        super(apiId);
    }
}
