package org.zstack.ticket.api

import org.zstack.ticket.api.APIQueryTicketFlowCollectionReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryTicketFlowCollection"

    category "ticket"

    desc """查询工单流程"""

    rest {
        request {
			url "GET /v1/tickets/flow-collections"
			url "GET /v1/tickets/flow-collections/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryTicketFlowCollectionMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryTicketFlowCollectionReply.class
        }
    }
}