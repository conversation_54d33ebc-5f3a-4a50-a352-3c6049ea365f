package org.zstack.ticket.entity;

import org.zstack.header.search.Inventory;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Inventory(mappingVOClass = TicketOperatorVO.class)
public class TicketOperatorInventory {
    private String uuid;
    private String operatorAccountUuid;
    private String operatorType;
    private String operatorContext;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public static TicketOperatorInventory valueOf(TicketOperatorVO vo) {
        TicketOperatorInventory inv = new TicketOperatorInventory();
        inv.uuid = vo.getUuid();
        inv.operatorAccountUuid = vo.getOperatorAccountUuid();
        inv.operatorType = vo.getOperatorContext();
        inv.operatorContext = vo.getOperatorContext();
        inv.createDate = vo.getCreateDate();
        inv.lastOpDate = vo.getLastOpDate();
        return inv;
    }

    public static List<TicketOperatorInventory> valueOf(Collection<TicketOperatorVO> vos) {
        return vos.stream().map(TicketOperatorInventory::valueOf).collect(Collectors.toList());
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getOperatorAccountUuid() {
        return operatorAccountUuid;
    }

    public void setOperatorAccountUuid(String operatorAccountUuid) {
        this.operatorAccountUuid = operatorAccountUuid;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }

    public String getOperatorContext() {
        return operatorContext;
    }

    public void setOperatorContext(String operatorContext) {
        this.operatorContext = operatorContext;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
