package org.zstack.ticket.entity;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

@StaticMetamodel(TicketFlowCollectionVO.class)
public class TicketFlowCollectionVO_ extends ResourceVO_ {
    public static SingularAttribute<TicketFlowCollectionVO, String> name;
    public static SingularAttribute<TicketFlowCollectionVO, String> description;
    public static SingularAttribute<TicketFlowCollectionVO, Boolean> isDefault;
    public static SingularAttribute<TicketFlowCollectionVO, TicketFlowCollectionStatus> status;
    public static SingularAttribute<TicketFlowCollectionVO, TicketFlowCollectionState> state;
    public static SingularAttribute<TicketFlowCollectionVO, Timestamp> createDate;
    public static SingularAttribute<TicketFlowCollectionVO, Timestamp> lastOpDate;
}
