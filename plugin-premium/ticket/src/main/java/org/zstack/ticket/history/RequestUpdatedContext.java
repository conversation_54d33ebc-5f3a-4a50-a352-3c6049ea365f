package org.zstack.ticket.history;

import org.zstack.utils.gson.JSONObjectUtil;

public class RequestUpdatedContext {
    public Object originalRequests;
    public Object updatedRequests;

    public RequestUpdatedContext(Object originalRequests, Object updatedRequests) {
        this.originalRequests = originalRequests;
        this.updatedRequests = updatedRequests;
    }

    public RequestUpdatedContext() {
    }

    public String toJSON() {
        return JSONObjectUtil.toJsonString(this);
    }
}
