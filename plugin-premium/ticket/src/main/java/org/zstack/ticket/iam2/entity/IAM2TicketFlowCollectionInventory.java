package org.zstack.ticket.iam2.entity;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;
import org.zstack.header.search.Parent;
import org.zstack.network.l2.vxlan.vxlanNetwork.L2VxlanNetworkInventory;
import org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkVO;
import org.zstack.ticket.entity.TicketFlowCollectionInventory;
import org.zstack.ticket.entity.TicketFlowInventory;
import org.zstack.ticket.iam2.IAM2TicketConstants;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by kayo on 2018/8/10.
 */
@PythonClassInventory
@Inventory(mappingVOClass = IAM2TicketFlowCollectionVO.class, collectionValueOfMethod = "valueOf1",
        parent = {@Parent(inventoryClass = TicketFlowCollectionInventory.class, type = IAM2TicketConstants.IAM2_TICKET_COLLECTION)})
public class IAM2TicketFlowCollectionInventory extends TicketFlowCollectionInventory {
    private String projectUuid;

    public IAM2TicketFlowCollectionInventory() {

    }

    protected IAM2TicketFlowCollectionInventory(IAM2TicketFlowCollectionVO vo) {
        super(vo);
        this.setProjectUuid(vo.getProjectUuid());
    }

    public static IAM2TicketFlowCollectionInventory valueOf(IAM2TicketFlowCollectionVO vo) {
        return new IAM2TicketFlowCollectionInventory(vo);
    }

    public static List<IAM2TicketFlowCollectionInventory> valueOf1(Collection<IAM2TicketFlowCollectionVO> vos) {
        List<IAM2TicketFlowCollectionInventory> invs = new ArrayList<IAM2TicketFlowCollectionInventory>(vos.size());
        for (IAM2TicketFlowCollectionVO vo : vos) {
            invs.add(new IAM2TicketFlowCollectionInventory(vo));
        }
        return invs;
    }

    public String getProjectUuid() {
        return projectUuid;
    }

    public void setProjectUuid(String projectUuid) {
        this.projectUuid = projectUuid;
    }
}
