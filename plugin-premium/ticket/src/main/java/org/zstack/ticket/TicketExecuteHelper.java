package org.zstack.ticket;

import org.zstack.header.core.StaticInit;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.ticket.executor.DefaultAdminSingletonRequestExecutor;
import org.zstack.ticket.executor.DefaultSingletonRequestExecutor;
import org.zstack.ticket.type.TicketInfo;
import org.zstack.ticket.type.TicketTypeHelper;
import org.zstack.utils.BeanUtils;

import java.util.HashMap;
import java.util.Map;

public class TicketExecuteHelper {
    private static Map<String, Class> ticketExecutors = new HashMap<>();

    @StaticInit
    static void staticInit() {
        BeanUtils.reflections.getSubTypesOf(TicketExecutor.class).forEach(clz -> {
            try {
                TicketExecutor exe = clz.getConstructor().newInstance();
                Class old = ticketExecutors.get(exe.getTicketRequestType());
                if (old != null) {
                    throw new CloudRuntimeException(String.format("duplicate TicketExecutor[%s, %s] with type[%s]", clz, old, exe.getTicketRequestType()));
                }

                String type = exe.getTicketRequestType();

                if (type == null) {
                    return;
                }

                ticketExecutors.put(exe.getTicketRequestType(), clz);
            } catch (Exception e) {
                throw new CloudRuntimeException(e);
            }
        });
    }

    public static TicketExecutor newTicketExecutor(String type) {
        Class clz = ticketExecutors.get(type);
        if (clz == null) {
            TicketInfo info = TicketTypeHelper.getTicketTypeByRequestName(type);

            if (info.isAdminOnly()) {
                return new DefaultAdminSingletonRequestExecutor();
            }

            return new DefaultSingletonRequestExecutor();
        }

        try {
            return (TicketExecutor) clz.getConstructor().newInstance();
        } catch (Exception e) {
            throw new CloudRuntimeException(e);
        }
    }
}
