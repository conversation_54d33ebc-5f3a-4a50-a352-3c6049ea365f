package org.zstack.ticket.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;
import org.zstack.ticket.entity.TicketTypeInventory;

import java.util.List;

import static java.util.Arrays.asList;

@AutoQuery(replyClass = APIQueryTicketTypeReply.class, inventoryClass = TicketTypeInventory.class)
@RestRequest(path = "/ticket-types", optionalPaths = {"/ticket-types/{uuid}"},
        method = HttpMethod.GET, responseClass = APIQueryTicketTypeReply.class)
public class APIQueryTicketTypeMsg extends APIQueryMessage {
    public static List<String> __example__() {
        return asList("uuid=" + uuid());
    }
}
