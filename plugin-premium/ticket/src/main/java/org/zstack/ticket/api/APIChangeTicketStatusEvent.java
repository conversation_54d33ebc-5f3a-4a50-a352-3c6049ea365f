package org.zstack.ticket.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.ticket.entity.TicketInventory;

@RestResponse(allTo = "inventory")
public class APIChangeTicketStatusEvent extends APIEvent {
    private TicketInventory inventory;

    public APIChangeTicketStatusEvent() {
    }

    public APIChangeTicketStatusEvent(String apiId) {
        super(apiId);
    }

    public TicketInventory getInventory() {
        return inventory;
    }

    public void setInventory(TicketInventory inventory) {
        this.inventory = inventory;
    }

    public static APIChangeTicketStatusEvent __example__() {
        APIChangeTicketStatusEvent evt = new APIChangeTicketStatusEvent();

        evt.setInventory(TicketInventory.__example__());

        return evt;
    }
}
