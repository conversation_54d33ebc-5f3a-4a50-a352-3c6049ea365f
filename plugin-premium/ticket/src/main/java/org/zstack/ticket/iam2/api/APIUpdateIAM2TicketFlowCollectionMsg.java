package org.zstack.ticket.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.NoDoc;
import org.zstack.header.rest.RestRequest;
import org.zstack.ticket.api.APIUpdateTicketFlowCollectionEvent;
import org.zstack.ticket.api.APIUpdateTicketFlowCollectionMsg;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by kayo on 2018/8/10.
 */
@RestRequest(
        path = "/tickets/flow-collections/{uuid}/actions",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APIUpdateTicketFlowCollectionEvent.class
)
public class APIUpdateIAM2TicketFlowCollectionMsg extends APIUpdateTicketFlowCollectionMsg {
    @Override
    public String getTicketFlowCollectionUuid() {
        return getUuid();
    }

    public static class IAM2FlowStruct {
        public String name;
        public String description;
        public String approverUuid;
        public String approverTitle;
    }

    private List<APIUpdateIAM2TicketFlowCollectionMsg.IAM2FlowStruct> flows;

    public List<APIUpdateIAM2TicketFlowCollectionMsg.IAM2FlowStruct> getFlows() {
        return flows;
    }

    public void setFlows(List<APIUpdateIAM2TicketFlowCollectionMsg.IAM2FlowStruct> flows) {
        this.flows = flows;
    }

    public static APIUpdateIAM2TicketFlowCollectionMsg  __exapmle__() {
        APIUpdateIAM2TicketFlowCollectionMsg msg = new APIUpdateIAM2TicketFlowCollectionMsg();

        msg.setUuid(uuid());

        List<APIUpdateIAM2TicketFlowCollectionMsg.IAM2FlowStruct> flows = new ArrayList<>();
        APIUpdateIAM2TicketFlowCollectionMsg.IAM2FlowStruct flow = new APIUpdateIAM2TicketFlowCollectionMsg.IAM2FlowStruct();
        flow.approverUuid = uuid();
        flow.name = "flow1";
        flows.add(flow);

        flow = new APIUpdateIAM2TicketFlowCollectionMsg.IAM2FlowStruct();
        flow.approverUuid = uuid();
        flow.name = "flow2";
        flows.add(flow);

        msg.setFlows(flows);

        return msg;
    }
}
