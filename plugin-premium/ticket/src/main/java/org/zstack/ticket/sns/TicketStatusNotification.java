package org.zstack.ticket.sns;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.sns.SNSConstants;
import org.zstack.sns.SNSPublishMsg;
import org.zstack.sns.platform.http.SNSHttpEndpointFactory;
import org.zstack.ticket.entity.TicketInventory;
import org.zstack.ticket.entity.TicketStatus;
import org.zstack.utils.gson.JSONObjectUtil;

import java.util.HashMap;
import java.util.Map;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class TicketStatusNotification {
    @Autowired
    private CloudBus bus;

    public static class Message {
        public TicketInventory ticket;
        public TicketStatus status;
        public String accountSystemType;
        public String operatorType;
        public String operatorUuid;
        public String operationType;
        public String flowName;
    }

    public void publish(Message message) {
        SNSPublishMsg msg = new SNSPublishMsg();
        bus.makeLocalServiceId(msg, SNSConstants.SERVICE_ID);
        msg.setTopicUuid(TicketSNSManager.TICKET_TOPIC_UUID);

        Map<String, String> body = new HashMap<>();
        Map<String, Object> metadata = new HashMap<>();
        createHttpMessage(message, body, metadata);
        msg.setMessage(body);
        msg.setMetadata(metadata);
        bus.send(msg);
    }


    private void createHttpMessage(Message message, Map<String,String> body, Map<String, Object> metadata) {
        body.put(SNSHttpEndpointFactory.type.toString(), JSONObjectUtil.toJsonString(message));
        metadata.put(SNSHttpEndpointFactory.type.toString(), new HashMap<>());
    }
}
