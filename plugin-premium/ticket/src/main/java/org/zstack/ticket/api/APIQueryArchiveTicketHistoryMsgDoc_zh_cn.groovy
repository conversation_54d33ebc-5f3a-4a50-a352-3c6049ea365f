package org.zstack.ticket.api

import org.zstack.ticket.api.APIQueryArchiveTicketHistoryReply
import org.zstack.header.query.APIQueryMessage
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryArchiveTicketHistory"

    category "ticket"

    desc """查询归档的工单历史信息"""

    rest {
        request {
			url "GET /v1/tickets/histories/archives"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryArchiveTicketHistoryMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryArchiveTicketHistoryReply.class
        }
    }
}