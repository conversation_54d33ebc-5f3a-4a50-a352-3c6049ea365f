package org.zstack.ticket.api

import org.zstack.ticket.api.APIChangeTicketFlowCollectionStateEvent

doc {
    title "ChangeTicketFlowCollectionState"

    category "ticket"

    desc """修改工单流程状态"""

    rest {
        request {
			url "PUT /v1/tickets/flow-collections/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIChangeTicketFlowCollectionStateMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn "changeTicketFlowCollectionState"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "3.0.0"
				}
				column {
					name "stateEvent"
					enclosedIn "changeTicketFlowCollectionState"
					desc ""
					location "body"
					type "String"
					optional false
					since "3.0.0"
					values ("enable","disable")
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
			}
        }

        response {
            clz APIChangeTicketFlowCollectionStateEvent.class
        }
    }
}