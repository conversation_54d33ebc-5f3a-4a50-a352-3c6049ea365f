package org.zstack.ticket.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.NoDoc;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.ticket.api.APIDeleteTicketFlowCollectionEvent;
import org.zstack.ticket.api.TicketFlowCollectionMessage;
import org.zstack.ticket.api.TicketFlowMessage;
import org.zstack.ticket.entity.TicketFlowCollectionVO;
import org.zstack.ticket.entity.TicketFlowVO;

/**
 * Created by kayo on 2018/8/11.
 */
@RestRequest(path = "/tickets/flow/{uuid}",
        method = HttpMethod.DELETE,
        responseClass = APIDeleteIAM2TicketFlowEvent.class
)
public class APIDeleteIAM2TicketFlowMsg extends APIDeleteMessage implements TicketFlowMessage {
    @APIParam(resourceType = TicketFlowVO.class, successIfResourceNotExisting = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public String getTickFlowUuid() {
        return uuid;
    }

    public static APIDeleteIAM2TicketFlowMsg __exapmle__() {
        APIDeleteIAM2TicketFlowMsg msg = new APIDeleteIAM2TicketFlowMsg();
        msg.setUuid(uuid());
        return msg;
    }
}
