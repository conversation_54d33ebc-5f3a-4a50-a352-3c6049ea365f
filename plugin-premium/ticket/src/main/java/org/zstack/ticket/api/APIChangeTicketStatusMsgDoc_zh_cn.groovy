package org.zstack.ticket.api

import org.zstack.ticket.api.APIChangeTicketStatusEvent

doc {
    title "ChangeTicketStatus"

    category "ticket"

    desc """修改工单状态"""

    rest {
        request {
			url "PUT /v1/tickets/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIChangeTicketStatusMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn "changeTicketStatus"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "3.0.0"
				}
				column {
					name "statusEvent"
					enclosedIn "changeTicketStatus"
					desc ""
					location "body"
					type "TicketStatusEvent"
					optional false
					since "3.0.0"
					values ("open","approve","cancel","reject","reopen")
				}
				column {
					name "comment"
					enclosedIn "changeTicketStatus"
					desc ""
					location "body"
					type "String"
					optional true
					since "3.0.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
			}
        }

        response {
            clz APIChangeTicketStatusEvent.class
        }
    }
}