package org.zstack.ticket.api;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;
import org.zstack.ticket.entity.TicketTypeInventory;
import org.zstack.ticket.type.CreateVmInstanceTicket;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

@RestResponse(allTo = "inventories")
public class APIQueryTicketTypeReply extends APIQueryReply {
    List<TicketTypeInventory> inventories;

    public List<TicketTypeInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<TicketTypeInventory> inventories) {
        this.inventories = inventories;
    }

    public static APIQueryTicketTypeReply __example__() {
        APIQueryTicketTypeReply reply = new APIQueryTicketTypeReply();

        TicketTypeInventory inv = new TicketTypeInventory();
        inv.setUuid(CreateVmInstanceTicket.CREATE_VM_INSTANCE_TICKET_UUID);
        inv.setCreateDate(new Timestamp(System.currentTimeMillis()));
        inv.setLastOpDate(new Timestamp(System.currentTimeMillis()));
        inv.setName(CreateVmInstanceTicket.CREATE_VM_INSTANCE_TICKET_TYPE);

        reply.setInventories(Collections.singletonList(inv));
        return reply;
    }
}
