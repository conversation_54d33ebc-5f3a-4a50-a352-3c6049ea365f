package org.zstack.ticket.entity;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

@StaticMetamodel(TicketFlowVO.class)
public class TicketFlowVO_ extends ResourceVO_ {
    public static SingularAttribute<TicketFlowVO, String> name;
    public static SingularAttribute<TicketFlowVO, String> description;
    public static SingularAttribute<TicketFlowVO, String> flowContext;
    public static SingularAttribute<TicketFlowVO, String> flowContextType;
    public static SingularAttribute<TicketFlowVO, String> parentFlowUuid;
    public static SingularAttribute<TicketFlowVO, String> collectionUuid;
    public static SingularAttribute<TicketFlowVO, Timestamp> createDate;
    public static SingularAttribute<TicketFlowVO, Timestamp> lastOpDate;
}
