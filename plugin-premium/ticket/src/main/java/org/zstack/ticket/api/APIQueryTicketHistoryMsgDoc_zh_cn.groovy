package org.zstack.ticket.api

import org.zstack.ticket.api.APIQueryTicketHistoryReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryTicketHistory"

    category "ticket"

    desc """查询工单历史"""

    rest {
        request {
			url "GET /v1/tickets/histories"
			url "GET /v1/tickets/histories/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryTicketHistoryMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryTicketHistoryReply.class
        }
    }
}