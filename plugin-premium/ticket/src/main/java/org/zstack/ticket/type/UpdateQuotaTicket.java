package org.zstack.ticket.type;

import org.zstack.header.identity.APIUpdateQuotaMsg;

import java.util.Arrays;
import java.util.List;

public class UpdateQuotaTicket implements TicketDescription {
    public static final String UPDATE_QUOTA_TICKET_UUID = "7745f49c383b419d93c5763ef15c861d";

    public static final String UPDATE_QUOTA_TICKET_TYPE = "UPDATE_QUOTA_TICKET_TYPE";


    @Override
    public void ticketTypeInfo() {
        ticketBuilder()
                .uuid(UPDATE_QUOTA_TICKET_UUID)
                .name(UPDATE_QUOTA_TICKET_TYPE)
                .type(UPDATE_QUOTA_TICKET_TYPE)
                .adminOnly(isAdminOnly())
                .requests(getTicketRequestClassNames())
                .build();
    }

    @Override
    public List<String> getTicketRequestClassNames() {
        return Arrays.asList(APIUpdateQuotaMsg.class.getCanonicalName());
    }

    @Override
    public boolean isAdminOnly() {
        return true;
    }
}
