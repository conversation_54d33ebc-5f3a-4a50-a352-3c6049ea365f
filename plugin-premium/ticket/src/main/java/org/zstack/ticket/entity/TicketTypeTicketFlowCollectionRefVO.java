package org.zstack.ticket.entity;

import org.zstack.header.vo.ForeignKey;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

@Entity
@Table
@IdClass(TicketTypeTicketFlowCollectionRefVO.CompositeID.class)
public class TicketTypeTicketFlowCollectionRefVO {
    static class CompositeID implements Serializable {
        private String ticketTypeUuid;
        private String ticketFlowCollectionUuid;

        public String getTicketTypeUuid() {
            return ticketTypeUuid;
        }

        public void setTicketTypeUuid(String ticketTypeUuid) {
            this.ticketTypeUuid = ticketTypeUuid;
        }

        public String getTicketFlowCollectionUuid() {
            return ticketFlowCollectionUuid;
        }

        public void setTicketFlowCollectionUuid(String ticketFlowCollectionUuid) {
            this.ticketFlowCollectionUuid = ticketFlowCollectionUuid;
        }
    }

    @Id
    @Column
    @org.zstack.header.vo.ForeignKey(parentEntityClass = TicketTypeVO.class, onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String ticketTypeUuid;
    @Id
    @Column
    @org.zstack.header.vo.ForeignKey(parentEntityClass = TicketFlowCollectionVO.class, onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String ticketFlowCollectionUuid;
    @Column
    private Timestamp createDate;
    @Column
    private Timestamp lastOpDate;

    public String getTicketTypeUuid() {
        return ticketTypeUuid;
    }

    public void setTicketTypeUuid(String ticketTypeUuid) {
        this.ticketTypeUuid = ticketTypeUuid;
    }

    public String getTicketFlowCollectionUuid() {
        return ticketFlowCollectionUuid;
    }

    public void setTicketFlowCollectionUuid(String ticketFlowCollectionUuid) {
        this.ticketFlowCollectionUuid = ticketFlowCollectionUuid;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
