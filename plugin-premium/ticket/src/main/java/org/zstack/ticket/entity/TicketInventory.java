package org.zstack.ticket.entity;

import org.zstack.core.Platform;
import org.zstack.header.message.NoJsonSchema;
import org.zstack.header.search.Inventory;
import org.zstack.header.vm.APICreateVmInstanceMsg;
import org.zstack.ticket.iam2.IAM2TicketContext;
import org.zstack.ticket.iam2.IAM2TicketManager;
import org.zstack.utils.gson.JSONObjectUtil;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Inventory(mappingVOClass = TicketVO.class)
public class TicketInventory {
    private String uuid;
    private String name;
    private String description;
    private TicketStatus status;
    @NoJsonSchema
    private List<TicketRequest> request;
    private String accountSystemType;
    private String ticketTypeUuid;
    @NoJsonSchema
    private Object accountSystemContext;
    private String currentFlowUuid;
    private String flowCollectionUuid;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public static TicketInventory valueOf(TicketVO vo) {
        TicketInventory inv = new TicketInventory();
        inv.uuid = vo.getUuid();
        inv.name = vo.getName();
        inv.description = vo.getDescription();
        inv.status = vo.getStatus();
        inv.request = TicketRequest.fromJSON(vo.getRequests());
        inv.currentFlowUuid = vo.getCurrentFlowUuid();
        inv.flowCollectionUuid = vo.getFlowCollectionUuid();
        inv.accountSystemType = vo.getAccountSystemType();
        inv.ticketTypeUuid = vo.getTicketTypeUuid();
        inv.accountSystemContext = JSONObjectUtil.toObject(vo.getAccountSystemContext(), LinkedHashMap.class);
        inv.createDate = vo.getCreateDate();
        inv.lastOpDate = vo.getLastOpDate();
        return inv;
    }

    public static TicketInventory __example__() {
        TicketInventory inv = new TicketInventory();
        inv.setUuid(Platform.getUuid());
        inv.setName("ticket");
        inv.setDescription("ticket description");
        inv.setFlowCollectionUuid(Platform.getUuid());
        inv.setStatus(TicketStatus.IntermediateApproved);
        inv.setTicketTypeUuid("3b933e9aaf2d49b9a3dcf0c92867790f");

        TicketRequest request = new TicketRequest();
        request.requestName = "create vm";

        APICreateVmInstanceMsg createVmInstanceMsg = new APICreateVmInstanceMsg();
        createVmInstanceMsg.setName("vm name");
        createVmInstanceMsg.setInstanceOfferingUuid(Platform.getUuid());
        createVmInstanceMsg.setL3NetworkUuids(Collections.singletonList(Platform.getUuid()));
        createVmInstanceMsg.setImageUuid(Platform.getUuid());

        request.apiBody = createVmInstanceMsg;
        request.executeTimes = 1;
        request.apiName = APICreateVmInstanceMsg.class.getName();

        inv.setRequest(Collections.singletonList(request));
        inv.setAccountSystemType(IAM2TicketManager.type);

        IAM2TicketContext context = new IAM2TicketContext();
        context.setProjectUuid(Platform.getUuid());
        context.setVirtualIDUuid(Platform.getUuid());

        inv.setAccountSystemContext(context);

        return inv;
    }

    public static List<TicketInventory> valueOf(Collection<TicketVO> vos) {
        return vos.stream().map(TicketInventory::valueOf).collect(Collectors.toList());
    }

    public <T> T getAccountSystemContextAs(Class<T> cz) {
        if (accountSystemContext == null) {
            return null;
        }

        return JSONObjectUtil.rehashObject(accountSystemContext, cz);
    }

    public String getAccountSystemType() {
        return accountSystemType;
    }

    public void setAccountSystemType(String accountSystemType) {
        this.accountSystemType = accountSystemType;
    }

    public Object getAccountSystemContext() {
        return accountSystemContext;
    }

    public void setAccountSystemContext(Object accountSystemContext) {
        this.accountSystemContext = accountSystemContext;
    }

    public String getCurrentFlowUuid() {
        return currentFlowUuid;
    }

    public void setCurrentFlowUuid(String currentFlowUuid) {
        this.currentFlowUuid = currentFlowUuid;
    }

    public String getFlowCollectionUuid() {
        return flowCollectionUuid;
    }

    public void setFlowCollectionUuid(String flowCollectionUuid) {
        this.flowCollectionUuid = flowCollectionUuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TicketStatus getStatus() {
        return status;
    }

    public void setStatus(TicketStatus status) {
        this.status = status;
    }

    public List<TicketRequest> getRequest() {
        return request;
    }

    public void setRequest(List<TicketRequest> request) {
        this.request = request;
    }

    public String getTicketTypeUuid() {
        return ticketTypeUuid;
    }

    public void setTicketTypeUuid(String ticketTypeUuid) {
        this.ticketTypeUuid = ticketTypeUuid;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
