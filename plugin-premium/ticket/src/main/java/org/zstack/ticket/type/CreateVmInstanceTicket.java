package org.zstack.ticket.type;

import org.zstack.header.vm.APICreateVmInstanceMsg;

import java.util.Arrays;
import java.util.List;

public class CreateVmInstanceTicket implements TicketDescription {
    public static final String CREATE_VM_INSTANCE_TICKET_UUID = "3b933e9aaf2d49b9a3dcf0c92867790f";

    public static final String CREATE_VM_INSTANCE_TICKET_TYPE = "CREATE_VM_INSTANCE_TICKET_TYPE";


    @Override
    public void ticketTypeInfo() {
        ticketBuilder()
                .uuid(CREATE_VM_INSTANCE_TICKET_UUID)
                .name(CREATE_VM_INSTANCE_TICKET_TYPE)
                .type(CREATE_VM_INSTANCE_TICKET_TYPE)
                .adminOnly(isAdminOnly())
                .requests(getTicketRequestClassNames())
                .build();
    }

    @Override
    public List<String> getTicketRequestClassNames() {
        return Arrays.asList(APICreateVmInstanceMsg.class.getCanonicalName());
    }

    @Override
    public boolean isAdminOnly() {
        return false;
    }
}
