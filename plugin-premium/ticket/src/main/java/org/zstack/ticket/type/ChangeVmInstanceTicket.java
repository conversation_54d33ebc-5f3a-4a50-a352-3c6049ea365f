package org.zstack.ticket.type;

import org.zstack.header.vm.APIAttachL3NetworkToVmMsg;
import org.zstack.header.vm.APIDetachL3NetworkFromVmMsg;
import org.zstack.header.vm.APIUpdateVmInstanceMsg;
import org.zstack.header.volume.APIResizeDataVolumeMsg;
import org.zstack.header.volume.APIResizeRootVolumeMsg;

import java.util.ArrayList;
import java.util.List;

public class ChangeVmInstanceTicket implements TicketDescription {
    public static final String CHANGE_VM_INSTANCE_TICKET_UUID = "ef064ef45fb446d381db7b7d5f71695c";

    public static final String CHANGE_VM_INSTANCE_TICKET_TYPE = "CHANGE_VM_INSTANCE_TICKET_TYPE";

    @Override
    public void ticketTypeInfo() {
        ticketBuilder()
                .uuid(CHANGE_VM_INSTANCE_TICKET_UUID)
                .name(CHANGE_VM_INSTANCE_TICKET_TYPE)
                .type(CHANGE_VM_INSTANCE_TICKET_TYPE)
                .adminOnly(isAdminOnly())
                .requests(getTicketRequestClassNames())
                .build();
    }

    @Override
    public List<String> getTicketRequestClassNames() {
        List<String> requests = new ArrayList<>();
        requests.add(APIAttachL3NetworkToVmMsg.class.getCanonicalName());
        requests.add(APIDetachL3NetworkFromVmMsg.class.getCanonicalName());
        requests.add(APIResizeRootVolumeMsg.class.getCanonicalName());
        requests.add(APIUpdateVmInstanceMsg.class.getCanonicalName());
        requests.add(APIResizeDataVolumeMsg.class.getCanonicalName());
        return requests;
    }

    @Override
    public boolean isAdminOnly() {
        return false;
    }
}
