package org.zstack.ticket.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.NoJsonSchema;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.vm.APICreateVmInstanceMsg;
import org.zstack.ticket.entity.TicketRequest;
import org.zstack.ticket.entity.TicketVO;

import java.util.Collections;
import java.util.List;

@RestRequest(path = "/tickets/{uuid}/actions", method = HttpMethod.PUT, isAction = true, responseClass = APIUpdateTicketRequestEvent.class)
public class APIUpdateTicketRequestMsg extends APIMessage implements TicketMessage {
    @APIParam(resourceType = TicketVO.class)
    private String uuid;
    @APIParam(nonempty = true)
    @NoJsonSchema
    private List<TicketRequest> requests;

    @APIParam(maxLength = 2048, required = false)
    private String description;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public List<TicketRequest> getRequests() {
        return requests;
    }

    public void setRequests(List<TicketRequest> requests) {
        this.requests = requests;
    }

    public String getDescription() {return description;}

    public void setDescription(String description) {this.description = description;}

    @Override
    public String getTicketUuid() {
        return uuid;
    }

    public static APIUpdateTicketRequestMsg __example__() {
        APIUpdateTicketRequestMsg msg = new APIUpdateTicketRequestMsg();
        msg.setUuid(uuid());

        TicketRequest request = new TicketRequest();
        request.requestName = "create vm";

        APICreateVmInstanceMsg createVmInstanceMsg = new APICreateVmInstanceMsg();
        createVmInstanceMsg.setName("vm name");
        createVmInstanceMsg.setInstanceOfferingUuid(uuid());
        createVmInstanceMsg.setL3NetworkUuids(Collections.singletonList(uuid()));
        createVmInstanceMsg.setImageUuid(uuid());

        request.apiBody = createVmInstanceMsg;
        request.executeTimes = 1;
        request.apiName = APICreateVmInstanceMsg.class.getName();

        msg.setRequests(Collections.singletonList(request));

        return msg;
    }
}
