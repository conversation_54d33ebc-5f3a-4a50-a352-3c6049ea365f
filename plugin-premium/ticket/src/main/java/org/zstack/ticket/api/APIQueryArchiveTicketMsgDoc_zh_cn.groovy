package org.zstack.ticket.api

import org.zstack.ticket.api.APIQueryArchiveTicketReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryArchiveTicket"

    category "ticket"

    desc """查询归档的工单"""

    rest {
        request {
			url "GET /v1/tickets/archives"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryArchiveTicketMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryArchiveTicketReply.class
        }
    }
}