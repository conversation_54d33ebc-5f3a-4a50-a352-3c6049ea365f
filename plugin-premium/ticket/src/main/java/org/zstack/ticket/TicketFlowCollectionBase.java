package org.zstack.ticket;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.ticket.api.*;
import org.zstack.ticket.entity.*;
import org.zstack.ticket.iam2.api.APIUpdateIAM2TicketFlowCollectionMsg;
import org.zstack.ticket.message.RepublishTicketMsg;
import org.zstack.ticket.message.TicketCollectionDeletionMsg;
import org.zstack.ticket.message.TicketCollectionDeletionReply;

/**
 * Created by kayo on 2018/8/10.
 */
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class TicketFlowCollectionBase implements TicketFlowCollection {
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected PluginRegistry pluginRgty;
    @Autowired
    private ThreadFacade thdf;

    protected String syncThreadName;

    protected TicketFlowCollectionVO self;

    public TicketFlowCollectionBase(TicketFlowCollectionVO self) {
        this.syncThreadName = "Ticket-flow-collection-" + self.getUuid();
        this.self = self;
    }

    public TicketFlowCollectionBase() {
    }

    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage(msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    @Override
    public void deleteHook() {
        new SQLBatch() {
            @Override
            protected void scripts() {
                sql(TicketFlowVO.class).eq(TicketFlowVO_.collectionUuid, self.getUuid()).delete();
                sql(TicketFlowCollectionVO.class).eq(TicketFlowCollectionVO_.uuid, self.getUuid()).delete();
                sql(TicketTypeTicketFlowCollectionRefVO.class).eq(TicketTypeTicketFlowCollectionRefVO_.ticketFlowCollectionUuid, self.getUuid()).hardDelete();
            }
        }.execute();
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof TicketCollectionDeletionMsg) {
            handle((TicketCollectionDeletionMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(TicketCollectionDeletionMsg msg) {
        deleteHook();
        TicketCollectionDeletionReply reply = new TicketCollectionDeletionReply();
        bus.reply(msg, reply);
    }

    private void handleApiMessage(Message msg) {
        if (msg instanceof APIChangeTicketFlowCollectionStateMsg) {
            handle((APIChangeTicketFlowCollectionStateMsg) msg);
        } else if (msg instanceof APIDeleteTicketFlowCollectionMsg) {
            handle((APIDeleteTicketFlowCollectionMsg) msg);
        } else if (msg instanceof APIUpdateIAM2TicketFlowCollectionMsg) {
            handle((APIUpdateIAM2TicketFlowCollectionMsg) msg);
        } else if (msg instanceof APIAddTicketTypesToTicketFlowCollectionMsg) {
            handle((APIAddTicketTypesToTicketFlowCollectionMsg) msg);
        } else if (msg instanceof APIRemoveTicketTypesFromTicketFlowCollectionMsg) {
            handle((APIRemoveTicketTypesFromTicketFlowCollectionMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APIAddTicketTypesToTicketFlowCollectionMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return syncThreadName;
            }

            @Override
            public void run(SyncTaskChain chain) {
                APIAddTicketTypesToTicketFlowCollectionEvent evt = new APIAddTicketTypesToTicketFlowCollectionEvent(msg.getId());

                for (String typeUuid : msg.getTicketTypeUuids()) {
                    TicketTypeTicketFlowCollectionRefVO ref = new TicketTypeTicketFlowCollectionRefVO();
                    ref.setTicketFlowCollectionUuid(msg.getTicketFlowCollectionUuid());
                    ref.setTicketTypeUuid(typeUuid);
                    dbf.persist(ref);
                }

                self = dbf.reload(self);
                for (AddTicketTypeToFlowCollectionExtensionPoint ext : pluginRgty.getExtensionList(AddTicketTypeToFlowCollectionExtensionPoint.class)) {
                    ext.afterAddTicketTypeToFlowCollection(TicketFlowCollectionInventory.valueOf(self));
                }

                bus.publish(evt);
                chain.next();
            }

            @Override
            public String getName() {
                return String.format("add-ticket-type-to-flow-collection-%s", self.getUuid());
            }
        });
    }

    private void handle(APIRemoveTicketTypesFromTicketFlowCollectionMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return syncThreadName;
            }

            @Override
            public void run(SyncTaskChain chain) {
                APIRemoveTicketTypesFromTicketFlowCollectionEvent evt = new APIRemoveTicketTypesFromTicketFlowCollectionEvent(msg.getId());

                SQL.New(TicketTypeTicketFlowCollectionRefVO.class)
                        .eq(TicketTypeTicketFlowCollectionRefVO_.ticketFlowCollectionUuid, msg.getTicketFlowCollectionUuid())
                        .in(TicketTypeTicketFlowCollectionRefVO_.ticketTypeUuid, msg.getTicketTypeUuids())
                        .hardDelete();

                RepublishTicketMsg rmsg = new RepublishTicketMsg();
                rmsg.setTicketTypeUuids(msg.getTicketTypeUuids());
                rmsg.setTicketFlowCollectionUuid(msg.getTicketFlowCollectionUuid());
                bus.makeTargetServiceIdByResourceUuid(rmsg, TicketManager.SERVICE_ID, rmsg.getTicketFlowCollectionUuid());
                bus.send(rmsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            evt.setError(reply.getError());
                        }

                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("remove-ticket-type-from-flow-collection-%s", self.getUuid());
            }
        });
    }

    private void handle(APIUpdateTicketFlowCollectionMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return syncThreadName;
            }

            @Override
            public void run(SyncTaskChain chain) {
                self = dbf.reload(self);

                TicketFlowCollectionVO vo = updateTicketFlowCollection(msg);

                if (vo != null) {
                    self = dbf.updateAndRefresh(vo);
                }

                APIUpdateTicketFlowCollectionEvent evt = new APIUpdateTicketFlowCollectionEvent(msg.getId());
                evt.setInventory(getSelfInventory());
                bus.publish(evt);
                chain.next();
            }

            @Override
            public String getName() {
                return String.format("update-ticket-flow-collection-%s", self.getUuid());
            }
        });
    }

    protected TicketFlowCollectionVO updateTicketFlowCollection(APIUpdateTicketFlowCollectionMsg msg) {
        boolean updated = false;

        if (msg.getName() != null) {
            self.setName(msg.getName());
            updated = true;
        }

        if (msg.getDescription() != null) {
            self.setDescription(msg.getDescription());
            updated = true;
        }

        if (msg.getDefault() != null) {
            self.setDefault(msg.getDefault());
            updated = true;
        }

        return updated ? self : null;
    }

    private void handle(APIDeleteTicketFlowCollectionMsg msg) {
        APIDeleteTicketFlowCollectionEvent evt = new APIDeleteTicketFlowCollectionEvent(msg.getId());
        for (DeleteTicketFlowCollectionExtensionPoint ext : pluginRgty.getExtensionList(DeleteTicketFlowCollectionExtensionPoint.class)) {
            ext.beforeDeleteTicketFlowCollection(self.toInventory());
        }
        deleteHook();
        bus.publish(evt);
    }

    private void handle(APIChangeTicketFlowCollectionStateMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return syncThreadName;
            }

            @Override
            public void run(SyncTaskChain chain) {
                APIChangeTicketFlowCollectionStateEvent evt = new APIChangeTicketFlowCollectionStateEvent(msg.getId());
                if (msg.getStateEvent().equals(TicketFlowCollectionStateEvent.enable.toString())) {
                    self.setState(TicketFlowCollectionState.Enabled);
                } else {
                    self.setState(TicketFlowCollectionState.Disabled);
                }

                self = dbf.updateAndRefresh(self);
                evt.setInventory(getSelfInventory());
                bus.publish(evt);
                chain.next();
            }

            @Override
            public String getName() {
                return String.format("change-ticket-flow-collection-%s-state", self.getUuid());
            }
        });
    }

    protected TicketFlowCollectionInventory getSelfInventory() {
        return TicketFlowCollectionInventory.valueOf(self);
    }
}
