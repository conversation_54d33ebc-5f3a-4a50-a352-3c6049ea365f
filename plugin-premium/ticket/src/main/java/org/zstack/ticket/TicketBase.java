package org.zstack.ticket;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SimpleQuery;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.thread.SyncTask;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.identity.AccountVO;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.ticket.api.*;
import org.zstack.ticket.entity.*;
import org.zstack.ticket.history.OperationContextType;
import org.zstack.ticket.history.RequestCompletedContext;
import org.zstack.ticket.history.RequestUpdatedContext;
import org.zstack.ticket.message.*;
import org.zstack.ticket.sns.TicketStatusNotification;
import org.zstack.utils.DebugUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import static org.zstack.core.Platform.*;

import java.util.ArrayList;
import java.util.List;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public abstract class TicketBase implements Ticket {
    private static final CLogger logger = Utils.getLogger(TicketBase.class);

    protected TicketVO self;

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private TicketManager ticketMgr;
    @Autowired
    private ThreadFacade thdf;

    protected abstract TicketOperator getTicketOperatorForNextFlow();

    protected abstract TicketOperator getTicketOperatorFromTicketSubmitter();

    protected abstract String getTicketFlowCollectionFromTicketSubmitter();

    protected void checkIfOperationAllowed(Message msg) {
    }

    public TicketBase(TicketVO self) {
        this.self = self;
    }

    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        checkIfOperationAllowed(msg);

        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof TicketDeletionMsg) {
            handle((TicketDeletionMsg) msg);
        } else if (msg instanceof ChangeTicketStatusMsg) {
            handle((ChangeTicketStatusMsg) msg);
        } else if (msg instanceof ResetTicketMsg) {
            handle((ResetTicketMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(ResetTicketMsg msg) {
        thdf.syncSubmit(new SyncTask<Void>() {
            @Override
            public Void call() {
                ResetTicketReply reply = new ResetTicketReply();

                if (msg.getCollectionUuid() == null) {
                    self.setFlowCollectionUuid(getTicketFlowCollectionFromTicketSubmitter());
                } else {
                    self.setFlowCollectionUuid(msg.getCollectionUuid());
                }

                self.setCurrentFlowUuid(null);

                // reset ticket should ignore changing status of Cancelled or Rejected ticket
                if (TicketStatus.Rejected.equals(self.getStatus()) || TicketStatus.Cancelled.equals(self.getStatus())) {
                    self = dbf.updateAndRefresh(self);
                    bus.reply(msg, reply);
                    return null;
                }

                self.setStatus(TicketStatus.Pending);
                self = dbf.updateAndRefresh(self);
                bus.reply(msg, reply);
                return null;
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }

            @Override
            public String getSyncSignature() {
                return String.format("ticket-change-status-%s", self.getUuid());
            }

            @Override
            public int getSyncLevel() {
                return 1;
            }
        });
    }

    private void handle(ChangeTicketStatusMsg msg) {
        final ChangeTicketStatusReply reply = new ChangeTicketStatusReply();

        thdf.syncSubmit(new SyncTask<Void>() {
            @Override
            public Void call() {
                changeStatus()
                        .skipSessionCheck(msg.isSkipSessionCheck())
                        .session(msg.getSession())
                        .comment(msg.getComment())
                        .change(msg.getStatusEvent(), new Completion(msg) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                    }
                });
                return null;
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }

            @Override
            public String getSyncSignature() {
                return String.format("ticket-change-status-%s", self.getUuid());
            }

            @Override
            public int getSyncLevel() {
                return 1;
            }
        });
    }

    private void handle(TicketDeletionMsg msg) {
        delete();
        bus.reply(msg, new TicketDeletionReply());
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIChangeTicketStatusMsg) {
            handle((APIChangeTicketStatusMsg) msg);
        } else if (msg instanceof APIUpdateTicketRequestMsg) {
            handle((APIUpdateTicketRequestMsg) msg);
        } else if (msg instanceof APIDeleteTicketMsg) {
            handle((APIDeleteTicketMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    protected void delete() {
        new SQLBatch() {
            @Override
            protected void scripts() {
                List<TicketStatusHistoryVO> histories = q(TicketStatusHistoryVO.class)
                                                        .eq(TicketStatusHistoryVO_.ticketUuid, self.getUuid())
                                                        .orderBy(TicketStatusHistoryVO_.sequence, SimpleQuery.Od.ASC)
                                                        .list();
                histories.forEach(h->  {
                    ArchiveTicketStatusHistoryVO ah = new ArchiveTicketStatusHistoryVO(h);
                    persist(ah);
                });

                ArchiveTicketVO at = new ArchiveTicketVO(self);
                persist(at);

                sql(TicketStatusHistoryVO.class).eq(TicketStatusHistoryVO_.ticketUuid, self.getUuid()).hardDelete();
                remove(self);
            }
        }.execute();
    }

    protected void handle(APIDeleteTicketMsg msg) {
        delete();
        bus.publish(new APIDeleteTicketEvent(msg.getId()));
    }

    private void handle(APIUpdateTicketRequestMsg msg) {
        APIUpdateTicketRequestEvent evt = new APIUpdateTicketRequestEvent(msg.getId());

        TicketOperator operator = getTicketOperatorFromTicketSubmitter();
        if (!operator.operatorUuid.equals(msg.getSession().getUserUuid())) {
            operator = getTicketOperatorForNextFlow();
            flowCheck(msg.getSession(), operator);
        } else {
            if (self.getStatus() != TicketStatus.Cancelled && self.getStatus() != TicketStatus.Rejected) {
                throw new OperationFailureException(argerr("ticket[uuid:%s, name:%s] can only be updated after being cancelled, current status is %s", self.getUuid(), self.getName(), self.getStatus()));
            }
        }

        TicketOperator finalOperator = operator;
        new SQLBatch() {
            @Override
            protected void scripts() {
                List origin = JSONObjectUtil.toCollection(self.getRequests(), ArrayList.class, TicketRequest.class);
                self.setRequests(JSONObjectUtil.toJsonString(msg.getRequests()));
                if (msg.getDescription() != null) {
                    self.setDescription(msg.getDescription());
                }
                self = merge(self);

                TicketStatusHistoryVO vo = new TicketStatusHistoryVO();
                vo.setAccountUuid(self.getAccountUuid());
                vo.setUuid(Platform.getUuid());
                vo.setFromStatus(self.getStatus());
                vo.setToStatus(self.getStatus());
                vo.setTicketUuid(self.getUuid());
                vo.setOperatorType(finalOperator.operatorType);
                vo.setOperatorUuid(finalOperator.operatorUuid);
                vo.setOperationContextType(OperationContextType.RequestUpdated.toString());
                vo.setOperationContext(new RequestUpdatedContext(origin, msg.getRequests()).toJSON());
                persist(vo);

                evt.setInventory(self.toInventory());
            }

        }.execute();

        bus.publish(evt);
    }

    protected String getCurrentFlowName() {
        if (self.getCurrentFlowUuid() == null) {
            return null;
        }
        return Q.New(TicketFlowVO.class)
                .select(TicketFlowVO_.name)
                .eq(TicketFlowVO_.uuid, self.getCurrentFlowUuid())
                .findValue();
    }

    protected TicketFlowVO getNextFlow() {
        if (self.getCurrentFlowUuid() == null) {
            return Q.New(TicketFlowVO.class)
                    .eq(TicketFlowVO_.collectionUuid, self.getFlowCollectionUuid())
                    .isNull(TicketFlowVO_.parentFlowUuid)
                    .find();
        } else {
            return Q.New(TicketFlowVO.class)
                    .eq(TicketFlowVO_.parentFlowUuid, self.getCurrentFlowUuid())
                    .find();
        }
    }

    protected void flowCheck(SessionInventory session, TicketOperator operator) {
        String operatorUuid;
        if (AccountVO.class.getSimpleName().equals(operator.operatorType)) {
            operatorUuid = session.getAccountUuid();
        } else {
            operatorUuid = session.getUserUuid();
        }

        if (!operator.operatorUuid.equals(operatorUuid)) {
            throw new OperationFailureException(argerr("operation denied. the operator needs to be done by account/virtual ID[uuid:%s]", operator.operatorUuid));
        }
    }

    protected void ticketFinalApproved(TicketOperator operator) {
        TicketInventory inv = self.toInventory();

        List<ErrorCode> errors = new ArrayList<>();
        RequestCompletedContext context = new RequestCompletedContext();
        new While<>(inv.getRequest()).each((req, completion) -> TicketExecuteHelper.newTicketExecutor(req.apiName).executeTicket(inv, req, new ReturnValueCompletion<RequestCompletedContext.RequestResult>(completion) {
            @Override
            public void success(RequestCompletedContext.RequestResult ret) {
                context.requestResults.add(ret);
                completion.done();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                errors.add(errorCode);
                completion.done();
            }
        })).run(new WhileDoneCompletion(null) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errors.isEmpty()) {
                    logger.warn(String.format("error when execute requests of ticket[uuid:%s, name:%s], %s", self.getUuid(), self.getName(), errors));
                }

                String currentFlowName = getCurrentFlowName();
                TicketStatusHistoryVO vo = new TicketStatusHistoryVO();
                vo.setAccountUuid(self.getAccountUuid());
                vo.setUuid(Platform.getUuid());
                vo.setFromStatus(self.getStatus());
                vo.setToStatus(self.getStatus());
                vo.setTicketUuid(self.getUuid());
                vo.setOperatorType(AccountVO.class.getSimpleName());
                vo.setOperatorUuid(operator == null ? AccountConstant.INITIAL_SYSTEM_ADMIN_UUID : operator.operatorUuid);
                vo.setOperationContextType(OperationContextType.RequestCompleted.toString());
                vo.setOperationContext(context.toJSON());
                vo.setFlowName(currentFlowName);
                dbf.persist(vo);

                TicketStatusNotification.Message message = new TicketStatusNotification.Message();
                message.accountSystemType = self.getAccountSystemType();
                message.ticket = self.toInventory();
                message.operatorType = AccountVO.class.getSimpleName();
                message.operatorUuid = AccountConstant.INITIAL_SYSTEM_ADMIN_UUID;
                message.operationType = OperationContextType.RequestCompleted.toString();
                message.flowName = currentFlowName;

                new TicketStatusNotification().publish(message);
            }
        });
    }

    class ChangeStatus {
        private String comment;
        private SessionInventory session;
        private boolean skipSessionCheck;
        private boolean skipRecording;

        public ChangeStatus comment(String v) {
            comment = v;
            return this;
        }

        public ChangeStatus session(SessionInventory s) {
            session = s;
            return this;
        }

        public ChangeStatus skipSessionCheck(boolean skip) {
            skipSessionCheck = skip;
            return this;
        }

        public ChangeStatus skipRecording(boolean skip) {
            skipRecording = skip;
            return this;
        }

        public void change(TicketStatusEvent statusEvent, Completion completion) {
            DebugUtils.Assert(session != null, "session cannot be null");

            TicketStatus originStatus = self.getStatus();

            TicketStatus nextStatus = self.getStatus().nextStatus(statusEvent);

            TicketFlowVO nextFlow = getNextFlow();
            if (nextFlow != null && nextStatus == TicketStatus.IntermediateApproved && !Q.New(TicketFlowVO.class).eq(TicketFlowVO_.parentFlowUuid, nextFlow.getUuid()).isExists()) {
                // the last flow approved
                nextStatus = TicketStatus.FinalApproved;
            }

            TicketOperator operator;
            String currentFlowUuid = null;
            // currentFlowName is null at pending and cancelled
            String currentFlowName = null;
            if (nextStatus == TicketStatus.Pending) {
                operator = getTicketOperatorFromTicketSubmitter();
            } else if (nextStatus == TicketStatus.Cancelled) {
                operator = getTicketOperatorFromTicketSubmitter();
            } else if (nextStatus == TicketStatus.IntermediateApproved) {
                DebugUtils.Assert(nextFlow != null, String.format("nextFlow cannot be null, %s", JSONObjectUtil.toJsonString(self.toInventory())));
                currentFlowUuid = nextFlow.getUuid();
                currentFlowName = nextFlow.getName();
                operator = getTicketOperatorForNextFlow();
            } else if (nextStatus == TicketStatus.FinalApproved) {
                DebugUtils.Assert(nextFlow != null, String.format("nextFlow cannot be null, %s", JSONObjectUtil.toJsonString(self.toInventory())));
                currentFlowUuid = nextFlow.getUuid();
                currentFlowName = nextFlow.getName();
                operator = getTicketOperatorForNextFlow();
            } else if (nextStatus == TicketStatus.Rejected) {
                DebugUtils.Assert(nextFlow != null, String.format("nextFlow cannot be null, %s", JSONObjectUtil.toJsonString(self.toInventory())));
                currentFlowName = nextFlow.getName();
                operator = getTicketOperatorForNextFlow();
            } else {
                throw new CloudRuntimeException(String.format("should not be here: %s", nextStatus));
            }

            if (!skipSessionCheck) {
                flowCheck(session, operator);
            }

            self.setStatus(nextStatus);
            if (currentFlowUuid != null) {
                self.setCurrentFlowUuid(currentFlowUuid);
            }
            self = dbf.updateAndRefresh(self);

            if (!skipRecording) {
                TicketStatusNotification.Message message = new TicketStatusNotification.Message();
                message.accountSystemType = self.getAccountSystemType();
                message.status = nextStatus;
                message.ticket = self.toInventory();
                message.operatorType = operator.operatorType;
                message.operatorUuid = operator.operatorUuid;
                message.operationType = OperationContextType.StatusChanged.toString();
                message.flowName = currentFlowName;

                new TicketStatusNotification().publish(message);

                TicketStatusHistoryVO vo = new TicketStatusHistoryVO();
                vo.setAccountUuid(self.getAccountUuid());
                vo.setUuid(Platform.getUuid());
                vo.setComment(comment);
                vo.setFromStatus(originStatus);
                vo.setToStatus(self.getStatus());
                vo.setTicketUuid(self.getUuid());
                vo.setOperatorType(operator.operatorType);
                vo.setOperatorUuid(operator.operatorUuid);
                vo.setOperationContextType(OperationContextType.StatusChanged.toString());
                vo.setFlowName(currentFlowName);
                dbf.persistAndRefresh(vo);
            }

            if (nextStatus == TicketStatus.FinalApproved) {
                ticketFinalApproved(operator);
            }

            if (nextStatus == TicketStatus.Rejected || nextStatus == TicketStatus.Cancelled) {
                self.setCurrentFlowUuid(null);

                dbf.update(self);
            }

            completion.success();
        }
    }

    public ChangeStatus changeStatus() {
        return new ChangeStatus();
    }

    private void handle(APIChangeTicketStatusMsg msg) {
        APIChangeTicketStatusEvent evt = new APIChangeTicketStatusEvent(msg.getId());

        thdf.syncSubmit(new SyncTask<Void>() {
            @Override
            public Void call() {
                changeStatus()
                        .session(msg.getSession())
                        .comment(msg.getComment())
                        .change(msg.getStatusEvent(), new Completion(msg) {
                    @Override
                    public void success() {
                        self = dbf.reload(self);
                        evt.setInventory(self.toInventory());
                        bus.publish(evt);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                    }
                });
                return null;
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }

            @Override
            public String getSyncSignature() {
                return String.format("ticket-change-status-%s", self.getUuid());
            }

            @Override
            public int getSyncLevel() {
                return 1;
            }
        });
    }
}
