package org.zstack.ticket.entity;

import org.zstack.header.vo.ResourceVO_;
import org.zstack.ticket.iam2.entity.IAM2TicketFlowVO;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@StaticMetamodel(TicketTypeVO.class)
public class TicketTypeVO_ extends ResourceVO_ {
    public static SingularAttribute<IAM2TicketFlowVO, String> name;
    public static SingularAttribute<IAM2TicketFlowVO, String> description;
    public static SingularAttribute<IAM2TicketFlowVO, String> type;
    public static SingularAttribute<IAM2TicketFlowVO, String> requests;
}
