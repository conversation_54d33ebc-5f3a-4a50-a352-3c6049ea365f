package org.zstack.ticket.type;

import org.zstack.header.vm.APIDestroyVmInstanceMsg;

import java.util.Arrays;
import java.util.List;

public class DestoryVmInstanceTicket implements TicketDescription {
    public static final String DESTROY_VM_INSTANCE_TICKET_UUID = "815311b80fe14ab69434c2a2b67c18d2";

    public static final String DESTROY_VM_INSTANCE_TICKET_TYPE = "DESTROY_VM_INSTANCE_TICKET_TYPE";

    @Override
    public void ticketTypeInfo() {
        ticketBuilder()
                .uuid(DESTROY_VM_INSTANCE_TICKET_UUID)
                .name(DESTROY_VM_INSTANCE_TICKET_TYPE)
                .type(DESTROY_VM_INSTANCE_TICKET_TYPE)
                .adminOnly(isAdminOnly())
                .requests(getTicketRequestClassNames())
                .build();
    }

    @Override
    public List<String> getTicketRequestClassNames() {
        return Arrays.asList(APIDestroyVmInstanceMsg.class.getCanonicalName());
    }

    @Override
    public boolean isAdminOnly() {
        return false;
    }
}
