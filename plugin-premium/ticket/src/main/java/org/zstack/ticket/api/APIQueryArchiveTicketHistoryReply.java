package org.zstack.ticket.api;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;
import org.zstack.ticket.entity.ArchiveTicketStatusHistoryInventory;

import java.util.List;

@RestResponse(allTo = "inventories")
public class APIQueryArchiveTicketHistoryReply extends APIQueryReply {
    private List<ArchiveTicketStatusHistoryInventory> inventories;

    public List<ArchiveTicketStatusHistoryInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<ArchiveTicketStatusHistoryInventory> inventories) {
        this.inventories = inventories;
    }
}
