package org.zstack.ticket.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse
public class APIRemoveTicketTypesFromTicketFlowCollectionEvent extends APIEvent {
    public static APIRemoveTicketTypesFromTicketFlowCollectionEvent __example__() {
        APIRemoveTicketTypesFromTicketFlowCollectionEvent evt = new APIRemoveTicketTypesFromTicketFlowCollectionEvent();
        return evt;
    }

    public APIRemoveTicketTypesFromTicketFlowCollectionEvent() {
    }

    public APIRemoveTicketTypesFromTicketFlowCollectionEvent(String apiId) {
        super(apiId);
    }
}
