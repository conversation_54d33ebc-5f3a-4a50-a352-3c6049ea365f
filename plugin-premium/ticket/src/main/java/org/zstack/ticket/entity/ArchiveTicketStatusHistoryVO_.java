package org.zstack.ticket.entity;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

@StaticMetamodel(ArchiveTicketStatusHistoryVO.class)
public class ArchiveTicketStatusHistoryVO_ {
    public static SingularAttribute<TicketStatusHistoryVO, String> uuid;
    public static SingularAttribute<TicketStatusHistoryVO, Integer> sequence;
    public static SingularAttribute<TicketStatusHistoryVO, String> historyUuid;
    public static SingularAttribute<TicketStatusHistoryVO, String> ticketUuid;
    public static SingularAttribute<TicketStatusHistoryVO, TicketStatus> fromStatus;
    public static SingularAttribute<TicketStatusHistoryVO, TicketStatus> toStatus;
    public static SingularAttribute<TicketStatusHistoryVO, String> comment;
    public static SingularAttribute<TicketStatusHistoryVO, String> operationContextType;
    public static SingularAttribute<TicketStatusHistoryVO, String> operationContext;
    public static SingularAttribute<TicketStatusHistoryVO, String> operatorType;
    public static SingularAttribute<TicketStatusHistoryVO, String> operatorUuid;
    public static SingularAttribute<TicketStatusHistoryVO, String> flowName;
    public static SingularAttribute<TicketStatusHistoryVO, Timestamp> createDate;
    public static SingularAttribute<TicketStatusHistoryVO, Timestamp> lastOpDate;
}
