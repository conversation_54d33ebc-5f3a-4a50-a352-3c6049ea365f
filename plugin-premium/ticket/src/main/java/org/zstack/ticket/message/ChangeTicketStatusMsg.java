package org.zstack.ticket.message;

import org.zstack.header.identity.SessionInventory;
import org.zstack.header.message.NeedReplyMessage;
import org.zstack.ticket.api.TicketMessage;
import org.zstack.ticket.entity.TicketStatusEvent;

/**
 * Created by kayo on 2018/7/19.
 */
public class ChangeTicketStatusMsg extends NeedReplyMessage implements TicketMessage {
    private String uuid;
    private TicketStatusEvent statusEvent;
    private String comment;
    private SessionInventory session;
    private boolean skipSessionCheck = false;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public TicketStatusEvent getStatusEvent() {
        return statusEvent;
    }

    public void setStatusEvent(TicketStatusEvent statusEvent) {
        this.statusEvent = statusEvent;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public SessionInventory getSession() {
        return session;
    }

    public void setSession(SessionInventory session) {
        this.session = session;
    }

    @Override
    public String getTicketUuid() {
        return uuid;
    }

    public boolean isSkipSessionCheck() {
        return skipSessionCheck;
    }

    public void setSkipSessionCheck(boolean skipSessionCheck) {
        this.skipSessionCheck = skipSessionCheck;
    }
}
