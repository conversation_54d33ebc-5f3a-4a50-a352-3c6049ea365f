package org.zstack.ticket.executor;

import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.ticket.utils.RestResponseUtils;
import org.zstack.header.rest.RestResponseWrapper;
import org.zstack.ticket.TicketExecutor;
import org.zstack.ticket.entity.TicketInventory;
import org.zstack.ticket.entity.TicketRequest;
import org.zstack.ticket.history.RequestCompletedContext;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.zstack.core.Platform.operr;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class DefaultSingletonRequestExecutor implements TicketExecutor {
    private static final CLogger logger = Utils.getLogger(DefaultSingletonRequestExecutor.class);

    @Autowired
    private CloudBus bus;

    @Override
    public String getTicketRequestType() {
        return null;
    }

    @Override
    public void executeTicket(TicketInventory ticket, TicketRequest request, ReturnValueCompletion<RequestCompletedContext.RequestResult> completion) {
        Optional opt = APIMessage.apiMessageClasses.stream().filter(apiClass -> apiClass.getCanonicalName().equals(request.apiName)).findFirst();

        if (!opt.isPresent()) {
            completion.fail(operr("No api class[name:%s] is found", request.apiName));
            return;
        }

        RequestCompletedContext.RequestResult result = new RequestCompletedContext.RequestResult();
        result.requestName = request.requestName != null ? request.requestName : request.apiName;
        result.errors = new ArrayList<>();

        APIMessage msg = createAPIMessage(ticket, request, ((Class) opt.get()));
        AtomicBoolean replied = new AtomicBoolean(false);
        bus.send(msg, (e) -> {
            // avoid handle replies of same msg
            if (replied.getAndSet(true)) {
                return;
            }

            if (!e.isSuccess()) {
                result.errors.add(e.getError());
                result.failureTimes++;
                completion.success(result);
                return;
            }

            Object obj = null;
            try {
                obj = getResponseValueFromEvent(e);
            } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException exception) {
                completion.fail(operr("failed to get value from event: %s", exception.getMessage()));
                return;
            }

            result.inventories = Collections.singletonList(obj);
            result.successTimes++;
            completion.success(result);
        });
    }

    private Object getResponseValueFromEvent(APIEvent event) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        Class clazz = event.getClass();

        RestResponseWrapper w = RestResponseUtils.responseAnnotationByClass.get(clazz);

        if (!w.annotation.allTo().equals("")) {
            return PropertyUtils.getProperty(event, w.annotation.allTo());
        } else {
            Map<String, Object> ret = new HashMap<>();
            for (Map.Entry<String, String> e : w.responseMappingFields.entrySet()) {
                ret.put(e.getKey(),
                        PropertyUtils.getProperty(event, e.getValue()));
            }

            return ret;
        }
    }
}
