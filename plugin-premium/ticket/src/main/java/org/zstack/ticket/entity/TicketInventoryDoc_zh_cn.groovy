package org.zstack.ticket.entity

import org.zstack.ticket.entity.TicketStatus
import org.zstack.ticket.entity.TicketRequest
import java.lang.Object
import java.sql.Timestamp
import java.sql.Timestamp

doc {

	title "操作返回结果"

	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "3.0.0"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "3.0.0"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "3.0.0"
	}
	ref {
		name "status"
		path "org.zstack.ticket.entity.TicketInventory.status"
		desc "null"
		type "TicketStatus"
		since "3.0.0"
		clz TicketStatus.class
	}
	ref {
		name "request"
		path "org.zstack.ticket.entity.TicketInventory.request"
		desc "null"
		type "List"
		since "3.0.0"
		clz TicketRequest.class
	}
	field {
		name "accountSystemType"
		desc ""
		type "String"
		since "3.0.0"
	}
	field {
		name "ticketTypeUuid"
		desc ""
		type "String"
		since "3.6.0"
	}
	field {
		name "accountSystemContext"
		desc ""
		type "Object"
		since "3.0.0"
	}
	field {
		name "currentFlowUuid"
		desc ""
		type "String"
		since "3.0.0"
	}
	field {
		name "flowCollectionUuid"
		desc ""
		type "String"
		since "3.0.0"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "3.0.0"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "3.0.0"
	}
}
