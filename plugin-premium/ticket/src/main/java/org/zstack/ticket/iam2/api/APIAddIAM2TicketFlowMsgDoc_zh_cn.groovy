package org.zstack.ticket.iam2.api

import org.zstack.ticket.iam2.api.APIAddIAM2TicketFlowEvent

doc {
    title "AddIAM2TicketFlow"

    category "ticket"

    desc """在这里填写API描述"""

    rest {
        request {
			url "POST /v1/tickets/flow"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIAddIAM2TicketFlowMsg.class

            desc """"""
            
			params {

				column {
					name "approverUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional false
					since "3.0.0"
				}
				column {
					name "approverTitle"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "3.0.0"
				}
				column {
					name "name"
					enclosedIn "params"
					desc "资源名称"
					location "body"
					type "String"
					optional false
					since "3.0.0"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "3.0.0"
				}
				column {
					name "collectionUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional false
					since "3.0.0"
				}
				column {
					name "parentFlowUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "3.0.0"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "3.0.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "3.4.0"
				}
			}
        }

        response {
            clz APIAddIAM2TicketFlowEvent.class
        }
    }
}