package org.zstack.ticket.entity;

import org.zstack.header.search.Inventory;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Inventory(mappingVOClass = TicketTypeVO.class)
public class TicketTypeInventory {
    private String uuid;
    private String name;
    private String description;
    private String type;
    private String requests;
    private boolean adminOnly;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public static TicketTypeInventory valueOf(TicketTypeVO vo) {
        TicketTypeInventory inv = new TicketTypeInventory();
        inv.uuid = vo.getUuid();
        inv.name = vo.getName();
        inv.description = vo.getDescription();
        inv.type = vo.getType();
        inv.requests = vo.getRequests();
        inv.adminOnly = vo.isAdminOnly();
        inv.createDate = vo.getCreateDate();
        inv.lastOpDate = vo.getLastOpDate();
        return inv;
    }

    public static List<TicketTypeInventory> valueOf(Collection<TicketTypeVO> vos) {
        return vos.stream().map(TicketTypeInventory::valueOf).collect(Collectors.toList());
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRequests() {
        return requests;
    }

    public void setRequests(String requests) {
        this.requests = requests;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
