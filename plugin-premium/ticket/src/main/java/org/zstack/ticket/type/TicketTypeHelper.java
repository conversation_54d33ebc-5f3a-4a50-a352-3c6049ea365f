package org.zstack.ticket.type;

import org.zstack.core.Platform;
import org.zstack.core.db.Q;
import org.zstack.header.core.StaticInit;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO_;
import org.zstack.ticket.entity.*;
import org.zstack.ticket.iam2.entity.IAM2TicketFlowCollectionVO;
import org.zstack.ticket.iam2.entity.IAM2TicketFlowCollectionVO_;

import java.util.*;
import java.util.stream.Collectors;

public class TicketTypeHelper {
    private static Map<String, TicketInfo> ticketTypeMap = new HashMap<>();

    private static Map<String, String> apiRequestsTicketTypeMap = new HashMap<>();

    @StaticInit
    static void staticInit() {
        Platform.getReflections().getSubTypesOf(TicketDescription.class).forEach(clz -> {
            try {
                TicketDescription ticketDescription = clz.newInstance();
                ticketDescription.ticketTypeInfo();
            } catch (Exception e) {
                throw new CloudRuntimeException(e);
            }
        });

        List<TicketInfo> ticketInfos = TicketDescription.ticketBuilders.stream().map(builder -> builder.ticketInfo).collect(Collectors.toList());
        ticketInfos.forEach(info -> ticketTypeMap.put(info.getUuid(), info));
        ticketTypeMap.forEach((k, v) -> v.getRequests().forEach(r -> apiRequestsTicketTypeMap.put(r, k)));
    }

    public static TicketInfo getTicketTypeByRequestName(String apiName) {
        return ticketTypeMap.get(apiRequestsTicketTypeMap.get(apiName));
    }

    public static Map<String, TicketInfo> getPredefinedTicketTypeMap() {
        return ticketTypeMap;
    }

    public static Map<String, Integer> getTicketTypesFromApiRequests(List<TicketRequest> requests) {
        Map<String, Integer> typeWeightMap = new HashMap<>();

        requests.forEach(request -> {
            String ticketType = apiRequestsTicketTypeMap.get(request.apiName);
            if (typeWeightMap.get(ticketType) == null) {
                typeWeightMap.put(apiRequestsTicketTypeMap.get(request.apiName), 0);
            } else {
                typeWeightMap.put(ticketType, typeWeightMap.get(apiRequestsTicketTypeMap.get(request.apiName)) + 1);
            }
        });

        return typeWeightMap;
    }

    public static String getSuitableFlowCollectionByProject(String projectUuid, String ticketTypeUuid) {
        String cuuid = getValidTicketFlowCollectionInProjectByType(projectUuid, ticketTypeUuid);
        if (cuuid != null) {
            return cuuid;
        }

        cuuid = Q.New(TicketFlowCollectionVO.class)
                .select(TicketFlowCollectionVO_.uuid)
                .eq(TicketFlowCollectionVO_.isDefault, true).findValue();

        return cuuid;
    }

    public static String getSuitableFlowCollectionByAccount(String accountUuid, String ticketTypeUuid) {
        String projectUuid = Q.New(IAM2ProjectAccountRefVO.class)
                .select(IAM2ProjectAccountRefVO_.projectUuid)
                .eq(IAM2ProjectAccountRefVO_.accountUuid, accountUuid)
                .findValue();

        String cuuid = getValidTicketFlowCollectionInProjectByType(projectUuid, ticketTypeUuid);
        if (cuuid != null) {
            return cuuid;
        }

        cuuid = Q.New(TicketFlowCollectionVO.class)
                .select(TicketFlowCollectionVO_.uuid)
                .eq(TicketFlowCollectionVO_.isDefault, true).findValue();

        return cuuid;
    }

    private static String getValidTicketFlowCollectionInProjectByType(String projectUuid, String ticketTypeUuid) {
        if (projectUuid == null || ticketTypeUuid == null) {
            return null;
        }

        List<String> ticketFlowCollectionUuids = Q.New(IAM2TicketFlowCollectionVO.class)
                .select(IAM2TicketFlowCollectionVO_.uuid)
                .eq(IAM2TicketFlowCollectionVO_.projectUuid, projectUuid)
                .eq(IAM2TicketFlowCollectionVO_.status, TicketFlowCollectionStatus.Valid)
                .listValues();

        if (ticketFlowCollectionUuids.isEmpty()) {
            return null;
        }

        return Q.New(TicketTypeTicketFlowCollectionRefVO.class)
                .select(TicketTypeTicketFlowCollectionRefVO_.ticketFlowCollectionUuid)
                .eq(TicketTypeTicketFlowCollectionRefVO_.ticketTypeUuid, ticketTypeUuid)
                .in(TicketTypeTicketFlowCollectionRefVO_.ticketFlowCollectionUuid, ticketFlowCollectionUuids)
                .findValue();
    }
}
