package org.zstack.ticket.api

import org.zstack.ticket.api.APIDeleteTicketEvent

doc {
    title "DeleteTicket"

    category "ticket"

    desc """删除工单"""

    rest {
        request {
			url "DELETE /v1/tickets/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIDeleteTicketMsg.class

            desc """"""
            
			params {

				column {
					name "uuid"
					enclosedIn ""
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "3.0.0"
					
				}
				column {
					name "deleteMode"
					enclosedIn ""
					desc ""
					location "body"
					type "String"
					optional true
					since "3.0.0"
					
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
					
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
					
				}
			}
        }

        response {
            clz APIDeleteTicketEvent.class
        }
    }
}