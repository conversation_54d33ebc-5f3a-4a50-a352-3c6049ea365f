package org.zstack.ticket.utils;

import org.zstack.header.core.StaticInit;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.rest.RestResponse;
import org.zstack.header.rest.RestResponseWrapper;
import org.zstack.utils.BeanUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class RestResponseUtils {
    public static Map<Class, RestResponseWrapper> responseAnnotationByClass = new HashMap<>();

    @StaticInit
    static void staticInit() {
        Set<Class> apiEventClasses = BeanUtils.reflections.getSubTypesOf(APIEvent.class).stream().filter(c
                -> !Modifier.isStatic(c.getModifiers()) && c.isAnnotationPresent(RestResponse.class))
                .collect(Collectors.toSet());

        for (Class clazz : apiEventClasses) {
            responseAnnotationByClass.put(clazz, new RestResponseWrapper((RestResponse) clazz.getAnnotation(RestResponse.class), clazz));
        }

        responseAnnotationByClass.put(APIEvent.class, new RestResponseWrapper(new RestResponse(){
            @Override
            public Class<? extends Annotation> annotationType() {
                return null;
            }

            @Override
            public String allTo() {
                return "";
            }

            @Override
            public String[] fieldsTo() {
                return new String[0];
            }

        }, APIEvent.class));
    }
}
