package org.zstack.ticket.iam2.api

import org.zstack.ticket.api.APIUpdateTicketFlowCollectionEvent

doc {
    title "UpdateIAM2TicketFlowCollection"

    category "ticket"

    desc """在这里填写API描述"""

    rest {
        request {
			url "PUT /v1/tickets/flow-collections/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIUpdateIAM2TicketFlowCollectionMsg.class

            desc """"""
            
			params {

				column {
					name "flows"
					enclosedIn "updateIAM2TicketFlowCollection"
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
				column {
					name "uuid"
					enclosedIn "updateIAM2TicketFlowCollection"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "3.0.0"
				}
				column {
					name "name"
					enclosedIn "updateIAM2TicketFlowCollection"
					desc "资源名称"
					location "body"
					type "String"
					optional true
					since "3.0.0"
				}
				column {
					name "description"
					enclosedIn "updateIAM2TicketFlowCollection"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "3.0.0"
				}
				column {
					name "isDefault"
					enclosedIn "updateIAM2TicketFlowCollection"
					desc ""
					location "body"
					type "Boolean"
					optional true
					since "3.0.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "3.0.0"
				}
			}
        }

        response {
            clz APIUpdateTicketFlowCollectionEvent.class
        }
    }
}