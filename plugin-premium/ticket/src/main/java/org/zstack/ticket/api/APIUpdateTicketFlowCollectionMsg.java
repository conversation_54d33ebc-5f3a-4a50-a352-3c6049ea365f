package org.zstack.ticket.api;

import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.ticket.entity.TicketFlowCollectionVO;

/**
 * Created by kayo on 2018/8/10.
 */
public abstract class APIUpdateTicketFlowCollectionMsg extends APIMessage implements TicketFlowCollectionMessage {
    @APIParam(resourceType = TicketFlowCollectionVO.class)
    private String uuid;

    @APIParam(maxLength = 255, required = false)
    private String name;
    @APIParam(maxLength = 2048, required = false)
    private String description;
    @APIParam(required = false)
    private Boolean isDefault;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }
}
