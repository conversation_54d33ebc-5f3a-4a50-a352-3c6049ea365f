package org.zstack.ticket.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.NoDoc;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDVO;
import org.zstack.ticket.api.TicketFlowCollectionMessage;
import org.zstack.ticket.api.TicketFlowMessage;
import org.zstack.ticket.entity.TicketFlowCollectionVO;
import org.zstack.ticket.entity.TicketFlowVO;

/**
 * Created by kayo on 2018/8/11.
 */
@RestRequest(path = "/tickets/flow/{uuid}/actions",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APIUpdateIAM2TicketFlowEvent.class
)
public class APIUpdateIAM2TicketFlowMsg extends APIMessage implements TicketFlowMessage {
    @APIParam(resourceType = TicketFlowVO.class)
    private String uuid;

    @APIParam(required = false)
    private String name;

    @APIParam(required = false)
    private String description;

    @APIParam(required = false, resourceType = IAM2VirtualIDVO.class)
    private String approverUuid;

    @APIParam(required = false)
    private String approverTitle;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getApproverUuid() {
        return approverUuid;
    }

    public void setApproverUuid(String approverUuid) {
        this.approverUuid = approverUuid;
    }

    public String getApproverTitle() {
        return approverTitle;
    }

    public void setApproverTitle(String approverTitle) {
        this.approverTitle = approverTitle;
    }

    @Override
    public String getTickFlowUuid() {
        return uuid;
    }

    public static APIUpdateIAM2TicketFlowMsg __example__() {
        APIUpdateIAM2TicketFlowMsg msg = new APIUpdateIAM2TicketFlowMsg();
        msg.setUuid(uuid());
        msg.setName("new ticket flow name");
        msg.setApproverUuid(uuid());
        msg.setDescription("new ticket flow description");

        return msg;
    }
}
