package org.zstack.ticket.api;

import org.springframework.http.HttpMethod;
import org.zstack.core.Platform;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.ticket.type.ChangeVmInstanceTicket;
import org.zstack.ticket.type.CreateVmInstanceTicket;
import org.zstack.ticket.type.TicketTypeHelper;
import org.zstack.ticket.entity.TicketFlowCollectionVO;
import org.zstack.ticket.entity.TicketTypeVO;

import java.util.Arrays;
import java.util.List;

@RestRequest(
        path = "/tickets/flow-collections/{ticketFlowCollectionUuid}/ticket-types",
        method = HttpMethod.DELETE,
        responseClass = APIRemoveTicketTypesFromTicketFlowCollectionEvent.class
)
public class APIRemoveTicketTypesFromTicketFlowCollectionMsg extends APIMessage implements TicketFlowCollectionMessage {
    @APIParam(resourceType = TicketFlowCollectionVO.class)
    private String ticketFlowCollectionUuid;

    @APIParam(resourceType = TicketTypeVO.class, nonempty = true)
    private List<String> ticketTypeUuids;

    @Override
    public String getTicketFlowCollectionUuid() {
        return ticketFlowCollectionUuid;
    }

    public void setTicketFlowCollectionUuid(String ticketFlowCollectionUuid) {
        this.ticketFlowCollectionUuid = ticketFlowCollectionUuid;
    }

    public List<String> getTicketTypeUuids() {
        return ticketTypeUuids;
    }

    public void setTicketTypeUuids(List<String> ticketTypeUuids) {
        this.ticketTypeUuids = ticketTypeUuids;
    }

    public static APIRemoveTicketTypesFromTicketFlowCollectionMsg __example__() {
        APIRemoveTicketTypesFromTicketFlowCollectionMsg msg = new APIRemoveTicketTypesFromTicketFlowCollectionMsg();
        msg.setTicketFlowCollectionUuid(Platform.getUuid());
        msg.setTicketTypeUuids(Arrays.asList(CreateVmInstanceTicket.CREATE_VM_INSTANCE_TICKET_UUID, ChangeVmInstanceTicket.CHANGE_VM_INSTANCE_TICKET_UUID));
        return msg;
    }
}
