package org.zstack.ticket.entity;

import org.zstack.header.vo.*;
import org.zstack.header.vo.EntityGraph;
import org.zstack.header.vo.ForeignKey;

import javax.persistence.*;
import java.sql.Timestamp;

@Entity
@Table
@EntityGraph(
        parents = {
                @EntityGraph.Neighbour(type = TicketFlowCollectionVO.class, myField = "collectionUuid", targetField = "uuid")
        }
)
@BaseResource
public class TicketFlowVO extends ResourceVO implements ToInventory {
    @Column
    private String name;
    @Column
    private String description;
    @Column
    @ForeignKey(parentEntityClass = TicketFlowCollectionVO.class, onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String collectionUuid;
    @Column
    private String parentFlowUuid;
    @Column
    private String flowContext;
    @Column
    private String flowContextType;
    @Column
    private Timestamp createDate;
    @Column
    private Timestamp lastOpDate;

    public TicketFlowVO() {}

    public TicketFlowVO(TicketFlowVO vo) {
        this.uuid = vo.uuid;
        this.name = vo.name;
        this.description = vo.description;
        this.collectionUuid = vo.collectionUuid;
        this.parentFlowUuid = vo.parentFlowUuid;
        this.flowContext = vo.flowContext;
        this.flowContextType = vo.flowContextType;
        this.createDate = vo.createDate;
        this.lastOpDate = vo.lastOpDate;
    }

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public String getCollectionUuid() {
        return collectionUuid;
    }

    public void setCollectionUuid(String collectionUuid) {
        this.collectionUuid = collectionUuid;
    }

    public String getFlowContextType() {
        return flowContextType;
    }

    public void setFlowContextType(String flowContextType) {
        this.flowContextType = flowContextType;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParentFlowUuid() {
        return parentFlowUuid;
    }

    public void setParentFlowUuid(String parentFlowUuid) {
        this.parentFlowUuid = parentFlowUuid;
    }

    public String getFlowContext() {
        return flowContext;
    }

    public void setFlowContext(String flowContext) {
        this.flowContext = flowContext;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
