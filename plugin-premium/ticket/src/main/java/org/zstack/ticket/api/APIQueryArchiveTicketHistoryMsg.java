package org.zstack.ticket.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.NoDoc;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;
import org.zstack.ticket.entity.ArchiveTicketStatusHistoryInventory;

@AutoQuery(replyClass = APIQueryArchiveTicketHistoryReply.class, inventoryClass = ArchiveTicketStatusHistoryInventory.class)
@RestRequest(path = "/tickets/histories/archives", method = HttpMethod.GET, responseClass = APIQueryArchiveTicketHistoryReply.class)
@NoDoc
public class APIQueryArchiveTicketHistoryMsg extends APIQueryMessage {
}
