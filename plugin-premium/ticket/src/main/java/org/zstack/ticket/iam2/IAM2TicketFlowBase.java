package org.zstack.ticket.iam2;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.db.SQLBatchWithReturn;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.ticket.DeleteTicketFlowExtensionPoint;
import org.zstack.ticket.TicketFlowBase;
import org.zstack.ticket.UpdateIAM2TicketFlowExtensionPoint;
import org.zstack.ticket.api.TicketFlowCollectionMessage;
import org.zstack.ticket.entity.TicketFlowInventory;
import org.zstack.ticket.entity.TicketFlowVO;
import org.zstack.ticket.entity.TicketFlowVO_;
import org.zstack.ticket.iam2.api.*;
import org.zstack.ticket.iam2.entity.IAM2TicketFlowVO;
import org.zstack.ticket.iam2.entity.IAM2TicketFlowVO_;

public class IAM2TicketFlowBase extends TicketFlowBase {
    @Autowired
    private PluginRegistry pluginRegistry;

    public IAM2TicketFlowBase(TicketFlowVO self) {
        super(self);
    }

    public IAM2TicketFlowBase() {
    }

    @Override
    public void handleMessage(Message msg) {
        try {
            if (msg instanceof APIMessage) {
                handleApiMessage((APIMessage) msg);
            } else {
                bus.dealWithUnknownMessage(msg);
            }
        } catch (Exception e) {
            bus.logExceptionWithMessageDump(msg, e);
            bus.replyErrorByMessageType(msg, e);
        }
    }

    private void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIUpdateIAM2TicketFlowMsg) {
            handle((APIUpdateIAM2TicketFlowMsg) msg);
        } else if (msg instanceof APIDeleteIAM2TicketFlowMsg) {
            handle((APIDeleteIAM2TicketFlowMsg) msg);
        } else if (msg instanceof TicketFlowCollectionMessage) {
            super.handleMessage(msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(APIDeleteIAM2TicketFlowMsg msg) {
        String parentFlowUuid = new SQLBatchWithReturn<String>() {
            @Override
            protected String scripts() {
                String parentFlowUuid = q(TicketFlowVO.class).select(TicketFlowVO_.parentFlowUuid).eq(TicketFlowVO_.uuid, msg.getUuid()).findValue();

                sql(TicketFlowVO.class).eq(TicketFlowVO_.parentFlowUuid, msg.getUuid()).set(TicketFlowVO_.parentFlowUuid, parentFlowUuid).update();

                sql(TicketFlowVO.class).eq(TicketFlowVO_.uuid, msg.getUuid()).hardDelete();

                return parentFlowUuid;
            }
        }.execute();

        for(DeleteTicketFlowExtensionPoint ext : pluginRegistry.getExtensionList(DeleteTicketFlowExtensionPoint.class)) {
            ext.afterDeleteTicketFlow(msg.getUuid(), parentFlowUuid);
        }

        APIDeleteIAM2TicketFlowEvent evt = new APIDeleteIAM2TicketFlowEvent(msg.getId());
        bus.publish(evt);
    }

    private void handle(APIUpdateIAM2TicketFlowMsg msg) {
        IAM2TicketFlowVO vo = Q.New(IAM2TicketFlowVO.class).eq(IAM2TicketFlowVO_.uuid, msg.getUuid()).find();

        boolean updated = false;

        if (msg.getName() != null) {
            vo.setName(msg.getName());
            updated = true;
        }

        if (msg.getDescription() != null) {
            vo.setDescription(msg.getDescription());
            updated = true;
        }

        IAM2TicketFlowContext fctx = IAM2TicketFlowContext.fromJSON(vo.getFlowContext());
        if (msg.getApproverUuid() != null && !msg.getApproverUuid().equals(fctx.approverUuid)) {
            fctx.approverUuid = msg.getApproverUuid();
            vo.setApproverUuid(msg.getApproverUuid());
            updated = true;
        }

        if (msg.getApproverTitle() != null && !msg.getApproverTitle().equals(fctx.approverTitle)) {
            fctx.approverTitle = msg.getApproverTitle();
            updated = true;
        }

        if (updated) {
            vo.setFlowContext(fctx.toJSON());
            vo = dbf.updateAndRefresh(vo);
        }

        TicketFlowInventory inv = vo.toInventory();

        for (UpdateIAM2TicketFlowExtensionPoint ext : pluginRegistry.getExtensionList(UpdateIAM2TicketFlowExtensionPoint.class)) {
            ext.afterUpdateIAM2TicketFlow(inv);
        }

        APIUpdateIAM2TicketFlowEvent evt = new APIUpdateIAM2TicketFlowEvent(msg.getId());
        evt.setInventory(inv);
        bus.publish(evt);
    }
}
