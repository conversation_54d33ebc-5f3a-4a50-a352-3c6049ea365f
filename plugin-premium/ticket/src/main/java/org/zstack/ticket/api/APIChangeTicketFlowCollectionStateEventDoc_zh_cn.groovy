package org.zstack.ticket.api

import org.zstack.header.errorcode.ErrorCode
import org.zstack.ticket.entity.TicketFlowCollectionInventory

doc {

	title "操作返回结果"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.ticket.api.APIChangeTicketFlowCollectionStateEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "3.0.0"
		clz ErrorCode.class
	}
	ref {
		name "inventory"
		path "org.zstack.ticket.api.APIChangeTicketFlowCollectionStateEvent.inventory"
		desc "null"
		type "TicketFlowCollectionInventory"
		since "3.0.0"
		clz TicketFlowCollectionInventory.class
	}
}
