package org.zstack.ticket.iam2.entity;

import org.zstack.header.vo.ForeignKey;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.ticket.entity.TicketFlowCollectionVO;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.PrimaryKeyJoinColumn;
import javax.persistence.Table;

/**
 * Created by kayo on 2018/8/10.
 */

@Entity
@Table
@PrimaryKeyJoinColumn(name = "uuid", referencedColumnName = "uuid")
public class IAM2TicketFlowCollectionVO extends TicketFlowCollectionVO {
    @Column
    @ForeignKey(parentEntityClass = IAM2ProjectVO.class, onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String projectUuid;

    public String getProjectUuid() {
        return projectUuid;
    }

    public void setProjectUuid(String projectUuid) {
        this.projectUuid = projectUuid;
    }

    public IAM2TicketFlowCollectionVO(TicketFlowCollectionVO vo) {
        super(vo);
    }

    public IAM2TicketFlowCollectionVO() {}
}
