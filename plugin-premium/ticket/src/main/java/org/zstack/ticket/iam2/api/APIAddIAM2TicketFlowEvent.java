package org.zstack.ticket.iam2.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.ticket.entity.TicketFlowInventory;

/**
 * Created by kayo on 2018/8/11.
 */
@RestResponse(allTo = "inventory")
public class APIAddIAM2TicketFlowEvent extends APIEvent {
    private TicketFlowInventory inventory;

    public APIAddIAM2TicketFlowEvent() {
    }

    public APIAddIAM2TicketFlowEvent(String apiId) {
        super(apiId);
    }

    public TicketFlowInventory getInventory() {
        return inventory;
    }

    public void setInventory(TicketFlowInventory inventory) {
        this.inventory = inventory;
    }

    public static APIAddIAM2TicketFlowEvent __example__() {
        APIAddIAM2TicketFlowEvent evt = new APIAddIAM2TicketFlowEvent();

        evt.setInventory(TicketFlowInventory.__example__().get(0));

        return evt;
    }
}
