package org.zstack.header.protocol



doc {

	title "在这里输入结构的名称"

	field {
		name "id"
		desc ""
		type "String"
		since "0.6"
	}
	field {
		name "priority"
		desc ""
		type "String"
		since "0.6"
	}
	field {
		name "state"
		desc ""
		type "String"
		since "0.6"
	}
	field {
		name "deadTime"
		desc ""
		type "String"
		since "0.6"
	}
	field {
		name "neighborAddress"
		desc ""
		type "String"
		since "0.6"
	}
	field {
		name "device"
		desc ""
		type "String"
		since "0.6"
	}
}
