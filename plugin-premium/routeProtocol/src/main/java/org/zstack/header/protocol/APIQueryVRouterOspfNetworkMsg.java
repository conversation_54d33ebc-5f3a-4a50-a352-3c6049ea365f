package org.zstack.header.protocol;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;

@RestRequest(
        path = "/routerArea/network",
        optionalPaths = {"/routerArea/networkR/{uuid}"},
        responseClass = APIQueryVRouterOspfNetworkReply.class,
        method = HttpMethod.GET
)
@Action(category = RouteProtocolConstants.ACTION_CATEGORY, names = {"read"})
@AutoQuery(replyClass = APIQueryVRouterOspfNetworkReply.class, inventoryClass = NetworkRouterAreaRefInventory.class)
public class APIQueryVRouterOspfNetworkMsg extends APIQueryMessage {
}
