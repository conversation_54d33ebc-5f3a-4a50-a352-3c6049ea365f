package org.zstack.routeProtocol;

import org.zstack.header.identity.rbac.RBACDescription;
import org.zstack.header.protocol.APICreateVRouterOspfAreaMsg;
import org.zstack.header.protocol.APIDeleteVRouterOspfAreaMsg;
import org.zstack.header.protocol.APIUpdateVRouterOspfAreaMsg;

public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("routeProtocol")
                .adminOnlyAPIs(APICreateVRouterOspfAreaMsg.class,
                        APIDeleteVRouterOspfAreaMsg.class,
                        APIUpdateVRouterOspfAreaMsg.class)
                .normalAPIs("org.zstack.header.protocol.**")
                .build();
    }

    @Override
    public void contributeToRoles() {
        roleContributorBuilder()
                .roleName("vpc")
                .actionsByPermissionName("routeProtocol")
                .build();
    }

    @Override
    public void roles() {

    }

    @Override
    public void globalReadableResources() {

    }
}
