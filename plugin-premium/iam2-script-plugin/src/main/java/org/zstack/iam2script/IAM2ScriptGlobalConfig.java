package org.zstack.iam2script;

import org.zstack.core.config.GlobalConfig;
import org.zstack.core.config.GlobalConfigDef;
import org.zstack.core.config.GlobalConfigDefinition;
import org.zstack.core.config.GlobalConfigValidation;

/**
 * Created by <PERSON> on 2020/3/3
 */

@GlobalConfigDefinition
public class IAM2ScriptGlobalConfig {
    public static final String CATEGORY = "iam2Script";

    @GlobalConfigValidation(validValues = {"true", "false"})
    @GlobalConfigDef(defaultValue = "false", description = "allow admin run iam2 script on management node if enabled.")
    public static GlobalConfig ENABLE_IAM2_SCRIPT = new GlobalConfig(CATEGORY, "enable.iam2.script");
}
