package org.zstack.iam2script;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.longjob.*;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.MessageReply;
import org.zstack.iam2script.api.APIRunIAM2ScriptEvent;
import org.zstack.iam2script.api.APIRunIAM2ScriptMsg;
import org.zstack.iam2script.message.RunIAM2ScriptMsg;
import org.zstack.iam2script.message.RunIAM2ScriptReply;
import org.zstack.longjob.LongJobUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import static org.zstack.core.Platform.err;
import static org.zstack.core.Platform.ioerr;
import static org.zstack.core.Platform.operr;
import static org.zstack.utils.path.PathUtil.createTempFile;
import static org.zstack.utils.path.PathUtil.forceRemoveFile;

/**
 * Created by Qi Le on 2020/2/27
 */

@LongJobFor(APIRunIAM2ScriptMsg.class)
@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class IAM2ScriptLongJob implements LongJob {
    private static final CLogger logger = Utils.getLogger(IAM2ScriptLongJob.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    protected DatabaseFacade dbf;

    @Override
    public void start(LongJobVO job, ReturnValueCompletion<APIEvent> completion) {
        RunIAM2ScriptMsg rmsg = JSONObjectUtil.toObject(job.getJobData(), RunIAM2ScriptMsg.class);

        String scriptFilePath;
        String outputFilePath;
        String errorFilePath;
        try {
            scriptFilePath = createTempFile("iam2Script", ScriptExecutorType.getSuffixByExecutorName(rmsg.getScriptExecutor()));
            outputFilePath = createTempFile("iam2ScriptOutput", "");
            errorFilePath = createTempFile("iam2ScriptError", "");
            logger.debug(String.format("Successfully created script file: %s, std output file: %s, std err file: %s.",
                    scriptFilePath, outputFilePath, errorFilePath));
        } catch (Exception e) {
            completion.fail(ioerr("Create temp file failed."));
            return;
        }
        rmsg.setScriptFilePath(scriptFilePath);
        rmsg.setOutputFilePath(outputFilePath);
        rmsg.setErrorFilePath(errorFilePath);

        bus.makeLocalServiceId(rmsg, IAM2ScriptManager.SERVICE_ID);
        bus.send(rmsg, new CloudBusCallBack(completion) {

            @Override
            public void run(MessageReply reply) {
                forceRemoveFile(scriptFilePath);
                forceRemoveFile(outputFilePath);
                forceRemoveFile(errorFilePath);
                if (reply instanceof RunIAM2ScriptReply) {
                    RunIAM2ScriptReply scriptReply = reply.castReply();
                    if (!scriptReply.isSuccess()) {
                        job.setJobResult(scriptReply.getErrorMessage());
                        job.setState(LongJobState.Failed);
                        dbf.update(job);
                        completion.fail(scriptReply.getError());
                        return;
                    }

                    APIRunIAM2ScriptEvent event = new APIRunIAM2ScriptEvent();
                    job.setJobResult(scriptReply.getOutputResult());
                    dbf.update(job);
                    completion.success(event);
                } else {
                    if (!reply.isSuccess()) {
                        job.setJobResult("Job failed by unknown reasons.");
                        job.setState(LongJobState.Failed);
                        dbf.update(job);
                        completion.fail(reply.getError());
                        return;
                    }

                    APIRunIAM2ScriptEvent event = new APIRunIAM2ScriptEvent();
                    job.setJobResult("Job finished.");
                    dbf.update(job);
                    completion.success(event);
                }
            }
        });
    }
}
