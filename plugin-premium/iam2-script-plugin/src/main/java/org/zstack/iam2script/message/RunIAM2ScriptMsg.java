package org.zstack.iam2script.message;

import org.zstack.header.message.NeedReplyMessage;

import java.util.List;

/**
 * Created by <PERSON> on 2020/2/27
 */
public class RunIAM2ScriptMsg extends NeedReplyMessage {

    private String scriptContent;

    private String scriptExecutor;

    private String scriptFilePath;

    private String outputFilePath;

    private String errorFilePath;

    private List<String> scriptParams;

    public String getScriptContent() {
        return scriptContent;
    }

    public void setScriptContent(String scriptContent) {
        this.scriptContent = scriptContent;
    }

    public String getScriptExecutor() {
        return scriptExecutor;
    }

    public void setScriptExecutor(String scriptExecutor) {
        this.scriptExecutor = scriptExecutor;
    }

    public String getScriptFilePath() {
        return scriptFilePath;
    }

    public void setScriptFilePath(String scriptFilePath) {
        this.scriptFilePath = scriptFilePath;
    }

    public List<String> getScriptParams() {
        return scriptParams;
    }

    public void setScriptParams(List<String> scriptParams) {
        this.scriptParams = scriptParams;
    }

    public String getOutputFilePath() {
        return outputFilePath;
    }

    public void setOutputFilePath(String outputFilePath) {
        this.outputFilePath = outputFilePath;
    }

    public String getErrorFilePath() {
        return errorFilePath;
    }

    public void setErrorFilePath(String errorFilePath) {
        this.errorFilePath = errorFilePath;
    }
}
