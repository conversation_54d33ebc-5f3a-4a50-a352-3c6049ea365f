package org.zstack.iam2script;

/**
 * Created by <PERSON> on 2020/2/26
 */
public enum ScriptExecutorType {
    Python("python", ".py");

    public static final ScriptExecutorType[] VALUES = values();
    public final String name;
    public final String suffix;

    public static String getSuffixByExecutorName(String name) {
        for (ScriptExecutorType type : VALUES) {
            if (type.name.equals(name)) {
                return type.suffix;
            }
        }
        return "";
    }

    ScriptExecutorType(String name, String fileSuffix) {
        this.name = name;
        this.suffix = fileSuffix;
    }
}
