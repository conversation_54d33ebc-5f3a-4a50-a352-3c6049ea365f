package org.zstack.network.service.virtualrouter.vip;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@StaticMetamodel(VirtualRouterVipVO.class)
public class VirtualRouterVipVO_ {
    public static volatile SingularAttribute<VirtualRouterVipVO, String> virtualRouterVmUuid;
    public static volatile SingularAttribute<VirtualRouterVipVO, String> uuid;
}
