package org.zstack.storage.cdp;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.compute.vm.NextVolumeDeviceIdGetter;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.*;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.core.*;
import org.zstack.header.core.progress.TaskProgressRange;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.host.HostInventory;
import org.zstack.header.host.HostVO;
import org.zstack.header.image.ImageVO;
import org.zstack.header.image.ImageVO_;
import org.zstack.header.message.MessageReply;
import org.zstack.header.storage.backup.VolumeMetadata;
import org.zstack.header.storage.cdp.CdpVolumeHistoryVO;
import org.zstack.header.storage.cdp.CdpVolumeHistoryVO_;
import org.zstack.header.storage.cdp.RecoverVmVolumesMsg;
import org.zstack.header.storage.cdp.RevertVmFromCdpBackupMsg;
import org.zstack.header.storage.primary.*;
import org.zstack.header.tag.TagInventory;
import org.zstack.header.vm.*;
import org.zstack.header.vm.devices.DeviceAddress;
import org.zstack.header.vm.devices.VirtualDeviceInfo;
import org.zstack.header.vm.devices.VmInstanceDeviceManager;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.volume.*;
import org.zstack.identity.AccountManager;
import org.zstack.kvm.KVMHostInventory;
import org.zstack.kvm.KVMHostVO;
import org.zstack.kvm.KVMHostVO_;
import org.zstack.kvm.VolumeTO;
import org.zstack.longjob.LongJobFlowContextHandler;
import org.zstack.longjob.LongJobUtils;
import org.zstack.mevoco.MevocoConstants;
import org.zstack.storage.backup.VolumeMetaDataMaker;
import org.zstack.storage.volume.VolumeUtils;
import org.zstack.tag.TagManager;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import javax.persistence.Tuple;
import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class KvmCdpVmLiveRestoreFlowChain {
    private static final CLogger logger = Utils.getLogger(KvmCdpVmLiveRestoreFlowChain.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private AccountManager acntMngr;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    private VolumeDeletionPolicyManager deletionPolicyMgr;
    @Autowired
    private CdpBackupStorage cdpBackupStorage;
    @Autowired
    private VmInstanceDeviceManager vidm;
    @Autowired
    protected PluginRegistry pluginRgty;

    private final RevertVmFromCdpBackupMsg msg;
    private final String cdpTaskUuid;

    private KvmCdpVmLiveRestoreFlowChain(final RevertVmFromCdpBackupMsg msg, String cdpTaskUuid) {
        this.msg = msg;
        this.cdpTaskUuid = cdpTaskUuid;
    }

    static KvmCdpVmLiveRestoreFlowChain getFlow(final RevertVmFromCdpBackupMsg msg, String cdpTaskUuid) {
        return new KvmCdpVmLiveRestoreFlowChain(msg, cdpTaskUuid);
    }

    // Root volume followed by data volumes sorted by device ID.
    private List<VolumeVO> getVmAllVolumes() {
        final List<VolumeVO> volumes = new ArrayList<>();
        final VmInstanceVO vmInstanceVO = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        volumes.add(vmInstanceVO.getRootVolume());
        volumes.addAll(vmInstanceVO.getAllDiskVolumes().stream()
                .filter(v -> v.getType().equals(VolumeType.Data))
                .sorted(Comparator.comparingInt(VolumeVO::getDeviceId))
                .collect(Collectors.toList())
        );
        return volumes;
    }

    private HostInventory getHostInv() {
        final String hostUuid = msg.getHostUuid();
        final HostVO host = dbf.findByUuid(hostUuid, HostVO.class);
        if (host != null) {
            return HostInventory.valueOf(host);
        }

        throw new OperationFailureException(operr("recoverVm: host[uuid: %s] not found for VM[uuid: %s]",
                hostUuid, this.msg.getVmInstanceUuid()));
    }

    private static class SortVolumeHelper {
        final private long deviceId;
        final private String volumeUuid;

        public SortVolumeHelper(long deviceId, String volumeUuid) {
            this.deviceId = deviceId;
            this.volumeUuid = volumeUuid;
        }

        public long getDeviceId() {
            return deviceId;
        }

        public String getVolumeUuid() {
            return volumeUuid;
        }
    }

    private static long getDeviceId(final String volumeUuid, final CdpBackupInfo info) {
        final String metadata = info.getMetadata();
        if (metadata != null) {
            VolumeMetadata meta = VolumeMetaDataMaker.parseVolumeMetadata(metadata);
            if (meta != null) {
                return VolumeType.Root.toString().equals(meta.getType()) ? 0 : meta.getDeviceId();
            }
        }

        // Guess from existing volume records.
        final Integer deviceId = Q.New(VolumeVO.class)
                .eq(VolumeVO_.uuid, volumeUuid)
                .select(VolumeVO_.deviceId)
                .findValue();

        return deviceId == null ? 1 : deviceId;
    }

    private static List<String> sortVolumeUuidByDeviceId(final Map<String, CdpBackupInfo> volumesToCreate) {
        List<SortVolumeHelper> volumes = new ArrayList<>();
        if (volumesToCreate.size() <= 1) {
            return new ArrayList<>(volumesToCreate.keySet());
        }

        for (Map.Entry<String, CdpBackupInfo> entry : volumesToCreate.entrySet()) {
            final String volumeUuid = entry.getKey();
            final long deviceId = getDeviceId(volumeUuid, entry.getValue());
            volumes.add(new SortVolumeHelper(deviceId, volumeUuid));
        }

        return volumes.stream().sorted(Comparator.comparingLong(SortVolumeHelper::getDeviceId))
                .map(SortVolumeHelper::getVolumeUuid)
                .collect(Collectors.toList());
    }

    private static String getVolumeImageUuidFromMetadata(String volumeUuid, CdpBackupInfo info) {
        String metadata = info.getMetadata();
        if (metadata == null) {
            return null;
        }

        VolumeMetadata meta = VolumeMetaDataMaker.parseVolumeMetadata(metadata);
        return meta == null ? null : meta.getRootImageUuid();
    }

    private static String findRootVolumeUuid(Map<String, CdpBackupInfo> volumes) {
        if (volumes.size() == 1) {
            // VM backup which contains only root volume.
            return volumes.keySet().iterator().next();
        }

        for (Map.Entry<String, CdpBackupInfo> volume : volumes.entrySet()) {
            final String metadata = volume.getValue().getMetadata();
            if (metadata == null) {
                continue;
            }

            if (isRootVolume(metadata)) {
                // volume has metadata, and is root volume.
                return volume.getKey();
            }
        }

        // No volume metadata found from CDP server (old deployments do not have metadata)
        final List<String> uuids = Q.New(VolumeVO.class)
                .eq(VolumeVO_.type, VolumeType.Root)
                .in(VolumeVO_.uuid, volumes.keySet())
                .orderBy(VolumeVO_.deviceId, SimpleQuery.Od.ASC)
                .select(VolumeVO_.uuid)
                .listValues();
        switch (uuids.size()) {
            case 0:
                return null;
            case 1:
                return uuids.get(0);
            default:
                throw new OperationFailureException(operr("multiple root volumes found: %s", uuids));
        }
    }

    private static boolean isRootVolume(String metadata) {
        VolumeMetadata meta = VolumeMetaDataMaker.parseVolumeMetadata(metadata);
        return meta != null && VolumeType.Root.toString().equals(meta.getType());
    }

    private static void fillDeviceIds(Collection<String> volumeUuids, Map<String, Integer> saved) {
        List<Tuple> tuples = Q.New(VolumeVO.class)
                .in(VolumeVO_.uuid, volumeUuids)
                .select(VolumeVO_.uuid, VolumeVO_.deviceId)
                .listTuple();
        for (Tuple t : tuples) {
            saved.put(t.get(0, String.class), t.get(1, Integer.class));
        }
    }

    private static long findVolumeSize(String volumeUuid, List<VolumeVO> existingVolumes) {
        for (VolumeVO vo : existingVolumes) {
            if (volumeUuid.equals(vo.getUuid())) {
                return vo.getSize();
            }
        }

        return 0;
    }

    private void doSaveContext() {
        LongJobContextUtil.saveContext(msg.getLongJobUuid(), msg.getJobContext());
    }

    public void run(Completion completion) {
        /**
         * run() launches VM with CDP recovery point and then use storage migration to copy
         * disk data back to primary storage.  The copy block job might be cancelled explicitly,
         * which will lead to recovery failure immediately. So that VM is stopped can user can
         * choose another CDP recovery point.
         * <p>
         * The recovering flow has a key assurance:
         * - It should be reentrant - the flow shall behave as if there was a failure.
         * - It implies the corresponding long job should implement cancel() and resume().
         */
        final HostInventory dstHostInventory = getHostInv();

        final TaskProgressRecorder progressRecorder = new TaskProgressRecorder();
        final TaskProgressRange PREP_VOLUME_STAGE = new TaskProgressRange(0, 20);
        final TaskProgressRange START_VM_STAGE = new TaskProgressRange(20, 30);
        final TaskProgressRange RECOVER_STAGE = new TaskProgressRange(30, 90);
        final TaskProgressRange UNEXPORT_RP_STAGE = new TaskProgressRange(90, 100);

        final RevertVmLongJobContext ctx = msg.getJobContext();

        // track volumes contains in a VM backup
        // volume uuid -> nbd target
        final Map<String, CdpBackupInfo> volumeTargetMap = new ConcurrentHashMap<>();

        if (ctx.getExistingRootVolumeUuid() == null) {
            ctx.setExistingVolumes(getVmAllVolumes());
            ctx.setExistingRootVolumeUuid(ctx.getExistingVolumes().get(0).getUuid());
            doSaveContext();
        }

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("live-recover-vm-%s-from-bs-%s", msg.getVmInstanceUuid(), msg.getBackupStorageUuid()));
        chain.preCheck(data -> LongJobUtils.buildErrIfCanceled());
        chain.then(new Flow() {
            // There might be no metadata ...
            // We will first bring up remote data sources.  It might have different number of volumes.
            final String __name__ = "setup-volume-recover-data-source-for-vm-" + msg.getVmInstanceUuid();

            @Override
            public void run(FlowTrigger trigger, Map data) {
                progressRecorder.reportRangeStart(PREP_VOLUME_STAGE);
                cdpBackupStorage.exportRp(msg.getVmInstanceUuid(),
                        msg.getGroupId(),
                        msg.getBackupStorageUuid(),
                        new ReturnValueCompletion<Map<String, CdpBackupInfo>>(trigger) {
                            @Override
                            public void success(Map<String, CdpBackupInfo> returnValue) {
                                volumeTargetMap.putAll(returnValue);
                                if (volumeTargetMap.isEmpty()) {
                                    trigger.fail(operr("no volume records found from VM backup"));
                                    return;
                                }

                                ctx.setRemoteRootVolumeUuid(findRootVolumeUuid(volumeTargetMap));
                                if (ctx.getRemoteRootVolumeUuid() == null) {
                                    trigger.fail(operr("no root volume found from VM backup"));
                                } else {
                                    trigger.next();
                                }
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (LongJobUtils.buildErrIfCanceled() == null) {
                    // Not cancelled, keep the overlay.
                    trigger.rollback();
                    return;
                }

                cdpBackupStorage.unexportRp(msg.getVmInstanceUuid(),
                        msg.getGroupId(),
                        msg.getBackupStorageUuid(), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.rollback();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.rollback();
                            }
                        }
                );
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "compute-new-volumes-for-recovering-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return ctx.isNewVolumesComputed();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                final Set<String> existingVolumeUuids = ctx.getExistingVolumes().stream()
                        .map(VolumeVO::getUuid)
                        .collect(Collectors.toSet());
                if (!msg.isUseExistingVolume()) {
                    ctx.getVolumesToCreate().putAll(volumeTargetMap);
                    ctx.getAllDataVolumesToCreate().addAll(ctx.getVolumesToCreate().keySet());
                    ctx.getAllDataVolumesToCreate().remove(ctx.getRemoteRootVolumeUuid());
                    ctx.getVolumesToDetach().addAll(existingVolumeUuids);

                    ctx.setNewVolumesComputed(true);
                    trigger.next();
                    return;
                }

                final Set<String> remoteVolumeUuids = new HashSet<>(volumeTargetMap.keySet());
                for (String volumeUuid : remoteVolumeUuids) {
                    if (volumeUuid.equals(ctx.getRemoteRootVolumeUuid())) continue;
                    ctx.getVolumeMappingDict().put(volumeUuid, volumeUuid);
                }
                ctx.getVolumeMappingDict().put(ctx.getExistingRootVolumeUuid(), ctx.getRemoteRootVolumeUuid());

                remoteVolumeUuids.removeAll(existingVolumeUuids);
                remoteVolumeUuids.remove(ctx.getRemoteRootVolumeUuid());
                for (String uuid : remoteVolumeUuids) {
                    // In case of 'overwrite' we need to check that VM still owns those disks.
                    if (dbf.isExist(uuid, ResourceVO.class)) {
                        trigger.fail(operr("volume %s contains in backup but detached from VM[uuid: %s]: you need to either attach it back or delete it",
                                uuid, msg.getVmInstanceUuid()
                        ));
                        return;
                    }

                    // This volume exists in backup, but has been deleted locally.
                    ctx.getVolumesToCreate().put(uuid, volumeTargetMap.get(uuid));
                }

                ctx.getAllDataVolumesToCreate().addAll(ctx.getVolumesToCreate().keySet());
                existingVolumeUuids.removeAll(volumeTargetMap.keySet());
                ctx.getVolumesToDelete().addAll(existingVolumeUuids);
                ctx.getVolumesToDelete().remove(ctx.getExistingRootVolumeUuid());  // root volume will be overwritten

                ctx.setNewVolumesComputed(true);
                trigger.next();
            }
        }).then(new Flow() {
            final String __name__ = "allocate-primary-storage-for-root-volume";

            @Override
            public boolean skip(Map data) {
                return !ctx.getVolumesToCreate().containsKey(ctx.getRemoteRootVolumeUuid()) || ctx.isNewVolumesCreated();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                AllocatePrimaryStorageSpaceMsg amsg = new AllocatePrimaryStorageSpaceMsg();
                amsg.setRequiredHostUuid(dstHostInventory.getUuid());
                amsg.setRequiredPrimaryStorageUuid(msg.getPrimaryStorageUuidForRootVolume());
                amsg.setSize(ctx.getVolumesToCreate().get(ctx.getRemoteRootVolumeUuid()).getSize());
                amsg.setPurpose(PrimaryStorageAllocationPurpose.CreateRootVolume.toString());
                bus.makeLocalServiceId(amsg, PrimaryStorageConstant.SERVICE_ID);
                bus.send(amsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            AllocatePrimaryStorageSpaceReply ar = (AllocatePrimaryStorageSpaceReply) reply;
                            ctx.setRootVolumeAllocatedUrl(ar.getAllocatedInstallUrl());
                            if (msg.getPrimaryStorageUuidForRootVolume() == null) {
                                msg.setPrimaryStorageUuidForRootVolume(ar.getPrimaryStorageInventory().getUuid());
                            }
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (msg.getPrimaryStorageUuidForRootVolume() != null) {
                    ReleasePrimaryStorageSpaceMsg rmsg = new ReleasePrimaryStorageSpaceMsg();
                    rmsg.setDiskSize(ctx.getVolumesToCreate().get(ctx.getRemoteRootVolumeUuid()).getSize());
                    rmsg.setPrimaryStorageUuid(msg.getPrimaryStorageUuidForRootVolume());
                    rmsg.setAllocatedInstallUrl(ctx.getRootVolumeAllocatedUrl());
                    bus.makeTargetServiceIdByResourceUuid(rmsg, PrimaryStorageConstant.SERVICE_ID, rmsg.getPrimaryStorageUuid());
                    bus.send(rmsg);
                }
                trigger.rollback();
            }
        }).then(new Flow() {
            final String __name__ = "allocate-primary-storage-for-data-volume";

            private long totalSize = 0;

            @Override
            public boolean skip(Map data) {
                return ctx.getAllDataVolumesToCreate().isEmpty() || ctx.isNewVolumesCreated();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                totalSize = ctx.getAllDataVolumesToCreate().stream()
                        .map(uuid -> ctx.getVolumesToCreate().get(uuid).getSize())
                        .reduce(0L, Long::sum);
                AllocatePrimaryStorageSpaceMsg amsg = new AllocatePrimaryStorageSpaceMsg();
                amsg.setRequiredHostUuid(dstHostInventory.getUuid());
                amsg.setRequiredPrimaryStorageUuid(msg.getPrimaryStorageUuidForDataVolume());
                amsg.setTotalSize(totalSize);
                amsg.setPurpose(PrimaryStorageAllocationPurpose.CreateDataVolume.toString());
                bus.makeLocalServiceId(amsg, PrimaryStorageConstant.SERVICE_ID);
                bus.send(amsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            AllocatePrimaryStorageSpaceReply ar = (AllocatePrimaryStorageSpaceReply) reply;
                            ctx.setDataVolumeAllocatedUrl(ar.getAllocatedInstallUrl());
                            if (msg.getPrimaryStorageUuidForDataVolume() == null) {
                                msg.setPrimaryStorageUuidForDataVolume(ar.getPrimaryStorageInventory().getUuid());
                            }
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                if (msg.getPrimaryStorageUuidForDataVolume() != null && totalSize > 0) {
                    ReleasePrimaryStorageSpaceMsg rmsg = new ReleasePrimaryStorageSpaceMsg();
                    rmsg.setDiskSize(totalSize);
                    rmsg.setPrimaryStorageUuid(msg.getPrimaryStorageUuidForDataVolume());
                    rmsg.setAllocatedInstallUrl(ctx.getDataVolumeAllocatedUrl());
                    bus.makeTargetServiceIdByResourceUuid(rmsg, PrimaryStorageConstant.SERVICE_ID, rmsg.getPrimaryStorageUuid());
                    bus.send(rmsg);
                }
                trigger.rollback();
            }
        }).then(new Flow() {
            final String __name__ = "allocate-volumes-for-recover-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return ctx.getVolumesToCreate().isEmpty() || ctx.isNewVolumesCreated();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                final String desc = String.format("[Recovered] cdpTaskUuid: %s, groupId: %d", cdpTaskUuid, msg.getGroupId());
                final String vmName = Q.New(VmInstanceVO.class)
                        .eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid())
                        .select(VmInstanceVO_.name)
                        .findValue();
                final String defaultRootVolumeName = String.format("ROOT-for-%s", vmName);
                final String defaultDataVolumeName = String.format("DATA-for-%s", vmName);

                // Sort volumes to create by device ID so that `createdVolumes' is ordered by device ID.
                new While<>(sortVolumeUuidByDeviceId(ctx.getVolumesToCreate())).each((volumeUuid, whileCompletion) -> {
                    CreateVolumeMsg cmsg = new CreateVolumeMsg();
                    if (msg.isUseExistingVolume()) {
                        cmsg.setResourceUuid(volumeUuid);
                    }
                    cmsg.setAccountUuid(acntMngr.getOwnerAccountUuidOfResource(msg.getVmInstanceUuid()));
                    cmsg.setSize(ctx.getVolumesToCreate().get(volumeUuid).getSize());
                    cmsg.setVmInstanceUuid(msg.getVmInstanceUuid());
                    if (volumeUuid.equals(ctx.getRemoteRootVolumeUuid())) {
                        cmsg.setPrimaryStorageUuid(msg.getPrimaryStorageUuidForRootVolume());
                        cmsg.setVolumeType(VolumeType.Root.toString());
                        cmsg.setName(defaultRootVolumeName);
                    } else {
                        cmsg.setPrimaryStorageUuid(msg.getPrimaryStorageUuidForDataVolume());
                        cmsg.setVolumeType(VolumeType.Data.toString());
                        cmsg.setName(defaultDataVolumeName);
                    }
                    cmsg.setDescription(desc);
                    // VolumeProvisioningStrategy is handled afterwards during volume instantiation.
                    cmsg.setSystemTags(new ArrayList<>());

                    final CdpBackupInfo info = ctx.getVolumesToCreate().get(volumeUuid);
                    final VolumeMetadata meta = VolumeMetaDataMaker.parseVolumeMetadata(info.getMetadata());
                    if (meta != null) {
                        cmsg.setName(meta.getName());
                        if (meta.getSystemTags() != null) {
                            cmsg.getSystemTags().addAll(meta.getSystemTags().stream()
                                    .filter(it -> tagMgr.isCloneable(it.getTag(), it.getResourceType()))
                                    .map(TagInventory::getTag)
                                    .collect(Collectors.toList())
                            );
                        }
                    }

                    bus.makeLocalServiceId(cmsg, VolumeConstant.SERVICE_ID);
                    bus.send(cmsg, new CloudBusCallBack(whileCompletion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                whileCompletion.addError(reply.getError());
                                whileCompletion.allDone();
                                return;
                            }

                            CreateVolumeReply r = reply.castReply();
                            ctx.getVolumeMappingDict().put(r.getInventory().getUuid(), volumeUuid);
                            ctx.getVolumeMappingDict().putIfAbsent(volumeUuid, r.getInventory().getUuid());
                            ctx.getCreatedVolumes().add(r.getInventory());
                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            ctx.setNewVolumesCreated(true);
                            trigger.next();
                        } else {
                            trigger.fail(errorCodeList.getCauses().get(0));
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                List<DeleteVolumeMsg> dmsgs = ctx.getCreatedVolumes().stream()
                        .map(v -> {
                            DeleteVolumeMsg dmsg = new DeleteVolumeMsg();
                            dmsg.setDetachBeforeDeleting(false);
                            dmsg.setUuid(v.getUuid());
                            dmsg.setDeletionPolicy(VolumeDeletionPolicyManager.VolumeDeletionPolicy.Direct.toString());
                            bus.makeTargetServiceIdByResourceUuid(dmsg, VolumeConstant.SERVICE_ID, v.getUuid());
                            return dmsg;
                        }).collect(Collectors.toList());
                if (!dmsgs.isEmpty()) {
                    bus.send(dmsgs);
                }

                trigger.rollback();
            }
        }).then(new Flow() {
            final String __name__ = "instantiate-volume-for-recover-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return ctx.getVolumesToCreate().isEmpty();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                new While<>(ctx.getCreatedVolumes()).each((volumeInventory, whileCompletion) -> {
                    if (ctx.getInitializedVolumes().stream().anyMatch(v -> v.getUuid().equals(volumeInventory.getUuid()))) {
                        whileCompletion.done();
                        return;
                    }

                    VolumeUtils.SetVolumeProvisioningStrategy(volumeInventory.getUuid(), VolumeProvisioningStrategy.ThickProvisioning);
                    // InstantiateVolumeMsg is re-entrant.
                    InstantiateVolumeMsg imsg = new InstantiateVolumeMsg();
                    imsg.setPrimaryStorageAllocated(true);
                    if (volumeInventory.getType().equals(VolumeType.Root.toString())) {
                        imsg.setPrimaryStorageUuid(msg.getPrimaryStorageUuidForRootVolume());
                        imsg.setAllocatedInstallUrl(ctx.getRootVolumeAllocatedUrl());
                    } else {
                        imsg.setPrimaryStorageUuid(msg.getPrimaryStorageUuidForDataVolume());
                        imsg.setAllocatedInstallUrl(ctx.getDataVolumeAllocatedUrl());
                    }
                    imsg.setVolumeUuid(volumeInventory.getUuid());
                    imsg.setHostUuid(dstHostInventory.getUuid());
                    bus.makeTargetServiceIdByResourceUuid(imsg, VolumeConstant.SERVICE_ID, imsg.getVolumeUuid());
                    bus.send(imsg, new CloudBusCallBack(whileCompletion) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                whileCompletion.addError(reply.getError());
                                whileCompletion.allDone();
                                return;
                            }

                            InstantiateVolumeReply r = reply.castReply();
                            ctx.getInitializedVolumes().add(r.getVolume());
                            doSaveContext();

                            if (VolumeType.Root.toString().equals(r.getVolume().getType())) {
                                new SQLBatch() {
                                    @ExceptionSafe
                                    @Override
                                    protected void scripts() {
                                        String rootImageUuid = q(VolumeVO.class)
                                                .eq(VolumeVO_.uuid, ctx.getExistingRootVolumeUuid())
                                                .select(VolumeVO_.rootImageUuid)
                                                .findValue();
                                        if (rootImageUuid != null) {
                                            sql(VolumeVO.class).eq(VolumeVO_.uuid, r.getVolume().getUuid())
                                                    .set(VolumeVO_.rootImageUuid, rootImageUuid)
                                                    .update();
                                        }
                                    }
                                }.execute();
                            }
                            whileCompletion.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                        } else {
                            trigger.fail(errorCodeList.getCauses().get(0));
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                List<DeleteVolumeOnPrimaryStorageMsg> dmsgs = ctx.getInitializedVolumes().stream()
                        .map(v -> {
                            DeleteVolumeOnPrimaryStorageMsg dmsg = new DeleteVolumeOnPrimaryStorageMsg();
                            dmsg.setUuid(v.getPrimaryStorageUuid());
                            dmsg.setVolume(v);
                            bus.makeTargetServiceIdByResourceUuid(dmsg, PrimaryStorageConstant.SERVICE_ID, v.getPrimaryStorageUuid());
                            return dmsg;
                        }).collect(Collectors.toList());
                if (!dmsgs.isEmpty()) {
                    bus.send(dmsgs);
                }

                trigger.rollback();
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "check-volume-resize-for-recovering-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return !ctx.getVolumeUuidsToUpdateInstallPath().isEmpty();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!msg.isUseExistingVolume()) {
                    ctx.getVolumeUuidsToUpdateInstallPath().addAll(ctx.getInitializedVolumes().stream().map(VolumeInventory::getUuid).collect(Collectors.toSet()));
                    trigger.next();
                    return;
                }

                // Root volume uuid might have been changed. c.f. #45919
                ctx.getVolumeUuidsToUpdateInstallPath().addAll(volumeTargetMap.keySet());
                if (!ctx.getExistingRootVolumeUuid().equals(ctx.getRemoteRootVolumeUuid())) {
                    ctx.getVolumeUuidsToUpdateInstallPath().remove(ctx.getRemoteRootVolumeUuid());
                    ctx.getVolumeUuidsToUpdateInstallPath().add(ctx.getExistingRootVolumeUuid());
                }

                final Set<String> existingVolumeUuids = new HashSet<>(ctx.getVolumeUuidsToUpdateInstallPath());
                existingVolumeUuids.removeAll(ctx.getVolumesToCreate().keySet());
                ctx.getExistingVolumesToReuse().addAll(existingVolumeUuids);

                new While<>(ctx.getExistingVolumesToReuse()).each((volumeUuid, compl) -> {
                    if (ctx.getResizedVolumes().contains(volumeUuid)) {
                        compl.done();
                        return;
                    }

                    VolumeUtils.SetVolumeProvisioningStrategy(volumeUuid, VolumeProvisioningStrategy.ThickProvisioning);

                    final long oldVolumeSize = findVolumeSize(volumeUuid, ctx.getExistingVolumes());
                    if (oldVolumeSize <= 0) {
                        compl.addError(operr("unexpected volume[uuid: %s] size: %d", volumeUuid, oldVolumeSize));
                        compl.allDone();
                        return;
                    }

                    final long newVolumeSize = volumeTargetMap.get(ctx.getVolumeMappingDict().get(volumeUuid)).getSize();
                    if (oldVolumeSize == newVolumeSize) {
                        compl.done();
                        return;
                    }

                    logger.info(String.format("volume[uuid: %s] size will be changed: now %d: after recover: %d", volumeUuid, oldVolumeSize, newVolumeSize));

                    // ResizeVolumeMsg/ResizeVolumeOnKvmMsg (no snapshot!)
                    ResizeVolumeMsg rmsg = new ResizeVolumeMsg();
                    rmsg.setTakeSnapshot(Boolean.FALSE);
                    rmsg.setSize(newVolumeSize);
                    rmsg.setVolumeUuid(volumeUuid);
                    rmsg.setForce(newVolumeSize < oldVolumeSize);
                    bus.makeTargetServiceIdByResourceUuid(rmsg, MevocoConstants.SERVICE_ID, volumeUuid);
                    bus.send(rmsg, new CloudBusCallBack(compl) {
                        @Override
                        public void run(MessageReply reply) {
                            if (reply.isSuccess()) {
                                ctx.getResizedVolumes().add(volumeUuid);
                                doSaveContext();
                                compl.done();
                            } else {
                                compl.addError(operr("resize volume[uuid: %s] failed: %s", volumeUuid, reply.getError().getDetails()));
                                compl.allDone();
                            }
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                        } else {
                            trigger.fail(errorCodeList.getCauses().get(0));
                        }
                    }
                });
            }
        }).then(new Flow() {
            final String __name__ = "update-volume-records-for-recovering-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return ctx.isRecordsUpdatedForRecovering();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                logger.info("volume uuids to recover: " + ctx.getVolumeUuidsToUpdateInstallPath());
                final ErrorCode err = new SQLBatchWithReturn<ErrorCode>() {
                    @Override
                    protected ErrorCode scripts() {
                        for (String volumeUuid : ctx.getVolumeUuidsToUpdateInstallPath()) {
                            String installPath = q(VolumeVO.class)
                                    .eq(VolumeVO_.uuid, volumeUuid)
                                    .select(VolumeVO_.installPath)
                                    .findValue();
                            try {
                                if (new URI(installPath).getQuery() == null) {
                                    sql(VolumeVO.class)
                                            .eq(VolumeVO_.uuid, volumeUuid)
                                            .set(VolumeVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                                            .set(VolumeVO_.installPath, String.format("%s?r=%s",
                                                    installPath,
                                                    volumeTargetMap.get(ctx.getVolumeMappingDict().get(volumeUuid)).getNbdTarget()))
                                            .update();
                                }
                            } catch (URISyntaxException ignored) {
                                return operr("volume[uuid: %s] has unexpected path: %s", volumeUuid, installPath);
                            }
                        }

                        // need to detach needless volumes before live recovering VM.
                        if (msg.isUseExistingVolume()) {
                            if (!ctx.getVolumesToDelete().isEmpty()) {
                                fillDeviceIds(ctx.getVolumesToDelete(), ctx.getDeviceIdDict());
                                sql(VolumeVO.class)
                                        .in(VolumeVO_.uuid, ctx.getVolumesToDelete())
                                        .set(VolumeVO_.vmInstanceUuid, null)
                                        .set(VolumeVO_.deviceId, null)
                                        .set(VolumeVO_.lastVmInstanceUuid, msg.getVmInstanceUuid())
                                        .set(VolumeVO_.lastDetachDate, Timestamp.valueOf(LocalDateTime.now()))
                                        .update();
                            }
                        } else {
                            sql(VmInstanceVO.class)
                                    .eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid())
                                    .set(VmInstanceVO_.rootVolumeUuid, ctx.getVolumeMappingDict().get(ctx.getRemoteRootVolumeUuid()))
                                    .update();
                            fillDeviceIds(ctx.getVolumesToDetach(), ctx.getDeviceIdDict());
                            if (!ctx.getVolumesToDetach().isEmpty()) {
                                sql(VolumeVO.class)
                                        .in(VolumeVO_.uuid, ctx.getVolumesToDetach())
                                        .set(VolumeVO_.vmInstanceUuid, null)
                                        .set(VolumeVO_.deviceId, null)
                                        .set(VolumeVO_.lastVmInstanceUuid, msg.getVmInstanceUuid())
                                        .set(VolumeVO_.lastDetachDate, Timestamp.valueOf(LocalDateTime.now()))
                                        .update();
                            }
                        }

                        return null;
                    }
                }.execute();

                if (!msg.isUseExistingVolume() && CollectionUtils.isNotEmpty(ctx.getVolumesToDetach())) {
                    ctx.getVolumesToDetach().forEach(volUuid -> {
                        DeviceAddress volDeviceAddress = vidm.getVmDeviceAddress(volUuid, msg.getVmInstanceUuid());
                        VirtualDeviceInfo volInfo = new VirtualDeviceInfo();
                        volInfo.setResourceUuid(volUuid);
                        volInfo.setDeviceAddress(volDeviceAddress);
                        ctx.getVolumesDeviceInfos().add(volInfo);

                        for (VmDetachVolumeExtensionPoint ext : pluginRgty.getExtensionList(VmDetachVolumeExtensionPoint.class)) {
                            ext.afterDetachVolume(dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class).toInventory(),
                                    dbf.findByUuid(volUuid, VolumeVO.class).toInventory(), new NopeCompletion());
                        }
                    });
                }

                for (VolumeInventory inv : ctx.getInitializedVolumes()) {
                    Integer deviceId = VolumeType.Root.toString().equals(inv.getType()) ? 0 :
                            new NextVolumeDeviceIdGetter().getNextVolumeDeviceId(msg.getVmInstanceUuid());
                    SQL.New(VolumeVO.class)
                            .eq(VolumeVO_.uuid, inv.getUuid())
                            .set(VolumeVO_.deviceId, deviceId)
                            .update();
                }

                if (err != null) {
                    trigger.fail(err);
                    return;
                }

                ctx.setRecordsUpdatedForRecovering(true);
                progressRecorder.reportRangeEnd();

                if (!msg.isUseExistingVolume() && ctx.getVolumesToDetach().contains(ctx.getExistingRootVolumeUuid())) {
                    // c.f. ZSTAC-45724
                    ChangeVolumeTypeMsg cmsg = new ChangeVolumeTypeMsg();
                    cmsg.setType(VolumeType.Data);
                    cmsg.setUuid(ctx.getExistingRootVolumeUuid());
                    bus.makeTargetServiceIdByResourceUuid(cmsg, VolumeConstant.SERVICE_ID, cmsg.getVolumeUuid());
                    bus.send(cmsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            trigger.next();
                        }
                    });
                } else {
                    trigger.next();
                }
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                new SQLBatch() {
                    @Override
                    protected void scripts() {
                        for (String volumeUuid : ctx.getVolumeUuidsToUpdateInstallPath()) {
                            String installPath = q(VolumeVO.class)
                                    .eq(VolumeVO_.uuid, volumeUuid)
                                    .select(VolumeVO_.installPath)
                                    .findValue();
                            try {
                                final String query = new URI(installPath).getQuery();
                                if (query != null) {
                                    sql(VolumeVO.class)
                                            .eq(VolumeVO_.uuid, volumeUuid)
                                            .set(VolumeVO_.installPath, StringUtils.removeEnd(installPath, '?' + query))
                                            .update();
                                }
                            } catch (URISyntaxException ignored) {
                                logger.warn(String.format("volume[uuid: %s] has unexpected path: %s", volumeUuid, installPath));
                            }
                        }

                        for (Map.Entry<String, Integer> entry : ctx.getDeviceIdDict().entrySet()) {
                            sql(VolumeVO.class)
                                    .eq(VolumeVO_.uuid, entry.getKey())
                                    .set(VolumeVO_.deviceId, entry.getValue())
                                    .update();
                        }

                        if(!CollectionUtils.isEmpty(ctx.getVolumesDeviceInfos())){
                            ctx.getVolumesDeviceInfos().forEach(info -> vidm.createOrUpdateVmDeviceAddress(info, msg.getVmInstanceUuid()));
                        }

                        if (msg.isUseExistingVolume()) {
                            if (!ctx.getVolumesToDelete().isEmpty()) {
                                sql(VolumeVO.class)
                                        .in(VolumeVO_.uuid, ctx.getVolumesToDelete())
                                        .set(VolumeVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                                        .update();
                            }
                            return;
                        }

                        sql(VmInstanceVO.class)
                                .eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid())
                                .set(VmInstanceVO_.rootVolumeUuid, ctx.getExistingRootVolumeUuid())
                                .update();

                        if (!ctx.getVolumesToDetach().isEmpty()) {
                            sql(VolumeVO.class)
                                    .in(VolumeVO_.uuid, ctx.getVolumesToDetach())
                                    .notEq(VolumeVO_.uuid, ctx.getExistingRootVolumeUuid())
                                    .set(VolumeVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                                    .set(VolumeVO_.lastVmInstanceUuid, null)
                                    .update();
                        }

                        sql(VolumeVO.class)
                                .in(VolumeVO_.uuid, ctx.getInitializedVolumes().stream()
                                        .map(VolumeInventory::getUuid)
                                        .collect(Collectors.toSet()))
                                .set(VolumeVO_.vmInstanceUuid, null)
                                .update();
                    }
                }.execute();

                if (!msg.isUseExistingVolume() && ctx.getVolumesToDetach().contains(ctx.getExistingRootVolumeUuid())) {
                    // c.f. ZSTAC-45724
                    ChangeVolumeTypeMsg cmsg = new ChangeVolumeTypeMsg();
                    cmsg.setType(VolumeType.Root);
                    cmsg.setUuid(ctx.getExistingRootVolumeUuid());
                    bus.makeTargetServiceIdByResourceUuid(cmsg, VolumeConstant.SERVICE_ID, cmsg.getVolumeUuid());
                    bus.send(cmsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            new SQLBatch() {
                                @Override
                                protected void scripts() {
                                    sql(VolumeVO.class)
                                            .eq(VolumeVO_.uuid, ctx.getExistingRootVolumeUuid())
                                            .set(VolumeVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                                            .set(VolumeVO_.lastVmInstanceUuid, null)
                                            .update();
                                }
                            }.execute();
                            trigger.rollback();
                        }
                    });
                } else {
                    trigger.rollback();
                }
            }
        }).then(new Flow() {
            final String __name__ = "start-fast-recovery-for-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return ctx.isVmStarted();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                // TODO should not allow direct transition from volumeRecovering to Running.
                progressRecorder.reportRangeStart(START_VM_STAGE);
                StartVmInstanceMsg smsg = new StartVmInstanceMsg();
                smsg.setVmInstanceUuid(msg.getVmInstanceUuid());
                smsg.setHostUuid(dstHostInventory.getUuid());
                bus.makeTargetServiceIdByResourceUuid(smsg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                bus.send(smsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            ctx.setVmStarted(true);
                            progressRecorder.reportRangeEnd();
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                StopVmInstanceMsg smsg = new StopVmInstanceMsg();
                smsg.setVmInstanceUuid(msg.getVmInstanceUuid());
                smsg.setGcOnFailure(false);
                bus.makeTargetServiceIdByResourceUuid(smsg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                bus.send(smsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        trigger.rollback();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "recovering-volume-data-for-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return ctx.isRecovered();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                progressRecorder.reportRangeStart(RECOVER_STAGE);

                List<VolumeTO> volumes = VolumeTO.valueOf(
                        VolumeInventory.valueOf(
                                Q.New(VolumeVO.class)
                                        .eq(VolumeVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                                        .in(VolumeVO_.uuid, ctx.getVolumeUuidsToUpdateInstallPath())
                                        .list()),
                        KVMHostInventory.valueOf((KVMHostVO) Q.New(KVMHostVO.class)
                                .eq(KVMHostVO_.uuid, dstHostInventory.getUuid())
                                .find())
                );

                RecoverVmVolumesMsg rmsg = new RecoverVmVolumesMsg();
                rmsg.setRecoverBandwidth(msg.getRecoverBandwidth());
                rmsg.setVmInstanceUuid(msg.getVmInstanceUuid());
                rmsg.setHostUuid(dstHostInventory.getUuid());
                rmsg.setVolumes(volumes);
                rmsg.setLongJobUuid(msg.getLongJobUuid());
                bus.makeTargetServiceIdByResourceUuid(rmsg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                bus.send(rmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            ctx.setRecovered(true);
                            progressRecorder.reportRangeEnd();
                            trigger.next();
                        } else {
                            trigger.fail(reply.getError());
                        }
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "update-volume-records-after-recovering-vm-" + msg.getVmInstanceUuid();

            @Override
            public void run(FlowTrigger trigger, Map data) {
                logger.info("volumes recovered: " + ctx.getVolumeUuidsToUpdateInstallPath());
                new SQLBatch() {
                    @Override
                    protected void scripts() {
                        for (String volumeUuid : ctx.getVolumeUuidsToUpdateInstallPath()) {
                            String installPath = q(VolumeVO.class)
                                    .eq(VolumeVO_.uuid, volumeUuid)
                                    .select(VolumeVO_.installPath)
                                    .findValue();
                            try {
                                final String query = new URI(installPath).getQuery();
                                if (query != null) {
                                    sql(VolumeVO.class)
                                            .eq(VolumeVO_.uuid, volumeUuid)
                                            .set(VolumeVO_.installPath, StringUtils.removeEnd(installPath, '?' + query))
                                            .update();
                                }
                            } catch (URISyntaxException ignored) {
                                logger.warn(String.format("volume[uuid: %s] has unexpected path: %s", volumeUuid, installPath));
                            }
                        }
                    }
                }.execute();

                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "sync-volume-sizes-for-vm-" + msg.getVmInstanceUuid();

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<SyncVolumeSizeMsg> syncVolumeSizeMsgs = ctx.getInitializedVolumes().stream().map(v -> {
                    SyncVolumeSizeMsg syncVolumeSizeMsg = new SyncVolumeSizeMsg();
                    syncVolumeSizeMsg.setVolumeUuid(v.getUuid());
                    bus.makeTargetServiceIdByResourceUuid(syncVolumeSizeMsg, VolumeConstant.SERVICE_ID, v.getUuid());
                    return syncVolumeSizeMsg;
                }).collect(Collectors.toList());
                bus.send(syncVolumeSizeMsgs);

                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "delete-unused-volumes-for-vm-" + msg.getVmInstanceUuid();

            @Override
            public boolean skip(Map data) {
                return !msg.isUseExistingVolume();
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<DeleteVolumeMsg> dmsgs = ctx.getVolumesToDelete().stream()
                        .map(volumeUuid -> {
                            DeleteVolumeMsg dmsg = new DeleteVolumeMsg();
                            dmsg.setDeletionPolicy(deletionPolicyMgr.getDeletionPolicy(volumeUuid).toString());
                            dmsg.setDetachBeforeDeleting(false);
                            dmsg.setUuid(volumeUuid);
                            bus.makeTargetServiceIdByResourceUuid(dmsg, VolumeConstant.SERVICE_ID, volumeUuid);
                            return dmsg;
                        }).collect(Collectors.toList());
                if (!dmsgs.isEmpty()) {
                    logger.info("unused volumes to delete: " + ctx.getVolumesToDelete());
                    bus.send(dmsgs);
                }
                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "regenerate-cdp-task-records-for-vm-" + msg.getVmInstanceUuid();

            @Override
            public void run(FlowTrigger trigger, Map data) {
                // if reuse existing volume, clear CdpVolumeHistoryVO since volume has been modified.
                // CdpTaskResourceRefVO remains the same because task <-> VM mapping still holds.
                if (msg.isUseExistingVolume()) {
                    final Set<String> existingVolumeUuids = ctx.getExistingVolumes().stream()
                            .map(VolumeVO::getUuid)
                            .collect(Collectors.toSet());

                    logger.info(String.format("delete obsoleted history: bs: %s: volumes: %s",
                            msg.getBackupStorageUuid(), existingVolumeUuids));
                    SQL.New(CdpVolumeHistoryVO.class)
                            .eq(CdpVolumeHistoryVO_.backupStorageUuid, msg.getBackupStorageUuid())
                            .in(CdpVolumeHistoryVO_.volumeUuid, existingVolumeUuids)
                            .hardDelete();
                }
                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "un-export-rp-from-server";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                progressRecorder.reportRangeStart(UNEXPORT_RP_STAGE);
                cdpBackupStorage.unexportRp(msg.getVmInstanceUuid(), msg.getGroupId(), msg.getBackupStorageUuid(), new Completion(trigger) {
                    @Override
                    public void success() {
                        progressRecorder.reportRangeEnd();
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        logger.warn(String.format("failed to un-export VM[uuid: %s] recovery point at groupId %d",
                                msg.getVmInstanceUuid(), msg.getGroupId()
                        ));
                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            final String __name__ = "update-volume-image-uuid-after-recovery";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                new SQLBatch() {
                    @Override
                    @ExceptionSafe
                    protected void scripts() {
                        for (String volumeUuid : ctx.getExistingVolumesToReuse()) {
                            String imageUuid = getVolumeImageUuidFromMetadata(volumeUuid, volumeTargetMap.get(volumeUuid));
                            if (imageUuid == null) {
                                continue;
                            }

                            // We have vm.imageUuid and volume.rootImageUuid
                            if (!q(ImageVO.class).eq(ImageVO_.uuid, imageUuid).isExists()) {
                                continue;
                            }

                            sql(VolumeVO.class).eq(VolumeVO_.uuid, volumeUuid)
                                    .set(VolumeVO_.rootImageUuid, imageUuid)
                                    .update();

                            if (ctx.getExistingRootVolumeUuid().equals(volumeUuid)) {
                                sql(VmInstanceVO.class).eq(VmInstanceVO_.uuid, msg.getVmInstanceUuid())
                                        .set(VmInstanceVO_.imageUuid, imageUuid)
                                        .update();
                            }
                        }
                    }
                }.execute();

                trigger.next();
            }
        }).ctxHandler(new LongJobFlowContextHandler(msg.getLongJobUuid()) {
            @Override
            public void saveContext(Flow toRun) {
                doSaveContext();
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                logger.warn(String.format("longjob[uuid: %s] failed with context: %s", msg.getLongJobUuid(),
                        JSONObjectUtil.toJsonString(msg.getJobContext())));
                completion.fail(errCode);
            }
        }).start();
    }
}
