package org.zstack.testlib.premium.vmware

import org.zstack.core.Platform
import org.zstack.sdk.VCenterClusterInventory
import org.zstack.testlib.*
import org.zstack.vmware.ESXConstant

class VCenterClusterSpec extends ClusterSpec {
    VCenterClusterSpec(EnvSpec envSpec) {
        super(envSpec)
    }

    @SpecParam(required = true)
    String name
    @SpecParam
    String description
    @SpecParam
    String hypervisorType = ESXConstant.VMWARE_HYPERVISOR_TYPE
    @SpecParam
    String architecture = "x86_64"
    public List<ESXHostSpec> eSXHosts = []
    public List<VCenterResourcePoolSpec> resourcePools = []

    List<String> primaryStorageToSync = []
    List<String> l2NetworkToSync = []

    VCenterClusterInventory inventory

    SpecID specID

    ESXHostSpec esx(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = ESXHostSpec.class) Closure c) {
        def hspec = new ESXHostSpec(envSpec)
        c.delegate = hspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(hspec)
        eSXHosts.add(hspec)
        return hspec
    }

    VCenterResourcePoolSpec resourcePool(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = VCenterResourcePoolSpec.class) Closure c) {
        def rp = new VCenterResourcePoolSpec(envSpec)
        c.delegate = rp
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(rp)
        resourcePools.add(rp)
        return rp
    }

    @SpecMethod
    void attachPrimaryStorage(String... names) {
        names.each { String primaryStorageName ->
            primaryStorageToSync.add(primaryStorageName)
        }
    }

    @SpecMethod
    void attachL2Network(String ...names) {
        names.each { String l2NetworkName ->
            l2NetworkToSync.add(l2NetworkName)
        }
    }

    SpecID create(String uuid, String sessionId) {
        specID = id(name, Platform.getUuid())
    }

    @Override
    void delete(String sessionId) {
        specID = null
    }

}
