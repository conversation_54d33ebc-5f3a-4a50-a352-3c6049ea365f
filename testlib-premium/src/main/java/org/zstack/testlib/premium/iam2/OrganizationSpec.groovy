package org.zstack.testlib.premium.iam2

import org.zstack.sdk.iam2.entity.IAM2OrganizationInventory
import org.zstack.sdk.iam2.entity.OrganizationType
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.HasSession
import org.zstack.testlib.Spec
import org.zstack.testlib.SpecID
import org.zstack.testlib.SpecParam

class OrganizationSpec extends Spec implements HasSession, HasAttribute {
    @SpecParam
    String name
    @SpecParam
    String description
    @SpecParam
    OrganizationType type

    IAM2OrganizationInventory inventory

    OrganizationSpec(EnvSpec envSpec) {
        super(envSpec)
    }

    void organization(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = OrganizationSpec.class) Closure c) {
        OrganizationSpec spec = new OrganizationSpec()
        c.delegate = spec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(spec)
    }

    void id(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = VirtualIDSpec.class) Closure c) {
        VirtualIDSpec spec = new VirtualIDSpec(envSpec)
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c.delegate = spec
        c()
        addChild(spec)
    }

    @Override
    SpecID create(String uuid, String sessionId) {
        inventory = createIAM2Organization {
            delegate.resourceUuid = uuid
            delegate.sessionId = sessionId
            delegate.name = name
            delegate.type = parent instanceof OrganizationSpec ? OrganizationType.Department : OrganizationType.Company
            delegate.description = description
        }

        if (parent instanceof OrganizationSpec) {
            postCreate {
                changeIAM2OrganizationParent {
                    parentUuid = (parent as OrganizationSpec).inventory.uuid
                    childrenUuids = [inventory.uuid]
                }
            }
        }

        postCreate {
            if (!attributes.isEmpty()) {
                addAttributesToOrganization {
                    delegate.uuid = inventory.uuid
                    delegate.attributes = attributes
                }
            }
        }

        return id(name, uuid)
    }

    @Override
    void delete(String sessionId) {
        if (inventory != null) {
            deleteIAM2Organization {
                delegate.uuid = inventory.uuid
                delegate.sessionId = sessionId
            }
        }
    }
}
