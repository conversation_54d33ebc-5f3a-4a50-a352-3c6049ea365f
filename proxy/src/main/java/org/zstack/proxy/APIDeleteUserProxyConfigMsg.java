package org.zstack.proxy;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

@RestRequest(
        path = "/user-proxy-configs/{uuid}",
        method = HttpMethod.DELETE,
        responseClass = APIDeleteUserProxyConfigEvent.class
)
public class APIDeleteUserProxyConfigMsg extends APIMessage {
    @APIParam(resourceType = UserProxyConfigVO.class, checkAccount = true, operationTarget = true, successIfResourceNotExisting = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public static APIDeleteUserProxyConfigMsg __example__() {
        APIDeleteUserProxyConfigMsg msg = new APIDeleteUserProxyConfigMsg();
        msg.setUuid(uuid());
        return msg;
    }
}
