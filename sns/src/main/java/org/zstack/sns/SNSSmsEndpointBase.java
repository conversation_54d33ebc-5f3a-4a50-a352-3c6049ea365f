package org.zstack.sns;

import org.zstack.core.Platform;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SQLBatch;
import org.zstack.header.message.APIMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> Le on 2019-07-10
 */
public class SNSSmsEndpointBase extends SNSApplicationEndpointBase implements SNSSmsEndpoint {

    public SNSSmsEndpointBase() {
    }

    public SNSSmsEndpointBase(SNSAliyunSmsEndpointVO self) {
        this.self = self;
    }

    protected void deleteHook() {
        new SQLBatch() {
            @Override
            protected void scripts() {
                sql(SNSSmsReceiverVO.class).eq(SNSSmsReceiverVO_.endpointUuid, self.getUuid()).hardDelete();
            }
        }.execute();
    }

    protected SNSAliyunSmsEndpointVO getSelf() {
        return (SNSAliyunSmsEndpointVO) self;
    }

    @Override
    protected void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIAddSNSSmsReceiverMsg) {
            handle((APIAddSNSSmsReceiverMsg) msg);
        } else if (msg instanceof APIRemoveSNSSmsReceiverMsg) {
            handle((APIRemoveSNSSmsReceiverMsg) msg);
        } else {
            super.handleApiMessage(msg);
        }
    }

    private void handle(APIRemoveSNSSmsReceiverMsg msg) {
        if (msg.getPhoneNumberList() != null && !msg.getPhoneNumberList().isEmpty()) {
            msg.getPhoneNumberList().forEach(phoneNumber -> SQL.New(SNSSmsReceiverVO.class)
                    .eq(SNSSmsReceiverVO_.endpointUuid, msg.getEndpointUuid())
                    .eq(SNSSmsReceiverVO_.phoneNumber, phoneNumber).hardDelete());
        }
        APIRemoveSNSSmsReceiverEvent event = new APIRemoveSNSSmsReceiverEvent(msg.getId());
        bus.publish(event);
    }

    private void handle(APIAddSNSSmsReceiverMsg msg) {
        List<SNSSmsReceiverVO> voList = new ArrayList<>();
        msg.getPhoneNumberList().forEach(phoneNumber -> {
            SNSSmsReceiverVO vo = new SNSSmsReceiverVO();
            vo.setUuid(msg.getResourceUuid() == null ? Platform.getUuid() : msg.getResourceUuid());
            vo.setPhoneNumber(phoneNumber);
            vo.setEndpointUuid(msg.getEndpointUuid());
            vo.setType(SmsReceiverType.valueOf(msg.getType()));
            vo.setDescription(msg.getDescription());
            voList.add(vo);
        });
        dbf.persistCollection(voList);
        APIAddSNSSmsReceiverEvent event = new APIAddSNSSmsReceiverEvent(msg.getId());
        event.setInventories(SNSSmsReceiverInventory.valueOf(voList));
        bus.publish(event);
    }
}
