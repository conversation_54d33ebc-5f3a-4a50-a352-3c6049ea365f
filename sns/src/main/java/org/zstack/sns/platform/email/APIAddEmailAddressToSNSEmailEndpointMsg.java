package org.zstack.sns.platform.email;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.sns.SNSApplicationEndpointMessage;
import org.zstack.sns.SNSConstants;

@Action(category = SNSConstants.ACTION_CATEGORY)
@RestRequest(
        path = "/sns/application-endpoints/emails/email-addresses",
        method = HttpMethod.POST,
        responseClass = APIAddEmailAddressToSNSEmailEndpointEvent.class,
        parameterName = "params"
)
public class APIAddEmailAddressToSNSEmailEndpointMsg extends APICreateMessage implements SNSApplicationEndpointMessage {
    @APIParam(maxLength = 1024)
    private String emailAddress;
    @APIParam(resourceType = SNSEmailEndpointVO.class, checkAccount = true, operationTarget = true)
    private String endpointUuid;

    public static APIAddEmailAddressToSNSEmailEndpointMsg __example__() {
        APIAddEmailAddressToSNSEmailEndpointMsg msg = new APIAddEmailAddressToSNSEmailEndpointMsg();
        msg.setEmailAddress("<EMAIL>");
        msg.setEndpointUuid(uuid());
        return msg;
    }

    public String getEndpointUuid() {
        return endpointUuid;
    }

    public void setEndpointUuid(String endpointUuid) {
        this.endpointUuid = endpointUuid;
    }

    @Override
    public String getApplicationEndpointUuid() {
        return endpointUuid;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }
}
