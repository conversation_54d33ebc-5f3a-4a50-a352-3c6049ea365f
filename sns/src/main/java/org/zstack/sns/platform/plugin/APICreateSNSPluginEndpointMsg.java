package org.zstack.sns.platform.plugin;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.rest.RestRequest;
import org.zstack.sns.APICreateSNSApplicationEndpointMsg;
import org.zstack.sns.SNSApplicationPlatformMessage;
import org.zstack.sns.SNSConstants;

import java.util.Map;

@RestRequest(path = "/sns/application-endpoints/plugin",
        method = HttpMethod.POST,
        responseClass = APICreateSNSPluginEndpointEvent.class,
        parameterName = "params")
@Action(category = SNSConstants.ACTION_CATEGORY)
public class APICreateSNSPluginEndpointMsg extends APICreateSNSApplicationEndpointMsg implements SNSApplicationPlatformMessage {
    @APIParam(maxLength = 64)
    private String type;
    @APIParam
    private long timeoutInSeconds;
    @APIParam(required = false)
    private Map<String, String> properties;

    @APINoSee
    private String pluginUuid;

    public static APICreateSNSPluginEndpointMsg __example__() {
        APICreateSNSPluginEndpointMsg msg = new APICreateSNSPluginEndpointMsg();
        msg.setName("SNS plugin");
        msg.setType("demo");
        msg.setTimeoutInSeconds(10);
        return msg;
    }

    @Override
    public String getPlatformUuid() {
        return SNSConstants.SYSTEM_PLATFORM_UUID;
    }

    @Override
    public String getApplicationPlatformUuid() {
        return getPlatformUuid();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getTimeoutInSeconds() {
        return timeoutInSeconds;
    }

    public void setTimeoutInSeconds(long timeoutInSeconds) {
        this.timeoutInSeconds = timeoutInSeconds;
    }

    @Override
    public String getApplicationEndpointType() {
        return SNSPluginEndpointFactory.type.toString();
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    public String getPluginUuid() {
        return pluginUuid;
    }

    public void setPluginUuid(String pluginUuid) {
        this.pluginUuid = pluginUuid;
    }
}

