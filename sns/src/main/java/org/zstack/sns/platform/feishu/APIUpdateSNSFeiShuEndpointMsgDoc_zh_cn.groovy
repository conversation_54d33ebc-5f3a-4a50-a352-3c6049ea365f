package org.zstack.sns.platform.feishu

import org.zstack.sns.APIUpdateSNSApplicationEndpointEvent

doc {
    title "UpdateSNSFeiShuEndpoint"

    category "sns"

    desc """更新飞书endpoint"""

    rest {
        request {
			url "PUT /v1/sns/application-endpoints/feishu/{uuid}/actions"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIUpdateSNSFeiShuEndpointMsg.class

            desc """"""
            
			params {

				column {
					name "url"
					enclosedIn "updateSNSFeiShuEndpoint"
					desc "webhook url"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "atAll"
					enclosedIn "updateSNSFeiShuEndpoint"
					desc "@所有人"
					location "body"
					type "Boolean"
					optional true
					since "5.0.0"
				}
				column {
					name "secret"
					enclosedIn "updateSNSFeiShuEndpoint"
					desc "秘钥"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "uuid"
					enclosedIn "updateSNSFeiShuEndpoint"
					desc "资源的UUID，唯一标示该资源"
					location "url"
					type "String"
					optional false
					since "5.0.0"
				}
				column {
					name "name"
					enclosedIn "updateSNSFeiShuEndpoint"
					desc "资源名称"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "description"
					enclosedIn "updateSNSFeiShuEndpoint"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "5.0.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.0.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.0.0"
				}
				column {
					name "platformUuid"
					enclosedIn "updateSNSFeiShuEndpoint"
					desc "平台uuid"
					location "body"
					type "String"
					optional true
					since "5.1.8"
				}
			}
        }

        response {
            clz APIUpdateSNSApplicationEndpointEvent.class
        }
    }
}