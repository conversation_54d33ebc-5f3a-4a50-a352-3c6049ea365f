package org.zstack.sns.platform.plugin;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.abstraction.sns.PluginEndpointData;
import org.zstack.abstraction.sns.EndpointDriver;
import org.zstack.core.plugin.PluginManager;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.header.core.Completion;
import org.zstack.header.rest.RESTFacade;
import org.zstack.sns.MessageStruct;
import org.zstack.sns.SNSApplicationEndpointBase;
import org.zstack.sns.SNSApplicationEndpointVO;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import static org.zstack.core.Platform.operr;

public class SNSPluginEndpoint extends SNSApplicationEndpointBase {
    private static final CLogger logger = Utils.getLogger(SNSPluginEndpoint.class);

    @Autowired
    private ThreadFacade thdf;
    @Autowired
    private RESTFacade restf;
    @Autowired
    private PluginManager pluginManager;

    protected SNSPluginEndpointVO getSelf() {
        return (SNSPluginEndpointVO) self;
    }

    public SNSPluginEndpoint() {
    }

    public SNSPluginEndpoint(SNSApplicationEndpointVO self) {
        super(self);
    }

    @Override
    public void publish(MessageStruct message, Completion completion) {
        if (message.getMessage() == null) {
            logger.debug("SNS message field not defined for Plugin");
            completion.success();
            return;
        }

        if (!pluginManager.isFeatureSupported(
                getSelf().getPluginDriverUuid(),
                SNSPluginEndpointCapabilities.SEND_SNS_MESSAGE.name())) {
            completion.fail(operr("unsupported SEND_SNS_MESSAGE capability"));
            return;
        }

        EndpointDriver plugin = pluginManager.getPlugin(getSelf().getPluginDriverUuid());
        logger.debug("find plugin of SNSPluginEndpoint");
        logger.debug("plugin version: " + plugin.version());
        logger.debug("plugin capabilities: \n" + JSONObjectUtil.toJsonString(plugin.features()));

        PluginEndpointData data = createPluginEndpointData(message);
        // TODO: use a separate thread pool
        EndpointSendTask task = new EndpointSendTask(TimeUnit.SECONDS, 0) {
            @Override
            protected void execute() {
                if (!plugin.send(data)) {
                    logger.debug(String.format("failed to send sns message on plugin %s",
                            getSelf().getPluginDriverUuid()));
                    completion.fail(operr("failed to send sns message on plugin %s",
                            getSelf().getPluginDriverUuid()));
                } else {
                    logger.debug(String.format("send sns message on plugin %s success",
                            getSelf().getPluginDriverUuid()));
                    completion.success();
                }
            }
        };
        task.start();
    }

    private PluginEndpointData createPluginEndpointData(MessageStruct message) {
        PluginEndpointData data = new PluginEndpointData();
        data.setMessage(message.getMessage());
        data.setMetadata(message.getMetadata());

        if (getSelf().getProperties() != null) {
            data.setProperties(JSONObjectUtil.toObject(getSelf().getProperties(), HashMap.class));
        }

        return data;
    }
}
