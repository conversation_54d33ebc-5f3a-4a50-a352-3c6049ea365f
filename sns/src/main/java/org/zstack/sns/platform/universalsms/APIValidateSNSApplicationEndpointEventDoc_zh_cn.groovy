package org.zstack.sns.platform.universalsms

import org.zstack.header.errorcode.ErrorCode

doc {

	title "验证短信通知对象返回"

	field {
		name "success"
		desc ""
		type "boolean"
		since "5.1.0"
	}
	ref {
		name "error"
		path "org.zstack.sns.platform.universalsms.APIValidateSNSApplicationEndpointEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.1.0"
		clz ErrorCode.class
	}
}
