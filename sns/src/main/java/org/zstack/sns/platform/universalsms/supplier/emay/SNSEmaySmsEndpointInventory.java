package org.zstack.sns.platform.universalsms.supplier.emay;

import org.zstack.core.Platform;
import org.zstack.header.query.ExpandedQueries;
import org.zstack.header.query.ExpandedQuery;
import org.zstack.header.query.ExpandedQueryAlias;
import org.zstack.header.query.ExpandedQueryAliases;
import org.zstack.header.search.Inventory;
import org.zstack.sns.*;
import org.zstack.sns.platform.universalsms.SNSUniversalSmsEndpointInventory;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by boce.wang on 05/07/2024.
 */
@Inventory(mappingVOClass = SNSEmaySmsEndpointVO.class)
@ExpandedQueries({
        @ExpandedQuery(expandedField = "platform", inventoryClass = SNSApplicationPlatformInventory.class,
                foreignKey = "platformUuid", expandedInventoryKey = "uuid"),
        @ExpandedQuery(expandedField = "topicRef", inventoryClass = SNSSubscriberInventory.class,
                foreignKey = "uuid", expandedInventoryKey = "endpointUuid", hidden = true)
})
@ExpandedQueryAliases({
        @ExpandedQueryAlias(alias = "topics", expandedField = "topicRef.topics")
})
public class SNSEmaySmsEndpointInventory extends SNSUniversalSmsEndpointInventory {
    private String requestUrl;

    public SNSEmaySmsEndpointInventory() {
    }

    public SNSEmaySmsEndpointInventory(SNSEmaySmsEndpointVO vo) {
        super(vo);
        this.requestUrl = vo.getRequestUrl();
    }

    public SNSEmaySmsEndpointInventory(SNSUniversalSmsEndpointInventory other) {
        super(other);
    }

    public static SNSEmaySmsEndpointInventory __example__() {
        SNSEmaySmsEndpointInventory inventory = new SNSEmaySmsEndpointInventory();
        inventory.setName("SNSEmaySmsEndpointInv");
        inventory.setUuid(Platform.getUuid());
        inventory.setDescription("example SNSEmaySmsEndpointInv Description");
        inventory.setType("UniversalSms");
        inventory.setSmsAccessKeyId("xxxxx");
        inventory.setSmsAccessKeySecret("xxxxx");
        inventory.setSupplier("Emay");
        inventory.setRequestUrl("http://test.com:888/request_url");
        inventory.setPlatformUuid(Platform.getUuid());
        inventory.setCreateDate(new Timestamp(System.currentTimeMillis()));
        inventory.setLastOpDate(inventory.getCreateDate());
        inventory.setState(SNSApplicationEndpointState.Enabled.toString());
        return inventory;
    }

    public static SNSEmaySmsEndpointInventory valueOf(SNSEmaySmsEndpointVO vo) {
        return new SNSEmaySmsEndpointInventory(vo);
    }

    public static List<SNSEmaySmsEndpointInventory> valueOf2(Collection<SNSEmaySmsEndpointVO> vos) {
        return vos.stream().map(SNSEmaySmsEndpointInventory::valueOf).collect(Collectors.toList());
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }
}
