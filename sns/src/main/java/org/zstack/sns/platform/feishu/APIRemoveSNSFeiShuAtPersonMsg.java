package org.zstack.sns.platform.feishu;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.sns.SNSApplicationEndpointMessage;
import org.zstack.sns.SNSConstants;

@Action(category = SNSConstants.ACTION_CATEGORY)
@RestRequest(path = "/sns/application-endpoints/feishu/{endpointUuid}/at-persons/{userId}",
        method = HttpMethod.DELETE,
        responseClass = APIRemoveSNSFeiShuAtPersonEvent.class)
public class APIRemoveSNSFeiShuAtPersonMsg extends APIDeleteMessage implements SNSApplicationEndpointMessage {
    @APIParam(resourceType = SNSFeiShuEndpointVO.class, checkAccount = true, operationTarget = true, successIfResourceNotExisting = true)
    private String endpointUuid;
    @APIParam
    private String userId;

    public static APIRemoveSNSFeiShuAtPersonMsg __example__() {
        APIRemoveSNSFeiShuAtPersonMsg msg = new APIRemoveSNSFeiShuAtPersonMsg();
        msg.endpointUuid = uuid();
        msg.userId = "***********";
        return msg;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getEndpointUuid() {
        return endpointUuid;
    }

    public void setEndpointUuid(String endpointUuid) {
        this.endpointUuid = endpointUuid;
    }

    @Override
    public String getApplicationEndpointUuid() {
        return endpointUuid;
    }
}
