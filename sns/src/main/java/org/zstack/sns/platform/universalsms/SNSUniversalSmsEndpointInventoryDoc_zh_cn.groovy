package org.zstack.sns.platform.universalsms

import java.sql.Timestamp
import org.zstack.sns.SNSApplicationPlatformInventory

doc {

	title "通用短信通知对象清单"

	field {
		name "smsAccessKeyId"
		desc "访问密钥ID"
		type "String"
		since "5.1.0"
	}
	field {
		name "smsAccessKeySecret"
		desc "访问密钥密码"
		type "String"
		since "5.1.0"
	}
	field {
		name "supplier"
		desc "短信提供商"
		type "String"
		since "5.1.0"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "5.1.0"
	}
	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "5.1.0"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "5.1.0"
	}
	field {
		name "state"
		desc ""
		type "String"
		since "5.1.0"
	}
	field {
		name "platformUuid"
		desc ""
		type "String"
		since "5.1.0"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "5.1.0"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "5.1.0"
	}
	ref {
		name "platform"
		path "org.zstack.sns.platform.universalsms.SNSUniversalSmsEndpointInventory.platform"
		desc "null"
		type "SNSApplicationPlatformInventory"
		since "5.1.0"
		clz SNSApplicationPlatformInventory.class
	}
}
