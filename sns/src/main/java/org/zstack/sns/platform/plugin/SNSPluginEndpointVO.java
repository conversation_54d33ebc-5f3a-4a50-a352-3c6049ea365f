package org.zstack.sns.platform.plugin;

import org.zstack.header.core.external.plugin.PluginDriverVO;
import org.zstack.header.vo.ForeignKey;
import org.zstack.sns.SNSApplicationEndpointVO;
import org.zstack.sns.SNSApplicationPlatformVO;

import javax.persistence.*;

@Table
@Entity
public class SNSPluginEndpointVO extends SNSApplicationEndpointVO {
    @Column
    private Long timeoutInSeconds;

    @Column
    private String properties;

    @Column
    @ForeignKey(parentEntityClass = PluginDriverVO.class,
            parentKey = "uuid",
            onDeleteAction = ForeignKey.ReferenceOption.SET_NULL)
    private String pluginDriverUuid;

    @ManyToOne
    @JoinColumn(name = "pluginDriverUuid", insertable = false, updatable = false)
    private PluginDriverVO driver;

    public SNSPluginEndpointVO() {
    }

    public SNSPluginEndpointVO(SNSApplicationEndpointVO other) {
        super(other);
    }

    public String getProperties() {
        return properties;
    }

    public void setProperties(String properties) {
        this.properties = properties;
    }

    public String getPluginDriverUuid() {
        return pluginDriverUuid;
    }

    public void setPluginDriverUuid(String pluginDriverUuid) {
        this.pluginDriverUuid = pluginDriverUuid;
    }

    public Long getTimeoutInSeconds() {
        return timeoutInSeconds;
    }

    public void setTimeoutInSeconds(Long timeoutInSeconds) {
        this.timeoutInSeconds = timeoutInSeconds;
    }

    public PluginDriverVO getDriver() {
        return driver;
    }

    public void setDriver(PluginDriverVO driver) {
        this.driver = driver;
    }
}
