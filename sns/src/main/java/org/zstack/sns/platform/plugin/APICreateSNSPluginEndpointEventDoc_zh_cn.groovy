package org.zstack.sns.platform.plugin

import org.zstack.sns.platform.plugin.SNSPluginEndpointInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "创建SNS插件端点事件"

	ref {
		name "inventory"
		path "org.zstack.sns.platform.plugin.APICreateSNSPluginEndpointEvent.inventory"
		desc "null"
		type "SNSPluginEndpointInventory"
		since "5.3.0"
		clz SNSPluginEndpointInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "5.3.0"
	}
	ref {
		name "error"
		path "org.zstack.sns.platform.plugin.APICreateSNSPluginEndpointEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.3.0"
		clz ErrorCode.class
	}
}
