package org.zstack.sns.platform.universalsms;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;

import java.util.List;
@RestResponse(allTo = "inventories")
public class APIQuerySNSUniversalSmsEndpointReply extends APIQueryReply {
    private List<SNSUniversalSmsEndpointInventory> inventories;

    public List<SNSUniversalSmsEndpointInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<SNSUniversalSmsEndpointInventory> inventories) {
        this.inventories = inventories;
    }
}
