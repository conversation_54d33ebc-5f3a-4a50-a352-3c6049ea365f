package org.zstack.sns.platform.universalsms.supplier.emay;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.zstack.core.db.Q;
import org.zstack.core.retry.Retry;
import org.zstack.core.retry.RetryCondition;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.rest.RESTFacade;
import org.zstack.sns.platform.universalsms.APICreateSNSUniversalSmsEndpointMsg;
import org.zstack.sns.platform.universalsms.SNSUniversalSmsEndpointVO;
import org.zstack.sns.platform.universalsms.UniversalSmsMessage;
import org.zstack.sns.platform.universalsms.supplier.SNSUniversalSmsSupplierConstant;
import org.zstack.sns.platform.universalsms.supplier.SNSUniversalSmsUtil;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

import static org.zstack.core.Platform.argerr;


/**
 * Created by boce.wang on 04/24/2024.
 */
public class SNSEmaySmsUtil implements SNSUniversalSmsUtil {

    private static final CLogger logger = Utils.getLogger(SNSUniversalSmsUtil.class);

    public SNSEmaySmsUtil() {
    }

    @Override
    public SNSUniversalSmsEndpointVO createEndpoint(SNSUniversalSmsEndpointVO vo, APICreateSNSUniversalSmsEndpointMsg msg) {
        if (!validCreateEndpoint(msg)) {
            throw new ApiMessageInterceptionException(argerr("invalid param with supplier[%s]", msg.getSupplier()));
        }
        SNSEmaySmsEndpointVO endpoint = new SNSEmaySmsEndpointVO(vo);
        endpoint.setRequestUrl(msg.getAdditionParam().get(SNSEmaySmsConstant.REQUEST_URL));
        return endpoint;
    }

    private boolean validCreateEndpoint(APICreateSNSUniversalSmsEndpointMsg msg) {
        if (!SNSUniversalSmsSupplierConstant.Supplier.Emay.toString().equals(msg.getSupplier())) {
            return false;
        }
        if (msg.getAdditionParam() == null || !msg.getAdditionParam().containsKey(SNSEmaySmsConstant.REQUEST_URL)) {
            return false;
        }
        return true;
    }

    public String sendSms(RESTFacade restf, SNSEmaySmsEndpointVO vo, String content, List<String> mobiles) {
        String appId = vo.getSmsAccessKeyId();
        String secretKey = vo.getSmsAccessKeySecret();
        String host = vo.getRequestUrl();
        try {
            LocalDateTime now = LocalDateTime.now();
            String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

            String sign = DigestUtils.md5Hex((appId + secretKey + timestamp).getBytes());
            String url = host + SNSEmaySmsConstant.SMS_SEND_ENDPOINT;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setAcceptCharset(Collections.emptyList());
            headers.setAccept(Collections.singletonList(MediaType.ALL));
            headers.set(HttpHeaders.ACCEPT_ENCODING, "gzip");
            MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
            paramMap.add("appId", appId);
            paramMap.add("sign", sign);
            paramMap.add("timestamp", timestamp);
            mobiles.forEach(mobile -> paramMap.add(mobile, content));
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(paramMap, headers);
            ResponseEntity<String> response = sendRequestWithRetry(restf, url, requestEntity);

            return response != null ? response.getBody() : null;
        } catch (Exception e) {
            logger.error("send sms failed", e);
            return null;
        }
    }

    private ResponseEntity<String> sendRequestWithRetry(RESTFacade restf, String url, HttpEntity<MultiValueMap<String, String>> requestEntity) {
        return new Retry<ResponseEntity<String>>() {
            @Override
            @RetryCondition(onExceptions = {IOException.class, HttpStatusCodeException.class}, times = 3, interval = 2)
            protected ResponseEntity<String> call() {
                return restf.getRESTTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
            }
        }.run();
    }

    @Override
    public String getSupplier() {
        return SNSEmaySmsConstant.EMAY_TYPE;
    }

    @Override
    public Integer sendSmsWithEndpoint(RESTFacade restf, SNSUniversalSmsEndpointVO vo, UniversalSmsMessage message, List<String> phones) {
        SNSEmaySmsEndpointVO emayVo = Q.New(SNSEmaySmsEndpointVO.class).eq(SNSEmaySmsEndpointVO_.uuid, vo.getUuid()).find();
        if (emayVo == null) {
            return 0;
        }
        int maxBatchSize = 100;
        int totalSuccessCount = 0;
        for (int start = 0; start < phones.size(); start += maxBatchSize) {
            int end = Math.min(start + maxBatchSize, phones.size());
            List<String> phoneBatch = phones.subList(start, end);
            String response = sendSms(restf, emayVo, message.getMessage(), phoneBatch);
            if (response != null && response.contains(SNSEmaySmsConstant.EMAY_RESULT)) {
                totalSuccessCount += phoneBatch.size();
            }
        }
        return totalSuccessCount;
    }
}
