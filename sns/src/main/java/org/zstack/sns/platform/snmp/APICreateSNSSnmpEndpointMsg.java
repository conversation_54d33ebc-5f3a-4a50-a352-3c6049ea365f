package org.zstack.sns.platform.snmp;

import org.springframework.http.HttpMethod;
import org.zstack.core.Platform;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.sns.APICreateSNSApplicationEndpointEvent;
import org.zstack.sns.APICreateSNSApplicationEndpointMsg;
import org.zstack.sns.SNSApplicationPlatformMessage;
import org.zstack.sns.SNSConstants;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> jingwang
 * @create 2023/7/13 7:13 PM
 */
@RestRequest(
        path = "/sns/application-endpoints/snmp",
        method = HttpMethod.POST,
        responseClass = APICreateSNSApplicationEndpointEvent.class,
        parameterName = "params"
)
@Action(category = SNSConstants.ACTION_CATEGORY)
public class APICreateSNSSnmpEndpointMsg extends APICreateSNSApplicationEndpointMsg implements SNSApplicationPlatformMessage {
    public static APICreateSNSSnmpEndpointMsg __example__() {
        APICreateSNSSnmpEndpointMsg msg = new APICreateSNSSnmpEndpointMsg();
        msg.setName("snmp endpoint");
        msg.setPlatformUuid(uuid());
        return msg;
    }
}
