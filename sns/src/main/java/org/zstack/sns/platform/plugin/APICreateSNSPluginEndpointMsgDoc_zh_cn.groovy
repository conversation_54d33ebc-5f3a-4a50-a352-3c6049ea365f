package org.zstack.sns.platform.plugin

import org.zstack.sns.platform.plugin.APICreateSNSPluginEndpointEvent

doc {
    title "CreateSNSPluginEndpoint"

    category "sns"

    desc """创建SNS插件端点请求"""

    rest {
        request {
			url "POST /v1/sns/application-endpoints/plugin"

			header (Authorization: 'OAuth the-session-uuid')

            clz APICreateSNSPluginEndpointMsg.class

            desc """"""
            
			params {

				column {
					name "type"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional false
					since "5.3.0"
				}
				column {
					name "timeoutInSeconds"
					enclosedIn "params"
					desc ""
					location "body"
					type "long"
					optional false
					since "5.3.0"
				}
				column {
					name "properties"
					enclosedIn "params"
					desc ""
					location "body"
					type "Map"
					optional true
					since "5.3.0"
				}
				column {
					name "name"
					enclosedIn "params"
					desc "资源名称"
					location "body"
					type "String"
					optional false
					since "5.3.0"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "platformUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc "资源UUID"
					location "body"
					type "String"
					optional true
					since "5.3.0"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.3.0"
				}
			}
        }

        response {
            clz APICreateSNSPluginEndpointEvent.class
        }
    }
}