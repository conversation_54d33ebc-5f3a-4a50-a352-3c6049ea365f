package org.zstack.sns.platform.plugin;

import org.zstack.sns.SNSApplicationEndpointVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;

@StaticMetamodel(SNSPluginEndpointVO.class)
public class SNSPluginEndpointVO_ extends SNSApplicationEndpointVO_ {
    public static volatile SingularAttribute<SNSPluginEndpointVO, String> pluginDriverType;
    public static volatile SingularAttribute<SNSPluginEndpointVO, Long> timeoutInSeconds;
}
