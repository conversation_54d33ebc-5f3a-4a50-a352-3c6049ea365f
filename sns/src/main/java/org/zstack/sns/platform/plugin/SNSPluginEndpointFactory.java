package org.zstack.sns.platform.plugin;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.abstraction.sns.EndpointDriver;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.plugin.PluginManager;
import org.zstack.header.Component;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.sns.*;
import org.zstack.sns.platform.feishu.SNSFeiShuEndpointVO;
import org.zstack.utils.DomainUtils;
import org.zstack.utils.gson.JSONObjectUtil;

import java.util.List;

public class SNSPluginEndpointFactory implements SNSApplicationEndpointFactory, Component {
    public static final SNSApplicationEndpointType type = new SNSApplicationEndpointType("Plugin");

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private PluginManager pluginManager;

    @Override
    public SNSPluginEndpointVO createApplicationEndpoint(SNSApplicationEndpointVO vo,
                                                         APICreateSNSApplicationEndpointMsg msg) {
        SNSPluginEndpointVO dvo = new SNSPluginEndpointVO(vo);
        APICreateSNSPluginEndpointMsg apiMsg = (APICreateSNSPluginEndpointMsg) msg;
        EndpointDriver driver = pluginManager.getPlugin(apiMsg.getPluginUuid());
        dvo.setTimeoutInSeconds(apiMsg.getTimeoutInSeconds());
        dvo.setProperties(JSONObjectUtil.toJsonString(apiMsg.getProperties()));
        dvo.setPluginDriverUuid(driver.uuid());
        return dvo;
    }

    @Override
    public String getApplicationEndpointType() {
        return type.toString();
    }

    @Override
    public SNSApplicationEndpointInventory getSNSApplicationEndpointInventory(SNSApplicationEndpointVO vo) {
        return SNSPluginEndpointInventory.valueOf((SNSPluginEndpointVO) vo);
    }

    @Override
    public SNSApplicationEndpoint getSNSApplicationEndpoint(String uuid) {
        return new SNSPluginEndpoint(dbf.findByUuid(uuid, SNSPluginEndpointVO.class));
    }

    @Override
    public SNSApplicationEndpoint getSNSApplicationEndpoint() {
        return new SNSPluginEndpoint();
    }

    @Override
    public List<SNSApplicationEndpoint> getSNSApplicationEndpoints(List<String> uuids) {
        throw new CloudRuntimeException("not supported");
    }

    @Override
    public void validateNetworkConnection(String uuid) {
        // TODO: introduce connection check for sns plugin
    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }
}
