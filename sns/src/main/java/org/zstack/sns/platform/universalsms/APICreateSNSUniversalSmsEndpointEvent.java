package org.zstack.sns.platform.universalsms;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

@RestResponse(allTo = "inventory")
public class APICreateSNSUniversalSmsEndpointEvent extends APIEvent {
    private SNSUniversalSmsEndpointInventory inventory;

    public static APICreateSNSUniversalSmsEndpointEvent __example__() {
        APICreateSNSUniversalSmsEndpointEvent evt = new APICreateSNSUniversalSmsEndpointEvent();
        evt.setInventory(SNSUniversalSmsEndpointInventory.__example1__());
        return evt;
    }

    public APICreateSNSUniversalSmsEndpointEvent() {
    }

    public APICreateSNSUniversalSmsEndpointEvent(String apiId) {
        super(apiId);
    }

    public SNSUniversalSmsEndpointInventory getInventory() {
        return inventory;
    }

    public void setInventory(SNSUniversalSmsEndpointInventory inventory) {
        this.inventory = inventory;
    }
}
