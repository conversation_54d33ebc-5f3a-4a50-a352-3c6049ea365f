package org.zstack.sns.platform.universalsms

import org.zstack.sns.platform.universalsms.SNSUniversalSmsEndpointInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "短信通知对象清单"

	ref {
		name "inventory"
		path "org.zstack.sns.platform.universalsms.APICreateSNSUniversalSmsEndpointEvent.inventory"
		desc "null"
		type "SNSUniversalSmsEndpointInventory"
		since "5.1.0"
		clz SNSUniversalSmsEndpointInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "5.1.0"
	}
	ref {
		name "error"
		path "org.zstack.sns.platform.universalsms.APICreateSNSUniversalSmsEndpointEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.1.0"
		clz ErrorCode.class
	}
}
