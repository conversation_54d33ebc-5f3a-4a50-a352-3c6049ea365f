package org.zstack.sns.platform.universalsms;

public class UniversalSmsMessage {
    private String message;
    private UniversalSmsMessageMetadata metadata;

    public UniversalSmsMessage() {
    }

    public UniversalSmsMessage(String message, UniversalSmsMessageMetadata metadata) {
        this.message = message;
        this.metadata = metadata;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public UniversalSmsMessageMetadata getMetadata() {
        return metadata;
    }

    public void setMetadata(UniversalSmsMessageMetadata metadata) {
        this.metadata = metadata;
    }
}

