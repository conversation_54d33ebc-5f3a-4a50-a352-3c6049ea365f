package org.zstack.sns;

import org.zstack.header.identity.rbac.RBACDescription;

public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("sns")
                .normalAPIs("org.zstack.sns.**")
                .build();
    }

    @Override
    public void contributeToRoles() {
        roleContributorBuilder()
                .roleName("zwatch")
                .actionsByPermissionName("sns")
                .build();
    }

    @Override
    public void roles() {
        roleBuilder()
                .name("sns")
                .uuid("001adb2ef25e41b7bd01b28651fcfa6a")
                .permissionsByName("sns")
                .build();
    }

    @Override
    public void globalReadableResources() {

    }
}
