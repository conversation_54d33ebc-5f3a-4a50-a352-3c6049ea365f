package org.zstack.appcenter.buildsystem;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.buildapp.*;
import org.zstack.header.buildsystem.APIAddAppBuildSystemMsg;
import org.zstack.header.buildsystem.AppBuildStorageType;
import org.zstack.header.buildsystem.AppBuildSystemVO;
import org.zstack.header.buildsystem.AppBuildSystemVO_;
import org.zstack.header.message.APIMessage;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageVO;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageVO_;
import org.zstack.utils.CollectionDSL;

import java.util.List;

import static org.zstack.core.Platform.argerr;

/**
 * Created by mingjian.deng on 2019/5/15.
 */
public class AppBuildSystemInterceptor implements ApiMessageInterceptor {
    @Autowired
    private DatabaseFacade dbf;

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIAddAppBuildSystemMsg) {
            validate((APIAddAppBuildSystemMsg) msg);
        } else if (msg instanceof APICreateBuildAppMsg) {
            validate((APICreateBuildAppMsg) msg);
        } else if (msg instanceof APIExportBuildAppMsg) {
            validate((APIExportBuildAppMsg) msg);
        } else if (msg instanceof APIAddBuildAppMsg) {
            validate((APIAddBuildAppMsg) msg);
        } else if (msg instanceof APICheckBuildAppParametersMsg) {
            validate((APICheckBuildAppParametersMsg) msg);
        } else if (msg instanceof APIDeleteBuildAppExportHistoryMsg) {
            validate((APIDeleteBuildAppExportHistoryMsg) msg);
        }
        return msg;
    }

    private void validate(final APIExportBuildAppMsg msg) {
        if (Q.New(BuildAppExportHistoryVO.class).eq(BuildAppExportHistoryVO_.buildAppUuid, msg.getUuid()).
                in(BuildAppExportHistoryVO_.status, CollectionDSL.list(BuildAppExportStatus.Exported, BuildAppExportStatus.Exporting)).isExists()) {
            throw new ApiMessageInterceptionException(argerr("build-app[%s] is exported or is exporting, please delete it first", msg.getUuid()));
        }
    }

    private void validate(final APIAddAppBuildSystemMsg msg) {
        if (msg.getStorageType() == null) {
            msg.setStorageType(AppBuildStorageType.localStorage.toString());
        }
        if (msg.getSshPort() == null) {
            msg.setSshPort(22);
        }
        AppBuildSystemVO build = Q.New(AppBuildSystemVO.class).eq(AppBuildSystemVO_.url, msg.getUrl()).eq(AppBuildSystemVO_.hostname, msg.getHostname()).find();
        if (build != null) {
            throw new ApiMessageInterceptionException(argerr("another build system[uuid: %s, name: %s] in this host[%s] used the url[%s]",
                    build.getUuid(), build.getName(), build.getHostname(), build.getUrl()));
        }
    }

    private void validate(final APICreateBuildAppMsg msg) {
        if (msg.getDataPath().startsWith("file:///")) {
            msg.setDataPath(msg.getDataPath().substring("file://".length()));
        }

        if (!msg.getDataPath().startsWith("/")) {
            throw new ApiMessageInterceptionException(argerr("dataPath must start with '/', actually got [%s]",
                    msg.getDataPath()));
        }
    }

    private void validate(final APIAddBuildAppMsg msg) {
        if (msg.getRepo() == null) {
            msg.setRepo(false);
        }
        if (msg.getType() == null) {
            msg.setType(BuildAppType.zstack.toString());
        }

        if (msg.getHostname() == null && msg.getBackupStorageUuid() == null) {
            throw new ApiMessageInterceptionException(argerr("both backupStorageUuid and hostname are null"));
        }

        if (msg.getHostname() != null) {
            List<ImageStoreBackupStorageVO> vos = Q.New(ImageStoreBackupStorageVO.class).
                    eq(ImageStoreBackupStorageVO_.hostname, msg.getHostname()).list();
            if (vos.isEmpty()) {
                throw new ApiMessageInterceptionException(argerr("cannot find imageStore which hostname is :%s", msg.getHostname()));
            }
            if (vos.size() > 1) {
                throw new ApiMessageInterceptionException(argerr("find more than one imageStore which hostname is: %s, " +
                        "please use backupStorageUuid instead", msg.getHostname()));
            }
            if (msg.getBackupStorageUuid() != null && !msg.getBackupStorageUuid().equalsIgnoreCase(vos.get(0).getUuid())) {
                throw new ApiMessageInterceptionException(argerr("both backupStorageUuid and hostname are set, but they are not the same host"));
            }
            msg.setBackupStorageUuid(vos.get(0).getUuid());
        }
    }

    private void validate(final APICheckBuildAppParametersMsg msg) {
        if (msg.getType() == null) {
            msg.setType(BuildAppType.zstack.toString());
        }
    }

    private void validate(final APIDeleteBuildAppExportHistoryMsg msg) {
        if (msg.getBuildAppUuid() == null && msg.getExportId() == null) {
            throw new ApiMessageInterceptionException(argerr("buildAppUuid and exportId cannot both be null"));
        }

        if (msg.getBuildAppUuid() == null && msg.getBuildSystemUuid() == null) {
            throw new ApiMessageInterceptionException(argerr("buildAppUuid and buildSystemUuid cannot both be null"));
        }

        if (msg.getExportId() != null) {
            BuildAppExportHistoryVO export = Q.New(BuildAppExportHistoryVO.class).eq(BuildAppExportHistoryVO_.id, msg.getExportId()).find();
            if (export == null) {
                throw new ApiMessageInterceptionException(argerr("no such exportId in build export history"));
            }

            if (msg.getBuildAppUuid() != null && msg.getBuildAppUuid().equalsIgnoreCase(export.getBuildAppUuid())) {
                throw new ApiMessageInterceptionException(argerr("both exportId and buildAppUuid are set but they are not equal"));
            }
        }
    }
}
