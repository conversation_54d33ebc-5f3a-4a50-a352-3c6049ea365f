package org.zstack.appcenter.buildsystem;

import com.google.gson.JsonParser;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.appcenter.AppCenterGlobalProperty;
import org.zstack.cloudformation.template.CloudFormationDecoder;
import org.zstack.cloudformation.template.decoder.ResourceDecoder;
import org.zstack.cloudformation.template.struct.CfnResults;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.ansible.AnsibleFacade;
import org.zstack.core.ansible.AnsibleGlobalProperty;
import org.zstack.core.ansible.AnsibleRunner;
import org.zstack.core.ansible.SshFileMd5Checker;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.MessageSafe;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.header.AbstractService;
import org.zstack.header.buildapp.*;
import org.zstack.header.buildsystem.*;
import org.zstack.header.cloudformation.StackParameters;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.errorcode.SysErrors;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.storage.backup.BackupStorageState;
import org.zstack.header.storage.backup.BackupStorageStatus;
import org.zstack.identity.AccountManager;
import org.zstack.premium.externalservice.appcenter.AppCenterServerGlobalProperty;
import org.zstack.storage.backup.imagestore.ImageStoreBackupStorageVO;
import org.zstack.storage.backup.imagestore.ImageStoreExtensionPoint;
import org.zstack.tag.TagManager;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.path.PathUtil;

import java.util.*;

import static org.zstack.core.Platform.*;

/**
 * Created by mingjian.deng on 2019/5/15.
 */
public class AppBuildSystemImpl extends AbstractService implements ImageStoreExtensionPoint {
    private static final CLogger logger = Utils.getLogger(AppBuildSystemImpl.class);

    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private ThreadFacade thdf;
    @Autowired
    protected CascadeFacade casf;
    @Autowired
    private AccountManager acmgr;
    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    private AnsibleFacade asf;

    private Map<String, BuildSystemFactory> buildStorageFactories = Collections.synchronizedMap(new HashMap<>());


    @Override
    @MessageSafe
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handleApiMessage((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    public void handleApiMessage(APIMessage msg) {
        if (msg instanceof APIAddAppBuildSystemMsg) {
            handle((APIAddAppBuildSystemMsg) msg);
        } else if (msg instanceof APIAttachAppBuildSystemToZoneMsg) {
            handle((APIAttachAppBuildSystemToZoneMsg) msg);
        } else if (msg instanceof APIDetachAppBuildSystemToZoneMsg) {
            handle((APIDetachAppBuildSystemToZoneMsg) msg);
        } else if (msg instanceof APIDeleteAppBuildSystemMsg) {
            handle((APIDeleteAppBuildSystemMsg) msg);
        } else if (msg instanceof APIChangeAppBuildSystemStateMsg) {
            handle((APIChangeAppBuildSystemStateMsg) msg);
        } else if (msg instanceof APIUpdateAppBuildSystemMsg) {
            handle((APIUpdateAppBuildSystemMsg) msg);
        } else if (msg instanceof APIGetAppBuildSystemCapacityMsg) {
            handle((APIGetAppBuildSystemCapacityMsg) msg);
        } else if (msg instanceof APIReconnectAppBuildSystemMsg) {
            handle((APIReconnectAppBuildSystemMsg) msg);
        } else if (msg instanceof APIDeleteBuildAppExportHistoryMsg) {
            handle((APIDeleteBuildAppExportHistoryMsg) msg);
        } else if (msg instanceof APICreateBuildAppMsg) {
            handle((APICreateBuildAppMsg) msg);
        } else if (msg instanceof APIDeleteBuildAppMsg) {
            handle((APIDeleteBuildAppMsg) msg);
        } else if (msg instanceof APIUpdateBuildAppMsg) {
            handle((APIUpdateBuildAppMsg) msg);
        } else if (msg instanceof APIExportBuildAppMsg) {
            handle((APIExportBuildAppMsg) msg);
        } else if (msg instanceof APIAddBuildAppMsg) {
            handle((APIAddBuildAppMsg) msg);
        } else if (msg instanceof APICheckBuildAppParametersMsg) {
            handle((APICheckBuildAppParametersMsg)msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    public void handleLocalMessage(Message msg) {
        if (msg instanceof AppBuildSystemMessage || msg instanceof BuildAppMessage) {
            passThrough(msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void passThrough(Message pmsg) {
        if (pmsg instanceof AppBuildSystemMessage && ((AppBuildSystemMessage)pmsg).getBuildSystemUuid() != null) {
            AppBuildSystemMessage msg = (AppBuildSystemMessage)pmsg;
            AppBuildSystemVO vo = dbf.findByUuid(msg.getBuildSystemUuid(), AppBuildSystemVO.class);
            if (vo == null) {
                String err = String.format("Cannot find app buildsystem[uuid:%s], it may have been deleted", msg.getBuildSystemUuid());
                bus.replyErrorByMessageType(pmsg, err);
                return;
            }

            BuildSystemFactory factory = getBuildSystemFactory(vo.getStorageType());
            AppBuildSystem ss = New(()-> factory.getAppBuildSystem(vo));
            ss.handleMessage(pmsg);
        } else {
            BuildSystemFactory factory = getBuildSystemFactory(AppBuildStorageType.localStorage);
            AppBuildSystem ss = factory.getAppBuildSystem();
            ss.handleMessage(pmsg);
        }
    }

    private BuildSystemFactory getBuildSystemFactory(AppBuildStorageType type) {
        BuildSystemFactory factory = buildStorageFactories.get(type.toString());
        if (factory == null) {
            throw new CloudRuntimeException(String.format("No BuildSystemFactory for type: %s found", type));
        }
        return factory;
    }

    private void addBuildSystem(final APIAddAppBuildSystemMsg msg, final ReturnValueCompletion<AppBuildSystemInventory> completion) {
        FlowChain chain = FlowChainBuilder.newShareFlowChain();

        chain.setName("add-app-buildsystem");
        chain.then(new ShareFlow() {
            AppBuildSystemVO vo;
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "init-add-buildsystem";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        vo = new AppBuildSystemVO();
                        if (msg.getResourceUuid() != null) {
                            vo.setUuid(msg.getResourceUuid());
                        } else {
                            vo.setUuid(Platform.getUuid());
                        }

                        vo.setUrl(msg.getUrl());
                        vo.setStorageType(AppBuildStorageType.valueOf(msg.getStorageType()));
                        vo.setName(msg.getName());
                        vo.setDescription(msg.getDescription());
                        vo.setState(AppBuildSystemState.Enabled);
                        vo.setStatus(AppBuildSystemStatus.Connecting);
                        vo.setHostname(msg.getHostname());
                        vo.setSshPort(msg.getSshPort());
                        vo.setUsername(msg.getUsername());
                        vo.setPassword(msg.getPassword());

                        vo = dbf.persistAndRefresh(vo);
                        tagMgr.createTagsFromAPICreateMessage(msg, vo.getUuid(), AppBuildSystemVO.class.getSimpleName());
                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (vo != null) {
                            dbf.removeByPrimaryKey(vo.getUuid(), AppBuildSystemVO.class);
                        }
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "deploy-buildsystem";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (CoreGlobalProperty.UNIT_TEST_ON) {
                            trigger.next();
                            return;
                        }

                        SshFileMd5Checker checker = new SshFileMd5Checker();
                        checker.setTargetIp(vo.getHostname());
                        checker.setUsername(vo.getUsername());
                        checker.setPassword(vo.getPassword());
                        checker.setSshPort(vo.getSshPort());
                        checker.addSrcDestPair(SshFileMd5Checker.ZSTACKLIB_SRC_PATH, String.format("/var/lib/zstack/appbuildsystem/package/%s", AnsibleGlobalProperty.ZSTACKLIB_PACKAGE_NAME));
                        checker.addSrcDestPair(PathUtil.findFileOnClassPath(String.format("ansible/appbuild/%s", AppCenterGlobalProperty.BUILD_SYSTEM_PACKAGE_NAME), true).getAbsolutePath(),
                                String.format("/var/lib/zstack/appbuildsystem/package/%s", AppCenterGlobalProperty.BUILD_SYSTEM_PACKAGE_NAME));

                        AnsibleRunner runner = new AnsibleRunner();
                        runner.installChecker(checker);
                        runner.setPassword(vo.getPassword());
                        runner.setUsername(vo.getUsername());
                        runner.setTargetIp(vo.getHostname());
                        runner.setSshPort(vo.getSshPort());
                        runner.setAgentPort(AppCenterGlobalProperty.BUILD_SYSTEM_SERVER_PORT);
                        runner.setPlayBookName(AppCenterGlobalProperty.BUILD_SYSTEM_PLAYBOOK_NAME);
                        runner.setDeployArguments(new AppBuildSystemDeployArguments());
                        runner.run(new ReturnValueCompletion<Boolean>(trigger) {
                            @Override
                            public void success(Boolean deployed) {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "connect-buildsystem";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        ConnectAppBuildSystemMsg cmsg = new ConnectAppBuildSystemMsg();
                        cmsg.setUuid(vo.getUuid());
                        bus.makeTargetServiceIdByResourceUuid(cmsg, AppBuildSystemConstant.SERVICE_ID, cmsg.getBuildSystemUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (reply.isSuccess()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(reply.getError());
                                }
                            }
                        });
                    }
                });
                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        vo = dbf.reload(vo);
                        vo.setStatus(AppBuildSystemStatus.Connected);
                        vo = dbf.updateAndRefresh(vo);
                        completion.success(AppBuildSystemInventory.valueOf(vo));
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private void handle(final APIAddAppBuildSystemMsg msg) {
        APIAddAppBuildSystemEvent evt = new APIAddAppBuildSystemEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                addBuildSystem(msg, new ReturnValueCompletion<AppBuildSystemInventory>(chain) {
                    @Override
                    public void success(AppBuildSystemInventory inventory) {
                        evt.setInventory(inventory);
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return "add-app-buildsystem";
            }
        });
    }

    private AppBuildSystemVO getBuildSystem(String buildSystemUuid) {
        return dbf.findByUuid(buildSystemUuid, AppBuildSystemVO.class);
    }

    private void attachBuildSystem(final APIAttachAppBuildSystemToZoneMsg msg, final ReturnValueCompletion<AppBuildSystemZoneRefInventory> completion) {
        AppBuildSystemVO bvo = getBuildSystem(msg.getBuildSystemUuid());
        if (bvo == null) {
            completion.fail(operr("cannot find build system [%s]", msg.getBuildSystemUuid()));
            return;
        }

        if (Q.New(AppBuildSystemZoneRefVO.class).eq(AppBuildSystemZoneRefVO_.buildSystemUuid, msg.getBuildSystemUuid()).
                eq(AppBuildSystemZoneRefVO_.zoneUuid, msg.getZoneUuid()).isExists()) {
            completion.fail(operr("build system[uuid: %s] has been attached to zone[uuid: %s]", msg.getBuildSystemUuid(), msg.getZoneUuid()));
            return;
        }

        AppBuildSystemZoneRefVO rvo = new AppBuildSystemZoneRefVO();
        rvo.setBuildSystemUuid(msg.getBuildSystemUuid());
        rvo.setZoneUuid(msg.getZoneUuid());
        dbf.persist(rvo);

        logger.debug(String.format("successfully attached build system[uuid: %s, name: %s] to zone[uuid: %s]", bvo.getUuid(), bvo.getName(), msg.getZoneUuid()));
        completion.success(AppBuildSystemZoneRefInventory.valueOf(rvo));
    }

    private void handle(final APIAttachAppBuildSystemToZoneMsg msg) {
        APIAttachAppBuildSystemToZoneEvent evt = new APIAttachAppBuildSystemToZoneEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getBuildSystemUuid();
            }

            @Override
            public void run(SyncTaskChain chain) {
                attachBuildSystem(msg, new ReturnValueCompletion<AppBuildSystemZoneRefInventory>(chain) {
                    @Override
                    public void success(AppBuildSystemZoneRefInventory inventory) {
                        evt.setInventory(inventory);
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("attach-buildsystem-%s-to-zone-%s", msg.getBuildSystemUuid(), msg.getZoneUuid());
            }
        });
    }

    private void detachBuildSystem(final APIDetachAppBuildSystemToZoneMsg msg, Completion completion) {
        AppBuildSystemVO bvo = getBuildSystem(msg.getBuildSystemUuid());
        if (bvo == null) {
            completion.fail(operr("cannot find build system [%s]", msg.getBuildSystemUuid()));
            return;
        }

        AppBuildSystemZoneRefVO ref = Q.New(AppBuildSystemZoneRefVO.class).eq(AppBuildSystemZoneRefVO_.buildSystemUuid, msg.getBuildSystemUuid()).
                eq(AppBuildSystemZoneRefVO_.zoneUuid, msg.getZoneUuid()).find();
        if (ref == null) {
            completion.fail(operr("build system[uuid: %s] has not been attached to zone[uuid: %s]", msg.getBuildSystemUuid(), msg.getZoneUuid()));
            return;
        }

        dbf.remove(ref);
        logger.debug(String.format("successfully detached build system[uuid: %s, name: %s] from zone[uuid: %s]", bvo.getUuid(), bvo.getName(), msg.getZoneUuid()));
        completion.success();
    }

    private void handle(final APIDetachAppBuildSystemToZoneMsg msg) {
        APIDetachAppBuildSystemToZoneEvent evt = new APIDetachAppBuildSystemToZoneEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getBuildSystemUuid();
            }

            @Override
            public void run(SyncTaskChain chain) {
                detachBuildSystem(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("detach-buildsystem-%s-from-zone-%s", msg.getBuildSystemUuid(), msg.getZoneUuid());
            }
        });
    }

    private void handle(final APIDeleteAppBuildSystemMsg msg) {
        APIDeleteAppBuildSystemEvent evt = new APIDeleteAppBuildSystemEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getUuid();
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                final String issuer = AppBuildSystemVO.class.getSimpleName();
                AppBuildSystemVO vo = dbf.findByUuid(msg.getUuid(), AppBuildSystemVO.class);
                final List<AppBuildSystemInventory> ctx = AppBuildSystemInventory.valueOf(Arrays.asList(vo));

                FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
                chain.setName(String.format("delete-build-system-%s", msg.getUuid()));
                chain.then(new NoRollbackFlow() {
                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                }).then(new NoRollbackFlow() {
                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                chain.done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                        bus.publish(evt);
                        taskChain.next();
                    }
                }).error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        evt.setError(err(SysErrors.DELETE_RESOURCE_ERROR, errCode, errCode.getDetails()));
                        bus.publish(evt);
                        taskChain.next();
                    }
                }).start();
            }

            @Override
            public String getName() {
                return String.format("delete-buildsystem-%s", msg.getUuid());
            }
        });
    }

    private void handle(final APIChangeAppBuildSystemStateMsg msg) {
        APIChangeAppBuildSystemStateEvent evt = new APIChangeAppBuildSystemStateEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getUuid();
            }

            @Override
            public void run(SyncTaskChain chain) {
                AppBuildSystemVO vo = dbf.findByUuid(msg.getUuid(), AppBuildSystemVO.class);
                if (msg.getStateEvent().equals("disable")) {
                    vo.setState(AppBuildSystemState.Disabled);
                } else {
                    vo.setState(AppBuildSystemState.Enabled);
                }
                vo = dbf.updateAndRefresh(vo);
                evt.setInventory(AppBuildSystemInventory.valueOf(vo));
                bus.publish(evt);
                chain.next();
            }

            @Override
            public String getName() {
                return String.format("change-buildsystem-%s-state-to-%s", msg.getUuid(), msg.getStateEvent());
            }
        });
    }

    private void handle(final APIUpdateAppBuildSystemMsg msg) {
        APIUpdateAppBuildSystemEvent evt = new APIUpdateAppBuildSystemEvent(msg.getId());
        AppBuildSystemVO vo = dbf.findByUuid(msg.getUuid(), AppBuildSystemVO.class);
        if (msg.getDescription() != null) {
            vo.setDescription(msg.getDescription());
        }

        if (msg.getName() != null) {
            vo.setName(msg.getName());
        }

        if (msg.getPassword() != null) {
            vo.setPassword(msg.getPassword());
        }

        if (msg.getUsername() != null) {
            vo.setUsername(msg.getUsername());
        }

        if (msg.getSshPort() != null) {
            vo.setSshPort(msg.getSshPort());
        }

        vo = dbf.updateAndRefresh(vo);
        evt.setInventory(AppBuildSystemInventory.valueOf(vo));

        bus.publish(evt);
    }

    private void handle(final APIGetAppBuildSystemCapacityMsg msg) {
        APIGetAppBuildSystemCapacityReply reply = new APIGetAppBuildSystemCapacityReply();
        AppBuildSystemVO vo = dbf.findByUuid(msg.getUuid(), AppBuildSystemVO.class);
        reply.setAvailableCapacity(vo.getAvailableCapacity());
        reply.setTotalCapacity(vo.getTotalCapacity());
        bus.reply(msg, reply);
    }

    private void handle(final APIReconnectAppBuildSystemMsg msg) {
        APIReconnectAppBuildSystemEvent evt = new APIReconnectAppBuildSystemEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getUuid();
            }

            @Override
            public void run(SyncTaskChain chain) {
                AppBuildSystemVO vo = dbf.findByUuid(msg.getUuid(), AppBuildSystemVO.class);
                vo.setStatus(AppBuildSystemStatus.Connecting);
                dbf.updateAndRefresh(vo);

                ConnectAppBuildSystemMsg cmsg = new ConnectAppBuildSystemMsg();
                cmsg.setUuid(msg.getUuid());
                bus.makeTargetServiceIdByResourceUuid(cmsg, AppBuildSystemConstant.SERVICE_ID, cmsg.getBuildSystemUuid());
                bus.send(cmsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        if (reply.isSuccess()) {
                            AppBuildSystemVO vo = dbf.findByUuid(msg.getUuid(), AppBuildSystemVO.class);
                            vo.setStatus(AppBuildSystemStatus.Connected);
                            vo = dbf.updateAndRefresh(vo);
                            evt.setInventory(AppBuildSystemInventory.valueOf(vo));
                        } else {
                            evt.setError(reply.getError());
                        }
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("reconnect-buildsystem-%s", msg.getUuid());
            }
        });
    }

    private String getBuildSystemFromApp(String appUuid) {
        BuildApplicationVO avo = dbf.findByUuid(appUuid, BuildApplicationVO.class);
        if (avo == null) {
            throw new OperationFailureException(operr("cannot find build application: [%s]", appUuid));
        }
        return avo.getBuildSystemUuid();
    }

    private void handle(final APIDeleteBuildAppExportHistoryMsg msg) {
        APIDeleteBuildAppExportHistoryEvent evt = new APIDeleteBuildAppExportHistoryEvent(msg.getId());
        List<BuildAppExportHistoryVO> export;
        String buildSystemUuid;

        if (msg.getBuildAppUuid() != null) {
            export = Q.New(BuildAppExportHistoryVO.class).
                    eq(BuildAppExportHistoryVO_.buildAppUuid, msg.getBuildAppUuid()).
                    notEq(BuildAppExportHistoryVO_.status, BuildAppExportStatus.Cleaned).list();
            buildSystemUuid = getBuildSystemFromApp(export.get(0).getBuildAppUuid());
        } else {
            export = Q.New(BuildAppExportHistoryVO.class).
                    eq(BuildAppExportHistoryVO_.id, msg.getExportId()).
                    notEq(BuildAppExportHistoryVO_.status, BuildAppExportStatus.Cleaned).list();
            buildSystemUuid = msg.getBuildSystemUuid();
        }

        if (export == null || export.isEmpty()) {
            bus.publish(evt);
            return;
        }


        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return "delete-export-buildapp";
            }

            @Override
            public void run(SyncTaskChain chain) {
                DeleteBuildAppHistoryMsg dmsg = new DeleteBuildAppHistoryMsg();
                dmsg.setAppUuid(export.get(0).getBuildAppUuid());
                dmsg.setBuildSystemUuid(buildSystemUuid);
                bus.makeTargetServiceIdByResourceUuid(dmsg, AppBuildSystemConstant.SERVICE_ID, export.get(0).getBuildAppUuid());
                bus.send(dmsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            evt.setError(reply.getError());
                        } else {
                            dbf.removeCollection(export, BuildAppExportHistoryVO.class);
//                            BuildAppExportHistoryVO history = dbf.findById(export.getId(), BuildAppExportHistoryVO.class);
//                            history.setStatus(BuildAppExportStatus.Cleaned);
//                            history = dbf.updateAndRefresh(history);
//                            evt.setInventory(BuildAppExportHistoryInventory.valueOf(history));
                        }
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("delete-export-app-%s", export.get(0).getBuildAppUuid());
            }

            @Override
            protected int getSyncLevel() {
                return 2;
            }
        });
    }

    private void createBuildApp(final APICreateBuildAppMsg msg, ReturnValueCompletion<BuildApplicationInventory> completion) {
        FlowChain chain = FlowChainBuilder.newShareFlowChain();
        chain.setName(String.format("create-build-application-on-%s", msg.getBuildSystemUuid()));
        chain.then(new ShareFlow() {
            BuildApplicationVO avo = new BuildApplicationVO();
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "create-application-in-db";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        if (msg.getResourceUuid() != null) {
                            avo.setUuid(msg.getResourceUuid());
                        } else {
                            avo.setUuid(Platform.getUuid());
                        }
                        avo.setAccountUuid(msg.getSession().getAccountUuid());
                        avo.setStatus(BuildApplicationStatus.Creating);
                        avo.setName("");
                        avo.setDescription("");
                        avo.setVersion("");
                        avo.setAppId("");
                        avo.setBuildSystemUuid(msg.getBuildSystemUuid());
                        avo.setTemplateContent("");
                        avo.setAppMetaData("");

                        dbf.persistAndRefresh(avo);
                        tagMgr.createTagsFromAPICreateMessage(msg, avo.getUuid(), BuildApplicationVO.class.getSimpleName());

                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (avo != null) {
                            dbf.removeByPrimaryKey(avo.getUuid(), BuildApplicationVO.class);
                        }
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = String.format("create-application-on-buildsystem-%s", msg.getBuildSystemUuid());
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        CreateBuildAppMsg cmsg = new CreateBuildAppMsg();
                        cmsg.setBuildSystemUuid(msg.getBuildSystemUuid());
                        cmsg.setApp(BuildApplicationInventory.valueOf(avo));
                        cmsg.setDataPath(msg.getDataPath());
                        cmsg.setBackupStorageUuid(msg.getBackupStorageUuid());
                        cmsg.setSession(msg.getSession());
                        bus.makeTargetServiceIdByResourceUuid(cmsg, AppBuildSystemConstant.SERVICE_ID, cmsg.getBuildSystemUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (reply.isSuccess()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(reply.getError());
                                }
                            }
                        });
                    }
                });


                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        avo = dbf.reload(avo);
                        avo.setStatus(BuildApplicationStatus.Ready);
                        avo = dbf.updateAndRefresh(avo);
                        completion.success(BuildApplicationInventory.valueOf(avo));
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();

    }

    private void handle(final APICreateBuildAppMsg msg) {
        APICreateBuildAppEvent evt = new APICreateBuildAppEvent(msg.getId());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain chain) {
                createBuildApp(msg, new ReturnValueCompletion<BuildApplicationInventory>(chain) {
                    @Override
                    public void success(BuildApplicationInventory inventory) {
                        // appMetaData is too long, we try to ignore it in return value
                        inventory.setAppMetaData(null);
                        evt.setInventory(inventory);
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("create-application-on-buildsystem-%s", msg.getBuildSystemUuid());
            }

            @Override
            protected int getSyncLevel() {
                return 2;
            }
        });
    }

    private void handle(final APIDeleteBuildAppMsg msg) {
        APIDeleteBuildAppEvent evt = new APIDeleteBuildAppEvent(msg.getId());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getUuid();
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                final String issuer = BuildApplicationVO.class.getSimpleName();
                BuildApplicationVO vo = dbf.findByUuid(msg.getUuid(), BuildApplicationVO.class);
                vo.setStatus(BuildApplicationStatus.Deleting);
                vo = dbf.updateAndRefresh(vo);

                final List<BuildApplicationInventory> ctx = BuildApplicationInventory.valueOf(Arrays.asList(vo));

                FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
                chain.setName(String.format("delete-buildapp-%s", msg.getUuid()));
                chain.then(new NoRollbackFlow() {
                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                }).then(new NoRollbackFlow() {
                    @Override
                    public void run(final FlowTrigger trigger, Map data) {
                        casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }
                });

                chain.done(new FlowDoneHandler(msg) {
                    @Override
                    public void handle(Map data) {
                        casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                        bus.publish(evt);
                        taskChain.next();
                    }
                }).error(new FlowErrorHandler(msg) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        evt.setError(err(SysErrors.DELETE_RESOURCE_ERROR, errCode, errCode.getDetails()));
                        bus.publish(evt);
                        taskChain.next();
                    }
                }).start();
            }

            @Override
            public String getName() {
                return String.format("delete-buildapp-%s", msg.getUuid());
            }
        });
    }

    private void handle(final APIUpdateBuildAppMsg msg) {
        APIUpdateBuildAppEvent evt = new APIUpdateBuildAppEvent(msg.getId());
        BuildApplicationVO vo = dbf.findByUuid(msg.getUuid(), BuildApplicationVO.class);
        if (msg.getDescription() != null) {
            vo.setDescription(msg.getDescription());
        }

        if (msg.getName() != null) {
            vo.setName(msg.getName());
        }

        if (msg.getVersion() != null) {
            vo.setVersion(msg.getVersion());
        }

        vo = dbf.updateAndRefresh(vo);
        evt.setInventory(BuildApplicationInventory.valueOf(vo));

        bus.publish(evt);
    }

    private String makeExportMetaCtx(BuildApplicationInventory buildapp) {
        BuildAppStruct struct = JSONObjectUtil.toObject(buildapp.getAppMetaData(), BuildAppStruct.class);
        struct.setTemplate(buildapp.getTemplateContent());
        return Base64.encodeBase64String(JSONObjectUtil.toJsonString(struct).getBytes());
    }

    private void handle(final APIExportBuildAppMsg msg) {
        APIExportBuildAppEvent evt = new APIExportBuildAppEvent(msg.getId());
        String buildSystemUuid = getBuildSystemFromApp(msg.getUuid());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("export-%s-on-buildsystem-%s", msg.getUuid(), buildSystemUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                BuildApplicationVO app = dbf.findByUuid(msg.getUuid(), BuildApplicationVO.class);

                BuildAppExportHistoryVO vo = new BuildAppExportHistoryVO();
                vo.setBuildAppUuid(app.getUuid());
                vo.setName(app.getAppId());
                vo.setVersion(app.getVersion());
                vo.setStatus(BuildAppExportStatus.Exporting);
                vo.setPath("");
                vo.setMd5Sum("");
                vo.setSize(0L);
                vo.setVersion(app.getVersion());
                dbf.persistAndRefresh(vo);

                ExportBuildAppMsg dmsg = new ExportBuildAppMsg();
                dmsg.setAppUuid(msg.getUuid());
                dmsg.setBuildSystemUuid(buildSystemUuid);
                dmsg.setMetaCtx(makeExportMetaCtx(BuildApplicationInventory.valueOf(app)));
                dmsg.setInstallPath(app.getInstallPath());
                bus.makeTargetServiceIdByResourceUuid(dmsg, AppBuildSystemConstant.SERVICE_ID, msg.getUuid());
                bus.send(dmsg, new CloudBusCallBack(msg) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            evt.setError(reply.getError());
                            dbf.remove(vo);
                        } else {
                            ExportBuildAppReply rly = reply.castReply();
                            BuildAppExportHistoryVO history = dbf.findById(vo.getId(), BuildAppExportHistoryVO.class);
                            history.setPath(rly.getPath());
                            history.setMd5Sum(rly.getMd5sum());
                            history.setSize(rly.getSize());
                            history.setStatus(BuildAppExportStatus.Exported);
                            dbf.updateAndRefresh(history);
                            evt.setInventory(BuildAppExportHistoryInventory.valueOf(history));
                        }
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("export-app-%s-on-buildsystem-%s", msg.getUuid(), buildSystemUuid);
            }

            @Override
            protected int getSyncLevel() {
                return 2;
            }
        });
    }

    private void addBuildApp(final APIAddBuildAppMsg msg, final ReturnValueCompletion<BuildApplicationInventory> completion) {
        ImageStoreBackupStorageVO vo = dbf.findByUuid(msg.getBackupStorageUuid(), ImageStoreBackupStorageVO.class);
        if (vo.getState() != BackupStorageState.Enabled) {
            completion.fail(operr("imageStore is not Enabled"));
            return;
        }
        if (vo.getStatus() != BackupStorageStatus.Connected) {
            completion.fail(operr("imageStore is not Connected"));
            return;
        }

        FlowChain chain = FlowChainBuilder.newShareFlowChain();

        chain.then(new ShareFlow() {
            BuildApplicationVO buildapp = new BuildApplicationVO();
            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "create build-app in db";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        buildapp.setAccountUuid(msg.getSession().getAccountUuid());
                        buildapp.setStatus(BuildApplicationStatus.Creating);
                        buildapp.setDescription("");
                        buildapp.setName("");
                        if (msg.getResourceUuid() != null) {
                            buildapp.setUuid(msg.getResourceUuid());
                        } else {
                            buildapp.setUuid(Platform.getUuid());
                        }

                        buildapp.setVersion("");
                        buildapp.setAppId("");
                        buildapp.setTemplateContent("");
                        buildapp.setAppMetaData("");
                        dbf.persistAndRefresh(buildapp);

                        tagMgr.createTagsFromAPICreateMessage(msg, buildapp.getUuid(), BuildApplicationVO.class.getSimpleName());
                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (buildapp.getUuid() != null) {
                            dbf.removeByPrimaryKey(buildapp.getUuid(), BuildApplicationVO.class);
                        }
                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    String __name__ = "create build-app in storage";
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        AddBuildAppMsg amsg = new AddBuildAppMsg();
                        amsg.setBuildAppUuid(buildapp.getUuid());
                        amsg.setUrl(msg.getUrl());
                        amsg.setBackupStorageUuid(msg.getBackupStorageUuid());
                        amsg.setSession(msg.getSession());

                        bus.makeTargetServiceIdByResourceUuid(amsg, AppBuildSystemConstant.SERVICE_ID, msg.getBackupStorageUuid());
                        bus.send(amsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (reply.isSuccess()) {
                                    trigger.next();
                                } else {
                                    trigger.fail(reply.getError());
                                }
                            }
                        });
                    }
                });

                done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        buildapp = dbf.reload(buildapp);
                        buildapp.setStatus(BuildApplicationStatus.Ready);
                        dbf.updateAndRefresh(buildapp);
                        completion.success(BuildApplicationInventory.valueOf(buildapp));
                    }
                });

                error(new FlowErrorHandler(completion) {
                    @Override
                    public void handle(ErrorCode errCode, Map data) {
                        completion.fail(errCode);
                    }
                });
            }
        }).start();
    }

    private void handle(final APIAddBuildAppMsg msg) {
        APIAddBuildAppEvent evt = new APIAddBuildAppEvent(msg.getId());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return getName();
            }

            @Override
            public void run(SyncTaskChain taskChain) {
                addBuildApp(msg, new ReturnValueCompletion<BuildApplicationInventory>(taskChain) {
                    @Override
                    public void success(BuildApplicationInventory inventory) {
                        // appMetaData is too long, we try to ignore it in return value
                        inventory.setAppMetaData(null);
                        evt.setInventory(inventory);
                        bus.publish(evt);
                        taskChain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        taskChain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return "add-build-app";
            }
        });
    }

    private List<StackParameters> getStackPreParameters(BuildApplicationVO vo) {
        List<StackParameters> parameters = new ArrayList<>();

        CloudFormationDecoder decoder = new CloudFormationDecoder();
        CfnResults result = decoder.decodeFromContent(vo.getTemplateContent(), null, null, false);
        ResourceDecoder rDecoder = new ResourceDecoder();
        Map<String, String> resourceTypes = rDecoder.getResourceParametersType(new JsonParser().parse(vo.getTemplateContent()));

        result.getPreparams().forEach(p -> {
            StackParameters param = new StackParameters();
            param.setConstraintDescription(p.getConstraintDescription());
            param.setDescription(p.getDescription());
            param.setLabel(p.getLabel());
            param.setNoEcho(p.getNoEcho());
            param.setType(p.getType());
            if (p.getDefaultValue() != null) {
                param.setDefaultValue(p.getDefaultValue().toString());
            }
            param.setParamName(p.getParamName());
            param.setResourceType(resourceTypes.get(p.getParamName()));
            parameters.add(param);
        });

        parameters.stream().filter(p -> p.getResourceType().equals("Image")).forEach(p -> {
            if (p.getDefaultValue() != null) {
                String imageUuid = Q.New(BuildAppImageRefVO.class).eq(BuildAppImageRefVO_.buildAppUuid, vo.getUuid()).eq(BuildAppImageRefVO_.imageName, p.getDefaultValue())
                        .select(BuildAppImageRefVO_.imageUuid).findValue();
                if (imageUuid != null) {
                    p.setDefaultValue(imageUuid);
                } else {
                    throw new OperationFailureException(operr("cannot find imageUuid for image[%s]", p.getDefaultValue()));
                }
            }
        });

        return parameters;
    }

    private void handle(final APICheckBuildAppParametersMsg msg) {
        APICheckBuildAppParametersReply sreply = new APICheckBuildAppParametersReply();
        BuildApplicationVO vo = dbf.findByUuid(msg.getUuid(), BuildApplicationVO.class);
        if (vo == null || vo.getStatus() == BuildApplicationStatus.Deleting) {
            sreply.setError(operr("cannot find build-app[uuid: %s], or it was in Deleting status", msg.getUuid()));
            bus.reply(msg, sreply);
            return;
        }

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return msg.getUuid();
            }

            @Override
            public void run(SyncTaskChain chain) {
                BuildApplicationVO vo = dbf.findByUuid(msg.getUuid(), BuildApplicationVO.class);
                sreply.setParameters(getStackPreParameters(vo));
                bus.reply(msg, sreply);
                chain.next();
            }

            @Override
            public String getName() {
                return String.format("check-rawapp-%s-parameters", msg.getUuid());
            }
        });
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(AppBuildSystemConstant.SERVICE_ID);
    }

    @Override
    public boolean start() {
        ansible();
        populate();
        return true;
    }

    private void ansible() {
        if (!CoreGlobalProperty.UNIT_TEST_ON) {
            asf.deployModule(AppCenterGlobalProperty.BUILD_SYSTEM_MODULE_PATH, AppCenterGlobalProperty.BUILD_SYSTEM_PLAYBOOK_NAME);
        }
    }

    private void populate() {
        for (BuildSystemFactory factory : pluginRgty.getExtensionList(BuildSystemFactory.class)) {
            BuildSystemFactory old = buildStorageFactories.get(factory.getBuildStorageType().toString());
            if (old != null) {
                throw new CloudRuntimeException(String.format("duplicate BuildSystemFactory[%s, %s] for type[%s]",
                        factory.getClass().getName(), old.getClass().getName(), old.getBuildStorageType().toString()));
            }
            buildStorageFactories.put(factory.getBuildStorageType().toString(), factory);
        }
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void addMoreAgentInBackupStorage(ImageStoreBackupStorageVO vo, Completion completion) {
        if (CoreGlobalProperty.UNIT_TEST_ON) {
            completion.success();
            return;
        }
        if (!AppCenterServerGlobalProperty.isAppCenterMode()) {
            completion.success();
            return;
        }

        SshFileMd5Checker checker = new SshFileMd5Checker();
        checker.setTargetIp(vo.getHostname());
        checker.setUsername(vo.getUsername());
        checker.setPassword(vo.getPassword());
        checker.setSshPort(vo.getSshPort());
        checker.addSrcDestPair(SshFileMd5Checker.ZSTACKLIB_SRC_PATH, String.format("/var/lib/zstack/appbuildsystem/package/%s", AnsibleGlobalProperty.ZSTACKLIB_PACKAGE_NAME));
        checker.addSrcDestPair(PathUtil.findFileOnClassPath(String.format("ansible/appbuild/%s", AppCenterGlobalProperty.BUILD_SYSTEM_PACKAGE_NAME), true).getAbsolutePath(),
                String.format("/var/lib/zstack/appbuildsystem/package/%s", AppCenterGlobalProperty.BUILD_SYSTEM_PACKAGE_NAME));

        AnsibleRunner runner = new AnsibleRunner();
        runner.installChecker(checker);
        runner.setPassword(vo.getPassword());
        runner.setUsername(vo.getUsername());
        runner.setTargetIp(vo.getHostname());
        runner.setSshPort(vo.getSshPort());
        runner.setAgentPort(AppCenterGlobalProperty.BUILD_SYSTEM_SERVER_PORT);
        runner.setPlayBookName(AppCenterGlobalProperty.BUILD_SYSTEM_PLAYBOOK_NAME);
        runner.setDeployArguments(new AppBuildSystemDeployArguments());
        runner.run(new ReturnValueCompletion<Boolean>(completion) {
            @Override
            public void success(Boolean deployed) {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }
}
