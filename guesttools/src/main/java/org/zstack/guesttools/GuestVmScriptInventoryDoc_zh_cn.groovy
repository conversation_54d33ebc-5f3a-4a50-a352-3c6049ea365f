package org.zstack.guesttools

import java.lang.Integer
import java.sql.Timestamp

doc {

	title "脚本"

	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "5.2.0"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "5.2.0"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "5.2.0"
	}
	field {
		name "scriptContent"
		desc "脚本内容"
		type "String"
		since "5.2.0"
	}
	field {
		name "renderParams"
		desc "参数"
		type "String"
		since "5.2.0"
	}
	field {
		name "platform"
		desc "平台"
		type "String"
		since "5.2.0"
	}
	field {
		name "scriptType"
		desc "类型"
		type "String"
		since "5.2.0"
	}
	field {
		name "scriptTimeout"
		desc "超时时间"
		type "Integer"
		since "5.2.0"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "5.2.0"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "5.2.0"
	}
}