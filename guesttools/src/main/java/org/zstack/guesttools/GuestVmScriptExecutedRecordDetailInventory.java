package org.zstack.guesttools;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@PythonClassInventory
@Inventory(mappingVOClass = GuestVmScriptExecutedRecordDetailVO.class)
public class GuestVmScriptExecutedRecordDetailInventory implements Serializable {
    private String recordUuid;
    private String vmInstanceUuid;

    private String vmName;
    private String status;

    private Integer exitCode;
    private String stdout;

    private String errCause;
    private String stderr;
    private Timestamp startTime;
    private Timestamp endTime;

    public static GuestVmScriptExecutedRecordDetailInventory valueOf(GuestVmScriptExecutedRecordDetailVO vo) {
        GuestVmScriptExecutedRecordDetailInventory inv = new GuestVmScriptExecutedRecordDetailInventory();
        inv.recordUuid = vo.getRecordUuid();
        inv.vmInstanceUuid = vo.getVmInstanceUuid();
        inv.vmName = vo.getVmName();
        inv.status = vo.getStatus();
        inv.stdout = vo.getStdout();
        inv.errCause = vo.getErrCause();
        inv.stderr = vo.getStderr();
        inv.startTime = vo.getStartTime();
        inv.endTime = vo.getEndTime();
        return inv;
    }

    public static List<GuestVmScriptExecutedRecordDetailInventory> valueOf(Collection<GuestVmScriptExecutedRecordDetailVO> vos) {
        return vos.stream().map(GuestVmScriptExecutedRecordDetailInventory::valueOf).collect(Collectors.toList());
    }


    public String getRecordUuid() {
        return recordUuid;
    }

    public void setRecordUuid(String recordUuid) {
        this.recordUuid = recordUuid;
    }

    public String getVmInstanceUuid() {
        return vmInstanceUuid;
    }

    public void setVmInstanceUuid(String vmInstanceUuid) {
        this.vmInstanceUuid = vmInstanceUuid;
    }

    public String getVmName() {
        return vmName;
    }

    public void setVmName(String vmName) {
        this.vmName = vmName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getExitCode() {
        return exitCode;
    }

    public void setExitCode(Integer exitCode) {
        this.exitCode = exitCode;
    }

    public String getStdout() {
        return stdout;
    }

    public void setStdout(String stdout) {
        this.stdout = stdout;
    }

    public String getErrCause() {
        return errCause;
    }

    public void setErrCause(String errCause) {
        this.errCause = errCause;
    }

    public String getStderr() {
        return stderr;
    }

    public void setStderr(String stderr) {
        this.stderr = stderr;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }
}
