package org.zstack.guesttools

import org.zstack.guesttools.GuestToolsStateInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "云主机GuestTools状态信息"

	ref {
		name "inventory"
		path "org.zstack.guesttools.APIUpdateGuestToolsStateReply.inventory"
		desc "null"
		type "GuestToolsStateInventory"
		since "4.6"
		clz GuestToolsStateInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.6"
	}
	ref {
		name "error"
		path "org.zstack.guesttools.APIUpdateGuestToolsStateReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.6"
		clz ErrorCode.class
	}
}
