package org.zstack.guesttools;


import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@PythonClassInventory
@Inventory(mappingVOClass = GuestVmScriptVO.class)
public class GuestVmScriptInventory implements Serializable {

    private String uuid;

    private String name;

    private String description;

    private String encodingType;

    private String scriptContent;

    private String renderParams;

    private String platform;

    private String scriptType;

    private Integer scriptTimeout;

    private Timestamp createDate;

    private Timestamp lastOpDate;

    public static GuestVmScriptInventory valueOf(GuestVmScriptVO vo) {
        GuestVmScriptInventory inv = new GuestVmScriptInventory();
        inv.uuid = vo.getUuid();
        inv.name = vo.getName();
        inv.description = vo.getDescription();
        inv.encodingType = vo.getEncodingType();
        inv.scriptContent = vo.getScriptContent();
        inv.renderParams = vo.getRenderParams();
        inv.platform = vo.getPlatform();
        inv.scriptType = vo.getScriptType();
        inv.scriptTimeout = vo.getScriptTimeout();
        inv.createDate = vo.getCreateDate();
        inv.lastOpDate = vo.getLastOpDate();
        return inv;
    }

    public static List<GuestVmScriptInventory> valueOf(Collection<GuestVmScriptVO> vos) {
        return vos.stream().map(GuestVmScriptInventory::valueOf).collect(Collectors.toList());
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getScriptContent() {
        return scriptContent;
    }

    public void setScriptContent(String scriptContent) {
        this.scriptContent = scriptContent;
    }

    public String getRenderParams() {
        return renderParams;
    }

    public void setRenderParams(String renderParams) {
        this.renderParams = renderParams;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public Integer getScriptTimeout() {
        return scriptTimeout;
    }

    public void setScriptTimeout(Integer scriptTimeout) {
        this.scriptTimeout = scriptTimeout;
    }


    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public String getEncodingType() {
        return encodingType;
    }

    public void setEncodingType(String encodingType) {
        this.encodingType = encodingType;
    }

}
