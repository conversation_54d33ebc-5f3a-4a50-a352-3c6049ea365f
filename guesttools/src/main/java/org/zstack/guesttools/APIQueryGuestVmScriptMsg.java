package org.zstack.guesttools;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;

import java.util.List;

import static java.util.Arrays.asList;

@AutoQuery(replyClass = APIQueryGuestVmScriptReply.class, inventoryClass = GuestVmScriptInventory.class)
@RestRequest(path = "/scripts", optionalPaths = {"/scripts/{uuid}"}, responseClass = APIQueryGuestVmScriptReply.class, method = HttpMethod.GET)
@Action(category = GuestToolsConstant.ACTION_CATEGORY)
public class APIQueryGuestVmScriptMsg extends APIQueryMessage {
    public static List<String> __example__() {
        return asList("uuid={uuid}", "name='script1'", "platform='Linux'", "scriptType='Shell'");
    }
}
