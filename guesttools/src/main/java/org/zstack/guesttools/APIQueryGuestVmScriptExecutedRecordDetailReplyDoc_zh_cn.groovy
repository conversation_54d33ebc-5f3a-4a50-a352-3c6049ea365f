package org.zstack.guesttools

import org.zstack.guesttools.GuestVmScriptExecutedRecordDetailInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "查询脚本执行明细"

	ref {
		name "inventories"
		path "org.zstack.guesttools.APIQueryGuestVmScriptExecutedRecordDetailReply.inventories"
		desc "null"
		type "List"
		since "5.2.0"
		clz GuestVmScriptExecutedRecordDetailInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "5.2.0"
	}
	ref {
		name "error"
		path "org.zstack.guesttools.APIQueryGuestVmScriptExecutedRecordDetailReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.2.0"
		clz ErrorCode.class
	}
}