package org.zstack.guesttools

import org.zstack.guesttools.GuestVmScriptExecutedRecordInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "查询脚本执行记录"

	ref {
		name "inventories"
		path "org.zstack.guesttools.APIQueryGuestVmScriptExecutedRecordReply.inventories"
		desc "null"
		type "List"
		since "5.2.0"
		clz GuestVmScriptExecutedRecordInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "5.2.0"
	}
	ref {
		name "error"
		path "org.zstack.guesttools.APIQueryGuestVmScriptExecutedRecordReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.2.0"
		clz ErrorCode.class
	}
}