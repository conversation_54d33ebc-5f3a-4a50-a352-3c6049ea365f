package org.zstack.guesttools;

import org.zstack.header.identity.rbac.RBACDescription;

public class RBACInfo implements RBACDescription {

    @Override
    public void permissions() {
        permissionBuilder()
                .name("guest-tools")
                .normalAPIs("org.zstack.guesttools.**")
                .adminOnlyAPIs(APIExecuteGuestVmScriptMsg.class, APIExecuteGuestVmCommandMsg.class, APIUploadFileToVmMsg.class)
                .build();
    }

    @Override
    public void contributeToRoles() {
        roleContributorBuilder()
                .roleName("vm")
                .actionsByPermissionName("guest-tools")
                .build();
    }

    @Override
    public void roles() {

    }

    @Override
    public void globalReadableResources() {

    }
}
