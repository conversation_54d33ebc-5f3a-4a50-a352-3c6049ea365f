package org.zstack.guesttools;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.guesttools.codec.CodecType;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.message.APIMessage;
import org.zstack.header.sriov.APIChangeVfNicHaStateMsg;
import org.zstack.header.sriov.VmVfNicConstant;
import org.zstack.header.sriov.VmVfNicHaState;
import org.zstack.header.sriov.VmVfNicVO;
import org.zstack.header.vm.*;
import org.zstack.identity.AccountManager;
import org.zstack.license.LicenseManager;
import org.zstack.storage.migration.primary.APIPrimaryStorageMigrateVmMsg;
import org.zstack.header.tag.SystemTagVO;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.utils.TagUtils;
import org.zstack.utils.gson.JSONObjectUtil;

import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

import javax.persistence.TypedQuery;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.zstack.header.vm.VmInstanceConstant.USER_VM_TYPE;

/**
 * Created by GuoYi on 2019-09-17.
 */
public class GuestToolsApiInterceptor implements ApiMessageInterceptor, GlobalApiMessageInterceptor {

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;
    @Autowired
    protected LicenseManager licMgr;
    @Autowired
    protected AccountManager accountManager;

    private void setServiceId(APIMessage msg) {
        if (msg instanceof APIAttachGuestToolsIsoToVmMsg) {
            APIAttachGuestToolsIsoToVmMsg amsg = (APIAttachGuestToolsIsoToVmMsg) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, GuestToolsConstant.SERVICE_ID, amsg.getUuid());
        } else if (msg instanceof APIGetVmGuestToolsInfoMsg) {
            APIGetVmGuestToolsInfoMsg gmsg = (APIGetVmGuestToolsInfoMsg) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, GuestToolsConstant.SERVICE_ID, gmsg.getVmInstanceUuid());
        } else if (msg instanceof APIUpdateVmNetworkConfigMsg) {
            APIUpdateVmNetworkConfigMsg umsg = (APIUpdateVmNetworkConfigMsg) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, GuestToolsConstant.SERVICE_ID, umsg.getVmInstanceUuid());
        } else if (msg instanceof APIUpdateGuestToolsStateMsg) {
            APIUpdateGuestToolsStateMsg umsg = (APIUpdateGuestToolsStateMsg) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, GuestToolsConstant.SERVICE_ID, umsg.getVmInstanceUuid());
        }
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        List<Class> ret = new ArrayList<>();
        ret.add(APISetVmHostnameMsg.class);
        ret.add(APIAttachL3NetworkToVmMsg.class);
        ret.add(APIMigrateVmMsg.class);
        ret.add(APIChangeVfNicHaStateMsg.class);
        ret.add(APIPrimaryStorageMigrateVmMsg.class);
        return ret;
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.END;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIAttachGuestToolsIsoToVmMsg) {
            validate((APIAttachGuestToolsIsoToVmMsg) msg);
        } else if (msg instanceof APIGetVmGuestToolsInfoMsg) {
            validate((APIGetVmGuestToolsInfoMsg) msg);
        } else if (msg instanceof APIUpdateVmNetworkConfigMsg) {
            validate((APIUpdateVmNetworkConfigMsg) msg);
        } else if (msg instanceof APIUpdateGuestToolsStateMsg) {
            validate((APIUpdateGuestToolsStateMsg) msg);
        } else if (msg instanceof APISetVmHostnameMsg) {
            validate((APISetVmHostnameMsg) msg);
        } else if (msg instanceof APIAttachL3NetworkToVmMsg) {
            validate((APIAttachL3NetworkToVmMsg) msg);
        } else if (msg instanceof APIMigrateVmMsg) {
            validate((APIMigrateVmMsg) msg);
        } else if (msg instanceof APIChangeVfNicHaStateMsg) {
            validate((APIChangeVfNicHaStateMsg) msg);
        } else if (msg instanceof APIPrimaryStorageMigrateVmMsg) {
            validate((APIPrimaryStorageMigrateVmMsg) msg);
        } else if (msg instanceof APICreateGuestVmScriptMsg) {
            validate((APICreateGuestVmScriptMsg) msg);
        } else if (msg instanceof APIExecuteGuestVmScriptMsg) {
            validate((APIExecuteGuestVmScriptMsg) msg);
        } else if (msg instanceof APIExecuteGuestVmCommandMsg) {
            validate((APIExecuteGuestVmCommandMsg) msg);
        } else if (msg instanceof APIUploadFileToVmMsg) {
            validate((APIUploadFileToVmMsg) msg);
        } else if (msg instanceof APIUpdateGuestVmScriptMsg) {
            validate((APIUpdateGuestVmScriptMsg) msg);
        }


        setServiceId(msg);
        return msg;
    }

    private void validate(APIUpdateGuestVmScriptMsg msg) {
        if (StringUtils.isBlank(msg.getEncodingType()) != StringUtils.isBlank(msg.getScriptContent())) {
            throw new ApiMessageInterceptionException(Platform.argerr("encodingType and scriptContent must be either both present or both absent"));
        }
    }

    private void validate(APIUploadFileToVmMsg msg) {
        checkUploadFileCondition(msg.getVmInstanceUuids());
    }

    private void validate(APIExecuteGuestVmCommandMsg msg) {
        checkUploadFileCondition(Collections.singletonList(msg.getVmInstanceUuid()));
    }

    private void validate(APIExecuteGuestVmScriptMsg msg) {
        checkUploadFileCondition(msg.getVmInstanceUuids());
    }

    private void validate(APICreateGuestVmScriptMsg msg) {
        if (msg.getDescription() == null) {
            msg.setDescription("");
        }
        if (msg.getRenderParams() == null) {
            msg.setRenderParams("");
        }
        if (!msg.getRenderParams().equals("")) {
            validateRenderParams(msg.getRenderParams());
        }
    }

    private void validateRenderParams(String renderParams) {
        List<Map<String, String>> params = JSONObjectUtil.toCollection(renderParams, ArrayList.class, Map.class);
        for (Map<String, String> param : params) {
            String key = param.get("key");
            String value = param.get("value");
            String description = param.get("description");
            if (description == null) {
                description = "";
            }
            if (StringUtils.isEmpty(key) || StringUtils.isEmpty(value)) {
                throw new IllegalArgumentException("Invalid renderParams format");
            }
            //use key.length() better
            if (!key.matches("[a-zA-z0-9_]{1,64}") || value.length() > 64 || description.length() > 64) {
                throw new IllegalArgumentException("Invalid renderParams format");
            }
        }
    }



    private void validate(APIPrimaryStorageMigrateVmMsg msg) {
        VmInstanceVO vm = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        if (vm.getState() != VmInstanceState.Running) {
            return;
        }

        List<String> nicUuids = new ArrayList<>();
        for (VmNicVO nic : vm.getVmNics()) {
            if (nic.getType().equals(VmVfNicConstant.VIRTUAL_FUNCTION_TYPE)) {
                nicUuids.add(nic.getUuid());
            }
        }
        if (nicUuids.isEmpty()) {
            return;
        }

        GuestToolsStateVO toolsState = Q.New(GuestToolsStateVO.class)
                .eq(GuestToolsStateVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                .find();
        if (!toolsState.getQgaState().equals(GuestToolsQgaState.Running)){
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "could not migrate primary storage, because vm[uuid:%s] attached vf nic but guesttools not running",
                    msg.getVmInstanceUuid()));
        }
    }

    private void validate(APIChangeVfNicHaStateMsg msg) {
        VmVfNicVO vfNicVO = dbf.findByUuid(msg.getVfNicUuid(), VmVfNicVO.class);
        if (vfNicVO == null) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "could not change vf nic ha state, because vf nic[uuid:%s] is not found", msg.getVfNicUuid()));
        }

        if (msg.getHaState().equals(VmVfNicHaState.Enabled.toString())) {
            GuestToolsStateVO toolsState = Q.New(GuestToolsStateVO.class)
                    .eq(GuestToolsStateVO_.vmInstanceUuid, vfNicVO.getVmInstanceUuid())
                    .find();
            if (!toolsState.getQgaState().equals(GuestToolsQgaState.Running)) {
                throw new ApiMessageInterceptionException(Platform.argerr(
                        "could not change vf nic ha state, because guesttools not running."));
            }
        }
    }

    private void validate(APIMigrateVmMsg msg) {
       VmInstanceVO vmVO = dbf.findByUuid(msg.getVmUuid(), VmInstanceVO.class);
        List<String> nicUuids = new ArrayList<>();
        for (VmNicVO nic : vmVO.getVmNics()) {
            if (nic.getType().equals(VmVfNicConstant.VIRTUAL_FUNCTION_TYPE)) {
                nicUuids.add(nic.getUuid());
            }
        }

        if (nicUuids.isEmpty()) {
            return;
        }

        GuestToolsStateVO toolsState = Q.New(GuestToolsStateVO.class)
                .eq(GuestToolsStateVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                .find();
        if (!toolsState.getQgaState().equals(GuestToolsQgaState.Running)) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "could not migrate vm[uuid:%s], because vf nic is attached but guesttools not running", msg.getVmUuid()));
        }
    }

    private void validate(APIAttachL3NetworkToVmMsg msg) {
        if (CollectionUtils.isEmpty(msg.getSystemTags())) {
            return;
        }

        boolean enableVFHA = false;
        for (String tag : msg.getSystemTags()) {
            if (tag.toLowerCase().contains("enableVFHA".toLowerCase())) {
                enableVFHA = true;
                break;
            }
        }

        if (enableVFHA) {
            GuestToolsStateVO toolsState = Q.New(GuestToolsStateVO.class)
                .eq(GuestToolsStateVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                .find();
            if (!toolsState.getQgaState().equals(GuestToolsQgaState.Running)){
                throw new ApiMessageInterceptionException(Platform.argerr(
                        "could not attach l3 network to vm[uuid:%s], because guesttools not running.", msg.getVmInstanceUuid()));
            }
        }
    }

    private void validate(APISetVmHostnameMsg msg) {
        String hostname = msg.getHostname();
        String vmUuid = msg.getVmInstanceUuid();
        VmInstanceVO vmVO = dbf.findByUuid(vmUuid, VmInstanceVO.class);
        String defaultL3uuid = vmVO.getDefaultL3NetworkUuid();
        if (defaultL3uuid == null || defaultL3uuid.isEmpty()) {
            throw new ApiMessageInterceptionException(Platform.operr("unable to set vm hostname. the vm[uuid:%s] do not have default L3 network", vmUuid));
        }
        String sql = "select t" +
                        " from SystemTagVO t, VmInstanceVO vm, VmNicVO nic" +
                        " where t.resourceUuid = vm.uuid" +
                        " and vm.uuid = nic.vmInstanceUuid" +
                        " and nic.l3NetworkUuid = :l3Uuid" +
                        " and t.tag = :sysTag";
        TypedQuery<SystemTagVO> q = dbf.getEntityManager().createQuery(sql, SystemTagVO.class);
        q.setParameter("l3Uuid", defaultL3uuid);
        q.setParameter("sysTag", TagUtils.tagPatternToSqlPattern(VmSystemTags.HOSTNAME.instantiateTag(
                                                    map(e(VmSystemTags.HOSTNAME_TOKEN, hostname)))));
        List<SystemTagVO> vos = q.getResultList();
        if (!vos.isEmpty()) {
            SystemTagVO sameTag = vos.get(0);
            throw new ApiMessageInterceptionException(Platform.argerr("conflict hostname, there has been a VM[uuid:%s] having hostname[%s] on L3 network[uuid:%s]",
                                                sameTag.getResourceUuid(), hostname, defaultL3uuid));
        }
    }

    private void validate(APIUpdateVmNetworkConfigMsg msg) {
        VmInstanceVO vm = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        if (vm.getState() != VmInstanceState.Running) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "update vm[uuid:%s] network config failed, because vm not running.", msg.getVmInstanceUuid()));
        }
        GuestToolsStateVO toolsState = Q.New(GuestToolsStateVO.class)
                .eq(GuestToolsStateVO_.vmInstanceUuid, msg.getVmInstanceUuid())
                .find();
        if (toolsState.getQgaState().equals(GuestToolsQgaState.NotUpgraded)) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "update vm[uuid:%s] network config failed, because the guesttools version is too low for this feature.",
                            msg.getVmInstanceUuid()));
        } else if (!toolsState.getQgaState().equals(GuestToolsQgaState.Running)){
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "update vm[uuid:%s] network config failed, because guesttools not running.", msg.getVmInstanceUuid()));
        }
    }

    private void validate(APIAttachGuestToolsIsoToVmMsg msg) {
        VmInstanceVO vm = dbf.findByUuid(msg.getUuid(), VmInstanceVO.class);
        if (!GuestToolsConstant.HYPERVISOR_TYPES_SUPPORT_GUEST_TOOLS.contains(vm.getHypervisorType())) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "cannot attach guest-tools iso to vm[uuid:%s] because it's hypervisor type is not supported",
                    msg.getUuid()
            ));
        }

        if (vm.getState() != VmInstanceState.Running) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "cannot attach guest-tools iso to vm[uuid:%s] because it's not running",
                    msg.getUuid()
            ));
        }

        if (!VmInstanceConstant.USER_VM_TYPE.equals(vm.getType())) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "cannot attach guest-tools iso to vm[uuid:%s] because it's not user vm",
                    msg.getUuid()
            ));
        }

        if (CollectionUtils.isEmpty(vm.getVmCdRoms())) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "cannot attach guest-tools iso to vm[uuid:%s] because it has no cdrom",
                    msg.getUuid()
            ));
        }

        // ZSTAC-55541: For now, we will sync NIC information to new created VM which include guest-tools in their image
        if (VmSystemTags.SYNC_PORTS.hasTag(vm.getUuid())) {
            VmSystemTags.SYNC_PORTS.delete(vm.getUuid());
        }

    }

    private void validate(APIGetVmGuestToolsInfoMsg msg) {
        validateVmGuestToolsMsg(msg);
        validateGuestToolsDebugItems(msg);
    }

    private void validateVmGuestToolsMsg(VmInstanceMessage msg) {
        final String vmUuid = msg.getVmInstanceUuid();
        VmInstanceVO vm = dbf.findByUuid(vmUuid, VmInstanceVO.class);

        if (vm.getState() != VmInstanceState.Running && vm.getState() != VmInstanceState.VolumeRecovering) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "cannot get guest-tools info from vm[uuid:%s] because it's not running", vmUuid
            ));
        }

        if (!VmInstanceConstant.USER_VM_TYPE.equals(vm.getType())) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "cannot get guest-tools info from vm[uuid:%s] because it's not user vm", vmUuid
            ));
        }
    }

    private void validateGuestToolsDebugItems(APIGetVmGuestToolsInfoMsg msg) {
        if (CollectionUtils.isEmpty(msg.getDebug())) {
            return;
        }

        if (msg.getDebug().contains(GuestToolsInfoDebugItem.ALL)) {
            msg.setDebug(Stream.of(GuestToolsInfoDebugItem.values())
                    .map(i -> i.item)
                    .collect(Collectors.toSet()));
            return;
        }

        Set<String> invalidSet = msg.getDebug().stream()
                .filter(item -> GuestToolsInfoDebugItem.findByItemName(item) == null)
                .collect(Collectors.toSet());
        if (!invalidSet.isEmpty()) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "invalid debug parameter: %s", invalidSet));
        }
    }

    private void validate(APIUpdateGuestToolsStateMsg msg) {
        VmInstanceVO vmVo = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        if (vmVo == null) {
            throw new ApiMessageInterceptionException(Platform.argerr(
                    "can not update guest tools state for vm [uuid:%s] because vm is deleted", msg.getVmInstanceUuid()));
        }
    }

    private void checkUploadFileCondition(List<String> vmInstanceUuids) {
        if (!licMgr.isEnterpriseLicense()) {
            throw new IllegalArgumentException("only enterprise license can upload guest tools");
        }

        List<String> notRunningVmUuids = Q.New(VmInstanceVO.class)
                .select(VmInstanceVO_.uuid)
                .in(VmInstanceVO_.uuid, vmInstanceUuids)
                .notEq(VmInstanceVO_.state, VmInstanceState.Running)
                .listValues();
        if (!notRunningVmUuids.isEmpty()) {
            throw new IllegalArgumentException(String.format("Could not upload file because vm [uuid:%s] is not running", notRunningVmUuids));
        }

        List<String> notRunningQgaVmUuids = Q.New(GuestToolsStateVO.class)
                .select(GuestToolsStateVO_.vmInstanceUuid)
                .in(GuestToolsStateVO_.vmInstanceUuid, vmInstanceUuids)
                .notEq(GuestToolsStateVO_.qgaState, GuestToolsQgaState.Running)
                .listValues();
        if (!notRunningQgaVmUuids.isEmpty() && !CoreGlobalProperty.UNIT_TEST_ON) {
            throw new IllegalArgumentException(String.format("Could not upload file because the QEMU Guest Agent (QGA) for the virtual machine (UUID: %s) is not running. " +
                    "Please ensure that the QGA is running on the VM and try again.", notRunningVmUuids));
        }
    }

}
