package org.zstack.guesttools.codec;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class Base64CodecFactory implements CodecFactory {
    @Override
    public CodecType getType() {
        return CodecType.Base64;
    }

    @Override
    public String encode(String data) {
        if (data == null) {
            throw new IllegalArgumentException("Data cannot be null");
        }
        return Base64.getEncoder().encodeToString(data.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public String decode(String data) {
        try {
            return new String(Base64.getDecoder().decode(data), StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Illegal Base64 String: " + e.getMessage(), e);
        }
    }
}