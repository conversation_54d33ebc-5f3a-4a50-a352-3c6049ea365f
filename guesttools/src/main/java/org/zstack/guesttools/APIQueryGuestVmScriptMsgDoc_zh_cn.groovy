package org.zstack.guesttools

import org.zstack.guesttools.APIQueryGuestVmScriptReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryGuestVmScript"

    category "guest.tools"

    desc """查询脚本"""

    rest {
        request {
			url "GET /v1/scripts"
			url "GET /v1/scripts/{uuid}"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryGuestVmScriptMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryGuestVmScriptReply.class
        }
    }
}