package org.zstack.guesttools

import org.zstack.guesttools.APIQueryGuestVmScriptExecutedRecordDetailReply
import org.zstack.header.query.APIQueryMessage

doc {
    title "QueryGuestVmScriptExecutedRecordDetail"

    category "guest.tools"

    desc """查询脚本执行记录明细"""

    rest {
        request {
			url "GET /v1/scripts/records/details"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIQueryGuestVmScriptExecutedRecordDetailMsg.class

            desc """"""
            
			params APIQueryMessage.class
        }

        response {
            clz APIQueryGuestVmScriptExecutedRecordDetailReply.class
        }
    }
}