package org.zstack.guesttools;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.vm.VmInstanceVO;

import java.util.List;

import static java.util.Arrays.asList;

@Action(category = GuestToolsConstant.ACTION_CATEGORY)
@RestRequest(path = "/upload-file", method = HttpMethod.POST, responseClass = APIUploadFileToVmEvent.class, parameterName = "params")
public class APIUploadFileToVmMsg extends APIMessage {

    @APIParam(resourceType = VmInstanceVO.class)
    private List<String> vmInstanceUuids;

    @APIParam(minLength = 1, maxLength = 262144)
    private String fileContent;

    @APIParam
    private String remotePath;

    public static APIUploadFileToVmMsg __example__() {
        APIUploadFileToVmMsg msg = new APIUploadFileToVmMsg();
        msg.setFileContent("ZWNobyBoZWxsb3dvcmxkCg==");
        msg.setVmInstanceUuids(asList(uuid()));
        msg.setRemotePath("/var/lib/zstack/uploadfile");
        return msg;
    }

    public List<String> getVmInstanceUuids() {
        return vmInstanceUuids;
    }

    public void setVmInstanceUuids(List<String> vmInstanceUuids) {
        this.vmInstanceUuids = vmInstanceUuids;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public String getRemotePath() {
        return remotePath;
    }

    public void setRemotePath(String remotePath) {
        this.remotePath = remotePath;
    }
}
