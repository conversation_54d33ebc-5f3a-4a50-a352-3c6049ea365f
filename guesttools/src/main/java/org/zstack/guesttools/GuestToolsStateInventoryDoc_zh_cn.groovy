package org.zstack.guesttools

import org.zstack.guesttools.GuestToolsQgaState
import org.zstack.guesttools.GuestToolsZWatchState
import java.sql.Timestamp

doc {

	title "云主机GuestTools状态清单"

	field {
		name "vmInstanceUuid"
		desc "云主机UUID"
		type "String"
		since "4.6"
	}
	ref {
		name "qgaState"
		path "org.zstack.guesttools.GuestToolsStateInventory.qgaState"
		desc "云主机GuestTools qga状态"
		type "GuestToolsQgaState"
		since "4.6"
		clz GuestToolsQgaState.class
	}
	ref {
		name "zwatchState"
		path "org.zstack.guesttools.GuestToolsStateInventory.zwatchState"
		desc "云主机GuestTools zwatch状态"
		type "GuestToolsZWatchState"
		since "4.6"
		clz GuestToolsZWatchState.class
	}
	field {
		name "version"
		desc "云主机GuestTools版本"
		type "String"
		since "4.6"
	}
	field {
		name "platform"
		desc "云主机OS平台类型"
		type "String"
		since "4.6"
	}
	field {
		name "osType"
		desc "云主机OS类型"
		type "String"
		since "4.6"
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "4.6"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "4.6"
	}
}
