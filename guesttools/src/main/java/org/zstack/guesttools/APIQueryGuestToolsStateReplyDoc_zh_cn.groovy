package org.zstack.guesttools

import org.zstack.guesttools.GuestToolsStateInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "获取云主机GuestTools状态应答"

	ref {
		name "inventories"
		path "org.zstack.guesttools.APIQueryGuestToolsStateReply.inventories"
		desc "null"
		type "List"
		since "4.6.11"
		clz GuestToolsStateInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "4.6.11"
	}
	ref {
		name "error"
		path "org.zstack.guesttools.APIQueryGuestToolsStateReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "4.6.11"
		clz ErrorCode.class
	}
}
