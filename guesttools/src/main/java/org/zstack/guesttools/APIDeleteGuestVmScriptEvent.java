package org.zstack.guesttools;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;


@RestResponse
public class APIDeleteGuestVmScriptEvent extends APIEvent {

    public APIDeleteGuestVmScriptEvent() {
    }

    public APIDeleteGuestVmScriptEvent(String apiId) {
        super(apiId);
    }

    public static APIDeleteGuestVmScriptEvent __example__() {
        APIDeleteGuestVmScriptEvent evt = new APIDeleteGuestVmScriptEvent();
        return evt;
    }
}
