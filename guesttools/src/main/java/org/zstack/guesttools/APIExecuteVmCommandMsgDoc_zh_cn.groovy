package org.zstack.guesttools

doc {
    title "ExecuteVmCommand"

    category "guest.tools"

    desc """在这里填写API描述"""

    rest {
        request {
			url "POST /v1/vm-instances/commands/exec"

			header (Authorization: 'OAuth the-session-uuid')

            clz APIExecuteGuestVmCommandMsg.class

            desc """"""
            
			params {

				column {
					name "vmInstanceUuid"
					enclosedIn "params"
					desc "云主机UUID"
					location "body"
					type "String"
					optional false
					since "5.2.0"
				}
				column {
					name "platform"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional false
					since "5.2.0"
					values ("Windows","Linux")
				}
				column {
					name "command"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional false
					since "5.2.0"
				}
				column {
					name "commandTimeout"
					enclosedIn "params"
					desc ""
					location "body"
					type "Integer"
					optional true
					since "5.2.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc "系统标签"
					location "body"
					type "List"
					optional true
					since "5.2.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc "用户标签"
					location "body"
					type "List"
					optional true
					since "5.2.0"
				}
			}
        }

        response {
            clz APIExecuteGuestVmCommandEvent.class
        }
    }
}