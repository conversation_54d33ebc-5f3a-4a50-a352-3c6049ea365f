package org.zstack.guesttools;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@PythonClassInventory
@Inventory(mappingVOClass = GuestVmScriptExecutedRecordVO.class)
public class GuestVmScriptExecutedRecordInventory implements Serializable {

    private String uuid;

    private String scriptUuid;

    private String recordName;

    private Integer scriptTimeout;

    private String status;

    private String executor;

    private Integer executionCount;

    private Integer version;

    private String encodingType;

    private String scriptContent;

    private String renderParams;

    private Timestamp startTime;

    private Timestamp endTime;

    public static GuestVmScriptExecutedRecordInventory valueOf(GuestVmScriptExecutedRecordVO vo) {
        GuestVmScriptExecutedRecordInventory inv = new GuestVmScriptExecutedRecordInventory();
        inv.uuid = vo.getUuid();
        inv.scriptUuid = vo.getScriptUuid();
        inv.recordName = vo.getRecordName();
        inv.scriptTimeout = vo.getScriptTimeout();
        inv.status = vo.getStatus();
        inv.executor = vo.getExecutor();
        inv.executionCount = vo.getExecutionCount();
        inv.version = vo.getVersion();
        inv.scriptContent = vo.getScriptContent();
        inv.renderParams = vo.getRenderParams();
        inv.startTime = vo.getStartTime();
        inv.endTime = vo.getEndTime();
        inv.encodingType = vo.getEncodingType();
        return inv;
    }

    public static List<GuestVmScriptExecutedRecordInventory> valueOf(Collection<GuestVmScriptExecutedRecordVO> vos) {
        return vos.stream().map(GuestVmScriptExecutedRecordInventory::valueOf).collect(Collectors.toList());
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getScriptUuid() {
        return scriptUuid;
    }

    public void setScriptUuid(String scriptUuid) {
        this.scriptUuid = scriptUuid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public Integer getScriptTimeout() {
        return scriptTimeout;
    }

    public void setScriptTimeout(Integer scriptTimeout) {
        this.scriptTimeout = scriptTimeout;
    }

    public String getExecutor() {
        return executor;
    }

    public void setExecutor(String executor) {
        this.executor = executor;
    }

    public Integer getExecutionCount() {
        return executionCount;
    }

    public void setExecutionCount(Integer executionCount) {
        this.executionCount = executionCount;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getScriptContent() {
        return scriptContent;
    }

    public void setScriptContent(String scriptContent) {
        this.scriptContent = scriptContent;
    }

    public String getRenderParams() {
        return renderParams;
    }

    public void setRenderParams(String renderParams) {
        this.renderParams = renderParams;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public String getEncodingType() {
        return encodingType;
    }

    public void setEncodingType(String encodingType) {
        this.encodingType = encodingType;
    }
}
