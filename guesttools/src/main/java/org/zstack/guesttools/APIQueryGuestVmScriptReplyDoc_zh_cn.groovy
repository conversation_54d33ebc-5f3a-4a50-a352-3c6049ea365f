package org.zstack.guesttools

import org.zstack.guesttools.GuestVmScriptInventory
import org.zstack.header.errorcode.ErrorCode

doc {

	title "查询脚本"

	ref {
		name "inventories"
		path "org.zstack.guesttools.APIQueryGuestVmScriptReply.inventories"
		desc "null"
		type "List"
		since "5.2.0"
		clz GuestVmScriptInventory.class
	}
	field {
		name "success"
		desc ""
		type "boolean"
		since "5.2.0"
	}
	ref {
		name "error"
		path "org.zstack.guesttools.APIQueryGuestVmScriptReply.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "5.2.0"
		clz ErrorCode.class
	}
}