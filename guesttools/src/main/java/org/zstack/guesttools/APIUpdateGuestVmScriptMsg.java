package org.zstack.guesttools;

import org.springframework.http.HttpMethod;
import org.zstack.guesttools.codec.CodecType;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;

@Action(category = GuestToolsConstant.ACTION_CATEGORY, accountOnly = true)
@RestRequest(path = "/scripts/{uuid}/actions", method = HttpMethod.PUT, isAction = true, responseClass = APIUpdateGuestVmScriptEvent.class)
public class APIUpdateGuestVmScriptMsg extends APIMessage {
    @APIParam(resourceType = GuestVmScriptVO.class)
    private String uuid;
    
    @APIParam(required = false, minLength = 1, maxLength = 128, validRegexValues = "^(?! )[\\u4E00-\\u9FFFa-zA-Z0-9_\\-\\.():+\\s]*(?<! )$")
    private String name;

    @APIParam(required = false, maxLength = 256)
    private String description;

    @APIParam(required = false, validValues = {"Base64","PlainText"})
    private String encodingType;

    @APIParam(required = false, minLength = 1, maxLength = 65536)
    private String scriptContent;

    @APIParam(required = false, maxLength = 5120)
    private String renderParams;

    @APIParam(required = false, validValues = {"Windows", "Linux"})
    private String platform;

    @APIParam(required = false, validValues = {"Shell", "Python", "Perl", "Bat", "Powershell"})
    private String scriptType;

    @APIParam(required = false, numberRange = {0, 86400})
    private Integer scriptTimeout;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getScriptContent() {
        return scriptContent;
    }

    public void setScriptContent(String scriptContent) {
        this.scriptContent = scriptContent;
    }

    public String getRenderParams() {
        return renderParams;
    }

    public void setRenderParams(String renderParams) {
        this.renderParams = renderParams;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public Integer getScriptTimeout() {
        return scriptTimeout;
    }

    public void setScriptTimeout(Integer scriptTimeout) {
        this.scriptTimeout = scriptTimeout;
    }

    public String getEncodingType() {
        return encodingType;
    }

    public void setEncodingType(String encodingType) {
        this.encodingType = encodingType;
    }
}
