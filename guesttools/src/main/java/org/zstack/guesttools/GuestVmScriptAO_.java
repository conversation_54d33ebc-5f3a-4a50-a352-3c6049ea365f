package org.zstack.guesttools;

import org.zstack.header.vo.ResourceVO_;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

@StaticMetamodel(GuestVmScriptAO.class)
public class GuestVmScriptAO_ extends ResourceVO_ {
    public static volatile SingularAttribute<GuestVmScriptAO, String> name;
    public static volatile SingularAttribute<GuestVmScriptAO, String> description;
    public static volatile SingularAttribute<GuestVmScriptAO, String> encodingType;
    public static volatile SingularAttribute<GuestVmScriptAO, String> scriptContent;
    public static volatile SingularAttribute<GuestVmScriptAO, String> renderParams;
    public static volatile SingularAttribute<GuestVmScriptAO, String> platform;
    public static volatile SingularAttribute<GuestVmScriptAO, String> scriptType;
    public static volatile SingularAttribute<GuestVmScriptAO, Integer> scriptTimeout;
    public static volatile SingularAttribute<GuestVmScriptAO, Integer> version;
    public static volatile SingularAttribute<GuestVmScriptAO, Timestamp> createDate;
    public static volatile SingularAttribute<GuestVmScriptAO, Timestamp> lastOpDate;
}
