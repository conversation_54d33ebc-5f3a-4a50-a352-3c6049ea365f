package org.zstack.twoFactorAuthentication;

import org.zstack.header.identity.rbac.RBACDescription;

public class RBACInfo implements RBACDescription {
    @Override
    public void permissions() {
        permissionBuilder()
                .name("twoFactorAuthentication")
                .adminOnlyAPIs(org.zstack.twoFactorAuthentication.APIResetTwoFactorAuthenticationSecretMsg.class)
                .normalAPIs("org.zstack.twoFactorAuthentication.*")
                .build();
    }

    @Override
    public void contributeToRoles() {
        roleContributorBuilder()
                .roleName("iam2-normal-user-roles")
                .actionsByPermissionName("twoFactorAuthentication")
                .build();
    }

    @Override
    public void roles() {

    }

    @Override
    public void globalReadableResources() {

    }
}
