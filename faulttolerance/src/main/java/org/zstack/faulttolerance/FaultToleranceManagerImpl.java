package org.zstack.faulttolerance;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.compute.host.HostSystemTags;
import org.zstack.compute.vm.MevocoVmSystemTags;
import org.zstack.compute.vm.VmGlobalConfig;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventCallback;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.cloudbus.ResourceDestinationMaker;
import org.zstack.core.componentloader.BannedModule;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SQLBatch;
import org.zstack.core.db.SQLBatchWithReturn;
import org.zstack.core.db.UpdateQuery;
import org.zstack.core.gc.GCStatus;
import org.zstack.core.gc.GarbageCollectorVO;
import org.zstack.core.gc.GarbageCollectorVO_;
import org.zstack.core.thread.ChainTask;
import org.zstack.core.thread.PeriodicTask;
import org.zstack.core.thread.SyncTaskChain;
import org.zstack.core.thread.ThreadFacade;
import org.zstack.core.upgrade.GrayVersion;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.core.workflow.ShareFlow;
import org.zstack.core.workflow.ShareFlowChain;
import org.zstack.core.workflow.SimpleFlowChain;
import org.zstack.externalbackup.CreateVmExternalBackupMessage;
import org.zstack.externalbackup.ExternalBackupExtensionPoint;
import org.zstack.externalbackup.ExternalBackupSpec;
import org.zstack.faulttolerance.api.APICreateFaultToleranceVmInstanceEvent;
import org.zstack.faulttolerance.api.APICreateFaultToleranceVmInstanceMsg;
import org.zstack.faulttolerance.api.APIFailoverFaultToleranceVmEvent;
import org.zstack.faulttolerance.api.APIFailoverFaultToleranceVmMsg;
import org.zstack.faulttolerance.api.APIGetFaultToleranceVmsMsg;
import org.zstack.faulttolerance.api.APIGetFaultToleranceVmsReply;
import org.zstack.faulttolerance.entity.FaultToleranceVmGroupInventory;
import org.zstack.faulttolerance.entity.FaultToleranceVmGroupVO;
import org.zstack.faulttolerance.entity.FaultToleranceVmGroupVO_;
import org.zstack.faulttolerance.entity.FaultToleranceVmInstanceGroupHostPortRefVO;
import org.zstack.faulttolerance.entity.FaultToleranceVmInstanceGroupHostPortRefVO_;
import org.zstack.faulttolerance.entity.VmInstanceVmNicRedirectPortRefVO;
import org.zstack.faulttolerance.entity.VmInstanceVmNicRedirectPortRefVO_;
import org.zstack.faulttolerance.message.ChangeFaultToleranceVmStatusMsg;
import org.zstack.faulttolerance.message.ChangeFaultToleranceVmStatusReply;
import org.zstack.faulttolerance.message.CreateFaultTolerancePrimaryVmMsg;
import org.zstack.faulttolerance.message.CreateFaultTolerancePrimaryVmReply;
import org.zstack.faulttolerance.message.CreateFaultToleranceVmMsg;
import org.zstack.faulttolerance.message.CreateFaultToleranceVmReply;
import org.zstack.faulttolerance.message.FailoverFaultToleranceVmMsg;
import org.zstack.faulttolerance.message.FailoverFaultToleranceVmReply;
import org.zstack.faulttolerance.message.FailoverPrimaryVmMsg;
import org.zstack.faulttolerance.message.FailoverPrimaryVmReply;
import org.zstack.faulttolerance.message.FailoverVmOverlayMsg;
import org.zstack.faulttolerance.message.FaultToleranceGCOverlayMsg;
import org.zstack.faulttolerance.message.FaultToleranceVmGCMsg;
import org.zstack.faulttolerance.message.FaultToleranceVmGCReply;
import org.zstack.faulttolerance.message.RecoverFailedVmInstanceMsg;
import org.zstack.faulttolerance.message.RecoverFailedVmInstanceReply;
import org.zstack.faulttolerance.message.StartFaultToleranceVmMsg;
import org.zstack.faulttolerance.message.StartFaultToleranceVmReply;
import org.zstack.ha.HaSystemTags;
import org.zstack.ha.VmHaLevel;
import org.zstack.header.AbstractService;
import org.zstack.header.HasThreadContext;
import org.zstack.header.allocator.HostAllocatorConstant;
import org.zstack.header.allocator.ReturnHostCapacityMsg;
import org.zstack.header.cluster.ClusterInventory;
import org.zstack.header.cluster.MiniClusterExtensionPoint;
import org.zstack.header.configuration.DiskOfferingState;
import org.zstack.header.configuration.DiskOfferingVO;
import org.zstack.header.configuration.InstanceOfferingVO;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ExceptionSafe;
import org.zstack.header.core.NoErrorCompletion;
import org.zstack.header.core.NopeNoErrorCompletion;
import org.zstack.header.core.NopeWhileDoneCompletion;
import org.zstack.header.core.PaginateCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.StaticInit;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.Flow;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.core.workflow.FlowDoneHandler;
import org.zstack.header.core.workflow.FlowErrorHandler;
import org.zstack.header.core.workflow.FlowRollback;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.AllocateHostPortMsg;
import org.zstack.header.host.AllocateHostPortReply;
import org.zstack.header.host.ConfigPrimaryVmMsg;
import org.zstack.header.host.ConfigSecondaryVmMsg;
import org.zstack.header.host.CpuArchitecture;
import org.zstack.header.host.HostConstant;
import org.zstack.header.host.HostPortVO;
import org.zstack.header.host.HostPortVO_;
import org.zstack.header.host.HostState;
import org.zstack.header.host.HostStatus;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.host.RegisterColoPrimaryCheckMsg;
import org.zstack.header.host.StartColoSyncMsg;
import org.zstack.header.host.VmNicRedirectConfig;
import org.zstack.header.image.ImagePlatform;
import org.zstack.header.image.ImageVO;
import org.zstack.header.image.ImageVO_;
import org.zstack.header.log.NoLogging;
import org.zstack.header.managementnode.ManagementNodeReadyExtensionPoint;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO_;
import org.zstack.header.rest.RESTFacade;
import org.zstack.header.rest.SyncHttpCallHandler;
import org.zstack.header.storage.backup.VolumeBackupVO;
import org.zstack.header.storage.backup.VolumeBackupVO_;
import org.zstack.header.storage.primary.PrimaryStorageConstant;
import org.zstack.header.tag.SystemTagInventory;
import org.zstack.header.tag.SystemTagLifeCycleListener;
import org.zstack.header.vm.ChangeVmStateMsg;
import org.zstack.header.vm.DestroyVmInstanceMsg;
import org.zstack.header.vm.InstantiateNewCreatedVmInstanceMsg;
import org.zstack.header.vm.InstantiateNewCreatedVmInstanceReply;
import org.zstack.header.vm.StartVmInstanceMsg;
import org.zstack.header.vm.StartVmInstanceReply;
import org.zstack.header.vm.StopVmInstanceMsg;
import org.zstack.header.vm.VmCanonicalEvents;
import org.zstack.header.vm.VmCreationStrategy;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.header.vm.VmInstanceDeletionPolicyManager;
import org.zstack.header.vm.VmInstanceInventory;
import org.zstack.header.vm.VmInstanceSequenceNumberVO;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmInstanceStateEvent;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmInstanceVO_;
import org.zstack.header.vm.VmNicInventory;
import org.zstack.header.vm.VmNicSpec;
import org.zstack.header.vm.VmNicVO;
import org.zstack.header.vm.VmNicVO_;
import org.zstack.header.volume.VolumeInventory;
import org.zstack.header.volume.VolumeType;
import org.zstack.identity.AccountManager;
import org.zstack.kvm.KVMAgentCommands;
import org.zstack.kvm.KVMConstant;
import org.zstack.kvm.KVMHostAsyncHttpCallMsg;
import org.zstack.kvm.KVMHostAsyncHttpCallReply;
import org.zstack.kvm.KVMHostConnectExtensionPoint;
import org.zstack.kvm.KVMHostConnectedContext;
import org.zstack.kvm.KVMHostInventory;
import org.zstack.kvm.KVMHostVO;
import org.zstack.kvm.VolumeTO;
import org.zstack.mevoco.DeployMode;
import org.zstack.mevoco.MevocoGlobalProperty;
import org.zstack.mevoco.MevocoSystemTags;
import org.zstack.resourceconfig.ResourceConfig;
import org.zstack.resourceconfig.ResourceConfigFacade;
import org.zstack.tag.SystemTagCreator;
import org.zstack.tag.SystemTagUtils;
import org.zstack.tag.TagManager;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.NetworkUtils;

import javax.persistence.Tuple;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.operr;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

public class FaultToleranceManagerImpl extends AbstractService implements FaultToleranceManager,
        KVMHostConnectExtensionPoint, ManagementNodeReadyExtensionPoint, ExternalBackupExtensionPoint,
        MiniClusterExtensionPoint {
    private static final CLogger logger = Utils.getLogger(FaultToleranceManagerImpl.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;
    @Autowired
    private RESTFacade restf;
    @Autowired
    private AccountManager accountManager;
    @Autowired
    private ThreadFacade thdf;
    @Autowired
    private TagManager tagMgr;
    @Autowired
    private EventFacade evtf;
    @Autowired
    private ResourceDestinationMaker destinationMaker;
    @Autowired
    private ResourceConfigFacade rcf;
    @Autowired
    private FaultToleranceFactory factory;

    private static final List<String> needChangeVmStatusPrimaryVmState = Arrays.asList(VmInstanceState.Stopped.toString(),
            VmInstanceState.Destroyed.toString(), VmInstanceState.Running.toString(),
            VmInstanceState.Rebooting.toString(), VmInstanceState.Starting.toString(),
            VmInstanceState.Destroying.toString(), VmInstanceState.Unknown.toString());

    private static final List<String> needChangeVmStatusSecondaryVmState = Arrays.asList(VmInstanceState.Stopped.toString(),
            VmInstanceState.Stopping.toString(),
            VmInstanceState.Unknown.toString(), VmInstanceState.Running.toString());
    
    private static final List<String> ignoredPrimaryVmStateForFTStatus = Arrays.asList(VmInstanceState.Destroying.toString(),
            VmInstanceState.Destroyed.toString(),
            VmInstanceState.Starting.toString(),
            VmInstanceState.Stopping.toString());

    public static final String DEPLOY_COLO_QEMU_PATH = "/deploy/colo/qemu";
    public static final String CHECK_COLO_VM_STATE_PATH = "/check/colo/vm/state";
    public static final String WAIT_COLO_VM_READY_PATH = "/wait/colo/vm/ready";
    public static final String ROLLBACK_QUORUM_CONFIG_PATH = "/rollback/quorum/config";
    public static final String FAIL_COLO_PVM_PATH = "/fail/colo/pvm";
    public static final String SETUP_SELF_FENCER_PATH = "/ft/selffencer/setup";

    private static final String FT_TAG_FORMAT = HaSystemTags.HA.instantiateTag(map(
            e(HaSystemTags.HA_TOKEN, VmHaLevel.FaultTolerance.toString())
    ));

    @Override
    public void afterCreateMiniCluster(ClusterInventory cluster) {
        List<KVMHostAsyncHttpCallMsg> messages = new ArrayList<>();

        List<Tuple> hosts = Q.New(HostVO.class)
                .select(HostVO_.uuid, HostVO_.managementIp)
                .eq(HostVO_.clusterUuid, cluster.getUuid())
                .listTuple();

        String host1Uuid = (String) hosts.get(0).get(0);
        String host1ManagementIp = (String) hosts.get(0).get(1);
        String host1StorageIp = getHostFaultToleranceAddress(host1Uuid, host1ManagementIp);

        String host2Uuid = (String) hosts.get(1).get(0);
        String host2ManagementIp = (String) hosts.get(1).get(1);
        String host2StorageIp = getHostFaultToleranceAddress(host2Uuid, host2ManagementIp);

        SetupSelfFencerCmd cmd = new SetupSelfFencerCmd();
        cmd.hostUuid = host1Uuid;
        cmd.hostManagementIp = host1ManagementIp;
        cmd.peerHostManagementNetworkIp = host2ManagementIp;
        cmd.peerHostStorageNetworkIp = host2StorageIp;

        KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
        msg.setHostUuid(host1Uuid);
        msg.setCommand(cmd);
        msg.setNoStatusCheck(false);
        msg.setPath(SETUP_SELF_FENCER_PATH);
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, host1Uuid);
        messages.add(msg);

        cmd = new SetupSelfFencerCmd();
        cmd.hostUuid = host2Uuid;
        cmd.hostManagementIp = host2ManagementIp;
        cmd.peerHostManagementNetworkIp = host1ManagementIp;
        cmd.peerHostStorageNetworkIp = host1StorageIp;

        msg = new KVMHostAsyncHttpCallMsg();
        msg.setHostUuid(host2Uuid);
        msg.setCommand(cmd);
        msg.setNoStatusCheck(false);
        msg.setPath(SETUP_SELF_FENCER_PATH);
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, host1Uuid);
        messages.add(msg);

        new While<>(messages).step((message, innerWhileCompletion) -> bus.send(message, new CloudBusCallBack(innerWhileCompletion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    logger.debug(String.format("Failed to setup ft vm fencer on host[uuid:%s]", message.getHostUuid()));
                }

                innerWhileCompletion.done();
            }
        }), 10).run(new NopeWhileDoneCompletion());
    }

    public static class DeployColoQemuCmd extends KVMAgentCommands.AgentCommand implements HasThreadContext, Serializable {
        @NoLogging
        @GrayVersion(value = "5.0.0")
        private String qemuUrl;

        public String getQemuUrl() {
            return qemuUrl;
        }

        public void setQemuUrl(String qemuUrl) {
            this.qemuUrl = qemuUrl;
        }
    }

    public static class SetupSelfFencerCmd extends KVMAgentCommands.AgentCommand {
        @GrayVersion(value = "5.0.0")
        public String hostUuid;
        @GrayVersion(value = "5.0.0")
        public String peerHostManagementNetworkIp;
        @GrayVersion(value = "5.0.0")
        public String hostManagementIp;
        @GrayVersion(value = "5.0.0")
        public String peerHostStorageNetworkIp;
    }

    public static class RollbackQuorumConfigCmd extends KVMAgentCommands.AgentCommand {
        @GrayVersion(value = "5.0.0")
        private String vmInstanceUuid;
        @GrayVersion(value = "5.0.0")
        private List<VolumeTO> volumes;
        @GrayVersion(value = "5.0.0")
        private Integer nicNumber;

        public String getVmInstanceUuid() {
            return vmInstanceUuid;
        }

        public void setVmInstanceUuid(String vmInstanceUuid) {
            this.vmInstanceUuid = vmInstanceUuid;
        }

        public List<VolumeTO> getVolumes() {
            return volumes;
        }

        public void setVolumes(List<VolumeTO> volumes) {
            this.volumes = volumes;
        }

        public Integer getNicNumber() {
            return nicNumber;
        }

        public void setNicNumber(Integer nicNumber) {
            this.nicNumber = nicNumber;
        }
    }

    public static class WaitColoVmReadyCmd extends KVMAgentCommands.AgentCommand {
        @GrayVersion(value = "5.0.0")
        private String vmInstanceUuid;
        @GrayVersion(value = "5.0.0")
        private String hostUuid;
        @GrayVersion(value = "5.0.0")
        private long coloCheckTimeout;

        public String getVmInstanceUuid() {
            return vmInstanceUuid;
        }

        public void setVmInstanceUuid(String vmInstanceUuid) {
            this.vmInstanceUuid = vmInstanceUuid;
        }

        public String getHostUuid() {
            return hostUuid;
        }

        public void setHostUuid(String hostUuid) {
            this.hostUuid = hostUuid;
        }

        public long getColoCheckTimeout() {
            return coloCheckTimeout;
        }

        public void setColoCheckTimeout(long coloCheckTimeout) {
            this.coloCheckTimeout = coloCheckTimeout;
        }
    }

    public static class CheckColoVmStateRsp extends KVMAgentCommands.AgentResponse {
        @GrayVersion(value = "5.0.0")
        private String state;
        @GrayVersion(value = "5.0.0")
        private String currentMode;

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getCurrentMode() {
            return currentMode;
        }

        public void setCurrentMode(String currentMode) {
            this.currentMode = currentMode;
        }
    }

    public static class FailColoPrimaryVmCmd extends KVMAgentCommands.AgentCommand implements Serializable {
        @GrayVersion(value = "5.0.0")
        private String vmInstanceUuid;
        @GrayVersion(value = "5.0.0")
        private String targetHostIp;
        @GrayVersion(value = "5.0.0")
        private Integer targetHostPort;
        @NoLogging
        @GrayVersion(value = "5.0.0")
        private String targetHostPassword;

        public String getVmInstanceUuid() {
            return vmInstanceUuid;
        }

        public void setVmInstanceUuid(String vmInstanceUuid) {
            this.vmInstanceUuid = vmInstanceUuid;
        }

        public String getTargetHostIp() {
            return targetHostIp;
        }

        public void setTargetHostIp(String targetHostIp) {
            this.targetHostIp = targetHostIp;
        }

        public Integer getTargetHostPort() {
            return targetHostPort;
        }

        public void setTargetHostPort(Integer targetHostPort) {
            this.targetHostPort = targetHostPort;
        }

        public String getTargetHostPassword() {
            return targetHostPassword;
        }

        public void setTargetHostPassword(String targetHostPassword) {
            this.targetHostPassword = targetHostPassword;
        }
    }

    public static class CheckColoVmStateCmd extends KVMAgentCommands.AgentCommand {
        @GrayVersion(value = "5.0.0")
        private String vmInstanceUuid;
        @GrayVersion(value = "5.0.0")
        private String hostUuid;

        public String getVmInstanceUuid() {
            return vmInstanceUuid;
        }

        public void setVmInstanceUuid(String vmInstanceUuid) {
            this.vmInstanceUuid = vmInstanceUuid;
        }

        public String getHostUuid() {
            return hostUuid;
        }

        public void setHostUuid(String hostUuid) {
            this.hostUuid = hostUuid;
        }
    }

    private List<String> createSecondaryVmWorkFlow;
    private FlowChainBuilder createSecondaryVmWorkFlowBuilder;

    private List<String> startFaultToleranceVmGroupWorkFlowElements;
    private FlowChainBuilder startFaultToleranceVmGroupWorkFlowBuilder;

    private List<String> destroyFaultToleranceVmGroupWorkFlowElements;
    private FlowChainBuilder destroyFaultToleranceVmGroupWorkFlowBuilder;

    public Future<Void> faultToleranceVmScanTracker = null;

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof APIMessage) {
            handle((APIMessage) msg);
        } else {
            handleLocalMessage(msg);
        }
    }

    private void handleLocalMessage(Message msg) {
        if (msg instanceof CreateFaultToleranceVmMsg) {
            handle((CreateFaultToleranceVmMsg) msg);
        } else if (msg instanceof StartFaultToleranceVmMsg) {
            handle((StartFaultToleranceVmMsg) msg);
        } else if (msg instanceof RecoverFailedVmInstanceMsg) {
            handle((RecoverFailedVmInstanceMsg) msg);
        } else if (msg instanceof FailoverPrimaryVmMsg) {
            handle((FailoverPrimaryVmMsg) msg);
        } else if (msg instanceof ChangeFaultToleranceVmStatusMsg) {
            handle((ChangeFaultToleranceVmStatusMsg) msg);
        } else if (msg instanceof FaultToleranceVmGCMsg) {
            handle((FaultToleranceVmGCMsg) msg);
        } else if (msg instanceof CreateFaultTolerancePrimaryVmMsg) {
            handle((CreateFaultTolerancePrimaryVmMsg) msg);
        } else if (msg instanceof FailoverFaultToleranceVmMsg) {
            handle((FailoverFaultToleranceVmMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(CreateFaultTolerancePrimaryVmMsg msg) {
        CreateFaultTolerancePrimaryVmReply createFaultTolerancePrimaryVmReply = new CreateFaultTolerancePrimaryVmReply();

        FlowChain chain = new ShareFlowChain();
        chain.then(new ShareFlow() {
            FaultToleranceVmStarter starter = new FaultToleranceVmStarter();

            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "create-fault-tolerance-primary-vm";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        VmInstanceVO pvm = persistVmInstance(msg, msg.getResourceUuid());
                        starter.primaryVm = VmInstanceInventory.valueOf(pvm);

                        SystemTagCreator creator = HaSystemTags.HA.newSystemTagCreator(starter.primaryVm.getUuid());
                        creator.setTagByTokens(map(e(HaSystemTags.HA_TOKEN, VmHaLevel.FaultTolerance)));
                        creator.ignoreIfExisting = true;
                        creator.create();

                        FaultToleranceVmGroupVO faultToleranceVmGroupVO = generateFaultToleranceVmVO(pvm, null);

                        InstantiateNewCreatedVmInstanceMsg cmsg = new InstantiateNewCreatedVmInstanceMsg();
                        cmsg.setHostUuid(msg.getHostUuid());
                        cmsg.setVmInstanceInventory(starter.primaryVm);
                        cmsg.setPrimaryStorageUuidForRootVolume(msg.getPrimaryStorageUuidForRootVolume());
                        cmsg.setStrategy(VmCreationStrategy.InstantStart.toString());
                        cmsg.setTimeout(msg.getTimeout());
                        cmsg.setRootVolumeSystemTags(msg.getRootVolumeSystemTags());
                        cmsg.setDataVolumeSystemTags(msg.getDataVolumeSystemTags());
                        cmsg.setL3NetworkUuids(getVmNicSpecsFromAPICreateVmInstanceMsg(msg.getSystemTags(), msg.getL3NetworkUuids()));
                        bus.makeTargetServiceIdByResourceUuid(cmsg, VmInstanceConstant.SERVICE_ID, cmsg.getVmInstanceUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(starter.primaryVm.getUuid());

                                    if (faultToleranceVmGroupVO != null) {
                                        FaultToleranceVmInstanceGroupHostPortRefVO ref = Q.New(FaultToleranceVmInstanceGroupHostPortRefVO.class)
                                                .eq(FaultToleranceVmInstanceGroupHostPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                                .find();

                                        if (ref != null) {
                                            SQL.New(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(
                                                    ref.getNbdServerPortId(),
                                                    ref.getBlockReplicationPortId(),
                                                    ref.getSecondaryVmMonitorPortId(),
                                                    ref.getPrimaryVmMonitorPortId())).delete();

                                            dbf.remove(ref);
                                        }

                                        List<VmInstanceVmNicRedirectPortRefVO> redirectPortRefVOS = Q.New(VmInstanceVmNicRedirectPortRefVO.class)
                                                .eq(VmInstanceVmNicRedirectPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                                .list();

                                        for (VmInstanceVmNicRedirectPortRefVO nicRedirectPortRefVO : redirectPortRefVOS) {
                                            SQL.New(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(nicRedirectPortRefVO.getMirrorPortId(),
                                                    nicRedirectPortRefVO.getPrimaryInPortId(),
                                                    nicRedirectPortRefVO.getSecondaryInPortId(),
                                                    nicRedirectPortRefVO.getPrimaryOutPortId())).delete();
                                            dbf.remove(nicRedirectPortRefVO);
                                        }

                                        dbf.remove(faultToleranceVmGroupVO);
                                    }

                                    trigger.fail(reply.getError());
                                    return;
                                }

                                InstantiateNewCreatedVmInstanceReply r = reply.castReply();
                                starter.primaryVm = r.getVmInventory();
                                createFaultTolerancePrimaryVmReply.setPrimaryVmInventory(starter.primaryVm);

                                VmInstanceVO vo = dbf.findByUuid(r.getVmInventory().getUuid(), VmInstanceVO.class);
                                vo.setLastHostUuid(r.getVmInventory().getHostUuid());
                                vo.setHostUuid(r.getVmInventory().getHostUuid());
                                vo.setClusterUuid(r.getVmInventory().getClusterUuid());
                                vo.setZoneUuid(r.getVmInventory().getZoneUuid());
                                vo.setHypervisorType(r.getVmInventory().getHypervisorType());
                                vo.setRootVolumeUuid(r.getVmInventory().getRootVolumeUuid());
                                vo.setState(VmInstanceState.Running);
                                dbf.updateAndRefresh(vo);

                                // move primary vm nics to ft group
                                SQL.New(VmNicVO.class).eq(VmNicVO_.vmInstanceUuid, pvm.getUuid()).set(VmNicVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid()).update();

                                trigger.next();
                            }
                        });
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        if (starter.primaryVm != null && dbf.isExist(starter.primaryVm.getUuid(), VmInstanceVO.class)) {
                            FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(starter.primaryVm.getUuid());

                            if (faultToleranceVmGroupVO != null) {
                                FaultToleranceVmInstanceGroupHostPortRefVO ref = Q.New(FaultToleranceVmInstanceGroupHostPortRefVO.class)
                                        .eq(FaultToleranceVmInstanceGroupHostPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                        .find();

                                if (ref != null) {
                                    SQL.New(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(
                                            ref.getNbdServerPortId(),
                                            ref.getBlockReplicationPortId(),
                                            ref.getSecondaryVmMonitorPortId(),
                                            ref.getPrimaryVmMonitorPortId())).delete();

                                    dbf.remove(ref);
                                }

                                List<VmInstanceVmNicRedirectPortRefVO> redirectPortRefVOS = Q.New(VmInstanceVmNicRedirectPortRefVO.class)
                                        .eq(VmInstanceVmNicRedirectPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                        .list();

                                for (VmInstanceVmNicRedirectPortRefVO nicRedirectPortRefVO : redirectPortRefVOS) {
                                    SQL.New(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(nicRedirectPortRefVO.getMirrorPortId(),
                                            nicRedirectPortRefVO.getPrimaryInPortId(),
                                            nicRedirectPortRefVO.getSecondaryInPortId(),
                                            nicRedirectPortRefVO.getPrimaryOutPortId())).delete();
                                    dbf.remove(nicRedirectPortRefVO);
                                }
                            }

                            DestroyVmInstanceMsg dmsg = new DestroyVmInstanceMsg();
                            dmsg.setVmInstanceUuid(starter.primaryVm.getUuid());
                            dmsg.setDeletionPolicy(VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.Direct);
                            bus.makeTargetServiceIdByResourceUuid(dmsg, VmInstanceConstant.SERVICE_ID, dmsg.getVmInstanceUuid());
                            bus.send(dmsg);

                            trigger.rollback();
                            return;
                        }

                        if (starter.secondaryVm == null) {
                            trigger.rollback();
                            return;
                        }

                        VmInstanceVO svm = dbf.findByUuid(starter.secondaryVm.getUuid(), VmInstanceVO.class);
                        dbf.remove(svm);

                        trigger.rollback();
                    }
                });

                flow(new NoRollbackFlow() {
                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        CreateFaultToleranceVmMsg cmsg = new CreateFaultToleranceVmMsg();
                        cmsg.setFromApiMessage(true);
                        cmsg.setPrimaryVmInstanceUuid(starter.primaryVm.getUuid());
                        String hostUuid = Q.New(VmInstanceVO.class).select(VmInstanceVO_.hostUuid).eq(VmInstanceVO_.uuid, cmsg.getPrimaryVmInstanceUuid()).findValue();

                        if (hostUuid == null) {
                            trigger.fail(operr("can not create secondary vm, because primary vm is stopped"));
                            return;
                        }

                        bus.makeTargetServiceIdByResourceUuid(cmsg, FaultToleranceManager.SERVICE_ID, hostUuid);
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (!reply.isSuccess()) {
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                createFaultTolerancePrimaryVmReply.setSecondaryVmInventory(((CreateFaultToleranceVmReply) reply).getInventory());
                                trigger.next();
                            }
                        });
                    }

                });

            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                createFaultTolerancePrimaryVmReply.setError(errCode);
                bus.reply(msg, createFaultTolerancePrimaryVmReply);
            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                FaultToleranceVmGroupVO group = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(createFaultTolerancePrimaryVmReply.getPrimaryVmInventory().getUuid());
                createFaultTolerancePrimaryVmReply.setFaultToleranceVmGroupInventory(FaultToleranceVmGroupInventory.valueOf(group));
                bus.reply(msg, createFaultTolerancePrimaryVmReply);
            }
        }).start();
    }

    private void handle(FaultToleranceVmGCMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("fault-tolerance-gc-for-ft-vm-group-%s", msg.getFaultToleranceVmGroupUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                FaultToleranceVmGCReply reply = new FaultToleranceVmGCReply();
                doFaultToleranceVmGC(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            protected int getMaxPendingTasks() {
                return 1;
            }

            @Override
            protected void exceedMaxPendingCallback() {
                FaultToleranceVmGCReply reply = new FaultToleranceVmGCReply();
                reply.setError(operr("an other fault tolerance gc task is running, cancel the new task and wait return"));
                bus.reply(msg, reply);
            }

            @Override
            protected String getDeduplicateString() {
                return getSyncSignature();
            }

            @Override
            public String getName() {
                return String.format("fault-tolerance-gc-for-ft-vm-group-%s", msg.getFaultToleranceVmGroupUuid());
            }
        });
    }

    private void doFaultToleranceVmGC(FaultToleranceVmGCMsg msg, Completion completion) {
        FaultToleranceVmGroupVO group = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getFaultToleranceVmGroupUuid());

        VmInstanceState primaryVmState = Q.New(VmInstanceVO.class).select(VmInstanceVO_.state)
                .eq(VmInstanceVO_.uuid, group.getPrimaryVmInstanceUuid()).findValue();

        if (primaryVmState == VmInstanceState.Stopped) {
            logger.debug("ft vm group is not running, try to start it");
            StartVmInstanceMsg smsg = new StartVmInstanceMsg();
            smsg.setVmInstanceUuid(group.getPrimaryVmInstanceUuid());
            bus.makeTargetServiceIdByResourceUuid(smsg, VmInstanceConstant.SERVICE_ID, smsg.getVmInstanceUuid());
            bus.send(smsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    completion.success();
                }
            });
            return;
        }

        VmInstanceVO svm = FaultToleranceUtils.getSecondaryVmByPrimaryVm(group.getPrimaryVmInstanceUuid());
        if (svm != null) {
            if (svm.getState() != VmInstanceState.Stopped) {
                logger.debug(String.format("secondary vm[uuid:%s] state exists but not stopped, skip this gc operation", svm.getUuid()));
                completion.success();
                return;
            }
            logger.debug(String.format("secondary vm[uuid:%s] exists, FaultToleranceVmGC try to start it", svm.getUuid()));
            StartFaultToleranceVmMsg smsg = new StartFaultToleranceVmMsg();
            smsg.setPrimaryVmInstanceUuid(group.getPrimaryVmInstanceUuid());
            smsg.setSecondaryVmInstanceUuid(group.getSecondaryVmInstanceUuid());

            String hostUuid = Q.New(VmInstanceVO.class).select(VmInstanceVO_.hostUuid).eq(VmInstanceVO_.uuid, group.getPrimaryVmInstanceUuid()).findValue();

            if (hostUuid == null) {
                completion.fail(operr("can not start secondary vm, because primary vm is stopped"));
                return;
            }

            bus.makeTargetServiceIdByResourceUuid(smsg, FaultToleranceManager.SERVICE_ID, hostUuid);

            FaultToleranceGCOverlayMsg omsg = new FaultToleranceGCOverlayMsg();
            omsg.setVmInstanceUuid(group.getPrimaryVmInstanceUuid());
            omsg.setMessage(smsg);
            bus.makeTargetServiceIdByResourceUuid(omsg, VmInstanceConstant.SERVICE_ID, omsg.getVmInstanceUuid());
            bus.send(omsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    completion.success();
                }
            });
        } else {
            logger.debug("secondary vm not exists, FaultToleranceVmGC try to create it");
            CreateFaultToleranceVmMsg cmsg = new CreateFaultToleranceVmMsg();
            cmsg.setPrimaryVmInstanceUuid(group.getPrimaryVmInstanceUuid());

            String hostUuid = Q.New(VmInstanceVO.class).select(VmInstanceVO_.hostUuid).eq(VmInstanceVO_.uuid, group.getPrimaryVmInstanceUuid()).findValue();
            bus.makeTargetServiceIdByResourceUuid(cmsg, FaultToleranceManager.SERVICE_ID, hostUuid);

            if (hostUuid == null) {
                completion.fail(operr("can not create secondary vm, because primary vm is stopped"));
                return;
            }

            FaultToleranceGCOverlayMsg omsg = new FaultToleranceGCOverlayMsg();
            omsg.setVmInstanceUuid(group.getPrimaryVmInstanceUuid());
            omsg.setMessage(cmsg);
            bus.makeTargetServiceIdByResourceUuid(omsg, VmInstanceConstant.SERVICE_ID, omsg.getVmInstanceUuid());
            bus.send(omsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    completion.success();
                }
            });
        }
    }

    private void handle(ChangeFaultToleranceVmStatusMsg msg) {
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("change-ft-vm-%s-status", msg.getPrimaryVmInstanceUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                ChangeFaultToleranceVmStatusReply reply = new ChangeFaultToleranceVmStatusReply();

                changeFaultToleranceVmStatus(msg.getPrimaryVmInstanceUuid());
                bus.reply(msg, reply);
                chain.next();
            }

            @Override
            public String getName() {
                return getSyncSignature();
            }
        });
    }

    private void changeFaultToleranceVmStatus(String primaryVmInstanceUuid) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(primaryVmInstanceUuid);

                if (faultToleranceVmGroupVO == null) {
                    logger.debug("No ft vm group found");
                    return;
                }

                FaultToleranceStatus fromStatus = faultToleranceVmGroupVO.getStatus();

                VmInstanceVO pvm = findByUuid(primaryVmInstanceUuid, VmInstanceVO.class);
                VmInstanceState primaryVmState = pvm.getState();
                if (faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() == null) {
                    UpdateQuery updateQuery = sql(FaultToleranceVmGroupVO.class)
                            .eq(FaultToleranceVmGroupVO_.uuid, faultToleranceVmGroupVO.getUuid());
                    logger.debug("No secondary vm for vm[uuid:%s] was found");
                    if (!ignoredPrimaryVmStateForFTStatus.contains(primaryVmState.toString())) {
                        updateQuery.set(FaultToleranceVmGroupVO_.state, primaryVmState);
                    }

                    FaultToleranceStatus toStatus = getFaultToleranceStatusFromVmStates(primaryVmState, null);
                    updateQuery.set(FaultToleranceVmGroupVO_.status, toStatus)
                            .set(FaultToleranceVmGroupVO_.hostUuid, pvm.getHostUuid())
                            .set(FaultToleranceVmGroupVO_.clusterUuid, pvm.getClusterUuid())
                            .set(FaultToleranceVmGroupVO_.lastHostUuid, pvm.getLastHostUuid())
                            .update();

                    logger.debug(String.format("Update fault tolerance vm group[uuid:%s] status from %s to %s", faultToleranceVmGroupVO.getUuid(), fromStatus, toStatus));
                    return;
                }

                VmInstanceVO svm = findByUuid(faultToleranceVmGroupVO.getSecondaryVmInstanceUuid(), VmInstanceVO.class);
                if (svm == null) {
                    sql(FaultToleranceVmGroupVO.class)
                            .eq(FaultToleranceVmGroupVO_.uuid, faultToleranceVmGroupVO.getUuid())
                            .set(FaultToleranceVmGroupVO_.status, getFaultToleranceStatusFromVmStates(primaryVmState, null))
                            .set(FaultToleranceVmGroupVO_.hostUuid, pvm.getHostUuid())
                            .set(FaultToleranceVmGroupVO_.clusterUuid, pvm.getClusterUuid())
                            .set(FaultToleranceVmGroupVO_.lastHostUuid, pvm.getLastHostUuid())
                            .update();
                    return;
                }

                FaultToleranceStatus originStatus = faultToleranceVmGroupVO.getStatus();
                FaultToleranceStatus currentStatus = getFaultToleranceStatusFromVmStates(primaryVmState, svm.getState());

                UpdateQuery updateQuery = sql(FaultToleranceVmGroupVO.class)
                        .eq(FaultToleranceVmGroupVO_.uuid, faultToleranceVmGroupVO.getUuid());
                if (currentStatus != null) {
                    updateQuery.set(FaultToleranceVmGroupVO_.status, currentStatus);
                }

                if (!ignoredPrimaryVmStateForFTStatus.contains(primaryVmState.toString())) {
                    updateQuery.set(FaultToleranceVmGroupVO_.state, primaryVmState);
                }

                updateQuery.set(FaultToleranceVmGroupVO_.hostUuid, pvm.getHostUuid());
                updateQuery.set(FaultToleranceVmGroupVO_.clusterUuid, pvm.getClusterUuid());
                updateQuery.set(FaultToleranceVmGroupVO_.lastHostUuid, pvm.getLastHostUuid());
                updateQuery.update();

                logger.debug(String.format("Change fault tolerance vm[uuid:%s] status from %s to %s, state to %s",
                        primaryVmInstanceUuid, originStatus, currentStatus != null ? currentStatus : originStatus, faultToleranceVmGroupVO.getState()));
            }
        }.execute();

    }

    private FaultToleranceStatus getFaultToleranceStatusFromVmStates(VmInstanceState primaryVmInstanceState, VmInstanceState secondaryVmInstanceState) {
        logger.debug("primary vm state: " + primaryVmInstanceState + "secondary vm state: " + secondaryVmInstanceState);

        if (secondaryVmInstanceState == null) {
            if (primaryVmInstanceState.equals(VmInstanceState.Running) || primaryVmInstanceState.equals(VmInstanceState.Paused)) {
                return FaultToleranceStatus.Stopped;
            } else if (primaryVmInstanceState.equals(VmInstanceState.Unknown)) {
                return FaultToleranceStatus.Unknown;
            } else {
                return FaultToleranceStatus.Unprotected;
            }
        }

        if ((primaryVmInstanceState.equals(VmInstanceState.Running) || primaryVmInstanceState.equals(VmInstanceState.Paused))
                && (secondaryVmInstanceState.equals(VmInstanceState.Stopped) || secondaryVmInstanceState.equals(VmInstanceState.Stopping))) {
            return FaultToleranceStatus.Stopped;
        } else if ((primaryVmInstanceState.equals(VmInstanceState.Running) || primaryVmInstanceState.equals(VmInstanceState.Paused))
                && secondaryVmInstanceState.equals(VmInstanceState.Starting)) {
            return FaultToleranceStatus.SecondaryVmStarting;
        } else if ((primaryVmInstanceState.equals(VmInstanceState.Running) || primaryVmInstanceState.equals(VmInstanceState.Paused))
                && (secondaryVmInstanceState.equals(VmInstanceState.Running) || secondaryVmInstanceState.equals(VmInstanceState.Paused))) {
            return FaultToleranceStatus.Protected;
        } else if (primaryVmInstanceState.equals(VmInstanceState.Unknown) || secondaryVmInstanceState.equals(VmInstanceState.Unknown)) {
            return FaultToleranceStatus.Unknown;
        } else {
            return FaultToleranceStatus.Unprotected;
        }
    }

    private void handle(FailoverPrimaryVmMsg msg) {
        FailoverPrimaryVmReply reply = new FailoverPrimaryVmReply();

        class FaultToleranceVmBundle {
            String primaryVmInstanceUuid;
            String secondaryVmInstanceUuid;
        }

        FaultToleranceVmBundle bundle = new FaultToleranceVmBundle();

        Set<Long> hostPortIds = new HashSet<>();
        FlowChain chain = new SimpleFlowChain();
        chain.setName("fail-over-primary-vm");
        chain.then(new NoRollbackFlow() {
            String __name__ = "swap-primary-vm-and-secondary-vm";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                FaultToleranceVmGroupVO faultToleranceVmGroupVO = new SQLBatchWithReturn<FaultToleranceVmGroupVO>() {
                    @Override
                    protected FaultToleranceVmGroupVO scripts() {
                        FaultToleranceVmGroupVO faultToleranceVmGroupVO = q(FaultToleranceVmGroupVO.class)
                                .eq(FaultToleranceVmGroupVO_.primaryVmInstanceUuid, msg.getPrimaryVmInstanceUuid())
                                .find();

                        if (faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() == null) {
                            logger.debug("current secondary vm is null, skip vm swap");
                            return faultToleranceVmGroupVO;
                        }

                        logger.debug("before swap: current primary vm uuid is " + faultToleranceVmGroupVO.getPrimaryVmInstanceUuid() + " current secondary vm uuid is " + faultToleranceVmGroupVO.getSecondaryVmInstanceUuid());

                        String originPrimaryVmInstanceUuid = faultToleranceVmGroupVO.getPrimaryVmInstanceUuid();
                        sql(FaultToleranceVmGroupVO.class)
                                .eq(FaultToleranceVmGroupVO_.uuid, faultToleranceVmGroupVO.getUuid())
                                .set(FaultToleranceVmGroupVO_.primaryVmInstanceUuid, faultToleranceVmGroupVO.getSecondaryVmInstanceUuid())
                                .set(FaultToleranceVmGroupVO_.secondaryVmInstanceUuid, originPrimaryVmInstanceUuid)
                                .update();

                        faultToleranceVmGroupVO = reload(faultToleranceVmGroupVO);

                        logger.debug("after swap: current primary vm uuid is " + faultToleranceVmGroupVO.getPrimaryVmInstanceUuid() + " current secondary vm uuid is " + faultToleranceVmGroupVO.getSecondaryVmInstanceUuid());

                        FaultToleranceVmInstanceGroupHostPortRefVO ref = q(FaultToleranceVmInstanceGroupHostPortRefVO.class)
                                .eq(FaultToleranceVmInstanceGroupHostPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                .find();

                        long originPrimaryVmMonitorPortId = ref.getPrimaryVmMonitorPortId();
                        ref.setPrimaryVmMonitorPortId(ref.getSecondaryVmMonitorPortId());
                        ref.setSecondaryVmMonitorPortId(originPrimaryVmMonitorPortId);

                        // need to delete last vm migration port due to primary vm fails
                        long reservedVmMigrationPort = ref.getReservedVmMigrationPortId();

                        ref.setReservedVmMigrationPortId(ref.getBlockReplicationPortId());
                        ref.setBlockReplicationPortId(0);

                        sql(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(
                                reservedVmMigrationPort,
                                ref.getBlockReplicationPortId(),
                                ref.getNbdServerPortId()))
                                .delete();
                        merge(ref);

                        List<VmInstanceVmNicRedirectPortRefVO> refs = q(VmInstanceVmNicRedirectPortRefVO.class)
                                .eq(VmInstanceVmNicRedirectPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                .list();
                        for (VmInstanceVmNicRedirectPortRefVO redirectPortRefVO : refs) {
                            hostPortIds.add(redirectPortRefVO.getMirrorPortId());
                            hostPortIds.add(redirectPortRefVO.getPrimaryInPortId());
                            hostPortIds.add(redirectPortRefVO.getSecondaryInPortId());
                            hostPortIds.add(redirectPortRefVO.getPrimaryOutPortId());
                            remove(redirectPortRefVO);
                        }

                        flush();

                        return reload(faultToleranceVmGroupVO);
                    }
                }.execute();

                bundle.primaryVmInstanceUuid = faultToleranceVmGroupVO.getPrimaryVmInstanceUuid();
                bundle.secondaryVmInstanceUuid = faultToleranceVmGroupVO.getSecondaryVmInstanceUuid();

                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "change-primary-volume-backups-to-secondary-vm";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (bundle.secondaryVmInstanceUuid == null) {
                    trigger.next();
                    return;
                }

                VmInstanceVO currentPrimaryVm = dbf.findByUuid(bundle.primaryVmInstanceUuid, VmInstanceVO.class);
                VmInstanceVO currentSecondaryVm = dbf.findByUuid(bundle.secondaryVmInstanceUuid, VmInstanceVO.class);

                SQL.New(VolumeBackupVO.class)
                        .eq(VolumeBackupVO_.volumeUuid, currentSecondaryVm.getRootVolumeUuid())
                        .set(VolumeBackupVO_.volumeUuid, currentPrimaryVm.getRootVolumeUuid())
                        .update();

                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "return-svm-preserved-memory";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(bundle.primaryVmInstanceUuid);

                if (faultToleranceVmGroupVO == null) {
                    logger.debug("missing ft vm group skip return capacity");
                    trigger.next();
                    return;
                }

                if (faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() == null) {
                    logger.debug("current secondary vm is null, skip vm swap");
                    trigger.next();
                    return;
                }

                // secondary vm need to reserve double size of primary vm as colo requested
                // recover secondary vm memory because primary vm failed
                // reference to http://jira.zstack.io/browse/MINI-2059
                VmInstanceVO pvm = dbf.findByUuid(faultToleranceVmGroupVO.getPrimaryVmInstanceUuid(), VmInstanceVO.class);
                pvm.setMemorySize(pvm.getMemorySize() / 2);
                pvm = dbf.updateAndRefresh(pvm);

                if (pvm.getState() == VmInstanceState.Stopped) {
                    logger.debug("primary vm is stopped, no need to return capacity");
                    trigger.next();
                    return;
                }

                ReturnHostCapacityMsg rmsg = new ReturnHostCapacityMsg();
                rmsg.setHostUuid(msg.getHostUuid());
                rmsg.setMemoryCapacity(pvm.getMemorySize());
                bus.makeTargetServiceIdByResourceUuid(rmsg, HostAllocatorConstant.SERVICE_ID, rmsg.getHostUuid());
                bus.send(rmsg);
                trigger.next();
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "clean-failed-vm";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(bundle.primaryVmInstanceUuid);

                if (faultToleranceVmGroupVO == null) {
                    logger.debug(String.format("missing ft vm group for vm %s skip clean up", bundle.primaryVmInstanceUuid));
                    trigger.next();
                    return;
                }

                if (faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() == null) {
                    trigger.next();
                    return;
                }

                String vmNeedBeDeleted = faultToleranceVmGroupVO.getSecondaryVmInstanceUuid();

                logger.debug("current primary vm uuid is " + faultToleranceVmGroupVO.getPrimaryVmInstanceUuid() + " current secondary vm uuid is " + faultToleranceVmGroupVO.getSecondaryVmInstanceUuid());

                faultToleranceVmGroupVO.setSecondaryVmInstanceUuid(null);
                dbf.updateAndRefresh(faultToleranceVmGroupVO);

                ChangeFaultToleranceVmStatusMsg cmsg = new ChangeFaultToleranceVmStatusMsg();
                cmsg.setPrimaryVmInstanceUuid(faultToleranceVmGroupVO.getPrimaryVmInstanceUuid());
                bus.makeTargetServiceIdByResourceUuid(cmsg, SERVICE_ID, cmsg.getPrimaryVmInstanceUuid());
                bus.send(cmsg);

                bus.reply(msg, reply);

                SystemTagCreator creator = FaultToleranceSystemTags.FT_ABANDONED_SECONDARY_VM.newSystemTagCreator(vmNeedBeDeleted);
                creator.inherent = false;
                creator.recreate = true;
                creator.create();

                DestroyVmInstanceMsg dmsg = new DestroyVmInstanceMsg();
                dmsg.setVmInstanceUuid(vmNeedBeDeleted);
                dmsg.setDeletionPolicy(VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.Direct);
                bus.makeTargetServiceIdByResourceUuid(dmsg, VmInstanceConstant.SERVICE_ID, dmsg.getVmInstanceUuid());
                bus.send(dmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format("Failed to destroy failed vm[uuid:%s]", vmNeedBeDeleted));
                        }

                        SQL.New(HostPortVO.class).in(HostPortVO_.id, hostPortIds).delete();

                        trigger.next();
                    }
                });
            }
        }).then(new NoRollbackFlow() {
            String __name__ = "create-new-secondary-vm";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(bundle.primaryVmInstanceUuid);

                if (faultToleranceVmGroupVO == null) {
                    logger.debug(String.format("missing ft vm group for vm %s  skip create new secondary vm", bundle.primaryVmInstanceUuid));
                    trigger.next();
                    return;
                }

                VmInstanceVO pvm = dbf.findByUuid(faultToleranceVmGroupVO.getPrimaryVmInstanceUuid(), VmInstanceVO.class);

                CreateFaultToleranceVmMsg cmsg = new CreateFaultToleranceVmMsg();
                cmsg.setPrimaryVmInstanceUuid(pvm.getUuid());

                String hostUuid = Q.New(VmInstanceVO.class).select(VmInstanceVO_.hostUuid).eq(VmInstanceVO_.uuid, cmsg.getPrimaryVmInstanceUuid()).findValue();

                if (hostUuid == null) {
                    logger.debug("current primary vm is stopped, no need to create fault tolerance secondary vm");
                    trigger.next();
                    return;
                }

                bus.makeTargetServiceIdByResourceUuid(cmsg, FaultToleranceManager.SERVICE_ID, hostUuid);
                bus.send(cmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug("Failed to create ft vm");

                            String gcName = FaultToleranceVmGC.getGCName(pvm.getUuid());
                            if (Q.New(GarbageCollectorVO.class)
                                    .eq(GarbageCollectorVO_.name, gcName)
                                    .notEq(GarbageCollectorVO_.status, GCStatus.Done)
                                    .isExists()) {
                                logger.debug(String.format("There is already a FaultToleranceVmGC on Vm[uuid:%s], skip.", pvm.getUuid()));
                                return;
                            }

                            FaultToleranceVmGC gc = new FaultToleranceVmGC();
                            gc.vm = VmInstanceInventory.valueOf(faultToleranceVmGroupVO);
                            gc.NAME = FaultToleranceVmGC.getGCName(faultToleranceVmGroupVO.getUuid());
                            gc.submit(FaultToleranceGlobalConfig.FAULT_TOLERANCE_RETRY_DELAY.value(Long.class), TimeUnit.SECONDS);
                        }
                    }
                });
                trigger.next();
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {

            }
        }).done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {

            }
        }).start();
    }

    private void handle(RecoverFailedVmInstanceMsg msg) {
        FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getVmInstanceUuid());
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("recover-failed-vm-instance-%s", faultToleranceVmGroupVO.getUuid());
            }

            @Override
            protected int getMaxPendingTasks() {
                return 1;
            }

            @Override
            protected void exceedMaxPendingCallback() {
                RecoverFailedVmInstanceReply reply = new RecoverFailedVmInstanceReply();
                bus.reply(msg, reply);
            }

            @Override
            protected String getDeduplicateString() {
                return String.format("recover-failed-vm-instance-%s", faultToleranceVmGroupVO.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                RecoverFailedVmInstanceReply recoverFailedVmInstanceReply = new RecoverFailedVmInstanceReply();

                recoverFaultToleranceAfterFailOver(msg, new Completion(chain) {
                    @Override
                    public void success() {
                        bus.reply(msg, recoverFailedVmInstanceReply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        recoverFailedVmInstanceReply.setError(errorCode);
                        bus.reply(msg, recoverFailedVmInstanceReply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return "recover-failed-vm-instance";
            }
        });
    }

    private void handle(StartFaultToleranceVmMsg msg) {
        StartFaultToleranceVmReply reply = new StartFaultToleranceVmReply();

        String hostUuid = Q.New(VmInstanceVO.class)
                .select(VmInstanceVO_.hostUuid)
                .eq(VmInstanceVO_.uuid, msg.getPrimaryVmInstanceUuid()).findValue();
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("ft-vm-operation-on-host-%s", hostUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getVmInstanceUuid());

                if (faultToleranceVmGroupVO == null) {
                    logger.debug("no ft vm group found. reply request success");
                    bus.reply(msg, reply);
                    chain.next();
                    return;
                }

                VmInstanceVO svm = dbf.findByUuid(faultToleranceVmGroupVO.getSecondaryVmInstanceUuid(), VmInstanceVO.class);
                if (svm.getState() == VmInstanceState.Created) {
                    dbf.removeByPrimaryKey(svm.getUuid(), VmInstanceVO.class);
                    reply.setError(operr("created svm found, report error for this start secondary vm request"));
                    bus.reply(msg, reply);
                    chain.next();
                    return;
                }

                VmInstanceVO pvm = dbf.findByUuid(faultToleranceVmGroupVO.getPrimaryVmInstanceUuid(), VmInstanceVO.class);

                if (svm.getState() == VmInstanceState.Running || svm.getState() == VmInstanceState.Paused) {
                    bus.reply(msg, reply);
                    chain.next();
                    return;
                }

                faultToleranceVmGroupVO.setStatus(FaultToleranceStatus.SecondaryVmStarting);
                faultToleranceVmGroupVO = dbf.updateAndRefresh(faultToleranceVmGroupVO);

                startFaultToleranceVm(pvm, svm, faultToleranceVmGroupVO, msg.isSkipPrimaryVmConfig(), new Completion(chain) {
                    @Override
                    public void success() {
                        FaultToleranceVmGroupVO ftVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(pvm.getUuid());

                        ChangeFaultToleranceVmStatusMsg cmsg = new ChangeFaultToleranceVmStatusMsg();
                        cmsg.setPrimaryVmInstanceUuid(ftVmGroupVO.getPrimaryVmInstanceUuid());
                        bus.makeTargetServiceIdByResourceUuid(cmsg, SERVICE_ID, cmsg.getPrimaryVmInstanceUuid());
                        bus.send(cmsg);

                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        FaultToleranceVmGroupVO ftVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(pvm.getUuid());

                        if (ftVmGroupVO != null && ftVmGroupVO.getPrimaryVmInstanceUuid() != null) {
                            ChangeFaultToleranceVmStatusMsg cmsg = new ChangeFaultToleranceVmStatusMsg();
                            cmsg.setPrimaryVmInstanceUuid(ftVmGroupVO.getPrimaryVmInstanceUuid());
                            bus.makeTargetServiceIdByResourceUuid(cmsg, SERVICE_ID, cmsg.getPrimaryVmInstanceUuid());
                            bus.send(cmsg);
                        }

                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("start-ft-vm-for-%s", msg.getPrimaryVmInstanceUuid());
            }
        });
    }

    private void handle(CreateFaultToleranceVmMsg msg) {
        String hostUuid = Q.New(VmInstanceVO.class)
                .select(VmInstanceVO_.hostUuid)
                .eq(VmInstanceVO_.uuid, msg.getPrimaryVmInstanceUuid()).findValue();
        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("ft-vm-operation-on-host-%s", hostUuid);
            }

            @Override
            public void run(SyncTaskChain chain) {
                CreateFaultToleranceVmReply reply = new CreateFaultToleranceVmReply();
                VmInstanceVO pvm = dbf.findByUuid(msg.getPrimaryVmInstanceUuid(), VmInstanceVO.class);
                if (VmInstanceState.Stopped.equals(pvm.getState())
                        || VmInstanceState.Destroying.equals(pvm.getState())
                        || VmInstanceState.Destroyed.equals(pvm.getState())) {
                    logger.debug(String.format("primary vm[uuid:%s] state %s, ignore secondary vm creation request", pvm.getUuid(), pvm.getState()));
                    bus.reply(msg, reply);
                    chain.next();
                    return;
                }

                String level = HaSystemTags.HA.getTokenByResourceUuid(pvm.getUuid(), HaSystemTags.HA_TOKEN);
                if (level == null || !level.equals(VmHaLevel.FaultTolerance.toString())) {
                    logger.debug("primary vm is not FaultTolerance level, ignore secondary vm creation request");
                    bus.reply(msg, reply);
                    chain.next();
                    return;
                }

                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getPrimaryVmInstanceUuid());

                if (faultToleranceVmGroupVO != null && faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() != null) {
                    VmInstanceVO vm = dbf.findByUuid(faultToleranceVmGroupVO.getSecondaryVmInstanceUuid(), VmInstanceVO.class);
                    if (vm == null) {
                        faultToleranceVmGroupVO.setSecondaryVmInstanceUuid(null);
                        faultToleranceVmGroupVO = dbf.updateAndRefresh(faultToleranceVmGroupVO);
                    } else {
                        if (vm.getState() != VmInstanceState.Created) {
                            reply.setInventory(VmInstanceInventory.valueOf(vm));
                            bus.reply(msg, reply);
                            chain.next();
                            return;
                        }

                        // remove dirty created secondary vm record
                        dbf.removeByPrimaryKey(vm.getUuid(), VmInstanceVO.class);
                    }
                }

                final VmInstanceVO fianlSvm = generateSecondaryVmVO(pvm, msg.getSecondaryVmInstanceUuid());
                if (faultToleranceVmGroupVO == null) {
                    faultToleranceVmGroupVO = generateFaultToleranceVmVO(pvm, fianlSvm);

                    SQL.New(VmNicVO.class).eq(VmNicVO_.vmInstanceUuid, pvm.getUuid()).set(VmNicVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid()).update();
                } else {
                    faultToleranceVmGroupVO.setStatus(FaultToleranceStatus.SecondaryVmStarting);
                    faultToleranceVmGroupVO.setSecondaryVmInstanceUuid(fianlSvm.getUuid());
                    faultToleranceVmGroupVO = dbf.updateAndRefresh(faultToleranceVmGroupVO);

                    cloneFaultToleranceVmTags(pvm.getUuid(), faultToleranceVmGroupVO.getUuid());
                }

                createFaultToleranceVm(pvm, fianlSvm, faultToleranceVmGroupVO, pvm.getHostUuid(), msg, new Completion(chain) {
                    @Override
                    public void success() {
                        FaultToleranceVmGroupVO ftVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(pvm.getUuid());

                        ChangeFaultToleranceVmStatusMsg cmsg = new ChangeFaultToleranceVmStatusMsg();
                        cmsg.setPrimaryVmInstanceUuid(ftVmGroupVO.getPrimaryVmInstanceUuid());
                        bus.makeTargetServiceIdByResourceUuid(cmsg, SERVICE_ID, cmsg.getPrimaryVmInstanceUuid());
                        bus.send(cmsg);

                        new FaultToleranceVmCanonicalEvents
                                .FailedRecoverFaultToleranceCanonicalEvent(ftVmGroupVO.getUuid(), null)
                                .fire();

                        reply.setInventory(FaultToleranceVmGroupInventory.valueOf(dbf.reload(fianlSvm)));
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        FaultToleranceVmGroupVO ftVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(pvm.getUuid());

                        if (ftVmGroupVO != null && ftVmGroupVO.getPrimaryVmInstanceUuid() != null) {
                            ChangeFaultToleranceVmStatusMsg cmsg = new ChangeFaultToleranceVmStatusMsg();
                            cmsg.setPrimaryVmInstanceUuid(ftVmGroupVO.getPrimaryVmInstanceUuid());
                            bus.makeTargetServiceIdByResourceUuid(cmsg, SERVICE_ID, cmsg.getPrimaryVmInstanceUuid());
                            bus.send(cmsg);

                            if (!msg.isFromApiMessage()) {
                                new FaultToleranceVmCanonicalEvents
                                        .FailedRecoverFaultToleranceCanonicalEvent(ftVmGroupVO.getUuid(), errorCode)
                                        .fire();
                            }
                        }

                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("create-ft-vm-for-%s-on-host-%s", msg.getPrimaryVmInstanceUuid(), hostUuid);
            }
        });
    }

    private void handle(APIMessage msg) {
        if (msg instanceof APICreateFaultToleranceVmInstanceMsg) {
            handle((APICreateFaultToleranceVmInstanceMsg) msg);
        } else if (msg instanceof APIGetFaultToleranceVmsMsg) {
            handle((APIGetFaultToleranceVmsMsg) msg);
        } else if (msg instanceof APIFailoverFaultToleranceVmMsg) {
            handle((APIFailoverFaultToleranceVmMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handle(FailoverFaultToleranceVmMsg msg) {
        FaultToleranceVmGroupVO group = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getVmInstanceUuid());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("failover-fault-tolerance-vm-group-%s", group.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                FailoverFaultToleranceVmReply reply = new FailoverFaultToleranceVmReply();
                doFailoverFaultToleranceVm(msg.getVmInstanceUuid(), new Completion(chain) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        reply.setError(errorCode);
                        bus.reply(msg, reply);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("failover-fault-tolerance-vm-group-%s", group.getUuid());
            }
        });
    }

    private void handle(APIFailoverFaultToleranceVmMsg msg) {
        FaultToleranceVmGroupVO group = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getFaultToleranceVmUuid());

        thdf.chainSubmit(new ChainTask(msg) {
            @Override
            public String getSyncSignature() {
                return String.format("failover-fault-tolerance-vm-group-%s", group.getUuid());
            }

            @Override
            public void run(SyncTaskChain chain) {
                APIFailoverFaultToleranceVmEvent evt = new APIFailoverFaultToleranceVmEvent(msg.getId());
                doFailoverFaultToleranceVm(msg.getFaultToleranceVmUuid(), new Completion(chain) {
                    @Override
                    public void success() {
                        bus.publish(evt);
                        chain.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        evt.setError(errorCode);
                        bus.publish(evt);
                        chain.next();
                    }
                });
            }

            @Override
            public String getName() {
                return String.format("failover-fault-tolerance-vm-group-%s", group.getUuid());
            }
        });
    }

    private void doFailoverFaultToleranceVm(String vmInstanceUuid, Completion completion) {
        FaultToleranceVmGroupVO group = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(vmInstanceUuid);

        if (group == null) {
            completion.fail(operr("could not failover vm[uuid:%s]. Related fault tolerance vm group not exists", vmInstanceUuid));
            return;
        }

        VmInstanceVO pvm = dbf.findByUuid(group.getPrimaryVmInstanceUuid(), VmInstanceVO.class);
        VmInstanceVO svm = dbf.findByUuid(group.getSecondaryVmInstanceUuid(), VmInstanceVO.class);

        if (pvm == null && svm != null) {
            group.setPrimaryVmInstanceUuid(group.getSecondaryVmInstanceUuid());
            group.setSecondaryVmInstanceUuid(null);
            dbf.updateAndRefresh(group);
            completion.success();
            return;
        }

        if (pvm == null) {
            completion.fail(operr("pvm[uuid:%s] not exists", group.getPrimaryVmInstanceUuid()));
            return;
        }

        if (pvm.getState() == VmInstanceState.Unknown && (svm.getState() == VmInstanceState.Running || svm.getState() == VmInstanceState.Stopped)) {
            KVMHostVO host = dbf.findByUuid(pvm.getHostUuid(), KVMHostVO.class);

            FailColoPrimaryVmCmd cmd = new FailColoPrimaryVmCmd();
            String targetHostIp = getHostFaultToleranceAddress(host.getUuid());

            if (targetHostIp == null) {
                completion.fail(operr("could not failover. Primary vm is unknown but no fault tolerance network address available"));
                return;
            }

            cmd.setTargetHostIp(targetHostIp);
            cmd.setTargetHostPort(host.getPort());
            cmd.setVmInstanceUuid(pvm.getUuid());
            cmd.setTargetHostPassword(host.getPassword());

            KVMHostAsyncHttpCallMsg kmsg = new KVMHostAsyncHttpCallMsg();
            kmsg.setHostUuid(svm.getHostUuid());
            kmsg.setCommand(cmd);
            kmsg.setNoStatusCheck(true);
            kmsg.setPath(FAIL_COLO_PVM_PATH);
            bus.makeTargetServiceIdByResourceUuid(kmsg, HostConstant.SERVICE_ID, host.getUuid());
            bus.send(kmsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    CheckColoVmStateRsp rsp = ((KVMHostAsyncHttpCallReply) reply).toResponse(CheckColoVmStateRsp.class);

                    if (!rsp.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    ChangeVmStateMsg cmsg = new ChangeVmStateMsg();
                    cmsg.setVmInstanceUuid(pvm.getUuid());
                    cmsg.setStateEvent(VmInstanceStateEvent.stopped.toString());
                    bus.makeTargetServiceIdByResourceUuid(cmsg, VmInstanceConstant.SERVICE_ID, cmsg.getVmInstanceUuid());
                    bus.send(cmsg);

                    completion.success();
                }
            });
        } else if (pvm.getState() == VmInstanceState.Running && svm.getState() == VmInstanceState.Running) {
            StopVmInstanceMsg smsg = new StopVmInstanceMsg();
            smsg.setVmInstanceUuid(group.getPrimaryVmInstanceUuid());
            bus.makeTargetServiceIdByResourceUuid(smsg, VmInstanceConstant.SERVICE_ID, smsg.getVmInstanceUuid());
            bus.send(smsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    completion.success();
                }
            });
        } else if (pvm.getState() == VmInstanceState.Running && svm.getState() == VmInstanceState.Unknown) {
            KVMHostVO host = dbf.findByUuid(svm.getHostUuid(), KVMHostVO.class);

            FailColoPrimaryVmCmd cmd = new FailColoPrimaryVmCmd();
            String targetHostIp = getHostFaultToleranceAddress(host.getUuid());

            if (targetHostIp == null) {
                completion.fail(operr("could not failover. Secondary vm is unknown but no fault tolerance network address available"));
                return;
            }

            cmd.setTargetHostIp(targetHostIp);
            cmd.setTargetHostPort(host.getPort());
            cmd.setVmInstanceUuid(svm.getUuid());
            cmd.setTargetHostPassword(host.getPassword());

            KVMHostAsyncHttpCallMsg kmsg = new KVMHostAsyncHttpCallMsg();
            kmsg.setHostUuid(pvm.getHostUuid());
            kmsg.setCommand(cmd);
            kmsg.setNoStatusCheck(true);
            kmsg.setPath(FAIL_COLO_PVM_PATH);
            bus.makeTargetServiceIdByResourceUuid(kmsg, HostConstant.SERVICE_ID, host.getUuid());
            bus.send(kmsg, new CloudBusCallBack(completion) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    CheckColoVmStateRsp rsp = ((KVMHostAsyncHttpCallReply) reply).toResponse(CheckColoVmStateRsp.class);

                    if (!rsp.isSuccess()) {
                        completion.fail(reply.getError());
                        return;
                    }

                    ChangeVmStateMsg cmsg = new ChangeVmStateMsg();
                    cmsg.setVmInstanceUuid(svm.getUuid());
                    cmsg.setStateEvent(VmInstanceStateEvent.stopped.toString());
                    bus.makeTargetServiceIdByResourceUuid(cmsg, VmInstanceConstant.SERVICE_ID, cmsg.getVmInstanceUuid());
                    bus.send(cmsg);

                    completion.success();
                }
            });
        } else {
            completion.fail(operr("unexpected exception"));
        }
    }

    private void handle(APIGetFaultToleranceVmsMsg msg) {
        APIGetFaultToleranceVmsReply reply = new APIGetFaultToleranceVmsReply();

        FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getFaultToleranceVmUuid());

        VmInstanceVO pvm = dbf.findByUuid(faultToleranceVmGroupVO.getPrimaryVmInstanceUuid(), VmInstanceVO.class);
        if (pvm != null) {
            reply.setPrimaryVmInventory(VmInstanceInventory.valueOf(pvm));
        }

        if (faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() == null) {
            bus.reply(msg, reply);
            return;
        }

        VmInstanceVO svm = dbf.findByUuid(faultToleranceVmGroupVO.getSecondaryVmInstanceUuid(), VmInstanceVO.class);
        if (svm != null) {
            reply.setSecondaryVmInventory(VmInstanceInventory.valueOf(svm));
        }

        bus.reply(msg, reply);
    }

    private void handle(APICreateFaultToleranceVmInstanceMsg msg) {
        APICreateFaultToleranceVmInstanceEvent evt = new APICreateFaultToleranceVmInstanceEvent(msg.getId());

        CreateFaultTolerancePrimaryVmMsg cmsg = new CreateFaultTolerancePrimaryVmMsg();
        cmsg.setResourceUuid(msg.getResourceUuid() == null ? Platform.getUuid() : msg.getResourceUuid());
        cmsg.setClusterUuid(msg.getClusterUuid());
        cmsg.setCpuNum(msg.getCpuNum());
        cmsg.setDataDiskOfferingUuids(msg.getDataDiskOfferingUuids());
        cmsg.setDataVolumeSystemTags(msg.getDataVolumeSystemTags());
        cmsg.setDefaultL3NetworkUuid(msg.getDefaultL3NetworkUuid());
        cmsg.setDescription(msg.getDescription());
        cmsg.setHostUuid(msg.getHostUuid());
        cmsg.setImageUuid(msg.getImageUuid());
        cmsg.setInstanceOfferingUuid(msg.getInstanceOfferingUuid());
        cmsg.setL3NetworkUuids(msg.getL3NetworkUuids());
        cmsg.setMemorySize(msg.getMemorySize());
        cmsg.setName(msg.getName());
        cmsg.setPrimaryStorageUuidForRootVolume(msg.getPrimaryStorageUuidForRootVolume());
        cmsg.setRootVolumeSystemTags(msg.getRootVolumeSystemTags());
        cmsg.setZoneUuid(msg.getZoneUuid());
        cmsg.setSession(msg.getSession());
        cmsg.setSystemTags(msg.getSystemTags());
        bus.makeLocalServiceId(cmsg, FaultToleranceManager.SERVICE_ID);
        bus.send(cmsg, new CloudBusCallBack(msg) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    evt.setError(reply.getError());
                    bus.publish(evt);
                    return;
                }

                evt.setPrimaryVmInventory(((CreateFaultTolerancePrimaryVmReply) reply).getPrimaryVmInventory());
                evt.setSecondaryVmInventory(((CreateFaultTolerancePrimaryVmReply) reply).getSecondaryVmInventory());
                evt.setFaultToleranceVmGroupInventory(((CreateFaultTolerancePrimaryVmReply) reply).getFaultToleranceVmGroupInventory());

                bus.publish(evt);
            }
        });
    }

    static Integer toFaultTolerancePort(Integer port) {
        return port;
    }

    protected static ErrorCode getNoFaultToleranceAddressErrorCode(String hostUuid) {
        return operr("cannot found available ip from current ft network." +
                " Check whether global config[category:ft name:fault.tolerance.network.cidr] is correctly set," +
                " and confirm that host[uuid:%s] own ip address in the CIDR", hostUuid);
    }

    public static String getHostFaultToleranceAddress(String hostUuid, String managementIp) {
        final String cidr = FaultToleranceGlobalConfig.FT_NETWORK_CIDR.value();
        if (StringUtils.isEmpty(cidr) || FaultToleranceConstant.MANAGEMENT_NETWORK.equals(cidr)) {
            logger.warn("no ft network set");
            return null;
        }

        if (managementIp != null && NetworkUtils.isIpv4InCidr(managementIp, cidr)) {
            return managementIp;
        }

        final String extraIps = HostSystemTags.EXTRA_IPS.getTokenByResourceUuid(
                hostUuid, HostSystemTags.EXTRA_IPS_TOKEN);
        if (extraIps == null) {
            logger.error(String.format("Host[uuid:%s] has no IPs in ft network", hostUuid));
            return null;
        }

        final String[] ips = extraIps.split(",");
        for (String ip: ips) {
            if (NetworkUtils.isIpv4InCidr(ip, cidr)) {
                return ip;
            }
        }

        return null;
    }

    public static String getHostFaultToleranceAddress(String hostUuid) {
        HostVO host = Q.New(HostVO.class).eq(HostVO_.uuid, hostUuid).find();
        return getHostFaultToleranceAddress(hostUuid, host.getManagementIp());
    }

    private VmInstanceVO persistVmInstance(CreateFaultTolerancePrimaryVmMsg msg, String resourceUuid) {
        VmInstanceVO vo = new VmInstanceVO();

        if (resourceUuid != null) {
            vo.setUuid(resourceUuid);
        } else {
            vo.setUuid(Platform.getUuid());
        }

        vo.setName(msg.getName());
        vo.setClusterUuid(msg.getClusterUuid());
        vo.setDescription(msg.getDescription());
        vo.setImageUuid(msg.getImageUuid());
        vo.setInstanceOfferingUuid(msg.getInstanceOfferingUuid());
        vo.setState(VmInstanceState.Created);


        if(msg.getZoneUuid() != null){
            vo.setZoneUuid(msg.getZoneUuid());
        }else{
            String zoneUuid = Q.New(L3NetworkVO.class)
                    .select(L3NetworkVO_.zoneUuid)
                    .eq(L3NetworkVO_.uuid, msg.getL3NetworkUuids().get(0))
                    .findValue();
            vo.setZoneUuid(zoneUuid);
        }

        vo.setInternalId(dbf.generateSequenceNumber(VmInstanceSequenceNumberVO.class));
        vo.setDefaultL3NetworkUuid(msg.getDefaultL3NetworkUuid());

        ImageVO imageVO = Q.New(ImageVO.class).eq(ImageVO_.uuid, msg.getImageUuid()).find();
        vo.setPlatform(imageVO.getPlatform().toString());
        vo.setGuestOsType(imageVO.getGuestOsType());

        if (msg.getCpuNum() != null) {
            vo.setCpuNum(msg.getCpuNum());
        }

        if (msg.getMemorySize() != null) {
            vo.setMemorySize(msg.getMemorySize());
        }

        if (msg.getInstanceOfferingUuid() != null) {
            InstanceOfferingVO ivo = dbf.findByUuid(msg.getInstanceOfferingUuid(), InstanceOfferingVO.class);
            vo.setCpuNum(ivo.getCpuNum());
            vo.setMemorySize(ivo.getMemorySize());
            vo.setAllocatorStrategy(ivo.getAllocatorStrategy());
        }

        vo.setAccountUuid(msg.getSession().getAccountUuid());

        vo.setType(VmInstanceConstant.USER_VM_TYPE);
        vo = dbf.persist(vo);

        APICreateFaultToleranceVmInstanceMsg apiMessage = new APICreateFaultToleranceVmInstanceMsg();
        apiMessage.setSystemTags(msg.getSystemTags());
        tagMgr.createTagsFromAPICreateMessage(apiMessage, vo.getUuid(), VmInstanceVO.class.getSimpleName());

        if (msg.getInstanceOfferingUuid() != null) {
            tagMgr.copySystemTag(
                    msg.getInstanceOfferingUuid(),
                    InstanceOfferingVO.class.getSimpleName(),
                    vo.getUuid(),
                    VmInstanceVO.class.getSimpleName(), false);
        }

        if (msg.getImageUuid() != null) {
            tagMgr.copySystemTag(
                    msg.getImageUuid(),
                    ImageVO.class.getSimpleName(),
                    vo.getUuid(),
                    VmInstanceVO.class.getSimpleName(), false);

            ImageVO image = dbf.findByUuid(msg.getImageUuid(), ImageVO.class);
            if (image.getPlatform() == ImagePlatform.Windows || image.getPlatform() == ImagePlatform.WindowsVirtio) {
                ResourceConfig rc = rcf.getResourceConfig(VmGlobalConfig.EMULATE_HYPERV.getIdentity());
                rc.updateValue(vo.getUuid(), "true");

                rc = rcf.getResourceConfig(VmGlobalConfig.KVM_HIDDEN_STATE.getIdentity());
                rc.updateValue(vo.getUuid(), "true");
            }
        }

        FaultToleranceCanonicalEvents.FaultToleranceVmInnerVmCreated created = new FaultToleranceCanonicalEvents.FaultToleranceVmInnerVmCreated();
        created.vm = VmInstanceInventory.valueOf(vo);
        created.fire();

        return vo;
    }

    private List<VmNicSpec> getVmNicSpecsFromAPICreateVmInstanceMsg(List<String> systemTags, List<String> l3NetworkUuids) {
        List<VmNicSpec> nicSpecs = new ArrayList<>();

        for (String l3Uuid : l3NetworkUuids) {
            List<L3NetworkInventory> l3Invs = new ArrayList<>();
            L3NetworkInventory inv = L3NetworkInventory.valueOf(dbf.findByUuid(l3Uuid, L3NetworkVO.class));
            l3Invs.add(inv);

            nicSpecs.add(new VmNicSpec(l3Invs));
        }

        return nicSpecs;
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(SERVICE_ID);
    }

    @Override
    public boolean start() {
        createSecondaryVmWorkFlowBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(getCreateSecondaryVmWorkFlow()).construct();
        startFaultToleranceVmGroupWorkFlowBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(getStartFaultToleranceVmGroupWorkFlowElements()).construct();
        destroyFaultToleranceVmGroupWorkFlowBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(getDestroyFaultToleranceVmGroupWorkFlowElements()).construct();

        HaSystemTags.HA.installLifeCycleListener(new SystemTagLifeCycleListener() {
            @Override
            public void tagCreated(SystemTagInventory tag) {

            }

            @Override
            public void tagDeleted(SystemTagInventory tag) {

            }

            @Override
            public void tagUpdated(SystemTagInventory old, SystemTagInventory newTag) {
            }
        });

        restf.registerSyncHttpCallHandler(FaultToleranceConstant.KVM_REPORT_FAIL_OVER, KVMAgentCommands.ReportFailoverCmd.class, new SyncHttpCallHandler<KVMAgentCommands.ReportFailoverCmd>() {
            @Override
            public String handleSyncHttpCall(KVMAgentCommands.ReportFailoverCmd cmd) {
                if (!factory.isFtEnabled()) {
                    logger.debug(String.format("FT is globally disabled, ignore report of vm[uuid:%s]", cmd.vmInstanceUuid));
                    return null;
                }

                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(cmd.vmInstanceUuid);

                if (faultToleranceVmGroupVO == null) {
                    logger.debug("No vm group found, ignore fail-over message");
                    return null;
                }

                if (FaultToleranceSystemTags.INHIBIT_FT.hasTag(faultToleranceVmGroupVO.getUuid())) {
                    logger.debug(String.format("Ignore fail-over report of vm[uuid:%s] from host[uuid:%s] as inhibitFT is set up", cmd.vmInstanceUuid, cmd.hostUuid));
                    return null;
                }

                RecoverFailedVmInstanceMsg rmsg = new RecoverFailedVmInstanceMsg();
                rmsg.setHostUuid(cmd.hostUuid);
                rmsg.setVmInstanceUuid(faultToleranceVmGroupVO.getUuid());
                rmsg.setPrimaryVmFailure(cmd.primaryVmFailure);
                bus.makeTargetServiceIdByResourceUuid(rmsg, SERVICE_ID, rmsg.getVmInstanceUuid());
                bus.send(rmsg);

                return null;
            }
        });

        FaultToleranceGlobalConfig.ALL.installUpdateExtension((oldConfig, newConfig) -> {
            if (!newConfig.value(Boolean.class)) {
                return;
            }

            long count = Q.New(HostVO.class)
                    .eq(HostVO_.state, HostState.Enabled)
                    .eq(HostVO_.status, HostStatus.Connected)
                    .eq(HostVO_.hypervisorType, KVMConstant.KVM_HYPERVISOR_TYPE)
                    .count();

            if (count == 0) {
                return;
            }

            DeployColoQemuCmd cmd = new DeployColoQemuCmd();
            cmd.qemuUrl = String.format(FaultToleranceGlobalConfig.COLO_QEMU_PATH.value(), Platform.getManagementServerIp(), Platform.getManagementNodeServicePort());

            SQL.New("select uuid from HostVO host where host.state = :state and host.status =:status" +
                    " and host.hypervisorType = :hypervisorType and host.architecture = :architecture", String.class)
                    .param("state", HostState.Enabled)
                    .param("status", HostStatus.Connected)
                    .param("hypervisorType", KVMConstant.KVM_HYPERVISOR_TYPE)
                    .param("architecture", CpuArchitecture.x86_64.toString())
                    .limit(10).paginate(count, (List<String> hostUuids, PaginateCompletion paginateCompletion) -> {
                List<KVMHostAsyncHttpCallMsg> msgs = new ArrayList<>();

                for (String hostUuid : hostUuids) {
                    KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
                    msg.setHostUuid(hostUuid);
                    msg.setCommand(cmd);
                    msg.setNoStatusCheck(true);
                    msg.setPath(DEPLOY_COLO_QEMU_PATH);
                    bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hostUuid);
                    msgs.add(msg);
                }

                new While<>(msgs).step((msg, innerWhileCompletion) -> bus.send(msg, new CloudBusCallBack(innerWhileCompletion) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format("Failed to deploy colo qemu on host[uuid:%s]", msg.getHostUuid()));
                        }

                        innerWhileCompletion.done();
                    }
                }), 10).run(new WhileDoneCompletion(paginateCompletion) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        paginateCompletion.done();
                    }
                });
            }, new NopeNoErrorCompletion());
        });

        FaultToleranceGlobalConfig.FAULT_TOLERANCE_VM_SCAN_INTERVAL.installUpdateExtension((oldConfig, newConfig) -> {
            setFtScheduler();
        });

        evtf.onLocal(VmCanonicalEvents.VM_FULL_STATE_CHANGED_PATH, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                VmCanonicalEvents.VmStateChangedData d = (VmCanonicalEvents.VmStateChangedData) data;
                // check if ft vm
                boolean coloPrimary = FaultToleranceUtils.isFaultTolerancePrimaryVm(d.getVmUuid());
                boolean coloSecondary = FaultToleranceUtils.isFaultToleranceSecondaryVm(d.getVmUuid());
                if (!coloPrimary && !coloSecondary) {
                    return;
                }

                if (coloPrimary && !needChangeVmStatusPrimaryVmState.contains(d.getNewState())) {
                    return;
                }

                if (coloSecondary && !needChangeVmStatusSecondaryVmState.contains(d.getNewState())) {
                    return;
                }

                FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(d.getVmUuid());
                if (faultToleranceVmGroupVO == null) {
                    logger.debug("no ft vm group found skip it");
                    return;
                }

                if (coloSecondary && VmInstanceState.Running.toString().equals(d.getNewState())
                        && faultToleranceVmGroupVO.getStatus() != FaultToleranceStatus.Protected && faultToleranceVmGroupVO.getStatus() != FaultToleranceStatus.Unknown) {
                    logger.debug(String.format("current ft vm group status is %s, skip secondary vm running state" +
                            " if ft vm is during recovery and status change will be triggered by start/create secondary vm operation", faultToleranceVmGroupVO.getStatus()));
                    return;
                }

                ChangeFaultToleranceVmStatusMsg cmsg = new ChangeFaultToleranceVmStatusMsg();
                cmsg.setPrimaryVmInstanceUuid(faultToleranceVmGroupVO.getPrimaryVmInstanceUuid());
                bus.makeTargetServiceIdByResourceUuid(cmsg, SERVICE_ID, cmsg.getPrimaryVmInstanceUuid());
                bus.send(cmsg);

                if (!VmInstanceState.Stopped.toString().equals(d.getNewState()) || !FaultToleranceUtils.isFaultTolerancePrimaryVm(d.getVmUuid())) {
                    return;
                }

                if (FaultToleranceSystemTags.INHIBIT_FT.hasTag(faultToleranceVmGroupVO.getUuid())) {
                    logger.debug("inhibit ft tag exists, ignore pvm stopped request");
                    return;
                }

                if (faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() == null) {
                    logger.debug("svm not exists, ignore pvm stopped request");
                    return;
                }

                VmInstanceVO svm = dbf.findByUuid(faultToleranceVmGroupVO.getSecondaryVmInstanceUuid(), VmInstanceVO.class);
                if (svm == null) {
                    logger.debug("svm not exists, ignore pvm stopped request");
                    return;
                }

                if (svm.getState() == VmInstanceState.Stopped || svm.getHostUuid() == null) {
                    logger.debug("svm is stopped or do not have host info, no need to trigger fail-over");
                    return;
                }

                // send a message for primary vm down
                RecoverFailedVmInstanceMsg rmsg = new RecoverFailedVmInstanceMsg();
                rmsg.setVmInstanceUuid(faultToleranceVmGroupVO.getUuid());
                rmsg.setHostUuid(svm.getHostUuid());
                rmsg.setPrimaryVmFailure(true);
                bus.makeTargetServiceIdByResourceUuid(rmsg, SERVICE_ID, rmsg.getVmInstanceUuid());
                bus.send(rmsg);
            }
        });

        return true;
    }

    @Override
    public void managementNodeReady() {
        setFtScheduler();
    }

    private synchronized void setFtScheduler() {
        if (faultToleranceVmScanTracker != null) {
            faultToleranceVmScanTracker.cancel(true);
        }

        final long interval = FaultToleranceGlobalConfig.FAULT_TOLERANCE_VM_SCAN_INTERVAL.value(Long.class);
        faultToleranceVmScanTracker = thdf.submitPeriodicTask(new PeriodicTask() {

            @Override
            @ExceptionSafe
            public void run() {
                setGCJobForFaultToleranceVm();
            }

            @Override
            public TimeUnit getTimeUnit() {
                return TimeUnit.SECONDS;
            }

            @Override
            public long getInterval() {
                return interval;
            }

            @Override
            public String getName() {
                return "FaultToleranceVmScanTracker";
            }

        });
    }

    private void setGCJobForFaultToleranceVm() {
        if (!factory.isFtEnabled()) {
            logger.debug("FT is globally disabled, skip gc collect.");
            return;
        }

        Long total = SQL.New("select count (vm) from VmInstanceVO vm, SystemTagVO tag where" +
                " tag.tag = :ftTag and tag.resourceUuid = vm.uuid", Long.class)
                .param("ftTag", FT_TAG_FORMAT)
                .find();

        logger.debug(String.format("Batch:set FT GC job for %d VMs.", total));
        SQL.New("select vm from VmInstanceVO vm, SystemTagVO tag where tag.tag = :ftTag and tag.resourceUuid = vm.uuid")
                .param("ftTag", FT_TAG_FORMAT)
                .limit(50)
                .paginate(total, (List<VmInstanceVO> vms) -> {
                    for (VmInstanceVO vm : vms) {
                        FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(vm.getUuid());
                        if (faultToleranceVmGroupVO == null) {
                            logger.debug("not ft vm, ignore it");
                            continue;
                        }

                        if (faultToleranceVmGroupVO.getUuid().equals(vm.getUuid())) {
                            logger.debug("ft vm group it self, ignore it");
                            continue;
                        }

                        if (FaultToleranceSystemTags.INHIBIT_FT.hasTag(faultToleranceVmGroupVO.getUuid())) {
                            logger.debug(String.format("Inhibit fault tolerance tag is set, skip set gc job for vm[uuid:%s]", vm.getUuid()));
                            continue;
                        }

                        // if svm exists and not in state of Stopped, skip submit a gc job
                        if (Q.New(VmInstanceVO.class)
                                .eq(VmInstanceVO_.uuid, faultToleranceVmGroupVO.getSecondaryVmInstanceUuid()).notEq(VmInstanceVO_.state, VmInstanceState.Stopped)
                                .isExists()) {
                            continue;
                        }

                        String gcName = FaultToleranceVmGC.getGCName(faultToleranceVmGroupVO.getUuid());
                        if (Q.New(GarbageCollectorVO.class)
                                .eq(GarbageCollectorVO_.name, gcName)
                                .notEq(GarbageCollectorVO_.status, GCStatus.Done)
                                .isExists()) {
                            logger.debug(String.format("There is already a FaultToleranceVmGC on Vm[uuid:%s], skip.", vm.getUuid()));
                            continue;
                        }

                        FaultToleranceVmGC gc = new FaultToleranceVmGC();
                        gc.vm = VmInstanceInventory.valueOf(faultToleranceVmGroupVO);
                        gc.NAME = FaultToleranceVmGC.getGCName(faultToleranceVmGroupVO.getUuid());
                        gc.submit(FaultToleranceGlobalConfig.FAULT_TOLERANCE_RETRY_DELAY.value(Long.class), TimeUnit.SECONDS);
                    }
                });
    }

    private VmInstanceVO generateSecondaryVmVO(VmInstanceVO pvm, String secondaryVmInstanceUuid) {
        VmInstanceVO vmInstanceVO = new VmInstanceVO(pvm);
        // secondary vm need to reserve double size of primary vm as colo requested
        // reference to http://jira.zstack.io/browse/MINI-2059
        vmInstanceVO.setMemorySize(vmInstanceVO.getMemorySize() * 2);
        vmInstanceVO.setUuid(secondaryVmInstanceUuid);
        vmInstanceVO.setInternalId(dbf.generateSequenceNumber(VmInstanceSequenceNumberVO.class));
        vmInstanceVO.setType(FaultToleranceConstant.SECONDARY_VM_TYPE);
        vmInstanceVO.setState(VmInstanceState.Created);
        vmInstanceVO.setHostUuid(null);
        vmInstanceVO.setRootVolumeUuid(null);
        vmInstanceVO.setAccountUuid(accountManager.getOwnerAccountUuidOfResource(pvm.getUuid()));
        vmInstanceVO = dbf.persist(vmInstanceVO);

        SystemTagUtils.cloneTag(VmSystemTags.VM_PRIORITY, VmSystemTags.VM_PRIORITY_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.BOOT_MODE, VmSystemTags.BOOT_MODE_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(MevocoVmSystemTags.VM_CPU_PINNING, MevocoVmSystemTags.VM_CPU_PINNING_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(MevocoSystemTags.VM_CONSOLE_MODE, MevocoSystemTags.VM_CONSOLE_MODE_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.RDP_ENABLE, VmSystemTags.RDP_ENABLE_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.USB_REDIRECT, VmSystemTags.USB_REDIRECT_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.SECURITY_ELEMENT_ENABLE, VmSystemTags.SECURITY_ELEMENT_ENABLE_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.USERDATA, VmSystemTags.USERDATA_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.SSHKEY, VmSystemTags.SSHKEY_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.CONSOLE_PASSWORD, VmSystemTags.CONSOLE_PASSWORD_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.ROOT_PASSWORD, VmSystemTags.ROOT_PASSWORD_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.VDI_MONITOR_NUMBER, VmSystemTags.VDI_MONITOR_NUMBER_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.MACHINE_TYPE, VmSystemTags.MACHINE_TYPE_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.VM_GUEST_TOOLS, VmSystemTags.VM_GUEST_TOOLS_VERSION_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(HaSystemTags.HA, HaSystemTags.HA_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.VM_INJECT_QEMUGA, VmSystemTags.VM_INJECT_QEMUGA_TOKEN, pvm.getUuid(), vmInstanceVO.getUuid());
        SystemTagUtils.cloneTag(VmSystemTags.CREATED_BY_MARKETPLACE, pvm.getUuid(), vmInstanceVO.getUuid());

        if (ImagePlatform.Windows.toString().equals(vmInstanceVO.getPlatform()) || ImagePlatform.WindowsVirtio.toString().equals(vmInstanceVO.getPlatform())) {
            ResourceConfig rc = rcf.getResourceConfig(VmGlobalConfig.EMULATE_HYPERV.getIdentity());
            rc.updateValue(vmInstanceVO.getUuid(), "true");

            rc = rcf.getResourceConfig(VmGlobalConfig.KVM_HIDDEN_STATE.getIdentity());
            rc.updateValue(vmInstanceVO.getUuid(), "true");
        }

        FaultToleranceCanonicalEvents.FaultToleranceVmInnerVmCreated created = new FaultToleranceCanonicalEvents.FaultToleranceVmInnerVmCreated();
        created.vm = VmInstanceInventory.valueOf(vmInstanceVO);
        created.fire();

        return vmInstanceVO;
    }

    private FaultToleranceVmGroupVO generateFaultToleranceVmVO(VmInstanceVO pvm, VmInstanceVO svm) {
        FaultToleranceVmGroupVO shadowVmInstanceVO = new FaultToleranceVmGroupVO(pvm);
        shadowVmInstanceVO.setUuid(Platform.getUuid());
        shadowVmInstanceVO.setInternalId(dbf.generateSequenceNumber(VmInstanceSequenceNumberVO.class));
        shadowVmInstanceVO.setStatus(FaultToleranceStatus.SecondaryVmStarting);
        shadowVmInstanceVO.setPrimaryVmInstanceUuid(pvm.getUuid());
        shadowVmInstanceVO.setRootVolumeUuid(null);

        if (svm != null) {
            shadowVmInstanceVO.setSecondaryVmInstanceUuid(svm.getUuid());
        }

        shadowVmInstanceVO.setType(FaultToleranceGroupVmFactory.type.toString());
        shadowVmInstanceVO.setState(VmInstanceState.Created);
        shadowVmInstanceVO.setAccountUuid(accountManager.getOwnerAccountUuidOfResource(pvm.getUuid()));
        shadowVmInstanceVO = dbf.persist(shadowVmInstanceVO);

        cloneFaultToleranceVmTags(pvm.getUuid(), shadowVmInstanceVO.getUuid());

        FaultToleranceCanonicalEvents.FaultToleranceVmInnerVmCreated created = new FaultToleranceCanonicalEvents.FaultToleranceVmInnerVmCreated();
        created.vm = FaultToleranceVmGroupInventory.valueOf(shadowVmInstanceVO);
        created.fire();

        return shadowVmInstanceVO;
    }

    private void cloneFaultToleranceVmTags(String srcVmUuid, String destVmUuid) {
        SystemTagUtils.cloneTag(VmSystemTags.BOOT_MODE, VmSystemTags.BOOT_MODE_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(MevocoVmSystemTags.VM_CPU_PINNING, MevocoVmSystemTags.VM_CPU_PINNING_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(MevocoSystemTags.VM_CONSOLE_MODE, MevocoSystemTags.VM_CONSOLE_MODE_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.RDP_ENABLE, VmSystemTags.RDP_ENABLE_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.USB_REDIRECT, VmSystemTags.USB_REDIRECT_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.SECURITY_ELEMENT_ENABLE, VmSystemTags.SECURITY_ELEMENT_ENABLE_TOKEN, srcVmUuid, destVmUuid);

        if (!VmSystemTags.USERDATA.hasTag(destVmUuid)) {
            SystemTagUtils.cloneTag(VmSystemTags.USERDATA, VmSystemTags.USERDATA_TOKEN, srcVmUuid, destVmUuid);
        }

        SystemTagUtils.cloneTag(VmSystemTags.SSHKEY, VmSystemTags.SSHKEY_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.CONSOLE_PASSWORD, VmSystemTags.CONSOLE_PASSWORD_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.ROOT_PASSWORD, VmSystemTags.ROOT_PASSWORD_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.VDI_MONITOR_NUMBER, VmSystemTags.VDI_MONITOR_NUMBER_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.MACHINE_TYPE, VmSystemTags.MACHINE_TYPE_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.VM_GUEST_TOOLS, VmSystemTags.VM_GUEST_TOOLS_VERSION_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(HaSystemTags.HA, HaSystemTags.HA_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.VM_INJECT_QEMUGA, VmSystemTags.VM_INJECT_QEMUGA_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.VM_PRIORITY, VmSystemTags.VM_PRIORITY_TOKEN, srcVmUuid, destVmUuid);
        SystemTagUtils.cloneTag(VmSystemTags.CREATED_BY_MARKETPLACE, srcVmUuid, destVmUuid);
    }

    private void getColoVmStateOnHypervisor(final String hostUuid, String vmUuid, final ReturnValueCompletion<CheckColoVmStateRsp> completion) {
        CheckColoVmStateCmd cmd = new CheckColoVmStateCmd();
        cmd.setVmInstanceUuid(vmUuid);
        cmd.setHostUuid(hostUuid);

        KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
        msg.setHostUuid(hostUuid);
        msg.setCommand(cmd);
        msg.setNoStatusCheck(true);
        msg.setPath(CHECK_COLO_VM_STATE_PATH);
        bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, hostUuid);
        bus.send(msg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                CheckColoVmStateRsp rsp = ((KVMHostAsyncHttpCallReply) reply).toResponse(CheckColoVmStateRsp.class);
                if (!rsp.isSuccess()) {
                    completion.fail(operr(rsp.getError()));
                    return;
                }

                completion.success(rsp);
            }
        });
    }

    private List<Flow> getSecondaryVmFailFlows(VmInstanceVO pvm, VmInstanceVO svm) {
        return Arrays.asList(new NoRollbackFlow() {
            String __name__ = "stop-secondary-vm-on-host";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                StopVmInstanceMsg smsg = new StopVmInstanceMsg();
                smsg.setVmInstanceUuid(svm.getUuid());
                smsg.setGcOnFailure(true);
                bus.makeTargetServiceIdByResourceUuid(smsg, VmInstanceConstant.SERVICE_ID, smsg.getVmInstanceUuid());
                bus.send(smsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format("Failed to stop svm[uuid:%s]", svm.getUuid()));
                        }

                        trigger.next();
                    }
                });
            }
        }, new NoRollbackFlow() {
            String __name__ = "start-secondary-vm-on-host";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                StartFaultToleranceVmMsg smsg = new StartFaultToleranceVmMsg();
                smsg.setPrimaryVmInstanceUuid(pvm.getUuid());
                smsg.setSecondaryVmInstanceUuid(svm.getUuid());
                smsg.setSkipPrimaryVmConfig(true);

                String hostUuid = Q.New(VmInstanceVO.class).select(VmInstanceVO_.hostUuid).eq(VmInstanceVO_.uuid, smsg.getPrimaryVmInstanceUuid()).findValue();

                if (hostUuid == null) {
                    trigger.fail(operr("can not start secondary vm, because primary vm is stopped"));
                    return;
                }

                bus.makeTargetServiceIdByResourceUuid(smsg, FaultToleranceManager.SERVICE_ID, hostUuid);
                bus.send(smsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            String gcName = FaultToleranceVmGC.getGCName(pvm.getUuid());
                            if (Q.New(GarbageCollectorVO.class)
                                    .eq(GarbageCollectorVO_.name, gcName)
                                    .notEq(GarbageCollectorVO_.status, GCStatus.Done)
                                    .isExists()) {
                                logger.debug(String.format("There is already a FaultToleranceVmGC on Vm[uuid:%s], skip.", pvm.getUuid()));
                                trigger.next();
                                return;
                            }

                            FaultToleranceVmGroupVO group = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(pvm.getUuid());

                            FaultToleranceVmGC gc = new FaultToleranceVmGC();
                            gc.vm = VmInstanceInventory.valueOf(group);
                            gc.NAME = FaultToleranceVmGC.getGCName(group.getUuid());
                            gc.submit(FaultToleranceGlobalConfig.FAULT_TOLERANCE_RETRY_DELAY.value(Long.class), TimeUnit.SECONDS);
                        }

                        trigger.next();
                    }
                });
            }
        });
    }

    private List<Flow> getPrimaryVmFailFlows(RecoverFailedVmInstanceMsg msg, VmInstanceVO pvm, VmInstanceVO svm) {
        return Arrays.asList(new NoRollbackFlow() {
            String __name__ = "lock-primary-vm-and-fail-over";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                FailoverPrimaryVmMsg failoverPrimaryVmMsg = new FailoverPrimaryVmMsg();
                failoverPrimaryVmMsg.setPrimaryVmInstanceUuid(pvm.getUuid());
                failoverPrimaryVmMsg.setSecondaryVmInstanceUuid(svm.getUuid());
                failoverPrimaryVmMsg.setHostUuid(msg.getHostUuid());
                bus.makeTargetServiceIdByResourceUuid(failoverPrimaryVmMsg, SERVICE_ID, failoverPrimaryVmMsg.getPrimaryVmInstanceUuid());

                FailoverVmOverlayMsg svmOverlay = new FailoverVmOverlayMsg();
                svmOverlay.setVmInstanceUuid(svm.getUuid());
                svmOverlay.setMessage(failoverPrimaryVmMsg);
                bus.makeTargetServiceIdByResourceUuid(svmOverlay, VmInstanceConstant.SERVICE_ID, svmOverlay.getVmInstanceUuid());

                FailoverVmOverlayMsg fmsg = new FailoverVmOverlayMsg();
                fmsg.setVmInstanceUuid(pvm.getUuid());
                fmsg.setMessage(svmOverlay);
                bus.makeTargetServiceIdByResourceUuid(fmsg, VmInstanceConstant.SERVICE_ID, fmsg.getVmInstanceUuid());
                bus.send(fmsg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            logger.debug(String.format("Failed to fail-over the vm[uuid:%s]", pvm.getUuid()));
                        }

                        trigger.next();
                    }
                });
            }
        });
    }

    class FaultToleranceChecker {
        VmInstanceVO primaryVmInstance;
        VmInstanceVO secondaryVmInstance;
        FaultToleranceVmGroupVO faultToleranceVmGroupVO;
        RecoverFailedVmInstanceMsg msg;
        boolean needRecreate = false;

        void checkVmFailure(Completion completion) {
            logger.debug(String.format("report fail-over from primary vm[uuid:%s] from host[uuid:%s], need create[%s], primary vm ",
                    primaryVmInstance.getUuid(), msg.getHostUuid(), needRecreate));

            // if svm stopped and down reported
            if (!needRecreate && VmInstanceState.Stopped.equals(secondaryVmInstance.getState())) {
                String message = "secondary vm down reported and current vm state is stopped, skip check it on hypervisor";
                startRecoverFlow(this, message, completion);
                return;
            }

            // if pvm stopped and down reported
            if (needRecreate && (VmInstanceState.Stopped.equals(primaryVmInstance.getState()) || primaryVmInstance.getHostUuid() == null)) {
                String message = "primary vm down reported and current pvm state is stopped, skip check it on hypervisor";
                startRecoverFlow(this, message, completion);
                return;
            }

            String targetHostUuid = needRecreate ? primaryVmInstance.getHostUuid() : secondaryVmInstance.getHostUuid();
            String targetVmUuid = needRecreate ? primaryVmInstance.getUuid() : secondaryVmInstance.getUuid();
            final FaultToleranceChecker checker = this;
            getColoVmStateOnHypervisor(targetHostUuid, targetVmUuid, new ReturnValueCompletion<CheckColoVmStateRsp>(completion) {
                @Override
                public void success(CheckColoVmStateRsp rsp) {
                    VmInstanceState stateOnHost;
                    if (rsp.getState() == null) {
                        stateOnHost = VmInstanceState.Stopped;
                    } else {
                        stateOnHost = KVMConstant.KvmVmState.valueOf(rsp.getState()).toVmInstanceState();
                    }

                    if (!VmInstanceState.Running.equals(stateOnHost) && !VmInstanceState.Paused.equals(stateOnHost)) {
                        String message = String.format("confirm vm[uuid:%s] in group[uuid:%s] is dead on host[uuid:%s]",
                                targetVmUuid, faultToleranceVmGroupVO.getUuid(), targetHostUuid);
                        startRecoverFlow(checker, message, completion);
                        return;
                    }

                    if (!needRecreate && ColoStatusMode.none.toString().equals(rsp.getCurrentMode())) {
                        String message = String.format("report secondary vm[uuid:%s] in group[uuid:%s] down, but find it is running on host[uuid:%s] " +
                                "and colo mode none, need to stop secondary vm", targetVmUuid, faultToleranceVmGroupVO.getUuid(), targetHostUuid);
                        checker.needRecreate = false;
                        startRecoverFlow(checker, message, completion);
                        return;
                    }

                    if (!needRecreate) {
                        String message = String.format("report secondary vm[uuid:%s] in group[uuid:%s] down on host[uuid:%s]," +
                                " need to recover it", targetVmUuid, faultToleranceVmGroupVO.getUuid(), targetHostUuid);
                        startRecoverFlow(checker, message, completion);
                        return;
                    }

                    // When primary vm is killed or crashed, libvirt might still return running state in several seconds
                    // but primary down will be reported to MN, so check colo mode for vm's actual state.
                    // colo mode will report nothing if guest is going down
                    if (ColoStatusMode.none.toString().equals(rsp.getCurrentMode())) {
                        String message = String.format("report primary vm[uuid:%s] in group[uuid:%s] down, but its colo mode return none," +
                                " means it is still running, stop secondary vm", targetVmUuid, faultToleranceVmGroupVO.getUuid());
                        checker.needRecreate = false;
                        startRecoverFlow(checker, message, completion);
                        return;
                    }

                    // check svm state
                    // notice: only report primary vm down but find primary vm is running can reach this code
                    getColoVmStateOnHypervisor(secondaryVmInstance.getHostUuid(), secondaryVmInstance.getUuid(), new ReturnValueCompletion<CheckColoVmStateRsp>(completion) {
                        @Override
                        public void success(CheckColoVmStateRsp returnValue) {
                            VmInstanceState stateOnHost;
                            if (rsp.getState() == null) {
                                stateOnHost = VmInstanceState.Stopped;
                            } else {
                                stateOnHost = KVMConstant.KvmVmState.valueOf(rsp.getState()).toVmInstanceState();
                            }

                            if (!VmInstanceState.Running.equals(stateOnHost) && !VmInstanceState.Paused.equals(stateOnHost)) {
                                logger.debug("secondary vm[uuid:%s] is dead, nothing need to do");
                                completion.success();
                                return;
                            }

                            // means secondary vm is running and colo mode report none, so secondary vm is just after failover
                            if (ColoStatusMode.none.toString().equals(rsp.getCurrentMode())) {
                                String message = String.format("report primary vm down and confirm" +
                                        " secondary vm[uuid:%s] in group[uuid:%s] on host[uuid:%s] colo mode is none, try to recover ft",
                                        secondaryVmInstance.getUuid(), faultToleranceVmGroupVO.getUuid(), secondaryVmInstance.getHostUuid());
                                checker.needRecreate = true;
                                startRecoverFlow(checker, message, completion);
                                return;
                            }

                            logger.debug(String.format("unexpected if secondary vm[uuid:%s] report primary or secondary for colo mode", secondaryVmInstance.getUuid()));
                            completion.success();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            logger.debug(String.format("failed to get vm[uuid:%s] colo mode", secondaryVmInstance.getUuid()));
                            completion.fail(errorCode);
                        }
                    });
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    logger.debug(String.format("Failed to check vm[uuid:%s] state on host[uuid:%s]", primaryVmInstance.getUuid(), targetHostUuid));
                    completion.fail(errorCode);
                }
            });
        }
    }

    private void recoverFaultToleranceAfterFailOver(RecoverFailedVmInstanceMsg msg, Completion completion) {
        FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(msg.getVmInstanceUuid());

        if (faultToleranceVmGroupVO == null) {
            logger.debug("no fault tolerance group found, skip recover ft");
            completion.success();
            return;
        }

        if (faultToleranceVmGroupVO.getSecondaryVmInstanceUuid() == null) {
            logger.debug("no svm found, skip recover ft");
            completion.success();
            return;
        }

        VmInstanceVO svm = dbf.findByUuid(faultToleranceVmGroupVO.getSecondaryVmInstanceUuid(), VmInstanceVO.class);
        if (svm == null) {
            logger.debug("no svm found, skip recover ft");
            completion.success();
            return;
        }
        
        VmInstanceVO pvm = dbf.findByUuid(faultToleranceVmGroupVO.getPrimaryVmInstanceUuid(), VmInstanceVO.class);

        FaultToleranceChecker checker = new FaultToleranceChecker();
        checker.primaryVmInstance = pvm;
        checker.secondaryVmInstance = svm;
        checker.faultToleranceVmGroupVO = faultToleranceVmGroupVO;
        checker.msg = msg;
        checker.needRecreate = msg.isPrimaryVmFailure();
        checker.checkVmFailure(new Completion(completion) {
            @Override
            public void success() {
                completion.success();
            }

            @Override
            public void fail(ErrorCode errorCode) {
                completion.fail(errorCode);
            }
        });
    }

    private void startRecoverFlow(FaultToleranceChecker checker, String reason, Completion completion) {
        FlowChain chain = new SimpleFlowChain();

        if (checker.needRecreate) {
            for (Flow flow : getPrimaryVmFailFlows(checker.msg, checker.primaryVmInstance, checker.secondaryVmInstance)) {
                chain.then(flow);
            }
        } else {
            for (Flow flow : getSecondaryVmFailFlows(checker.primaryVmInstance, checker.secondaryVmInstance)) {
                chain.then(flow);
            }
        }

        chain.done(new FlowDoneHandler(null) {
            @Override
            public void handle(Map data) {
                logger.debug(reason);
                new FaultToleranceVmCanonicalEvents
                        .RequestRecoverFaultToleranceCanonicalEvent(checker.faultToleranceVmGroupVO.getUuid(), reason)
                        .fire();

                completion.success();
            }
        }).error(new FlowErrorHandler(null) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }
    class VmStarterVmNicRedirectPort {
        HostPortVO mirrorPort;
        HostPortVO primaryInPort;
        HostPortVO secondaryInPort;
        HostPortVO primaryOutPort;
        int deviceId;
    }

    private List<VmStarterVmNicRedirectPort> getVmNicRedirectPortsFromNics(Set<VmNicVO> nics) {
        List<VmStarterVmNicRedirectPort> ports = new ArrayList<>();

        if (nics.isEmpty()) {
            return new ArrayList<>();
        }

        FaultToleranceVmGroupVO faultToleranceVmGroupVO = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(nics.iterator().next().getVmInstanceUuid());

        String primaryVmHostUuid = Q.New(VmInstanceVO.class)
                .select(VmInstanceVO_.hostUuid)
                .eq(VmInstanceVO_.uuid, faultToleranceVmGroupVO.getPrimaryVmInstanceUuid())
                .findValue();

        for (VmNicVO nic : nics) {
            List<VmInstanceVmNicRedirectPortRefVO> refVOS = Q.New(VmInstanceVmNicRedirectPortRefVO.class)
                    .eq(VmInstanceVmNicRedirectPortRefVO_.vmInstanceUuid, nic.getVmInstanceUuid())
                    .eq(VmInstanceVmNicRedirectPortRefVO_.vmNicUuid, nic.getUuid())
                    .list();
            VmInstanceVmNicRedirectPortRefVO nicPortRef;
            if (refVOS.size() > 1) {
                Iterator<VmInstanceVmNicRedirectPortRefVO> iterator = refVOS.iterator();
                while (iterator.hasNext()) {
                    VmInstanceVmNicRedirectPortRefVO ref = iterator.next();
                    if (Q.New(HostPortVO.class)
                            .eq(HostPortVO_.id, ref.getMirrorPortId())
                            .eq(HostPortVO_.hostUuid, primaryVmHostUuid)
                            .isExists()) {
                        continue;
                    }

                    iterator.remove();
                }

                nicPortRef = refVOS.get(0);
            } else if (refVOS.size() == 1) {
                nicPortRef = refVOS.get(0);
            } else {
                nicPortRef = null;
            }

            if (nicPortRef == null) {
                throw new CloudRuntimeException("VmInstanceVmNicRedirectPortRefVO can not be null, thrown exception and redo svm start/create");
            }

            VmStarterVmNicRedirectPort port = new VmStarterVmNicRedirectPort();
            port.mirrorPort = dbf.findById(nicPortRef.getMirrorPortId(), HostPortVO.class);
            port.primaryInPort = dbf.findById(nicPortRef.getPrimaryInPortId(), HostPortVO.class);
            port.primaryOutPort = dbf.findById(nicPortRef.getPrimaryOutPortId(), HostPortVO.class);
            port.secondaryInPort = dbf.findById(nicPortRef.getSecondaryInPortId(), HostPortVO.class);
            port.deviceId = nic.getDeviceId();

            ports.add(port);
        }

        return ports;
    }


    class FaultToleranceVmStarter {
        VmInstanceInventory primaryVm;
        VmInstanceInventory secondaryVm;

        List<VmStarterVmNicRedirectPort> vmNicRedirectPorts = new ArrayList<>();
        HostPortVO nbdServerPort;
        HostPortVO blockReplicationPort;
        HostPortVO primaryMonitorPort;
        HostPortVO secondaryMonitorPort;
        boolean fullSync = true;

        private List<VmNicRedirectConfig> toNicRedirectConfig() {
            List<VmNicRedirectConfig> configs = new ArrayList<>();
            for (VmStarterVmNicRedirectPort port : vmNicRedirectPorts) {
                VmNicRedirectConfig config = new VmNicRedirectConfig();
                config.setMirrorPort(toFaultTolerancePort(port.mirrorPort.getPort()));
                config.setSecondaryInPort(toFaultTolerancePort(port.secondaryInPort.getPort()));
                config.setPrimaryInPort(toFaultTolerancePort(port.primaryInPort.getPort()));
                config.setPrimaryOutPort(toFaultTolerancePort(port.primaryOutPort.getPort()));
                config.setDeviceId(port.deviceId);

                configs.add(config);
            }

            return configs;
        }

        public Flow getConfigPrimaryVmFlow() {
            return new Flow() {
                String __name__ = "config-primary-vm";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    ConfigPrimaryVmMsg cmsg = new ConfigPrimaryVmMsg();
                    cmsg.setHostUuid(primaryVm.getHostUuid());

                    cmsg.setConfigs(toNicRedirectConfig());
                    cmsg.setVmInstanceUuid(primaryVm.getUuid());

                    HostVO host = dbf.findByUuid(primaryVm.getHostUuid(), HostVO.class);
                    String ftAddress = getHostFaultToleranceAddress(host.getUuid());
                    if (ftAddress != null) {
                        cmsg.setHostIp(ftAddress);
                    } else {
                        cmsg.setHostIp(host.getManagementIp());
                    }

                    bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, cmsg.getHostUuid());
                    bus.send(cmsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                trigger.fail(reply.getError());
                                return;
                            }

                            trigger.next();
                        }
                    });
                }

                @Override
                public void rollback(FlowRollback trigger, Map data) {
                    trigger.rollback();
                }
            };
        }

        public Flow getConfigSecondaryVmFlow() {
            return new Flow() {
                String __name__ = "config-secondary-vm";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    ConfigSecondaryVmMsg cmsg = new ConfigSecondaryVmMsg();
                    cmsg.setHostUuid(secondaryVm.getHostUuid());
                    cmsg.setVmInstanceUuid(secondaryVm.getUuid());
                    cmsg.setNbdServerPort(toFaultTolerancePort(nbdServerPort.getPort()));

                    HostVO host = dbf.findByUuid(secondaryVm.getHostUuid(), HostVO.class);
                    String ftAddress = getHostFaultToleranceAddress(host.getUuid());
                    if (ftAddress != null) {
                        cmsg.setPrimaryVmHostIp(ftAddress);
                    } else {
                        cmsg.setPrimaryVmHostIp(host.getManagementIp());
                    }

                    bus.makeTargetServiceIdByResourceUuid(cmsg, HostConstant.SERVICE_ID, cmsg.getHostUuid());
                    bus.send(cmsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                trigger.fail(reply.getError());
                                return;
                            }

                            trigger.next();
                        }
                    });
                }

                @Override
                public void rollback(FlowRollback trigger, Map data) {
                    trigger.rollback();
                }
            };
        }

        public Flow getWaitColoSecondaryReadyFlow() {
            return new NoRollbackFlow() {
                String __name__ = "wait-colo-secondary-ready";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    WaitColoVmReadyCmd cmd = new WaitColoVmReadyCmd();
                    cmd.setVmInstanceUuid(secondaryVm.getUuid());
                    cmd.setHostUuid(secondaryVm.getHostUuid());
                    cmd.setColoCheckTimeout(FaultToleranceGlobalConfig.FAULT_TOLERANCE_SECONDARY_READY_INTERVAL.value(Long.class));

                    KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
                    msg.setHostUuid(secondaryVm.getHostUuid());
                    msg.setCommand(cmd);
                    msg.setNoStatusCheck(true);
                    msg.setPath(WAIT_COLO_VM_READY_PATH);
                    bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, msg.getHostUuid());
                    bus.send(msg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                trigger.fail(reply.getError());
                                return;
                            }

                            KVMAgentCommands.AgentResponse rsp = ((KVMHostAsyncHttpCallReply) reply).toResponse(KVMAgentCommands.AgentResponse.class);

                            if (!rsp.isSuccess()) {
                                trigger.fail(reply.getError());
                                return;
                            }

                            logger.debug(String.format("ft secondary vm of vm[uuid:%s] is ready", secondaryVm.getUuid()));
                            trigger.next();
                        }
                    });
                }
            };
        }

        public Flow getStartColoSyncFlow() {
            return new Flow() {
                String __name__ = "start-colo-sync";

                boolean coloMigrationFinished = false;

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    StartColoSyncMsg startColoSyncMsg = new StartColoSyncMsg();
                    startColoSyncMsg.setBlockReplicationPort(toFaultTolerancePort(blockReplicationPort.getPort()));
                    startColoSyncMsg.setHostUuid(primaryVm.getHostUuid());
                    startColoSyncMsg.setVmInstanceUuid(primaryVm.getUuid());
                    startColoSyncMsg.setNbdServerPort(toFaultTolerancePort(nbdServerPort.getPort()));
                    startColoSyncMsg.setSecondaryVmHostIp(getHostFaultToleranceAddress(secondaryVm.getHostUuid()));
                    startColoSyncMsg.setCheckpointDelay(FaultToleranceGlobalConfig.CHECKPOINT_DELAY.value(Long.class));
                    startColoSyncMsg.setFullSync(fullSync);
                    FaultToleranceVmGroupVO group = FaultToleranceUtils.getFaultToleranceGroupByVmUuid(primaryVm.getUuid());
                    startColoSyncMsg.setNics(VmNicInventory.valueOf(group.getVmNics()));
                    bus.makeTargetServiceIdByResourceUuid(startColoSyncMsg, HostConstant.SERVICE_ID, startColoSyncMsg.getHostUuid());
                    bus.send(startColoSyncMsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                trigger.fail(reply.getError());
                                return;
                            }

                            coloMigrationFinished = true;
                            trigger.next();
                        }
                    });
                }

                @Override
                public void rollback(FlowRollback trigger, Map data) {
                    if (!coloMigrationFinished) {
                        trigger.rollback();
                        return;
                    }

                    RollbackQuorumConfigCmd cmd = new RollbackQuorumConfigCmd();
                    cmd.setVmInstanceUuid(primaryVm.getUuid());
                    cmd.setNicNumber(vmNicRedirectPorts.size());

                    VmInstanceVO vm = dbf.findByUuid(primaryVm.getUuid(), VmInstanceVO.class);
                    List<VolumeInventory> volumes = vm.getAllVolumes().stream().filter(v -> v.getType() == VolumeType.Data || v.getType() == VolumeType.Root).map(VolumeInventory::valueOf).collect(Collectors.toList());
                    KVMHostVO host = dbf.findByUuid(primaryVm.getHostUuid(), KVMHostVO.class);
                    cmd.setVolumes(VolumeTO.valueOf(volumes, KVMHostInventory.valueOf(host)));

                    KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
                    msg.setHostUuid(primaryVm.getHostUuid());
                    msg.setCommand(cmd);
                    msg.setNoStatusCheck(true);
                    msg.setPath(ROLLBACK_QUORUM_CONFIG_PATH);
                    bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, msg.getHostUuid());
                    bus.send(msg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.debug("failed to rollback primary vm config");
                                trigger.rollback();
                                return;
                            }

                            KVMAgentCommands.AgentResponse rsp = ((KVMHostAsyncHttpCallReply) reply).toResponse(KVMAgentCommands.AgentResponse.class);
                            if (!rsp.isSuccess()) {
                                logger.debug("failed to rollback primary vm config");
                                trigger.rollback();
                                return;
                            }

                            logger.debug(String.format("ft secondary vm of vm[uuid:%s] is ready", secondaryVm.getUuid()));
                            trigger.rollback();
                        }
                    });
                }
            };
        }

        Flow getRegisterPrimaryHeartBeatFlow() {
            return new Flow() {
                String __name__ = "register-primary-vm-heartbeat-service";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    RegisterColoPrimaryCheckMsg registerColoPrimaryCheckMsg = new RegisterColoPrimaryCheckMsg();
                    registerColoPrimaryCheckMsg.setHostUuid(secondaryVm.getHostUuid());
                    registerColoPrimaryCheckMsg.setHeartbeatPort(toFaultTolerancePort(primaryMonitorPort.getPort()));
                    registerColoPrimaryCheckMsg.setTargetHostIp(getHostFaultToleranceAddress(primaryVm.getHostUuid()));
                    registerColoPrimaryCheckMsg.setVmInstanceUuid(secondaryVm.getUuid());
                    registerColoPrimaryCheckMsg.setRedirectNum(vmNicRedirectPorts.size());
                    bus.makeTargetServiceIdByResourceUuid(registerColoPrimaryCheckMsg, HostConstant.SERVICE_ID, registerColoPrimaryCheckMsg.getHostUuid());
                    bus.send(registerColoPrimaryCheckMsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                trigger.fail(reply.getError());
                                return;
                            }

                            trigger.next();
                        }
                    });
                }

                @Override
                public void rollback(FlowRollback trigger, Map data) {
                    trigger.rollback();
                }
            };
        }

        Flow getRegisterSecondaryHeartBeatFlow() {
            return new Flow() {
                String __name__ = "register-secondary-vm-heartbeat-service";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    RegisterColoPrimaryCheckMsg registerColoPrimaryCheckMsg = new RegisterColoPrimaryCheckMsg();
                    registerColoPrimaryCheckMsg.setHostUuid(primaryVm.getHostUuid());
                    registerColoPrimaryCheckMsg.setHeartbeatPort(toFaultTolerancePort(secondaryMonitorPort.getPort()));
                    registerColoPrimaryCheckMsg.setTargetHostIp(getHostFaultToleranceAddress(secondaryVm.getHostUuid()));
                    registerColoPrimaryCheckMsg.setVmInstanceUuid(primaryVm.getUuid());
                    registerColoPrimaryCheckMsg.setColoPrimary(true);
                    registerColoPrimaryCheckMsg.setRedirectNum(vmNicRedirectPorts.size());
                    bus.makeTargetServiceIdByResourceUuid(registerColoPrimaryCheckMsg, HostConstant.SERVICE_ID, registerColoPrimaryCheckMsg.getHostUuid());
                    bus.send(registerColoPrimaryCheckMsg, new CloudBusCallBack(trigger) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                trigger.fail(reply.getError());
                                return;
                            }

                            trigger.next();
                        }
                    });
                }

                @Override
                public void rollback(FlowRollback trigger, Map data) {
                    trigger.rollback();
                }
            };
        }
    }

    private void startFaultToleranceVm(VmInstanceVO pvm, VmInstanceVO svm, FaultToleranceVmGroupVO faultToleranceVmGroupVO, boolean skipPrimaryVmConfig, Completion completion) {
        FaultToleranceVmStarter starter = new FaultToleranceVmStarter();
        starter.primaryVm = VmInstanceInventory.valueOf(pvm);
        starter.secondaryVm = VmInstanceInventory.valueOf(svm);


        List<FaultToleranceVmInstanceGroupHostPortRefVO> refVOs = Q.New(FaultToleranceVmInstanceGroupHostPortRefVO.class)
                .eq(FaultToleranceVmInstanceGroupHostPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                .list();

        if (refVOs.isEmpty()) {
            completion.fail(operr("not fault tolerance vm port found"));
            return;
        }

        if (refVOs.size() != 1) {
            Iterator<FaultToleranceVmInstanceGroupHostPortRefVO> iterator = refVOs.iterator();
            while (iterator.hasNext()) {
                FaultToleranceVmInstanceGroupHostPortRefVO refVO = iterator.next();
                if (Q.New(HostPortVO.class)
                        .eq(HostPortVO_.id, refVO.getPrimaryVmMonitorPortId())
                        .eq(HostPortVO_.hostUuid, pvm.getHostUuid())
                        .isExists()) {
                    continue;
                }

                SQL.New(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(
                        refVO.getNbdServerPortId(),
                        refVO.getBlockReplicationPortId(),
                        refVO.getSecondaryVmMonitorPortId(),
                        refVO.getPrimaryVmMonitorPortId(),
                        refVO.getReservedVmMigrationPortId())).delete();
                dbf.remove(refVO);
                iterator.remove();
            }
        }

        FaultToleranceVmInstanceGroupHostPortRefVO ref = refVOs.get(0);
        
        starter.vmNicRedirectPorts = getVmNicRedirectPortsFromNics(faultToleranceVmGroupVO.getVmNics());
        starter.primaryMonitorPort = dbf.findById(ref.getPrimaryVmMonitorPortId(), HostPortVO.class);

        FlowChain chain = new SimpleFlowChain();

        if (!skipPrimaryVmConfig) {
            chain.then(starter.getConfigPrimaryVmFlow());
        }

        chain.then(new Flow() {
            @Override
            public void run(FlowTrigger trigger, Map data) {
                StartVmInstanceMsg msg = new StartVmInstanceMsg();
                msg.setVmInstanceUuid(starter.secondaryVm.getUuid());
                msg.setAvoidHostUuids(Collections.singletonList(starter.primaryVm.getHostUuid()));
                msg.setStartPaused(true);
                bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, msg.getVmInstanceUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        starter.secondaryVm = ((StartVmInstanceReply) reply).getInventory();

                        FaultToleranceVmInstanceGroupHostPortRefVO refVO = dbf.reload(ref);
                        starter.nbdServerPort = dbf.findById(refVO.getNbdServerPortId(), HostPortVO.class);
                        starter.blockReplicationPort = dbf.findById(refVO.getBlockReplicationPortId(), HostPortVO.class);
                        starter.secondaryMonitorPort = dbf.findById(refVO.getSecondaryVmMonitorPortId(), HostPortVO.class);

                        trigger.next();
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                StopVmInstanceMsg smsg = new StopVmInstanceMsg();
                smsg.setVmInstanceUuid(starter.secondaryVm.getUuid());
                bus.makeTargetServiceIdByResourceUuid(smsg, VmInstanceConstant.SERVICE_ID, smsg.getVmInstanceUuid());
                bus.send(smsg);

                trigger.rollback();
            }
        }).then(starter.getConfigSecondaryVmFlow())
                .then(starter.getStartColoSyncFlow())
                .then(starter.getWaitColoSecondaryReadyFlow())
                .then(starter.getRegisterPrimaryHeartBeatFlow())
                .then(starter.getRegisterSecondaryHeartBeatFlow())
                .done(new FlowDoneHandler(completion) {
                    @Override
                    public void handle(Map data) {
                        completion.success();
                    }
                }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    private void createFaultToleranceVm(VmInstanceVO pvm, final VmInstanceVO svm, final FaultToleranceVmGroupVO faultToleranceVmGroupVO, String avoidHostUuid, CreateFaultToleranceVmMsg msg, Completion completion) {
        FlowChain chain = new ShareFlowChain();
        chain.then(new ShareFlow() {
            FaultToleranceVmStarter starter = new FaultToleranceVmStarter();

            @Override
            public void setup() {
                flow(new Flow() {
                    String __name__ = "prepare-port-and-context";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        logger.debug("internal create port");

                        starter.fullSync = true;
                        starter.primaryVm = VmInstanceInventory.valueOf(pvm);
                        starter.secondaryVm = VmInstanceInventory.valueOf(svm);

                        new SQLBatch() {
                            @Override
                            protected void scripts() {
                                List<FaultToleranceVmInstanceGroupHostPortRefVO> refVOs = q(FaultToleranceVmInstanceGroupHostPortRefVO.class)
                                        .eq(FaultToleranceVmInstanceGroupHostPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                        .list();

                                if (refVOs.isEmpty()) {
                                    trigger.fail(operr("not fault tolerance vm port found"));
                                    return;
                                }

                                if (refVOs.size() != 1) {
                                    Iterator<FaultToleranceVmInstanceGroupHostPortRefVO> iterator = refVOs.iterator();
                                    while (iterator.hasNext()) {
                                        FaultToleranceVmInstanceGroupHostPortRefVO ref = iterator.next();
                                        if (q(HostPortVO.class)
                                                .eq(HostPortVO_.id, ref.getPrimaryVmMonitorPortId())
                                                .eq(HostPortVO_.hostUuid, pvm.getHostUuid())
                                                .isExists()) {
                                            continue;
                                        }

                                        sql(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(
                                                ref.getNbdServerPortId(),
                                                ref.getBlockReplicationPortId(),
                                                ref.getSecondaryVmMonitorPortId(),
                                                ref.getPrimaryVmMonitorPortId(),
                                                ref.getReservedVmMigrationPortId())).delete();
                                        remove(ref);
                                        iterator.remove();
                                    }
                                }

                                FaultToleranceVmInstanceGroupHostPortRefVO refVO = refVOs.get(0);

                                for (VmNicVO nic : faultToleranceVmGroupVO.getVmNics()) {
                                    List<VmInstanceVmNicRedirectPortRefVO> refVOS = q(VmInstanceVmNicRedirectPortRefVO.class)
                                            .eq(VmInstanceVmNicRedirectPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                            .eq(VmInstanceVmNicRedirectPortRefVO_.vmNicUuid, nic.getUuid())
                                            .list();
                                    VmInstanceVmNicRedirectPortRefVO nicRedirectPortRefVO;
                                    if (refVOS.size() > 1) {
                                        Iterator<VmInstanceVmNicRedirectPortRefVO> iterator = refVOS.iterator();
                                        while (iterator.hasNext()) {
                                            VmInstanceVmNicRedirectPortRefVO ref = iterator.next();
                                            if (q(HostPortVO.class)
                                                    .eq(HostPortVO_.id, ref.getMirrorPortId())
                                                    .eq(HostPortVO_.hostUuid, pvm.getHostUuid())
                                                    .isExists()) {
                                                continue;
                                            }

                                            iterator.remove();
                                        }

                                        nicRedirectPortRefVO = refVOS.get(0);
                                    } else if (refVOS.size() == 1) {
                                        nicRedirectPortRefVO = refVOS.get(0);
                                    } else {
                                        nicRedirectPortRefVO = null;
                                    }

                                    if (nicRedirectPortRefVO == null) {
                                        VmInstanceVmNicRedirectPortRefVO redirectPortRefVO = new VmInstanceVmNicRedirectPortRefVO();
                                        ErrorCode errorCode = allocateNicRedirectPorts(nic.getUuid(), redirectPortRefVO);
                                        if (errorCode != null) {
                                            trigger.fail(errorCode);
                                            return;
                                        }
                                        persist(redirectPortRefVO);
                                    } else {
                                        HostPortVO port = dbf.findById(nicRedirectPortRefVO.getMirrorPortId(), HostPortVO.class);
                                        if (port != null && port.getHostUuid().equals(starter.primaryVm.getHostUuid())) {
                                            continue;
                                        }

                                        logger.debug("primary vm host changed need to reallocate port");

                                        sql(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(
                                                nicRedirectPortRefVO.getMirrorPortId(),
                                                nicRedirectPortRefVO.getPrimaryInPortId(),
                                                nicRedirectPortRefVO.getSecondaryInPortId(),
                                                nicRedirectPortRefVO.getPrimaryOutPortId()))
                                                .delete();

                                        ErrorCode errorCode = allocateNicRedirectPorts(nic.getUuid(), nicRedirectPortRefVO);
                                        if (errorCode != null) {
                                            trigger.fail(errorCode);
                                            return;
                                        }

                                        merge(nicRedirectPortRefVO);
                                    }
                                }

                                starter.vmNicRedirectPorts = getVmNicRedirectPortsFromNics(faultToleranceVmGroupVO.getVmNics());

                                starter.primaryMonitorPort = dbf.findById(refVO.getPrimaryVmMonitorPortId(), HostPortVO.class);
                            }

                            private ErrorCode allocateNicRedirectPorts(String nicUuid, VmInstanceVmNicRedirectPortRefVO redirectPortRefVO) {
                                AllocateHostPortMsg amsg = new AllocateHostPortMsg();
                                amsg.setHostUuid(pvm.getHostUuid());
                                amsg.setAllocateCount(4);
                                bus.makeTargetServiceIdByResourceUuid(amsg, HostConstant.SERVICE_ID, amsg.getHostUuid());
                                MessageReply areply = bus.call(amsg);

                                if (!areply.isSuccess()) {
                                    return operr("failed to allocate port of nic[uuid: %s] on host[uuid: %s]", nicUuid, pvm.getHostUuid());
                                }

                                AllocateHostPortReply reply = areply.castReply();
                                if (reply.getHostPortIds().size() != 4) {
                                    return operr("allocated port num less than requested of nic[uuid: %s] on host[uuid: %s]", nicUuid, pvm.getHostUuid());
                                }

                                redirectPortRefVO.setPrimaryInPortId(reply.getHostPortIds().get(0));
                                redirectPortRefVO.setPrimaryOutPortId(reply.getHostPortIds().get(1));
                                redirectPortRefVO.setMirrorPortId(reply.getHostPortIds().get(2));
                                redirectPortRefVO.setSecondaryInPortId(reply.getHostPortIds().get(3));
                                redirectPortRefVO.setVmNicUuid(nicUuid);
                                redirectPortRefVO.setVmInstanceUuid(faultToleranceVmGroupVO.getUuid());
                                return null;
                            }
                        }.execute();

                        trigger.next();
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        FaultToleranceVmInstanceGroupHostPortRefVO ref = Q.New(FaultToleranceVmInstanceGroupHostPortRefVO.class)
                                .eq(FaultToleranceVmInstanceGroupHostPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                .find();

                        if (ref != null) {
                            SQL.New(HostPortVO.class).in(HostPortVO_.id, Arrays.asList(
                                    ref.getNbdServerPortId(),
                                    ref.getBlockReplicationPortId(),
                                    ref.getSecondaryVmMonitorPortId())).delete();
                        }

                        if (starter.secondaryVm == null || msg.isSkipSvmRollBack()) {
                            trigger.rollback();
                            return;
                        }

                        if (VmInstanceState.Created.toString().equals(starter.secondaryVm.getState())) {
                            faultToleranceVmGroupVO.setSecondaryVmInstanceUuid(null);
                            dbf.updateAndRefresh(faultToleranceVmGroupVO);
                            dbf.removeByPrimaryKey(starter.secondaryVm.getUuid(), VmInstanceVO.class);
                        } else {
                            DestroyVmInstanceMsg dmsg = new DestroyVmInstanceMsg();
                            dmsg.setVmInstanceUuid(starter.secondaryVm.getUuid());
                            dmsg.setDeletionPolicy(VmInstanceDeletionPolicyManager.VmInstanceDeletionPolicy.Direct);
                            bus.makeTargetServiceIdByResourceUuid(dmsg, VmInstanceConstant.SERVICE_ID, dmsg.getVmInstanceUuid());
                            bus.send(dmsg, new CloudBusCallBack(null) {
                                @Override
                                public void run(MessageReply reply) {
                                    if (dbf.isExist(starter.secondaryVm.getUuid(), VmInstanceVO.class)) {
                                        return;
                                    }

                                    faultToleranceVmGroupVO.setSecondaryVmInstanceUuid(null);
                                    dbf.updateAndRefresh(faultToleranceVmGroupVO);
                                }
                            });
                        }

                        trigger.rollback();
                    }
                });

                if (!msg.isSkipPrimaryVmConfig()) {
                    flow(starter.getConfigPrimaryVmFlow());
                }

                flow(new Flow() {
                    String __name__ = "start-secondary-vm";

                    @Override
                    public void run(FlowTrigger trigger, Map data) {
                        InstantiateNewCreatedVmInstanceMsg cmsg = new InstantiateNewCreatedVmInstanceMsg();
                        cmsg.setVmInstanceInventory(starter.secondaryVm);
                        cmsg.setPrimaryStorageUuidForRootVolume(pvm.getRootVolume().getPrimaryStorageUuid());
                        cmsg.setStrategy(VmCreationStrategy.CreatedPaused.toString());
                        cmsg.setL3NetworkUuids(getVmNicSpecsFromAPICreateVmInstanceMsg(new ArrayList<>(), faultToleranceVmGroupVO.getVmNics().stream().map(VmNicVO::getL3NetworkUuid).collect(Collectors.toList())));

                        DiskOfferingVO dvo = new DiskOfferingVO();
                        dvo.setUuid(Platform.getUuid());
                        dvo.setAccountUuid(accountManager.getOwnerAccountUuidOfResource(pvm.getUuid()));
                        dvo.setDiskSize(pvm.getRootVolume().getSize());
                        dvo.setName("for-create-vm-" + svm.getUuid());
                        dvo.setType("DefaultDiskOfferingType");
                        dvo.setState(DiskOfferingState.Enabled);
                        dvo.setAllocatorStrategy(PrimaryStorageConstant.DEFAULT_PRIMARY_STORAGE_ALLOCATION_STRATEGY_TYPE);
                        dbf.persist(dvo);
                        cmsg.setRootDiskOfferingUuid(dvo.getUuid());

                        bus.makeTargetServiceIdByResourceUuid(cmsg, VmInstanceConstant.SERVICE_ID, cmsg.getVmInstanceUuid());
                        bus.send(cmsg, new CloudBusCallBack(trigger) {
                            @Override
                            public void run(MessageReply reply) {
                                if (cmsg.getRootDiskOfferingUuid() != null) {
                                    dbf.removeByPrimaryKey(cmsg.getRootDiskOfferingUuid(), DiskOfferingVO.class);
                                }

                                if (!reply.isSuccess()) {
                                    cleanDB();
                                    trigger.fail(reply.getError());
                                    return;
                                }

                                InstantiateNewCreatedVmInstanceReply r = reply.castReply();
                                starter.secondaryVm = r.getVmInventory();

                                VmInstanceVO svo = dbf.findByUuid(r.getVmInventory().getUuid(), VmInstanceVO.class);
                                svo.setLastHostUuid(r.getVmInventory().getHostUuid());
                                svo.setHostUuid(r.getVmInventory().getHostUuid());
                                svo.setClusterUuid(r.getVmInventory().getClusterUuid());
                                svo.setZoneUuid(r.getVmInventory().getZoneUuid());
                                svo.setHypervisorType(r.getVmInventory().getHypervisorType());
                                svo.setRootVolumeUuid(r.getVmInventory().getRootVolumeUuid());
                                svo.setState(VmInstanceState.Running);
                                svo.setType(VmInstanceConstant.USER_VM_TYPE);
                                dbf.updateAndRefresh(svo);

                                // TODO change all ports related to ft vm group
                                FaultToleranceVmInstanceGroupHostPortRefVO ref = Q.New(FaultToleranceVmInstanceGroupHostPortRefVO.class)
                                        .eq(FaultToleranceVmInstanceGroupHostPortRefVO_.vmInstanceUuid, faultToleranceVmGroupVO.getUuid())
                                        .find();
                                starter.blockReplicationPort = dbf.findById(ref.getBlockReplicationPortId(), HostPortVO.class);
                                starter.nbdServerPort = dbf.findById(ref.getNbdServerPortId(), HostPortVO.class);
                                starter.secondaryMonitorPort = dbf.findById(ref.getSecondaryVmMonitorPortId(), HostPortVO.class);
                                starter.primaryMonitorPort = dbf.findById(ref.getPrimaryVmMonitorPortId(), HostPortVO.class);

                                trigger.next();
                            }
                        });
                    }

                    private void cleanDB() {
                        // if InstantiateNewCreatedVmInstanceMsg failed due to mn crash, clean db
                        VmInstanceVO vm = dbf.findByUuid(starter.secondaryVm.getUuid(), VmInstanceVO.class);
                        if (vm != null) {
                            dbf.remove(vm);
                            // clean up EO, otherwise API-retry may cause conflict if
                            // the resource uuid is set
                            try {
                                dbf.eoCleanup(VmInstanceVO.class, vm.getUuid());
                            } catch (Exception e) {
                                logger.warn(e.getMessage());
                            }
                        }
                    }

                    @Override
                    public void rollback(FlowRollback trigger, Map data) {
                        trigger.rollback();
                    }
                });

                flow(starter.getConfigSecondaryVmFlow());
                flow(starter.getStartColoSyncFlow());
                flow(starter.getWaitColoSecondaryReadyFlow());
                flow(starter.getRegisterPrimaryHeartBeatFlow());
                flow(starter.getRegisterSecondaryHeartBeatFlow());
            }
        }).error(new FlowErrorHandler(null) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).done(new FlowDoneHandler(null) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).start();
    }

    @Override
    public boolean stop() {
        return true;
    }

    public List<String> getCreateSecondaryVmWorkFlow() {
        return createSecondaryVmWorkFlow;
    }

    public void setCreateSecondaryVmWorkFlow(List<String> createSecondaryVmWorkFlow) {
        this.createSecondaryVmWorkFlow = createSecondaryVmWorkFlow;
    }

    @Override
    public FlowChainBuilder getCreateSecondaryVmWorkFlowBuilder() {
        return createSecondaryVmWorkFlowBuilder;
    }

    public void setCreateSecondaryVmWorkFlowBuilder(FlowChainBuilder createSecondaryVmWorkFlowBuilder) {
        this.createSecondaryVmWorkFlowBuilder = createSecondaryVmWorkFlowBuilder;
    }

    public List<String> getStartFaultToleranceVmGroupWorkFlowElements() {
        return startFaultToleranceVmGroupWorkFlowElements;
    }

    public void setStartFaultToleranceVmGroupWorkFlowElements(List<String> startFaultToleranceVmGroupWorkFlowElements) {
        this.startFaultToleranceVmGroupWorkFlowElements = startFaultToleranceVmGroupWorkFlowElements;
    }

    public List<String> getDestroyFaultToleranceVmGroupWorkFlowElements() {
        return destroyFaultToleranceVmGroupWorkFlowElements;
    }

    public void setDestroyFaultToleranceVmGroupWorkFlowElements(List<String> destroyFaultToleranceVmGroupWorkFlowElements) {
        this.destroyFaultToleranceVmGroupWorkFlowElements = destroyFaultToleranceVmGroupWorkFlowElements;
    }

    @Override
    public FlowChainBuilder getStartFaultToleranceVmGroupWorkFlowBuilder() {
        return startFaultToleranceVmGroupWorkFlowBuilder;
    }

    @Override
    public FlowChainBuilder getDestroyFaultToleranceVmGroupWorkFlowBuilder() {
        return destroyFaultToleranceVmGroupWorkFlowBuilder;
    }

    public void setStartFaultToleranceVmGroupWorkFlowBuilder(FlowChainBuilder startFaultToleranceVmGroupWorkFlowBuilder) {
        this.startFaultToleranceVmGroupWorkFlowBuilder = startFaultToleranceVmGroupWorkFlowBuilder;
    }

    @Override
    public Flow createKvmHostConnectingFlow(KVMHostConnectedContext context) {
        return new Flow() {
            @Override
            public boolean skip(Map data) {
                return !FaultToleranceGlobalConfig.ALL.value(Boolean.class);
            }

            @Override
            public void run(FlowTrigger trigger, Map data) {
                DeployColoQemuCmd cmd = new DeployColoQemuCmd();
                cmd.qemuUrl = String.format(FaultToleranceGlobalConfig.COLO_QEMU_PATH.value(), Platform.getManagementServerIp(), Platform.getManagementNodeServicePort());

                KVMHostAsyncHttpCallMsg msg = new KVMHostAsyncHttpCallMsg();
                msg.setHostUuid(context.getInventory().getUuid());
                msg.setCommand(cmd);
                msg.setNoStatusCheck(true);
                msg.setPath(DEPLOY_COLO_QEMU_PATH);
                bus.makeTargetServiceIdByResourceUuid(msg, HostConstant.SERVICE_ID, context.getInventory().getUuid());
                bus.send(msg, new CloudBusCallBack(trigger) {
                    @Override
                    public void run(MessageReply reply) {
                        if (!reply.isSuccess()) {
                            trigger.fail(reply.getError());
                            return;
                        }

                        trigger.next();
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                trigger.rollback();
            }
        };
    }

    @Override
    public void beforeBackup(ExternalBackupSpec spec, NoErrorCompletion completion) {
        if (spec.getAllHostUuids() == null || spec.getAllHostUuids().isEmpty()) {
            completion.done();
            return;
        }

        Set<String> allVmUuids = spec.getAllVmUuids().values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        List<String> secVmUuids = SQL.New("select vm.uuid from VmInstanceVO vm, FaultToleranceVmGroupVO ft" +
                " where vm.uuid = ft.secondaryVmInstanceUuid" +
                " and vm.uuid in :vmUuids" +
                " and vm.hostUuid is not null", String.class)
                .param("vmUuids", allVmUuids)
                .list();

        new While<>(secVmUuids).step((vmUuid, compl) -> {
            StopVmInstanceMsg msg = new StopVmInstanceMsg();
            msg.setVmInstanceUuid(vmUuid);
            bus.makeTargetServiceIdByResourceUuid(msg, VmInstanceConstant.SERVICE_ID, vmUuid);
            bus.send(msg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    compl.done();
                }
            });
        }, 10).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                completion.done();
            }
        });
    }

    /**
     * Secondary vm backup should be later than primary vm backup. Make minimal changes.
     * @param msgs
     */
    @Override
    public void sortBackupVm(List<CreateVmExternalBackupMessage> msgs) {
        Map<String, Integer> vmUuidIndex = new HashMap<>();
        for (int i = 0; i < msgs.size(); i++) {
            vmUuidIndex.put(msgs.get(i).getVmInstanceUuid(), i);
        }

        List<Tuple> groups = Q.New(FaultToleranceVmGroupVO.class)
                .in(FaultToleranceVmGroupVO_.primaryVmInstanceUuid, vmUuidIndex.keySet())
                .select(FaultToleranceVmGroupVO_.primaryVmInstanceUuid, FaultToleranceVmGroupVO_.secondaryVmInstanceUuid)
                .listTuple();

        for (Tuple group : groups) {
            Integer priIndex = vmUuidIndex.get(group.get(0, String.class));
            Integer secIndex = vmUuidIndex.get(group.get(1, String.class));
            if (secIndex != null && priIndex < secIndex) {
                Collections.swap(msgs, priIndex, secIndex);
            }
        }
    }

    @Override
    public void failToBackup(ExternalBackupSpec spec, NoErrorCompletion completion) {
        completion.done();
    }

    @StaticInit
    static void staticInit() {
        if (CoreGlobalProperty.UNIT_TEST_ON) {
            return;
        }
        if (!DeployMode.mini.toString().equals(MevocoGlobalProperty.UI_MODE)) {
            BannedModule.bannedModules.add("org.zstack.faulttolerance");
        }
    }
}
