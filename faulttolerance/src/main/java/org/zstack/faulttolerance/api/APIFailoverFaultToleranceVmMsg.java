package org.zstack.faulttolerance.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.header.vm.VmInstanceVO;

@TagResourceType(VmInstanceVO.class)
@RestRequest(
        path = "/vm-instances/fault-tolerance",
        method = HttpMethod.PUT,
        responseClass = APIFailoverFaultToleranceVmEvent.class,
        isAction = true
)
public class APIFailoverFaultToleranceVmMsg extends APIMessage {
    @APIParam(resourceType = VmInstanceVO.class)
    private String faultToleranceVmUuid;

    public String getFaultToleranceVmUuid() {
        return faultToleranceVmUuid;
    }

    public void setFaultToleranceVmUuid(String faultToleranceVmUuid) {
        this.faultToleranceVmUuid = faultToleranceVmUuid;
    }
}
