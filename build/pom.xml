<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>zstack</artifactId>
        <groupId>org.zstack</groupId>
        <version>5.4.0</version>
        <relativePath>..</relativePath>
    </parent>
    <artifactId>build</artifactId>
    <packaging>war</packaging>

    <profiles>
        <profile>
            <id>premium</id>

            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>2.3</version>
                        <configuration>
                            <packagingExcludes>
                                %regex[WEB-INF/lib/maven(?!-artifact).*.jar],
                                WEB-INF/lib/plexus*.jar,
                                WEB-INF/lib/cvsclient*.jar,
                                WEB-INF/lib/classworlds*.jar,
                                WEB-INF/lib/junit*.jar,
                                WEB-INF/lib/regexp*.jar,
                                WEB-INF/lib/jtidy*.jar,
                                WEB-INF/lib/servlet-api*.jar,
                                WEB-INF/lib/c3p0-*******.jar,
                                WEB-INF/lib/reflections-0.9.8.jar,
                                WEB-INF/lib/groovy-2.4.7.jar,
                                WEB-INF/lib/sisu-guava-0.9.9.jar,
                                WEB-INF/lib/groovy-sandbox-1.19.jar,
                                WEB-INF/lib/bcpkix-jdk18on-1.72.jar,
                                WEB-INF/lib/bcprov-jdk18on-1.72.jar,
                                WEB-INF/lib/bcutil-jdk18on-1.72.jar,
                            </packagingExcludes>
                            <webResources>
                                <resource>
                                    <directory>../conf</directory>
                                    <targetPath>WEB-INF/classes</targetPath>
                                    <includes>
                                        <include>**/*</include>
                                    </includes>
                                    <excludes>
                                        <exclude>zstack.xml</exclude>
                                        <exclude>persistence.xml</exclude>
                                        <exclude>springConfigXml/localStorage.xml</exclude>
                                        <exclude>springConfigXml/HostAllocatorManager.xml</exclude>
                                        <exclude>springConfigXml/NfsPrimaryStorage.xml</exclude>
                                        <exclude>springConfigXml/sharedMountPointPrimaryStorage.xml</exclude>
                                        <exclude>springConfigXml/ceph.xml</exclude>
                                        <exclude>springConfigXml/AccountManager.xml</exclude>
                                        <exclude>serviceConfig/metadata.xml</exclude>
                                        <exclude>searchConfig/indexConfig.xml</exclude>
                                        <exclude>springConfigXml/resourceConfig.xml</exclude>
                                    </excludes>
                                </resource>
                                <resource>
                                    <directory>../conf</directory>
                                    <targetPath>WEB-INF/</targetPath>
                                    <includes>
                                        <include>urlrewrite.xml</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>../premium/conf</directory>
                                    <targetPath>WEB-INF/classes</targetPath>
                                    <includes>
                                        <include>**/*</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>.</directory>
                                    <targetPath>META-INF/</targetPath>
                                    <includes>
                                        <include>context.xml</include>
                                    </includes>
                                </resource>
                            </webResources>
                            <webXml>../premium/conf/cas-web.xml</webXml>
                        </configuration>
                    </plugin>

                </plugins>
            </build>

            <dependencies>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>core</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>external-service</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>premium-external-service</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>compute</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>header</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>portal</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>utils</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>image</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>network</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>configuration</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>identity</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>search</artifactId>
                    <version>${project.version}</version>
                </dependency>

                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>applianceVm</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>ceph</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>eip</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>flatNetworkProvider</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>kvm</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>loadBalancer</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>localstorage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>externalStorage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zbs</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>cbd</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>expon</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>xinfini</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>vhost</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>iscsi</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>mediator</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>nfsPrimaryStorage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>portForwarding</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>securityGroup</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sftpBackupStorage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>vip</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>virtualRouterProvider</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sharedMountPointPrimaryStorage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>ldap</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>rest</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>vxlan</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>longjob</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>mevoco</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>simulator2</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>cloudformation</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>appcenter</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>hybrid</artifactId>
                    <version>${project.version}</version>
                </dependency>
		        <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zwatch</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sns</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>premium-host-allocator</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sharedblock</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>ministorage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>storage-device</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>mini</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>autoscaling</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>drs</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>volumebackup</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>imagereplicator</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>xdragon</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zstack-iam2</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sanyuan</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>proxy</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zstack-ticket</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>aliyun-storage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>block-primary-storage</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>memory-balloon</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sdk</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>twoFactorAuthentication</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>crypto</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>loginControl</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>log</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>baremetal</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>baremetal2</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>external-api-adapter</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>aliyun-proxy</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>v2v</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>upgrade-hack</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>yunshan</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>iam2-ldap-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>storage-ha-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>routeProtocol</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>flowMeter</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>accessKey</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>tag2</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>vpc</artifactId>
                    <version>${project.version}</version>
                </dependency>

                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>policyRoute</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>vpcFirewall</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sns-aliyun-sms</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>guesttools</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>faulttolerance</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sdnController</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>iam2-script-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>iam2project-securitygroup-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zbox</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>externalbackup</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zboxbackup</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>log4j2</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>acl</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>slb</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>billing</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>network-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>cas-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sugonSdnController</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>ovf</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>sso-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>cube</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zops-plugin</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zsv</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>virtualSwitchNetwork</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>hostNetworkInterface</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>ovn</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>observabilityServer</artifactId>
                    <version>${project.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.zstack</groupId>
                    <artifactId>zdfs</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <finalName>zstack</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.2.2</version>
                <configuration>
                    <descriptors>
                        <descriptor>assembly.xml</descriptor>
                    </descriptors>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <packagingExcludes>
                        %regex[WEB-INF/lib/maven(?!-artifact).*.jar],
                        WEB-INF/lib/plexus*.jar,
                        WEB-INF/lib/cvsclient*.jar,
                        WEB-INF/lib/classworlds*.jar,
                        WEB-INF/lib/junit*.jar,
                        WEB-INF/lib/regexp*.jar,
                        WEB-INF/lib/jtidy*.jar,
                        WEB-INF/lib/servlet-api*.jar,
                        WEB-INF/lib/c3p0-*******.jar,
                        WEB-INF/lib/reflections-0.9.8.jar,
                        WEB-INF/lib/groovy-2.4.7.jar,
                    </packagingExcludes>
                    <webResources>
                        <resource>
                            <directory>../conf</directory>
                            <targetPath>WEB-INF/classes</targetPath>
                            <includes>
                                <include>**/*</include>
                            </includes>
                        </resource>
                        <resource>
                            <directory>../conf</directory>
                            <targetPath>WEB-INF/</targetPath>
                            <includes>
                                <include>urlrewrite.xml</include>
                            </includes>
                        </resource>
                        <resource>
                            <directory>.</directory>
                            <targetPath>META-INF/</targetPath>
                            <includes>
                                <include>context.xml</include>
                            </includes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>external-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>compute</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>header</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>portal</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>image</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>network</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>configuration</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>identity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>search</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>applianceVm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>ceph</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>eip</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>flatNetworkProvider</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>kvm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>loadBalancer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>localstorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>externalStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>zbs</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>cbd</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>expon</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>xinfini</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>vhost</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>iscsi</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>mediator</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>nfsPrimaryStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>portForwarding</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>securityGroup</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sftpBackupStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>vip</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>virtualRouterProvider</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sharedMountPointPrimaryStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>ldap</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>rest</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>vxlan</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>longjob</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sdk</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>acl</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>directory</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>macvlan</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>pvlan</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sshKeyPair</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>ai</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>container</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>iam2-container</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>snmp</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>hostNetworkInterface</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>observabilityServer</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
