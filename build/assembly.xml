<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
	<id>zstack</id>
	<formats>
		<format>dir</format>
	</formats>

	<baseDirectory>zstack</baseDirectory>
	<dependencySets>
		<dependencySet>
			<outputDirectory>lib</outputDirectory>
			<useTransitiveDependencies>true</useTransitiveDependencies>
			<useProjectArtifact>false</useProjectArtifact>
			<useProjectAttachments>false</useProjectAttachments>
			<scope>runtime</scope>
			<excludes>
				<exclude>*maven*:*:*</exclude>
				<exclude>*:*:plexus*</exclude>
				<exclude>*:*:cvsclient*</exclude>
				<exclude>classworlds:classworlds</exclude>
				<exclude>ch.ethz.ganymed:ganymed-ssh2</exclude>
				<exclude>*:*:junit*</exclude>
				<exclude>org.hamcrest:hamcrest-core</exclude>
				<exclude>regexp:regexp</exclude>
				<exclude> jtidy:jtidy</exclude>
				<exclude>*:*:servlet-api*</exclude>
			</excludes>
		</dependencySet>
	</dependencySets>
	<fileSets>
		<fileSet>
			<directory>../bin</directory>
		</fileSet>
		<fileSet>
			<directory>../conf</directory>
		</fileSet>
	</fileSets>
	<files>
		<file>
			<source>zstack</source>
			<fileMode>755</fileMode>
			<lineEnding>unix</lineEnding>
		</file>
		<file>
			<source>zstack-debug</source>
			<fileMode>755</fileMode>
			<lineEnding>unix</lineEnding>
		</file>
	</files>
</assembly>