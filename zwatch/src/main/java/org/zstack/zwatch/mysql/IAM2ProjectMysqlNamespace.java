package org.zstack.zwatch.mysql;

import org.zstack.compute.vm.VmQuotaConstant;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.header.affinitygroup.AffinityGroupConstants;
import org.zstack.header.affinitygroup.AffinityGroupVO;
import org.zstack.header.core.StaticInit;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.identity.AccountResourceRefVO;
import org.zstack.header.identity.QuotaVO;
import org.zstack.header.identity.QuotaVO_;
import org.zstack.header.image.ImageVO;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l3.L3NetworkQuotaConstant;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.storage.backup.VolumeBackupVO;
import org.zstack.header.storage.snapshot.VolumeSnapshotVO;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.volume.VolumeStatus;
import org.zstack.header.volume.VolumeType;
import org.zstack.header.volume.VolumeVO;
import org.zstack.iam2.entity.IAM2ProjectAccountRefVO;
import org.zstack.iam2.entity.ProjectState;
import org.zstack.image.ImageQuotaConstant;
import org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkFactory;
import org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkQuotaConstant;
import org.zstack.network.securitygroup.SecurityGroupQuotaConstant;
import org.zstack.network.securitygroup.SecurityGroupVO;
import org.zstack.network.service.eip.EipQuotaConstant;
import org.zstack.network.service.eip.EipVO;
import org.zstack.network.service.lb.LoadBalanceQuotaConstant;
import org.zstack.network.service.lb.LoadBalancerListenerVO;
import org.zstack.network.service.lb.LoadBalancerVO;
import org.zstack.network.service.portforwarding.PortForwardingQuotaConstant;
import org.zstack.network.service.portforwarding.PortForwardingRuleVO;
import org.zstack.network.service.vip.VipQuotaConstant;
import org.zstack.network.service.vip.VipVO;
import org.zstack.pciDevice.PciDeviceConstants;
import org.zstack.pciDevice.PciDeviceType;
import org.zstack.storage.snapshot.VolumeSnapshotQuotaConstant;
import org.zstack.zwatch.datatype.*;
import org.zstack.zwatch.namespace.IAM2ProjectCountNamespace;

import javax.persistence.Tuple;
import java.util.*;

public class IAM2ProjectMysqlNamespace extends AbstractMysqlNamespace {

    private String VOLUME_BACKUP_SIZE = "volume.backup.size";
    private String VOLUME_BACKUP_NUM = "volume.backup.num";

    public IAM2ProjectMysqlNamespace(Namespace namespace) {
        super(namespace);
    }

    @StaticInit
    static void staticInit() {
        MysqlNamespace.namespacesClasses.put(IAM2ProjectCountNamespace.class, IAM2ProjectMysqlNamespace.class);
    }

    @Override
    protected List<Datapoint> doQuery(MetricQueryObject queryObject) {

        if (queryObject.getLabels().size() == 0) {
            return new ArrayList<>();
        }

        String resourceLabelName = IAM2ProjectCountNamespace.LabelNames.projectUuid.toString();

        List<String> projectUuids = null;

        for (Label label : queryObject.getLabels()) {
            if (resourceLabelName.equals(label.getKey())) {
                projectUuids = !label.getValue().contains("*") ? Arrays.asList(label.getValue().split("\\|")) : new ArrayList<>();
                break;
            }
        }

        if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.VmQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {

                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                List<Tuple> listCount = SQL.New("select ref.accountUuid, count(ref.resourceUuid) from AccountResourceRefVO ref, VmInstanceVO vm " +
                        "where ref.accountUuid in(:accountUuids) and ref.resourceType= :type and vm.uuid = ref.resourceUuid " +
                        "and vm.state != :state group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Destroyed)
                        .list();

                Map<String, Long> accountResourceCount = new HashMap<>();

                for (Tuple t : listCount) {
                    accountResourceCount.put(t.get(0, String.class), t.get(1, Long.class));
                }

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VmQuotaConstant.VM_TOTAL_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));

            } else {
                Long value = SQL.New("select count(ref.resourceUuid) from AccountResourceRefVO ref, VmInstanceVO vm " +
                        "where ref.accountUuid=:accountUuid and ref.resourceType= :type and vm.uuid = ref.resourceUuid " +
                        "and vm.state != :state", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Destroyed)
                        .find();
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VmQuotaConstant.VM_TOTAL_NUM);
                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);

            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.RunningVMInPercent.getName())) {

            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {

                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                List<Tuple> listCount = SQL.New("select ref.accountUuid, count(ref.resourceUuid) from AccountResourceRefVO ref, VmInstanceVO vm " +
                        "where ref.accountUuid in(:accountUuids) and ref.resourceType= :type and vm.uuid = ref.resourceUuid " +
                        "and vm.state = :state group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Running)
                        .list();

                Map<String, Long> accountVmRunning = new HashMap<>();

                for (Tuple t : listCount) {
                    accountVmRunning.put(t.get(0, String.class), t.get(1, Long.class));
                }

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VmQuotaConstant.VM_RUNNING_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountVmRunning, accountQuota, resourceLabelName));

            } else {
                Long running = SQL.New("select count(ref.resourceUuid) from AccountResourceRefVO ref, VmInstanceVO vm " +
                        "where ref.accountUuid=:accountUuid and ref.resourceType= :type and vm.uuid = ref.resourceUuid " +
                        "and vm.state = :state", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Running)
                        .find();
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VmQuotaConstant.VM_RUNNING_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) running / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.CPUQuotaUsedInPercent.getName())) {

            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });


                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                List<Tuple> tuples = SQL.New("select ref.accountUuid, sum(vm.cpuNum) from VmInstanceVO vm, AccountResourceRefVO ref " +
                        "where vm.uuid = ref.resourceUuid and ref.accountUuid in (:accountUuids) " +
                        "and ref.resourceType = :type and vm.state != :state group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Destroyed)
                        .list();
                Map<String, Long> accountUseCpeNum = new HashMap<>();
                for (Tuple t : tuples) {
                    accountUseCpeNum.put(t.get(0, String.class), t.get(1, Long.class));
                }
                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VmQuotaConstant.VM_RUNNING_CPU_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountUseCpeNum, accountQuota, resourceLabelName));
            } else {
                Long value = SQL.New( "select sum(vm.cpuNum)" +
                        " from VmInstanceVO vm , AccountResourceRefVO ref " +
                        "where vm.uuid = ref.resourceUuid and " +
                        "ref.accountUuid = :accountUuid and " +
                        "ref.resourceType = :type and vm.state != :state", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Destroyed)
                        .find();

                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VmQuotaConstant.VM_RUNNING_CPU_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) (value == null ? 0: value) / quota) * 100);
            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.MemoryQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });


                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                List<Tuple> tuples = SQL.New("select ref.accountUuid, sum(vm.memorySize)" +
                        " from VmInstanceVO vm , AccountResourceRefVO ref " +
                        "where vm.uuid = ref.resourceUuid and " +
                        "ref.accountUuid in(:accountUuids) and " +
                        "ref.resourceType = :type and vm.state != :state group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Destroyed)
                        .list();
                Map<String, Long> accountUseMemoryNum = new HashMap<>();
                for (Tuple t : tuples) {
                    accountUseMemoryNum.put(t.get(0, String.class), t.get(1, Long.class));
                }
                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VmQuotaConstant.VM_RUNNING_MEMORY_SIZE);


                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountUseMemoryNum, accountQuota, resourceLabelName));

            } else {
                Long value = SQL.New( "select sum(vm.memorySize)" +
                        " from VmInstanceVO vm , AccountResourceRefVO ref " +
                        "where vm.uuid = ref.resourceUuid and " +
                        "ref.accountUuid = :accountUuid and " +
                        "ref.resourceType = :type and vm.state != :state", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("state", VmInstanceState.Destroyed)
                        .find();
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VmQuotaConstant.VM_RUNNING_MEMORY_SIZE);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) (value == null ? 0: value) / quota) * 100);
            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.GPUQuotaUsedInPercent.getName())) {

            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });


                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                List<Tuple> tuples = SQL.New("select ref.accountUuid, count(1) " +
                        "from PciDeviceVO pci, AccountResourceRefVO ref, VmInstanceVO vm " +
                        "ref.resourceUuid = vm.uuid  and pci.vmInstanceUuid = vm.uuid  " +
                        "and ref.accountUuid in(:accountUuids) " +
                        "and ref.resourceType = :type " +
                        "and (pci.type = :DeviceType1 or pci.type= :DeviceType2) group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", queryObject.getAccountUuid())
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("DeviceType1", PciDeviceType.GPU_3D_Controller)
                        .param("DeviceType2", PciDeviceType.GPU_Video_Controller)
                        .list();
                Map<String, Long> accountUseGpuNum = new HashMap<>();
                for (Tuple t : tuples) {
                    accountUseGpuNum.put(t.get(0, String.class), t.get(1, Long.class));
                }
                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, PciDeviceConstants.GPU_NUMBER);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountUseGpuNum, accountQuota, resourceLabelName));
            } else {
                Long value = SQL.New( "select count(1)  from PciDeviceVO pci, AccountResourceRefVO ref, VmInstanceVO vm " +
                        "where ref.resourceUuid = vm.uuid  and pci.vmInstanceUuid = vm.uuid " +
                        "and ref.accountUuid = :accountUuid " +
                        "and ref.resourceType = :type " +
                        "and (pci.type = :DeviceType1 " +
                        "or pci.type= :DeviceType2)", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", VmInstanceVO.class.getSimpleName())
                        .param("DeviceType1", PciDeviceType.GPU_3D_Controller)
                        .param("DeviceType2", PciDeviceType.GPU_Video_Controller)
                        .find();

                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), PciDeviceConstants.GPU_NUMBER);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.AffinityGroupQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();


                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        AffinityGroupVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, AffinityGroupConstants.AFFINITYGROUP_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));
            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), AffinityGroupVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), AffinityGroupConstants.AFFINITYGROUP_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.VolumeSnapshotQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        VolumeSnapshotVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VolumeSnapshotQuotaConstant.VOLUME_SNAPSHOT_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));
            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), VolumeSnapshotVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VolumeSnapshotQuotaConstant.VOLUME_SNAPSHOT_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.VolumeDataQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();


                List<Tuple> tuples = SQL.New("select ref.accountUuid, count(vo) " +
                        "from VolumeVO vo, AccountResourceRefVO ref " +
                        "where vo.uuid = ref.resourceUuid " +
                        "and ref.resourceType =:resourceType " +
                        "and ref.accountUuid in (:accountUuids) and vo.type = :type and vo.status = :status " +
                        "group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("resourceType", VolumeVO.class.getSimpleName())
                        .param("type", VolumeType.Data)
                        .param("status", VolumeStatus.Ready)
                        .list();

                Map<String, Long> accountResourceCount = new HashMap<>();
                for (Tuple t : tuples) {
                    accountResourceCount.put(t.get(0, String.class), t.get(1, Long.class));
                }

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VmQuotaConstant.DATA_VOLUME_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));
            } else {
                Long value = SQL.New("select count(vo) from VolumeVO vo, AccountResourceRefVO ref " +
                        "where vo.uuid = ref.resourceUuid " +
                        "and ref.resourceType =:resourceType and ref.accountUuid=:accountUuid " +
                        "and vo.type = :type and vo.status = :status", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("resourceType", VolumeVO.class.getSimpleName())
                        .param("type", VolumeType.Data)
                        .param("status", VolumeStatus.Ready)
                        .find();

                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VmQuotaConstant.DATA_VOLUME_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.VolumeCapacityQuotaUsedInPercent.getName())) {

            if(AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VmQuotaConstant.VOLUME_SIZE);

                List<Tuple> tuples = SQL.New("select ref.accountUuid, sum(vo.size) " +
                        "from VolumeVO vo , AccountResourceRefVO ref " +
                        "where vo.uuid = ref.resourceUuid and " +
                        "ref.accountUuid in(:accountUuids) and " +
                        "ref.resourceType = :type and vo.status != :status group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("type", VolumeVO.class.getSimpleName())
                        .param("status", VolumeStatus.NotInstantiated)
                        .list();
                Map<String, Long> accountUseVolumeCapacity = new HashMap<>();
                for (Tuple t : tuples) {
                    accountUseVolumeCapacity.put(t.get(0, String.class), t.get(1, Long.class));
                }
                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountUseVolumeCapacity, accountQuota, resourceLabelName));

            } else {
                Long value = SQL.New("select sum(vo.size) from VolumeVO vo, AccountResourceRefVO ref " +
                        "where vo.uuid = ref.resourceUuid " +
                        "and ref.resourceType =:type and ref.accountUuid=:accountUuid and vo.status != :status", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", VolumeVO.class.getSimpleName())
                        .param("status", VolumeStatus.NotInstantiated)
                        .find();
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VmQuotaConstant.VOLUME_SIZE);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double)(value == null ? 0: value)/ quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.ImageQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();


                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        ImageVO.class.getSimpleName());
                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, ImageQuotaConstant.IMAGE_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));

            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), ImageVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), ImageQuotaConstant.IMAGE_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }


        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.ImageCapacityQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {

                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();


                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, ImageQuotaConstant.IMAGE_SIZE);

                List<Tuple> tuples = SQL.New("select ref.accountUuid, sum(vo.actualSize) " +
                        "from ImageVO vo , AccountResourceRefVO ref " +
                        "where vo.uuid = ref.resourceUuid and " +
                        "ref.accountUuid in(:accountUuids) and " +
                        "ref.resourceType = :type group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("type", ImageVO.class.getSimpleName())
                        .list();
                Map<String, Long> accountUseVolumeCapacity = new HashMap<>();
                for (Tuple t : tuples) {
                    accountUseVolumeCapacity.put(t.get(0, String.class), t.get(1, Long.class));
                }
                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountUseVolumeCapacity, accountQuota, resourceLabelName));

            } else {
                Long value = SQL.New("select sum(vo.actualSize) " +
                        "from ImageVO vo, AccountResourceRefVO ref " +
                        "where vo.uuid = ref.resourceUuid " +
                        "and ref.resourceType=:type and ref.accountUuid=:accountUuid", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", ImageVO.class.getSimpleName())
                        .find();
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), ImageQuotaConstant.IMAGE_SIZE);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) (value == null ? 0: value) / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.BackupStorageCapacityQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();


                List<Tuple> tuples = SQL.New("select ref.accountUuid, sum(vo.size) " +
                        "from VolumeBackupVO vo , AccountResourceRefVO ref " +
                        "where vo.uuid = ref.resourceUuid and " +
                        "ref.accountUuid in(:accountUuids) and " +
                        "ref.resourceType =:type group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("type", VolumeBackupVO.class.getSimpleName())
                        .list();

                Map<String, Long> accountAvailableBackupStorageCapacity = new HashMap<>();
                for (Tuple t : tuples) {
                    accountAvailableBackupStorageCapacity.put(t.get(0, String.class), t.get(1, Long.class));
                }
                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VOLUME_BACKUP_SIZE);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef,
                        accountAvailableBackupStorageCapacity, accountQuota, resourceLabelName));

            } else {
                Long value = SQL.New("select sum(vo.size) " +
                        "from VolumeBackupVO  vo, AccountResourceRefVO ref where vo.uuid = ref.resourceUuid " +
                        "and ref.resourceType=:type and ref.accountUuid=:accountUuid", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("type", VolumeBackupVO.class.getSimpleName())
                        .find();

                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VOLUME_BACKUP_SIZE);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) (value == null ? 0: value) / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.BackupStorageQuotaUsedInPercent.getName())) {

            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        VolumeBackupVO.class.getSimpleName());
                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VOLUME_BACKUP_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef,
                        accountResourceCount, accountQuota, resourceLabelName));

            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), VolumeBackupVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VOLUME_BACKUP_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.VxlanNetworkQuotaUsedInPercent.getName())) {

            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                long count = SQL.New("select count(ref.resourceUuid) " +
                        "from AccountResourceRefVO ref, L2NetworkVO l2 " +
                        "where ref.accountUuid in (:accountUuids) and ref.resourceType= :resourceType and " +
                        "ref.resourceUuid = l2.uuid and l2.type = :type " +
                        "group by ref.accountUuid", Long.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("resourceType", L2NetworkVO.class.getSimpleName())
                        .param("type", VxlanNetworkFactory.type.toString())
                        .find();

                Map<String, Long> accountResourceCount = new HashMap<>();

                SQL.New("select ref.accountUuid, count(ref.resourceUuid) " +
                        "from AccountResourceRefVO ref, L2NetworkVO l2 " +
                        "where ref.accountUuid in (:accountUuids) and ref.resourceType= :resourceType and " +
                        "ref.resourceUuid = l2.uuid and l2.type = :type " +
                        "group by ref.accountUuid", Tuple.class)
                        .param("accountUuids", projectAccountUuids)
                        .param("resourceType", L2NetworkVO.class.getSimpleName())
                        .param("type", VxlanNetworkFactory.type.toString())
                        .limit(1000)
                        .paginate(count, (List<Tuple> tuples) ->{
                            tuples.forEach(t -> {
                                accountResourceCount.put(t.get(0, String.class), t.get(1, Long.class));
                            });
                        });


                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VxlanNetworkQuotaConstant.VXLAN_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));

            } else {

                Long value = SQL.New("select count(l2.uuid)  from AccountResourceRefVO ref, L2NetworkVO l2 " +
                        "where ref.resourceType= :resourceType and ref.resourceUuid = l2.uuid " +
                        "and l2.type = :type and ref.accountUuid = :accountUuid", Long.class)
                        .param("accountUuid", queryObject.getAccountUuid())
                        .param("resourceType", L2NetworkVO.class.getSimpleName())
                        .param("type", VxlanNetworkFactory.type.toString())
                        .find();

                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VxlanNetworkQuotaConstant.VXLAN_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }


        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.L3NetworkQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        L3NetworkVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, L3NetworkQuotaConstant.L3_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));

            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), L3NetworkVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), L3NetworkQuotaConstant.L3_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.SecurityGroupQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        SecurityGroupVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, SecurityGroupQuotaConstant.SG_NUM);

                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));
            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), SecurityGroupVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), SecurityGroupQuotaConstant.SG_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.VipQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        VipVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, VipQuotaConstant.VIP_NUM);
                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));
            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), VipVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), VipQuotaConstant.VIP_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.EipQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        EipVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, EipQuotaConstant.EIP_NUM);
                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));
            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), EipVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), EipQuotaConstant.EIP_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }

        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.PortForwardQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        PortForwardingRuleVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, PortForwardingQuotaConstant.PF_NUM);
                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));

            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), PortForwardingRuleVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), PortForwardingQuotaConstant.PF_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }


        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.LoadBalancerQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        LoadBalancerVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, LoadBalanceQuotaConstant.LOAD_BALANCER_NUM);
                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));

            } else {

                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), LoadBalancerVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), LoadBalanceQuotaConstant.LOAD_BALANCER_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }
        } else if (queryObject.getMetricName().equals(IAM2ProjectCountNamespace.LoadBalancerListenerQuotaUsedInPercent.getName())) {
            if (AccountConstant.isAdminPermission(queryObject.getAccountUuid())) {
                Map<String, String> accountResourceRef = getIAM2ProjectAccountUuid(projectUuids);
                List<String> projectAccountUuids = new ArrayList<>();
                accountResourceRef.forEach( (key, value) ->{
                    projectAccountUuids.add(key);
                });

                if (projectAccountUuids.size() == 0)
                    return new ArrayList<>();

                Map<String, Long> accountResourceCount = getAccountUseResourceNumber(projectAccountUuids,
                        LoadBalancerListenerVO.class.getSimpleName());

                Map<String, Long> accountQuota = getAccountQuotaValue(projectAccountUuids, LoadBalanceQuotaConstant.LOAD_BALANCER_LISTENER_NUM);
                return transformResourceQuotaUsageToDataPointList(getAccountResourceQuota(accountResourceRef, accountResourceCount, accountQuota, resourceLabelName));
            } else {
                Long value = getAccountUseResourceNumber(queryObject.getAccountUuid(), LoadBalancerListenerVO.class.getSimpleName());
                Long quota = getAccountQuotaValue(queryObject.getAccountUuid(), LoadBalanceQuotaConstant.LOAD_BALANCER_LISTENER_NUM);

                return transformSingleValueToDataPointList(quota == 0 ? 0 : ((double) value / quota) * 100);
            }
        }
        return null;
    }

    private Map<String, Long> getAccountUseResourceNumber (List<String> accountuuids, String resourceType) {
        long count = Q.New(AccountResourceRefVO.class).count();
        Map<String, Long> accountResourceNumber = new HashMap<>();

        SQL.New("select ref.accountUuid, count(ref.resourceUuid) " +
                "from AccountResourceRefVO ref " +
                "where ref.accountUuid in (:accountUuids) and ref.resourceType= :type " +
                "group by ref.accountUuid", Tuple.class)
                .param("accountUuids", accountuuids)
                .param("type", resourceType)
                .limit(1000)
                .paginate(count, (List<Tuple> tuples) ->{
                    tuples.forEach(t -> {
                        accountResourceNumber.put(t.get(0, String.class), t.get(1, Long.class));
                    });
                });

        return accountResourceNumber;
    }

    private Long getAccountUseResourceNumber(String accountUuid, String name) {
        Long value = SQL.New("select count(ref.resourceUuid) from AccountResourceRefVO ref where ref.accountUuid=:accountUuid and ref.resourceType= :type", Long.class)
                .param("accountUuid", accountUuid)
                .param("type", name)
                .find();

        return value;
    }



    private Map<String, Long> getAccountQuotaValue(List<String> accountuuids, String name) {
        List<Tuple> tupleQuotas = Q.New(QuotaVO.class)
                .select(QuotaVO_.identityUuid, QuotaVO_.value)
                .in(QuotaVO_.identityUuid, accountuuids)
                .eq(QuotaVO_.name, name)
                .listTuple();

        Map<String, Long> accountQuota = new HashMap<>();
        for (Tuple t : tupleQuotas) {
            accountQuota.put(t.get(0, String.class), t.get(1, Long.class));
        }

        return accountQuota;
    }

    private Long getAccountQuotaValue(String accountUuid, String name) {
        return Q.New(QuotaVO.class).select(QuotaVO_.value)
                .eq(QuotaVO_.identityUuid, accountUuid)
                .eq(QuotaVO_.name, name)
                .findValue();
    }

    private Map<String, String> getIAM2ProjectAccountUuid(List<String> projectUuids) {
        long count = Q.New(IAM2ProjectAccountRefVO.class).count();
        Map<String, String> accountResourceRef = new HashMap<>();

        String sql = "SELECT ref.accountUuid, ref.projectUuid FROM IAM2ProjectAccountRefVO ref, IAM2ProjectVO iam2 where ref.projectUuid = iam2.uuid and iam2.state = :state";
        if (projectUuids != null && projectUuids.size() > 0) {
            sql += " and ref.projectUuid in (:projectUuids)";

            SQL.New(sql, Tuple.class).param("state", ProjectState.Enabled).param("projectUuids", projectUuids).limit(1000).paginate(count, (List<Tuple> tuples) ->{

                tuples.forEach(t -> {
                    accountResourceRef.put(t.get(0, String.class), t.get(1, String.class));
                });

            });

            return accountResourceRef;
        }

        SQL.New(sql, Tuple.class).param("state", ProjectState.Enabled).limit(1000).paginate(count, (List<Tuple> tuples) ->{

            tuples.forEach(t -> {
                accountResourceRef.put(t.get(0, String.class), t.get(1, String.class));
            });

        });

        return accountResourceRef;
    }



    private List<AccountResourceQuotaData> getAccountResourceQuota (Map<String, String> accountResourceRef,
                                                                    Map<String, Long> accountResourceNumber,
                                                                    Map<String, Long> accountQuota,
                                                                    String resourceName) {
        List<AccountResourceQuotaData> list =  new ArrayList<>();
        accountResourceRef.forEach((key, value) -> {
            AccountResourceQuotaData resourceQuota = new AccountResourceQuotaData();
            resourceQuota.setAccountUuid(key);
            resourceQuota.setResourceUuid(value);
            resourceQuota.setResporceName(resourceName);
            resourceQuota.setUseNumber(accountResourceNumber.get(key) == null ? 0L : accountResourceNumber.get(key));
            resourceQuota.setQuota(accountQuota.get(key) == null ? 0L : accountQuota.get(key));

            list.add(resourceQuota);

        });

        return list;
    }
}
