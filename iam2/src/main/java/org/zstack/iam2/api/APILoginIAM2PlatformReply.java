package org.zstack.iam2.api;

import org.zstack.header.identity.APILogInReply;
import org.zstack.header.identity.SessionInventory;
import org.zstack.header.rest.RestResponse;

@RestResponse(allTo = "inventory")
public class APILoginIAM2PlatformReply extends APILogInReply {
    private SessionInventory inventory;

    public SessionInventory getInventory() {
        return inventory;
    }

    public void setInventory(SessionInventory inventory) {
        this.inventory = inventory;
    }

    public static APILoginIAM2PlatformReply __example__() {
        APILoginIAM2PlatformReply ret = new APILoginIAM2PlatformReply();
        ret.setInventory(new SessionInventory());
        ret.getInventory().setUuid(uuid());
        return ret;
    }
}
