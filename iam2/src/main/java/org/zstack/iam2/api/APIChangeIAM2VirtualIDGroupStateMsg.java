package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.core.Platform;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDGroupVO;
import org.zstack.iam2.entity.StateEvent;

@RestRequest(path = "/iam2/projects/groups/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIChangeIAM2VirtualIDGroupStateEvent.class)
public class APIChangeIAM2VirtualIDGroupStateMsg extends APIMessage implements GroupMessage {
    @APIParam(resourceType = IAM2VirtualIDGroupVO.class)
    private String uuid;
    @APIParam(validValues = {"enable", "disable"})
    private StateEvent stateEvent;

    public static APIChangeIAM2VirtualIDGroupStateMsg __example__() {
        APIChangeIAM2VirtualIDGroupStateMsg ret = new APIChangeIAM2VirtualIDGroupStateMsg();
        ret.uuid = Platform.getUuid();
        ret.stateEvent = StateEvent.enable;
        return ret;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public StateEvent getStateEvent() {
        return stateEvent;
    }

    public void setStateEvent(StateEvent stateEvent) {
        this.stateEvent = stateEvent;
    }

    @Override
    public String getGroupUuid() {
        return uuid;
    }
}
