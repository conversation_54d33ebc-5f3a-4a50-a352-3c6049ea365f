package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.role.RoleVO;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.iam2.entity.IAM2VirtualIDVO;

import java.util.List;

import static java.util.Arrays.asList;

@RestRequest(path = "/iam2/projects/virtual-ids/{virtualIDUuid}/roles", method = HttpMethod.POST,
        responseClass = APIAddRolesToIAM2VirtualIDEvent.class, parameterName = "params")
public class APIAddRolesToIAM2VirtualIDMsg extends APIMessage implements VirtualIDMessage {
    @APIParam(resourceType = IAM2VirtualIDVO.class)
    private String virtualIDUuid;
    @APIParam(resourceType = RoleVO.class, nonempty = true)
    private List<String> roleUuids;
    @APIParam(resourceType = IAM2ProjectVO.class, required = false)
    private String projectUuid;

    public static APIAddRolesToIAM2VirtualIDMsg __example__() {
        APIAddRolesToIAM2VirtualIDMsg ret = new APIAddRolesToIAM2VirtualIDMsg();
        ret.virtualIDUuid = uuid();
        ret.roleUuids = asList(uuid(), uuid());
        return ret;
    }

    @Override
    public String getVirtualIDUuid() {
        return virtualIDUuid;
    }

    public void setVirtualIDUuid(String virtualIDUuid) {
        this.virtualIDUuid = virtualIDUuid;
    }

    public List<String> getRoleUuids() {
        return roleUuids;
    }

    public void setRoleUuids(List<String> roleUuids) {
        this.roleUuids = roleUuids;
    }

    public String getProjectUuid() {
        return projectUuid;
    }

    public void setProjectUuid(String projectUuid) {
        this.projectUuid = projectUuid;
    }
}
