package org.zstack.iam2.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2VirtualIDInventory;

@RestResponse(allTo = "inventory")
public class APIChangeIAM2VirtualIDTypeEvent extends APIEvent {
    private IAM2VirtualIDInventory inventory;

    public static APIChangeIAM2VirtualIDTypeEvent __example__() {
        APIChangeIAM2VirtualIDTypeEvent ret = new APIChangeIAM2VirtualIDTypeEvent();
        ret.inventory = IAM2VirtualIDInventory.__example__();
        return ret;
    }

    public APIChangeIAM2VirtualIDTypeEvent() {
    }

    public APIChangeIAM2VirtualIDTypeEvent(String apiId) {
        super(apiId);
    }

    public IAM2VirtualIDInventory getInventory() {
        return inventory;
    }

    public void setInventory(IAM2VirtualIDInventory inventory) {
        this.inventory = inventory;
    }
}
