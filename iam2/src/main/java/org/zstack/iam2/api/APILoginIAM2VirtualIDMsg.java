package org.zstack.iam2.api;

import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpMethod;
import org.zstack.header.identity.APILogInReply;
import org.zstack.header.core.encrypt.EncryptionParamAllowed;
import org.zstack.header.identity.APISessionMessage;
import org.zstack.header.identity.SessionVO;
import org.zstack.header.identity.SuppressCredentialCheck;
import org.zstack.header.identity.login.APICaptchaMessage;
import org.zstack.header.log.NoLogging;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.APIReply;
import org.zstack.header.other.APILoginAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.IAM2Constant;

import java.io.Serializable;
import java.util.Map;

@SuppressCredentialCheck
@RestRequest(path = "/iam2/virtual-ids/login",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APILoginIAM2VirtualIDReply.class)
@EncryptionParamAllowed(actions = { EncryptionParamAllowed.ACTION_PUT_USER_INFO_INTO_SYSTEM_TAG })
public class APILoginIAM2VirtualIDMsg extends APISessionMessage implements APILoginAuditor, APICaptchaMessage, Serializable {
    @APIParam
    private String name;
    @APIParam(password = true)
    @NoLogging
    private String password;
    @APIParam(required = false)
    private String captchaUuid;
    @APIParam(required = false)
    private String verifyCode;
    @APIParam(required = false)
    private Map<String, String> clientInfo;

    public static APILoginIAM2VirtualIDMsg __example__() {
        APILoginIAM2VirtualIDMsg ret = new APILoginIAM2VirtualIDMsg();
        ret.name = "id1";
        ret.password = "password";
        return ret;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCaptchaUuid() {
        return captchaUuid;
    }

    public void setCaptchaUuid(String captchaUuid) {
        this.captchaUuid = captchaUuid;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public Map<String, String> getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(Map<String, String> clientInfo) {
        this.clientInfo = clientInfo;
    }

    @Override
    public String getOperator() {
        return name;
    }

    @Override
    public LoginResult loginAudit(APIMessage msg, APIReply reply) {
        String clientIp = "";
        String clientBrowser = "";
        APILoginIAM2VirtualIDMsg amsg = (APILoginIAM2VirtualIDMsg) msg;
        Map<String, String> clientInfo = amsg.getClientInfo();
        if (clientInfo != null && !clientInfo.isEmpty()) {
            clientIp = StringUtils.isNotEmpty(clientInfo.get("clientIp")) ? clientInfo.get("clientIp") : "";
            clientBrowser = StringUtils.isNotEmpty(clientInfo.get("clientBrowser")) ? clientInfo.get("clientBrowser") : "";
        } else {
            clientIp = StringUtils.isNotBlank(msg.getClientIp()) ? msg.getClientIp() : "";
            clientBrowser = StringUtils.isNotBlank(msg.getClientBrowser()) ? msg.getClientBrowser() : "";
        }
        String resourceUuid = reply.isSuccess() ? ((APILogInReply) reply).getInventory().getUuid() : "";
        return new LoginResult(clientIp, clientBrowser, resourceUuid, SessionVO.class);
    }

    @Override
    public String getUsername() {
        return name;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getLoginType() {
        return IAM2Constant.LOGIN_TYPE;
    }
}
