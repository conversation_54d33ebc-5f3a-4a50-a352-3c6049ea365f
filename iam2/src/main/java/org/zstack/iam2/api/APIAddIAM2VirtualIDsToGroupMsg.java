package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDGroupVO;
import org.zstack.iam2.entity.IAM2VirtualIDVO;

import java.util.List;

import static java.util.Arrays.asList;

@RestRequest(path = "/iam2/projects/groups/{groupUuid}/virtual-ids", method = HttpMethod.POST,
        responseClass = APIAddIAM2VirtualIDToGroupEvent.class, parameterName = "params")
public class APIAddIAM2VirtualIDsToGroupMsg extends APIMessage implements GroupMessage {
    @APIParam(resourceType = IAM2VirtualIDVO.class, nonempty = true)
    private List<String> virtualIDUuids;
    @APIParam(resourceType = IAM2VirtualIDGroupVO.class)
    private String groupUuid;

    public static APIAddIAM2VirtualIDsToGroupMsg __example__() {
        APIAddIAM2VirtualIDsToGroupMsg ret = new APIAddIAM2VirtualIDsToGroupMsg();
        ret.virtualIDUuids = asList(uuid(), uuid());
        ret.groupUuid = uuid();
        return ret;
    }

    public List<String> getVirtualIDUuids() {
        return virtualIDUuids;
    }

    public void setVirtualIDUuids(List<String> virtualIDUuids) {
        this.virtualIDUuids = virtualIDUuids;
    }

    public String getGroupUuid() {
        return groupUuid;
    }

    public void setGroupUuid(String groupUuid) {
        this.groupUuid = groupUuid;
    }
}
