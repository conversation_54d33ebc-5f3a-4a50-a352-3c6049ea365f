package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.NoDoc;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectTemplateVO;
import org.zstack.iam2.entity.ProjectTemplateMessage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.zstack.utils.CollectionDSL.list;

@RestRequest(path = "/iam2/projects/templates/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIUpdateIAM2ProjectTemplateEvent.class)
@NoDoc
public class APIUpdateIAM2ProjectTemplateMsg extends APIMessage implements ProjectTemplateMessage {
    @APIParam(resourceType = IAM2ProjectTemplateVO.class)
    private String uuid;
    @APIParam(maxLength = 255, required = false)
    private String name;
    @APIParam(maxLength = 2048, required = false)
    private String description;
    private List<Attribute> attributes;
    private Map<String, Long> quota;

    public static APIUpdateIAM2ProjectTemplateMsg __example__() {
        APIUpdateIAM2ProjectTemplateMsg ret = new APIUpdateIAM2ProjectTemplateMsg();
        ret.name = "new-name";
        ret.attributes = list(new Attribute("name", "value"));
        ret.quota = new HashMap<>();
        ret.quota.put("vm.num", 10L);

        return ret;
    }

    @Override
    public String getProjectTemplateUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }

    public Map<String, Long> getQuota() {
        return quota;
    }

    public void setQuota(Map<String, Long> quota) {
        this.quota = quota;
    }
}
