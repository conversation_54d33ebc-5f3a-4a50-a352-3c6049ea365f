package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.NoDoc;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectVO;

@RestRequest(path = "/iam2/projects/{uuid}/actions", method = HttpMethod.PUT,
        responseClass = APIExpungeIAM2ProjectEvent.class, isAction = true)
@NoDoc
public class APIExpungeIAM2ProjectMsg extends APIMessage implements ProjectMessage {
    @APIParam(resourceType = IAM2ProjectVO.class, successIfResourceNotExisting = true)
    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public String getProjectUuid() {
        return uuid;
    }
}
