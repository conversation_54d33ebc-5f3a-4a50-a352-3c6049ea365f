package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationVO;
import org.zstack.iam2.entity.OrganizationType;

@RestRequest(path = "/iam2/organizations/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIUpdateIAM2OrganizationEvent.class)
public class APIUpdateIAM2OrganizationMsg extends APIMessage implements OrganizationMessage {
    @APIParam(resourceType = IAM2OrganizationVO.class)
    private String uuid;
    @APIParam(maxLength = 255, required = false)
    private String name;
    @APIParam(maxLength = 2048, required = false)
    private String description;
    @APIParam(resourceType = IAM2OrganizationVO.class, required = false)
    private String parentUuid;
    @APIParam(validValues = {"Company", "Department"}, required = false)
    private OrganizationType type;

    public static APIUpdateIAM2OrganizationMsg __example__() {
        APIUpdateIAM2OrganizationMsg ret = new APIUpdateIAM2OrganizationMsg();
        ret.uuid = uuid();
        ret.name = "new-name";
        ret.description = "new-description";
        ret.parentUuid = uuid();
        return ret;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParentUuid() {
        return parentUuid;
    }

    public void setParentUuid(String parentUuid) {
        this.parentUuid = parentUuid;
    }

    public OrganizationType getType() {
        return type;
    }

    public void setType(OrganizationType type) {
        this.type = type;
    }

    @Override
    public String getOrganizationUuid() {
        return uuid;
    }
}
