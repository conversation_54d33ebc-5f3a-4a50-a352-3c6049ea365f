package org.zstack.iam2.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2VirtualIDInventory;

@RestResponse(allTo = "inventory")
public class APICreateIAM2VirtualIDEvent extends APIEvent {
    private IAM2VirtualIDInventory inventory;

    public static APICreateIAM2VirtualIDEvent __example__() {
        APICreateIAM2VirtualIDEvent ret = new APICreateIAM2VirtualIDEvent();
        ret.inventory = IAM2VirtualIDInventory.__example__();
        return ret;
    }

    public APICreateIAM2VirtualIDEvent() {
    }

    public APICreateIAM2VirtualIDEvent(String apiId) {
        super(apiId);
    }

    public IAM2VirtualIDInventory getInventory() {
        return inventory;
    }

    public void setInventory(IAM2VirtualIDInventory inventory) {
        this.inventory = inventory;
    }
}
