package org.zstack.iam2.api;

import org.zstack.header.identity.PolicyStatement;
import org.zstack.header.identity.PolicyStatementEffect;
import org.zstack.header.identity.role.RolePolicyStatementInventory;
import org.zstack.header.identity.role.RoleState;
import org.zstack.header.identity.role.RoleType;
import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2ProjectRoleInventory;
import org.zstack.iam2.entity.IAM2ProjectRoleType;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

import static java.util.Arrays.asList;

@RestResponse(allTo = "inventories")
public class APIQueryIAM2ProjectRoleReply extends APIQueryReply {
    private List<IAM2ProjectRoleInventory> inventories;

    public List<IAM2ProjectRoleInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<IAM2ProjectRoleInventory> inventories) {
        this.inventories = inventories;
    }
    
    public static APIQueryIAM2ProjectRoleReply __example__() {
        APIQueryIAM2ProjectRoleReply reply = new APIQueryIAM2ProjectRoleReply();

        IAM2ProjectRoleInventory role = new IAM2ProjectRoleInventory();
        role.setName("role-1");
        RolePolicyStatementInventory inv = new RolePolicyStatementInventory();
        PolicyStatement statement = new PolicyStatement();
        statement.setEffect(PolicyStatementEffect.Allow);
        statement.setActions(asList("org.zstack.header.vm.APICreateVmInstanceMsg"));
        statement.setName("statement for test");

        inv.setUuid(uuid());
        inv.setStatement(statement);
        inv.setCreateDate(new Timestamp(System.currentTimeMillis()));
        inv.setLastOpDate(new Timestamp(System.currentTimeMillis()));
        inv.setRoleUuid(uuid());

        role.setStatements(asList(inv));
        role.setDescription("role for test");
        role.setUuid(uuid());
        role.setState(RoleState.Enabled);
        role.setType(RoleType.Customized);
        role.setIam2ProjectRoleType(IAM2ProjectRoleType.CreateInProject.toString());

        reply.setInventories(Collections.singletonList(role));

        return reply;
    }
}
