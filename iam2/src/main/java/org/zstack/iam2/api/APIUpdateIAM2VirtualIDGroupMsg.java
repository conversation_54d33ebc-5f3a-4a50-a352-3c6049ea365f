package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDGroupVO;

@RestRequest(path = "/iam2/projects/groups/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIUpdateIAM2VirtualIDGroupEvent.class)
public class APIUpdateIAM2VirtualIDGroupMsg extends APIMessage implements GroupMessage {
    @APIParam(resourceType = IAM2VirtualIDGroupVO.class)
    private String uuid;
    @APIParam(maxLength = 255, required = false)
    private String name;
    @APIParam(maxLength = 2048, required = false)
    private String description;

    public static APIUpdateIAM2VirtualIDGroupMsg __example__() {
        APIUpdateIAM2VirtualIDGroupMsg ret = new APIUpdateIAM2VirtualIDGroupMsg();
        ret.uuid = uuid();
        ret.name = "new-name";
        return ret;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getGroupUuid() {
        return uuid;
    }
}
