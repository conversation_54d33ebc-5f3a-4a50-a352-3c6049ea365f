package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.APIGetAccountQuotaUsageReply;
import org.zstack.header.identity.AccountMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationVO;

@RestRequest(
        path = "/iam2/organizations/quota/{uuid}/usages",
        method = HttpMethod.GET,
        responseClass = APIGetOrganizationQuotaUsageReply.class
)
public class APIGetOrganizationQuotaUsageMsg extends APISyncCallMessage implements OrganizationMessage {
    @APIParam(resourceType = IAM2OrganizationVO.class)
    private String uuid;
    @Override
    public String getOrganizationUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public static APIGetOrganizationQuotaUsageMsg __example__() {
        APIGetOrganizationQuotaUsageMsg ret = new APIGetOrganizationQuotaUsageMsg();
        ret.setUuid(uuid());

        return ret;
    }
}
