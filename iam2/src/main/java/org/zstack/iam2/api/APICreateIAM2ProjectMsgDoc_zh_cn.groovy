package org.zstack.iam2.api

import org.zstack.iam2.api.APICreateIAM2ProjectEvent

doc {
    title "CreateIAM2Project"

    category "iam2"

    desc """创建IAM2项目"""

    rest {
        request {
			url "POST /v1/iam2/projects"

			header (Authorization: 'OAuth the-session-uuid')

            clz APICreateIAM2ProjectMsg.class

            desc """"""
            
			params {

				column {
					name "name"
					enclosedIn "params"
					desc "资源名称"
					location "body"
					type "String"
					optional false
					since "2.4.0"
				}
				column {
					name "description"
					enclosedIn "params"
					desc "资源的详细描述"
					location "body"
					type "String"
					optional true
					since "2.4.0"
				}
				column {
					name "attributes"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "2.4.0"
				}
				column {
					name "resourceUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "2.4.0"
				}
				column {
					name "systemTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "2.4.0"
				}
				column {
					name "userTags"
					enclosedIn ""
					desc ""
					location "body"
					type "List"
					optional true
					since "2.4.0"
				}
				column {
					name "quota"
					enclosedIn "params"
					desc ""
					location "body"
					type "Map"
					optional true
					since "2.4.0"
				}
				column {
					name "roleUuids"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "2.4.0"
				}
				column {
					name "resourceTemplates"
					enclosedIn "params"
					desc ""
					location "body"
					type "List"
					optional true
					since "0.6"
				}
				column {
					name "organizationUuid"
					enclosedIn "params"
					desc ""
					location "body"
					type "String"
					optional true
					since "0.6"
				}
				column {
					name "tagUuids"
					enclosedIn "params"
					desc "标签UUID列表"
					location "body"
					type "List"
					optional true
					since "3.4.0"
				}
			}
        }

        response {
            clz APICreateIAM2ProjectEvent.class
        }
    }
}