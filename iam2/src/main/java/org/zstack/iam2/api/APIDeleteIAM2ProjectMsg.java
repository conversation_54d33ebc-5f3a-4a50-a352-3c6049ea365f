package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectVO;

@RestRequest(path = "/iam2/projects/{uuid}", method = HttpMethod.DELETE,
        responseClass = APIDeleteIAM2ProjectEvent.class)
public class APIDeleteIAM2ProjectMsg extends APIMessage implements ProjectMessage  {
    @APIParam(resourceType = IAM2ProjectVO.class, successIfResourceNotExisting = true)
    private String uuid;

    public static APIDeleteIAM2ProjectMsg __example__() {
        APIDeleteIAM2ProjectMsg ret = new APIDeleteIAM2ProjectMsg();
        ret.uuid = uuid();
        return ret;
    }

    @Override
    public String getProjectUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
