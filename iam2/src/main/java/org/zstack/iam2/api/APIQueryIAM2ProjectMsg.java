package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectInventory;

import java.util.List;

import static java.util.Arrays.asList;

@AutoQuery(replyClass = APIQueryIAM2ProjectReply.class, inventoryClass = IAM2ProjectInventory.class)
@RestRequest(path = "/iam2/projects",
        optionalPaths = {"/iam2/projects/{uuid}"},
        method = HttpMethod.GET, responseClass = APIQueryIAM2ProjectReply.class)
public class APIQueryIAM2ProjectMsg extends APIQueryMessage {
    public static List<String> __example__() {
        return asList("name=test");
    }
}
