package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationInventory;

import java.util.List;

import static java.util.Arrays.asList;

@AutoQuery(replyClass = APIQueryIAM2OrganizationReply.class, inventoryClass = IAM2OrganizationInventory.class)
@RestRequest(path = "/iam2/organizations",
        optionalPaths = {"/iam2/organizations/{uuid}"},
        method = HttpMethod.GET, responseClass = APIQueryIAM2OrganizationReply.class)
public class APIQueryIAM2OrganizationMsg extends APIQueryMessage {
    public static List<String> __example__() {
        return asList("name=test");
    }
}
