package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDVO;

@RestRequest(path = "/iam2/virtual-ids/{uuid}", method = HttpMethod.DELETE,
        responseClass = APIDeleteIAM2VirtualIDEvent.class)
public class APIDeleteIAM2VirtualIDMsg extends APIDeleteMessage implements VirtualIDMessage {
    @APIParam(resourceType = IAM2VirtualIDVO.class, successIfResourceNotExisting = true)
    private String uuid;

    public static APIDeleteIAM2VirtualIDMsg __example__() {
        APIDeleteIAM2VirtualIDMsg ret = new APIDeleteIAM2VirtualIDMsg();
        ret.uuid = uuid();
        return ret;
    }

    @Override
    public String getVirtualIDUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
