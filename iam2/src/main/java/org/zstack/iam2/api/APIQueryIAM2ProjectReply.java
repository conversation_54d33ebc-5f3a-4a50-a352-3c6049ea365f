package org.zstack.iam2.api;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2ProjectInventory;

import java.util.List;

import static java.util.Arrays.asList;

@RestResponse(allTo = "inventories")
public class APIQueryIAM2ProjectReply extends APIQueryReply {
    private List<IAM2ProjectInventory> inventories;

    public static APIQueryIAM2ProjectReply __example__() {
        APIQueryIAM2ProjectReply ret = new APIQueryIAM2ProjectReply();
        ret.inventories = asList(IAM2ProjectInventory.__example__());
        return ret;
    }

    public List<IAM2ProjectInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<IAM2ProjectInventory> inventories) {
        this.inventories = inventories;
    }
}
