package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationProjectRefInventory;
import org.zstack.iam2.entity.IAM2ProjectAccountRefInventory;
import java.util.List;

import static java.util.Arrays.asList;

@AutoQuery(replyClass = APIQueryIAM2ProjectAccountRefReply.class, inventoryClass = IAM2ProjectAccountRefInventory.class)
@RestRequest(
        path = "/iam2/projects/account/refs",
        method = HttpMethod.GET,
        responseClass = APIQueryIAM2ProjectAccountRefReply.class
)
public class APIQueryIAM2ProjectAccountRefMsg extends APIQueryMessage {

    public static List<String> __example__() {
        return asList(String.format("projectUuid=%s", uuid()));
    }

}
