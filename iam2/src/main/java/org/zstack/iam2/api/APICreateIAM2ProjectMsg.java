package org.zstack.iam2.api;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import org.springframework.http.HttpMethod;
import org.zstack.header.identity.role.RoleVO;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.iam2.entity.IAM2OrganizationVO;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.iam2.project.template.SecurityGroupTemplate;
import org.zstack.network.securitygroup.SecurityGroupVO;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static java.util.Arrays.asList;

@TagResourceType(IAM2ProjectVO.class)
@RestRequest(path = "/iam2/projects", method = HttpMethod.POST,
        responseClass = APICreateIAM2ProjectEvent.class, parameterName = "params")
public class APICreateIAM2ProjectMsg extends APICreateMessage implements APIAuditor {
    @APIParam(maxLength = 255)
    private String name;
    @APIParam(maxLength = 2048, required = false)
    private String description;
    private List<Attribute> attributes;
    private Map<String, Long> quota;
    @APIParam(resourceType = RoleVO.class, required = false)
    private List<String> roleUuids;
    @APIParam(required = false)
    private List<String> resourceTemplates;
    @APIParam(resourceType = IAM2OrganizationVO.class, required = false)
    private String organizationUuid;

    public static APICreateIAM2ProjectMsg __example__() {
        APICreateIAM2ProjectMsg ret = new APICreateIAM2ProjectMsg();
        ret.name = "project";
        ret.attributes = asList(Attribute.__example__());
        SecurityGroupTemplate template = new SecurityGroupTemplate();
        template.setName("sgName");
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        JsonElement jsonElement = gson.toJsonTree(template);
        jsonElement.getAsJsonObject().addProperty("resourceType", SecurityGroupVO.class.getSimpleName());
        ret.resourceTemplates = Arrays.asList(gson.toJson(jsonElement));
        ret.setOrganizationUuid(uuid());
        return ret;
    }

    public String getOrganizationUuid() {
        return organizationUuid;
    }

    public void setOrganizationUuid(String organizationUuid) {
        this.organizationUuid = organizationUuid;
    }

    public List<String> getRoleUuids() {
        return roleUuids;
    }

    public void setRoleUuids(List<String> roleUuids) {
        this.roleUuids = roleUuids;
    }

    public Map<String, Long> getQuota() {
        return quota;
    }

    public void setQuota(Map<String, Long> quota) {
        this.quota = quota;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getResourceTemplates() {
        return resourceTemplates;
    }

    public void setResourceTemplates(List<String> resourceTemplates) {
        this.resourceTemplates = resourceTemplates;
    }

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(
                rsp.isSuccess() ? ((APICreateIAM2ProjectEvent) rsp).getInventory().getUuid() : "",
                IAM2ProjectVO.class
        );
    }
}
