package org.zstack.iam2.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2ProjectInventory;

@RestResponse(allTo = "inventory")
public class APIChangeIAM2ProjectStateEvent extends APIEvent {
    private IAM2ProjectInventory inventory;

    public static APIChangeIAM2ProjectStateEvent __example__() {
        APIChangeIAM2ProjectStateEvent ret = new APIChangeIAM2ProjectStateEvent();
        ret.inventory = IAM2ProjectInventory.__example__();
        return ret;
    }

    public APIChangeIAM2ProjectStateEvent() {
    }

    public APIChangeIAM2ProjectStateEvent(String apiId) {
        super(apiId);
    }

    public IAM2ProjectInventory getInventory() {
        return inventory;
    }

    public void setInventory(IAM2ProjectInventory inventory) {
        this.inventory = inventory;
    }
}
