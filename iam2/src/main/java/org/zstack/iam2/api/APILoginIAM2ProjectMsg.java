package org.zstack.iam2.api;

import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpMethod;
import org.zstack.header.identity.APILogInReply;
import org.zstack.header.identity.APISessionMessage;
import org.zstack.header.identity.SessionVO;
import org.zstack.header.identity.rbac.SuppressRBACCheck;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.APIReply;
import org.zstack.header.other.APILoginAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.IAM2Constant;

import java.util.Map;

@RestRequest(path = "/iam2/projects/login",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APILoginIAM2ProjectReply.class)
@SuppressRBACCheck
public class APILoginIAM2ProjectMsg extends APISessionMessage implements APILoginAuditor {
    @APIParam
    private String projectName;
    @APIParam(required = false)
    private Map<String, String> clientInfo;

    public static APILoginIAM2ProjectMsg __example__() {
        APILoginIAM2ProjectMsg ret = new APILoginIAM2ProjectMsg();
        ret.projectName = "project";
        return ret;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Map<String, String> getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(Map<String, String> clientInfo) {
        this.clientInfo = clientInfo;
    }

    @Override
    public LoginResult loginAudit(APIMessage msg, APIReply reply) {
        String clientIp = "";
        String clientBrowser = "";
        APILoginIAM2ProjectMsg amsg = (APILoginIAM2ProjectMsg) msg;
        Map<String, String> clientInfo = amsg.getClientInfo();
        if (clientInfo != null && !clientInfo.isEmpty()) {
            clientIp = StringUtils.isNotEmpty(clientInfo.get("clientIp")) ? clientInfo.get("clientIp") : "";
            clientBrowser = StringUtils.isNotEmpty(clientInfo.get("clientBrowser")) ? clientInfo.get("clientBrowser") : "";
        } else {
            clientIp = StringUtils.isNotBlank(msg.getClientIp()) ? msg.getClientIp() : "";
            clientBrowser = StringUtils.isNotBlank(msg.getClientBrowser()) ? msg.getClientBrowser() : "";
        }
        String resourceUuid = reply.isSuccess() ? ((APILogInReply) reply).getInventory().getUuid() : "";
        return new LoginResult(clientIp, clientBrowser, resourceUuid, SessionVO.class);
    }

    @Override
    public String getUsername() {
        return projectName;
    }

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getLoginType() {
        return IAM2Constant.PROJECT_LOGIN_TYPE;
    }
}
