package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.core.Platform;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDVO;

@RestRequest(path = "/iam2/virtual-ids/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIChangeIAM2VirtualIDTypeEvent.class)
public class APIChangeIAM2VirtualIDTypeMsg extends APIMessage implements VirtualIDMessage {
    @APIParam(resourceType = IAM2VirtualIDVO.class)
    private String uuid;
    @APIParam(validValues = {"ZStack"})
    private String type;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String getVirtualIDUuid() {
        return uuid;
    }

    public static APIChangeIAM2VirtualIDTypeMsg __example__() {
        APIChangeIAM2VirtualIDTypeMsg ret = new APIChangeIAM2VirtualIDTypeMsg();
        ret.uuid = Platform.getUuid();
        ret.type = "ZStack";
        return ret;
    }
}
