package org.zstack.iam2.api;

public class ErrorResult {
    private int line;
    private String detail;

    public ErrorResult() {
    }

    public ErrorResult(int line, String detail) {
        this.line = line;
        this.detail = detail;
    }

    public static ErrorResult __example__() {
        ErrorResult ret = new ErrorResult();
        ret.line = 123;
        ret.detail = "用户名重复";
        return ret;
    }

    public int getLine() {
        return line;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
}
