package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.query.APIQueryMessage;
import org.zstack.header.query.AutoQuery;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.storage.backup.APIQueryBackupStorageReply;
import org.zstack.iam2.entity.IAM2ProjectRoleInventory;

import java.util.Collections;
import java.util.List;

@AutoQuery(replyClass = APIQueryIAM2ProjectRoleReply.class, inventoryClass = IAM2ProjectRoleInventory.class)
@RestRequest(
        path = "/iam2/project-roles",
        optionalPaths = {"/iam2/project-roles/{uuid}"},
        method = HttpMethod.GET,
        responseClass = APIQueryIAM2ProjectRoleReply.class
)
public class APIQueryIAM2ProjectRoleMsg extends APIQueryMessage {

    public static List<String> __example__() {
        return Collections.singletonList("uuid=" + uuid());
    }

}
