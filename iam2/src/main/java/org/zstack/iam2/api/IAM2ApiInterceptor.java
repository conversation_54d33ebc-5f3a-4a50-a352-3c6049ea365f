package org.zstack.iam2.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.config.GlobalConfigVO;
import org.zstack.core.config.GlobalConfigVO_;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SQLBatch;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.identity.*;
import org.zstack.header.message.APIMessage;
import org.zstack.iam2.IAM2Constant;
import org.zstack.iam2.IAM2Manager;
import org.zstack.iam2.IAM2ProjectBase;
import org.zstack.iam2.attribute.project.Retire;
import org.zstack.iam2.attribute.project.RetirePolicy;
import org.zstack.iam2.entity.*;
import org.zstack.iam2.rbac.IAM2RolePolicyStatementHelper;
import org.zstack.identity.AccountManager;
import org.zstack.identity.QuotaUtil;
import org.zstack.resourceconfig.ResourceConfigFacade;
import org.zstack.utils.gson.JSONObjectUtil;

import javax.persistence.Query;
import javax.persistence.Tuple;
import java.util.*;
import java.util.stream.Collectors;
import static org.zstack.core.Platform.*;
import static org.zstack.core.Platform.argerr;

public class IAM2ApiInterceptor implements ApiMessageInterceptor {
    @Autowired
    private AccountManager acntMgr;
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private ResourceConfigFacade rcf;

    private void validateAttributes(List<Attribute> attrs) {
        attrs.forEach(attr -> {
            if (attr.getName() == null) {
                throw new ApiMessageInterceptionException(argerr("attribute name cannot be null, value[%s]", attr.getValue()));
            }

            if (attr.getName().length() > 2048) {
                throw new ApiMessageInterceptionException(argerr("attribute name[%s] exceed the max length of 2048 chars", attr.getName()));
            }
            if (attr.getValue() != null && attr.getValue().length() > 2048) {
                throw new ApiMessageInterceptionException(argerr("attribute[name:%s] value[%s] exceed the max length of 2048 chars", attr.getName(), attr.getValue()));
            }
        });
    }

    private void setServiceId(APIMessage msg) {
        if (msg instanceof VirtualIDMessage) {
            VirtualIDMessage vmsg = (VirtualIDMessage) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, IAM2Manager.SERVICE_ID, vmsg.getVirtualIDUuid());
        } else if (msg instanceof ProjectMessage) {
            ProjectMessage pmsg = (ProjectMessage) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, IAM2Manager.SERVICE_ID, pmsg.getProjectUuid());
        } else if (msg instanceof OrganizationMessage) {
            OrganizationMessage omsg = (OrganizationMessage) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, IAM2Manager.SERVICE_ID, omsg.getOrganizationUuid());
        }
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIAddAttributesToIAM2ProjectMsg) {
            validate((APIAddAttributesToIAM2ProjectMsg) msg);
        } else if (msg instanceof APIAddAttributesToIAM2VirtualIDGroupMsg) {
            validate((APIAddAttributesToIAM2VirtualIDGroupMsg) msg);
        } else if (msg instanceof APIAddAttributesToIAM2VirtualIDMsg) {
            validate((APIAddAttributesToIAM2VirtualIDMsg) msg);
        } else if (msg instanceof APIAddAttributesToIAM2OrganizationMsg) {
            validate((APIAddAttributesToIAM2OrganizationMsg) msg);
        } else if (msg instanceof APIAddIAM2VirtualIDsToGroupMsg) {
            validate((APIAddIAM2VirtualIDsToGroupMsg) msg);
        } else if (msg instanceof APIAddIAM2VirtualIDsToOrganizationMsg) {
            validate((APIAddIAM2VirtualIDsToOrganizationMsg) msg);
        } else if (msg instanceof APIAddIAM2VirtualIDsToProjectMsg) {
            validate((APIAddIAM2VirtualIDsToProjectMsg) msg);
        } else if (msg instanceof APIAddRolesToIAM2VirtualIDGroupMsg) {
            validate((APIAddRolesToIAM2VirtualIDGroupMsg) msg);
        } else if (msg instanceof APIAddRolesToIAM2VirtualIDMsg) {
            validate((APIAddRolesToIAM2VirtualIDMsg) msg);
        } else if (msg instanceof APIChangeIAM2OrganizationParentMsg) {
            validate((APIChangeIAM2OrganizationParentMsg) msg);
        } else if (msg instanceof APICreateIAM2OrganizationMsg) {
            validate((APICreateIAM2OrganizationMsg) msg);
        } else if (msg instanceof APICreateIAM2ProjectMsg) {
            validate((APICreateIAM2ProjectMsg) msg);
        } else if (msg instanceof APICreateIAM2VirtualIDGroupMsg) {
            validate((APICreateIAM2VirtualIDGroupMsg) msg);
        } else if (msg instanceof APICreateIAM2VirtualIDMsg) {
            validate((APICreateIAM2VirtualIDMsg) msg);
        } else if (msg instanceof APIUpdateIAM2OrganizationMsg) {
            validate((APIUpdateIAM2OrganizationMsg) msg);
        } else if (msg instanceof APIGetIAM2VirtualIDAPIPermissionMsg) {
            validate((APIGetIAM2VirtualIDAPIPermissionMsg) msg);
        } else if (msg instanceof APIGetIAM2ProjectsOfVirtualIDMsg) {
            validate((APIGetIAM2ProjectsOfVirtualIDMsg) msg);
        } else if (msg instanceof APICreateIAM2ProjectTemplateMsg) {
            validate((APICreateIAM2ProjectTemplateMsg) msg);
        } else if (msg instanceof APILoginIAM2ProjectMsg) {
            validate((APILoginIAM2ProjectMsg) msg);
        } else if (msg instanceof APIChangeIAM2ProjectStateMsg) {
            validate((APIChangeIAM2ProjectStateMsg) msg);
        } else if (msg instanceof APIUpdateIAM2ProjectTemplateMsg) {
            validate((APIUpdateIAM2ProjectTemplateMsg) msg);
        } else if (msg instanceof APIUpdateIAM2VirtualIDAttributeMsg) {
            validate((APIUpdateIAM2VirtualIDAttributeMsg) msg);
        } else if (msg instanceof APIUpdateIAM2ProjectAttributeMsg) {
            validate((APIUpdateIAM2ProjectAttributeMsg) msg);
        } else if (msg instanceof APIUpdateIAM2OrganizationAttributeMsg) {
            validate((APIUpdateIAM2OrganizationAttributeMsg) msg);
        } else if (msg instanceof APIUpdateIAM2VirtualIDGroupAttributeMsg) {
            validate((APIUpdateIAM2VirtualIDGroupAttributeMsg) msg);
        } else if (msg instanceof APICreateIAM2ProjectFromTemplateMsg) {
            validate((APICreateIAM2ProjectFromTemplateMsg) msg);
        } else if (msg instanceof APIUpdateIAM2VirtualIDMsg) {
            validate((APIUpdateIAM2VirtualIDMsg) msg);
        } else if (msg instanceof APIUpdateIAM2ProjectMsg) {
            validate((APIUpdateIAM2ProjectMsg) msg);
        } else if (msg instanceof APISetIAM2ProjectRetirePolicyMsg) {
            validate((APISetIAM2ProjectRetirePolicyMsg) msg);
        } else if (msg instanceof APIAttachIAM2ProjectToIAM2OrganizationMsg) {
            validate((APIAttachIAM2ProjectToIAM2OrganizationMsg) msg);
        } else if (msg instanceof APIAddIAM2VirtualIDGroupToProjectsMsg) {
            validate((APIAddIAM2VirtualIDGroupToProjectsMsg) msg);
        } else if (msg instanceof APIAddIAM2VirtualIDsToProjectsMsg) {
            validate((APIAddIAM2VirtualIDsToProjectsMsg) msg);
        } else if (msg instanceof APIDeleteIAM2OrganizationMsg) {
            validate((APIDeleteIAM2OrganizationMsg) msg);
        } else if (msg instanceof APICreateIAM2ProjectTemplateFromProjectMsg) {
            validate((APICreateIAM2ProjectTemplateFromProjectMsg) msg);
        } else if (msg instanceof APISetIAM2ProjectLoginExpiredMsg) {
            validate((APISetIAM2ProjectLoginExpiredMsg) msg);
        } else if (msg instanceof APIUpdateOrganizationQuotaMsg) {
            validate((APIUpdateOrganizationQuotaMsg) msg);
        }

        setServiceId(msg);

        return msg;
    }

    private void validate(APIUpdateOrganizationQuotaMsg msg) {
        if (msg.getValue() == -1) {
            return;
        }

        QuotaVO quotaVO = Q.New(QuotaVO.class)
                .eq(QuotaVO_.identityUuid, msg.getOrganizationUuid())
                .eq(QuotaVO_.identityType, IAM2OrganizationVO.class.getSimpleName())
                .eq(QuotaVO_.name, msg.getName())
                .find();

        if (quotaVO == null) {
            return;
        }

        long projectUsed = IAM2OrganizationQuotaUtil.sumOfProjectQuotasInOrganization(quotaVO.getName(), msg.getOrganizationUuid());
        if (projectUsed > msg.getValue()) {
            throw new ApiMessageInterceptionException(argerr("The Organization[uuid: %s] used [name: %s, usedValue: %s] exceeds Request:%s.",
                    msg.getOrganizationUuid(), msg.getName(), projectUsed, msg.getValue()));
        }
    }

    private void validate(APISetIAM2ProjectLoginExpiredMsg msg) {
        if (!(msg.getLoginExpired().contains(IAM2ProjectBase.Policy.allow.toString()) || msg.getLoginExpired().contains(IAM2ProjectBase.Policy.rejection.toString()))) {
            throw new OperationFailureException(operr("%s is not a valid value. Valid values are  allow/rejection  xxx to xxx", msg.getLoginExpired()));
        }

        IAM2ProjectBase.Policy policy = msg.getLoginExpired().contains(IAM2ProjectBase.Policy.allow.toString()) ? IAM2ProjectBase.Policy.allow : IAM2ProjectBase.Policy.rejection;
        String[] crons = msg.getLoginExpired().split(policy.name())[1].split("to");
        if (crons.length != 2) {
            throw new OperationFailureException(operr("%s is not a valid value. Valid values are  allow/rejection  xxx to xxx", msg.getLoginExpired()));
        }
    }

    private void validate(APIDeleteIAM2OrganizationMsg msg) {
        if (msg.getOrganizationUuid().equals(IAM2Constant.INITIAL_ORGANIZATION_DEFAULT_UUID)) {
            throw new ApiMessageInterceptionException(argerr("The default organization[%s] cannot be deleted", msg.getUuid()));
        }
    }

    private void validate(APICreateIAM2ProjectTemplateFromProjectMsg msg) {
        boolean exist = Q.New(IAM2ProjectTemplateVO.class)
                .eq(IAM2ProjectTemplateVO_.name, msg.getName())
                .isExists();
        if (exist) {
            throw new ApiMessageInterceptionException(argerr("duplicate template name[%s]", msg.getName()));
        }
    }

    private void validate(APIAddIAM2VirtualIDsToProjectsMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                checkIfAnyVirtualIDExists(msg.getVirtualIDUuids());

                if (msg.getProjectUuids() != null) {
                    List<String> already = q(IAM2ProjectVirtualIDRefVO.class).select(IAM2ProjectVirtualIDRefVO_.virtualIDUuid).in(IAM2ProjectVirtualIDRefVO_.projectUuid, msg.getProjectUuids())
                            .in(IAM2ProjectVirtualIDRefVO_.virtualIDUuid, msg.getVirtualIDUuids()).listValues();
                    msg.getVirtualIDUuids().removeAll(already);
                }

            }
        }.execute();

        if (msg.getRoleUuids() != null && !msg.getRoleUuids().isEmpty() && msg.getRoleUuids().contains(IAM2RolePolicyStatementHelper.PROJECT_ADMIN_ROLE_UUID)) {
            throw new ApiMessageInterceptionException(argerr("illegal operation, cannot add Role[%s]", IAM2RolePolicyStatementHelper.PROJECT_ADMIN_ROLE_NAME));
        }

    }

    private void validate(APIAddIAM2VirtualIDGroupToProjectsMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                msg.getStructs().forEach(struct -> {
                    if (!struct.getRoleUuids().isEmpty()) {

                        String accountUuid =  q(IAM2ProjectAccountRefVO.class)
                                .select(IAM2ProjectAccountRefVO_.accountUuid)
                                .eq(IAM2ProjectAccountRefVO_.projectUuid, struct.getProjectUuid())
                                .findValue();
                        List<String> alreadyRoleUuid = q(IAM2VirtualIDGroupRoleRefVO.class)
                                .select(IAM2VirtualIDGroupRoleRefVO_.roleUuid)
                                .in(IAM2VirtualIDGroupRoleRefVO_.groupUuid, struct.getGroupUuids())
                                .eq(IAM2VirtualIDGroupRoleRefVO_.targetAccountUuid, accountUuid)
                                .in(IAM2VirtualIDGroupRoleRefVO_.roleUuid, struct.getRoleUuids())
                                .listValues();

                        struct.getRoleUuids().removeAll(alreadyRoleUuid);
                    }
                });
            }
        }.execute();
    }

    private void validate(APIUpdateIAM2ProjectMsg msg) {
        if (msg.getName() != null) {
            notAdminName(msg.getName());
        }
    }

    private void validate(APIUpdateIAM2VirtualIDMsg msg) {
        if (msg.getName() != null) {
            notAdminName(msg.getName());
        }
    }

    private void notAdminName(String name) {
        if (name.equalsIgnoreCase("admin")) {
            throw new ApiMessageInterceptionException(argerr("admin is a reserved name, please use another name"));
        }
    }

    private void validate(APICreateIAM2ProjectFromTemplateMsg msg) {
        notAdminName(msg.getName());

        if (Q.New(AccountVO.class).eq(AccountVO_.name, msg.getName()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("invalid name[%s], there has been a project or account with the same name", msg.getName()));
        }
    }

    private void validate(APIUpdateIAM2VirtualIDGroupAttributeMsg msg) {
        msg.setGroupUuid(Q.New(IAM2VirtualIDGroupAttributeVO.class).select(IAM2VirtualIDGroupAttributeVO_.groupUuid)
                .eq(IAM2VirtualIDGroupAttributeVO_.uuid, msg.getUuid()).findValue());
        if (msg.getGroupUuid() == null) {
            throw new ApiMessageInterceptionException(argerr("attribute[uuid:%s] is not for any group", msg.getUuid()));
        }
    }

    private void validate(APIUpdateIAM2OrganizationAttributeMsg msg) {
        msg.setOrganizationUuid(Q.New(IAM2OrganizationAttributeVO.class).select(IAM2OrganizationAttributeVO_.organizationUuid)
                .eq(IAM2OrganizationAttributeVO_.uuid, msg.getUuid()).findValue());
        if (msg.getOrganizationUuid() == null) {
            throw new ApiMessageInterceptionException(argerr("attribute[uuid:%s] is not for any organization", msg.getUuid()));
        }
    }

    private void validate(APIUpdateIAM2ProjectAttributeMsg msg) {
        msg.setProjectUuid(Q.New(IAM2ProjectAttributeVO.class).select(IAM2ProjectAttributeVO_.projectUuid)
                .eq(IAM2ProjectAttributeVO_.uuid, msg.getUuid()).findValue());
        if (msg.getProjectUuid() == null) {
            throw new ApiMessageInterceptionException(argerr("attribute[uuid:%s] is not for any project", msg.getUuid()));
        }
    }

    private void validate(APIUpdateIAM2VirtualIDAttributeMsg msg) {
        msg.setVirtualIDUuid(Q.New(IAM2VirtualIDAttributeVO.class).select(IAM2VirtualIDAttributeVO_.virtualIDUuid)
                .eq(IAM2VirtualIDAttributeVO_.uuid, msg.getUuid()).findValue());
        if (msg.getVirtualIDUuid() == null) {
            throw new ApiMessageInterceptionException(argerr("attribute[uuid:%s] is not for any virtual ID", msg.getUuid()));
        }
    }

    private void validate(APIUpdateIAM2ProjectTemplateMsg msg) {
        if (msg.getQuota() != null) {
            checkQuota(msg.getQuota());
        }
    }

    private void validate(APIChangeIAM2ProjectStateMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                if (q(IAM2ProjectVO.class)
                        .eq(IAM2ProjectVO_.uuid, msg.getProjectUuid())
                        .eq(IAM2ProjectVO_.state, ProjectState.Retired).isExists()) {
                    if (q(IAM2ProjectAttributeVO.class)
                            .eq(IAM2ProjectAttributeVO_.projectUuid, msg.getProjectUuid())
                            .eq(IAM2ProjectAttributeVO_.name, Retire.RETIRE_POLICY.getName()).isExists()) {
                        throw new OperationFailureException(operr("retire policy must be deleted before pull the project out of Retired state"));
                    }
                }
            }
        }.execute();
    }

    private void validate(APILoginIAM2ProjectMsg msg) {
        ProjectState state = Q.New(IAM2ProjectVO.class).select(IAM2ProjectVO_.state)
                .eq(IAM2ProjectVO_.name, msg.getProjectName()).findValue();

        if (state != ProjectState.Enabled) {
            throw new ApiMessageInterceptionException(operr("login is prohibited because the project is in state of %s", state));
        }
    }

    private void checkQuota(Map<String, Long> qs) {
        qs.forEach((name, value) -> {
            if (acntMgr.getQuotasDefinitions().containsKey(name)) {
                return;
            }

            throw new ApiMessageInterceptionException(argerr("no quota[name:%s] found", name));
        });

        if (!acntMgr.getQuotasDefinitions().containsKey("container.requested.cpu.num")
             || !acntMgr.getQuotasDefinitions().containsKey("container.cpu.num.limitation")
             || !acntMgr.getQuotasDefinitions().containsKey("container.requested.memory.size")
             || !acntMgr.getQuotasDefinitions().containsKey("container.memory.size.limitation")) {
            return;
        }

        Long requestedCpuNum = qs.get("container.requested.cpu.num");
        Long cpuNumLimitation = qs.get("container.cpu.num.limitation");

        if (requestedCpuNum == null) {
            requestedCpuNum = acntMgr.getQuotasDefinitions().get("container.requested.cpu.num").getDefaultValue();
        }
        if (cpuNumLimitation == null) {
            cpuNumLimitation = acntMgr.getQuotasDefinitions().get("container.cpu.num.limitation").getDefaultValue();
        }

        if (cpuNumLimitation < requestedCpuNum) {
            throw new ApiMessageInterceptionException(argerr("container.cpu.num.limitation should be greater than container.requested.cpu.num"));
        }

        Long requestedMemorySize = qs.get("container.requested.memory.size");
        Long memorySizeLimitation = qs.get("container.memory.size.limitation");

        if (requestedMemorySize == null) {
            requestedMemorySize = acntMgr.getQuotasDefinitions().get("container.requested.memory.size").getDefaultValue();
        }
        if (memorySizeLimitation == null) {
            memorySizeLimitation = acntMgr.getQuotasDefinitions().get("container.memory.size.limitation").getDefaultValue();
        }

        if (memorySizeLimitation < requestedMemorySize) {
            throw new ApiMessageInterceptionException(argerr("container.memory.size.limitation should be greater than container.requested.memory.size"));
        }
    }

    private void validate(APICreateIAM2ProjectTemplateMsg msg) {
        if (msg.getQuota() != null) {
            checkQuota(msg.getQuota());
        }
    }

    private void validate(APIGetIAM2ProjectsOfVirtualIDMsg msg) {
        msg.setUuid(msg.getSession().getUserUuid());
    }

    private void validate(APIGetIAM2VirtualIDAPIPermissionMsg msg) {
        msg.setUuid(msg.getSession().getUserUuid());
        if (msg.getApisToCheck() != null) {
            msg.setApisToCheckList(msg.getApisToCheck().stream().map(s -> JSONObjectUtil.toObject(s, APIGetIAM2VirtualIDAPIPermissionMsg.APIPermissionStruct.class)).collect(Collectors.toList()));
        }
    }

    private void validate(APIUpdateIAM2OrganizationMsg msg) {
        if (msg.getParentUuid() != null && Q.New(IAM2OrganizationVO.class).eq(IAM2OrganizationVO_.type, OrganizationType.Company)
                .eq(IAM2OrganizationVO_.uuid, msg.getOrganizationUuid()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("organization[uuid:%s] is a Company that cannot have parent organization", msg.getUuid()));
        }

        if (msg.getParentUuid() != null) {
            List<String> uuids = Q.New(IAM2OrganizationVO.class)
                    .eq(IAM2OrganizationVO_.parentUuid, msg.getUuid())
                    .select(IAM2OrganizationVO_.uuid)
                    .listValues();
            while (uuids != null && !uuids.isEmpty()) {
                if (uuids.contains(msg.getParentUuid())) {
                    throw new ApiMessageInterceptionException(argerr("parent organization[uuid:%s] cannot be a " +
                            "child organization[uuid:%s] of a childOrganization", msg.getUuid(), msg.getParentUuid()));
                }
                uuids = Q.New(IAM2OrganizationVO.class)
                        .in(IAM2OrganizationVO_.parentUuid, uuids)
                        .select(IAM2OrganizationVO_.uuid)
                        .listValues();
            }
        }
    }

    private void validate(APICreateIAM2VirtualIDMsg msg) {
        notAdminName(msg.getName());

        if (msg.getAttributes() != null) {
            validateAttributes(msg.getAttributes());
        }

        if (Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.name, msg.getName()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("duplicate virtualID name[%s]", msg.getName()));
        }
    }

    private void validate(APICreateIAM2VirtualIDGroupMsg msg) {
        if (msg.getAttributes() != null) {
            validateAttributes(msg.getAttributes());
        }
    }

    private void validate(APICreateIAM2ProjectMsg msg) {
        notAdminName(msg.getName());

        if (msg.getAttributes() != null) {
            validateAttributes(msg.getAttributes());
        }

        if (Q.New(IAM2ProjectVO.class).eq(IAM2ProjectVO_.name, msg.getName()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("duplicate project name[%s]", msg.getName()));
        }

        if (msg.getQuota() != null) {
            checkQuota(msg.getQuota());
        }

        if (Q.New(AccountVO.class).eq(AccountVO_.name, msg.getName()).isExists()) {
            throw new ApiMessageInterceptionException(argerr("invalid project name[%s], an account or project with the same name exists", msg.getName()));
        }

        if (msg.getOrganizationUuid() != null) {
            IAM2OrganizationVO organizationVO = dbf.findByUuid(msg.getOrganizationUuid(), IAM2OrganizationVO.class);
            if (organizationVO == null) {
                throw new ApiMessageInterceptionException(argerr("IAM2OrganizationVO[uuid:%s] is not exists", msg.getOrganizationUuid()));
            }
            List<String> organizationUuids = Q.New(IAM2OrganizationVO.class)
                    .eq(IAM2OrganizationVO_.rootOrganizationUuid, organizationVO.getRootOrganizationUuid())
                    .select(IAM2OrganizationVO_.uuid)
                    .listValues();

            List<String> accountUuids = getIAM2OrigationAccountUuids(organizationUuids);
            if (accountUuids.isEmpty()) {
                return;
            }

            List<QuotaVO> quotaVOS = Q.New(QuotaVO.class)
                    .eq(QuotaVO_.identityUuid, organizationVO.getRootOrganizationUuid())
                    .eq(QuotaVO_.identityType, IAM2OrganizationVO.class.getSimpleName())
                    .list();

            if(quotaVOS.isEmpty()) {
                return;
            }

            List<Tuple> projectTs = Q.New(GlobalConfigVO.class).select(GlobalConfigVO_.name, GlobalConfigVO_.value)
                    .eq(GlobalConfigVO_.category, AccountConstant.QUOTA_GLOBAL_CONFIG_CATETORY).listTuple();

            Map<String, Long> projectQuotaMap = new HashMap<>();
            for (Tuple t : projectTs) {
                String rtype = t.get(0, String.class);
                long quota = Long.parseLong(t.get(1, String.class));
                projectQuotaMap.put(rtype, quota);
            }
            checkIAM2OrigationQuota(quotaVOS, msg.getQuota(), projectQuotaMap, msg.getSession().getAccountUuid(), accountUuids);
        }
    }

    public void checkIAM2OrigationQuota(List<QuotaVO> quotaVOS, Map<String, Long> quota,
                                        Map<String, Long> projectQuotaMap, String accountUuid, List<String> accountUuids) {
        for (QuotaVO quotaVO : quotaVOS) {

            if (quotaVO.getValue() == -1) {
                continue;
            }

            long requireQuota = quota != null && quota.get(quotaVO.getName()) != null ?  quota.get(quotaVO.getName()) : projectQuotaMap.get(quotaVO.getName());
            QuotaUtil.QuotaCompareInfo quotaCompareInfo = new QuotaUtil.QuotaCompareInfo();
            quotaCompareInfo.request = requireQuota;
            quotaCompareInfo.quotaValue = quotaVO.getValue();
            quotaCompareInfo.quotaName = quotaVO.getName();
            quotaCompareInfo.currentAccountUuid = accountUuid;
            quotaCompareInfo.resourceTargetOwnerAccountUuid = accountUuid;

            checkIAM2OrigationQuota(accountUuids, quotaCompareInfo, quotaVO.getName());
        }
    }

    public void checkIAM2OrigationQuota(List<String> accountUuids, QuotaUtil.QuotaCompareInfo quotaCompareInfo, String quotaName) {
        quotaCompareInfo.currentUsed = IAM2OrganizationQuotaUtil.getQuotaValue(accountUuids, quotaName);
        new QuotaUtil().CheckQuota(quotaCompareInfo);
    }


    public List<String> getIAM2OrigationAccountUuids(List<String> organizationUuids) {
        List<String> accountUuids = SQL.New("select account.accountUuid from IAM2OrganizationProjectRefVO organ, " +
                "IAM2ProjectAccountRefVO account where organ.projectUuid = account.projectUuid and organ.organizationUuid in(:organizationUuids)")
                .param("organizationUuids", organizationUuids)
                .list();
        return accountUuids == null ? new ArrayList<>() : accountUuids;
    }

    private void validate(APICreateIAM2OrganizationMsg msg) {
        if (msg.getAttributes() != null) {
            validateAttributes(msg.getAttributes());
        }
        if (msg.getQuota() != null) {
            checkQuota(msg.getQuota());
        }
    }

    private void validate(APIAttachIAM2ProjectToIAM2OrganizationMsg msg) {
        IAM2OrganizationProjectRefVO refVO = Q.New(IAM2OrganizationProjectRefVO.class)
                .eq(IAM2OrganizationProjectRefVO_.projectUuid, msg.getProjectUuid())
                .find();

        if (refVO != null) {
            throw new ApiMessageInterceptionException(argerr("The project[uuid=%s] has been attached to the organization[uuid=%s]", refVO.getProjectUuid(), refVO.getOrganizationUuid()));
        }

        IAM2OrganizationVO organizationVO = dbf.findByUuid(msg.getOrganizationUuid(), IAM2OrganizationVO.class);
        List<String> organizationUuids = Q.New(IAM2OrganizationVO.class)
                .eq(IAM2OrganizationVO_.rootOrganizationUuid, organizationVO.getRootOrganizationUuid())
                .select(IAM2OrganizationVO_.uuid)
                .listValues();

        List<QuotaVO> quotaVOS = Q.New(QuotaVO.class)
                .eq(QuotaVO_.identityUuid, msg.getOrganizationUuid())
                .eq(QuotaVO_.identityType, IAM2OrganizationVO.class.getSimpleName())
                .list();
        if (quotaVOS.isEmpty()) {
            return;
        }

        String accountUuid = Q.New(IAM2ProjectAccountRefVO.class)
                .eq(IAM2ProjectAccountRefVO_.projectUuid, msg.getProjectUuid())
                .select(IAM2ProjectAccountRefVO_.accountUuid)
                .findValue();

        List<Tuple> projectTs = Q.New(QuotaVO.class).select(QuotaVO_.name, QuotaVO_.value)
                .eq(QuotaVO_.identityUuid, accountUuid).eq(QuotaVO_.identityType, AccountVO.class.getSimpleName())
                .listTuple();

        Map<String, Long> projectQuotaMap = new HashMap<>();
        for (Tuple t : projectTs) {
            String rtype = t.get(0, String.class);
            long quota = t.get(1, Long.class);
            projectQuotaMap.put(rtype, quota);
        }
        checkIAM2OrigationQuota(quotaVOS, null, projectQuotaMap,
                msg.getSession().getAccountUuid(), getIAM2OrigationAccountUuids(organizationUuids));
    }

    private void validate(APIDetachIAM2ProjectFromIAM2OrganizationMsg msg) {
        IAM2OrganizationProjectRefVO refVO = Q.New(IAM2OrganizationProjectRefVO.class)
                .eq(IAM2OrganizationProjectRefVO_.projectUuid, msg.getProjectUuid())
                .find();

        if (refVO == null) {
            throw new ApiMessageInterceptionException(argerr("The project[uuid=%s] is not attached", msg.getProjectUuid()));
        }
    }

    private void validate(APIChangeIAM2OrganizationParentMsg msg) {
        List<String> uuids = Q.New(IAM2OrganizationVO.class).select(IAM2OrganizationVO_.uuid)
                .in(IAM2OrganizationVO_.uuid, msg.getChildrenUuids())
                .eq(IAM2OrganizationVO_.type, OrganizationType.Company)
                .listValues();

        if (!uuids.isEmpty()) {
            throw new ApiMessageInterceptionException(argerr("organizations%s are company that cannot be children of other organization", uuids));
        }
    }

    private void validate(APIAddRolesToIAM2VirtualIDMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                String targetAccountUuid;
                if (msg.getProjectUuid() != null) {
                    if (!q(IAM2ProjectVirtualIDRefVO.class)
                            .eq(IAM2ProjectVirtualIDRefVO_.projectUuid, msg.getProjectUuid())
                            .eq(IAM2ProjectVirtualIDRefVO_.virtualIDUuid, msg.getVirtualIDUuid())
                            .isExists()) {
                        throw new ApiMessageInterceptionException(argerr("virtual id[uuid: %s] is not in project[uuid: %s]", msg.getVirtualIDUuid(), msg.getProjectUuid()));
                    }

                    targetAccountUuid = q(IAM2ProjectAccountRefVO.class)
                            .select(IAM2ProjectAccountRefVO_.accountUuid)
                            .eq(IAM2ProjectAccountRefVO_.projectUuid, msg.getProjectUuid())
                            .findValue();

                } else {
                    targetAccountUuid = msg.getSession().getAccountUuid();
                }

                List<String> already = q(IAM2VirtualIDRoleRefVO.class).select(IAM2VirtualIDRoleRefVO_.roleUuid)
                        .eq(IAM2VirtualIDRoleRefVO_.virtualIDUuid, msg.getVirtualIDUuid())
                        .in(IAM2VirtualIDRoleRefVO_.roleUuid, msg.getRoleUuids())
                        .eq(IAM2VirtualIDRoleRefVO_.targetAccountUuid, targetAccountUuid)
                        .listValues();
                msg.getRoleUuids().removeAll(already);
                msg.setRoleUuids(msg.getRoleUuids().stream().distinct().collect(Collectors.toList()));
            }
        }.execute();
    }

    private void validate(APIAddRolesToIAM2VirtualIDGroupMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                String accountUuid;
                String sql = "select ref.roleUuid from IAM2VirtualIDGroupRoleRefVO ref where ref.groupUuid = :groupUuid " +
                        "and ref.roleUuid in(:roleUuids)";
                if (msg.getProjectUuid()  != null) {
                    sql += " and targetAccountUuid = :targetAccountUuid";
                }

                Query query = dbf.getEntityManager().createQuery(sql);
                query.setParameter("groupUuid", msg.getGroupUuid());
                query.setParameter("roleUuids",  msg.getRoleUuids());

                if (msg.getProjectUuid() != null) {
                    accountUuid = Q.New(IAM2ProjectAccountRefVO.class)
                            .select(IAM2ProjectAccountRefVO_.accountUuid)
                            .eq(IAM2ProjectAccountRefVO_.projectUuid, msg.getProjectUuid()).findValue();
                    query.setParameter("targetAccountUuid",  accountUuid);
                }

                List<String> already = query.getResultList();

                if (already != null) {
                    msg.getRoleUuids().removeAll(already);
                }
            }
        }.execute();
    }

    private void validate(APIAddIAM2VirtualIDsToProjectMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                checkIfAnyVirtualIDExists(msg.getVirtualIDUuids());

                List<String> already = q(IAM2ProjectVirtualIDRefVO.class).select(IAM2ProjectVirtualIDRefVO_.virtualIDUuid).eq(IAM2ProjectVirtualIDRefVO_.projectUuid, msg.getProjectUuid())
                        .in(IAM2ProjectVirtualIDRefVO_.virtualIDUuid, msg.getVirtualIDUuids()).listValues();
                msg.getVirtualIDUuids().removeAll(already);
            }
        }.execute();

        if (msg.getRoleUuids() != null && !msg.getRoleUuids().isEmpty())
            msg.setRoleUuids(msg.getRoleUuids().stream().distinct().collect(Collectors.toList()));

        if (msg.getRoleUuids() != null && !msg.getRoleUuids().isEmpty() && msg.getRoleUuids().contains(IAM2RolePolicyStatementHelper.PROJECT_ADMIN_ROLE_UUID)) {
            throw new ApiMessageInterceptionException(argerr("illegal operation, cannot add Role[%s]", IAM2RolePolicyStatementHelper.PROJECT_ADMIN_ROLE_NAME));
        }

    }

    private void validate(APIAddIAM2VirtualIDsToOrganizationMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                checkIfAnyVirtualIDExists(msg.getVirtualIDUuids());

                List<String> already = q(IAM2VirtualIDOrganizationRefVO.class).select(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid)
                        .eq(IAM2VirtualIDOrganizationRefVO_.organizationUuid, msg.getOrganizationUuid())
                        .in(IAM2VirtualIDOrganizationRefVO_.virtualIDUuid, msg.getVirtualIDUuids()).listValues();
                msg.getVirtualIDUuids().removeAll(already);
            }
        }.execute();
    }

    private void checkIfAnyVirtualIDExists(List<String> virtualIDsNeedToCheck) {
        List<String> staleVirtualIDs = Q.New(IAM2VirtualIDVO.class)
                .select(IAM2VirtualIDVO_.uuid)
                .eq(IAM2VirtualIDVO_.state, IAM2State.Staled)
                .in(IAM2VirtualIDVO_.uuid, virtualIDsNeedToCheck)
                .listValues();

        if (!staleVirtualIDs.isEmpty()) {
            throw new ApiMessageInterceptionException(argerr("can not operate stale virtual ids: %s", staleVirtualIDs));
        }
    }

    private void validate(APIAddIAM2VirtualIDsToGroupMsg msg) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                checkIfAnyVirtualIDExists(msg.getVirtualIDUuids());

                List<String> in = sql("select vr.virtualIDUuid from IAM2ProjectVirtualIDRefVO vr, IAM2VirtualIDGroupVO g" +
                        " where vr.projectUuid = g.projectUuid and vr.virtualIDUuid in (:uuids) and g.uuid = :guuid")
                        .param("uuids", msg.getVirtualIDUuids())
                        .param("guuid", msg.getGroupUuid())
                        .list();

                /*if (!in.containsAll(msg.getVirtualIDUuids())) {
                    List<String> wrong = msg.getVirtualIDUuids().stream().filter(vid->!in.contains(vid)).collect(Collectors.toList());

                    throw new ApiMessageInterceptionException(argerr("virtual ids%s not in the project the group[uuid:%s] belongs to",
                            wrong, msg.getGroupUuid()));
                }*/

                List<String> already = q(IAM2VirtualIDGroupRefVO.class).select(IAM2VirtualIDGroupRefVO_.virtualIDUuid)
                        .eq(IAM2VirtualIDGroupRefVO_.groupUuid, msg.getGroupUuid()).in(IAM2VirtualIDGroupRefVO_.virtualIDUuid, msg.getVirtualIDUuids()).listValues();
                msg.getVirtualIDUuids().removeAll(already);
            }
        }.execute();
    }

    private void validate(APIAddAttributesToIAM2OrganizationMsg msg) {
        validateAttributes(msg.getAttributes());
    }

    private void validate(APIAddAttributesToIAM2VirtualIDMsg msg) {
        validateAttributes(msg.getAttributes());
    }

    private void validate(APIAddAttributesToIAM2VirtualIDGroupMsg msg) {
        validateAttributes(msg.getAttributes());
    }

    private void validate(APIAddAttributesToIAM2ProjectMsg msg) {
        validateAttributes(msg.getAttributes());
    }

    private void validate(APISetIAM2ProjectRetirePolicyMsg msg) {
        new RetirePolicy(msg.getPolicy(), true);

        Attribute attr = new Attribute();
        attr.setName(Retire.RETIRE_POLICY.getName());
        attr.setValue(msg.getPolicy());

        validateAttributes(Collections.singletonList(attr));
    }
}
