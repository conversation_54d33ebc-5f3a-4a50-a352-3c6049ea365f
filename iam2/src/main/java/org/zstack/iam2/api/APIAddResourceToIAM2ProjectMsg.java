package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.iam2.project.template.SecurityGroupTemplate;

import java.util.List;

import static java.util.Arrays.asList;

/**
 * Created by LiangHanY<PERSON> on 2022/3/3 15:30
 */
@RestRequest(path = "/iam2/projects/add/resource/", method = HttpMethod.POST,
        responseClass = APIAddResourceToIAM2ProjectEvent.class, parameterName = "params")
public class APIAddResourceToIAM2ProjectMsg extends APIMessage implements ProjectMessage {
    @APIParam(resourceType = IAM2ProjectVO.class)
    private String projectUuid;

    @APIParam
    private List<String> resourceTemplates;

    public String getProjectUuid() {
        return projectUuid;
    }

    public void setProjectUuid(String projectUuid) {
        this.projectUuid = projectUuid;
    }

    public List<String> getResourceTemplates() {
        return resourceTemplates;
    }

    public void setResourceTemplates(List<String> resourceTemplates) {
        this.resourceTemplates = resourceTemplates;
    }

    public static APIAddResourceToIAM2ProjectMsg __example__() {
        APIAddResourceToIAM2ProjectMsg ret = new APIAddResourceToIAM2ProjectMsg();
        ret.setProjectUuid(uuid());
        ret.resourceTemplates = asList(new SecurityGroupTemplate().toJSON(),new SecurityGroupTemplate().toJSON());
        return ret;
    }
}