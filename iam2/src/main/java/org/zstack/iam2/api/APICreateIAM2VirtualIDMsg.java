package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.encrypt.EncryptionParamAllowed;
import org.zstack.header.log.NoLogging;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationVO;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.iam2.entity.IAM2VirtualIDVO;

import java.util.List;

import static java.util.Arrays.asList;

@RestRequest(path = "/iam2/virtual-ids", method = HttpMethod.POST,
        responseClass = APICreateIAM2VirtualIDEvent.class, parameterName = "params")
@EncryptionParamAllowed
public class APICreateIAM2VirtualIDMsg extends APICreateMessage implements APIAuditor {
    @APIParam(maxLength = 255)
    private String name;
    @APIParam(maxLength = 2048, password = true)
    @NoLogging
    private String password;
    @APIParam(maxLength = 2048, required = false)
    private String description;
    private List<Attribute> attributes;
    @APIParam(resourceType = IAM2ProjectVO.class, required = false)
    private String projectUuid;
    @APIParam(resourceType = IAM2OrganizationVO.class, required = false)
    private String organizationUuid;
    @APIParam(required = false)
    private boolean withoutDefaultRole = false;

    public static APICreateIAM2VirtualIDMsg __example__() {
        APICreateIAM2VirtualIDMsg ret = new APICreateIAM2VirtualIDMsg();
        ret.name = "id1";
        ret.password = "password";
        ret.attributes = asList(Attribute.__example__());
        ret.projectUuid = uuid();
        ret.organizationUuid = uuid();
        return ret;
    }

    public String getOrganizationUuid() {
        return organizationUuid;
    }

    public void setOrganizationUuid(String organizationUuid) {
        this.organizationUuid = organizationUuid;
    }

    public String getProjectUuid() {
        return projectUuid;
    }

    public void setProjectUuid(String projectUuid) {
        this.projectUuid = projectUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }

    public boolean isWithoutDefaultRole() {
        return withoutDefaultRole;
    }

    public void setWithoutDefaultRole(boolean withoutDefaultRole) {
        this.withoutDefaultRole = withoutDefaultRole;
    }

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(
                rsp.isSuccess() ? ((APICreateIAM2VirtualIDEvent) rsp).getInventory().getUuid() : "",
                IAM2VirtualIDVO.class
        );
    }
}
