package org.zstack.iam2.api

import org.zstack.header.errorcode.ErrorCode
import org.zstack.iam2.entity.IAM2VirtualIDInventory

doc {

	title "操作返回结果"

	field {
		name "success"
		desc ""
		type "boolean"
		since "0.6"
	}
	ref {
		name "error"
		path "org.zstack.iam2.api.APIUpdateIAM2VirtualIDEvent.error"
		desc "错误码，若不为null，则表示操作失败, 操作成功时该字段为null",false
		type "ErrorCode"
		since "2.4.0"
		clz ErrorCode.class
	}
	ref {
		name "inventory"
		path "org.zstack.iam2.api.APIUpdateIAM2VirtualIDEvent.inventory"
		desc "null"
		type "IAM2VirtualIDInventory"
		since "2.4.0"
		clz IAM2VirtualIDInventory.class
	}
}
