package org.zstack.iam2.api;

import org.zstack.header.identity.QuotaInventory;
import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

/**
 * <AUTHOR>
 * @Package org.zstack.iam2.api
 * @date 2021/6/6 11:49 AM
 */
@RestResponse(allTo = "inventory")
public class APIUpdateOrganizationQuotaEvent extends APIEvent {
    private QuotaInventory inventory;

    public APIUpdateOrganizationQuotaEvent() {
    }

    public APIUpdateOrganizationQuotaEvent(String apiId) {
        super(apiId);
    }

    public QuotaInventory getInventory() {
        return inventory;
    }

    public void setInventory(QuotaInventory inventory) {
        this.inventory = inventory;
    }

    public static APIUpdateOrganizationQuotaEvent __example__() {
        APIUpdateOrganizationQuotaEvent event = new APIUpdateOrganizationQuotaEvent();
        QuotaInventory inventory = new QuotaInventory();
        inventory.setName("quota");
        inventory.setValue(20);
        inventory.setIdentityUuid(uuid());
        event.setInventory(inventory);
        return event;
    }

}
