package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.core.NoDoc;
import org.zstack.header.identity.UserVO;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDGroupVO;

@RestRequest(path = "/iam2/IAM2VirtualIDGroup/IAM2VirtualID", method = HttpMethod.GET,
        responseClass = APIGetIAM2VirtualIDInGroupReply.class)
public class APIGetIAM2VirtualIDInGroupMsg extends APISyncCallMessage implements GroupMessage {
    @APIParam(resourceType = IAM2VirtualIDGroupVO.class)
    private String groupUuid;
    @APIParam(required = false)
    private Integer limit = 1000;
    @APIParam(required = false)
    private Integer start = 0;
    @APIParam(required = false)
    private boolean count;
    @APIParam(required = false, validValues = {"asc", "desc"})
    private String sortDirection;
    @APIParam(required = false)
    private String sortBy;
    @APIParam(required = false)
    private boolean replyWithCount = false;

    public String getGroupUuid() {
        return groupUuid;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public boolean isCount() {
        return count;
    }

    public void setCount(boolean count) {
        this.count = count;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public boolean isReplyWithCount() {
        return replyWithCount;
    }

    public void setReplyWithCount(boolean replyWithCount) {
        this.replyWithCount = replyWithCount;
    }

    public void setGroupUuid(String groupUuid) {
        this.groupUuid = groupUuid;
    }

    public static APIGetIAM2VirtualIDInGroupMsg __example__() {
        APIGetIAM2VirtualIDInGroupMsg ret = new APIGetIAM2VirtualIDInGroupMsg();
        ret.setGroupUuid(uuid());
        ret.setLimit(20);
        ret.setStart(0);
        ret.setReplyWithCount(true);
        ret.setCount(false);
        ret.setSortBy("uploadDate");
        ret.setSortDirection("desc");
        return ret;
    }
}
