package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.core.Platform;
import org.zstack.header.identity.role.RoleVO;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationVO;
import org.zstack.iam2.entity.IAM2VirtualIDVO;

@RestRequest(
        path = "/iam2/organizations/{uuid}/operation",
        method = HttpMethod.PUT,
        isAction = true,
        responseClass = APISetOrganizationOperationEvent.class
)
public class APISetOrganizationOperationMsg extends APIMessage implements OrganizationMessage {
    @APIParam(resourceType = IAM2OrganizationVO.class)
    private String uuid;
    @APIParam(resourceType = IAM2VirtualIDVO.class)
    private String virtualIDUuid;
    @Override
    public String getOrganizationUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getVirtualIDUuid() {
        return virtualIDUuid;
    }

    public void setVirtualIDUuid(String virtualIDUuid) {
        this.virtualIDUuid = virtualIDUuid;
    }

    public static APISetOrganizationOperationMsg __example__() {
        APISetOrganizationOperationMsg msg = new APISetOrganizationOperationMsg();
        msg.setVirtualIDUuid(Platform.getUuid());
        msg.setUuid(Platform.getUuid());

        return msg;
    }
}
