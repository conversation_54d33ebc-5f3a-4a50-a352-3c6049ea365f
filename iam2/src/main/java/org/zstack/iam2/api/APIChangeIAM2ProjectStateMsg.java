package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.iam2.entity.StateEvent;

@RestRequest(path = "/iam2/projects/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIChangeIAM2ProjectStateEvent.class)
public class APIChangeIAM2ProjectStateMsg extends APIMessage implements ProjectMessage {
    @APIParam(resourceType = IAM2ProjectVO.class)
    private String uuid;
    @APIParam(validValues = {"enable", "disable"})
    private StateEvent stateEvent;

    public static APIChangeIAM2ProjectStateMsg __example__() {
        APIChangeIAM2ProjectStateMsg ret = new APIChangeIAM2ProjectStateMsg();
        ret.uuid = uuid();
        ret.stateEvent = StateEvent.enable;
        return ret;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public StateEvent getStateEvent() {
        return stateEvent;
    }

    public void setStateEvent(StateEvent stateEvent) {
        this.stateEvent = stateEvent;
    }

    @Override
    public String getProjectUuid() {
        return uuid;
    }
}
