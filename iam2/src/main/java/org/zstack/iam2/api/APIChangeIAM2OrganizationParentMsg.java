package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationVO;

import java.util.List;

import static java.util.Arrays.asList;

@RestRequest(path = "/iam2/organizations/{parentUuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIChangeIAM2OrganizationParentEvent.class)
public class APIChangeIAM2OrganizationParentMsg extends APIMessage implements OrganizationMessage {
    @APIParam(resourceType = IAM2OrganizationVO.class)
    private String parentUuid;
    @APIParam(resourceType = IAM2OrganizationVO.class, nonempty = true)
    private List<String> childrenUuids;

    public static APIChangeIAM2OrganizationParentMsg __example__() {
        APIChangeIAM2OrganizationParentMsg ret = new APIChangeIAM2OrganizationParentMsg();
        ret.parentUuid = uuid();
        ret.childrenUuids = asList(uuid(), uuid());
        return ret;
    }

    public String getParentUuid() {
        return parentUuid;
    }

    public void setParentUuid(String parentUuid) {
        this.parentUuid = parentUuid;
    }

    public List<String> getChildrenUuids() {
        return childrenUuids;
    }

    public void setChildrenUuids(List<String> childrenUuids) {
        this.childrenUuids = childrenUuids;
    }

    @Override
    public String getOrganizationUuid() {
        return parentUuid;
    }
}
