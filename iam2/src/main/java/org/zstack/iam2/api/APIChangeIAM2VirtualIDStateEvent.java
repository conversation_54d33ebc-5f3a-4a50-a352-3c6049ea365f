package org.zstack.iam2.api;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2VirtualIDInventory;

@RestResponse(allTo = "inventory")
public class APIChangeIAM2VirtualIDStateEvent extends APIEvent {
    private IAM2VirtualIDInventory inventory;

    public static APIChangeIAM2VirtualIDStateEvent __example__() {
        APIChangeIAM2VirtualIDStateEvent ret = new APIChangeIAM2VirtualIDStateEvent();
        ret.inventory = IAM2VirtualIDInventory.__example__();
        return ret;
    }

    public APIChangeIAM2VirtualIDStateEvent() {
    }

    public APIChangeIAM2VirtualIDStateEvent(String apiId) {
        super(apiId);
    }

    public IAM2VirtualIDInventory getInventory() {
        return inventory;
    }

    public void setInventory(IAM2VirtualIDInventory inventory) {
        this.inventory = inventory;
    }
}
