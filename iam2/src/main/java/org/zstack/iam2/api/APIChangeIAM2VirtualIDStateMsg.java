package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.core.Platform;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2VirtualIDVO;
import org.zstack.iam2.entity.StateEvent;

@RestRequest(path = "/iam2/virtual-ids/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIChangeIAM2VirtualIDStateEvent.class)
public class APIChangeIAM2VirtualIDStateMsg extends APIMessage implements VirtualIDMessage {
    @APIParam(resourceType = IAM2VirtualIDVO.class)
    private String uuid;
    @APIParam(validValues = {"enable", "disable"})
    private StateEvent stateEvent;

    public static APIChangeIAM2VirtualIDStateMsg __example__() {
        APIChangeIAM2VirtualIDStateMsg ret = new APIChangeIAM2VirtualIDStateMsg();
        ret.uuid = Platform.getUuid();
        ret.stateEvent = StateEvent.enable;
        return ret;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public StateEvent getStateEvent() {
        return stateEvent;
    }

    public void setStateEvent(StateEvent stateEvent) {
        this.stateEvent = stateEvent;
    }

    @Override
    public String getVirtualIDUuid() {
        return uuid;
    }
}
