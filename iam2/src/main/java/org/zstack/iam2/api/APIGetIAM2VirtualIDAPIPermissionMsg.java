package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.rbac.SuppressRBACCheck;
import org.zstack.header.message.APIParam;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.message.NoJsonSchema;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.rest.SDK;
import org.zstack.iam2.entity.IAM2VirtualIDVO;

import java.util.Collections;
import java.util.List;

@RestRequest(path = "/iam2/virtual-ids/api-permissions", method = HttpMethod.GET, responseClass = APIGetIAM2VirtualIDAPIPermissionReply.class)
@SuppressRBACCheck
public class APIGetIAM2VirtualIDAPIPermissionMsg extends APISyncCallMessage implements VirtualIDMessage {
    @APINoSee
    private String uuid;

    public static APIGetIAM2VirtualIDAPIPermissionMsg __example__() {
        APIGetIAM2VirtualIDAPIPermissionMsg ret = new APIGetIAM2VirtualIDAPIPermissionMsg();
        ret.setApisToCheck(Collections.singletonList("{\"apiName\":\"org.zstack.header.vm.APICreateVmInstanceMsg\",\"body\":{\"name\":\"vm\"}}"));
        return ret;
    }

    @SDK
    public static class APIPermissionStruct {
        public String apiName;
        public Object body;
    }

    private List<String> apisToCheck;

    private boolean onlyCheckParams = false;

    @APINoSee
    @NoJsonSchema
    private List<APIPermissionStruct> apisToCheckList;

    public List<APIPermissionStruct> getApisToCheckList() {
        return apisToCheckList;
    }

    public List<String> getApisToCheck() {
        return apisToCheck;
    }

    public void setApisToCheck(List<String> apisToCheck) {
        this.apisToCheck = apisToCheck;
    }

    public void setApisToCheckList(List<APIPermissionStruct> apisToCheckList) {
        this.apisToCheckList = apisToCheckList;
    }

    @Override
    public String getVirtualIDUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public boolean isOnlyCheckParams() {
        return onlyCheckParams;
    }

    public void setOnlyCheckParams(boolean onlyCheckParams) {
        this.onlyCheckParams = onlyCheckParams;
    }
}
