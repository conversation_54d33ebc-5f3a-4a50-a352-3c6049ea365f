package org.zstack.iam2.api;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2VirtualIDInventory;

import java.util.List;

import static java.util.Arrays.asList;

@RestResponse(allTo = "inventories")
public class APIQueryIAM2VirtualIDReply extends APIQueryReply {
    private List<IAM2VirtualIDInventory> inventories;

    public static APIQueryIAM2VirtualIDReply __example__() {
        APIQueryIAM2VirtualIDReply ret = new APIQueryIAM2VirtualIDReply();
        ret.inventories = asList(IAM2VirtualIDInventory.__example__());
        return ret;
    }

    public List<IAM2VirtualIDInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<IAM2VirtualIDInventory> inventories) {
        this.inventories = inventories;
    }
}
