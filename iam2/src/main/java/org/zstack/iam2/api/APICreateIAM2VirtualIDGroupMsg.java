package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2ProjectVO;
import org.zstack.iam2.entity.IAM2VirtualIDGroupVO;

import java.util.List;

@RestRequest(path = "/iam2/groups", optionalPaths = {"/iam2/projects/{projectUuid}/groups"}, method = HttpMethod.POST,
        responseClass = APICreateIAM2VirtualIDGroupEvent.class, parameterName = "params")
public class APICreateIAM2VirtualIDGroupMsg extends APICreateMessage implements APIAuditor {
    @APIParam(required = false, resourceType = IAM2ProjectVO.class)
    private String projectUuid;
    @APIParam(maxLength = 255)
    private String name;
    @APIParam(maxLength = 2048, required = false)
    private String description;
    private List<Attribute> attributes;

    public static APICreateIAM2VirtualIDGroupMsg __example__() {
        APICreateIAM2VirtualIDGroupMsg ret = new APICreateIAM2VirtualIDGroupMsg();
        ret.projectUuid = uuid();
        ret.name = "group";
        return ret;
    }

    public String getProjectUuid() {
        return projectUuid;
    }

    public void setProjectUuid(String projectUuid) {
        this.projectUuid = projectUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(
                rsp.isSuccess() ? ((APICreateIAM2VirtualIDGroupEvent) rsp).getInventory().getUuid() : "",
                IAM2VirtualIDGroupVO.class
        );
    }
}
