package org.zstack.iam2.api;

import org.zstack.core.config.APIQueryGlobalConfigMsg;
import org.zstack.core.db.Q;
import org.zstack.header.identity.APIQueryQuotaMsg;
import org.zstack.header.identity.AccountConstant;
import org.zstack.header.identity.rbac.RBACDescription;
import org.zstack.iam2.IAM2Manager;
import org.zstack.iam2.attribute.virtualid.*;
import org.zstack.iam2.entity.IAM2OrganizationVO;
import org.zstack.iam2.entity.IAM2VirtualIDAttributeVO;
import org.zstack.iam2.entity.IAM2VirtualIDAttributeVO_;
import org.zstack.iam2.entity.IAM2VirtualIDGroupVO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import static org.zstack.utils.CollectionDSL.list;

public class RBACInfo implements RBACDescription {
    private boolean isProjectAdminOfProject(Attribute attr, String virtualIDUuid) {
        return Q.New(IAM2VirtualIDAttributeVO.class).eq(IAM2VirtualIDAttributeVO_.virtualIDUuid, virtualIDUuid)
                .in(IAM2VirtualIDAttributeVO_.name, IAM2ProjectAdmin.legacyNames)
                .eq(IAM2VirtualIDAttributeVO_.value, attr.getValue()).isExists();
    }

    @Override
    public void permissions() {
        permissionBuilder()
                .name("iam2")
                .adminOnlyAPIs(
                        APICreateIAM2ProjectMsg.class,
                        APIDeleteIAM2ProjectMsg.class,
                        APIChangeIAM2OrganizationStateMsg.class,
                        APIAddAttributesToIAM2ProjectMsg.class,
                        APIRemoveAttributesFromIAM2ProjectMsg.class,
                        APICreateIAM2OrganizationMsg.class,
                        APIDeleteIAM2OrganizationMsg.class,
                        APIUpdateIAM2OrganizationMsg.class,
                        APIChangeIAM2OrganizationParentMsg.class,
                        APIChangeIAM2OrganizationStateMsg.class,
                        APIUpdateIAM2ProjectTemplateMsg.class,
                        APIStopAllResourcesInIAM2ProjectMsg.class,
                        APIAttachIAM2ProjectToIAM2OrganizationMsg.class,
                        APIDetachIAM2ProjectFromIAM2OrganizationMsg.class,
                        APIAddResourceToIAM2ProjectMsg.class
                )
                .targetResources(IAM2OrganizationVO.class, IAM2VirtualIDGroupVO.class)
                .normalAPIs("org.zstack.iam2.api.**")
                .build();

        registerAPIPermissionChecker(null, msg -> !msg.getSession().getAccountUuid().equals(IAM2Manager.DUMMY_ACCOUNT_UUID) || msg instanceof APIUpdateIAM2VirtualIDMsg || msg instanceof APIQueryGlobalConfigMsg);
        registerAPIPermissionChecker(APIDeleteIAM2VirtualIDMsg.class, msg -> AccountConstant.isAdminPermission(msg.getSession()));
        registerAPIPermissionChecker(APIDeleteIAM2ProjectMsg.class, msg -> AccountConstant.isAdminPermission(msg.getSession()));



        registerAPIPermissionChecker(APIAddAttributesToIAM2VirtualIDMsg.class, imsg -> {
            APIAddAttributesToIAM2VirtualIDMsg msg = (APIAddAttributesToIAM2VirtualIDMsg) imsg;
            if (AccountConstant.isAdmin(msg.getSession())) {
                return true;
            }

            for (Attribute attr : msg.getAttributes()) {
                if (IAM2PlatformAdmin.legacyNames.contains(attr.getName())) {
                    // only admin can do it
                    return false;
                } else if (AccountConstant.isAdminPermission(msg.getSession())) {
                    // platform admin can do everything
                    return true;
                } else if (IAM2ProjectAdmin.legacyNames.contains(attr.getName()) || IAM2ProjectOperator.legacyNames.contains(attr.getName())) {
                    if (!isProjectAdminOfProject(attr, msg.getSession().getUserUuid())) {
                        return false;
                    }
                }
            }

            return true;
        });

        registerAPIPermissionChecker(APIRemoveAttributesFromIAM2VirtualIDMsg.class, m -> {
            APIRemoveAttributesFromIAM2VirtualIDMsg msg = (APIRemoveAttributesFromIAM2VirtualIDMsg) m;

            if (AccountConstant.isAdmin(msg.getSession())) {
                return true;
            }

            List<String> attributeNames = new ArrayList<>(IAM2ProjectOperator.legacyNames);
            attributeNames.addAll(IAM2ProjectAdmin.legacyNames);
            attributeNames.addAll(IAM2PlatformAdmin.legacyNames);
            List<IAM2VirtualIDAttributeVO> attrs = Q.New(IAM2VirtualIDAttributeVO.class)
                    .in(IAM2VirtualIDAttributeVO_.name, attributeNames)
                    .in(IAM2VirtualIDAttributeVO_.uuid, msg.getAttributeUuids()).list();

            for (IAM2VirtualIDAttributeVO attr : attrs) {
                if (IAM2PlatformAdmin.legacyNames.contains(attr.getName())) {
                    // only admin can do it
                    return false;
                } else if (AccountConstant.isAdminPermission(msg.getSession())) {
                    // platform admin can do everything
                    return true;
                } else {
                    Attribute attribute = new Attribute();
                    attribute.setName(attr.getName());
                    attribute.setValue(attr.getValue());

                    if (!isProjectAdminOfProject(attribute, msg.getSession().getUserUuid())) {
                        return false;
                    }
                }
            }

            return true;
        });
    }

    @Override
    public void contributeToRoles() {

    }

    @Override
    public void roles() {
        roleBuilder()
                .uuid("9517462744b34495b80679b405215490")
                .name("iam2-project-admin-roles")
                .permissionsByName("iam2")
                .notPredefined()
                .build();

        roleBuilder()
                .uuid("e39691314bae43168d5d7a69ac3e91c1")
                .name("iam2-project-operator-roles")
                .permissionsByName("iam2")
                .notPredefined()
                .build();

        roleBuilder()
                .uuid("3968db40f92a46289de2554eec9dc9b1")
                .name("iam2-normal-user-roles")
                .actions(
                        APIUpdateIAM2VirtualIDMsg.class,
                        APIQueryIAM2VirtualIDMsg.class,
                        APIQueryIAM2VirtualIDGroupMsg.class,
                        APIQueryQuotaMsg.class
                )
                .build();

        roleBuilder()
                .uuid("f0ed893bc7004740b9f195989dd1f412")
                .name("privilege-admin-basic-roles")
                .actions(
                        APIUpdateIAM2VirtualIDMsg.class,
                        APIQueryIAM2VirtualIDMsg.class,
                        APIQueryIAM2VirtualIDGroupMsg.class,
                        APIQueryQuotaMsg.class
                )
                .notPredefined()
                .build();
    }

    @Override
    public void globalReadableResources() {

    }
}
