package org.zstack.iam2.api;

import org.zstack.header.query.APIQueryReply;
import org.zstack.header.rest.RestResponse;
import org.zstack.iam2.entity.IAM2OrganizationInventory;

import java.util.List;

import static java.util.Arrays.asList;

@RestResponse(allTo = "inventories")
public class APIQueryIAM2OrganizationReply extends APIQueryReply {
    private List<IAM2OrganizationInventory> inventories;

    public static APIQueryIAM2OrganizationReply __example__() {
        APIQueryIAM2OrganizationReply ret = new APIQueryIAM2OrganizationReply();
        ret.inventories = asList(IAM2OrganizationInventory.__example__());
        return ret;
    }

    public List<IAM2OrganizationInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<IAM2OrganizationInventory> inventories) {
        this.inventories = inventories;
    }
}
