package org.zstack.iam2.api;

import org.springframework.http.HttpMethod;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.iam2.entity.IAM2OrganizationVO;
import org.zstack.iam2.entity.StateEvent;

@RestRequest(path = "/iam2/organizations/{uuid}/actions", method = HttpMethod.PUT,
        isAction = true, responseClass = APIChangeIAM2OrganizationStateEvent.class)
public class APIChangeIAM2OrganizationStateMsg extends APIMessage implements OrganizationMessage {
    @APIParam(resourceType = IAM2OrganizationVO.class)
    private String uuid;
    @APIParam(validValues = {"enable", "disable"})
    private StateEvent stateEvent;

    public static APIChangeIAM2OrganizationStateMsg __example__() {
        APIChangeIAM2OrganizationStateMsg ret = new APIChangeIAM2OrganizationStateMsg();
        ret.uuid = uuid();
        ret.stateEvent = StateEvent.enable;
        return ret;
    }

    @Override
    public String getOrganizationUuid() {
        return uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public StateEvent getStateEvent() {
        return stateEvent;
    }

    public void setStateEvent(StateEvent stateEvent) {
        this.stateEvent = stateEvent;
    }
}
