package org.zstack.privilege.admin;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatch;
import org.zstack.iam2.attribute.AttributeAction;
import org.zstack.iam2.attribute.SystemAttributes;
import org.zstack.iam2.attribute.virtualid.AbstractAdminAttribute;
import org.zstack.iam2.entity.*;

import java.util.ArrayList;
import java.util.List;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class SystemAdmin extends AbstractAdminAttribute implements AttributeAction {
    public static final IAM2AttributeInventory SYSTEM_ADMIN = SystemAttributes.define("__SystemAdmin__", new IAM2VirtualIDAttributeInventory());

    @Override
    public Class getResourceType() {
        return IAM2VirtualIDVO.class;
    }

    @Override
    public String getAttributeName() {
        return SYSTEM_ADMIN.getName();
    }

    @Override
    public void actAtAdd(IAM2AttributeInventory inv) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                checkDuplicate(((IAM2VirtualIDAttributeInventory) inv).getVirtualIDUuid(), getAttributeName());
            }
        }.execute();
    }

    public static boolean isSystemAdmin(String uuid) {
        return Q.New(IAM2VirtualIDAttributeVO.class)
                .eq(IAM2VirtualIDAttributeVO_.name, SYSTEM_ADMIN.getName())
                .eq(IAM2VirtualIDAttributeVO_.virtualIDUuid, uuid)
                .isExists();
    }

    @Override
    public void actAtRemove(IAM2AttributeInventory inv) {
    }

    @Override
    public List<String> getPolicyBlackList() {
        return new ArrayList<>(IAM2PrivilegeAdminPolicyHelper.systemAdminBlackList);
    }

    @Override
    public int getOrder() {
        return 2;
    }

}
