package org.zstack.privilege.admin;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.Platform;
import org.zstack.core.config.APIQueryGlobalConfigMsg;
import org.zstack.core.config.APIUpdateGlobalConfigMsg;
import org.zstack.core.config.GlobalConfigBeforeResetExtensionPoint;
import org.zstack.core.config.SkipResetGlobalConfigException;
import org.zstack.core.db.*;
import org.zstack.header.Component;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.core.StaticInit;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.identity.*;
import org.zstack.header.identity.rbac.PolicyMatcher;
import org.zstack.header.identity.rbac.RBAC;
import org.zstack.header.identity.role.*;
import org.zstack.header.identity.role.api.*;
import org.zstack.header.image.APISetImageSecurityLevelMsg;
import org.zstack.header.license.LicenseMessage;
import org.zstack.header.managementnode.APIQueryManagementNodeMsg;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APISyncCallMessage;
import org.zstack.header.vm.APISetVmSecurityLevelMsg;
import org.zstack.iam2.IAM2AttributeCalculateExtensionPoint;
import org.zstack.iam2.IAM2Constant;
import org.zstack.iam2.IAM2Manager;
import org.zstack.iam2.IAM2SystemTags;
import org.zstack.iam2.api.*;
import org.zstack.iam2.attribute.AttributeAction;
import org.zstack.iam2.entity.*;
import org.zstack.iam2.rbac.IAM2AuthorizationBackend;
import org.zstack.iam2.rbac.IAM2RolePolicyStatementHelper;
import org.zstack.identity.AccountLoginExtensionPoint;
import org.zstack.identity.AccountLoginStruct;
import org.zstack.identity.IdentityResourceGenerateExtensionPoint;
import org.zstack.identity.rbac.PolicyCreationExtensionPoint;
import org.zstack.identity.rbac.PolicyUtils;
import org.zstack.loginControl.PasswordStrategyGlobalConfig;
import org.zstack.query.APIZQLQueryMsg;
import org.zstack.tag.TagManager;
import org.zstack.twoFactorAuthentication.TwoFactorAuthenticationGlobalConfig;

import java.util.*;
import java.util.stream.Collectors;

import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.err;

public class IAM2PrivilegeAdminPolicyHelper implements Component, AccountLoginExtensionPoint, PolicyCreationExtensionPoint, GlobalApiMessageInterceptor, IdentityResourceGenerateExtensionPoint, IAM2AttributeCalculateExtensionPoint {

    @Autowired
    private TagManager tagMgr;
    @Autowired
    private DatabaseFacade dbf;

    public static Set<String> systemAdminBlackList = new HashSet<>();
    public static Set<String> securityAdminWhiteList = new HashSet<>();
    public static Set<String> auditAdminWhiteList = new HashSet<>();
    public static List<String> systemAdminApis = new ArrayList<>();
    public static String readAPIsForAuditAdminJSONStatement;
    public static String permittedAPIsForSecurityAdminJSONStatement;

    public static final String SECURITY_ADMIN_ROLE_UUID = IAM2RolePolicyStatementHelper.SECURITY_ADMIN_ROLE_UUID;
    public static final String SECURITY_ADMIN_ROLE_NAME = "SECURITY_ADMIN_ROLE";
    public static final String SECURITY_ADMIN_ROLE_IDENTITY = "SECURITY_ADMIN";
    public static final String SECURITY_ADMIN_UUID = IAM2RolePolicyStatementHelper.SECURITY_ADMIN_UUID;
    public static final String SECURITY_ADMIN_NAME = "secadmin";
    public static final String INITIAL_SECURITY_ADMIN_PASSWORD = "c518f374a404a77ab3b604ec64c36d57d444af00007339cac0bc9554b48e0d52d2cb77d3d688cfec5e82325a6a1686365cd07fd9f4ef940ccbcb7cef558184e7";

    public static final String AUDIT_ADMIN_ROLE_UUID = IAM2RolePolicyStatementHelper.AUDIT_ADMIN_ROLE_UUID;
    public static final String AUDIT_ADMIN_ROLE_NAME = "AUDIT_ADMIN_ROLE";
    public static final String AUDIT_ADMIN_ROLE_IDENTITY = "AUDIT_ADMIN";
    public static final String AUDIT_ADMIN_UUID = IAM2RolePolicyStatementHelper.AUDIT_ADMIN_UUID;
    public static final String AUDIT_ADMIN_NAME = "secauditor";
    public static final String INITIAL_AUDIT_ADMIN_PASSWORD = "ee4863240c7372a8663ba06a822fc22b18332157a549f2c4b431afbe73c5231572a4eff53e91a3676b4bcd494fb60f84364af51df5e5464b0bf6c1f0bf474d71";

    public static final String SYSTEM_ADMIN_ROLE_UUID = IAM2RolePolicyStatementHelper.SYSTEM_ADMIN_ROLE_UUID;
    public static final String SYSTEM_ADMIN_ROLE_NAME = "SYSTEM_ADMIN_ROLE";
    public static final String SYSTEM_ADMIN_ROLE_IDENTITY = "SYSTEM_ADMIN";
    public static final String SYSTEM_ADMIN_UUID = IAM2RolePolicyStatementHelper.SYSTEM_ADMIN_UUID;
    public static final String SYSTEM_ADMIN_NAME = "sysadmin";
    public static final String INITIAL_SYSTEM_ADMIN_PASSWORD = "dcdddd7692115a76dba92a4984b4d9af9a158b577b381285c748b5da661e8d9f111394ec737400a0d0729974ae6604625ffd50ddcacd037245a3dac596fc1de1";

    private static final IdentityType identityType = new IdentityType("PRIVILEGE_ADMIN");

    static boolean isInitialPrivilegeAdmin(String virtualIDUuid) {
        return virtualIDUuid.equals(AUDIT_ADMIN_UUID) || virtualIDUuid.equals(SECURITY_ADMIN_UUID) || virtualIDUuid.equals(SYSTEM_ADMIN_UUID);
    }

    @StaticInit(order = -999)
    private static void staticInit() {
        Set<String> normalPermissions = RBAC.roles.stream()
                .filter(p -> p.getName() != null && (p.getName().equals("privilege-admin-basic-roles")))
                .flatMap(p -> new HashSet<>(p.getAllowedActions()).stream()).collect(Collectors.toSet());

        securityAdminWhiteList.addAll(getPermissionExceptIdentity());
        securityAdminWhiteList.add(APIZQLQueryMsg.class.getName());
        securityAdminWhiteList.add(APIQueryGlobalConfigMsg.class.getName());
        securityAdminWhiteList.add(APIUpdateGlobalConfigMsg.class.getName());
        securityAdminWhiteList.add(APISetImageSecurityLevelMsg.class.getName());
        securityAdminWhiteList.add(APISetVmSecurityLevelMsg.class.getName());
        systemAdminBlackList.addAll(getPermissionExceptIdentity());

        List<String> queryApis = APIMessage.apiMessageClasses.stream()
                .filter(APISyncCallMessage.class::isAssignableFrom)
                .map(Class::getName).collect(Collectors.toList());

        auditAdminWhiteList.addAll(queryApis);
        auditAdminWhiteList.addAll(normalPermissions);
        securityAdminWhiteList.addAll(normalPermissions);
        securityAdminWhiteList.addAll(queryApis);

        readAPIsForAuditAdminJSONStatement = PolicyStatement.builder().name("read-apis-for-audit-admin")
                .effect(PolicyStatementEffect.Allow)
                .actions(new ArrayList<>(auditAdminWhiteList))
                .build().toJSON();
        permittedAPIsForSecurityAdminJSONStatement = PolicyStatement.builder().name("apis-for-security-admin")
                .effect(PolicyStatementEffect.Allow)
                .actions(new ArrayList<>(securityAdminWhiteList))
                .build().toJSON();

        systemAdminApis = RBAC.permissions.stream()
                .flatMap(p -> {
                    Set<String> as = new HashSet<>();
                    as.addAll(p.getAdminOnlyAPIs());
                    as.addAll(p.getNormalAPIs());
                    return as.stream();
                }).distinct().collect(Collectors.toList());
        systemAdminApis.removeAll(systemAdminBlackList);

        RoleIdentity systemRoleIdentity = new RoleIdentity(SYSTEM_ADMIN_ROLE_IDENTITY);
        systemRoleIdentity.installRoleIdentityValidator((identity, policyStatements) -> {
            Set<String> matchedApis = new HashSet<>();

            for(PolicyStatement st : policyStatements) {
                if (!st.getEffect().equals(PolicyStatementEffect.Allow)) {
                    return;
                }

                matchedApis.addAll(st.getActions().stream()
                        .filter(action -> systemAdminBlackList.stream()
                                .anyMatch(apiName -> isMatch(action, apiName)))
                        .collect(Collectors.toList()));
            }

            if (matchedApis.isEmpty()) {
                return;
            }


            throw new ApiMessageInterceptionException(argerr("action: %s, is not supported for role identity: %s", matchedApis, identity.toString()));
        });

        RoleIdentity auditRoleIdentity = new RoleIdentity(AUDIT_ADMIN_ROLE_IDENTITY);
        auditRoleIdentity.installRoleIdentityValidator((identity, policyStatements) -> {
            Set<String> unmatchedApis = new HashSet<>();

            for(PolicyStatement st : policyStatements) {
                if (!st.getEffect().equals(PolicyStatementEffect.Allow)) {
                    return;
                }

                unmatchedApis.addAll(st.getActions().stream()
                        .filter(action -> auditAdminWhiteList.stream()
                                .noneMatch(apiName -> isMatch(action, apiName)))
                        .collect(Collectors.toList()));
            }

            if (unmatchedApis.isEmpty()) {
                return;
            }

            throw new ApiMessageInterceptionException(argerr("action: %s, is not supported for role identity: %s", unmatchedApis, identity.toString()));
        });

        RoleIdentity securityRoleIdentity = new RoleIdentity(SECURITY_ADMIN_ROLE_IDENTITY);
        securityRoleIdentity.installRoleIdentityValidator((identity, policyStatements) -> {
            Set<String> unmatchedApis = new HashSet<>();

            for(PolicyStatement st : policyStatements) {
                if (!st.getEffect().equals(PolicyStatementEffect.Allow)) {
                    return;
                }

                unmatchedApis.addAll(st.getActions().stream()
                        .filter(action -> securityAdminWhiteList.stream()
                                .noneMatch(apiName -> isMatch(action, apiName)))
                        .collect(Collectors.toList()));
            }

            if (unmatchedApis.isEmpty()) {
                return;
            }

            throw new ApiMessageInterceptionException(argerr("action: %s, is not supported for role identity: %s", unmatchedApis, identity.toString()));
        });

        IAM2AuthorizationBackend.installPreApiRequestChecker(new IAM2PrivilegeAdminAPIRequestChecker());
    }

    private static PolicyMatcher policyMatcher = new PolicyMatcher();

    private static boolean isMatch(String as, String apiClassName) {
        String ap = PolicyUtils.apiNamePatternFromAction(as);
        return policyMatcher.match(ap, apiClassName);
    }

    public static List<String> getPrivilegeAttributes() {
        return Arrays.asList(SystemAdmin.SYSTEM_ADMIN.getName(),
                SecurityAdmin.SECURITY_ADMIN.getName(),
                AuditAdmin.AUDIT_ADMIN.getName());
    }

    public static boolean isPrivilegeAdmin(String uuid) {
        return Q.New(IAM2VirtualIDAttributeVO.class)
                .in(IAM2VirtualIDAttributeVO_.name, getPrivilegeAttributes())
                .eq(IAM2VirtualIDAttributeVO_.virtualIDUuid, uuid)
                .isExists();
    }

    private static List<String> getPermissionExceptIdentity() {
        return Arrays.asList(APIAddRolesToIAM2VirtualIDMsg.class.getName(),
                APIRemoveRolesFromIAM2VirtualIDMsg.class.getName(),
                APICreateRoleMsg.class.getName(),
                APIUpdateRoleMsg.class.getName(),
                APIDeleteRoleMsg.class.getName(),
                APIQueryRoleMsg.class.getName(),
                APIChangeRoleStateMsg.class.getName(),
                APIAttachPolicyToRoleMsg.class.getName(),
                APIAttachRoleToAccountMsg.class.getName(),
                APIDetachPolicyFromRoleMsg.class.getName(),
                APIDetachRoleFromAccountMsg.class.getName(),
                APIAddPolicyStatementsToRoleMsg.class.getName(),
                APIRemovePolicyStatementsFromRoleMsg.class.getName(),
                APICreatePolicyMsg.class.getName(),
                APIQueryPolicyMsg.class.getName(),
                APIAttachPolicyToUserGroupMsg.class.getName(),
                APIDetachPolicyFromUserGroupMsg.class.getName());
    }

    @Override
    public AccountLoginStruct getLoginEntry(String name, String password, String type) {
        return new SQLBatchWithReturn<AccountLoginStruct>() {
            @Override
            protected AccountLoginStruct scripts() {
                if (type != null && !type.equals(IAM2Constant.LOGIN_TYPE)) {
                    return null;
                }

                IAM2VirtualIDVO vid = q(IAM2VirtualIDVO.class)
                        .eq(IAM2VirtualIDVO_.name, name)
                        .eq(IAM2VirtualIDVO_.password, password)
                        .find();

                if (vid == null) {
                    return null;
                }

                if (isPrivilegeAdmin(vid.getUuid())) {
                    AccountLoginStruct struct = new AccountLoginStruct();
                    struct.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
                    struct.setUserUuid(vid.getUuid());
                    struct.setResourceType(IAM2VirtualIDVO.class.getSimpleName());
                    return struct;
                }

                return null;
            }
        }.execute();
    }

    @Override
    public AccountLoginStruct getLoginEntryByName(String name, String accountType) {
        return new SQLBatchWithReturn<AccountLoginStruct>() {
            @Override
            protected AccountLoginStruct scripts() {
                if (accountType != null && !accountType.equals(IAM2Constant.LOGIN_TYPE)) {
                    return null;
                }

                IAM2VirtualIDVO vid = q(IAM2VirtualIDVO.class)
                        .eq(IAM2VirtualIDVO_.name, name)
                        .find();

                if (vid == null) {
                    return null;
                }

                if (isPrivilegeAdmin(vid.getUuid())) {
                    AccountLoginStruct struct = new AccountLoginStruct();
                    struct.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
                    struct.setUserUuid(vid.getUuid());
                    struct.setResourceType(IAM2VirtualIDVO.class.getSimpleName());
                    struct.setLastOpTime(vid.getLastOpDate());
                    return struct;
                }

                return null;
            }
        }.execute();
    }

    public boolean checkPermission(SessionInventory session) {
        return new SQLBatchWithReturn<Boolean>() {
            @Override
            protected Boolean scripts() {
                if (!q(IAM2VirtualIDVO.class)
                        .eq(IAM2VirtualIDVO_.uuid, session.getUserUuid())
                        .isExists()) {
                    return false;
                }

                return SecurityAdmin.isSecurityAdmin(session.getUserUuid());
            }
        }.execute();
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        return null;
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.FRONT;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APIUpdateAccountMsg
                || msg instanceof APISessionMessage
                || msg instanceof APIQueryGlobalConfigMsg
                || msg instanceof APIQueryManagementNodeMsg
                || msg instanceof LicenseMessage) {
            return msg;
        }

        if (msg instanceof APIUpdateIAM2VirtualIDMsg) {
            validate((APIUpdateIAM2VirtualIDMsg) msg);
            return msg;
        }

        if (SanYuanGlobalProperty.SANYUAN_INSTALLED) {
            check(msg.getSession());
        }

        if (msg instanceof APIAddIAM2VirtualIDsToProjectMsg) {
            validate((APIAddIAM2VirtualIDsToProjectMsg) msg);
        } else if (msg instanceof APIAddRolesToIAM2VirtualIDMsg) {
            validate((APIAddRolesToIAM2VirtualIDMsg) msg);
        } else if (msg instanceof APIDeleteIAM2VirtualIDMsg) {
            validate((APIDeleteIAM2VirtualIDMsg) msg);
        } else if (msg instanceof APIRemoveRolesFromIAM2VirtualIDMsg) {
            validate((APIRemoveRolesFromIAM2VirtualIDMsg) msg);
        }

        return msg;
    }

    private void validate(APIUpdateIAM2VirtualIDMsg msg) {
        if (msg.getName() == null) {
            return;
        }

        if (isInitialPrivilegeAdmin(msg.getVirtualIDUuid())) {
            throw new ApiMessageInterceptionException(argerr("the name of initial user can not be updated"));
        } else if (isInitialAdminName(msg.getName())) {
            throw new ApiMessageInterceptionException(argerr("%s is a reserved name, please use another name", msg.getName()));
        }
    }

    private boolean isInitialAdminName(String name) {
        return SYSTEM_ADMIN_NAME.equals(name) || SECURITY_ADMIN_NAME.equals(name) || AUDIT_ADMIN_NAME.equals(name);
    }

    private void check(SessionInventory session) {
        if (session == null || session.getUserUuid() == null) {
            return;
        }

        new SQLBatch() {
            @Override
            protected void scripts() {
                String virtualIDUuid = session.getUserUuid();
                if (!Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.uuid, virtualIDUuid).isExists()) {
                    return;
                }

                if (IAM2SystemTags.IAM2_VIRTUAL_ID_PASSWORD_NOT_CHANGED.hasTag(virtualIDUuid)) {
                    throw new OperationFailureException(err(IdentityErrors.NEED_CHANGE_PASSWORD, "VirtualID[uuid:%s] need to change initial password before do any other operation", virtualIDUuid));
                }
            }
        }.execute();

    }

    private void validate(APIRemoveRolesFromIAM2VirtualIDMsg msg) {
        if (msg.getVirtualIDUuid().equals(AUDIT_ADMIN_UUID) && msg.getRoleUuids().contains(AUDIT_ADMIN_ROLE_UUID)) {
            throw new ApiMessageInterceptionException(argerr("cannot remove builtin audit admin role from builtin audit admin."));
        } else if (msg.getVirtualIDUuid().equals(SECURITY_ADMIN_UUID) && msg.getRoleUuids().contains(SECURITY_ADMIN_ROLE_UUID)) {
            throw new ApiMessageInterceptionException(argerr("cannot remove builtin security admin role from builtin security admin."));
        } else if (msg.getVirtualIDUuid().equals(SYSTEM_ADMIN_UUID) && msg.getRoleUuids().contains(SYSTEM_ADMIN_ROLE_UUID)) {
            throw new ApiMessageInterceptionException(argerr("cannot remove builtin system admin role from builtin system admin."));
        }
    }

    private void validate(APIDeleteIAM2VirtualIDMsg msg) {
        if (msg.getVirtualIDUuid().equals(AUDIT_ADMIN_UUID)) {
            throw new ApiMessageInterceptionException(argerr("cannot delete builtin audit admin."));
        } else if (msg.getVirtualIDUuid().equals(SECURITY_ADMIN_UUID)) {
            throw new ApiMessageInterceptionException(argerr("cannot delete builtin security admin."));
        } else if (msg.getVirtualIDUuid().equals(SYSTEM_ADMIN_UUID)) {
            throw new ApiMessageInterceptionException(argerr("cannot delete builtin system admin."));
        }
    }

    private void validate(APIAddRolesToIAM2VirtualIDMsg msg) {
        List<String> identityList = Q.New(RoleVO.class)
                .select(RoleVO_.identity)
                .notNull(RoleVO_.identity)
                .in(RoleVO_.uuid, msg.getRoleUuids())
                .listValues();

        Set<String> identitySet = new HashSet<>(identityList);
        if (identitySet.size() > 1) {
            throw new ApiMessageInterceptionException(argerr("Confirm the roles you want to add have same identity"));
        }

        if (identitySet.isEmpty()) {
            return;
        }

        Long roleIdentityCount = SQL.New("SELECT count(role) from RoleVO role, IAM2VirtualIDRoleRefVO ref where ref.virtualIDUuid = :virtualIDUuid" +
                " and role.uuid = ref.roleUuid and role.identity not in (:roleIdentity)", Long.class)
                .param("virtualIDUuid", msg.getVirtualIDUuid())
                .param("roleIdentity", identitySet)
                .find();

        if (roleIdentityCount > 0) {
            throw new ApiMessageInterceptionException(argerr("Cannot add role: %s with identity: %s to virtualID[uuid:%s]",
                    msg.getRoleUuids(), identitySet, msg.getVirtualIDUuid()));
        }
    }

    private void validate(APIAddIAM2VirtualIDsToProjectMsg msg) {
        List<String> privilegeAdminUuids = msg.getVirtualIDUuids()
                .stream()
                .filter(IAM2PrivilegeAdminPolicyHelper::isPrivilegeAdmin)
                .collect(Collectors.toList());

        if (!privilegeAdminUuids.isEmpty()) {
            throw new ApiMessageInterceptionException(argerr("can not add privilege admin[uuids:%s] " +
                    "to project[uuid:%s]", String.join(",", privilegeAdminUuids), msg.getProjectUuid()));
        }
    }

    @Override
    public String getIdentityType() {
        return identityType.toString();
    }

    @Override
    public void prepareResources() {
        if (!Q.New(RoleVO.class).eq(RoleVO_.uuid, AUDIT_ADMIN_ROLE_UUID).isExists()) {
            // audit admin
            RoleVO role = new RoleVO();
            role.setUuid(AUDIT_ADMIN_ROLE_UUID);
            role.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            role.setName(AUDIT_ADMIN_ROLE_NAME);
            role.setDescription(role.getName());
            role.setType(RoleType.PredefinedBySystem);
            role.setIdentity(AUDIT_ADMIN_ROLE_IDENTITY);
            dbf.persist(role);

            RolePolicyStatementVO rs = new RolePolicyStatementVO();
            rs.setRoleUuid(role.getUuid());
            rs.setUuid(Platform.getUuid());
            rs.setStatement(readAPIsForAuditAdminJSONStatement);
            dbf.persist(rs);
        } else {
            SQL.New(RoleVO.class)
                    .eq(RoleVO_.uuid, AUDIT_ADMIN_ROLE_UUID)
                    .set(RoleVO_.identity, AUDIT_ADMIN_ROLE_IDENTITY).update();
            SQL.New(RolePolicyStatementVO.class)
                    .eq(RolePolicyStatementVO_.roleUuid, AUDIT_ADMIN_ROLE_UUID)
                    .set(RolePolicyStatementVO_.statement, readAPIsForAuditAdminJSONStatement).update();
        }

        if (!Q.New(RoleVO.class).eq(RoleVO_.uuid, SECURITY_ADMIN_ROLE_UUID).isExists()) {
            RoleVO role = new RoleVO();
            role.setUuid(SECURITY_ADMIN_ROLE_UUID);
            role.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            role.setName(SECURITY_ADMIN_ROLE_NAME);
            role.setDescription(role.getName());
            role.setType(RoleType.PredefinedBySystem);
            role.setIdentity(SECURITY_ADMIN_ROLE_IDENTITY);
            dbf.persist(role);

            RolePolicyStatementVO rs = new RolePolicyStatementVO();
            rs.setRoleUuid(role.getUuid());
            rs.setUuid(Platform.getUuid());
            rs.setStatement(permittedAPIsForSecurityAdminJSONStatement);
            dbf.persist(rs);
        } else {
            SQL.New(RoleVO.class)
                    .eq(RoleVO_.uuid, SECURITY_ADMIN_ROLE_UUID)
                    .set(RoleVO_.identity, SECURITY_ADMIN_ROLE_IDENTITY).update();
            SQL.New(RolePolicyStatementVO.class)
                    .eq(RolePolicyStatementVO_.roleUuid, SECURITY_ADMIN_ROLE_UUID)
                    .set(RolePolicyStatementVO_.statement, permittedAPIsForSecurityAdminJSONStatement)
                    .update();
        }

        if (!Q.New(RoleVO.class).eq(RoleVO_.uuid, SYSTEM_ADMIN_ROLE_UUID).isExists()) {
            RoleVO role = new RoleVO();
            role.setUuid(SYSTEM_ADMIN_ROLE_UUID);
            role.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            role.setName(SYSTEM_ADMIN_ROLE_NAME);
            role.setDescription(role.getName());
            role.setType(RoleType.PredefinedBySystem);
            role.setIdentity(SYSTEM_ADMIN_ROLE_IDENTITY);
            dbf.persist(role);

            RolePolicyStatementVO rs = new RolePolicyStatementVO();
            rs.setRoleUuid(role.getUuid());
            rs.setUuid(Platform.getUuid());
            rs.setStatement(PolicyStatement
                    .builder().effect(PolicyStatementEffect.Allow)
                    .actions(systemAdminApis)
                    .build().toJSON());
            dbf.persist(rs);
        } else {
            SQL.New(RoleVO.class)
                    .eq(RoleVO_.uuid, SYSTEM_ADMIN_ROLE_UUID)
                    .set(RoleVO_.identity, SYSTEM_ADMIN_ROLE_IDENTITY).update();
            SQL.New(RolePolicyStatementVO.class)
                    .eq(RolePolicyStatementVO_.roleUuid, SYSTEM_ADMIN_ROLE_UUID)
                    .set(RolePolicyStatementVO_.statement, PolicyStatement
                            .builder().effect(PolicyStatementEffect.Allow)
                            .actions(systemAdminApis)
                            .build().toJSON())
                    .update();
        }

        if (!Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.uuid, SECURITY_ADMIN_UUID).isExists()) {
            IAM2VirtualIDVO virtualIDVO = new IAM2VirtualIDVO();
            virtualIDVO.setUuid(SECURITY_ADMIN_UUID);
            virtualIDVO.setName(SECURITY_ADMIN_NAME);
            virtualIDVO.setPassword(INITIAL_SECURITY_ADMIN_PASSWORD);
            virtualIDVO.setState(IAM2State.Enabled);
            virtualIDVO.setType(IAM2Manager.type.toString());
            virtualIDVO.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            dbf.persist(virtualIDVO);

            tagMgr.createInherentSystemTag(SECURITY_ADMIN_UUID, IAM2SystemTags.IAM2_VIRTUAL_ID_PASSWORD_NOT_CHANGED.getTagFormat(), IAM2VirtualIDVO.class.getSimpleName());

            IAM2VirtualIDRoleRefVO ref = new IAM2VirtualIDRoleRefVO();
            ref.setVirtualIDUuid(virtualIDVO.getUuid());
            ref.setRoleUuid(SECURITY_ADMIN_ROLE_UUID);
            ref.setTargetAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            dbf.persist(ref);

            IAM2VirtualIDAttributeVO attributeVO = new IAM2VirtualIDAttributeVO();
            attributeVO.setUuid(Platform.getUuid());
            attributeVO.setVirtualIDUuid(virtualIDVO.getUuid());
            attributeVO.setType(AttributeType.System);
            attributeVO.setName(SecurityAdmin.SECURITY_ADMIN.getName());
            dbf.persist(attributeVO);
        }

        if (!Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.uuid, AUDIT_ADMIN_UUID).isExists()) {
            IAM2VirtualIDVO virtualIDVO = new IAM2VirtualIDVO();
            virtualIDVO.setUuid(AUDIT_ADMIN_UUID);
            virtualIDVO.setName(AUDIT_ADMIN_NAME);
            virtualIDVO.setPassword(INITIAL_AUDIT_ADMIN_PASSWORD);
            virtualIDVO.setState(IAM2State.Enabled);
            virtualIDVO.setType(IAM2Manager.type.toString());
            virtualIDVO.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            dbf.persist(virtualIDVO);

            tagMgr.createInherentSystemTag(AUDIT_ADMIN_UUID, IAM2SystemTags.IAM2_VIRTUAL_ID_PASSWORD_NOT_CHANGED.getTagFormat(), IAM2VirtualIDVO.class.getSimpleName());

            IAM2VirtualIDRoleRefVO ref = new IAM2VirtualIDRoleRefVO();
            ref.setVirtualIDUuid(virtualIDVO.getUuid());
            ref.setRoleUuid(AUDIT_ADMIN_ROLE_UUID);
            ref.setTargetAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            dbf.persist(ref);

            IAM2VirtualIDAttributeVO attributeVO = new IAM2VirtualIDAttributeVO();
            attributeVO.setUuid(Platform.getUuid());
            attributeVO.setVirtualIDUuid(virtualIDVO.getUuid());
            attributeVO.setType(AttributeType.System);
            attributeVO.setName(AuditAdmin.AUDIT_ADMIN.getName());
            dbf.persist(attributeVO);
        }

        if (!Q.New(IAM2VirtualIDVO.class).eq(IAM2VirtualIDVO_.uuid, SYSTEM_ADMIN_UUID).isExists()) {
            IAM2VirtualIDVO virtualIDVO = new IAM2VirtualIDVO();
            virtualIDVO.setUuid(SYSTEM_ADMIN_UUID);
            virtualIDVO.setName(SYSTEM_ADMIN_NAME);
            virtualIDVO.setPassword(INITIAL_SYSTEM_ADMIN_PASSWORD);
            virtualIDVO.setState(IAM2State.Enabled);
            virtualIDVO.setType(IAM2Manager.type.toString());
            virtualIDVO.setAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            dbf.persist(virtualIDVO);

            tagMgr.createInherentSystemTag(SYSTEM_ADMIN_UUID, IAM2SystemTags.IAM2_VIRTUAL_ID_PASSWORD_NOT_CHANGED.getTagFormat(), IAM2VirtualIDVO.class.getSimpleName());

            IAM2VirtualIDRoleRefVO ref = new IAM2VirtualIDRoleRefVO();
            ref.setVirtualIDUuid(virtualIDVO.getUuid());
            ref.setRoleUuid(SYSTEM_ADMIN_ROLE_UUID);
            ref.setTargetAccountUuid(AccountConstant.INITIAL_SYSTEM_ADMIN_UUID);
            dbf.persist(ref);

            IAM2VirtualIDAttributeVO attributeVO = new IAM2VirtualIDAttributeVO();
            attributeVO.setUuid(Platform.getUuid());
            attributeVO.setVirtualIDUuid(virtualIDVO.getUuid());
            attributeVO.setType(AttributeType.System);
            attributeVO.setName(SystemAdmin.SYSTEM_ADMIN.getName());
            dbf.persist(attributeVO);
        }
    }

    @Override
    public boolean start() {
        installResetGlobalConfigExtensions();
        return true;
    }

    private void installResetGlobalConfigExtensions() {
        GlobalConfigBeforeResetExtensionPoint ext = session -> {
            if (!SanYuanGlobalProperty.SANYUAN_INSTALLED) {
                return;
            }

            if (SystemAdmin.isSystemAdmin(session.getUserUuid())) {
                throw new SkipResetGlobalConfigException();
            }
        };

        PasswordStrategyGlobalConfig.ENABLE_FORCE_CHANGE_PASSWORD_PERIOD.installBeforeResetExtension(ext);
        PasswordStrategyGlobalConfig.FORCE_CHANGE_PASSWORD_PERIOD.installBeforeResetExtension(ext);
        PasswordStrategyGlobalConfig.ENABLE_HISTORICAL_PASSWORD_COMPARE.installBeforeResetExtension(ext);
        PasswordStrategyGlobalConfig.HISTORICAL_PASSWORD_NUM.installBeforeResetExtension(ext);
        PasswordStrategyGlobalConfig.ENABLE_LOCK_LOGIN_ATTEMPTS_MAXIMUM.installBeforeResetExtension(ext);
        PasswordStrategyGlobalConfig.LOCK_LOGIN_ATTEMPTS_MAXIMUM.installBeforeResetExtension(ext);
        PasswordStrategyGlobalConfig.LOCK_LOGIN_PERIOD.installBeforeResetExtension(ext);
        PasswordStrategyGlobalConfig.PASSWORD_STRENGTH_CHECK_CONFIG.installBeforeResetExtension(ext);

        TwoFactorAuthenticationGlobalConfig.ENABLE_TWOFA_ATUH.installBeforeResetExtension(ext);
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public AttributeAction getAdminAttributeAction(String virtualIDUuid, List<String> excludeRoleUuids) {
        if (isPrivilegeAdminRoleAttached(IAM2PrivilegeAdminPolicyHelper.SYSTEM_ADMIN_ROLE_IDENTITY, virtualIDUuid, excludeRoleUuids)) {
            return new SystemAdmin();
        } else if (isPrivilegeAdminRoleAttached(IAM2PrivilegeAdminPolicyHelper.SECURITY_ADMIN_ROLE_IDENTITY, virtualIDUuid, excludeRoleUuids)) {
            return new SecurityAdmin();
        } else if (isPrivilegeAdminRoleAttached(IAM2PrivilegeAdminPolicyHelper.AUDIT_ADMIN_ROLE_IDENTITY, virtualIDUuid, excludeRoleUuids)) {
            return new AuditAdmin();
        }

        return null;
    }

    private boolean isPrivilegeAdminRoleAttached(String roleIdentity, String virtualIDUuid, List<String> excludeRoleUuids) {
        String sql = "select count (*) from IAM2VirtualIDRoleRefVO ref, RoleVO role where ref.roleUuid = role.uuid" +
                " and ref.virtualIDUuid = :virtualIDUuid and role.identity = :roleIdentity ";


        if (excludeRoleUuids != null && !excludeRoleUuids.isEmpty()) {
            sql += "and role.uuid not in (:excludeRoleUuids)";
        }

        SQL query = SQL.New(sql, Long.class)
                .param("virtualIDUuid", virtualIDUuid)
                .param("roleIdentity", roleIdentity);

        if (excludeRoleUuids != null && !excludeRoleUuids.isEmpty()) {
            query.param("excludeRoleUuids", excludeRoleUuids);
        }

        return (Long) query.find() > 0;
    }

}
