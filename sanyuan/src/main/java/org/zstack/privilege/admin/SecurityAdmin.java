package org.zstack.privilege.admin;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQLBatch;
import org.zstack.iam2.attribute.AttributeAction;
import org.zstack.iam2.attribute.SystemAttributes;
import org.zstack.iam2.attribute.virtualid.AbstractAdminAttribute;
import org.zstack.iam2.entity.*;

import java.util.ArrayList;
import java.util.List;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class SecurityAdmin extends AbstractAdminAttribute implements AttributeAction {
    public static final IAM2AttributeInventory SECURITY_ADMIN = SystemAttributes.define("__SecurityAdmin__", new IAM2VirtualIDAttributeInventory());

    @Override
    public Class getResourceType() {
        return IAM2VirtualIDVO.class;
    }

    @Override
    public String getAttributeName() {
        return SECURITY_ADMIN.getName();
    }

    @Override
    public void actAtAdd(IAM2AttributeInventory inv) {
        new SQLBatch() {
            @Override
            protected void scripts() {
                IAM2VirtualIDAttributeInventory idinv = (IAM2VirtualIDAttributeInventory) inv;

                checkDuplicate(((IAM2VirtualIDAttributeInventory) inv).getVirtualIDUuid(), getAttributeName());
            }
        }.execute();
    }

    @Override
    public void actAtRemove(IAM2AttributeInventory inv) {
    }

    public static boolean isSecurityAdmin(String uuid) {
        return Q.New(IAM2VirtualIDAttributeVO.class)
                .eq(IAM2VirtualIDAttributeVO_.name, SECURITY_ADMIN.getName())
                .eq(IAM2VirtualIDAttributeVO_.virtualIDUuid, uuid)
                .isExists();
    }

    @Override
    public List<String> getPolicyWhiteList() {
        return new ArrayList<>(IAM2PrivilegeAdminPolicyHelper.securityAdminWhiteList);
    }

    @Override
    public int getOrder() {
        return 1;
    }

}
